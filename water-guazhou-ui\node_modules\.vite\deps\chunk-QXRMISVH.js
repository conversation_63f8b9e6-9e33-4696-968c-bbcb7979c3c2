import {
  f as f5,
  h as h4
} from "./chunk-JF4IAAZV.js";
import {
  v as v8
} from "./chunk-OY3C7FMJ.js";
import {
  e as e8,
  l as l9,
  r as r10,
  s as s9,
  w as w6
} from "./chunk-3UMLI77U.js";
import {
  e as e7
} from "./chunk-2WS4DQ5K.js";
import {
  d as d3
} from "./chunk-MWAJDZK5.js";
import {
  l as l8
} from "./chunk-WZNPTIYX.js";
import {
  r as r8,
  t as t10
} from "./chunk-VCFT2OOI.js";
import {
  U as U4
} from "./chunk-UVTCDHAH.js";
import {
  c as c10
} from "./chunk-22FAZXOH.js";
import {
  Gt,
  H,
  St,
  Y,
  Z,
  gt,
  ht,
  jt,
  pt,
  tt,
  wt,
  xt
} from "./chunk-T7HWQQFI.js";
import {
  h as h3
} from "./chunk-MSIU52YL.js";
import {
  r as r7
} from "./chunk-QCTKOQ44.js";
import {
  R as R3,
  b as b5
} from "./chunk-W2A4ESM5.js";
import {
  d as d4
} from "./chunk-WD3CFUZ4.js";
import {
  c as c11
} from "./chunk-O6CFGYVQ.js";
import {
  u as u3
} from "./chunk-THUK4WUF.js";
import {
  t as t11
} from "./chunk-RVGLVPCD.js";
import {
  t as t12
} from "./chunk-RYZGQKBF.js";
import {
  e as e6,
  t as t9
} from "./chunk-LGZ4BCMJ.js";
import {
  n as n6,
  r as r5
} from "./chunk-LVUK4QJX.js";
import {
  e as e5
} from "./chunk-JP4EK4VO.js";
import {
  t as t8
} from "./chunk-MI2Z635K.js";
import {
  n as n8
} from "./chunk-APXZNW5O.js";
import {
  W,
  e as e3,
  e2 as e4,
  o as o4,
  r as r4,
  t as t7
} from "./chunk-NQQSL2QK.js";
import {
  f as f4,
  h as h2,
  i as i3,
  n2 as n5,
  v as v5,
  w as w5
} from "./chunk-IZLLLMFE.js";
import {
  j as j5,
  p as p4
} from "./chunk-IEIKQ72S.js";
import {
  L as L3
} from "./chunk-UXW4QK2C.js";
import {
  o as o7
} from "./chunk-EM6CPBT6.js";
import {
  l as l6
} from "./chunk-JJ3NE6DY.js";
import {
  D,
  R as R2
} from "./chunk-4VO6N7OL.js";
import {
  i as i11,
  n as n9,
  o as o8,
  r as r9
} from "./chunk-HAEVWZ5B.js";
import {
  P,
  i as i10,
  o as o6,
  u as u4
} from "./chunk-HURTVQSL.js";
import {
  a as a8,
  m as m3,
  o as o5,
  r as r6
} from "./chunk-SROTSYJS.js";
import {
  n as n7
} from "./chunk-FOE4ICAJ.js";
import {
  a as a10
} from "./chunk-5VSS44JR.js";
import {
  a as a11,
  i as i7,
  l as l7
} from "./chunk-56K7OMWB.js";
import {
  l as l5
} from "./chunk-TDC6MNNF.js";
import {
  s as s8
} from "./chunk-UMW4I2EJ.js";
import {
  He
} from "./chunk-33Z6JDMT.js";
import {
  v as v7
} from "./chunk-ZJC3GHA7.js";
import {
  c as c8
} from "./chunk-IKGI4J4I.js";
import {
  s as s6
} from "./chunk-IU22XAFH.js";
import {
  d as d2
} from "./chunk-4FIRBBKR.js";
import {
  a as a5
} from "./chunk-2WMCP27R.js";
import {
  b as b3,
  j as j4
} from "./chunk-P37TUI4J.js";
import {
  An,
  en,
  rn,
  tn
} from "./chunk-UYAKJRPP.js";
import {
  c as c4
} from "./chunk-ZVU4V5QV.js";
import {
  m as m4,
  x
} from "./chunk-W3CLOCDX.js";
import {
  a as a7
} from "./chunk-FZ7BG3VX.js";
import {
  a as a9,
  c as c3,
  d
} from "./chunk-Q4VCSCSY.js";
import {
  b as b4
} from "./chunk-FBVKALLT.js";
import {
  T
} from "./chunk-N7ADFPOO.js";
import {
  U as U2,
  a as a6,
  f as f3,
  h,
  j as j3,
  l as l3,
  w as w4
} from "./chunk-QUHG7NMD.js";
import {
  v as v6
} from "./chunk-D7S3BWBP.js";
import {
  g as g3
} from "./chunk-TLKX5XIJ.js";
import {
  I,
  c2 as c5,
  c3 as c6,
  c4 as c7,
  i2 as i5,
  i3 as i6,
  i4 as i8,
  u as u2
} from "./chunk-MQ2IOGEF.js";
import {
  a as a12,
  a2 as a13,
  c as c9
} from "./chunk-24NZLSKM.js";
import {
  L as L2,
  S,
  m,
  u
} from "./chunk-RFYOGM4H.js";
import {
  he,
  ie,
  xe
} from "./chunk-VNYCO3JG.js";
import {
  i as i9
} from "./chunk-57XIOVP5.js";
import {
  S as S2
} from "./chunk-R3VLALN5.js";
import {
  i as i4,
  s as s7
} from "./chunk-22GGEXM2.js";
import {
  m as m5
} from "./chunk-37DYRJVQ.js";
import {
  j as j2
} from "./chunk-JOV46W3N.js";
import {
  l as l2,
  s as s5
} from "./chunk-5GX2JMCX.js";
import {
  _,
  m as m2
} from "./chunk-EDS4WCRT.js";
import {
  n as n4
} from "./chunk-SGIJIEHB.js";
import {
  i as i2
} from "./chunk-FLHLIVG4.js";
import {
  U as U3
} from "./chunk-AW4AS2UW.js";
import {
  o as o3,
  v as v4
} from "./chunk-X7FOCGBC.js";
import {
  c,
  r as r3,
  w as w3
} from "./chunk-XTO3XXZ3.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import {
  M,
  R
} from "./chunk-R5MYQRRS.js";
import {
  E as E2,
  f2
} from "./chunk-JXLVNWKF.js";
import {
  s as s4
} from "./chunk-7SWS36OI.js";
import {
  c as c2
} from "./chunk-G5KX4JSG.js";
import {
  l as l4
} from "./chunk-T23PB46T.js";
import {
  a as a4,
  b as b2
} from "./chunk-EIGTETCG.js";
import {
  g as g2
} from "./chunk-MQAXMQFG.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e,
  o as o2,
  t as t3,
  t2 as t4,
  v as v3
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  t2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  n
} from "./chunk-HP475EI3.js";
import {
  A as A2,
  n as n3,
  v as v2
} from "./chunk-C5VMWMBD.js";
import {
  t as t5
} from "./chunk-TUM6KUQZ.js";
import {
  n as n2
} from "./chunk-2CM7MIII.js";
import {
  A,
  C,
  E,
  L,
  U,
  a as a3,
  b,
  f,
  g,
  i,
  j,
  p as p3,
  t as t6,
  v,
  y as y2
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  r as r2
} from "./chunk-LTKA6OKA.js";
import {
  has,
  p as p2
} from "./chunk-REW33H3I.js";
import {
  a,
  e as e2,
  o,
  p,
  r,
  s,
  t,
  w
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/BreakpointsOwner.js
var n10 = { widthBreakpoint: { getValue(e21) {
  const i28 = e21.viewSize[0], s22 = e21.breakpoints, a32 = this.values;
  return i28 <= s22.xsmall ? a32.xsmall : i28 <= s22.small ? a32.small : i28 <= s22.medium ? a32.medium : i28 <= s22.large ? a32.large : a32.xlarge;
}, values: { xsmall: "xsmall", small: "small", medium: "medium", large: "large", xlarge: "xlarge" }, valueToClassName: { xsmall: "esri-view-width-xsmall esri-view-width-less-than-small esri-view-width-less-than-medium esri-view-width-less-than-large esri-view-width-less-than-xlarge", small: "esri-view-width-small esri-view-width-greater-than-xsmall esri-view-width-less-than-medium esri-view-width-less-than-large esri-view-width-less-than-xlarge", medium: "esri-view-width-medium esri-view-width-greater-than-xsmall esri-view-width-greater-than-small esri-view-width-less-than-large esri-view-width-less-than-xlarge", large: "esri-view-width-large esri-view-width-greater-than-xsmall esri-view-width-greater-than-small esri-view-width-greater-than-medium esri-view-width-less-than-xlarge", xlarge: "esri-view-width-xlarge esri-view-width-greater-than-xsmall esri-view-width-greater-than-small esri-view-width-greater-than-medium esri-view-width-greater-than-large" } }, heightBreakpoint: { getValue(e21) {
  const i28 = e21.viewSize[1], s22 = e21.breakpoints, a32 = this.values;
  return i28 <= s22.xsmall ? a32.xsmall : i28 <= s22.small ? a32.small : i28 <= s22.medium ? a32.medium : i28 <= s22.large ? a32.large : a32.xlarge;
}, values: { xsmall: "xsmall", small: "small", medium: "medium", large: "large", xlarge: "xlarge" }, valueToClassName: { xsmall: "esri-view-height-xsmall esri-view-height-less-than-small esri-view-height-less-than-medium esri-view-height-less-than-large esri-view-height-less-than-xlarge", small: "esri-view-height-small esri-view-height-greater-than-xsmall esri-view-height-less-than-medium esri-view-height-less-than-large esri-view-height-less-than-xlarge", medium: "esri-view-height-medium esri-view-height-greater-than-xsmall esri-view-height-greater-than-small esri-view-height-less-than-large esri-view-height-less-than-xlarge", large: "esri-view-height-large esri-view-height-greater-than-xsmall esri-view-height-greater-than-small esri-view-height-greater-than-medium esri-view-height-less-than-xlarge", xlarge: "esri-view-height-xlarge esri-view-height-greater-than-xsmall esri-view-height-greater-than-small esri-view-height-greater-than-medium esri-view-height-greater-than-large" } }, orientation: { getValue(e21) {
  const i28 = e21.viewSize, s22 = i28[0], a32 = i28[1], t26 = this.values;
  return a32 >= s22 ? t26.portrait : t26.landscape;
}, values: { portrait: "portrait", landscape: "landscape" }, valueToClassName: { portrait: "esri-view-orientation-portrait", landscape: "esri-view-orientation-landscape" } } };
var o9 = { xsmall: 544, small: 768, medium: 992, large: 1200 };
function m6(e21) {
  const i28 = e21;
  return i28 && i28.xsmall < i28.small && i28.small < i28.medium && i28.medium < i28.large;
}
function g4(e21, i28) {
  return i28 ? n10[e21].valueToClassName[i28].split(" ") : [];
}
var w7 = (w18) => {
  let d15 = class extends w18 {
    constructor(...e21) {
      super(...e21), this._breakpointsHandles = new t4(), this.orientation = null, this.widthBreakpoint = null, this.heightBreakpoint = null, this.breakpoints = o9;
    }
    initialize() {
      this._breakpointsHandles.add(l3(() => [this.breakpoints, this.size], () => this._updateClassNames(), h));
    }
    destroy() {
      this.destroyed || (this._removeActiveClassNames(), this._breakpointsHandles = a(this._breakpointsHandles));
    }
    set breakpoints(e21) {
      if (e21 === this._get("breakpoints")) return;
      const i28 = m6(e21);
      if (!i28) {
        const e22 = JSON.stringify(o9, null, 2);
        console.warn("provided breakpoints are not valid, using defaults:" + e22);
      }
      e21 = i28 ? e21 : o9, this._set("breakpoints", { ...e21 });
    }
    _updateClassNames() {
      if (!this.container) return;
      const e21 = t5.acquire(), s22 = t5.acquire();
      let a32, t26 = false;
      for (a32 in n10) {
        const i28 = this[a32], r20 = n10[a32].getValue({ viewSize: this.size, breakpoints: this.breakpoints });
        i28 !== r20 && (t26 = true, this[a32] = r20, g4(a32, i28).forEach((e22) => s22.push(e22)), g4(a32, r20).forEach((i29) => e21.push(i29)));
      }
      t26 && (this._applyClassNameChanges(e21, s22), t5.release(e21), t5.release(s22));
    }
    _applyClassNameChanges(e21, i28) {
      const s22 = this.container;
      s22 && (i28.forEach((e22) => s22.classList.remove(e22)), e21.forEach((e22) => s22.classList.add(e22)));
    }
    _removeActiveClassNames() {
      const e21 = this.container;
      if (!e21) return;
      let i28;
      for (i28 in n10) g4(i28, this[i28]).forEach((i29) => e21.classList.remove(i29));
    }
  };
  return e([y()], d15.prototype, "breakpoints", null), e([y()], d15.prototype, "orientation", void 0), e([y()], d15.prototype, "widthBreakpoint", void 0), e([y()], d15.prototype, "heightBreakpoint", void 0), d15 = e([a2("esri.views.BreakpointsOwner")], d15), d15;
};

// node_modules/@arcgis/core/views/overlay/ViewOverlay.js
var a14 = class extends v3 {
  constructor() {
    super(...arguments), this.items = new j2(), this._watchUpdatingTracking = new c3(), this._callbacks = /* @__PURE__ */ new Map(), this._projector = i3(), this._hiddenProjector = i3();
  }
  get needsRender() {
    return this.items.length > 0;
  }
  get updating() {
    var _a;
    return ((_a = this._watchUpdatingTracking) == null ? void 0 : _a.updating) ?? false;
  }
  initialize() {
    const t26 = document.createElement("div");
    t26.className = "esri-overlay-surface", this._set("surface", t26), this._hiddenSurface = document.createElement("div"), this._hiddenSurface.setAttribute("style", "visibility: hidden;"), t26.appendChild(this._hiddenSurface), this._watchUpdatingTracking.addOnCollectionChange(() => this.items, (t27) => {
      for (const e21 of t27.added) {
        const t28 = () => e21.render();
        this._callbacks.set(e21, t28), this._projector.append(this.surface, t28);
      }
      for (const e21 of t27.removed) {
        const t28 = this._projector.detach(this._callbacks.get(e21));
        this.surface.removeChild(t28.domNode), this._callbacks.delete(e21);
      }
    });
  }
  addItem(t26) {
    this.items.add(t26);
  }
  removeItem(t26) {
    this.items.remove(t26);
  }
  destroy() {
    this.items.removeAll(), this._callbacks.forEach((t26) => this._projector.detach(t26)), this._callbacks = null, this._projector = null, this._watchUpdatingTracking.destroy();
  }
  render() {
    this._projector.renderNow();
  }
  computeBoundingRect(t26) {
    const e21 = this._hiddenSurface, r20 = this._hiddenProjector;
    let o27;
    const s22 = () => (o27 = t26.render(), o27);
    r20.append(e21, s22), r20.renderNow();
    const i28 = { left: 0, top: 0, right: 0, bottom: 0 };
    if (o27 && o27.domNode) {
      const t27 = o27.domNode.getBoundingClientRect();
      i28.left = t27.left, i28.top = t27.top, i28.right = t27.right, i28.bottom = t27.bottom;
    }
    for (r20.detach(s22); e21.firstChild; ) e21.removeChild(e21.firstChild);
    return i28;
  }
  overlaps(t26, e21) {
    const r20 = this.computeBoundingRect(t26), o27 = this.computeBoundingRect(e21);
    return Math.max(r20.left, o27.left) <= Math.min(r20.right, o27.right) && Math.max(r20.top, o27.top) <= Math.min(r20.bottom, o27.bottom);
  }
  get hasVisibleItems() {
    return this.items.some((t26) => t26.visible);
  }
  async prepare() {
    await document.fonts.load(this._fontString()).catch(() => {
    });
  }
  renderCanvas(t26) {
    if (!this.items.some((t27) => t27.visible)) return;
    const e21 = t26.getContext("2d");
    e21.save(), e21.font = this._fontString(), this.items.forEach((t27) => {
      e21.save(), t27.renderCanvas(e21), e21.restore();
    }), e21.restore();
  }
  _fontString() {
    return `10px ${getComputedStyle(this.surface).fontFamily}`;
  }
};
e([y({ readOnly: true })], a14.prototype, "surface", void 0), e([y({ readOnly: true })], a14.prototype, "items", void 0), e([y({ readOnly: true })], a14.prototype, "needsRender", null), e([y({ readOnly: true })], a14.prototype, "_watchUpdatingTracking", void 0), e([y({ readOnly: true })], a14.prototype, "updating", null), a14 = e([a2("esri.views.overlay.ViewOverlay")], a14);
var c12 = a14;

// node_modules/@arcgis/core/widgets/Feature/support/featureUtils.js
var p5 = "esri.widgets.Feature.support.featureUtils";
var m7 = s2.getLogger(p5);
var y3 = /href=(""|'')/gi;
var g5 = /(\{([^\{\r\n]+)\})/g;
var h5 = /\'/g;
var I2 = /^\s*expression\//i;
var b6 = /(\n)/gi;
var F = /[\u00A0-\u9999<>\&]/gim;
var w8 = /href\s*=\s*(?:\"([^\"]+)\"|\'([^\']+)\')/gi;
var N = /^(?:mailto:|tel:)/;
var E3 = "relationships/";
var T2 = S("short-date-short-time");
function j6(e21) {
  if (!t(e21)) return e21.get("sourceLayer") || e21.get("layer");
}
async function x2(e21, t26) {
  return "function" == typeof e21 ? e21.call(null, t26) : e21;
}
function C2(e21 = "") {
  if (e21) return !N.test(e21.trim().toLowerCase());
}
function M2(e21) {
  return !!e21 && I2.test(e21);
}
function q(e21, t26) {
  if (!M2(t26) || !e21) return null;
  const r20 = t26.replace(I2, "").toLowerCase();
  let n27 = null;
  return e21.some((e22) => e22.name.toLowerCase() === r20 && (n27 = e22, true)), n27;
}
function R4(e21, t26) {
  const r20 = q(t26, e21 == null ? void 0 : e21.fieldName);
  return r20 ? r20.title || null : e21 ? e21.label || e21.fieldName : null;
}
function v9(e21, t26) {
  const r20 = t26.get(e21.toLowerCase());
  return `{${r20 && r20.fieldName || e21}}`;
}
function D2(e21) {
  return e21.replace(y3, "");
}
function L4(e21, t26) {
  const r20 = A3(t26, e21);
  return r20 ? r20.name : e21;
}
function U5(e21, t26) {
  return e21 && e21.map((e22) => L4(e22, t26));
}
function A3(e21, t26) {
  return e21 && "function" == typeof e21.getField && t26 ? e21.getField(t26) ?? null : null;
}
function $(e21) {
  return `${e21}`.trim();
}
function S3({ attributes: e21, globalAttributes: t26, layer: r20, text: n27, expressionAttributes: i28, fieldInfoMap: o27 }) {
  return n27 ? G({ formattedAttributes: t26, template: z(n27, { ...t26, ...i28, ...e21 }, r20), fieldInfoMap: o27 }) : "";
}
function G({ formattedAttributes: e21, template: t26, fieldInfoMap: r20 }) {
  return $(D2(r2(r2(t26, (e22) => v9(e22, r20)), e21)));
}
function k(e21, t26, r20 = false) {
  const n27 = t26[e21];
  if ("string" == typeof n27) {
    const i28 = "%27", o27 = (r20 ? encodeURIComponent(n27) : n27).replace(h5, i28);
    t26[e21] = o27;
  }
}
function O(e21, t26 = false) {
  const r20 = { ...e21 };
  return Object.keys(r20).forEach((e22) => k(e22, r20, t26)), r20;
}
function P2(e21, t26, r20) {
  const i28 = (t26 = $(t26)) && "{" !== t26[0];
  return r2(e21, O(r20, i28 || false));
}
function _2(e21, t26) {
  return e21.replace(g5, (e22, r20, n27) => {
    const i28 = A3(t26, n27);
    return i28 ? `{${i28.name}}` : r20;
  });
}
function z(e21, t26, r20) {
  const n27 = _2(e21, r20);
  return n27 ? n27.replace(w8, (e22, r21, n28) => P2(e22, r21 || n28, t26)) : n27;
}
function H2(e21, t26) {
  if ("string" == typeof e21 && t26 && null == t26.dateFormat && (null != t26.places || null != t26.digitSeparator)) {
    const t27 = Number(e21);
    if (!isNaN(t27)) return t27;
  }
  return e21;
}
function Q(e21) {
  return "feature" === (e21 == null ? void 0 : e21.type);
}
function Z2(e21) {
  return !!(e21 == null ? void 0 : e21.layer);
}
function V(e21) {
  return "map-image" === (e21 == null ? void 0 : e21.type);
}
function B(e21, t26) {
  var _a;
  const r20 = t26.fieldInfos, n27 = t26.fieldName, i28 = (_a = J(r20, n27)) == null ? void 0 : _a.clone(), o27 = t26.preventPlacesFormatting, s22 = t26.layer, f17 = A3(s22, n27);
  if (i28 && "date" === (f17 == null ? void 0 : f17.type)) {
    const e22 = i28.format || new u2();
    e22.dateFormat = e22.dateFormat || "short-date-short-time", e22.dateTimeFormatOptions = !Z2(s22) && Q(s22) && s22.datesInUnknownTimezone || Z2(s22) && V(s22.layer) && s22.layer.datesInUnknownTimezone ? { timeZone: "UTC" } : null, i28.format = e22;
  }
  const d15 = i28 && i28.format;
  return "string" == typeof e21 && he(n27) && d15 ? d15.formatRasterPixelValue(e21) : "string" == typeof (e21 = H2(e21, d15)) || null == e21 || null == d15 ? re(e21) : o27 ? m(e21, { ...u(d15), minimumFractionDigits: 0, maximumFractionDigits: 20 }) : d15.format(e21);
}
function J(e21, t26) {
  if (!e21 || !e21.length || !t26) return;
  const r20 = t26.toLowerCase();
  let n27;
  return e21.some((e22) => !(!e22.fieldName || e22.fieldName.toLowerCase() !== r20) && (n27 = e22, true)), n27;
}
function K({ fieldName: e21, graphic: t26, layer: r20 }) {
  if (ue(e21)) return null;
  if (!r20 || "function" != typeof r20.getFeatureType) return null;
  const { typeIdField: n27 } = r20;
  if (!n27 || e21 !== n27) return null;
  const i28 = r20.getFeatureType(t26);
  return i28 ? i28.name : null;
}
function W2({ fieldName: e21, value: t26, graphic: r20, layer: n27 }) {
  if (ue(e21)) return null;
  if (!n27 || "function" != typeof n27.getFieldDomain) return null;
  const i28 = r20 && n27.getFieldDomain(e21, { feature: r20 });
  return i28 && "coded-value" === i28.type ? i28.getName(t26) : null;
}
function X(e21, t26) {
  const { creatorField: r20, creationDateField: n27, editorField: o27, editDateField: a32 } = e21;
  if (!t26) return;
  const l25 = t26[a32];
  if ("number" == typeof l25) {
    const e22 = t26[o27];
    return { type: "edit", date: L2(l25, T2), user: e22 };
  }
  const u18 = t26[n27];
  if ("number" == typeof u18) {
    const e22 = t26[r20];
    return { type: "create", date: L2(u18, T2), user: e22 };
  }
  return null;
}
function Y2(e21, t26) {
  const r20 = /* @__PURE__ */ new Map();
  return e21 ? (e21.forEach((e22) => {
    const n27 = L4(e22.fieldName, t26);
    e22.fieldName = n27, r20.set(n27.toLowerCase(), e22);
  }), r20) : r20;
}
function ee(e21) {
  const t26 = [];
  if (!e21) return t26;
  const { fieldInfos: r20, content: n27 } = e21;
  return r20 && t26.push(...r20), n27 && Array.isArray(n27) ? (n27.forEach((e22) => {
    if ("fields" === e22.type) {
      const r21 = e22 && e22.fieldInfos;
      r21 && t26.push(...r21);
    }
  }), t26) : t26;
}
function te(e21) {
  return e21.replace(F, (e22) => `&#${e22.charCodeAt(0)};`);
}
function re(e21) {
  return "string" == typeof e21 ? e21.replace(b6, '<br class="esri-text-new-line" />') : e21;
}
function ne(e21) {
  const { value: t26, fieldName: r20, fieldInfos: n27, fieldInfoMap: o27, layer: a32, graphic: l25 } = e21;
  if (null == t26) return "";
  const u18 = W2({ fieldName: r20, value: t26, graphic: l25, layer: a32 });
  if (u18) return u18;
  const s22 = K({ fieldName: r20, graphic: l25, layer: a32 });
  if (s22) return s22;
  if (o27.get(r20.toLowerCase())) return B(t26, { fieldInfos: n27 || Array.from(o27.values()), fieldName: r20, layer: a32 });
  const f17 = a32 && a32.fieldsIndex;
  return f17 && f17.isDateField(r20) ? L2(t26, T2) : re(t26);
}
function ie2({ fieldInfos: e21, attributes: t26, layer: r20, graphic: n27, fieldInfoMap: i28, relatedInfos: o27 }) {
  const a32 = {};
  return o27 == null ? void 0 : o27.forEach((t27) => ce({ attributes: a32, relatedInfo: t27, fieldInfoMap: i28, fieldInfos: e21, layer: r20 })), t26 && Object.keys(t26).forEach((o28) => {
    const l25 = t26[o28];
    a32[o28] = ne({ fieldName: o28, fieldInfos: e21, fieldInfoMap: i28, layer: r20, value: l25, graphic: n27 });
  }), a32;
}
async function oe(e21, t26) {
  var _a, _b;
  const { layer: r20, graphic: n27, outFields: i28, objectIds: o27, returnGeometry: a32, spatialReference: l25 } = e21, u18 = o27[0];
  if ("number" != typeof u18 && "string" != typeof u18) {
    const e22 = "Could not query required fields for the specified feature. The feature's ID is invalid.", t27 = { layer: r20, graphic: n27, objectId: u18, requiredFields: i28 };
    return m7.warn(e22, t27), null;
  }
  if (!((_b = (_a = v6(r20)) == null ? void 0 : _a.operations) == null ? void 0 : _b.supportsQuery)) {
    const e22 = "The specified layer cannot be queried. The following fields will not be available.", t27 = { layer: r20, graphic: n27, requiredFields: i28, returnGeometry: a32 };
    return m7.warn(e22, t27), null;
  }
  const s22 = r20.createQuery();
  s22.objectIds = o27, s22.outFields = (i28 == null ? void 0 : i28.length) ? i28 : [r20.objectIdField], s22.returnGeometry = !!a32, s22.returnZ = !!a32, s22.returnM = !!a32, s22.outSpatialReference = l25;
  return (await r20.queryFeatures(s22, t26)).features[0];
}
async function ae(e21) {
  var _a;
  if (!((_a = e21.expressionInfos) == null ? void 0 : _a.length)) return false;
  const t26 = await i9(), { arcadeUtils: { hasGeometryFunctions: r20 } } = t26;
  return r20(e21);
}
async function le({ graphic: e21, popupTemplate: t26, layer: r20, spatialReference: n27 }, i28) {
  if (!r20 || !t26) return;
  if ("function" == typeof r20.load && await r20.load(i28), !e21.attributes) return;
  const o27 = e21.attributes[r20.objectIdField];
  if (null == o27) return;
  const a32 = [o27], l25 = await t26.getRequiredFields(r20.fieldsIndex), u18 = xe(l25, e21), f17 = u18 ? [] : l25, c34 = t26.returnGeometry || await ae(t26);
  if (u18 && !c34) return;
  const d15 = await oe({ layer: r20, graphic: e21, outFields: f17, objectIds: a32, returnGeometry: c34, spatialReference: n27 }, i28);
  d15 && (d15.geometry && (e21.geometry = d15.geometry), d15.attributes && (e21.attributes = { ...e21.attributes, ...d15.attributes }));
}
function ue(e21 = "") {
  return !!e21 && e21.includes(E3);
}
function se(e21) {
  return e21 ? `${E3}${e21.layerId}/${e21.fieldName}` : "";
}
function fe({ attributes: e21, graphic: t26, relatedInfo: r20, fieldInfos: n27, fieldInfoMap: i28, layer: o27 }) {
  e21 && t26 && r20 && Object.keys(t26.attributes).forEach((a32) => {
    const l25 = se({ layerId: r20.relation.id.toString(), fieldName: a32 }), u18 = t26.attributes[a32];
    e21[l25] = ne({ fieldName: l25, fieldInfos: n27, fieldInfoMap: i28, layer: o27, value: u18, graphic: t26 });
  });
}
function ce({ attributes: e21, relatedInfo: t26, fieldInfoMap: r20, fieldInfos: n27, layer: i28 }) {
  e21 && t26 && (t26.relatedFeatures && t26.relatedFeatures && t26.relatedFeatures.forEach((o27) => fe({ attributes: e21, graphic: o27, relatedInfo: t26, fieldInfoMap: r20, fieldInfos: n27, layer: i28 })), t26.relatedStatsFeatures && t26.relatedStatsFeatures && t26.relatedStatsFeatures.forEach((o27) => fe({ attributes: e21, graphic: o27, relatedInfo: t26, fieldInfoMap: r20, fieldInfos: n27, layer: i28 })));
}
var de = (e21) => {
  if (!e21) return false;
  const t26 = e21.toUpperCase();
  return t26.includes("CURRENT_TIMESTAMP") || t26.includes("CURRENT_DATE") || t26.includes("CURRENT_TIME");
};
var pe = ({ layer: e21, method: t26, query: n27, definitionExpression: i28 }) => {
  var _a, _b;
  if (!((_b = (_a = e21.capabilities) == null ? void 0 : _a.query) == null ? void 0 : _b.supportsCacheHint) || "attachments" === t26) return;
  const o27 = r(n27.where) ? n27.where : null, a32 = r(n27.geometry) ? n27.geometry : null;
  de(i28) || de(o27) || "extent" === (a32 == null ? void 0 : a32.type) || "tile" === n27.resultType || (n27.cacheHint = true);
};
var me = ({ query: e21, layer: t26, method: r20 }) => {
  pe({ layer: t26, method: r20, query: e21, definitionExpression: `${t26.definitionExpression} ${t26.serviceDefinitionExpression}` });
};
var ye = ({ queryPayload: e21, layer: t26, method: r20 }) => {
  pe({ layer: t26, method: r20, query: e21, definitionExpression: `${t26.definitionExpression} ${t26.serviceDefinitionExpression}` });
};
function ge(e21, t26, r20) {
  return e21 && t26 && r20 ? he2(e21.allLayers, t26, r20) || he2(e21.allTables, t26, r20) : null;
}
function he2(e21, t26, r20) {
  return e21.find((e22) => e22 !== t26 && "feature" === e22.type && e22.url === t26.url && e22.layerId === r20.relatedTableId);
}

// node_modules/@arcgis/core/widgets/Attachments/AttachmentsViewModel.js
var f6 = { editing: false, operations: { add: true, update: true, delete: true } };
var u5 = j2.ofType(a10);
var y4 = class extends v3 {
  constructor(t26) {
    super(t26), this._getAttachmentsPromise = null, this._attachmentLayer = null, this.abilities = { ...f6 }, this.activeAttachmentInfo = null, this.activeFileInfo = null, this.attachmentInfos = new u5(), this.fileInfos = new j2(), this.graphic = null, this.mode = "view", this.filesEnabled = false, this.addHandles(l3(() => this.graphic, () => this._graphicChanged(), h));
  }
  destroy() {
    this._attachmentLayer = null, this.graphic = null;
  }
  castAbilities(t26) {
    return { ...f6, ...t26 };
  }
  get state() {
    return this._getAttachmentsPromise ? "loading" : this.graphic ? "ready" : "disabled";
  }
  get supportsResizeAttachments() {
    const { graphic: t26 } = this;
    if (!t26) return false;
    const e21 = t26.layer || t26.sourceLayer;
    return (e21 == null ? void 0 : e21.loaded) && "capabilities" in e21 && e21.capabilities && "operations" in e21.capabilities && "supportsResizeAttachments" in e21.capabilities.operations && e21.capabilities.operations.supportsResizeAttachments || false;
  }
  async getAttachments() {
    const { _attachmentLayer: t26, attachmentInfos: e21 } = this;
    if (!t26 || "function" != typeof t26.queryAttachments) throw new s3("invalid-layer", "getAttachments(): A valid layer is required.");
    const i28 = this._getObjectId(), a32 = new c4({ objectIds: [i28], returnMetadata: true }), r20 = [], s22 = t26.queryAttachments(a32).then((t27) => t27[i28] || r20).catch(() => r20);
    this._getAttachmentsPromise = s22, this.notifyChange("state");
    const o27 = await s22;
    return e21.removeAll(), o27.length && e21.addMany(o27), this._getAttachmentsPromise = null, this.notifyChange("state"), o27;
  }
  async addAttachment(t26, e21 = this.graphic) {
    var _a;
    const { _attachmentLayer: i28, attachmentInfos: a32, abilities: r20 } = this;
    if (!e21) throw new s3("invalid-graphic", "addAttachment(): A valid graphic is required.", { graphic: e21 });
    if (!t26) throw new s3("invalid-attachment", "addAttachment(): An attachment is required.", { attachment: t26 });
    if (!((_a = r20.operations) == null ? void 0 : _a.add)) throw new s3("invalid-abilities", "addAttachment(): add abilities are required.");
    if (!i28 || "function" != typeof i28.addAttachment) throw new s3("invalid-layer", "addAttachment(): A valid layer is required.");
    const s22 = i28.addAttachment(e21, t26).then((t27) => this._queryAttachment(t27.objectId, e21)), o27 = await s22;
    return a32.add(o27), o27;
  }
  async deleteAttachment(t26) {
    var _a;
    const { _attachmentLayer: e21, attachmentInfos: i28, graphic: a32, abilities: r20 } = this;
    if (!t26) throw new s3("invalid-attachment-info", "deleteAttachment(): An attachmentInfo is required.", { attachmentInfo: t26 });
    if (!((_a = r20.operations) == null ? void 0 : _a.delete)) throw new s3("invalid-abilities", "deleteAttachment(): delete abilities are required.");
    if (!e21 || "function" != typeof e21.deleteAttachments) throw new s3("invalid-layer", "deleteAttachment(): A valid layer is required.");
    if (!a32) throw new s3("invalid-graphic", "deleteAttachment(): A graphic is required.");
    const s22 = e21.deleteAttachments(a32, [t26.id]).then(() => t26), o27 = await s22;
    return i28.remove(o27), o27;
  }
  async updateAttachment(t26, e21 = this.activeAttachmentInfo) {
    var _a;
    const { _attachmentLayer: i28, attachmentInfos: a32, graphic: r20, abilities: s22 } = this;
    if (!t26) throw new s3("invalid-attachment", "updateAttachment(): An attachment is required.", { attachment: t26 });
    if (!e21) throw new s3("invalid-attachment-info", "updateAttachment(): An attachmentInfo is required.", { attachmentInfo: e21 });
    if (!((_a = s22.operations) == null ? void 0 : _a.update)) throw new s3("invalid-abilities", "updateAttachment(): Update abilities are required.");
    const o27 = a32.findIndex((t27) => t27 === e21);
    if (!i28 || "function" != typeof i28.updateAttachment) throw new s3("invalid-layer", "updateAttachment(): A valid layer is required.");
    if (!r20) throw new s3("invalid-graphic", "updateAttachment(): A graphic is required.");
    const c34 = i28.updateAttachment(r20, e21.id, t26).then((t27) => this._queryAttachment(t27.objectId)), h18 = await c34;
    return a32.splice(o27, 1, h18), h18;
  }
  async commitFiles() {
    return await Promise.all(this.fileInfos.items.map((t26) => this.addAttachment(t26.form))), this.fileInfos.removeAll(), this.getAttachments();
  }
  addFile(t26, e21) {
    if (!t26 || !e21) return null;
    const i28 = { file: t26, form: e21 };
    return this.fileInfos.add(i28), i28;
  }
  updateFile(t26, e21, i28 = this.activeFileInfo) {
    if (!t26 || !e21 || !i28) return null;
    const a32 = this.fileInfos.findIndex((t27) => i28 === t27);
    return a32 > -1 && this.fileInfos.splice(a32, 1, { file: t26, form: e21 }), this.fileInfos.items[a32];
  }
  deleteFile(t26) {
    const e21 = this.fileInfos.find((e22) => e22.file === t26);
    return e21 ? (this.fileInfos.remove(e21), e21) : null;
  }
  async _queryAttachment(t26, e21) {
    const { _attachmentLayer: i28 } = this;
    if (!t26 || !(i28 == null ? void 0 : i28.queryAttachments)) throw new s3("invalid-attachment-id", "Could not query attachment.");
    const a32 = this._getObjectId(e21), r20 = new c4({ objectIds: [a32], attachmentsWhere: `AttachmentId=${t26}`, returnMetadata: true });
    return i28.queryAttachments(r20).then((t27) => t27[a32][0]);
  }
  _getObjectId(t26 = this.graphic) {
    return (t26 == null ? void 0 : t26.getObjectId()) ?? null;
  }
  _graphicChanged() {
    this.graphic && (this._setAttachmentLayer(), this.getAttachments().catch(() => {
    }));
  }
  _setAttachmentLayer() {
    const { graphic: t26 } = this, e21 = j6(t26);
    this._attachmentLayer = e21 ? "scene" === e21.type && r(e21.associatedLayer) ? e21.associatedLayer : e21 : null;
  }
};
e([y()], y4.prototype, "abilities", void 0), e([s4("abilities")], y4.prototype, "castAbilities", null), e([y()], y4.prototype, "activeAttachmentInfo", void 0), e([y()], y4.prototype, "activeFileInfo", void 0), e([y({ readOnly: true, type: u5 })], y4.prototype, "attachmentInfos", void 0), e([y()], y4.prototype, "fileInfos", void 0), e([y({ type: g3 })], y4.prototype, "graphic", void 0), e([y()], y4.prototype, "mode", void 0), e([y({ readOnly: true })], y4.prototype, "state", null), e([y()], y4.prototype, "filesEnabled", void 0), e([y({ readOnly: true })], y4.prototype, "supportsResizeAttachments", null), y4 = e([a2("esri.widgets.Attachments.AttachmentsViewModel")], y4);
var A4 = y4;

// node_modules/@arcgis/core/widgets/Attachments/support/attachmentUtils.js
function e9(i28) {
  const e21 = i28.toLowerCase();
  return "image/bmp" === e21 || "image/emf" === e21 || "image/exif" === e21 || "image/gif" === e21 || "image/x-icon" === e21 || "image/jpeg" === e21 || "image/png" === e21 || "image/tiff" === e21 || "image/x-wmf" === e21;
}
function p6(e21) {
  const p30 = a7("esri/themes/base/images/files/");
  return e21 ? "text/plain" === e21 ? `${p30}text-32.svg` : "application/pdf" === e21 ? `${p30}pdf-32.svg` : "text/csv" === e21 ? `${p30}csv-32.svg` : "application/gpx+xml" === e21 ? `${p30}gpx-32.svg` : "application/x-dwf" === e21 ? `${p30}cad-32.svg` : "application/postscript" === e21 || "application/json" === e21 || "text/xml" === e21 || "model/vrml" === e21 ? `${p30}code-32.svg` : "application/x-zip-compressed" === e21 || "application/x-7z-compressed" === e21 || "application/x-gzip" === e21 || "application/x-tar" === e21 || "application/x-gtar" === e21 || "application/x-bzip2" === e21 || "application/gzip" === e21 || "application/x-compress" === e21 || "application/x-apple-diskimage" === e21 || "application/x-rar-compressed" === e21 || "application/zip" === e21 ? `${p30}zip-32.svg` : e21.includes("image/") ? `${p30}image-32.svg` : e21.includes("audio/") ? `${p30}sound-32.svg` : e21.includes("video/") ? `${p30}video-32.svg` : e21.includes("msexcel") || e21.includes("ms-excel") || e21.includes("spreadsheetml") ? `${p30}excel-32.svg` : e21.includes("msword") || e21.includes("ms-word") || e21.includes("wordprocessingml") ? `${p30}word-32.svg` : e21.includes("powerpoint") || e21.includes("presentationml") ? `${p30}report-32.svg` : `${p30}generic-32.svg` : `${p30}generic-32.svg`;
}

// node_modules/@arcgis/core/widgets/Attachments.js
var v10 = { addButton: true, addSubmitButton: true, cancelAddButton: true, cancelUpdateButton: true, deleteButton: true, errorMessage: true, progressBar: true, updateButton: true };
var y5 = "esri-attachments";
var w9 = "esri-button";
var A5 = { base: y5, loaderContainer: `${y5}__loader-container`, loader: `${y5}__loader`, fadeIn: `${y5}--fade-in`, container: `${y5}__container`, containerList: `${y5}__container--list`, containerPreview: `${y5}__container--preview`, actions: `${y5}__actions`, deleteButton: `${y5}__delete-button`, addAttachmentButton: `${y5}__add-attachment-button`, errorMessage: `${y5}__error-message`, items: `${y5}__items`, item: `${y5}__item`, itemButton: `${y5}__item-button`, itemMask: `${y5}__item-mask`, itemMaskIcon: `${y5}__item-mask--icon`, itemImage: `${y5}__image`, itemImageResizable: `${y5}__image--resizable`, itemLabel: `${y5}__label`, itemFilename: `${y5}__filename`, itemChevronIcon: `${y5}__item-chevron-icon`, itemLink: `${y5}__item-link`, itemLinkOverlay: `${y5}__item-link-overlay`, itemLinkOverlayIcon: `${y5}__item-link-overlay-icon`, itemEditIcon: `${y5}__item-edit-icon`, itemAddIcon: `${y5}__item-add-icon`, itemAddButton: `${y5}__item-add-button`, formNode: `${y5}__form-node`, fileFieldset: `${y5}__file-fieldset`, fileLabel: `${y5}__file-label`, fileName: `${y5}__file-name`, fileInput: `${y5}__file-input`, metadata: `${y5}__metadata`, metadataFieldset: `${y5}__metadata-fieldset`, progressBar: `${y5}__progress-bar`, esriWidget: "esri-widget", esriButton: w9, buttonDisabled: `${w9}--disabled`, esriButtonSecondary: `${w9}--secondary`, esriButtonTertiary: `${w9}--tertiary`, esriButtonThird: `${w9}--third`, esriButtonSmall: `${w9}--small`, esriButtonHalf: `${w9}--half`, empty: "esri-widget__content--empty", iconExternalLink: "esri-icon-link-external", iconEdit: "esri-icon-edit", iconRight: "esri-icon-right", iconLeft: "esri-icon-left", iconPlus: "esri-icon-plus" };
var F2 = window.CSS;
var k2 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this.displayType = "auto", this.messages = null, this.messagesUnits = null, this.selectedFile = null, this.submitting = false, this.viewModel = null, this.visibleElements = { ...v10 }, this._supportsImageOrientation = F2 && F2.supports && F2.supports("image-orientation", "from-image"), this._addAttachmentForm = null, this._updateAttachmentForm = null;
  }
  initialize() {
    this.viewModel || (this.viewModel = new A4()), this.addHandles([a6(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.attachmentInfos;
    }, "change", () => this.scheduleRender()), a6(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.fileInfos;
    }, "change", () => this.scheduleRender()), l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.mode;
    }, () => this._modeChanged(), h)]);
  }
  loadDependencies() {
    return Promise.all([import("./calcite-icon-AXY32QDW.js")]);
  }
  get abilities() {
    return this.viewModel.abilities;
  }
  set abilities(e21) {
    this.viewModel.abilities = e21;
  }
  get effectiveDisplayType() {
    const { displayType: e21 } = this;
    return e21 && "auto" !== e21 ? e21 : this.viewModel.supportsResizeAttachments ? "preview" : "list";
  }
  get graphic() {
    return this.viewModel.graphic;
  }
  set graphic(e21) {
    this.viewModel.graphic = e21;
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(e21) {
    this._overrideIfSome("label", e21);
  }
  castVisibleElements(e21) {
    return { ...v10, ...e21 };
  }
  addAttachment() {
    const { _addAttachmentForm: e21, viewModel: s22 } = this;
    return this._set("submitting", true), this._set("error", null), s22.addAttachment(e21).then((e22) => (this._set("submitting", false), this._set("error", null), s22.mode = "view", e22)).catch((e22) => {
      throw this._set("submitting", false), this._set("error", new s3("attachments:add-attachment", this.messages.addErrorMessage, e22)), e22;
    });
  }
  deleteAttachment(e21) {
    const { viewModel: s22 } = this;
    return this._set("submitting", true), this._set("error", null), s22.deleteAttachment(e21).then((e22) => (this._set("submitting", false), this._set("error", null), s22.mode = "view", e22)).catch((e22) => {
      throw this._set("submitting", false), this._set("error", new s3("attachments:delete-attachment", this.messages.deleteErrorMessage, e22)), e22;
    });
  }
  updateAttachment() {
    const { viewModel: e21 } = this, { _updateAttachmentForm: s22 } = this;
    return this._set("submitting", true), this._set("error", null), e21.updateAttachment(s22).then((t26) => (this._set("submitting", false), this._set("error", null), e21.mode = "view", t26)).catch((e22) => {
      throw this._set("submitting", false), this._set("error", new s3("attachments:update-attachment", this.messages.updateErrorMessage, e22)), e22;
    });
  }
  addFile() {
    const e21 = this.viewModel.addFile(this.selectedFile, this._addAttachmentForm);
    return this.viewModel.mode = "view", e21;
  }
  updateFile() {
    const { viewModel: e21 } = this, t26 = e21.updateFile(this.selectedFile, this._updateAttachmentForm, e21.activeFileInfo);
    return e21.mode = "view", t26;
  }
  deleteFile(e21) {
    var _a;
    const t26 = this.viewModel.deleteFile(e21 || ((_a = this.viewModel.activeFileInfo) == null ? void 0 : _a.file));
    return this.viewModel.mode = "view", t26;
  }
  render() {
    const { submitting: e21, viewModel: t26 } = this, { state: s22 } = t26;
    return n5("div", { class: this.classes(A5.base, A5.esriWidget) }, e21 ? this.renderProgressBar() : null, "loading" === s22 ? this.renderLoading() : this.renderAttachments(), this.renderErrorMessage());
  }
  renderErrorMessage() {
    const { error: e21, visibleElements: t26 } = this;
    return e21 && t26.errorMessage ? n5("div", { key: "error-message", class: A5.errorMessage }, e21.message) : null;
  }
  renderAttachments() {
    const { activeFileInfo: e21, mode: t26, activeAttachmentInfo: s22 } = this.viewModel;
    return "add" === t26 ? this.renderAddForm() : "edit" === t26 ? this.renderDetailsForm(s22 || e21) : this.renderAttachmentContainer();
  }
  renderLoading() {
    return n5("div", { class: A5.loaderContainer, key: "loader" }, n5("div", { class: A5.loader }));
  }
  renderProgressBar() {
    return this.visibleElements.progressBar ? n5("div", { class: A5.progressBar, key: "progress-bar" }) : null;
  }
  renderAddForm() {
    const { submitting: e21, selectedFile: t26 } = this, s22 = e21 || !t26, i28 = this.visibleElements.cancelAddButton ? n5("button", { type: "button", bind: this, disabled: e21, onclick: this._cancelForm, class: this.classes(A5.esriButton, A5.esriButtonTertiary, A5.esriButtonSmall, A5.esriButtonHalf, e21 && A5.buttonDisabled) }, this.messages.cancel) : null, a32 = this.visibleElements.addSubmitButton ? n5("button", { type: "submit", disabled: s22, class: this.classes(A5.esriButton, A5.esriButtonSecondary, A5.esriButtonSmall, A5.esriButtonHalf, { [A5.buttonDisabled]: s22 }) }, this.messages.add) : null, n27 = t26 ? n5("span", { key: "file-name", class: A5.fileName }, t26.name) : null, r20 = n5("form", { bind: this, afterCreate: v5, afterRemoved: h2, "data-node-ref": "_addAttachmentForm", onsubmit: this._submitAddAttachment }, n5("fieldset", { class: A5.fileFieldset }, n27, n5("label", { class: this.classes(A5.fileLabel, A5.esriButton, A5.esriButtonSecondary) }, t26 ? this.messages.changeFile : this.messages.selectFile, n5("input", { class: A5.fileInput, type: "file", name: "attachment", bind: this, onchange: this._handleFileInputChange }))), a32, i28);
    return n5("div", { key: "add-form-container", class: A5.formNode }, r20);
  }
  renderDetailsForm(e21) {
    var _a, _b, _c;
    const { visibleElements: t26, viewModel: i28, selectedFile: a32, submitting: n27 } = this, { abilities: l25 } = i28, o27 = n27 || !a32;
    let d15, c34, m24, h18;
    a32 ? (d15 = a32.type, c34 = a32.name, m24 = a32.size) : e21 && "file" in e21 ? (d15 = e21.file.type, c34 = e21.file.name, m24 = e21.file.size) : e21 && "contentType" in e21 && (d15 = e21.contentType, c34 = e21.name, m24 = e21.size, h18 = e21.url);
    const u18 = l25.editing && ((_a = l25.operations) == null ? void 0 : _a.delete) && t26.deleteButton ? n5("button", { key: "delete-button", type: "button", disabled: n27, bind: this, onclick: (t27) => this._submitDeleteAttachment(t27, e21), class: this.classes(A5.esriButton, A5.esriButtonSmall, A5.esriButtonTertiary, A5.deleteButton, { [A5.buttonDisabled]: n27 }) }, this.messages.delete) : void 0, b16 = l25.editing && ((_b = l25.operations) == null ? void 0 : _b.update) && t26.updateButton ? n5("button", { disabled: o27, key: "update-button", type: "submit", class: this.classes(A5.esriButton, A5.esriButtonSmall, A5.esriButtonThird, { [A5.buttonDisabled]: o27 }) }, this.messages.update) : void 0, f17 = this.visibleElements.cancelUpdateButton ? n5("button", { disabled: n27, key: "cancel-button", type: "button", bind: this, onclick: this._cancelForm, class: this.classes(A5.esriButton, A5.esriButtonSmall, A5.esriButtonTertiary, A5.esriButtonThird, { [A5.buttonDisabled]: n27 }) }, this.messages.cancel) : void 0, v16 = l25.editing && ((_c = l25.operations) == null ? void 0 : _c.update) ? n5("fieldset", { key: "file", class: A5.fileFieldset }, n5("span", { key: "file-name", class: A5.fileName }, c34), n5("label", { class: this.classes(A5.fileLabel, A5.esriButton, A5.esriButtonSecondary) }, this.messages.changeFile, n5("input", { class: A5.fileInput, type: "file", name: "attachment", bind: this, onchange: this._handleFileInputChange }))) : void 0, y16 = n5("fieldset", { key: "size", class: A5.metadataFieldset }, n5("label", null, v8(this.messagesUnits, m24 ?? 0))), w18 = n5("fieldset", { key: "content-type", class: A5.metadataFieldset }, n5("label", null, d15)), F7 = r(h18) ? n5("a", { class: A5.itemLink, href: h18, rel: "noreferrer", target: "_blank" }, this.renderImageMask(e21, 400), n5("div", { class: A5.itemLinkOverlay }, n5("span", { class: A5.itemLinkOverlayIcon }, n5("calcite-icon", { icon: "launch" })))) : this.renderImageMask(e21, 400), k4 = n5("form", { bind: this, afterCreate: v5, afterRemoved: h2, "data-node-ref": "_updateAttachmentForm", onsubmit: (t27) => this._submitUpdateAttachment(t27, e21) }, n5("div", { class: A5.metadata }, y16, w18), v16, n5("div", { class: A5.actions }, u18, f17, b16));
    return n5("div", { key: "edit-form-container", class: A5.formNode }, F7, k4);
  }
  renderImageMask(e21, t26) {
    return e21 ? "file" in e21 ? this.renderGenericImageMask(e21.file.name, e21.file.type) : this.renderImageMaskForAttachment(e21, t26) : null;
  }
  renderGenericImageMask(e21, t26) {
    const { supportsResizeAttachments: s22 } = this.viewModel, i28 = p6(t26), a32 = { [A5.itemImageResizable]: s22 };
    return n5("div", { class: this.classes(A5.itemMaskIcon, A5.itemMask) }, n5("img", { title: e21, alt: e21, src: i28, class: this.classes(a32, A5.itemImage) }));
  }
  renderImageMaskForAttachment(e21, t26) {
    const { supportsResizeAttachments: s22 } = this.viewModel;
    if (!e21) return null;
    const { contentType: i28, name: a32, url: n27 } = e21;
    if (!s22 || !e9(i28)) return this.renderGenericImageMask(a32, i28);
    const r20 = this._getCSSTransform(e21), l25 = r20 ? { transform: r20, "image-orientation": "none" } : {}, o27 = `${n27}${(n27 == null ? void 0 : n27.includes("?")) ? "&" : "?"}w=${t26}`, d15 = { [A5.itemImageResizable]: s22 };
    return n5("div", { class: this.classes(A5.itemMask) }, n5("img", { styles: l25, alt: a32, title: a32, src: o27, class: this.classes(d15, A5.itemImage) }));
  }
  renderFile(e21) {
    const { file: t26 } = e21;
    return n5("li", { class: A5.item, key: t26 }, n5("button", { key: "details-button", bind: this, class: A5.itemButton, title: this.messages.attachmentDetails, "aria-label": this.messages.attachmentDetails, onclick: () => this._startEditFile(e21), type: "button" }, this.renderImageMask(e21), n5("label", { class: A5.itemLabel }, n5("span", { class: A5.itemFilename }, t26.name || this.messages.noTitle), n5("span", { "aria-hidden": "true", class: this.classes(A5.itemChevronIcon, f4(this.container) ? A5.iconLeft : A5.iconRight) }))));
  }
  renderAttachmentInfo({ attachmentInfo: e21, displayType: t26 }) {
    const { viewModel: s22, effectiveDisplayType: i28 } = this, { abilities: a32, supportsResizeAttachments: n27 } = s22, { contentType: r20, name: l25, url: o27 } = e21, d15 = this.renderImageMask(e21, "list" === t26 ? 48 : 400), c34 = a32.editing ? n5("span", { "aria-hidden": "true", class: this.classes(A5.itemChevronIcon, f4(this.container) ? A5.iconLeft : A5.iconRight) }) : null, m24 = [d15, "preview" === i28 && n27 && e9(r20) ? null : n5("label", { class: A5.itemLabel }, n5("span", { class: A5.itemFilename }, l25 || this.messages.noTitle), c34)], h18 = a32.editing ? n5("button", { key: "details-button", bind: this, class: A5.itemButton, title: this.messages.attachmentDetails, "aria-label": this.messages.attachmentDetails, "data-attachment-info-id": e21.id, onclick: () => this._startEditAttachment(e21), type: "button" }, m24) : n5("a", { key: "details-link", class: A5.itemButton, href: o27 ?? void 0, target: "_blank" }, m24);
    return n5("li", { class: A5.item, key: e21 }, h18);
  }
  renderAttachmentContainer() {
    var _a;
    const { effectiveDisplayType: e21, viewModel: t26, visibleElements: s22 } = this, { attachmentInfos: i28, abilities: a32, fileInfos: n27 } = t26, r20 = !!(i28 == null ? void 0 : i28.length), l25 = !!(n27 == null ? void 0 : n27.length), o27 = { [A5.containerList]: "preview" !== e21, [A5.containerPreview]: "preview" === e21 }, d15 = a32.editing && ((_a = a32.operations) == null ? void 0 : _a.add) && s22.addButton ? n5("button", { bind: this, onclick: () => this._startAddAttachment(), class: this.classes(A5.esriButton, A5.esriButtonTertiary, A5.addAttachmentButton), type: "button" }, n5("span", { "aria-hidden": "true", class: this.classes(A5.itemAddIcon, A5.iconPlus) }), this.messages.add) : void 0, c34 = r20 ? n5("ul", { key: "attachments-list", class: A5.items }, i28.toArray().map((t27) => this.renderAttachmentInfo({ attachmentInfo: t27, displayType: e21 }))) : void 0, m24 = l25 ? n5("ul", { key: "file-list", class: A5.items }, n27.toArray().map((e22) => this.renderFile(e22))) : void 0, h18 = l25 || r20 ? void 0 : n5("div", { class: A5.empty }, this.messages.noAttachments);
    return n5("div", { key: "attachments-container", class: this.classes(A5.container, o27) }, c34, m24, h18, d15);
  }
  _modeChanged() {
    this._set("error", null), this._set("selectedFile", null);
  }
  _handleFileInputChange(e21) {
    const t26 = e21.target, s22 = t26 && t26.files && t26.files.item(0);
    this._set("selectedFile", s22);
  }
  _submitDeleteAttachment(e21, t26) {
    e21.preventDefault(), t26 && ("file" in t26 ? this.deleteFile(t26.file) : t26 && this.deleteAttachment(t26));
  }
  _submitAddAttachment(e21) {
    e21.preventDefault(), this.viewModel.filesEnabled ? this.addFile() : this.addAttachment();
  }
  _submitUpdateAttachment(e21, t26) {
    e21.preventDefault(), t26 && "file" in t26 ? this.updateFile() : this.updateAttachment();
  }
  _startEditAttachment(e21) {
    const { viewModel: t26 } = this;
    t26.activeFileInfo = null, t26.activeAttachmentInfo = e21, t26.mode = "edit";
  }
  _startEditFile(e21) {
    const { viewModel: t26 } = this;
    t26.activeAttachmentInfo = null, t26.activeFileInfo = e21, t26.mode = "edit";
  }
  _startAddAttachment() {
    this.viewModel.mode = "add";
  }
  _cancelForm(e21) {
    e21.preventDefault(), this.viewModel.mode = "view";
  }
  _getCSSTransform(e21) {
    const { orientationInfo: t26 } = e21;
    return !this._supportsImageOrientation && t26 ? [t26.rotation ? `rotate(${t26.rotation}deg)` : "", t26.mirrored ? "scaleX(-1)" : ""].join(" ") : "";
  }
};
e([y()], k2.prototype, "abilities", null), e([y()], k2.prototype, "displayType", void 0), e([y({ readOnly: true })], k2.prototype, "effectiveDisplayType", null), e([y()], k2.prototype, "graphic", null), e([y()], k2.prototype, "label", null), e([y(), e4("esri/widgets/Attachments/t9n/Attachments")], k2.prototype, "messages", void 0), e([y(), e4("esri/core/t9n/Units")], k2.prototype, "messagesUnits", void 0), e([y({ readOnly: true })], k2.prototype, "selectedFile", void 0), e([y({ readOnly: true })], k2.prototype, "submitting", void 0), e([y({ readOnly: true })], k2.prototype, "error", void 0), e([y({ type: A4 })], k2.prototype, "viewModel", void 0), e([y()], k2.prototype, "visibleElements", void 0), e([s4("visibleElements")], k2.prototype, "castVisibleElements", null), k2 = e([a2("esri.widgets.Attachments")], k2);
var B2 = k2;

// node_modules/@arcgis/core/widgets/Feature/FeatureAttachments/FeatureAttachmentsViewModel.js
var o10 = class extends A4 {
  constructor(t26) {
    super(t26), this.description = null, this.title = null;
  }
};
e([y()], o10.prototype, "description", void 0), e([y()], o10.prototype, "title", void 0), o10 = e([a2("esri.widgets.Feature.FeatureAttachments.FeatureAttachmentsViewModel")], o10);
var c13 = o10;

// node_modules/@arcgis/core/widgets/Feature/support/FeatureElementInfo.js
var p7 = "esri-feature-element-info";
var n11 = { base: p7, title: `${p7}__title`, description: `${p7}__description` };
var c14 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this.description = null, this.headingLevel = 2, this.title = null;
  }
  render() {
    return n5("div", { class: n11.base }, this.renderTitle(), this.renderDescription());
  }
  renderTitle() {
    const { title: e21 } = this;
    return e21 ? n5(n8, { level: this.headingLevel, class: n11.title }, e21) : null;
  }
  renderDescription() {
    const { description: e21 } = this;
    return e21 ? n5("div", { key: "description", class: n11.description }, e21) : null;
  }
};
e([y()], c14.prototype, "description", void 0), e([y()], c14.prototype, "headingLevel", void 0), e([y()], c14.prototype, "title", void 0), c14 = e([a2("esri.widgets.Feature.support.FeatureElementInfo")], c14);
var l10 = c14;

// node_modules/@arcgis/core/widgets/Feature/FeatureAttachments.js
var d5 = { base: "esri-feature-attachments" };
var c15 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._featureElementInfo = null, this.attachmentsWidget = new B2(), this.headingLevel = 2, this.viewModel = new c13();
  }
  initialize() {
    this._featureElementInfo = new l10(), this.addHandles([l3(() => {
      var _a, _b;
      return [(_a = this.viewModel) == null ? void 0 : _a.description, (_b = this.viewModel) == null ? void 0 : _b.title, this.headingLevel];
    }, () => this._setupFeatureElementInfo(), h), l3(() => this.viewModel, (e21) => this.attachmentsWidget.viewModel = e21, h)]);
  }
  destroy() {
    var _a;
    this.attachmentsWidget.destroy(), (_a = this._featureElementInfo) == null ? void 0 : _a.destroy();
  }
  get description() {
    return this.viewModel.description;
  }
  set description(e21) {
    this.viewModel.description = e21;
  }
  get displayType() {
    return this.attachmentsWidget.displayType;
  }
  set displayType(e21) {
    this.attachmentsWidget.displayType = e21;
  }
  get graphic() {
    return this.viewModel.graphic;
  }
  set graphic(e21) {
    this.viewModel.graphic = e21;
  }
  get title() {
    return this.viewModel.title;
  }
  set title(e21) {
    this.viewModel.title = e21;
  }
  render() {
    var _a;
    const { attachmentsWidget: e21 } = this;
    return n5("div", { class: d5.base }, (_a = this._featureElementInfo) == null ? void 0 : _a.render(), e21 == null ? void 0 : e21.render());
  }
  _setupFeatureElementInfo() {
    var _a;
    const { description: e21, title: t26, headingLevel: i28 } = this;
    (_a = this._featureElementInfo) == null ? void 0 : _a.set({ description: e21, title: t26, headingLevel: i28 });
  }
};
e([y({ readOnly: true })], c15.prototype, "attachmentsWidget", void 0), e([y()], c15.prototype, "description", null), e([y()], c15.prototype, "displayType", null), e([y()], c15.prototype, "graphic", null), e([y()], c15.prototype, "headingLevel", void 0), e([y()], c15.prototype, "title", null), e([y({ type: c13 })], c15.prototype, "viewModel", void 0), c15 = e([a2("esri.widgets.Feature.FeatureAttachments")], c15);
var h6 = c15;

// node_modules/@arcgis/core/widgets/Feature/FeatureContent/FeatureContentViewModel.js
var p8 = class extends a9(v3) {
  constructor(t26) {
    super(t26), this._loadingPromise = null, this.created = null, this.creator = null, this.destroyer = null, this.graphic = null, this.handles.add(l3(() => this.creator, (t27) => {
      this._destroyContent(), this._createContent(t27);
    }, h));
  }
  destroy() {
    this._destroyContent();
  }
  get state() {
    return this._loadingPromise ? "loading" : "ready";
  }
  _destroyContent() {
    const { created: t26, graphic: e21, destroyer: r20 } = this;
    t26 && e21 && (x2(r20, { graphic: e21 }).catch(() => null), this._set("created", null));
  }
  async _createContent(t26) {
    const e21 = this.graphic;
    if (!e21 || !t26) return;
    const r20 = x2(t26, { graphic: e21 }).catch(() => null);
    this._loadingPromise = r20, this.notifyChange("state");
    const o27 = await r20;
    r20 === this._loadingPromise && (this._loadingPromise = null, this.notifyChange("state"), this._set("created", o27));
  }
};
e([y({ readOnly: true })], p8.prototype, "created", void 0), e([y()], p8.prototype, "creator", void 0), e([y()], p8.prototype, "destroyer", void 0), e([y({ type: g3 })], p8.prototype, "graphic", void 0), e([y({ readOnly: true })], p8.prototype, "state", null), p8 = e([a2("esri.widgets.Feature.FeatureContent.FeatureContentViewModel")], p8);
var l11 = p8;

// node_modules/@arcgis/core/widgets/Feature/FeatureContent.js
var n12 = "esri-feature-content";
var c16 = { base: n12, loaderContainer: `${n12}__loader-container`, loader: `${n12}__loader` };
var l12 = class extends W {
  constructor(e21, r20) {
    super(e21, r20), this.viewModel = null, this._addTargetToAnchors = (e22) => {
      Array.from(e22.querySelectorAll("a")).forEach((e23) => {
        C2(e23.href) && !e23.hasAttribute("target") && e23.setAttribute("target", "_blank");
      });
    };
  }
  get creator() {
    var _a;
    return (_a = this.viewModel) == null ? void 0 : _a.creator;
  }
  set creator(e21) {
    this.viewModel && (this.viewModel.creator = e21);
  }
  get graphic() {
    var _a;
    return (_a = this.viewModel) == null ? void 0 : _a.graphic;
  }
  set graphic(e21) {
    this.viewModel && (this.viewModel.graphic = e21);
  }
  renderLoading() {
    return n5("div", { class: c16.loaderContainer, key: "loader" }, n5("div", { class: c16.loader }));
  }
  renderCreated() {
    var _a;
    const e21 = (_a = this.viewModel) == null ? void 0 : _a.created;
    return e21 ? e21 instanceof HTMLElement ? n5("div", { key: e21, bind: e21, afterCreate: this._attachToNode }) : e6(e21) ? n5("div", { key: e21 }, !e21.destroyed && e21.render()) : n5("div", { key: e21, innerHTML: e21, afterCreate: this._addTargetToAnchors }) : null;
  }
  render() {
    var _a;
    const e21 = (_a = this.viewModel) == null ? void 0 : _a.state;
    return n5("div", { class: c16.base }, "loading" === e21 ? this.renderLoading() : this.renderCreated());
  }
  _attachToNode(e21) {
    const r20 = this;
    e21.appendChild(r20);
  }
};
e([y()], l12.prototype, "creator", null), e([y()], l12.prototype, "graphic", null), e([y({ type: l11 })], l12.prototype, "viewModel", void 0), l12 = e([a2("esri.widgets.Feature.FeatureContent")], l12);
var p9 = l12;

// node_modules/@arcgis/core/widgets/Feature/FeatureFields/FeatureFieldsViewModel.js
var l13 = class extends v3 {
  constructor(o27) {
    super(o27), this.attributes = null, this.expressionInfos = null, this.description = null, this.fieldInfos = null, this.title = null;
  }
  get formattedFieldInfos() {
    const { expressionInfos: o27, fieldInfos: e21 } = this, s22 = [];
    return e21 == null ? void 0 : e21.forEach((e22) => {
      if (!(!e22.hasOwnProperty("visible") || e22.visible)) return;
      const t26 = e22.clone();
      t26.label = R4(t26, o27), s22.push(t26);
    }), s22;
  }
};
e([y()], l13.prototype, "attributes", void 0), e([y({ type: [i8] })], l13.prototype, "expressionInfos", void 0), e([y()], l13.prototype, "description", void 0), e([y({ type: [c5] })], l13.prototype, "fieldInfos", void 0), e([y({ readOnly: true })], l13.prototype, "formattedFieldInfos", null), e([y()], l13.prototype, "title", void 0), l13 = e([a2("esri.widgets.Feature.FeatureFields.FeatureFieldsViewModel")], l13);
var n13 = l13;

// node_modules/@arcgis/core/widgets/support/uriUtils.js
var s10 = [{ pattern: /^\s*(https?:\/\/([^\s]+))\s*$/i, target: "_blank", label: "{messages.view}" }, { pattern: /^\s*(tel:([^\s]+))\s*$/i, label: "{hierPart}" }, { pattern: /^\s*(mailto:([^\s]+))\s*$/i, label: "{hierPart}" }, { pattern: /^\s*(arcgis-appstudio-player:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "App Studio Player" }, { pattern: /^\s*(arcgis-collector:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Collector" }, { pattern: /^\s*(arcgis-explorer:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Explorer" }, { pattern: /^\s*(arcgis-navigator:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Navigator" }, { pattern: /^\s*(arcgis-survey123:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Survey123" }, { pattern: /^\s*(arcgis-trek2there:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Trek2There" }, { pattern: /^\s*(arcgis-workforce:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Workforce" }, { pattern: /^\s*(iform:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "iForm" }, { pattern: /^\s*(flow:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "FlowFinity" }, { pattern: /^\s*(lfmobile:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Laserfische" }, { pattern: /^\s*(mspbi:\/\/([^\s]+))\s*$/i, label: "{messages.openInApp}", appName: "Microsoft Power Bi" }];
function a15(e21) {
  let a32 = null;
  return s10.some((s22) => (s22.pattern.test(e21) && (a32 = s22), !!a32)), a32;
}
function p10(s22, p30) {
  if ("string" != typeof p30 || !p30) return p30;
  const r20 = a15(p30);
  if (!r20) return p30;
  const t26 = p30.match(r20.pattern), n27 = t26 && t26[2], l25 = r2(r2(r20.label, { messages: s22, hierPart: n27 }), { appName: r20.appName }), i28 = r20.target ? ` target="${r20.target}"` : "", o27 = "_blank" === r20.target ? ' rel="noreferrer"' : "";
  return p30.replace(r20.pattern, `<a${i28} href="$1"${o27}>${l25}</a>`);
}

// node_modules/@arcgis/core/widgets/Feature/FeatureFields.js
var f7 = "esri-feature-fields";
var u6 = { base: f7, fieldHeader: `${f7}__field-header`, fieldData: `${f7}__field-data`, fieldDataDate: `${f7}__field-data--date`, esriTable: "esri-widget__table" };
var m8 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._featureElementInfo = null, this.viewModel = new n13(), this.messages = null, this.messagesURIUtils = null;
  }
  initialize() {
    this._featureElementInfo = new l10(), this.addHandles(l3(() => {
      var _a, _b;
      return [(_a = this.viewModel) == null ? void 0 : _a.description, (_b = this.viewModel) == null ? void 0 : _b.title];
    }, () => this._setupFeatureElementInfo(), h));
  }
  destroy() {
    var _a;
    (_a = this._featureElementInfo) == null ? void 0 : _a.destroy();
  }
  get attributes() {
    return this.viewModel.attributes;
  }
  set attributes(e21) {
    this.viewModel.attributes = e21;
  }
  get description() {
    return this.viewModel.description;
  }
  set description(e21) {
    this.viewModel.description = e21;
  }
  get expressionInfos() {
    return this.viewModel.expressionInfos;
  }
  set expressionInfos(e21) {
    this.viewModel.expressionInfos = e21;
  }
  get fieldInfos() {
    return this.viewModel.fieldInfos;
  }
  set fieldInfos(e21) {
    this.viewModel.fieldInfos = e21;
  }
  get title() {
    return this.viewModel.title;
  }
  set title(e21) {
    this.viewModel.title = e21;
  }
  renderFieldInfo(e21, t26) {
    const { attributes: s22 } = this.viewModel, i28 = e21.fieldName, r20 = e21.label || i28, o27 = s22 ? null == s22[i28] ? "" : s22[i28] : "", l25 = !(!e21.format || !e21.format.dateFormat), n27 = "number" == typeof o27 && !l25 ? this._forceLTR(o27) : p10(this.messagesURIUtils, o27), a32 = { [u6.fieldDataDate]: l25 };
    return n5("tr", { key: `fields-element-info-row-${i28}-${t26}` }, n5("th", { key: `fields-element-info-row-header-${i28}-${t26}`, class: u6.fieldHeader, innerHTML: r20 }), n5("td", { key: `fields-element-info-row-data-${i28}-${t26}`, class: this.classes(u6.fieldData, a32), innerHTML: n27 }));
  }
  renderFields() {
    const { formattedFieldInfos: e21 } = this.viewModel;
    return (e21 == null ? void 0 : e21.length) ? n5("table", { class: u6.esriTable, summary: this.messages.fieldsSummary }, n5("tbody", null, e21.map((e22, t26) => this.renderFieldInfo(e22, t26)))) : null;
  }
  render() {
    var _a;
    return n5("div", { class: u6.base }, (_a = this._featureElementInfo) == null ? void 0 : _a.render(), this.renderFields());
  }
  _setupFeatureElementInfo() {
    var _a;
    const { description: e21, title: t26 } = this;
    (_a = this._featureElementInfo) == null ? void 0 : _a.set({ description: e21, title: t26 });
  }
  _forceLTR(e21) {
    return `&lrm;${e21}`;
  }
};
e([y()], m8.prototype, "attributes", null), e([y()], m8.prototype, "description", null), e([y()], m8.prototype, "expressionInfos", null), e([y()], m8.prototype, "fieldInfos", null), e([y()], m8.prototype, "title", null), e([y({ type: n13, nonNullable: true })], m8.prototype, "viewModel", void 0), e([y(), e4("esri/widgets/Feature/t9n/Feature")], m8.prototype, "messages", void 0), e([y(), e4("esri/widgets/support/t9n/uriUtils")], m8.prototype, "messagesURIUtils", void 0), m8 = e([a2("esri.widgets.Feature.FeatureFields")], m8);
var c17 = m8;

// node_modules/@arcgis/core/widgets/Feature/support/relatedFeatureUtils.js
var c18 = "esri.widgets.Feature.support.relatedFeatureUtils";
var f8 = s2.getLogger(c18);
var d6 = /* @__PURE__ */ new Map();
function m9(e21) {
  if (!ue(e21)) return null;
  const [t26, r20] = e21.split("/").slice(1);
  return { layerId: t26, fieldName: r20 };
}
function y6(e21, t26) {
  if (!t26.relationships) return null;
  let r20 = null;
  const { relationships: s22 } = t26;
  return s22.some((t27) => t27.id === parseInt(e21, 10) && (r20 = t27, true)), r20;
}
function j7({ originRelationship: e21, relationships: t26, layerId: r20 }) {
  let s22 = null;
  return t26 && t26.some((t27) => (`${t27.relatedTableId}` === r20 && t27.id === (e21 == null ? void 0 : e21.id) && (s22 = t27), !!s22)), s22;
}
function h7(e21, t26) {
  const r20 = t26.toLowerCase();
  for (const s22 in e21) if (s22.toLowerCase() === r20) return e21[s22];
  return null;
}
function g6(e21, t26) {
  const r20 = y6(e21, t26);
  if (!r20) return;
  return { url: `${t26.url}/${r20.relatedTableId}`, sourceSpatialReference: t26.spatialReference, relation: r20, relatedFields: [], outStatistics: [] };
}
function F3(e21, t26) {
  if (!t26) return;
  if (!e21) return;
  const { features: r20, statsFeatures: s22 } = e21, o27 = r20 && r20.value;
  t26.relatedFeatures = o27 ? o27.features : [];
  const i28 = s22 && s22.value;
  t26.relatedStatsFeatures = i28 ? i28.features : [];
}
function I3(e21, t26, r20, s22) {
  var _a;
  const o27 = new d2();
  return o27.outFields = ["*"], o27.relationshipId = "number" == typeof t26.id ? t26.id : parseInt(t26.id, 10), o27.objectIds = [e21.attributes[r20.objectIdField]], ((_a = r20.queryRelatedFeatures) == null ? void 0 : _a.call(r20, o27, s22)) ?? Promise.resolve({});
}
function S4(e21, t26, r20) {
  let s22 = 0;
  const o27 = [];
  for (; s22 < t26.length; ) o27.push(`${e21} IN (${t26.slice(s22, r20 + s22)})`), s22 += r20;
  return o27.join(" OR ");
}
async function w10(e21, t26, r20, s22) {
  const l25 = r20.layerId.toString(), { layerInfo: u18, relation: p30, relatedFields: c34, outStatistics: f17, url: d15, sourceSpatialReference: m24 } = t26;
  if (!u18 || !p30) return null;
  const y16 = j7({ originRelationship: p30, relationships: u18.relationships, layerId: l25 });
  if ((y16 == null ? void 0 : y16.relationshipTableId) && y16.keyFieldInRelationshipTable) {
    const t27 = (await I3(e21, y16, r20, s22))[e21.attributes[r20.objectIdField]];
    if (!t27) return null;
    const i28 = t27.features.map((e22) => e22.attributes[u18.objectIdField]);
    if ((f17 == null ? void 0 : f17.length) && u18.supportsStatistics) {
      const e22 = new x();
      e22.where = S4(u18.objectIdField, i28, 1e3), e22.outFields = c34, e22.outStatistics = f17, e22.sourceSpatialReference = m24;
      const r21 = { features: Promise.resolve(t27), statsFeatures: s8(d15, e22) };
      return E(r21);
    }
  }
  const g16 = y16 == null ? void 0 : y16.keyField;
  if (g16) {
    const r21 = ie(T3(u18.fields, g16)), l26 = h7(e21.attributes, p30.keyField), c35 = r21 ? `${g16}=${l26}` : `${g16}='${l26}'`, f18 = s8(d15, new x({ where: c35, outFields: t26.relatedFields, sourceSpatialReference: m24 }), s22), y17 = t26.outStatistics && t26.outStatistics.length > 0 && u18.supportsStatistics ? s8(d15, new x({ where: c35, outFields: t26.relatedFields, outStatistics: t26.outStatistics, sourceSpatialReference: m24 }), s22) : null, j10 = { features: f18 };
    return y17 && (j10.statsFeatures = y17), E(j10);
  }
  return null;
}
function b7(t26, r20) {
  return U3(t26, { query: { f: "json" }, signal: r20 && r20.signal });
}
function R5({ relatedInfos: e21, layer: r20 }, i28) {
  const n27 = {};
  return e21.forEach((e22, s22) => {
    const { relation: o27 } = e22;
    if (!o27) {
      const e23 = new s3("relation-required", "A relation is required on a layer to retrieve related records.");
      throw f8.error(e23), e23;
    }
    const { relatedTableId: i29 } = o27;
    if ("number" != typeof i29) {
      const e23 = new s3("A related table ID is required on a layer to retrieve related records.");
      throw f8.error(e23), e23;
    }
    const a32 = `${r20.url}/${i29}`, l25 = d6.get(a32), u18 = l25 || b7(a32);
    l25 || d6.set(a32, u18), n27[s22] = u18;
  }), y2(E(n27), i28);
}
function $2({ graphic: e21, relatedInfos: t26, layer: r20 }, s22) {
  const i28 = {};
  return t26.forEach((t27, o27) => {
    t27.layerInfo && (i28[o27] = w10(e21, t27, r20, s22));
  }), E(i28);
}
function U6({ relatedInfo: e21, fieldName: t26, fieldInfo: r20 }) {
  var _a, _b;
  if ((_a = e21.relatedFields) == null ? void 0 : _a.push(t26), r20.statisticType) {
    const s22 = new m4({ statisticType: r20.statisticType, onStatisticField: t26, outStatisticFieldName: t26 });
    (_b = e21.outStatistics) == null ? void 0 : _b.push(s22);
  }
}
function T3(e21, t26) {
  if (null != e21) {
    t26 = t26.toLowerCase();
    for (const r20 of e21) if (r20 && r20.name.toLowerCase() === t26) return r20;
  }
  return null;
}

// node_modules/@arcgis/core/widgets/Feature/FeatureMedia/FeatureMediaViewModel.js
var I4 = { chartAnimation: true };
var v11 = class extends v3 {
  constructor(t26) {
    super(t26), this.abilities = { ...I4 }, this.activeMediaInfoIndex = 0, this.attributes = null, this.description = null, this.fieldInfoMap = null, this.formattedAttributes = null, this.expressionAttributes = null, this.isAggregate = false, this.layer = null, this.mediaInfos = null, this.popupTemplate = null, this.relatedInfos = null, this.title = null;
  }
  castAbilities(t26) {
    return { ...I4, ...t26 };
  }
  get activeMediaInfo() {
    return this.formattedMediaInfos[this.activeMediaInfoIndex] || null;
  }
  get formattedMediaInfos() {
    return this._formatMediaInfos() || [];
  }
  get formattedMediaInfoCount() {
    return this.formattedMediaInfos.length;
  }
  setActiveMedia(t26) {
    this._setContentElementMedia(t26);
  }
  next() {
    this._pageContentElementMedia(1);
  }
  previous() {
    this._pageContentElementMedia(-1);
  }
  _setContentElementMedia(t26) {
    const { formattedMediaInfoCount: e21 } = this, i28 = (t26 + e21) % e21;
    this.activeMediaInfoIndex = i28;
  }
  _pageContentElementMedia(t26) {
    const { activeMediaInfoIndex: e21 } = this, i28 = e21 + t26;
    this._setContentElementMedia(i28);
  }
  _formatMediaInfos() {
    const { mediaInfos: t26, layer: e21 } = this, o27 = this.attributes ?? {}, r20 = this.formattedAttributes ?? {}, a32 = this.expressionAttributes ?? {}, s22 = this.fieldInfoMap ?? /* @__PURE__ */ new Map();
    return (t26 == null ? void 0 : t26.map((t27) => {
      const i28 = t27 == null ? void 0 : t27.clone();
      if (!i28) return null;
      if (i28.title = S3({ attributes: o27, fieldInfoMap: s22, globalAttributes: r20, expressionAttributes: a32, layer: e21, text: i28.title }), i28.caption = S3({ attributes: o27, fieldInfoMap: s22, globalAttributes: r20, expressionAttributes: a32, layer: e21, text: i28.caption }), i28.altText = S3({ attributes: o27, fieldInfoMap: s22, globalAttributes: r20, expressionAttributes: a32, layer: e21, text: i28.altText }), "image" === i28.type) {
        const { value: t28 } = i28;
        return this._setImageValue({ value: t28, formattedAttributes: r20, layer: e21 }), i28.value.sourceURL ? i28 : void 0;
      }
      if ("pie-chart" === i28.type || "line-chart" === i28.type || "column-chart" === i28.type || "bar-chart" === i28.type) {
        const { value: t28 } = i28;
        return this._setChartValue({ value: t28, chartType: i28.type, attributes: o27, formattedAttributes: r20, layer: e21, expressionAttributes: a32 }), i28;
      }
      return null;
    }).filter(r)) ?? [];
  }
  _setImageValue(t26) {
    const e21 = this.fieldInfoMap ?? /* @__PURE__ */ new Map(), { value: i28, formattedAttributes: o27, layer: r20 } = t26, { linkURL: a32, sourceURL: s22 } = i28;
    if (s22) {
      const t27 = _2(s22, r20);
      i28.sourceURL = G({ formattedAttributes: o27, template: t27, fieldInfoMap: e21 });
    }
    if (a32) {
      const t27 = _2(a32, r20);
      i28.linkURL = G({ formattedAttributes: o27, template: t27, fieldInfoMap: e21 });
    }
  }
  _setChartValue(t26) {
    const { value: e21, attributes: i28, formattedAttributes: o27, chartType: r20, layer: a32, expressionAttributes: s22 } = t26, { popupTemplate: l25, relatedInfos: n27 } = this, { fields: p30, normalizeField: d15 } = e21, c34 = a32;
    e21.fields = U5(p30, c34), d15 && (e21.normalizeField = L4(d15, c34));
    if (!p30.some((t27) => !!(null != o27[t27] || ue(t27) && (n27 == null ? void 0 : n27.size)))) return;
    const h18 = (l25 == null ? void 0 : l25.fieldInfos) ?? [];
    p30.forEach((t27) => {
      if (ue(t27)) return void (e21.series = [...e21.series, ...this._getRelatedChartInfos({ fieldInfos: h18, fieldName: t27, formattedAttributes: o27, chartType: r20, value: e21 })]);
      const a33 = this._getChartOption({ value: e21, attributes: i28, chartType: r20, formattedAttributes: o27, expressionAttributes: s22, fieldName: t27, fieldInfos: h18 });
      e21.series.push(a33);
    });
  }
  _getRelatedChartInfos(t26) {
    var _a;
    const { fieldInfos: e21, fieldName: i28, formattedAttributes: o27, chartType: r20, value: a32 } = t26, s22 = [], l25 = m9(i28), n27 = l25 && ((_a = this.relatedInfos) == null ? void 0 : _a.get(l25.layerId.toString()));
    if (!n27) return s22;
    const { relatedFeatures: p30, relation: d15 } = n27;
    if (!d15 || !p30) return s22;
    const { cardinality: u18 } = d15;
    p30.forEach((t27) => {
      const { attributes: n28 } = t27;
      n28 && Object.keys(n28).forEach((t28) => {
        t28 === l25.fieldName && s22.push(this._getChartOption({ value: a32, attributes: n28, formattedAttributes: o27, fieldName: i28, chartType: r20, relatedFieldName: t28, hasMultipleRelatedFeatures: (p30 == null ? void 0 : p30.length) > 1, fieldInfos: e21 }));
      });
    });
    return "one-to-many" === u18 || "many-to-many" === u18 ? s22 : [s22[0]];
  }
  _getTooltip({ label: t26, value: e21, chartType: i28 }) {
    return "pie-chart" === i28 ? `${t26}` : `${t26}: ${e21}`;
  }
  _getChartOption(t26) {
    var _a;
    const { value: e21, attributes: i28, formattedAttributes: o27, expressionAttributes: r20, fieldName: a32, relatedFieldName: n27, fieldInfos: p30, chartType: d15, hasMultipleRelatedFeatures: u18 } = t26, I7 = this.layer, v16 = this.fieldInfoMap ?? /* @__PURE__ */ new Map(), { normalizeField: M6, tooltipField: A7 } = e21, g16 = M6 ? ue(M6) ? i28[m9(M6).fieldName] : i28[M6] : null, x4 = M2(a32) && r20 && void 0 !== r20[a32] ? r20[a32] : n27 && void 0 !== i28[n27] ? i28[n27] : void 0 !== i28[a32] ? i28[a32] : o27[a32], _9 = new i6({ fieldName: a32, value: void 0 === x4 ? null : x4 && g16 ? x4 / g16 : x4 });
    if (ue(a32)) {
      const t27 = v16.get(a32.toLowerCase()), e22 = A7 && v16.get(A7.toLowerCase()), r21 = (t27 == null ? void 0 : t27.fieldName) ?? a32, s22 = u18 && A7 ? m9(A7).fieldName : (e22 == null ? void 0 : e22.fieldName) ?? A7, l25 = u18 && s22 ? i28[s22] : o27[s22] ?? (t27 == null ? void 0 : t27.label) ?? (t27 == null ? void 0 : t27.fieldName) ?? n27, p31 = u18 && n27 ? i28[n27] : o27[r21];
      return _9.tooltip = this._getTooltip({ label: l25, value: p31, chartType: d15 }), _9;
    }
    const C9 = J(p30, a32), T5 = L4(a32, I7), N2 = A7 && void 0 !== o27[A7] ? o27[A7] : R4(C9 || new c5({ fieldName: T5 }), (_a = this.popupTemplate) == null ? void 0 : _a.expressionInfos), F7 = o27[T5];
    return _9.tooltip = this._getTooltip({ label: N2, value: F7, chartType: d15 }), _9;
  }
};
e([y()], v11.prototype, "abilities", void 0), e([s4("abilities")], v11.prototype, "castAbilities", null), e([y()], v11.prototype, "activeMediaInfoIndex", void 0), e([y({ readOnly: true })], v11.prototype, "activeMediaInfo", null), e([y()], v11.prototype, "attributes", void 0), e([y()], v11.prototype, "description", void 0), e([y()], v11.prototype, "fieldInfoMap", void 0), e([y()], v11.prototype, "formattedAttributes", void 0), e([y()], v11.prototype, "expressionAttributes", void 0), e([y({ readOnly: true })], v11.prototype, "formattedMediaInfos", null), e([y()], v11.prototype, "isAggregate", void 0), e([y()], v11.prototype, "layer", void 0), e([y({ readOnly: true })], v11.prototype, "formattedMediaInfoCount", null), e([y()], v11.prototype, "mediaInfos", void 0), e([y()], v11.prototype, "popupTemplate", void 0), e([y()], v11.prototype, "relatedInfos", void 0), e([y()], v11.prototype, "title", void 0), v11 = e([a2("esri.widgets.Feature.FeatureMedia.FeatureMediaViewModel")], v11);
var M3 = v11;

// node_modules/@esri/calcite-colors/dist/colors.module.js
var f9 = ["#ffffff", "#858585", "#ffbebe", "#ffebbe", "#ffebaf", "#ffffbe", "#e9ffbe", "#d3ffbe", "#beffe8", "#bee8ff", "#bed2ff", "#e8beff", "#ffbee8", "#ebebeb", "#707070", "#ff7f7f", "#ffa77f", "#ffd37f", "#ffff73", "#d1ff73", "#a3ff73", "#73ffdf", "#73dfff", "#73b2ff", "#df73ff", "#ff73df", "#d6d6d6", "#5c5c5c", "#ff0000", "#ff5500", "#ffaa00", "#ffff00", "#aaff00", "#55ff00", "#00ffc5", "#00c5ff", "#0070ff", "#c500ff", "#ff00c5", "#c2c2c2", "#474747", "#e60000", "#e64c00", "#e69800", "#e6e600", "#98e600", "#4ce600", "#00e6a9", "#00a9e6", "#005ce6", "#a900e6", "#e600a9", "#adadad", "#242424", "#a80000", "#a83800", "#a87000", "#a8a800", "#70a800", "#38a800", "#00a884", "#0084a8", "#004da8", "#8400a8", "#a80084", "#999999", "#1a1a1a", "#730000", "#732600", "#734c00", "#737300", "#4c7300", "#267300", "#00734c", "#004c73", "#002673", "#4c0073", "#73004"];
var b8 = [].concat(f9.slice(30, 39), f9.slice(28, 30).reverse());
var e10 = [{ name: "default", colors: b8 }, { name: "cat-dark", colors: ["#ed5151", "#149ece", "#a7c636", "#9e559c", "#fc921f", "#ffde3e", "#f789d8", "#b7814a", "#3caf99", "#6b6bd6", "#b54779", "#7f7f7f"] }, { name: "tropical-bliss", colors: ["#fce138", "#ff9399", "#fcd27e", "#f1983c", "#a553b7", "#b1a9d0", "#6ecffc", "#4c81cd", "#fc6f84", "#fc3e5a", "#6af689", "#48885c"] }, { name: "desert-blooms", colors: ["#102432", "#144d59", "#ffc730", "#ed9310", "#a64f1b", "#661510", "#d9351a", "#b31515", "#4a0932", "#8c213f", "#18382e", "#2c6954"] }, { name: "under-the-sea", colors: ["#bf9727", "#607100", "#00734c", "#704489", "#01acca", "#024e76", "#f09100", "#ea311f", "#c6004b", "#7570b3", "#666666", "#333333"] }, { name: "vibrant-rainbow", colors: ["#fffb00", "#f5cb11", "#9fd40c", "#46e39c", "#32b8a6", "#7ff2fa", "#ac08cc", "#dd33ff", "#eb7200", "#e8a784", "#bf2e2e", "#6c7000"] }, { name: "ocean-bay", colors: ["#191921", "#11495c", "#78b1c2", "#454f4b", "#8f8f82", "#9be0c0", "#87b051", "#f7ec88", "#ebdcc1", "#dbb658", "#c43541", "#75351e"] }, { name: "prairie-summer", colors: ["#332424", "#751555", "#d47013", "#d68989", "#211173", "#82aad6", "#7bfaeb", "#6ec9a8", "#6b6408", "#eada40", "#ccc54a", "#1fc235"] }, { name: "pastel-chalk", colors: ["#fffd99", "#f5e6a4", "#c1d48c", "#b8e3d0", "#a0b8b5", "#cbf7fa", "#d791f2", "#dfc1eb", "#f2b983", "#e8c4b2", "#bf8e8e", "#94995c"] }, { name: "seq-yellow-orange-red-bright", colors: ["#910000", "#b1260b", "#c0370f", "#e05919", "#ef6a1d", "#ff7b22", "#ffa143", "#ffb454", "#ffda74", "#ffed85"] }, { name: "seq-reds-bright", colors: ["#57453b", "#7b4238", "#9f4036", "#c23d33", "#d7483c", "#ec5244", "#f3696c", "#f9816c", "#ffc4ae", "#fff0dc"] }, { name: "seq-purples-bright", colors: ["#4e465c", "#5a4a78", "#695291", "#775baa", "#8663c3", "#946bdc", "#aa89e8", "#c1a6f3", "#d7c4ff", "#e6e1ff"] }, { name: "seq-blues-bright", colors: ["#404d54", "#435c6c", "#48799d", "#4b88b6", "#4d96ce", "#50a5e7", "#74bbed", "#98d0f3", "#bce6f9", "#e6faff"] }, { name: "seq-greens-bright", colors: ["#39544c", "#386757", "#368165", "#359b73", "#33b581", "#4bc392", "#64d2a2", "#7ce0b3", "#cbf6d9", "#f4ffea"] }, { name: "seq-browns-bright", colors: ["#524834", "#715b38", "#8f6e3c", "#ae8140", "#cc9444", "#eba748", "#eeb664", "#f0c47f", "#f9e0b7", "#fff8eb"] }];

// node_modules/@arcgis/core/widgets/support/chartUtils.js
var n14 = "en-us";
var h8 = /* @__PURE__ */ new Map([["ar", () => import("./ar-AN57JK7F.js").then((t26) => t26.a)], ["bg-bg", () => import("./bg_BG-WID5SIJM.js").then((t26) => t26.b)], ["bs-ba", () => import("./bs_BA-E235YTQL.js").then((t26) => t26.b)], ["ca-es", () => import("./ca_ES-CHSL6QAZ.js").then((t26) => t26.c)], ["cs-cz", () => import("./cs_CZ-XOPYWVGB.js").then((t26) => t26.c)], ["da-dk", () => import("./da_DK-XZJ3BREZ.js").then((t26) => t26.d)], ["de-de", () => import("./de_DE-B5TP4IUT.js").then((t26) => t26.d)], ["de-ch", () => import("./de_CH-SPZPH3BO.js").then((t26) => t26.d)], ["el-gr", () => import("./el_GR-LKVOCI7E.js").then((t26) => t26.e)], ["en-us", () => import("./en_US-T2OWVLQ6.js").then((t26) => t26.e)], ["en-ca", () => import("./en_CA-F75BAQF4.js").then((t26) => t26.e)], ["es-es", () => import("./es_ES-HPD4SD45.js").then((t26) => t26.e)], ["et-ee", () => import("./et_EE-CIOJ7SQA.js").then((t26) => t26.e)], ["fi-fi", () => import("./fi_FI-LPOLLDA2.js").then((t26) => t26.f)], ["fr-fr", () => import("./fr_FR-AUI4TQWY.js").then((t26) => t26.f)], ["he-il", () => import("./he_IL-TWCA37KW.js").then((t26) => t26.h)], ["hr-hr", () => import("./hr_HR-6SGN4MWU.js").then((t26) => t26.h)], ["hu-hu", () => import("./hu_HU-K7M6HWCD.js").then((t26) => t26.h)], ["id-id", () => import("./id_ID-FFXABU5X.js").then((t26) => t26.i)], ["it-it", () => import("./it_IT-HD5KXDMV.js").then((t26) => t26.i)], ["ja-jp", () => import("./ja_JP-3TTT5RFR.js").then((t26) => t26.j)], ["ko-kr", () => import("./ko_KR-BCI46JIB.js").then((t26) => t26.k)], ["lt-lt", () => import("./lt_LT-7UR7DQUX.js").then((t26) => t26.l)], ["lv-lv", () => import("./lv_LV-QIZQBKEH.js").then((t26) => t26.l)], ["nb-no", () => import("./nb_NO-2TQANBUV.js").then((t26) => t26.n)], ["nl-nl", () => import("./nl_NL-2NGBMYV7.js").then((t26) => t26.n)], ["pl-pl", () => import("./pl_PL-BJHVJOLJ.js").then((t26) => t26.p)], ["pt-br", () => import("./pt_BR-GIRWKK3K.js").then((t26) => t26.p)], ["pt-pt", () => import("./pt_PT-H7XO7P4P.js").then((t26) => t26.p)], ["ro-ro", () => import("./ro_RO-7PNPTOHV.js").then((t26) => t26.r)], ["ru-ru", () => import("./ru_RU-2OXDVMBO.js").then((t26) => t26.r)], ["sk-sk", () => import("./sk_SK-32J3IPRO.js").then((t26) => t26.s)], ["sl-sl", () => import("./sl_SL-6TWU7P2A.js").then((t26) => t26.s)], ["sr-rs", () => import("./sr_RS-KTWIGXOI.js").then((t26) => t26.s)], ["sv-se", () => import("./sv_SE-GPNV72BY.js").then((t26) => t26.s)], ["th-th", () => import("./th_TH-KLGGJA5K.js").then((t26) => t26.t)], ["tr-tr", () => import("./tr_TR-QIE3RYWD.js").then((t26) => t26.t)], ["uk-ua", () => import("./uk_UA-QAIFUR2G.js").then((t26) => t26.u)], ["vi-vn", () => import("./vi_VN-BHKGFLNK.js").then((t26) => t26.v)], ["zh-cn", () => import("./zh_Hans-OFYGSTUR.js").then((t26) => t26.z)], ["zh-hk", () => import("./zh_Hant-3UGAAG6T.js").then((t26) => t26.z)], ["zh-tw", () => import("./zh_Hant-3UGAAG6T.js").then((t26) => t26.z)]]);
function e11(t26) {
  const s22 = t26.split("-")[0].toLowerCase();
  let n27 = null;
  for (const e21 of h8.keys()) if (e21.startsWith(s22)) {
    n27 = e21;
    break;
  }
  return n27;
}
function r11(t26) {
  return t26 ? h8.has(t26.toLowerCase()) ? t26.toLowerCase() : e11(t26) || n14 : n14;
}
var o11 = null;
var i12 = null;
async function u7(t26 = l2()) {
  if (t26 = r11(t26), o11 && t26 === i12) return o11;
  o11 = import("./chunks-IL7OVE6A.js").then((t27) => t27.i), i12 = t26;
  try {
    const [s22, n27] = await Promise.all([o11, h8.get(i12)()]);
    i12 === t26 && (s22.am4core.options.defaultLocale = n27.default), s22.am4core.options.suppressWarnings = true, s22.am4core.options.autoDispose = true;
  } catch {
    return o11 = null, i12 = null, null;
  }
  return o11;
}
function c19(s22, n27 = "default") {
  const h18 = e10.find((t26) => t26.name === n27);
  return h18 ? h18.colors.map((t26) => s22.color(t26)) : null;
}

// node_modules/@arcgis/core/widgets/Feature/FeatureMedia.js
var M4 = "esri-feature-media";
var I5 = { base: M4, mediaContainer: `${M4}__container`, mediaItemContainer: `${M4}__item-container`, mediaItem: `${M4}__item`, mediaItemTitle: `${M4}__item-title`, mediaItemCaption: `${M4}__item-caption`, mediaPrevious: `${M4}__previous`, mediaPreviousIconLTR: `${M4}__previous-icon`, mediaPreviousIconRTL: `${M4}__previous-icon--rtl`, mediaNext: `${M4}__next`, mediaNextIconLTR: `${M4}__next-icon`, mediaNextIconRTL: `${M4}__next-icon--rtl`, mediaChart: `${M4}__chart`, mediaButton: `${M4}__button`, mediaIcon: `${M4}__icon`, iconLeftTriangleArrow: "esri-icon-left-triangle-arrow", iconRightTriangleArrow: "esri-icon-right-triangle-arrow" };
var _3 = 0.05;
var g7 = 0.95;
var w11 = 15;
var y7 = "color";
var T4 = "tooltip";
var b9 = "value";
var C3 = "default-line-value";
var x3 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._refreshTimer = null, this._refreshIntervalInfo = null, this._featureElementInfo = null, this.viewModel = new M3(), this.messages = null, this._getChartDependencies = async (e22) => {
      const t27 = await u7(), { destroyed: i28, viewModel: r20 } = this;
      if (i28 || !r20 || !e22) return;
      const { activeMediaInfo: s22 } = r20, a32 = await this._getRendererColors(t27);
      this._renderChart({ chartDiv: e22, mediaInfo: s22, chartsModule: t27, colorMap: a32 });
    };
  }
  initialize() {
    this._featureElementInfo = new l10(), this.addHandles([l3(() => {
      var _a, _b;
      return [(_a = this.viewModel) == null ? void 0 : _a.activeMediaInfo, (_b = this.viewModel) == null ? void 0 : _b.activeMediaInfoIndex];
    }, () => this._setupMediaRefreshTimer(), h), l3(() => {
      var _a, _b;
      return [(_a = this.viewModel) == null ? void 0 : _a.description, (_b = this.viewModel) == null ? void 0 : _b.title];
    }, () => this._setupFeatureElementInfo(), h)]);
  }
  destroy() {
    var _a;
    this._clearMediaRefreshTimer(), (_a = this._featureElementInfo) == null ? void 0 : _a.destroy();
  }
  get attributes() {
    return this.viewModel.attributes;
  }
  set attributes(e21) {
    this.viewModel.attributes = e21;
  }
  get activeMediaInfoIndex() {
    return this.viewModel.activeMediaInfoIndex;
  }
  set activeMediaInfoIndex(e21) {
    this.viewModel.activeMediaInfoIndex = e21;
  }
  get description() {
    return this.viewModel.description;
  }
  set description(e21) {
    this.viewModel.description = e21;
  }
  get fieldInfoMap() {
    return this.viewModel.fieldInfoMap;
  }
  set fieldInfoMap(e21) {
    this.viewModel.fieldInfoMap = e21;
  }
  get layer() {
    return this.viewModel.layer;
  }
  set layer(e21) {
    this.viewModel.layer = e21;
  }
  get mediaInfos() {
    return this.viewModel.mediaInfos;
  }
  set mediaInfos(e21) {
    this.viewModel.mediaInfos = e21;
  }
  get popupTemplate() {
    return this.viewModel.popupTemplate;
  }
  set popupTemplate(e21) {
    this.viewModel.popupTemplate = e21;
  }
  get relatedInfos() {
    return this.viewModel.relatedInfos;
  }
  set relatedInfos(e21) {
    this.viewModel.relatedInfos = e21;
  }
  get title() {
    return this.viewModel.title;
  }
  set title(e21) {
    this.viewModel.title = e21;
  }
  render() {
    var _a;
    return n5("div", { bind: this, class: I5.base, onkeyup: this._handleMediaKeyup }, (_a = this._featureElementInfo) == null ? void 0 : _a.render(), this.renderMedia());
  }
  renderMedia() {
    const { formattedMediaInfoCount: e21 } = this.viewModel;
    return e21 ? n5("div", { key: "media-element-container", class: I5.mediaContainer }, this.renderMediaPageButton("previous"), this.renderMediaInfo(), this.renderMediaPageButton("next")) : null;
  }
  renderImageMediaInfo(e21) {
    const { _refreshIntervalInfo: t26 } = this, { activeMediaInfoIndex: i28, formattedMediaInfoCount: r20 } = this.viewModel, { value: s22, refreshInterval: a32, altText: o27, title: n27, type: l25 } = e21, { sourceURL: d15, linkURL: u18 } = s22, m24 = C2(u18 ?? void 0) ? "_blank" : "_self", h18 = "_blank" === m24 ? "noreferrer" : "", p30 = a32 ? t26 : null, v16 = p30 ? p30.timestamp : 0, M6 = p30 ? p30.sourceURL : d15, I7 = n5("img", { alt: o27 || n27, key: `media-${l25}-${i28}-${r20}-${v16}`, src: M6 ?? void 0 }), _9 = u18 ? n5("a", { title: n27, href: u18, rel: h18, target: m24 }, I7) : null;
    return _9 || I7;
  }
  renderChartMediaInfo(e21) {
    const { activeMediaInfoIndex: t26, formattedMediaInfoCount: i28 } = this.viewModel;
    return n5("div", { key: `media-${e21.type}-${t26}-${i28}`, bind: this, class: I5.mediaChart, afterCreate: this._getChartDependencies });
  }
  renderMediaInfoType() {
    const { activeMediaInfo: e21 } = this.viewModel;
    return e21 ? "image" === e21.type ? this.renderImageMediaInfo(e21) : e21.type.includes("chart") ? this.renderChartMediaInfo(e21) : null : null;
  }
  renderMediaInfo() {
    const { activeMediaInfo: e21 } = this.viewModel;
    if (!e21) return null;
    const t26 = e21.title ? n5("div", { key: "media-title", class: I5.mediaItemTitle, innerHTML: e21.title }) : null, i28 = e21.caption ? n5("div", { key: "media-caption", class: I5.mediaItemCaption, innerHTML: e21.caption }) : null;
    return n5("div", { key: "media-container", class: I5.mediaItemContainer }, n5("div", { key: "media-item-container", class: I5.mediaItem }, this.renderMediaInfoType()), t26, i28);
  }
  renderMediaPageButton(e21) {
    if (this.viewModel.formattedMediaInfoCount < 2) return null;
    const t26 = "previous" === e21, i28 = t26 ? this.messages.previous : this.messages.next, r20 = t26 ? this.classes(I5.mediaButton, I5.mediaPrevious) : this.classes(I5.mediaButton, I5.mediaNext), s22 = t26 ? this.classes(I5.mediaIcon, I5.mediaPreviousIconLTR, I5.iconLeftTriangleArrow) : this.classes(I5.mediaIcon, I5.mediaNextIconLTR, I5.iconRightTriangleArrow), a32 = t26 ? this.classes(I5.mediaIcon, I5.mediaPreviousIconRTL, I5.iconRightTriangleArrow) : this.classes(I5.mediaIcon, I5.mediaNextIconRTL, I5.iconLeftTriangleArrow), o27 = t26 ? "media-previous" : "media-next", n27 = t26 ? this._previous : this._next;
    return n5("button", { type: "button", key: o27, title: i28, "aria-label": i28, tabIndex: 0, class: r20, bind: this, onclick: n27 }, n5("span", { "aria-hidden": "true", class: s22 }), n5("span", { "aria-hidden": "true", class: a32 }));
  }
  _setupFeatureElementInfo() {
    var _a;
    const { description: e21, title: t26 } = this;
    (_a = this._featureElementInfo) == null ? void 0 : _a.set({ description: e21, title: t26 });
  }
  _next() {
    this.viewModel.next();
  }
  _previous() {
    this.viewModel.previous();
  }
  _getRenderer() {
    const { isAggregate: e21, layer: t26 } = this.viewModel;
    return e21 && (t26 == null ? void 0 : t26.featureReduction) && "renderer" in t26.featureReduction ? t26.featureReduction.renderer : t26 == null ? void 0 : t26.renderer;
  }
  async _getRendererColors(e21) {
    const { am4core: t26 } = e21, i28 = /* @__PURE__ */ new Map(), r20 = this._getRenderer(), s22 = "default";
    if (!r20) return i28;
    const a32 = await b5(r20);
    a32.delete(s22);
    return Array.from(a32.values()).every((e22) => 1 === (e22 == null ? void 0 : e22.length)) ? (i28.set(C3, t26.color({ r: 50, g: 50, b: 50, a: 1 })), Array.from(a32.keys()).forEach((e22) => {
      var _a;
      e22 && i28.set(e22, t26.color((_a = a32.get(e22)) == null ? void 0 : _a[0].toCss(true)));
    }), i28) : i28;
  }
  _handleMediaKeyup(e21) {
    const i28 = i(e21);
    "ArrowLeft" === i28 && (e21.stopPropagation(), this.viewModel.previous()), "ArrowRight" === i28 && (e21.stopPropagation(), this.viewModel.next());
  }
  _renderChart(e21) {
    const { abilities: t26 } = this.viewModel, { chartsModule: i28, chartDiv: r20, mediaInfo: s22, colorMap: a32 } = e21, { value: o27, type: n27 } = s22, { am4core: l25 } = i28, d15 = c19(l25);
    function c34(e22) {
      e22 instanceof l25.ColorSet && d15 && (e22.list = d15);
    }
    r5() && l25.useTheme(i28.am4themes_dark);
    const u18 = window.matchMedia("(prefers-reduced-motion: reduce)");
    t26.chartAnimation && !u18.matches ? l25.useTheme(i28.am4themes_animated) : l25.unuseTheme(i28.am4themes_animated), l25.useTheme(c34);
    const h18 = "pie-chart" === n27 ? this._createPieChart(e21) : this._createXYChart(e21);
    r20.setAttribute("aria-label", s22.altText || s22.title), h18.data = o27.series.map((e22) => ({ [T4]: e22.tooltip, [b9]: e22.value, [y7]: a32.get(e22.fieldName) })).filter((e22) => "pie-chart" !== n27 || null != e22.value && e22.value > 0);
  }
  _customizeChartTooltip(e21, t26) {
    e21 && (e21.label.wrap = true, e21.label.maxWidth = 200, e21.autoTextColor = false, e21.getFillFromObject = false, e21.label.fill = t26.color("#ffffff"), e21.background.fill = t26.color({ r: 0, g: 0, b: 0, a: 0.7 }));
  }
  _createPieChart(e21) {
    const { chartDiv: t26, chartsModule: i28 } = e21, { am4core: r20, am4charts: s22 } = i28, a32 = r20.create(t26, s22.PieChart);
    a32.rtl = f4(this.container);
    const o27 = a32.series.push(new s22.PieSeries());
    return o27.labels.template.disabled = true, o27.ticks.template.disabled = true, o27.dataFields.value = b9, o27.dataFields.category = T4, this._customizeChartTooltip(o27.tooltip, r20), o27.slices.template.propertyFields.fill = y7, o27.slices.template.propertyFields.stroke = y7, a32;
  }
  _getMinSeriesValue(e21) {
    let t26 = 0;
    return e21.forEach((e22) => t26 = Math.min(e22.value, t26)), t26;
  }
  _createColumnChart(e21, t26) {
    const { chartsModule: i28, mediaInfo: r20 } = t26, { value: s22 } = r20, { am4core: a32, am4charts: o27 } = i28, n27 = e21.xAxes.push(new o27.CategoryAxis());
    n27.dataFields.category = T4, n27.renderer.labels.template.disabled = true;
    const l25 = n27.tooltip;
    this._customizeChartTooltip(l25, a32), l25.events.on("sizechanged", () => {
      l25.dy = -l25.contentHeight;
    });
    const d15 = e21.yAxes.push(new o27.ValueAxis()), c34 = d15.renderer.labels.template;
    d15.renderer.minLabelPosition = _3, d15.renderer.maxLabelPosition = g7, d15.min = this._getMinSeriesValue(s22.series), this._customizeChartTooltip(d15.tooltip, a32), c34.wrap = true;
    const u18 = e21.series.push(new o27.ColumnSeries());
    u18.dataFields.valueY = b9, u18.dataFields.categoryX = T4, u18.columns.template.propertyFields.fill = y7, u18.columns.template.propertyFields.stroke = y7, e21.cursor = new o27.XYCursor(), s22.series.length > w11 && (e21.scrollbarX = new a32.Scrollbar());
  }
  _createBarChart(e21, t26) {
    const { chartsModule: i28, mediaInfo: r20 } = t26, { value: s22 } = r20, { am4core: a32, am4charts: o27 } = i28, n27 = e21.yAxes.push(new o27.CategoryAxis());
    n27.dataFields.category = T4, n27.renderer.inversed = true, n27.renderer.labels.template.disabled = true;
    const l25 = n27.tooltip;
    this._customizeChartTooltip(l25, a32), l25.events.on("sizechanged", () => {
      l25.dx = l25.contentWidth;
    });
    const d15 = e21.xAxes.push(new o27.ValueAxis()), c34 = d15.renderer.labels.template;
    d15.renderer.minLabelPosition = _3, d15.renderer.maxLabelPosition = g7, d15.min = this._getMinSeriesValue(s22.series), this._customizeChartTooltip(d15.tooltip, a32), c34.wrap = true;
    const u18 = e21.series.push(new o27.ColumnSeries());
    u18.dataFields.valueX = b9, u18.dataFields.categoryY = T4, u18.columns.template.propertyFields.fill = y7, u18.columns.template.propertyFields.stroke = y7, e21.cursor = new o27.XYCursor(), s22.series.length > w11 && (e21.scrollbarY = new a32.Scrollbar());
  }
  _createLineChart(e21, t26) {
    const { chartsModule: i28, mediaInfo: r20, colorMap: s22 } = t26, { value: a32 } = r20, { am4core: o27, am4charts: n27 } = i28, l25 = e21.xAxes.push(new n27.CategoryAxis());
    l25.dataFields.category = T4, l25.renderer.labels.template.disabled = true;
    const d15 = l25.tooltip;
    this._customizeChartTooltip(d15, o27), d15.events.on("sizechanged", () => {
      d15.dy = -d15.contentHeight;
    });
    const c34 = e21.yAxes.push(new n27.ValueAxis()), u18 = c34.renderer.labels.template;
    c34.renderer.minLabelPosition = _3, c34.renderer.maxLabelPosition = g7, c34.min = this._getMinSeriesValue(a32.series), this._customizeChartTooltip(c34.tooltip, o27), u18.wrap = true;
    const m24 = e21.series.push(new n27.LineSeries());
    m24.dataFields.categoryX = T4, m24.dataFields.valueY = b9, m24.strokeWidth = 1;
    const h18 = s22.get(C3);
    h18 && (m24.stroke = h18);
    const p30 = m24.bullets.push(new n27.CircleBullet());
    p30.propertyFields.fill = y7, p30.propertyFields.stroke = y7, e21.cursor = new n27.XYCursor(), a32.series.length > w11 && (e21.scrollbarX = new o27.Scrollbar());
  }
  _createXYChart(e21) {
    const { chartDiv: t26, chartsModule: i28, mediaInfo: r20 } = e21, { type: s22 } = r20, { am4core: a32, am4charts: o27 } = i28, n27 = a32.create(t26, o27.XYChart);
    return n27.rtl = f4(this.container), "column-chart" === s22 && this._createColumnChart(n27, e21), "bar-chart" === s22 && this._createBarChart(n27, e21), "line-chart" === s22 && this._createLineChart(n27, e21), n27;
  }
  _clearMediaRefreshTimer() {
    const { _refreshTimer: e21 } = this;
    e21 && (clearTimeout(e21), this._refreshTimer = null);
  }
  _updateMediaInfoTimestamp(e21) {
    const t26 = Date.now();
    this._refreshIntervalInfo = { timestamp: t26, sourceURL: e21 && this._getImageSource(e21, t26) }, this.scheduleRender();
  }
  _setupMediaRefreshTimer() {
    this._clearMediaRefreshTimer();
    const { activeMediaInfo: e21 } = this.viewModel;
    e21 && "image" === e21.type && e21.refreshInterval && this._setRefreshTimeout(e21);
  }
  _setRefreshTimeout(e21) {
    const { refreshInterval: t26, value: i28 } = e21;
    if (!t26) return;
    const r20 = 6e4 * t26;
    this._updateMediaInfoTimestamp(i28.sourceURL);
    const s22 = setInterval(() => {
      this._updateMediaInfoTimestamp(i28.sourceURL);
    }, r20);
    this._refreshTimer = s22;
  }
  _getImageSource(e21, t26) {
    const i28 = e21.includes("?") ? "&" : "?", [r20, s22 = ""] = e21.split("#");
    return `${r20}${i28}timestamp=${t26}${s22 ? "#" : ""}${s22}`;
  }
};
e([y()], x3.prototype, "attributes", null), e([y()], x3.prototype, "activeMediaInfoIndex", null), e([y()], x3.prototype, "description", null), e([y()], x3.prototype, "fieldInfoMap", null), e([y()], x3.prototype, "layer", null), e([y()], x3.prototype, "mediaInfos", null), e([y()], x3.prototype, "popupTemplate", null), e([y()], x3.prototype, "relatedInfos", null), e([y()], x3.prototype, "title", null), e([y({ type: M3 })], x3.prototype, "viewModel", void 0), e([y(), e4("esri/widgets/Feature/t9n/Feature")], x3.prototype, "messages", void 0), x3 = e([a2("esri.widgets.Feature.FeatureMedia")], x3);
var R6 = x3;

// node_modules/@arcgis/core/widgets/Feature/support/arcadeFeatureUtils.js
var n15 = ["$datastore", "$map", "$layer", "$aggregatedfeatures"];
var s11 = "esri.widgets.Feature.support.arcadeFeatureUtils";
var o12 = s2.getLogger(s11);
function c20(e21) {
  return "string" == typeof e21 ? re(te(e21)) : Array.isArray(e21) ? p11(e21) : "esri.arcade.Dictionary" === (e21 == null ? void 0 : e21.declaredClass) ? l14(e21) : e21;
}
function p11(e21) {
  return `<ul class="esri-widget__list">${e21.map((e22) => `<li>${"string" == typeof e22 ? re(te(e22)) : e22}</li>`).join("")}</ul>`;
}
function l14(e21) {
  return `<table class="esri-widget__table">${e21.keys().map((r20) => {
    const t26 = e21.field(r20);
    return `<tr><th>${r20}</th><td>${"string" == typeof t26 ? re(te(t26)) : t26}</td></tr>`;
  }).join("")}</table>`;
}
function u8({ aggregatedFeatures: e21, arcadeUtils: r20, featureSetVars: a32, context: i28, viewInfo: n27, map: s22, graphic: o27, interceptor: c34 }) {
  a32.forEach((a33) => {
    const p30 = a33.toLowerCase(), l25 = n27.sr, u18 = { map: s22, spatialReference: l25, interceptor: c34 };
    if ("$map" === p30 && (i28.vars[p30] = r20.convertMapToFeatureSetCollection(u18)), "$layer" === p30 && (i28.vars[p30] = r20.convertFeatureLayerToFeatureSet({ layer: o27.sourceLayer, spatialReference: l25, interceptor: c34 })), "$datastore" === p30 && (i28.vars[p30] = r20.convertServiceUrlToWorkspace({ url: o27.sourceLayer.url, spatialReference: l25, interceptor: c34 })), "$aggregatedfeatures" === p30) {
      const a34 = o27.layer, { fields: n28, objectIdField: s23, geometryType: l26, spatialReference: u19, displayField: f17 } = a34, g16 = new He({ fields: n28, objectIdField: s23, geometryType: l26, spatialReference: u19, displayField: f17, ..."feature" === a34.type ? { templates: a34.templates, typeIdField: a34.typeIdField, types: a34.types } : null, source: e21 });
      i28.vars[p30] = r20.convertFeatureLayerToFeatureSet({ layer: g16, spatialReference: u19, interceptor: c34 });
    }
  });
}
function f10() {
  return import("./arcadeUtils-KQLQHL4N.js");
}
function g8(e21) {
  return "createQuery" in e21 && "queryFeatures" in e21;
}
async function y8({ graphic: e21, view: r20 }) {
  const { isAggregate: t26, layer: a32 } = e21;
  if (!t26 || !a32 || "2d" !== (r20 == null ? void 0 : r20.type)) return [];
  const i28 = await r20.whenLayerView(a32);
  if (!g8(i28)) return [];
  const n27 = i28.createQuery(), s22 = e21.getObjectId();
  n27.aggregateIds = null != s22 ? [s22] : [];
  const { features: o27 } = await i28.queryFeatures(n27);
  return o27;
}
async function d7({ expressionInfo: e21, arcadeUtils: r20, interceptor: t26, spatialReference: a32, map: i28, graphic: s22, view: c34 }) {
  if (!e21 || !e21.expression) return null;
  const p30 = r20.createSyntaxTree(e21.expression), l25 = n15.filter((e22) => r20.hasVariable(p30, e22)), [f17] = await Promise.all([y8({ graphic: s22, view: c34 }), r20.loadScriptDependencies(p30, true, l25)]), g16 = r20.getViewInfo({ spatialReference: a32 }), d15 = r20.createExecContext(s22, g16);
  d15.interceptor = t26, d15.useAsync = true, u8({ aggregatedFeatures: f17, arcadeUtils: r20, featureSetVars: l25, context: d15, viewInfo: g16, map: i28, graphic: s22, interceptor: t26 });
  const m24 = r20.createFunction(p30, d15);
  return r20.executeAsyncFunction(m24, d15).catch((r21) => o12.error("arcade-execution-error", { error: r21, graphic: s22, expressionInfo: e21 }));
}
async function m10({ expressionInfos: e21, spatialReference: t26, graphic: a32, interceptor: i28, map: n27, view: s22 }) {
  if (!e21 || !e21.length) return {};
  const o27 = await f10(), p30 = {};
  for (const r20 of e21) p30[`expression/${r20.name}`] = d7({ expressionInfo: r20, arcadeUtils: o27, interceptor: i28, spatialReference: t26, map: n27, graphic: a32, view: s22 });
  const l25 = await E(p30), u18 = {};
  for (const r20 in l25) u18[r20] = c20(l25[r20].value);
  return u18;
}

// node_modules/@arcgis/core/widgets/Feature/FeatureExpression/FeatureExpressionViewModel.js
var w12 = 1;
var j8 = class extends a9(v3) {
  constructor(t26) {
    super(t26), this._abortController = null, this.expressionInfo = null, this.graphic = null, this.contentElement = null, this.contentElementViewModel = null, this.interceptor = null, this.view = null, this._cancelQuery = () => {
      const { _abortController: t27 } = this;
      t27 && t27.abort(), this._abortController = null;
    }, this._createVM = () => {
      var _a, _b;
      const t27 = (_a = this.contentElement) == null ? void 0 : _a.type;
      (_b = this.contentElementViewModel) == null ? void 0 : _b.destroy();
      const e21 = "fields" === t27 ? new n13() : "media" === t27 ? new M3() : "text" === t27 ? new l11() : null;
      this._set("contentElementViewModel", e21);
    }, this._compile = async () => {
      this._cancelQuery();
      const t27 = new AbortController();
      this._abortController = t27, await this._compileExpression(), this._abortController === t27 && (this._abortController = null);
    }, this._compileThrottled = e7(this._compile, w12, this), this._compileExpression = async () => {
      const { expressionInfo: t27, graphic: e21, interceptor: o27, spatialReference: r20, map: n27, view: s22, _abortController: i28 } = this;
      if (!(t27 && e21 && r20 && n27)) return void this._set("contentElement", null);
      const l25 = await f10();
      if (i28 !== this._abortController) return;
      const p30 = await d7({ arcadeUtils: l25, expressionInfo: t27, graphic: e21, interceptor: o27, map: n27, spatialReference: r20, view: s22 });
      if (!p30 || "esri.arcade.Dictionary" !== p30.declaredClass) return void this._set("contentElement", null);
      const h18 = await p30.castAsJsonAsync(i28 == null ? void 0 : i28.signal), u18 = h18 == null ? void 0 : h18.type, d15 = "media" === u18 ? I.fromJSON(h18) : "text" === u18 ? c7.fromJSON(h18) : "fields" === u18 ? c6.fromJSON(h18) : null;
      this._set("contentElement", d15);
    }, this.handles.add([l3(() => [this.expressionInfo, this.graphic, this.map, this.spatialReference, this.view], () => this._compileThrottled(), h), l3(() => [this.contentElement], () => this._createVM(), h)]);
  }
  destroy() {
    var _a;
    this._cancelQuery(), (_a = this.contentElementViewModel) == null ? void 0 : _a.destroy(), this._set("contentElementViewModel", null), this._set("contentElement", null);
  }
  get spatialReference() {
    var _a;
    return ((_a = this.view) == null ? void 0 : _a.spatialReference) ?? null;
  }
  set spatialReference(t26) {
    this._override("spatialReference", t26);
  }
  get state() {
    const { _abortController: t26, contentElement: e21, contentElementViewModel: o27 } = this;
    return t26 ? "loading" : e21 || o27 ? "ready" : "disabled";
  }
  get map() {
    var _a;
    return ((_a = this.view) == null ? void 0 : _a.map) ?? null;
  }
  set map(t26) {
    this._override("map", t26);
  }
};
e([y()], j8.prototype, "_abortController", void 0), e([y({ type: i5 })], j8.prototype, "expressionInfo", void 0), e([y({ type: g3 })], j8.prototype, "graphic", void 0), e([y({ readOnly: true })], j8.prototype, "contentElement", void 0), e([y({ readOnly: true })], j8.prototype, "contentElementViewModel", void 0), e([y()], j8.prototype, "interceptor", void 0), e([y()], j8.prototype, "spatialReference", null), e([y({ readOnly: true })], j8.prototype, "state", null), e([y()], j8.prototype, "map", null), e([y()], j8.prototype, "view", void 0), j8 = e([a2("esri.widgets.Feature.FeatureExpression.FeatureExpressionViewModel")], j8);
var C4 = j8;

// node_modules/@arcgis/core/widgets/Feature/FeatureExpression.js
var c21 = { iconLoading: "esri-icon-loading-indicator esri-rotating", base: "esri-feature-expression", loadingSpinnerContainer: "esri-feature__loading-container", spinner: "esri-feature__loading-spinner" };
var u9 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._contentWidget = null, this.viewModel = new C4();
  }
  initialize() {
    this.addHandles(l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.contentElementViewModel;
    }, () => this._setupExpressionWidget(), h));
  }
  destroy() {
    this._destroyContentWidget();
  }
  renderLoading() {
    return n5("div", { key: "loading-container", class: c21.loadingSpinnerContainer }, n5("span", { class: this.classes(c21.iconLoading, c21.spinner) }));
  }
  render() {
    var _a;
    const { state: e21 } = this.viewModel;
    return n5("div", { class: c21.base }, "loading" === e21 ? this.renderLoading() : "disabled" === e21 ? null : (_a = this._contentWidget) == null ? void 0 : _a.render());
  }
  _destroyContentWidget() {
    const { _contentWidget: e21 } = this;
    e21 && (e21.viewModel = null, e21.destroy()), this._contentWidget = null;
  }
  _setupExpressionWidget() {
    const { contentElementViewModel: e21, contentElement: t26 } = this.viewModel, s22 = t26 == null ? void 0 : t26.type;
    this._destroyContentWidget();
    const i28 = e21 ? "fields" === s22 ? new c17({ viewModel: e21 }) : "media" === s22 ? new R6({ viewModel: e21 }) : "text" === s22 ? new p9({ viewModel: e21 }) : null : null;
    this._contentWidget = i28, this.scheduleRender();
  }
};
e([y({ type: C4 })], u9.prototype, "viewModel", void 0), u9 = e([a2("esri.widgets.Feature.FeatureExpression")], u9);
var m11 = u9;

// node_modules/@arcgis/core/widgets/Feature/FeatureRelationship/FeatureRelationshipViewModel.js
var b10 = 100;
var F4 = class extends i2(s7(a9(v3))) {
  constructor(e21) {
    super(e21), this._queryAbortController = null, this._queryPageAbortController = null, this._queryFeatureCountAbortController = null, this.featuresPerPage = 10, this.description = null, this.graphic = null, this.layer = null, this.map = null, this.orderByFields = null, this.featureCount = 0, this.relationshipId = null, this.showAllEnabled = false, this.title = null, this._cancelQuery = () => {
      const { _queryAbortController: e22 } = this;
      e22 && e22.abort(), this._queryAbortController = null;
    }, this._cancelQueryFeatureCount = () => {
      const { _queryFeatureCountAbortController: e22 } = this;
      e22 && e22.abort(), this._queryFeatureCountAbortController = null;
    }, this._cancelQueryPage = () => {
      const { _queryPageAbortController: e22 } = this;
      e22 && e22.abort(), this._queryPageAbortController = null;
    }, this._queryController = async () => {
      this._cancelQuery();
      const e22 = new AbortController();
      this._queryAbortController = e22, await g(this._query()), this._queryAbortController === e22 && (this._queryAbortController = null);
    }, this._queryFeatureCountController = async () => {
      this._cancelQueryFeatureCount();
      const e22 = new AbortController();
      this._queryFeatureCountAbortController = e22, await g(this._queryFeatureCount()), this._queryFeatureCountAbortController === e22 && (this._queryFeatureCountAbortController = null);
    }, this._queryPageController = async () => {
      const e22 = new AbortController();
      this._queryPageAbortController = e22, await g(this._queryPage()), this._queryPageAbortController === e22 && (this._queryPageAbortController = null);
    }, this._queryThrottled = e7(this._queryController, b10, this), this._queryFeatureCountThrottled = e7(this._queryFeatureCountController, b10, this), this._queryPageThrottled = e7(this._queryPageController, b10, this), this._query = async () => {
      const { _queryAbortController: e22, relatedFeatures: t26 } = this;
      this._destroyRelatedFeatureViewModels(), this.featurePage = 1, t26.removeAll(), t26.addMany(this._sliceFeatures(await this._queryRelatedFeatures({ signal: e22 == null ? void 0 : e22.signal })));
    }, this.handles.add([l3(() => [this.displayCount, this.graphic, this.layer, this.map, this.orderByFieldsFixedCasing, this.relationshipId, this.featuresPerPage, this.showAllEnabled, this.canQuery, this.featureCount], () => this._queryThrottled(), h), l3(() => [this.featurePage, this.showAllEnabled], () => this._queryPageThrottled()), l3(() => [this.layer, this.relationshipId, this.objectId, this.canQuery], () => this._queryFeatureCountThrottled())]);
  }
  destroy() {
    this._destroyRelatedFeatureViewModels(), this.relatedFeatures.removeAll(), this._cancelQuery(), this._cancelQueryFeatureCount(), this._cancelQueryPage();
  }
  set featurePage(e21) {
    const { featuresPerPage: t26, featureCount: r20 } = this, o27 = 1, l25 = Math.ceil(r20 / t26) || 1;
    this._set("featurePage", Math.min(Math.max(e21, o27), l25));
  }
  get featurePage() {
    return this._get("featurePage");
  }
  get orderByFieldsFixedCasing() {
    const { orderByFields: e21, relatedLayer: t26 } = this;
    return e21 && (t26 == null ? void 0 : t26.loaded) ? e21.map((e22) => {
      const r20 = e22.clone(), o27 = L4(e22.field, t26);
      return r20.field = o27, r20;
    }) : e21 ?? [];
  }
  get supportsCacheHint() {
    var _a, _b, _c;
    return !!((_c = (_b = (_a = this.layer) == null ? void 0 : _a.capabilities) == null ? void 0 : _b.queryRelated) == null ? void 0 : _c.supportsCacheHint);
  }
  get canQuery() {
    var _a, _b;
    const e21 = (_b = (_a = this.layer) == null ? void 0 : _a.capabilities) == null ? void 0 : _b.queryRelated;
    return !!(this.relatedLayer && this.relationship && "number" == typeof this.relationshipId && "number" == typeof this.objectId && (e21 == null ? void 0 : e21.supportsCount) && (e21 == null ? void 0 : e21.supportsPagination));
  }
  get itemDescriptionFieldName() {
    var _a, _b;
    return ((_b = (_a = this.orderByFieldsFixedCasing) == null ? void 0 : _a[0]) == null ? void 0 : _b.field) || null;
  }
  set displayCount(e21) {
    const t26 = 0, r20 = 10;
    this._set("displayCount", Math.min(Math.max(e21, t26), r20));
  }
  get displayCount() {
    return this._get("displayCount");
  }
  get objectId() {
    var _a, _b;
    return (this.objectIdField && ((_b = (_a = this.graphic) == null ? void 0 : _a.attributes) == null ? void 0 : _b[this.objectIdField])) ?? null;
  }
  get objectIdField() {
    var _a;
    return ((_a = this.layer) == null ? void 0 : _a.objectIdField) || null;
  }
  get relatedFeatures() {
    return this._get("relatedFeatures") || new j2();
  }
  get relatedLayer() {
    const { layer: e21, map: t26, relationship: r20 } = this;
    return (e21 == null ? void 0 : e21.loaded) && t26 && r20 ? ge(t26, e21, r20) ?? null : null;
  }
  get relationship() {
    var _a;
    const { relationshipId: e21, layer: t26 } = this;
    return "number" == typeof e21 ? ((_a = t26 == null ? void 0 : t26.relationships) == null ? void 0 : _a.find(({ id: t27 }) => t27 === e21)) ?? null : null;
  }
  get relatedFeatureViewModels() {
    return this._get("relatedFeatureViewModels") || new j2();
  }
  get state() {
    const { _queryAbortController: e21, _queryFeatureCountAbortController: t26, _queryPageAbortController: r20, canQuery: o27 } = this;
    return t26 ? "loading" : e21 || r20 ? "querying" : o27 ? "ready" : "disabled";
  }
  _destroyRelatedFeatureViewModels() {
    var _a;
    (_a = this.relatedFeatureViewModels) == null ? void 0 : _a.forEach((e21) => !e21.destroyed && e21.destroy()), this.relatedFeatureViewModels.removeAll();
  }
  async _queryFeatureCount() {
    const { layer: e21, relatedLayer: t26, relationshipId: r20, objectId: o27, _queryFeatureCountAbortController: l25, canQuery: s22, supportsCacheHint: a32 } = this;
    if (await (e21 == null ? void 0 : e21.load()), await (t26 == null ? void 0 : t26.load()), !s22 || !e21 || !t26) return void this._set("featureCount", 0);
    const n27 = t26.createQuery(), u18 = new d2({ cacheHint: a32, relationshipId: r20, returnGeometry: false, objectIds: [o27], where: s(n27.where, void 0) }), d15 = await e21.queryRelatedFeaturesCount(u18, { signal: l25 == null ? void 0 : l25.signal });
    this._set("featureCount", d15[o27] || 0);
  }
  _sliceFeatures(e21) {
    const { showAllEnabled: t26, displayCount: r20 } = this;
    return t26 ? e21 : r20 ? e21.slice(0, r20) : [];
  }
  async _queryPage() {
    const { relatedFeatures: e21, featurePage: t26, showAllEnabled: r20, _queryPageAbortController: o27 } = this;
    !r20 || t26 < 2 || e21.addMany(await this._queryRelatedFeatures({ signal: o27 == null ? void 0 : o27.signal }));
  }
  async _queryRelatedFeatures(e21) {
    var _a;
    const { orderByFieldsFixedCasing: t26, showAllEnabled: r20, featuresPerPage: o27, displayCount: l25, layer: s22, relationshipId: a32, featurePage: u18, featureCount: d15, relatedLayer: y16, supportsCacheHint: h18 } = this, { canQuery: p30, objectId: c34 } = this;
    if (!p30 || !s22 || !y16) return [];
    const _9 = r20 ? ((u18 - 1) * o27 + d15) % d15 : 0, g16 = r20 ? o27 : l25, b16 = y16.objectIdField, F7 = [...t26 == null ? void 0 : t26.map((e22) => e22.field), b16].filter(r), m24 = t26 == null ? void 0 : t26.map((e22) => `${e22.field} ${e22.order}`), f17 = y16.createQuery(), q2 = new d2({ orderByFields: m24, start: _9, num: g16, outFields: F7, cacheHint: h18, relationshipId: a32, returnGeometry: false, objectIds: [c34], where: s(f17.where, void 0) }), A7 = ((_a = (await s22.queryRelatedFeatures(q2, { signal: e21 == null ? void 0 : e21.signal }))[c34]) == null ? void 0 : _a.features) || [];
    return A7.forEach((e22) => {
      e22.sourceLayer = y16, e22.layer = y16;
    }), A7;
  }
};
e([y()], F4.prototype, "_queryAbortController", void 0), e([y()], F4.prototype, "_queryPageAbortController", void 0), e([y()], F4.prototype, "_queryFeatureCountAbortController", void 0), e([y({ value: 1 })], F4.prototype, "featurePage", null), e([y()], F4.prototype, "featuresPerPage", void 0), e([y({ readOnly: true })], F4.prototype, "orderByFieldsFixedCasing", null), e([y({ readOnly: true })], F4.prototype, "supportsCacheHint", null), e([y({ readOnly: true })], F4.prototype, "canQuery", null), e([y()], F4.prototype, "description", void 0), e([y({ readOnly: true })], F4.prototype, "itemDescriptionFieldName", null), e([y({ value: 3 })], F4.prototype, "displayCount", null), e([y({ type: g3 })], F4.prototype, "graphic", void 0), e([y()], F4.prototype, "layer", void 0), e([y()], F4.prototype, "map", void 0), e([y({ readOnly: true })], F4.prototype, "objectId", null), e([y({ readOnly: true })], F4.prototype, "objectIdField", null), e([y()], F4.prototype, "orderByFields", void 0), e([y({ readOnly: true })], F4.prototype, "relatedFeatures", null), e([y({ readOnly: true })], F4.prototype, "relatedLayer", null), e([y({ readOnly: true })], F4.prototype, "relationship", null), e([y()], F4.prototype, "featureCount", void 0), e([y({ readOnly: true })], F4.prototype, "relatedFeatureViewModels", null), e([y()], F4.prototype, "relationshipId", void 0), e([y()], F4.prototype, "showAllEnabled", void 0), e([y()], F4.prototype, "state", null), e([y()], F4.prototype, "title", void 0), F4 = e([a2("esri.widgets.Feature.FeatureRelationship.FeatureRelationshipViewModel")], F4);
var m12 = F4;

// node_modules/@arcgis/core/widgets/Feature/FeatureRelationship.js
var g9 = "esri-feature";
var w13 = `${g9}-relationship`;
var b11 = { base: w13, esriWidget: "esri-widget", listContainer: `${w13}__list`, listContainerQuerying: `${w13}__list--querying`, featureObserver: `${g9}__feature-observer`, stickySpinnerContainer: `${g9}__sticky-loading-container`, loadingSpinnerContainer: `${g9}__loading-container`, spinner: `${g9}__loading-spinner`, iconLoading: "esri-icon-loading-indicator esri-rotating" };
var f11 = { title: true, description: true };
var y9 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._featureElementInfo = null, this._relatedFeatureIntersectionObserverNode = null, this._relatedFeatureIntersectionObserver = new IntersectionObserver(([e22]) => {
      (e22 == null ? void 0 : e22.isIntersecting) && this._increaseFeaturePage();
    }, { root: window.document }), this.headingLevel = 2, this.viewModel = new m12(), this.messages = null, this.messagesCommon = null, this.visibleElements = { ...f11 }, this._increaseFeaturePage = () => {
      const { state: e22, showAllEnabled: t27, relatedFeatures: s22, featuresPerPage: r20, featurePage: i28 } = this.viewModel;
      "ready" === e22 && t27 && s22.length >= r20 * i28 && this.viewModel.featurePage++;
    };
  }
  initialize() {
    this._featureElementInfo = new l10(), this.addHandles([l3(() => [this.viewModel.description, this.viewModel.title, this.headingLevel], () => this._setupFeatureElementInfo(), h), l3(() => [this.viewModel.state, this.viewModel.showAllEnabled, this._relatedFeatureIntersectionObserverNode], () => this._handleRelatedFeatureObserverChange()), a6(() => this.viewModel.relatedFeatureViewModels, "change", () => this._setupRelatedFeatureViewModels())]);
  }
  loadDependencies() {
    return Promise.all([import("./calcite-list-MTDB2VA4.js"), import("./calcite-list-item-ZCXDY4LU.js"), import("./calcite-icon-AXY32QDW.js"), import("./calcite-notice-XIIT5X5V.js")]);
  }
  destroy() {
    this._unobserveRelatedFeatureObserver(), this._featureElementInfo = a(this._featureElementInfo);
  }
  get displayShowAllButton() {
    const { showAllEnabled: e21, featureCount: t26, displayCount: s22 } = this.viewModel;
    return !e21 && !!t26 && (t26 > s22 || 0 === s22);
  }
  get displayListItems() {
    return this.displayShowAllButton || this.viewModel.relatedFeatureViewModels.length > 0;
  }
  get description() {
    return this.viewModel.description;
  }
  set description(e21) {
    this.viewModel.description = e21;
  }
  get featureCountDescription() {
    const { messages: e21 } = this, { featureCount: t26 } = this.viewModel;
    return s6(1 === t26 ? e21 == null ? void 0 : e21.numberRecord : e21 == null ? void 0 : e21.numberRecords, { number: t26 });
  }
  get title() {
    return this.viewModel.title;
  }
  set title(e21) {
    this.viewModel.title = e21;
  }
  castVisibleElements(e21) {
    return { ...f11, ...e21 };
  }
  renderStickyLoading() {
    return "querying" === this.viewModel.state ? n5("div", { key: "sticky-loader", class: b11.stickySpinnerContainer }, this.renderLoadingIcon()) : null;
  }
  renderLoadingIcon() {
    return n5("span", { class: this.classes(b11.iconLoading, b11.spinner) });
  }
  renderLoading() {
    return n5("div", { key: "loading-container", class: b11.loadingSpinnerContainer }, this.renderLoadingIcon());
  }
  renderShowAllIconNode() {
    return n5("calcite-icon", { scale: "s", icon: "list", slot: "content-end" });
  }
  renderChevronIconNode() {
    const e21 = f4(this.container) ? "chevron-left" : "chevron-right";
    return n5("calcite-icon", { scale: "s", icon: e21, slot: "content-end" });
  }
  renderRelatedFeature(e21) {
    var _a, _b;
    const { itemDescriptionFieldName: t26 } = this.viewModel, s22 = e21.title;
    e21.description = t26 && ((_a = e21.formattedAttributes) == null ? void 0 : _a.global[t26]);
    const r20 = "loading" === e21.state;
    return n5("calcite-list-item", { key: e21.uid, label: r20 ? `${(_b = this.messagesCommon) == null ? void 0 : _b.loading}…` : s22, description: r20 ? "…" : e21.description ?? "", onCalciteListItemSelect: () => this.emit("select-record", { featureViewModel: e21 }) }, this.renderChevronIconNode());
  }
  renderShowAllListItem() {
    var _a;
    return this.displayShowAllButton ? n5("calcite-list-item", { key: "show-all-item", label: (_a = this.messages) == null ? void 0 : _a.showAll, description: this.featureCountDescription, onCalciteListItemSelect: () => this.emit("show-all-records") }, this.renderShowAllIconNode()) : null;
  }
  renderNoRelatedFeaturesMessage() {
    var _a;
    return n5("calcite-notice", { key: "no-related-features-message", icon: "information", open: true, kind: "brand", scale: "s", width: "full" }, n5("div", { slot: "message" }, (_a = this.messages) == null ? void 0 : _a.noRelatedFeatures));
  }
  renderFeatureObserver() {
    return n5("div", { key: "feature-observer", class: b11.featureObserver, bind: this, afterCreate: this._relatedFeatureIntersectionObserverCreated });
  }
  renderList() {
    const { relatedFeatureViewModels: e21 } = this.viewModel;
    return n5("calcite-list", null, e21.toArray().map((e22) => this.renderRelatedFeature(e22)), this.renderShowAllListItem());
  }
  renderRelatedFeatures() {
    const { displayListItems: e21 } = this, { state: t26 } = this.viewModel;
    return n5("div", { key: "list-container", class: this.classes(b11.listContainer, { [b11.listContainerQuerying]: "querying" === t26 }) }, e21 ? this.renderList() : "ready" === t26 ? this.renderNoRelatedFeaturesMessage() : null, this.renderStickyLoading(), this.renderFeatureObserver());
  }
  renderRelationshipNotFound() {
    var _a;
    return n5("calcite-notice", { key: "relationship-not-found", icon: "exclamation-mark-triangle", open: true, kind: "danger", scale: "s", width: "full" }, n5("div", { slot: "message" }, (_a = this.messages) == null ? void 0 : _a.relationshipNotFound));
  }
  render() {
    var _a;
    const { state: e21 } = this.viewModel;
    return n5("div", { class: this.classes(b11.base, b11.esriWidget) }, (_a = this._featureElementInfo) == null ? void 0 : _a.render(), "loading" === e21 ? this.renderLoading() : "disabled" === e21 ? this.renderRelationshipNotFound() : this.renderRelatedFeatures());
  }
  _setupRelatedFeatureViewModels() {
    const { relatedFeatureViewModels: e21 } = this.viewModel, t26 = "related-feature-viewmodels";
    this.removeHandles(t26), e21 == null ? void 0 : e21.forEach((e22) => {
      this.addHandles(l3(() => [e22.title, e22.state], () => this.scheduleRender(), h), t26);
    }), this.scheduleRender();
  }
  _setupFeatureElementInfo() {
    var _a;
    const { headingLevel: e21, visibleElements: t26 } = this, s22 = t26.description && this.description, r20 = t26.title && this.title;
    (_a = this._featureElementInfo) == null ? void 0 : _a.set({ description: s22, title: r20, headingLevel: e21 });
  }
  async _handleRelatedFeatureObserverChange() {
    this._unobserveRelatedFeatureObserver();
    const { state: e21, showAllEnabled: t26 } = this.viewModel;
    await U(0), this._relatedFeatureIntersectionObserverNode && "ready" === e21 && t26 && this._relatedFeatureIntersectionObserver.observe(this._relatedFeatureIntersectionObserverNode);
  }
  _relatedFeatureIntersectionObserverCreated(e21) {
    this._relatedFeatureIntersectionObserverNode = e21;
  }
  _unobserveRelatedFeatureObserver() {
    this._relatedFeatureIntersectionObserverNode && this._relatedFeatureIntersectionObserver.unobserve(this._relatedFeatureIntersectionObserverNode);
  }
};
e([y()], y9.prototype, "_relatedFeatureIntersectionObserverNode", void 0), e([y({ readOnly: true })], y9.prototype, "displayShowAllButton", null), e([y({ readOnly: true })], y9.prototype, "displayListItems", null), e([y()], y9.prototype, "description", null), e([y({ readOnly: true })], y9.prototype, "featureCountDescription", null), e([y()], y9.prototype, "headingLevel", void 0), e([y()], y9.prototype, "title", null), e([y({ type: m12 })], y9.prototype, "viewModel", void 0), e([y(), e4("esri/widgets/Feature/t9n/Feature")], y9.prototype, "messages", void 0), e([y(), e4("esri/t9n/common")], y9.prototype, "messagesCommon", void 0), e([y()], y9.prototype, "visibleElements", void 0), e([s4("visibleElements")], y9.prototype, "castVisibleElements", null), y9 = e([a2("esri.widgets.Feature.FeatureRelationship")], y9);
var F5 = y9;

// node_modules/@arcgis/core/arcade/featureset/support/FeatureSetQueryInterceptor.js
var e12 = class {
  constructor(e21, a32) {
    this.preLayerQueryCallback = e21, this.preRequestCallback = a32, this.preLayerQueryCallback || (this.preLayerQueryCallback = (e22) => {
    }), this.preRequestCallback || (this.preLayerQueryCallback = (e22) => {
    });
  }
};

// node_modules/@arcgis/core/widgets/Feature/FeatureViewModel.js
var G2;
var H3 = 1;
var z2 = "content-view-models";
var J2 = "relationship-view-models";
var K2 = { attachmentsContent: true, chartAnimation: true, customContent: true, expressionContent: true, fieldsContent: true, mediaContent: true, textContent: true, relationshipContent: true };
var W3 = G2 = class extends s7(v3) {
  constructor(e21) {
    super(e21), this._handles = new t4(), this._error = null, this._featureAbortController = null, this._graphicChangedThrottled = e7(this._graphicChanged, H3, this), this._expressionAttributes = null, this._graphicExpressionAttributes = null, this.abilities = { ...K2 }, this.content = null, this.contentViewModels = [], this.description = null, this.defaultPopupTemplateEnabled = false, this.formattedAttributes = null, this.lastEditInfo = null, this.relatedInfos = /* @__PURE__ */ new Map(), this.title = "", this.view = null, this._isAllowedContentType = (e22) => {
      const { abilities: t26 } = this;
      return "attachments" === e22.type && !!t26.attachmentsContent || "custom" === e22.type && !!t26.customContent || "fields" === e22.type && !!t26.fieldsContent || "media" === e22.type && !!t26.mediaContent || "text" === e22.type && !!t26.textContent || "expression" === e22.type && !!t26.expressionContent || "relationship" === e22.type && !!t26.relationshipContent;
    }, this._handles.add(l3(() => [this.graphic, this._effectivePopupTemplate, this.abilities], () => this._graphicChangedThrottled(), h));
  }
  destroy() {
    this._clear(), this._cancelFeatureQuery(), this._error = null, this._handles.destroy(), this.graphic = null, this._destroyContentViewModels(), this.relatedInfos.clear();
  }
  get _effectivePopupTemplate() {
    return r(this.graphic) ? this.graphic.getEffectivePopupTemplate(this.defaultPopupTemplateEnabled) : null;
  }
  get _fieldInfoMap() {
    return Y2(ee(this._effectivePopupTemplate), this._sourceLayer);
  }
  get _sourceLayer() {
    return j6(this.graphic);
  }
  castAbilities(e21) {
    return { ...K2, ...e21 };
  }
  get isTable() {
    var _a;
    return ((_a = this._sourceLayer) == null ? void 0 : _a.isTable) || false;
  }
  get state() {
    return this.graphic ? this._error ? "error" : this.waitingForContent ? "loading" : "ready" : "disabled";
  }
  set graphic(e21) {
    this._set("graphic", e21 ? e21.clone() : null);
  }
  get spatialReference() {
    var _a;
    return ((_a = this.view) == null ? void 0 : _a.spatialReference) ?? null;
  }
  set spatialReference(e21) {
    this._override("spatialReference", e21);
  }
  get map() {
    var _a;
    return ((_a = this.view) == null ? void 0 : _a.map) || null;
  }
  set map(e21) {
    this._override("map", e21);
  }
  get waitingForContent() {
    return !!this._featureAbortController;
  }
  setActiveMedia(e21, t26) {
    const r20 = this.contentViewModels[e21];
    r20 instanceof M3 && r20.setActiveMedia(t26);
  }
  nextMedia(e21) {
    const t26 = this.contentViewModels[e21];
    t26 instanceof M3 && t26.next();
  }
  previousMedia(e21) {
    const t26 = this.contentViewModels[e21];
    t26 instanceof M3 && t26.previous();
  }
  async updateGeometry() {
    var _a, _b;
    const { graphic: e21, spatialReference: t26, _sourceLayer: r20 } = this;
    await (r20 == null ? void 0 : r20.load());
    const i28 = r20 == null ? void 0 : r20.objectIdField;
    if (!i28 || !e21 || !r20) return;
    const s22 = (_a = e21 == null ? void 0 : e21.attributes) == null ? void 0 : _a[i28];
    if (null == s22) return;
    const o27 = [s22];
    if (!e21.geometry) {
      const i29 = (_b = await oe({ layer: r20, graphic: e21, outFields: [], objectIds: o27, returnGeometry: true, spatialReference: t26 })) == null ? void 0 : _b.geometry;
      i29 && (e21.geometry = i29);
    }
  }
  _clear() {
    this._set("title", ""), this._set("content", null), this._set("formattedAttributes", null);
  }
  async _graphicChanged() {
    this._cancelFeatureQuery(), this._error = null, this._clear();
    const { graphic: e21 } = this;
    if (!e21) return;
    const t26 = new AbortController();
    this._featureAbortController = t26;
    try {
      await this._queryFeature({ signal: t26.signal });
    } catch (r20) {
      j(r20) || (this._error = r20, s2.getLogger(this.declaredClass).error("error", "The popupTemplate could not be displayed for this feature.", { error: r20, graphic: e21, popupTemplate: this._effectivePopupTemplate }));
    }
    this._featureAbortController === t26 && (this._featureAbortController = null);
  }
  _cancelFeatureQuery() {
    const { _featureAbortController: e21 } = this;
    e21 && e21.abort(), this._featureAbortController = null;
  }
  _compileContentElement(e21, t26) {
    return "attachments" === e21.type ? this._compileAttachments(e21, t26) : "custom" === e21.type ? this._compileCustom(e21, t26) : "fields" === e21.type ? this._compileFields(e21, t26) : "media" === e21.type ? this._compileMedia(e21, t26) : "text" === e21.type ? this._compileText(e21, t26) : "expression" === e21.type ? this._compileExpression(e21, t26) : "relationship" === e21.type ? this._compileRelationship(e21, t26) : void 0;
  }
  _compileContent(e21) {
    if (this._destroyContentViewModels(), this.graphic) return Array.isArray(e21) ? e21.filter(this._isAllowedContentType).map((e22, t26) => this._compileContentElement(e22, t26)).filter(r) : "string" == typeof e21 ? this._compileText(new c7({ text: e21 }), 0).text : e21;
  }
  _destroyContentViewModels() {
    var _a, _b;
    (_a = this._handles) == null ? void 0 : _a.remove(J2), (_b = this._handles) == null ? void 0 : _b.remove(z2), this.contentViewModels.forEach((e21) => e21 && !e21.destroyed && e21.destroy()), this._set("contentViewModels", []);
  }
  _matchesFeature(e21, t26) {
    var _a;
    const r20 = (_a = e21 == null ? void 0 : e21.graphic) == null ? void 0 : _a.getObjectId(), i28 = t26 == null ? void 0 : t26.getObjectId();
    return r(r20) && r(i28) && r20 === i28;
  }
  _setRelatedFeaturesViewModels({ relatedFeatureViewModels: e21, relatedFeatures: t26, map: r20 }) {
    const { view: i28, spatialReference: s22 } = this;
    t26 == null ? void 0 : t26.filter(Boolean).forEach((t27) => {
      e21.find((e22) => this._matchesFeature(e22, t27)) || e21.add(new G2({ abilities: { relationshipContent: false }, map: r20, view: i28, spatialReference: s22, graphic: t27 }));
    }), e21.forEach((r21) => {
      const i29 = t26 == null ? void 0 : t26.find((e22) => this._matchesFeature(r21, e22));
      i29 || e21.remove(r21);
    });
  }
  _setExpressionContentVM(e21, t26) {
    const r20 = this.formattedAttributes, { contentElement: i28, contentElementViewModel: s22 } = e21, o27 = i28 == null ? void 0 : i28.type;
    s22 && o27 && ("fields" === o27 && (this._createFieldsFormattedAttributes({ contentElement: i28, contentElementIndex: t26, formattedAttributes: r20 }), s22.set(this._createFieldsVMParams(i28, t26))), "media" === o27 && (this._createMediaFormattedAttributes({ contentElement: i28, contentElementIndex: t26, formattedAttributes: r20 }), s22.set(this._createMediaVMParams(i28, t26))), "text" === o27 && s22.set(this._createTextVMParams(i28)));
  }
  _compileRelationship(e21, t26) {
    const { displayCount: r20, orderByFields: i28, relationshipId: s22, title: o27, description: n27 } = e21, { _sourceLayer: a32, graphic: l25, map: p30 } = this, c34 = new m12({ displayCount: r20, graphic: l25, orderByFields: i28, relationshipId: s22, layer: a32, map: p30, ...this._compileTitleAndDesc({ title: o27, description: n27 }) });
    return this.contentViewModels[t26] = c34, this._handles.add(a6(() => c34.relatedFeatures, "change", () => this._setRelatedFeaturesViewModels(c34)), J2), e21;
  }
  _compileExpression(e21, t26) {
    const { expressionInfo: r20 } = e21, { graphic: i28, map: s22, spatialReference: o27, view: n27 } = this, a32 = new C4({ expressionInfo: r20, graphic: i28, interceptor: G2.interceptor, map: s22, spatialReference: o27, view: n27 });
    return this.contentViewModels[t26] = a32, this._handles.add(l3(() => a32.contentElementViewModel, () => this._setExpressionContentVM(a32, t26), h), z2), e21;
  }
  _compileAttachments(e21, t26) {
    const { graphic: r20 } = this, { description: i28, title: s22 } = e21;
    return this.contentViewModels[t26] = new c13({ graphic: r20, ...this._compileTitleAndDesc({ title: s22, description: i28 }) }), e21;
  }
  _compileCustom(e21, t26) {
    const { graphic: r20 } = this, { creator: i28, destroyer: s22 } = e21;
    return this.contentViewModels[t26] = new l11({ graphic: r20, creator: i28, destroyer: s22 }), e21;
  }
  _compileTitleAndDesc({ title: e21, description: t26 }) {
    const { _fieldInfoMap: r20, _sourceLayer: i28, graphic: s22, formattedAttributes: o27 } = this, n27 = s22 == null ? void 0 : s22.attributes, a32 = this._expressionAttributes, l25 = o27.global;
    return { title: S3({ attributes: n27, fieldInfoMap: r20, globalAttributes: l25, expressionAttributes: a32, layer: i28, text: e21 }), description: S3({ attributes: n27, fieldInfoMap: r20, globalAttributes: l25, expressionAttributes: a32, layer: i28, text: t26 }) };
  }
  _createFieldsVMParams(e21, t26) {
    var _a;
    const r20 = this._effectivePopupTemplate, i28 = this.formattedAttributes, s22 = { ...i28 == null ? void 0 : i28.global, ...i28 == null ? void 0 : i28.content[t26] }, o27 = (_a = (e21 == null ? void 0 : e21.fieldInfos) || (r20 == null ? void 0 : r20.fieldInfos)) == null ? void 0 : _a.filter(({ fieldName: e22 }) => M2(e22) || ue(e22) || s22.hasOwnProperty(e22)), n27 = r20 == null ? void 0 : r20.expressionInfos, { description: a32, title: l25 } = e21;
    return { attributes: s22, expressionInfos: n27, fieldInfos: o27, ...this._compileTitleAndDesc({ title: l25, description: a32 }) };
  }
  _compileFields(e21, t26) {
    const r20 = e21.clone(), i28 = new n13(this._createFieldsVMParams(e21, t26));
    return this.contentViewModels[t26] = i28, r20.fieldInfos = i28.formattedFieldInfos.slice(0), r20;
  }
  _createMediaVMParams(e21, t26) {
    const { abilities: r20, graphic: i28, _fieldInfoMap: s22, _effectivePopupTemplate: o27, relatedInfos: n27, _sourceLayer: a32, _expressionAttributes: l25 } = this, p30 = this.formattedAttributes, c34 = (i28 == null ? void 0 : i28.attributes) ?? {}, { description: d15, mediaInfos: u18, title: h18 } = e21;
    return { abilities: { chartAnimation: r20.chartAnimation }, activeMediaInfoIndex: e21.activeMediaInfoIndex || 0, attributes: c34, isAggregate: i28 == null ? void 0 : i28.isAggregate, layer: a32, fieldInfoMap: s22, formattedAttributes: { ...p30 == null ? void 0 : p30.global, ...p30 == null ? void 0 : p30.content[t26] }, expressionAttributes: l25, mediaInfos: u18, popupTemplate: o27, relatedInfos: n27, ...this._compileTitleAndDesc({ title: h18, description: d15 }) };
  }
  _compileMedia(e21, t26) {
    const r20 = e21.clone(), i28 = new M3(this._createMediaVMParams(e21, t26));
    return r20.mediaInfos = i28.formattedMediaInfos.slice(0), this.contentViewModels[t26] = i28, r20;
  }
  _createTextVMParams(e21) {
    var _a;
    const { graphic: t26, _fieldInfoMap: r20, _sourceLayer: i28, _expressionAttributes: s22 } = this;
    if (e21 && e21.text) {
      const o27 = (t26 == null ? void 0 : t26.attributes) ?? {}, n27 = ((_a = this.formattedAttributes) == null ? void 0 : _a.global) ?? {};
      e21.text = S3({ attributes: o27, fieldInfoMap: r20, globalAttributes: n27, expressionAttributes: s22, layer: i28, text: e21.text });
    }
    return { graphic: t26, creator: e21.text };
  }
  _compileText(e21, t26) {
    const r20 = e21.clone();
    return this.contentViewModels[t26] = new l11(this._createTextVMParams(r20)), r20;
  }
  _compileLastEditInfo() {
    const { _effectivePopupTemplate: e21, _sourceLayer: t26, graphic: r20 } = this;
    if (!e21) return;
    const { lastEditInfoEnabled: i28 } = e21, s22 = t26 == null ? void 0 : t26.editFieldsInfo;
    return i28 && s22 ? X(s22, r20 == null ? void 0 : r20.attributes) : void 0;
  }
  _compileTitle(e21) {
    var _a;
    const { _fieldInfoMap: t26, _sourceLayer: r20, graphic: i28, _expressionAttributes: s22 } = this, o27 = (i28 == null ? void 0 : i28.attributes) ?? {}, n27 = ((_a = this.formattedAttributes) == null ? void 0 : _a.global) ?? {};
    return S3({ attributes: o27, fieldInfoMap: t26, globalAttributes: n27, expressionAttributes: s22, layer: r20, text: e21 });
  }
  async _getTitle() {
    const { _effectivePopupTemplate: e21, graphic: t26 } = this;
    if (!t26) return null;
    const r20 = e21 == null ? void 0 : e21.title;
    return x2(r20, { graphic: t26 });
  }
  async _getContent() {
    const { _effectivePopupTemplate: e21, graphic: t26 } = this;
    if (!t26) return null;
    const r20 = e21 == null ? void 0 : e21.content;
    return x2(r20, { graphic: t26 });
  }
  async _queryFeature(e21) {
    const { _featureAbortController: t26, _sourceLayer: r20, graphic: i28, _effectivePopupTemplate: s22 } = this, o27 = this.map, n27 = this.view, a32 = this.spatialReference;
    if (t26 !== this._featureAbortController || !i28) return;
    await le({ graphic: i28, popupTemplate: s22, layer: r20, spatialReference: a32 }, e21);
    const { content: { value: l25 }, title: { value: c34 } } = await E({ content: this._getContent(), title: this._getTitle() }), { expressionAttributes: { value: d15 } } = await E({ checkForRelatedFeatures: this._checkForRelatedFeatures(e21), expressionAttributes: m10({ expressionInfos: s22 == null ? void 0 : s22.expressionInfos, spatialReference: a32, graphic: i28, map: o27, interceptor: G2.interceptor, view: n27 }) });
    t26 === this._featureAbortController && i28 && (this._expressionAttributes = d15, this._graphicExpressionAttributes = { ...i28.attributes, ...d15 }, this._set("formattedAttributes", this._createFormattedAttributes(l25)), this._set("title", this._compileTitle(c34)), this._set("lastEditInfo", this._compileLastEditInfo() || null), this._set("content", this._compileContent(l25) || null));
  }
  _createMediaFormattedAttributes({ contentElement: e21, contentElementIndex: t26, formattedAttributes: r20 }) {
    const { _effectivePopupTemplate: i28, graphic: s22, relatedInfos: o27, _sourceLayer: n27, _fieldInfoMap: a32, _graphicExpressionAttributes: l25 } = this;
    r20.content[t26] = ie2({ fieldInfos: i28 == null ? void 0 : i28.fieldInfos, graphic: s22, attributes: { ...l25, ...e21.attributes }, layer: n27, fieldInfoMap: a32, relatedInfos: o27 });
  }
  _createFieldsFormattedAttributes({ contentElement: e21, contentElementIndex: t26, formattedAttributes: r20 }) {
    if (e21.fieldInfos) {
      const { graphic: i28, relatedInfos: s22, _sourceLayer: o27, _fieldInfoMap: n27, _graphicExpressionAttributes: a32 } = this;
      r20.content[t26] = ie2({ fieldInfos: e21.fieldInfos, graphic: i28, attributes: { ...a32, ...e21.attributes }, layer: o27, fieldInfoMap: n27, relatedInfos: s22 });
    }
  }
  _createFormattedAttributes(e21) {
    const { _effectivePopupTemplate: t26, graphic: r20, relatedInfos: i28, _sourceLayer: s22, _fieldInfoMap: o27, _graphicExpressionAttributes: n27 } = this, a32 = t26 == null ? void 0 : t26.fieldInfos, l25 = { global: ie2({ fieldInfos: a32, graphic: r20, attributes: n27, layer: s22, fieldInfoMap: o27, relatedInfos: i28 }), content: [] };
    return Array.isArray(e21) && e21.forEach((e22, t27) => {
      "fields" === e22.type && this._createFieldsFormattedAttributes({ contentElement: e22, contentElementIndex: t27, formattedAttributes: l25 }), "media" === e22.type && this._createMediaFormattedAttributes({ contentElement: e22, contentElementIndex: t27, formattedAttributes: l25 });
    }), l25;
  }
  _checkForRelatedFeatures(e21) {
    const { graphic: t26, _effectivePopupTemplate: r20 } = this;
    return this._queryRelatedInfos(t26, ee(r20), e21);
  }
  async _queryRelatedInfos(e21, t26, r20) {
    const { relatedInfos: i28, _sourceLayer: s22 } = this;
    i28.clear();
    const o27 = r(s22 == null ? void 0 : s22.associatedLayer) ? await (s22 == null ? void 0 : s22.associatedLayer.load(r20)) : s22;
    if (!o27 || !e21) return;
    const n27 = t26.filter((e22) => e22 && ue(e22.fieldName));
    if (!n27 || !n27.length) return;
    t26.forEach((e22) => this._configureRelatedInfo(e22, o27));
    const l25 = await R5({ relatedInfos: i28, layer: o27 }, r20);
    Object.keys(l25).forEach((e22) => {
      var _a;
      const t27 = i28.get(e22.toString()), r21 = (_a = l25[e22]) == null ? void 0 : _a.value;
      t27 && r21 && (t27.layerInfo = r21.data);
    });
    const p30 = await $2({ graphic: e21, relatedInfos: i28, layer: o27 }, r20);
    Object.keys(p30).forEach((e22) => {
      var _a;
      F3((_a = p30[e22]) == null ? void 0 : _a.value, i28.get(e22.toString()));
    });
  }
  _configureRelatedInfo(e21, t26) {
    const { relatedInfos: r20 } = this, i28 = m9(e21.fieldName);
    if (!i28) return;
    const { layerId: s22, fieldName: o27 } = i28;
    if (!s22) return;
    const n27 = r20.get(s22.toString()) || g6(s22, t26);
    n27 && (U6({ relatedInfo: n27, fieldName: o27, fieldInfo: e21 }), this.relatedInfos.set(s22, n27));
  }
};
W3.interceptor = new e12(me, ye), e([y()], W3.prototype, "_error", void 0), e([y()], W3.prototype, "_featureAbortController", void 0), e([y({ readOnly: true })], W3.prototype, "_effectivePopupTemplate", null), e([y({ readOnly: true })], W3.prototype, "_fieldInfoMap", null), e([y({ readOnly: true })], W3.prototype, "_sourceLayer", null), e([y()], W3.prototype, "abilities", void 0), e([s4("abilities")], W3.prototype, "castAbilities", null), e([y({ readOnly: true })], W3.prototype, "content", void 0), e([y({ readOnly: true })], W3.prototype, "contentViewModels", void 0), e([y()], W3.prototype, "description", void 0), e([y({ type: Boolean })], W3.prototype, "defaultPopupTemplateEnabled", void 0), e([y({ readOnly: true })], W3.prototype, "isTable", null), e([y({ readOnly: true })], W3.prototype, "state", null), e([y({ readOnly: true })], W3.prototype, "formattedAttributes", void 0), e([y({ type: g3, value: null })], W3.prototype, "graphic", null), e([y({ readOnly: true })], W3.prototype, "lastEditInfo", void 0), e([y({ readOnly: true })], W3.prototype, "relatedInfos", void 0), e([y()], W3.prototype, "spatialReference", null), e([y({ readOnly: true })], W3.prototype, "title", void 0), e([y()], W3.prototype, "map", null), e([y({ readOnly: true })], W3.prototype, "waitingForContent", null), e([y()], W3.prototype, "view", void 0), W3 = G2 = e([a2("esri.widgets.FeatureViewModel")], W3);
var X2 = W3;

// node_modules/@arcgis/core/widgets/Feature/resources.js
var e13 = "esri-feature";
var t13 = { iconText: "esri-icon-font-fallback-text", iconLoading: "esri-icon-loading-indicator esri-rotating", esriTable: "esri-widget__table", esriWidget: "esri-widget", base: e13, container: `${e13}__size-container`, title: `${e13}__title`, main: `${e13}__main-container`, btn: `${e13}__button`, icon: `${e13}__icon`, content: `${e13}__content`, contentNode: `${e13}__content-node`, contentElement: `${e13}__content-element`, text: `${e13}__text`, lastEditedInfo: `${e13}__last-edited-info`, fields: `${e13}__fields`, fieldHeader: `${e13}__field-header`, fieldData: `${e13}__field-data`, fieldDataDate: `${e13}__field-data--date`, loadingSpinnerContainer: `${e13}__loading-container`, spinner: `${e13}__loading-spinner` };

// node_modules/@arcgis/core/widgets/Feature/support/FeatureContentMixin.js
var i13 = (i28) => {
  let n27 = class extends i28 {
    constructor() {
      super(...arguments), this.renderNodeContent = (e21) => e6(e21) && !e21.destroyed ? n5("div", { class: t13.contentNode, key: e21 }, e21.render()) : e21 instanceof HTMLElement ? n5("div", { class: t13.contentNode, key: e21, bind: e21, afterCreate: this._attachToNode }) : t9(e21) ? n5("div", { class: t13.contentNode, key: e21, bind: e21.domNode, afterCreate: this._attachToNode }) : null;
    }
    _attachToNode(e21) {
      const o27 = this;
      e21.appendChild(o27);
    }
  };
  return n27 = e([a2("esri.widgets.Feature.ContentMixin")], n27), n27;
};

// node_modules/@arcgis/core/widgets/Feature.js
var E4;
var b12 = { title: true, content: true, lastEditedInfo: true };
var _4 = "relationship-handles";
var W4 = E4 = class extends i13(W) {
  constructor(e21, t26) {
    super(e21, t26), this._contentWidgets = [], this.flowItems = null, this.headingLevel = 2, this.messages = null, this.messagesCommon = null, this.messagesURIUtils = null, this.visibleElements = { ...b12 }, this.viewModel = new X2();
  }
  initialize() {
    this.addHandles(l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.contentViewModels;
    }, () => this._setupContentWidgets(), h));
  }
  loadDependencies() {
    return import("./calcite-notice-XIIT5X5V.js");
  }
  destroy() {
    this._destroyContentWidgets();
  }
  get graphic() {
    return this.viewModel.graphic;
  }
  set graphic(e21) {
    this.viewModel.graphic = e21;
  }
  get defaultPopupTemplateEnabled() {
    return this.viewModel.defaultPopupTemplateEnabled;
  }
  set defaultPopupTemplateEnabled(e21) {
    this.viewModel.defaultPopupTemplateEnabled = e21;
  }
  get isTable() {
    return this.viewModel.isTable;
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(e21) {
    this._overrideIfSome("label", e21);
  }
  get spatialReference() {
    return this.viewModel.spatialReference;
  }
  set spatialReference(e21) {
    this.viewModel.spatialReference = e21;
  }
  get title() {
    return this.viewModel.title;
  }
  castVisibleElements(e21) {
    return { ...b12, ...e21 };
  }
  get map() {
    return this.viewModel.map;
  }
  set map(e21) {
    this.viewModel.map = e21;
  }
  get view() {
    return this.viewModel.view;
  }
  set view(e21) {
    this.viewModel.view = e21;
  }
  render() {
    const { state: e21 } = this.viewModel, t26 = n5("div", { class: t13.container, key: "container" }, this.renderTitle(), "error" === e21 ? this.renderError() : "loading" === e21 ? this.renderLoading() : this.renderContentContainer());
    return n5("div", { class: this.classes(t13.base, t13.esriWidget) }, t26);
  }
  setActiveMedia(e21, t26) {
    return this.viewModel.setActiveMedia(e21, t26);
  }
  nextMedia(e21) {
    return this.viewModel.nextMedia(e21);
  }
  previousMedia(e21) {
    return this.viewModel.previousMedia(e21);
  }
  renderError() {
    const { messagesCommon: e21, messages: t26, visibleElements: s22 } = this;
    return n5("calcite-notice", { open: true, kind: "danger", icon: "exclamation-mark-circle", scale: "s" }, s22.title ? n5("div", { key: "error-title", slot: "title" }, e21.errorMessage) : null, n5("div", { key: "error-message", slot: "message" }, t26.loadingError));
  }
  renderLoading() {
    return n5("div", { key: "loading-container", class: t13.loadingSpinnerContainer }, n5("span", { class: this.classes(t13.iconLoading, t13.spinner) }));
  }
  renderContentContainer() {
    const { visibleElements: e21 } = this;
    return e21.content ? n5("div", { class: t13.main }, [this.renderContent(), this.renderLastEditInfo()]) : null;
  }
  renderTitle() {
    const { visibleElements: e21, title: t26 } = this;
    return e21.title ? n5(n8, { level: this.headingLevel, class: t13.title, innerHTML: t26 }) : null;
  }
  renderContent() {
    const e21 = this.viewModel.content, t26 = "content";
    if (!e21) return null;
    if (Array.isArray(e21)) return e21.length ? n5("div", { class: t13.contentNode, key: `${t26}-content-elements` }, e21.map(this.renderContentElement, this)) : null;
    if ("string" == typeof e21) {
      const e22 = this._contentWidgets[0];
      return !e22 || e22.destroyed ? null : n5("div", { class: t13.contentNode, key: `${t26}-content` }, e22.render());
    }
    return this.renderNodeContent(e21);
  }
  renderContentElement(e21, t26) {
    var _a;
    const { visibleElements: s22 } = this;
    if ("boolean" != typeof s22.content && !((_a = s22.content) == null ? void 0 : _a[e21.type])) return null;
    switch (e21.type) {
      case "attachments":
        return this.renderAttachments(t26);
      case "custom":
        return this.renderCustom(e21, t26);
      case "fields":
        return this.renderFields(t26);
      case "media":
        return this.renderMedia(t26);
      case "text":
        return this.renderText(e21, t26);
      case "expression":
        return this.renderExpression(t26);
      case "relationship":
        return this.renderRelationship(t26);
      default:
        return null;
    }
  }
  renderAttachments(e21) {
    const t26 = this._contentWidgets[e21];
    if (!t26 || t26.destroyed) return null;
    const { state: s22, attachmentInfos: i28 } = t26.viewModel;
    return "loading" === s22 || i28.length > 0 ? n5("div", { key: this._buildKey("attachments-element", e21), class: this.classes(t13.contentElement) }, t26.render()) : null;
  }
  renderRelationship(e21) {
    const t26 = this._contentWidgets[e21];
    return t26 && !t26.destroyed && this.flowItems ? n5("div", { key: this._buildKey("relationship-element", e21), class: t13.contentElement }, t26.render()) : null;
  }
  renderExpression(e21) {
    const t26 = this._contentWidgets[e21];
    return !t26 || t26.destroyed ? null : n5("div", { key: this._buildKey("expression-element", e21), class: t13.contentElement }, t26.render());
  }
  renderCustom(e21, t26) {
    const { creator: s22 } = e21, i28 = this._contentWidgets[t26];
    return !i28 || i28.destroyed ? null : s22 ? n5("div", { key: this._buildKey("custom-element", t26), class: t13.contentElement }, i28.render()) : null;
  }
  renderFields(e21) {
    const t26 = this._contentWidgets[e21];
    return !t26 || t26.destroyed ? null : n5("div", { key: this._buildKey("fields-element", e21), class: t13.contentElement }, t26.render());
  }
  renderMedia(e21) {
    const t26 = this._contentWidgets[e21];
    return !t26 || t26.destroyed ? null : n5("div", { key: this._buildKey("media-element", e21), class: t13.contentElement }, t26.render());
  }
  renderLastEditInfo() {
    const { visibleElements: e21, messages: t26 } = this, { lastEditInfo: s22 } = this.viewModel;
    if (!s22 || !e21.lastEditedInfo) return null;
    const { date: i28, user: n27 } = s22, r20 = "edit" === s22.type ? n27 ? t26.lastEditedByUser : t26.lastEdited : n27 ? t26.lastCreatedByUser : t26.lastCreated, o27 = s6(r20, { date: i28, user: n27 });
    return n5("div", { key: "edit-info-element", class: this.classes(t13.lastEditedInfo, t13.contentElement) }, o27);
  }
  renderText(e21, t26) {
    const s22 = e21.text, i28 = this._contentWidgets[t26];
    return !i28 || i28.destroyed ? null : s22 ? n5("div", { key: this._buildKey("text-element", t26), class: this.classes(t13.contentElement, t13.text) }, i28.render()) : null;
  }
  _buildKey(e21, ...t26) {
    return `${e21}__${this.get("viewModel.graphic.uid") || "0"}-${t26.join("-")}`;
  }
  _destroyContentWidget(e21) {
    e21 && (e21.viewModel = null, !e21.destroyed && e21.destroy());
  }
  _destroyContentWidgets() {
    this.removeHandles(_4), this._contentWidgets.forEach((e21) => this._destroyContentWidget(e21)), this._contentWidgets = [];
  }
  _addFeatureRelationshipHandles(e21) {
    const { flowItems: t26, visibleElements: i28 } = this;
    this.addHandles([a6(() => e21, "select-record", ({ featureViewModel: e22 }) => {
      t26 && (e22.abilities = { relationshipContent: true }, t26.push(new E4({ flowItems: t26, viewModel: e22, visibleElements: i28 })));
    }), a6(() => e21, "show-all-records", () => {
      if (!t26) return;
      const { viewModel: s22 } = e21;
      s22.showAllEnabled = true;
      const i29 = new F5({ visibleElements: { title: false, description: false }, viewModel: s22 });
      this._addFeatureRelationshipHandles(i29), t26.push(i29);
    })], _4);
  }
  _setupContentWidgets() {
    this._destroyContentWidgets();
    const { headingLevel: e21, visibleElements: t26 } = this, s22 = this.get("viewModel.content"), { contentViewModels: i28 } = this.viewModel;
    if (Array.isArray(s22)) s22.forEach((s23, n27) => {
      if ("attachments" === s23.type && (this._contentWidgets[n27] = new h6({ displayType: s23.displayType, headingLevel: t26.title ? e21 + 1 : e21, viewModel: i28[n27] })), "fields" === s23.type && (this._contentWidgets[n27] = new c17({ viewModel: i28[n27] })), "media" === s23.type && (this._contentWidgets[n27] = new R6({ viewModel: i28[n27] })), "text" === s23.type && (this._contentWidgets[n27] = new p9({ viewModel: i28[n27] })), "custom" === s23.type && (this._contentWidgets[n27] = new p9({ viewModel: i28[n27] })), "expression" === s23.type && (this._contentWidgets[n27] = new m11({ viewModel: i28[n27] })), "relationship" === s23.type) {
        const e22 = new F5({ viewModel: i28[n27] });
        this._addFeatureRelationshipHandles(e22), this._contentWidgets[n27] = e22;
      }
    }, this);
    else {
      const e22 = i28[0];
      e22 && !e22.destroyed && (this._contentWidgets[0] = new p9({ viewModel: e22 }));
    }
    this.scheduleRender();
  }
};
e([y()], W4.prototype, "graphic", null), e([y()], W4.prototype, "defaultPopupTemplateEnabled", null), e([y()], W4.prototype, "flowItems", void 0), e([y()], W4.prototype, "headingLevel", void 0), e([y({ readOnly: true })], W4.prototype, "isTable", null), e([y()], W4.prototype, "label", null), e([y(), e4("esri/widgets/Feature/t9n/Feature")], W4.prototype, "messages", void 0), e([y(), e4("esri/t9n/common")], W4.prototype, "messagesCommon", void 0), e([y(), e4("esri/widgets/support/t9n/uriUtils")], W4.prototype, "messagesURIUtils", void 0), e([y()], W4.prototype, "spatialReference", null), e([y({ readOnly: true })], W4.prototype, "title", null), e([y()], W4.prototype, "visibleElements", void 0), e([s4("visibleElements")], W4.prototype, "castVisibleElements", null), e([y()], W4.prototype, "map", null), e([y()], W4.prototype, "view", null), e([y({ type: X2 })], W4.prototype, "viewModel", void 0), W4 = E4 = e([a2("esri.widgets.Feature")], W4);
var C5 = W4;

// node_modules/@arcgis/core/widgets/support/AnchorElementViewModel.js
var l15;
var d8 = Symbol("anchorHandles");
var m13 = class extends n4.EventedAccessor {
  constructor(e21) {
    super(e21), this[l15] = new t4(), this.location = null, this.screenLocationEnabled = false, this.view = null, this[d8].add([f3(() => o(this.screenLocationEnabled ? this.view : null, (e22) => [e22.size, "3d" === e22.type ? e22.camera : e22.viewpoint]), () => this.notifyChange("screenLocation")), l3(() => this.screenLocation, (e22, o27) => {
      r(e22) && r(o27) && this.emit("view-change");
    })]);
  }
  destroy() {
    this.view = null, this[d8] = a(this[d8]);
  }
  get screenLocation() {
    var _a;
    const { location: e21, view: o27, screenLocationEnabled: t26 } = this;
    return t26 && r(e21) && r(o27) && o27.ready ? (_a = o27.toScreen) == null ? void 0 : _a.call(o27, e21) : null;
  }
};
l15 = d8, e([y()], m13.prototype, "location", void 0), e([y()], m13.prototype, "screenLocation", null), e([y()], m13.prototype, "screenLocationEnabled", void 0), e([y()], m13.prototype, "view", void 0), m13 = e([a2("esri.widgets.support.AnchorElementViewModel")], m13);
var h9 = m13;

// node_modules/@arcgis/core/widgets/Spinner/SpinnerViewModel.js
var t14 = "esri.widgets.CompassViewModel";
var p12 = class extends h9 {
  constructor(s22) {
    super(s22), this.visible = false;
  }
};
e([y()], p12.prototype, "visible", void 0), p12 = e([a2(t14)], p12);
var c22 = p12;

// node_modules/@arcgis/core/widgets/Spinner.js
var c23 = "esri-spinner";
var p13 = { base: c23, spinnerStart: `${c23}--start`, spinnerFinish: `${c23}--finish` };
var h10 = class extends W {
  constructor(i28, e21) {
    super(i28, e21), this._animationDelay = 500, this._animationPromise = null, this.viewModel = new c22();
  }
  initialize() {
    this.addHandles(l3(() => this.visible, (i28) => this._visibleChange(i28)));
  }
  destroy() {
    this._animationPromise = null;
  }
  get location() {
    return this.viewModel.location;
  }
  set location(i28) {
    this.viewModel.location = i28;
  }
  get view() {
    return this.viewModel.view;
  }
  set view(i28) {
    this.viewModel.view = i28;
  }
  get visible() {
    return this.viewModel.visible;
  }
  set visible(i28) {
    this.viewModel.visible = i28;
  }
  show(i28) {
    const { location: e21, promise: t26 } = i28 ?? {};
    e21 && (this.viewModel.location = e21), this.visible = true;
    const s22 = () => this.hide();
    t26 && t26.catch(() => {
    }).then(s22);
  }
  hide() {
    this.visible = false;
  }
  render() {
    const { visible: i28 } = this, { screenLocation: e21 } = this.viewModel, t26 = !!e21, s22 = i28 && t26, o27 = !i28 && t26, r20 = { [p13.spinnerStart]: s22, [p13.spinnerFinish]: o27 }, n27 = this._getPositionStyles();
    return n5("div", { class: this.classes(p13.base, r20), styles: n27 });
  }
  _visibleChange(i28) {
    if (i28) return void (this.viewModel.screenLocationEnabled = true);
    const e21 = U(this._animationDelay);
    this._animationPromise = e21, e21.catch(() => {
    }).then(() => {
      this._animationPromise === e21 && (this.viewModel.screenLocationEnabled = false, this._animationPromise = null);
    });
  }
  _getPositionStyles() {
    const { screenLocation: i28, view: t26 } = this.viewModel;
    if (t(t26) || t(i28)) return {};
    const { padding: s22 } = t26;
    return { left: i28.x - s22.left + "px", top: i28.y - s22.top + "px" };
  }
};
e([y()], h10.prototype, "location", null), e([y()], h10.prototype, "view", null), e([y({ type: c22 })], h10.prototype, "viewModel", void 0), e([y()], h10.prototype, "visible", null), h10 = e([a2("esri.widgets.Spinner")], h10);
var m14 = h10;

// node_modules/@arcgis/core/widgets/Popup/actions.js
var s12 = { iconZoom: "esri-icon-zoom-in-magnifying-glass", iconTrash: "esri-icon-trash", iconBrowseClusteredFeatures: "esri-icon-table" };
var o13 = new a12({ id: "zoom-to-feature", title: "{messages.zoom}", className: s12.iconZoom });
var t15 = new a12({ id: "remove-selected-feature", title: "{messages.remove}", className: s12.iconTrash });
var r12 = new a12({ id: "zoom-to-clustered-features", title: "{messages.zoom}", className: s12.iconZoom });
var i14 = new a12({ id: "browse-clustered-features", title: "{messages.browseClusteredFeatures}", className: s12.iconBrowseClusteredFeatures });

// node_modules/@arcgis/core/widgets/Popup/actionUtils.js
var u10 = "esri.widgets.Popup.PopupViewModel";
var l16 = s2.getLogger(u10);
var d9 = (t26) => {
  const { event: r20, view: o27 } = t26, { action: u18 } = r20, l25 = o27 && o27.popup;
  if (!u18) return Promise.reject(new s3("trigger-action:missing-arguments", "Event has no action"));
  if (!l25) return Promise.reject(new s3("trigger-action:missing-arguments", "view.popup is missing"));
  const { disabled: d15, id: g16 } = u18;
  if (!g16) return Promise.reject(new s3("trigger-action:invalid-action", "action.id is missing"));
  if (d15) return Promise.reject(new s3("trigger-action:invalid-action", "Action is disabled"));
  if (g16 === o13.id) return p14(l25.viewModel).catch(b);
  if (g16 === r12.id) return m15(l25.viewModel);
  if (g16 === i14.id) return l25.featureMenuOpen = !l25.featureMenuOpen, l25.viewModel.browseClusterEnabled = !l25.viewModel.browseClusterEnabled, Promise.resolve();
  if (l25.viewModel.browseClusterEnabled = false, g16 === t15.id) {
    l25.close();
    const { selectedFeature: t27 } = l25;
    if (!t27) return Promise.reject(new s3(`trigger-action:${t15.id}`, "selectedFeature is required", { selectedFeature: t27 }));
    const { sourceLayer: r21 } = t27;
    return r21 ? r21.remove(t27) : o27.graphics.remove(t27), Promise.resolve();
  }
  return Promise.resolve();
};
function g10(e21) {
  const { selectedFeature: t26, location: r20, view: o27 } = e21;
  if (!o27) return null;
  if ("3d" === o27.type) return t26 ?? r20 ?? null;
  return e21.get("selectedFeature.geometry") || r20;
}
function w14(e21) {
  var _a, _b;
  return !!e21 && e21.isAggregate && "cluster" === ((_b = (_a = e21.sourceLayer) == null ? void 0 : _a.featureReduction) == null ? void 0 : _b.type);
}
async function f12(e21, t26) {
  if ("3d" !== (t26 == null ? void 0 : t26.type) || !e21 || "esri.Graphic" !== e21.declaredClass) return true;
  const r20 = t26.getViewForGraphic(e21);
  if (r20 && "whenGraphicBounds" in r20) {
    let t27;
    try {
      t27 = await r20.whenGraphicBounds(e21, { useViewElevation: true });
    } catch (o27) {
    }
    return !t27 || !t27.boundingBox || t27.boundingBox[0] === t27.boundingBox[3] && t27.boundingBox[1] === t27.boundingBox[4] && t27.boundingBox[2] === t27.boundingBox[5];
  }
  return true;
}
async function p14(t26) {
  var _a;
  const { location: o27, selectedFeature: i28, view: a32, zoomFactor: s22 } = t26, c34 = g10(t26);
  if (!a32 || !c34) {
    const t27 = new s3("zoom-to:invalid-target-or-view", "Cannot zoom to location without a target and view.", { target: c34, view: a32 });
    throw l16.error(t27), t27;
  }
  const u18 = a32.scale / s22, d15 = (_a = t26.selectedFeature) == null ? void 0 : _a.geometry, w18 = d15 ?? o27, p30 = r(w18) && "point" === w18.type && await f12(i28, a32);
  o13.active = true, o13.disabled = true;
  try {
    await t26.zoomTo({ target: { target: c34, scale: p30 ? u18 : void 0 } });
  } catch (m24) {
    const t27 = new s3("zoom-to:invalid-graphic", "Could not zoom to the location of the graphic.", { graphic: i28 });
    l16.error(t27);
  } finally {
    o13.active = false, o13.disabled = false, t26.zoomToLocation = null, p30 && (t26.location = w18);
  }
}
async function m15(t26) {
  const { selectedFeature: r20, view: o27 } = t26;
  if ("2d" !== (o27 == null ? void 0 : o27.type)) {
    const t27 = new s3("zoomToCluster:invalid-view", "View must be 2d MapView.", { view: o27 });
    throw l16.error(t27), t27;
  }
  if (!r20 || !w14(r20)) {
    const t27 = new s3("zoomToCluster:invalid-selectedFeature", "Selected feature must represent an aggregate/cluster graphic.", { selectedFeature: r20 });
    throw l16.error(t27), t27;
  }
  const [i28, n27] = await h11(o27, r20);
  r12.active = true, r12.disabled = true;
  const { extent: s22 } = await i28.queryExtent(n27);
  await t26.zoomTo({ target: s22 }), r12.active = false, r12.disabled = false;
}
async function v12(e21) {
  const { view: t26, selectedFeature: r20 } = e21;
  if (!t26 || !r20) return;
  const [o27, i28] = await h11(t26, r20), { extent: n27 } = await o27.queryExtent(i28);
  e21.selectedClusterBoundaryFeature.geometry = n27, t26.graphics.add(e21.selectedClusterBoundaryFeature);
}
async function y10(e21) {
  var _a;
  const { selectedFeature: t26, view: r20 } = e21;
  if (!r20 || !t26) return;
  const [o27, i28] = await h11(r20, t26);
  i14.active = true, i14.disabled = true;
  const { features: n27 } = await o27.queryFeatures(i28);
  i14.active = false, i14.disabled = false, (_a = r20.popup) == null ? void 0 : _a.open({ features: [t26].concat(n27), featureMenuOpen: true });
}
async function h11(e21, t26) {
  const r20 = await e21.whenLayerView(t26.sourceLayer), o27 = r20.createQuery(), i28 = t26.getObjectId();
  return o27.aggregateIds = null != i28 ? [i28] : [], [r20, o27];
}
function b13(e21) {
  const t26 = e21.features.filter((e22) => w14(e22));
  t26.length && (e21.features = t26);
}
function F6(e21) {
  var _a;
  if (t(e21)) return null;
  switch (e21.type) {
    case "point":
      return e21;
    case "extent":
      return e21.center;
    case "polygon":
      return e21.centroid;
    case "multipoint":
    case "polyline":
      return (_a = e21.extent) == null ? void 0 : _a.center;
    default:
      return null;
  }
}

// node_modules/@arcgis/core/widgets/Popup/css.js
var e14 = "esri-popup";
var t16 = `${e14}__header`;
var o14 = `${e14}--is-docked`;
var n16 = { calciteThemeLight: "calcite-mode-light", calciteThemeDark: "calcite-mode-dark", iconLeftTriangleArrow: "esri-icon-left-triangle-arrow", iconRightTriangleArrow: "esri-icon-right-triangle-arrow", iconDockToTop: "esri-icon-maximize", iconDockToBottom: "esri-icon-dock-bottom", iconDockToLeft: "esri-icon-dock-left", iconDockToRight: "esri-icon-dock-right", iconClose: "esri-icon-close", iconUndock: "esri-icon-minimize", iconCheckMark: "esri-icon-check-mark", iconLoading: "esri-icon-loading-indicator", iconDefaultAction: "esri-icon-default-action", iconActionsMenu: "esri-icon-handle-horizontal", rotating: "esri-rotating", base: e14, widget: "esri-widget", main: `${e14}__main-container`, loadingContainer: `${e14}__loading-container`, isCollapsible: `${e14}--is-collapsible`, isCollapsed: `${e14}--is-collapsed`, shadow: `${e14}--shadow`, isDocked: o14, isDockedTopLeft: `${o14}-top-left`, isDockedTopCenter: `${o14}-top-center`, isDockedTopRight: `${o14}-top-right`, isDockedBottomLeft: `${o14}-bottom-left`, isDockedBottomCenter: `${o14}-bottom-center`, isDockedBottomRight: `${o14}-bottom-right`, alignTopCenter: `${e14}--aligned-top-center`, alignBottomCenter: `${e14}--aligned-bottom-center`, alignTopLeft: `${e14}--aligned-top-left`, alignBottomLeft: `${e14}--aligned-bottom-left`, alignTopRight: `${e14}--aligned-top-right`, alignBottomRight: `${e14}--aligned-bottom-right`, isFeatureMenuOpen: `${e14}--feature-menu-open`, isActionsMenuOpen: `${e14}--actions-menu-open`, hasFeatureUpdated: `${e14}--feature-updated`, header: t16, headerButtons: `${t16}-buttons`, headerContainer: `${t16}-container`, headerContainerButton: `${t16}-container--button`, headerTitle: `${t16}-title`, content: `${e14}__content`, contentHasFlows: "esri-content--has-flows", contentFlowItem: "esri-content__flow-item", footer: `${e14}__footer`, footerHasPagination: `${e14}__footer--has-pagination`, footerHasActions: `${e14}__footer--has-actions`, footerHasActionsMenu: `${e14}__footer--has-actions-menu`, button: `${e14}__button`, buttonDisabled: `${e14}__button--disabled`, buttonDock: `${e14}__button--dock`, icon: `${e14}__icon`, iconDock: `${e14}__icon--dock-icon`, inlineActionsContainer: `${e14}__inline-actions-container`, actionsMenuButton: `${e14}__actions-menu-button`, actions: `${e14}__actions`, action: `${e14}__action`, actionImage: `${e14}__action-image`, actionText: `${e14}__action-text`, actionToggle: `${e14}__action-toggle`, actionToggleOn: `${e14}__action-toggle--on`, actionExit: `${e14}__action--exit`, actionSelectFeature: `${e14}__action--select-feature`, pointer: `${e14}__pointer`, pointerDirection: `${e14}__pointer-direction`, navigation: `${e14}__navigation`, paginationPrevious: `${e14}__pagination-previous`, paginationNext: `${e14}__pagination-next`, paginationPreviousIconLTR: `${e14}__pagination-previous-icon`, paginationPreviousIconRTL: `${e14}__pagination-previous-icon--rtl`, paginationNextIconLTR: `${e14}__pagination-next-icon`, paginationNextIconRTL: `${e14}__pagination-next-icon--rtl`, featureMenu: `${e14}__feature-menu`, featureMenuList: `${e14}__feature-menu-list`, featureMenuItem: `${e14}__feature-menu-item`, featureMenuViewport: `${e14}__feature-menu-viewport`, featureMenuHeader: `${e14}__feature-menu-header`, featureMenuNote: `${e14}__feature-menu-note`, featureMenuSelected: `${e14}__feature-menu-item--selected`, featureMenuButton: `${e14}__feature-menu-button`, featureMenuTitle: `${e14}__feature-menu-title`, featureMenuObserver: `${e14}__feature-menu-observer`, featureMenuLoader: `${e14}__feature-menu-loader`, collapseButton: `${e14}__collapse-button`, collapseIcon: `${e14}__collapse-icon` };

// node_modules/@arcgis/core/layers/LayerConstants.js
var o15 = "OBJECTID";

// node_modules/@arcgis/core/widgets/Popup/PopupViewModel.js
var B3 = j2.ofType({ key: "type", defaultKeyValue: "button", base: c9, typeMap: { button: a12, toggle: a13 } });
var R7 = () => [o13.clone()];
var G3 = () => [r12.clone(), i14.clone()];
var z3 = class extends t12(h9) {
  get isLoadingFeature() {
    return this.featureViewModels.some((e21) => e21.waitingForContent);
  }
  constructor(e21) {
    super(e21), this._handles = new t4(), this._pendingPromises = /* @__PURE__ */ new Set(), this._fetchFeaturesController = null, this._highlightSelectedFeaturePromise = null, this._highlightActiveFeaturePromise = null, this._selectedClusterFeature = null, this.featurePage = null, this.actions = new B3(), this.activeFeature = null, this.defaultPopupTemplateEnabled = false, this.autoCloseEnabled = false, this.autoOpenEnabled = true, this.browseClusterEnabled = false, this.content = null, this.featuresPerPage = 20, this.featureViewModelAbilities = null, this.featureViewModels = [], this.highlightEnabled = true, this.includeDefaultActions = true, this.selectedClusterBoundaryFeature = new g3({ symbol: new S2({ outline: { width: 1.5, color: "cyan" }, style: "none" }) }), this.title = null, this.updateLocationEnabled = false, this.view = null, this.visible = false, this.zoomFactor = 4, this.zoomToLocation = null;
  }
  initialize() {
    this._handles.add([l3(() => [this.autoOpenEnabled, this.view], () => this._autoOpenEnabledChange()), this.on("view-change", () => this._autoClose()), l3(() => [this.highlightEnabled, this.selectedFeature, this.visible, this.view], () => this._highlightSelectedFeature()), l3(() => [this.highlightEnabled, this.activeFeature, this.visible, this.view], () => this._highlightActiveFeature()), l3(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.animation) == null ? void 0 : _b.state;
    }, (e21) => this._animationStateChange(e21)), l3(() => this.location, (e21) => this._locationChange(e21)), l3(() => this.selectedFeature, (e21) => this._selectedFeatureChange(e21)), l3(() => [this.selectedFeatureIndex, this.featureCount, this.featuresPerPage], () => this._selectedFeatureIndexChange()), l3(() => [this.featurePage, this.selectedFeatureIndex, this.featureCount, this.featuresPerPage, this.featureViewModels], () => this._setGraphicOnFeatureViewModels()), l3(() => this.featureViewModels, () => this._featureViewModelsChange()), this.on("trigger-action", (e21) => d9({ event: e21, view: this.view })), f3(() => !this.waitingForResult, () => this._waitingForResultChange(), U2), l3(() => {
      var _a, _b;
      return [this.features, (_a = this.view) == null ? void 0 : _a.map, (_b = this.view) == null ? void 0 : _b.spatialReference];
    }, () => this._updateFeatureVMs()), l3(() => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.scale;
    }, () => this._viewScaleChange()), f3(() => !this.visible, () => this.browseClusterEnabled = false), l3(() => this.browseClusterEnabled, (e21) => e21 ? this.enableClusterBrowsing() : this.disableClusterBrowsing())]);
  }
  destroy() {
    this._cancelFetchingFeatures(), this._handles.destroy(), this._pendingPromises.clear(), this.browseClusterEnabled = false, this.view = null;
  }
  get active() {
    return !(!this.visible || this.waitingForResult);
  }
  get allActions() {
    const e21 = this._get("allActions") || new B3();
    e21.removeAll();
    const { actions: t26, defaultActions: s22, defaultPopupTemplateEnabled: i28, includeDefaultActions: r20, selectedFeature: o27 } = this, a32 = r20 ? s22.concat(t26) : t26, n27 = o27 && ("function" == typeof o27.getEffectivePopupTemplate && o27.getEffectivePopupTemplate(i28) || o27.popupTemplate), l25 = n27 && n27.actions, h18 = n27 && n27.overwriteActions ? l25 : l25 ? l25.concat(a32) : a32;
    return h18 && h18.filter(Boolean).forEach((t27) => e21.add(t27)), e21;
  }
  get defaultActions() {
    const e21 = this._get("defaultActions") || new B3();
    return e21.removeAll(), e21.addMany(w14(this.selectedFeature) ? G3() : R7()), e21;
  }
  get featureCount() {
    return this.features.length;
  }
  get features() {
    return this._get("features") || [];
  }
  set features(e21) {
    const t26 = e21 || [];
    this._set("features", t26);
    const { pendingPromisesCount: s22, promiseCount: i28, selectedFeatureIndex: r20 } = this, o27 = i28 && t26.length;
    o27 && s22 && -1 === r20 ? this.selectedFeatureIndex = 0 : o27 && -1 !== r20 || (this.selectedFeatureIndex = t26.length ? 0 : -1);
  }
  get location() {
    return this._get("location") || null;
  }
  set location(e21) {
    var _a, _b, _c;
    let t26 = e21;
    const s22 = (_b = (_a = this.view) == null ? void 0 : _a.spatialReference) == null ? void 0 : _b.isWebMercator;
    e21 && ((_c = e21 == null ? void 0 : e21.spatialReference) == null ? void 0 : _c.isWGS84) && s22 && (t26 = R(e21)), this._set("location", t26);
  }
  get pendingPromisesCount() {
    return this._pendingPromises.size;
  }
  get waitingForResult() {
    return !(!(!!this._fetchFeaturesController || this.pendingPromisesCount > 0) || 0 !== this.featureCount);
  }
  get promiseCount() {
    return this.promises.length;
  }
  get promises() {
    return this._get("promises") || [];
  }
  set promises(e21) {
    if (this._pendingPromises.clear(), this.features = [], !Array.isArray(e21) || !e21.length) return this._set("promises", []), void this.notifyChange("pendingPromisesCount");
    this._set("promises", e21), (e21 = e21.slice(0)).forEach((e22) => {
      this._pendingPromises.add(e22);
      const t26 = (t27) => {
        this._pendingPromises.has(e22) && this._updateFeatures(t27), this._updatePendingPromises(e22);
      }, s22 = () => this._updatePendingPromises(e22);
      e22.then(t26, s22);
    }), this.notifyChange("pendingPromisesCount");
  }
  get selectedFeature() {
    const { features: e21, selectedFeatureIndex: t26 } = this;
    if (-1 === t26) return null;
    return e21[t26] || null;
  }
  get selectedFeatureIndex() {
    const e21 = this._get("selectedFeatureIndex");
    return "number" == typeof e21 ? e21 : -1;
  }
  set selectedFeatureIndex(e21) {
    const { featureCount: t26 } = this;
    e21 = isNaN(e21) || e21 < -1 || !t26 ? -1 : (e21 + t26) % t26, this.activeFeature = null, this._set("selectedFeatureIndex", e21);
  }
  get selectedFeatureViewModel() {
    return this.featureViewModels[this.selectedFeatureIndex] || null;
  }
  get state() {
    return this.get("view.ready") ? "ready" : "disabled";
  }
  centerAtLocation() {
    const { view: e21 } = this, t26 = g10(this);
    return t26 && e21 ? this.callGoTo({ target: { target: t26, scale: e21.scale } }) : Promise.reject(new s3("center-at-location:invalid-target-or-view", "Cannot center at a location without a target and view.", { target: t26, view: e21 }));
  }
  zoomTo(e21) {
    return this.callGoTo(e21);
  }
  clear() {
    this.set({ promises: [], features: [], content: null, title: null, location: null, activeFeature: null });
  }
  fetchFeatures(e21, t26) {
    const { view: s22 } = this;
    if (!s22 || !e21) throw new s3("fetch-features:invalid-screenpoint-or-view", "Cannot fetch features without a screenPoint and view.", { screenPoint: e21, view: s22 });
    return s22.fetchPopupFeatures(e21, { event: t26 && t26.event, defaultPopupTemplateEnabled: this.defaultPopupTemplateEnabled, signal: t26 && t26.signal });
  }
  open(e21) {
    const t26 = { updateLocationEnabled: false, promises: [], fetchFeatures: false, ...e21, visible: true }, { fetchFeatures: s22 } = t26;
    delete t26.fetchFeatures, s22 && this._setFetchFeaturesPromises(t26.location);
    const i28 = ["actionsMenuOpen", "collapsed", "featureMenuOpen"];
    for (const r20 of i28) delete t26[r20];
    this.set(t26);
  }
  triggerAction(e21) {
    const t26 = this.allActions.getItemAt(e21);
    t26 && !t26.disabled && this.emit("trigger-action", { action: t26 });
  }
  next() {
    return this.selectedFeatureIndex = this.selectedFeatureIndex + 1, this;
  }
  previous() {
    return this.selectedFeatureIndex = this.selectedFeatureIndex - 1, this;
  }
  disableClusterBrowsing() {
    b13(this), this._clearBrowsedClusterGraphics();
  }
  async enableClusterBrowsing() {
    const { view: e21, selectedFeature: t26 } = this;
    "2d" === (e21 == null ? void 0 : e21.type) ? w14(t26) ? (await v12(this), await y10(this)) : s2.getLogger(this.declaredClass).warn("enableClusterBrowsing:invalid-selectedFeature: Selected feature must represent an aggregate/cluster graphic.", t26) : s2.getLogger(this.declaredClass).warn("enableClusterBrowsing:invalid-view: View must be 2d MapView.", t26);
  }
  _animationStateChange(e21) {
    this.zoomToLocation || (o13.disabled = "waiting-for-target" === e21);
  }
  _clearBrowsedClusterGraphics() {
    var _a;
    const e21 = (_a = this.view) == null ? void 0 : _a.graphics;
    e21 && (e21.remove(this.selectedClusterBoundaryFeature), this._selectedClusterFeature && e21.remove(this._selectedClusterFeature)), this._selectedClusterFeature = null, this.selectedClusterBoundaryFeature.geometry = null;
  }
  _viewScaleChange() {
    if (w14(this.selectedFeature)) return this.browseClusterEnabled = false, this.visible = false, void this.clear();
    this.browseClusterEnabled && (this.features = this.selectedFeature ? [this.selectedFeature] : []);
  }
  _locationChange(e21) {
    const { selectedFeature: t26, updateLocationEnabled: s22 } = this;
    s22 && e21 && (!t26 || t26.geometry) && this.centerAtLocation();
  }
  _selectedFeatureIndexChange() {
    this.featurePage = this.featureCount > 1 ? Math.floor(this.selectedFeatureIndex / this.featuresPerPage) + 1 : null;
  }
  _featureViewModelsChange() {
    this.featurePage = this.featureCount > 1 ? 1 : null;
  }
  _setGraphicOnFeatureViewModels() {
    const { features: e21, featureCount: t26, featurePage: s22, featuresPerPage: i28, featureViewModels: r20 } = this;
    if (null === s22) return;
    const o27 = ((s22 - 1) * i28 + t26) % t26, a32 = o27 + i28;
    r20.slice(o27, a32).forEach((t27, s23) => {
      t27 && !t27.graphic && (t27.graphic = e21[o27 + s23]);
    });
  }
  async _selectedFeatureChange(e21) {
    const { location: t26, updateLocationEnabled: s22, view: i28 } = this;
    if (e21 && i28) {
      if (this.browseClusterEnabled) {
        if (this._selectedClusterFeature && (i28.graphics.remove(this._selectedClusterFeature), this._selectedClusterFeature = null), w14(e21)) return;
        return e21.symbol = await R3(e21), this._selectedClusterFeature = e21, void i28.graphics.add(this._selectedClusterFeature);
      }
      !s22 && t26 || !e21.geometry ? s22 && !e21.geometry && this.centerAtLocation().then(() => {
        var _a;
        const e22 = (_a = i28.center) == null ? void 0 : _a.clone();
        e22 && (this.location = e22);
      }) : this.location = e2(F6(e21.geometry));
    }
  }
  _waitingForResultChange() {
    !this.featureCount && this.promises && (this.visible = false);
  }
  _setFetchFeaturesPromises(e21) {
    return this._fetchFeaturesWithController(this._getScreenPoint(e21 || this.location)).then((e22) => {
      const { clientOnlyGraphics: t26, promisesPerLayerView: s22 } = e22, i28 = Promise.resolve(t26), r20 = s22.map((e23) => e23.promise);
      this.promises = [i28, ...r20];
    });
  }
  _destroyFeatureVMs() {
    this.featureViewModels.forEach((e21) => e21 && !e21.destroyed && e21.destroy()), this._set("featureViewModels", []);
  }
  _updateFeatureVMs() {
    const { selectedFeature: e21, features: t26, featureViewModels: s22 } = this;
    if (w14(e21) || (this.browseClusterEnabled = false), this._destroyFeatureVMs(), !t26 || !t26.length) return;
    const i28 = s22.slice(0), r20 = [];
    t26.forEach((t27, s23) => {
      var _a, _b;
      if (!t27) return;
      let o27 = null;
      if (i28.some((e22, s24) => (e22 && e22.graphic === t27 && (o27 = e22, i28.splice(s24, 1)), !!o27)), o27) r20[s23] = o27;
      else {
        const i29 = new X2({ abilities: this.featureViewModelAbilities, defaultPopupTemplateEnabled: this.defaultPopupTemplateEnabled, spatialReference: (_a = this.view) == null ? void 0 : _a.spatialReference, graphic: t27 === e21 ? t27 : null, map: (_b = this.view) == null ? void 0 : _b.map, view: this.view });
        r20[s23] = i29;
      }
    }), i28.forEach((e22) => e22 && !e22.destroyed && e22.destroy()), this._set("featureViewModels", r20);
  }
  _getScreenPoint(e21) {
    const { view: t26 } = this;
    return t26 && e21 && "function" == typeof t26.toScreen ? t26.toScreen(e21) : null;
  }
  _autoOpenEnabledChange() {
    const e21 = "auto-fetch-features", { _handles: t26, autoOpenEnabled: s22 } = this;
    if (t26.remove(e21), s22 && this.view) {
      const s23 = this.view.on("click", (e22) => {
        "mouse" === e22.pointerType && 0 !== e22.button || this._fetchFeaturesAndOpen(e22);
      }, P.WIDGET);
      t26.add(s23, e21);
    }
  }
  _cancelFetchingFeatures() {
    const e21 = this._fetchFeaturesController;
    e21 && e21.abort(), this._fetchFeaturesController = null, this.notifyChange("waitingForResult");
  }
  _fetchFeaturesWithController(e21, t26) {
    this._cancelFetchingFeatures();
    const s22 = new AbortController(), { signal: i28 } = s22;
    this._fetchFeaturesController = s22, this.notifyChange("waitingForResult");
    const r20 = this.fetchFeatures(e21, { signal: i28, event: t26 });
    return r20.catch(() => {
    }).then(() => {
      this._fetchFeaturesController = null, this.notifyChange("waitingForResult");
    }), r20;
  }
  _fetchFeaturesAndOpen(e21) {
    const { screenPoint: t26, mapPoint: s22 } = e21, { view: i28 } = this;
    this._fetchFeaturesWithController(t26, e21).then((e22) => {
      var _a;
      const { clientOnlyGraphics: t27, promisesPerLayerView: r20, location: o27 } = e22, a32 = [Promise.resolve(t27), ...r20.map((e23) => e23.promise)];
      return (_a = i28 == null ? void 0 : i28.popup) == null ? void 0 : _a.open({ location: o27 || s22, promises: a32 }), e22;
    });
  }
  _updatePendingPromises(e21) {
    e21 && this._pendingPromises.has(e21) && (this._pendingPromises.delete(e21), this.notifyChange("pendingPromisesCount"));
  }
  _autoClose() {
    this.autoCloseEnabled && (this.visible = false);
  }
  async _getLayerView(e21, t26) {
    return await e21.when(), e21.whenLayerView(t26);
  }
  _getHighlightLayer(e21) {
    const { layer: t26, sourceLayer: s22 } = e21;
    return s22 && "layer" in s22 && s22.layer ? s22.layer : "map-notes" === (s22 == null ? void 0 : s22.type) || "subtype-group" === (s22 == null ? void 0 : s22.type) ? s22 : t26;
  }
  _getHighlightTarget(e21, t26) {
    const s22 = "imagery" === t26.type ? void 0 : "objectIdField" in t26 ? t26.objectIdField || o15 : null, i28 = e21.attributes;
    return i28 && s22 && i28[s22] || e21;
  }
  async _highlightActiveFeature() {
    const e21 = "highlight-active-feature";
    this._handles.remove(e21);
    const { highlightEnabled: t26, view: s22, activeFeature: i28, visible: r20 } = this;
    if (!(i28 && s22 && t26 && r20)) return;
    const o27 = this._getHighlightLayer(i28);
    if (!(o27 && o27 instanceof b4)) return;
    const a32 = this._getLayerView(s22, o27);
    this._highlightActiveFeaturePromise = a32;
    const n27 = await a32;
    if (!(n27 && t11(n27) && this._highlightActiveFeaturePromise === a32 && this.activeFeature && this.highlightEnabled)) return;
    const l25 = n27.highlight(this._getHighlightTarget(i28, o27));
    this._handles.add(l25, e21);
  }
  async _highlightSelectedFeature() {
    const e21 = "highlight-selected-feature";
    this._handles.remove(e21);
    const { selectedFeature: t26, highlightEnabled: s22, view: i28, visible: r20 } = this;
    if (!(t26 && i28 && s22 && r20)) return;
    const o27 = this._getHighlightLayer(t26);
    if (!(o27 && o27 instanceof b4)) return;
    const a32 = this._getLayerView(i28, o27);
    this._highlightSelectedFeaturePromise = a32;
    const n27 = await a32;
    if (!(n27 && t11(n27) && this._highlightSelectedFeaturePromise === a32 && this.selectedFeature && this.highlightEnabled && this.visible)) return;
    const l25 = n27.highlight(this._getHighlightTarget(t26, o27));
    this._handles.add(l25, e21);
  }
  _updateFeatures(e21) {
    const { features: t26 } = this;
    if (!e21 || !e21.length) return;
    if (!t26.length) return void (this.features = e21);
    const s22 = e21.filter((e22) => !t26.includes(e22));
    this.features = t26.concat(s22);
  }
};
e([y()], z3.prototype, "featurePage", void 0), e([y()], z3.prototype, "isLoadingFeature", null), e([y({ type: B3 })], z3.prototype, "actions", void 0), e([y({ readOnly: true })], z3.prototype, "active", null), e([y()], z3.prototype, "activeFeature", void 0), e([y({ readOnly: true })], z3.prototype, "allActions", null), e([y({ type: Boolean })], z3.prototype, "defaultPopupTemplateEnabled", void 0), e([y()], z3.prototype, "autoCloseEnabled", void 0), e([y()], z3.prototype, "autoOpenEnabled", void 0), e([y()], z3.prototype, "browseClusterEnabled", void 0), e([y()], z3.prototype, "content", void 0), e([y({ type: B3, readOnly: true })], z3.prototype, "defaultActions", null), e([y({ readOnly: true })], z3.prototype, "featureCount", null), e([y()], z3.prototype, "features", null), e([y()], z3.prototype, "featuresPerPage", void 0), e([y()], z3.prototype, "featureViewModelAbilities", void 0), e([y({ readOnly: true })], z3.prototype, "featureViewModels", void 0), e([y()], z3.prototype, "highlightEnabled", void 0), e([y()], z3.prototype, "includeDefaultActions", void 0), e([y({ type: w2 })], z3.prototype, "location", null), e([y({ readOnly: true })], z3.prototype, "pendingPromisesCount", null), e([y({ readOnly: true })], z3.prototype, "selectedClusterBoundaryFeature", void 0), e([y({ readOnly: true })], z3.prototype, "waitingForResult", null), e([y({ readOnly: true })], z3.prototype, "promiseCount", null), e([y()], z3.prototype, "promises", null), e([y({ value: null, readOnly: true })], z3.prototype, "selectedFeature", null), e([y({ value: -1 })], z3.prototype, "selectedFeatureIndex", null), e([y({ readOnly: true })], z3.prototype, "selectedFeatureViewModel", null), e([y({ readOnly: true })], z3.prototype, "state", null), e([y()], z3.prototype, "title", void 0), e([y()], z3.prototype, "updateLocationEnabled", void 0), e([y()], z3.prototype, "view", void 0), e([y()], z3.prototype, "visible", void 0), e([y()], z3.prototype, "zoomFactor", void 0), e([y()], z3.prototype, "zoomToLocation", void 0), e([y()], z3.prototype, "centerAtLocation", null), z3 = e([a2("esri.widgets.Popup.PopupViewModel")], z3);
var H4 = z3;

// node_modules/@arcgis/core/widgets/Popup.js
var B4 = "selected-index";
var D3 = 0;
var L5 = "popup-spinner";
var S5 = { buttonEnabled: true, position: "auto", breakpoint: { width: 544 } };
var R8 = "esri-popup";
function V2(e21, t26) {
  return void 0 === t26 ? `${R8}__${e21}` : `${R8}__${e21}-${t26}`;
}
var U7 = { closeButton: true, featureNavigation: true };
var W5 = class extends i13(W) {
  constructor(e21, i28) {
    super(e21, i28), this._blurClose = false, this._blurContainer = false, this._containerNode = null, this._mainContainerNode = null, this._featureMenuNode = null, this._actionsMenuNode = null, this._focusClose = false, this._focusContainer = false, this._focusDockButton = false, this._focusFeatureMenuButton = false, this._focusActionsMenuButton = false, this._focusFirstFeature = false, this._focusFirstAction = false, this._handles = new t4(), this._pointerOffsetInPx = 16, this._spinner = null, this._feature = null, this._featureMenuIntersectionObserverNode = null, this._featureMenuViewportNode = null, this._rootFlowItemNode = null, this._featureMenuIntersectionObserverCallback = ([e22]) => {
      (e22 == null ? void 0 : e22.isIntersecting) && null != this.viewModel.featurePage && this.viewModel.featurePage++;
    }, this._featureMenuIntersectionObserver = new IntersectionObserver(this._featureMenuIntersectionObserverCallback, { root: window.document }), this._displaySpinnerThrottled = e7(() => this._displaySpinner(), D3), this._exitRelatedRecordsActions = /* @__PURE__ */ new WeakMap(), this._featureSelectionActions = /* @__PURE__ */ new WeakMap(), this._flowItems = new j2(), this.alignment = "auto", this.collapsed = false, this.collapseEnabled = true, this.dockEnabled = false, this.featureMenuOpen = false, this.headingLevel = 2, this.maxInlineActions = 3, this.messages = null, this.messagesCommon = null, this.spinnerEnabled = true, this.viewModel = new H4(), this.visibleElements = { ...U7 }, this._handleOpenRelatedFeature = (e22) => {
      this._openRelatedFeature(e22);
    }, this._addSelectedFeatureIndexHandle(), this.addHandles([l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.screenLocation;
    }, () => this._positionContainer()), l3(() => {
      var _a;
      return [(_a = this.viewModel) == null ? void 0 : _a.active, this.dockEnabled];
    }, () => this._toggleScreenLocationEnabled()), l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.screenLocation;
    }, (e22, t26) => {
      !!e22 != !!t26 && this.reposition();
    }), l3(() => {
      var _a, _b, _c, _d, _e2, _f;
      return [(_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.padding, (_d = (_c = this.viewModel) == null ? void 0 : _c.view) == null ? void 0 : _d.size, (_e2 = this.viewModel) == null ? void 0 : _e2.active, (_f = this.viewModel) == null ? void 0 : _f.location, this.alignment];
    }, () => this.reposition()), l3(() => this.spinnerEnabled, (e22) => this._spinnerEnabledChange(e22)), l3(() => {
      var _a, _b;
      return (_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.size;
    }, (e22, t26) => this._updateDockEnabledForViewSize(e22, t26)), l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.view;
    }, (e22, t26) => this._viewChange(e22, t26)), l3(() => {
      var _a, _b;
      return (_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.ready;
    }, (e22, t26) => this._viewReadyChange(e22 ?? false, t26 ?? false)), l3(() => {
      var _a, _b;
      return [(_a = this.viewModel) == null ? void 0 : _a.waitingForResult, (_b = this.viewModel) == null ? void 0 : _b.location];
    }, () => {
      this._hideSpinner(), this._displaySpinnerThrottled();
    }), l3(() => this.selectedFeatureWidget, () => this._destroyFlowItemWidgets()), l3(() => {
      var _a, _b, _c, _d;
      return [(_b = (_a = this.selectedFeatureWidget) == null ? void 0 : _a.viewModel) == null ? void 0 : _b.title, (_d = (_c = this.selectedFeatureWidget) == null ? void 0 : _c.viewModel) == null ? void 0 : _d.state];
    }, () => this._setTitleFromFeatureWidget()), l3(() => {
      var _a, _b, _c, _d;
      return [(_b = (_a = this.selectedFeatureWidget) == null ? void 0 : _a.viewModel) == null ? void 0 : _b.content, (_d = (_c = this.selectedFeatureWidget) == null ? void 0 : _c.viewModel) == null ? void 0 : _d.state];
    }, () => this._setContentFromFeatureWidget()), f3(() => !this.collapsed, () => {
      var _a, _b;
      "xsmall" === ((_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.widthBreakpoint) && this.viewModel.active && this.collapseEnabled && this.viewModel.centerAtLocation();
    }), a6(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.allActions;
    }, "change", () => this._watchActions()), l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.allActions;
    }, () => this._watchActions(), h), l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.featureViewModels;
    }, () => this._featureMenuViewportScrollTop()), a6(() => this._flowItems, "change", () => {
      this.notifyChange("_activeFlowItemWidget"), this.scheduleRender();
    }), l3(() => {
      var _a, _b, _c, _d;
      return [(_b = (_a = this._activeFlowItemWidget) == null ? void 0 : _a.viewModel) == null ? void 0 : _b.state, (_d = (_c = this._activeFlowItemWidget) == null ? void 0 : _c.viewModel) == null ? void 0 : _d.title];
    }, () => this.scheduleRender())]);
  }
  loadDependencies() {
    return Promise.all([import("./calcite-flow-INQK6224.js"), import("./calcite-flow-item-MFEEWLSL.js"), import("./calcite-action-MOYDKC5B.js"), import("./calcite-tooltip-DEJ67I3M.js"), import("./calcite-icon-AXY32QDW.js")]);
  }
  destroy() {
    var _a, _b;
    this._destroyFlowItemWidgets(), this._destroySelectedFeatureWidget(), this._destroySpinner(), (_a = this._handles) == null ? void 0 : _a.destroy(), this._unobserveFeatureMenuObserver(), (_b = this._featureMenuIntersectionObserver) == null ? void 0 : _b.disconnect();
  }
  get actionsMenuId() {
    return `${this.id}-actions-menu`;
  }
  get actionsMenuButtonId() {
    return `${this.id}-actions-menu-button`;
  }
  get featureMenuId() {
    return `${this.id}-feature-menu`;
  }
  get titleId() {
    return `${this.id}-popup-title`;
  }
  get contentId() {
    return `${this.id}-popup-content`;
  }
  get hasContent() {
    const { selectedFeatureWidget: e21, viewModel: t26 } = this;
    if (!e21) return !!(t26 == null ? void 0 : t26.content);
    const i28 = e21.viewModel;
    if ((i28 == null ? void 0 : i28.waitingForContent) || "error" === (i28 == null ? void 0 : i28.state)) return true;
    const o27 = i28 == null ? void 0 : i28.content;
    return Array.isArray(o27) ? !!o27.length : !!o27;
  }
  get featureNavigationVisible() {
    return this.viewModel.active && this.viewModel.featureCount > 1 && null != this.visibleElements.featureNavigation;
  }
  get collapsible() {
    return !!(this.collapseEnabled && this.viewModel.title && this.hasContent);
  }
  get featureMenuVisible() {
    return this.featureNavigationVisible && this.featureMenuOpen;
  }
  get contentCollapsed() {
    return this.collapsible && !this.featureMenuVisible && this.collapsed;
  }
  get dividedActions() {
    return this._divideActions();
  }
  get _activeFlowItemWidget() {
    const { _flowItems: e21 } = this;
    return e21.getItemAt(e21.length - 1) || null;
  }
  get actions() {
    return this.viewModel.actions;
  }
  set actions(e21) {
    this.viewModel.actions = e21;
  }
  set actionsMenuOpen(e21) {
    this._set("actionsMenuOpen", !!e21);
  }
  get actionsMenuOpen() {
    return !!this.viewModel.active && this._get("actionsMenuOpen");
  }
  get autoCloseEnabled() {
    return this.viewModel.autoCloseEnabled;
  }
  set autoCloseEnabled(e21) {
    this.viewModel.autoCloseEnabled = e21;
  }
  get autoOpenEnabled() {
    return this.viewModel.autoOpenEnabled;
  }
  set autoOpenEnabled(e21) {
    this.viewModel.autoOpenEnabled = e21;
  }
  get defaultPopupTemplateEnabled() {
    return this.viewModel.defaultPopupTemplateEnabled;
  }
  set defaultPopupTemplateEnabled(e21) {
    this.viewModel.defaultPopupTemplateEnabled = e21;
  }
  get content() {
    return this.viewModel.content;
  }
  set content(e21) {
    this.viewModel.content = e21;
  }
  get currentAlignment() {
    return this._getCurrentAlignment();
  }
  get currentDockPosition() {
    return this._getCurrentDockPosition();
  }
  get dockOptions() {
    return this._get("dockOptions") || S5;
  }
  set dockOptions(e21) {
    var _a, _b;
    const t26 = { ...S5 }, i28 = (_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.breakpoints, o27 = {};
    i28 && (o27.width = i28.xsmall, o27.height = i28.xsmall);
    const n27 = { ...t26, ...e21 }, s22 = { ...t26.breakpoint, ...o27 }, { breakpoint: r20 } = n27;
    "object" == typeof r20 ? n27.breakpoint = { ...s22, ...r20 } : r20 && (n27.breakpoint = s22), this._set("dockOptions", n27), this._setCurrentDockPosition(), this.reposition();
  }
  get featureCount() {
    return this.viewModel.featureCount;
  }
  get features() {
    return this.viewModel.features;
  }
  set features(e21) {
    this.viewModel.features = e21;
  }
  get goToOverride() {
    return this.viewModel.goToOverride;
  }
  set goToOverride(e21) {
    this.viewModel.goToOverride = e21;
  }
  get highlightEnabled() {
    return this.viewModel.highlightEnabled;
  }
  set highlightEnabled(e21) {
    this.viewModel.highlightEnabled = e21;
  }
  get location() {
    return this.viewModel.location;
  }
  set location(e21) {
    this.viewModel.location = e21;
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(e21) {
    this._overrideIfSome("label", e21);
  }
  get promises() {
    return this.viewModel.promises;
  }
  set promises(e21) {
    this.viewModel.promises = e21;
  }
  get selectedFeature() {
    return this.viewModel.selectedFeature;
  }
  get selectedFeatureIndex() {
    return this.viewModel.selectedFeatureIndex;
  }
  set selectedFeatureIndex(e21) {
    this.viewModel.selectedFeatureIndex = e21;
  }
  get selectedFeatureWidget() {
    const { _feature: e21, visibleElements: t26, headingLevel: i28, _flowItems: o27 } = this, { selectedFeatureViewModel: n27 } = this.viewModel, s22 = { ...t26, title: false };
    return n27 ? (e21 ? (e21.viewModel = n27, e21.visibleElements = s22) : this._feature = new C5({ flowItems: o27, headingLevel: i28 + 1, viewModel: n27, visibleElements: s22 }), this._feature) : null;
  }
  get title() {
    return this.viewModel.title;
  }
  set title(e21) {
    this.viewModel.title = e21;
  }
  get updateLocationEnabled() {
    return this.viewModel.updateLocationEnabled;
  }
  set updateLocationEnabled(e21) {
    this.viewModel.updateLocationEnabled = e21;
  }
  get view() {
    return this.viewModel.view;
  }
  set view(e21) {
    this.viewModel.view = e21;
  }
  get visible() {
    return this.viewModel.visible;
  }
  set visible(e21) {
    this.viewModel.visible = e21;
  }
  castVisibleElements(e21) {
    return { ...U7, ...e21 };
  }
  blur() {
    const { active: e21 } = this.viewModel;
    e21 || s2.getLogger(this.declaredClass).warn("Popup can only be blurred when currently active."), this.visibleElements.closeButton ? this._blurClose = true : this._blurContainer = true, this.scheduleRender();
  }
  clear() {
    return this.viewModel.clear();
  }
  close() {
    this.visible = false;
  }
  fetchFeatures(e21, t26) {
    return this.viewModel.fetchFeatures(e21, t26);
  }
  focus() {
    const { active: e21 } = this.viewModel;
    e21 || s2.getLogger(this.declaredClass).warn("Popup can only be focused when currently active."), this.visibleElements.closeButton ? this._focusClose = true : this._focusContainer = true, this.scheduleRender();
  }
  next() {
    return this.viewModel.next();
  }
  open(e21) {
    var _a, _b;
    this._handles.remove(B4);
    const t26 = !!e21 && !!e21.featureMenuOpen, i28 = !!e21 && !!e21.actionsMenuOpen, o27 = { collapsed: !!e21 && !!e21.collapsed, actionsMenuOpen: i28, featureMenuOpen: t26 };
    "xsmall" === ((_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.widthBreakpoint) && (o27.collapsed = true), this.set(o27), this.viewModel.open(e21), this._shouldFocus(e21), this._addSelectedFeatureIndexHandle();
  }
  previous() {
    return this.viewModel.previous();
  }
  reposition() {
    this.renderNow(), this._positionContainer(), this._setCurrentAlignment();
  }
  triggerAction(e21) {
    return this.viewModel.triggerAction(e21);
  }
  render() {
    var _a, _b, _c, _d;
    const { actionsMenuOpen: e21, dockEnabled: t26, featureMenuVisible: i28, dividedActions: o27, currentAlignment: n27, currentDockPosition: s22 } = this, { active: r20 } = this.viewModel, { menuActions: l25 } = o27, a32 = r20 && l25.length > 1 && e21, c34 = r20 && t26, d15 = r20 && !t26, u18 = (_b = (_a = this.selectedFeature) == null ? void 0 : _a.layer) == null ? void 0 : _b.title, h18 = (_d = (_c = this.selectedFeature) == null ? void 0 : _c.layer) == null ? void 0 : _d.id, p30 = { [n16.alignTopCenter]: "top-center" === n27, [n16.alignBottomCenter]: "bottom-center" === n27, [n16.alignTopLeft]: "top-left" === n27, [n16.alignBottomLeft]: "bottom-left" === n27, [n16.alignTopRight]: "top-right" === n27, [n16.alignBottomRight]: "bottom-right" === n27, [n16.isDocked]: c34, [n16.shadow]: d15, [n16.isDockedTopLeft]: "top-left" === s22, [n16.isDockedTopCenter]: "top-center" === s22, [n16.isDockedTopRight]: "top-right" === s22, [n16.isDockedBottomLeft]: "bottom-left" === s22, [n16.isDockedBottomCenter]: "bottom-center" === s22, [n16.isDockedBottomRight]: "bottom-right" === s22, [n16.isFeatureMenuOpen]: i28, [n16.isActionsMenuOpen]: a32 };
    return n5("div", { class: this.classes(n16.base, p30), role: "presentation", "data-layer-title": u18, "data-layer-id": h18, bind: this, afterCreate: this._positionContainer, afterUpdate: this._positionContainer }, r20 ? [this.renderMainContainer(), this.renderPointer()] : null);
  }
  renderLoadingIcon() {
    return n5("span", { "aria-hidden": "true", class: this.classes(n16.icon, n16.iconLoading, n16.rotating) });
  }
  renderNavigationLoading() {
    const { messagesCommon: e21 } = this;
    return this.viewModel.pendingPromisesCount ? n5("div", { key: V2("loading-container"), role: "presentation", class: n16.loadingContainer, "aria-label": e21.loading, title: e21.loading }, this.renderLoadingIcon()) : null;
  }
  renderPreviousIcon() {
    const e21 = f4(this.container), t26 = { [n16.iconRightTriangleArrow]: e21, [n16.paginationPreviousIconRTL]: e21, [n16.iconLeftTriangleArrow]: !e21, [n16.paginationPreviousIconLTR]: !e21 };
    return n5("span", { "aria-hidden": "true", class: this.classes(n16.icon, t26) });
  }
  renderPreviousButton() {
    const { messages: e21 } = this;
    return n5("div", { role: "button", tabIndex: 0, bind: this, onclick: this._previous, onkeydown: this._previous, class: this.classes(n16.button, n16.paginationPrevious), "aria-label": e21.previous, title: e21.previous }, this.renderPreviousIcon());
  }
  renderNextIcon() {
    const e21 = f4(this.container), t26 = { [n16.iconLeftTriangleArrow]: e21, [n16.paginationNextIconRTL]: e21, [n16.iconRightTriangleArrow]: !e21, [n16.paginationNextIconLTR]: !e21 };
    return n5("span", { "aria-hidden": "true", class: this.classes(n16.icon, t26) });
  }
  renderNextButton() {
    const { messages: e21 } = this;
    return n5("div", { role: "button", tabIndex: 0, bind: this, onclick: this._next, onkeydown: this._next, class: this.classes(n16.button, n16.paginationNext), "aria-label": e21.next, title: e21.next }, this.renderNextIcon());
  }
  renderFeatureMenuButton() {
    const { featureMenuOpen: e21, featureMenuId: t26, messagesCommon: i28 } = this, { featureCount: o27, selectedFeatureIndex: n27 } = this.viewModel;
    return n5("div", { role: "button", tabIndex: 0, bind: this, onclick: this._toggleFeatureMenu, onkeydown: this._toggleFeatureMenu, afterCreate: this._focusFeatureMenuButtonNode, afterUpdate: this._focusFeatureMenuButtonNode, class: this.classes(n16.button, n16.featureMenuButton), "aria-haspopup": "true", "aria-controls": t26, "aria-expanded": e21.toString(), "aria-label": i28.menu, title: i28.menu }, this._getPageText(o27, n27));
  }
  renderNavigationButtons() {
    return this.featureNavigationVisible ? [this.renderPreviousButton(), this.renderNavigationLoading() || this.renderFeatureMenuButton(), this.renderNextButton()] : [];
  }
  renderDockIcon() {
    const { dockEnabled: e21 } = this, t26 = this._wouldDockTo(), i28 = { [n16.iconUndock]: e21, [n16.iconDock]: !e21, [n16.iconDockToRight]: !e21 && ("top-right" === t26 || "bottom-right" === t26), [n16.iconDockToLeft]: !e21 && ("top-left" === t26 || "bottom-left" === t26), [n16.iconDockToTop]: !e21 && "top-center" === t26, [n16.iconDockToBottom]: !e21 && "bottom-center" === t26 };
    return n5("span", { "aria-hidden": "true", class: this.classes(i28, n16.icon) });
  }
  renderDockButton() {
    var _a, _b, _c;
    const { dockEnabled: e21, messages: t26 } = this, i28 = (_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.widthBreakpoint, o27 = e21 ? t26.undock : t26.dock;
    return "xsmall" !== i28 && ((_c = this.dockOptions) == null ? void 0 : _c.buttonEnabled) ? n5("div", { role: "button", "aria-label": o27, title: o27, tabIndex: 0, bind: this, onclick: this._toggleDockEnabled, onkeydown: this._toggleDockEnabled, afterCreate: this._focusDockButtonNode, afterUpdate: this._focusDockButtonNode, class: this.classes(n16.button, n16.buttonDock) }, this.renderDockIcon()) : null;
  }
  renderTitle() {
    const { title: e21 } = this.viewModel, { titleId: t26, collapsible: i28, contentCollapsed: o27, messagesCommon: n27 } = this, s22 = { [n16.headerContainerButton]: i28 }, r20 = n5(n8, { level: this.headingLevel, class: n16.headerTitle, innerHTML: e21 ?? "" }), l25 = i28 ? n5("button", { key: `${e21}--collapsible`, id: t26, title: o27 ? n27.expand : n27.collapse, bind: this, enterAnimation: this._createFeatureUpdatedAnimation(), class: this.classes(n16.headerContainer, s22), "aria-expanded": o27 ? "false" : "true", onclick: this._toggleCollapsed, type: "button" }, r20, n5("calcite-icon", { class: n16.collapseIcon, key: "collapse-icon", icon: o27 ? "chevron-down" : "chevron-up", scale: "m" })) : n5("div", { key: e21 ?? "title", id: t26, bind: this, enterAnimation: this._createFeatureUpdatedAnimation(), class: this.classes(n16.headerContainer, s22) }, r20);
    return e21 ? l25 : null;
  }
  renderCloseIcon() {
    return n5("span", { "aria-hidden": "true", class: this.classes(n16.icon, n16.iconClose) });
  }
  renderCloseButton() {
    const { visibleElements: e21, messagesCommon: t26 } = this;
    return e21.closeButton ? n5("div", { role: "button", tabIndex: 0, bind: this, onclick: this._close, onkeydown: this._close, class: n16.button, "aria-label": t26.close, title: t26.close, afterCreate: this._closeButtonNodeUpdated, afterUpdate: this._closeButtonNodeUpdated }, this.renderCloseIcon()) : null;
  }
  renderHeader() {
    return n5("header", { class: n16.header }, this.renderTitle(), n5("div", { class: n16.headerButtons }, this.renderDockButton(), this.renderCloseButton()));
  }
  renderContentContainer() {
    const { contentId: e21, hasContent: t26, contentCollapsed: i28, _flowItems: o27 } = this, { content: n27 } = this.viewModel, s22 = o27.toArray(), r20 = { [n16.contentHasFlows]: !!s22.length };
    return t26 && !i28 ? n5("div", { key: n27 ?? "content", enterAnimation: this._createFeatureUpdatedAnimation(), id: e21, class: this.classes(n16.content, r20) }, n5("calcite-flow", { bind: this }, n5("calcite-flow-item", { bind: this, "data-node-ref": "_rootFlowItemNode", afterCreate: v5, key: "root-flow-item", onCalciteFlowItemBack: this._handleBackClick }, this.renderContent()), s22.map((e22) => this.renderFlowItem(e22))), s22.map((e22) => this.renderFlowItemTooltips(e22))) : null;
  }
  renderFlowItem(e21) {
    const { messages: t26 } = this, i28 = r5(), o27 = "graphic" in e21 && !e21.isTable;
    return n5("calcite-flow-item", { bind: this, class: this.classes({ [n16.calciteThemeDark]: !i28, [n16.calciteThemeLight]: i28 }), heading: e21.title ?? "", description: this._getFlowItemDescription(e21), onCalciteFlowItemBack: this._handleBackClick, key: `flow-item-${e21.viewModel.uid}` }, n5("calcite-action", { class: n16.actionExit, icon: "move-up", label: t26 == null ? void 0 : t26.exitRelatedRecords, text: t26 == null ? void 0 : t26.exitRelatedRecords, slot: "header-actions-start", bind: this, afterCreate: (t27) => this._storeExitRelatedRecordsAction(e21, t27), onclick: this._destroyFlowItemWidgets }), o27 ? n5("calcite-action", { class: n16.actionSelectFeature, icon: "zoom-to-object", label: t26 == null ? void 0 : t26.selectFeature, text: t26 == null ? void 0 : t26.selectFeature, slot: "header-actions-end", bind: this, afterCreate: (t27) => this._storeFeatureSelectionAction(e21, t27), onclick: () => this._handleOpenRelatedFeature(e21) }) : null, n5("div", { class: this.classes(n16.contentFlowItem, { [n16.calciteThemeDark]: i28, [n16.calciteThemeLight]: !i28 }) }, e21.render()));
  }
  renderFlowItemTooltips(e21) {
    const { messages: t26, _exitRelatedRecordsActions: i28, _featureSelectionActions: o27 } = this, n27 = r5(), s22 = o27.get(e21);
    return [n5("calcite-tooltip", { class: this.classes({ [n16.calciteThemeDark]: !n27, [n16.calciteThemeLight]: n27 }), key: `exit-related-records-tooltip-${e21.viewModel.uid}`, label: t26 == null ? void 0 : t26.exitRelatedRecords, overlayPositioning: "fixed", referenceElement: i28.get(e21), placement: "top" }, t26 == null ? void 0 : t26.exitRelatedRecords), s22 ? n5("calcite-tooltip", { class: this.classes({ [n16.calciteThemeDark]: !n27, [n16.calciteThemeLight]: n27 }), key: `select-related-record-tooltip-${e21.viewModel.uid}`, label: t26 == null ? void 0 : t26.selectFeature, overlayPositioning: "fixed", referenceElement: s22, placement: "top" }, t26 == null ? void 0 : t26.selectFeature) : null];
  }
  renderActionsMenuButton() {
    const { actionsMenuId: e21, actionsMenuButtonId: t26, actionsMenuOpen: i28, dividedActions: o27, messagesCommon: n27 } = this, s22 = i28 ? n27.close : n27.open, { menuActions: r20 } = o27;
    return r20.length ? n5("div", { key: V2("actions-menu-button"), class: this.classes(n16.button, n16.actionsMenuButton), role: "button", id: t26, "aria-haspopup": "true", "aria-controls": i28 ? e21 : null, tabIndex: 0, bind: this, onclick: this._toggleActionsMenu, onkeydown: this._toggleActionsMenu, afterCreate: this._focusActionsMenuButtonNode, afterUpdate: this._focusActionsMenuButtonNode, "aria-label": s22, title: s22 }, n5("span", { "aria-hidden": "true", class: n16.iconActionsMenu })) : null;
  }
  renderMenuActions() {
    const { actionsMenuId: e21, actionsMenuButtonId: t26, actionsMenuOpen: i28, dividedActions: o27 } = this, { menuActions: n27 } = o27;
    return n27.length && i28 ? n5("ul", { id: e21, role: "menu", "aria-labelledby": t26, key: V2("actions"), class: n16.actions, bind: this, onkeyup: this._handleActionMenuKeyup, afterCreate: this._actionsMenuNodeUpdated, afterUpdate: this._actionsMenuNodeUpdated }, n27.toArray().map((e22) => this.renderAction({ action: e22, type: "menu-item" }))) : null;
  }
  renderInlineActions() {
    const { inlineActions: e21 } = this.dividedActions;
    return e21.length ? e21.toArray().map((e22) => this.renderAction({ action: e22, type: "inline" })) : [];
  }
  renderInlineActionsContainer() {
    const { inlineActions: e21, menuActions: t26 } = this.dividedActions, i28 = !!e21.length, o27 = !!t26.length;
    return i28 || o27 ? n5("div", { key: "inline-actions-container", "data-inline-actions": i28.toString(), "data-menu-actions": o27.toString(), class: n16.inlineActionsContainer }, this.renderInlineActions(), this.renderActionsMenuButton(), this.renderMenuActions()) : null;
  }
  renderNavigation() {
    return this.featureNavigationVisible ? n5("section", { key: V2("navigation"), class: this.classes(n16.navigation) }, this.renderNavigationButtons()) : null;
  }
  renderFooter() {
    const { featureNavigationVisible: e21, dividedActions: t26 } = this, { inlineActions: i28, menuActions: o27 } = t26, n27 = !!i28.length, s22 = !!o27.length, r20 = { [n16.footerHasPagination]: e21, [n16.footerHasActions]: n27, [n16.footerHasActionsMenu]: s22 };
    return e21 || n27 ? n5("div", { key: V2("feature-buttons"), class: this.classes(n16.footer, r20) }, this.renderInlineActionsContainer(), this.renderNavigation()) : null;
  }
  renderFeatureMenuContainer() {
    const { messages: e21 } = this, { featureViewModels: t26, isLoadingFeature: i28 } = this.viewModel, o27 = s6(e21.selectedFeatures, { total: t26.length });
    return n5("section", { key: V2("menu"), class: n16.featureMenu }, n5("strong", { class: n16.featureMenuHeader }, o27), n5("nav", { bind: this, class: n16.featureMenuViewport, "data-node-ref": "_featureMenuViewportNode", afterCreate: v5 }, this.renderFeatureMenu(), n5("div", { class: n16.featureMenuObserver, bind: this, afterCreate: this._featureMenuIntersectionObserverCreated }), i28 ? n5("div", { class: n16.featureMenuLoader }, this.renderLoadingIcon()) : null));
  }
  renderPointer() {
    return this.dockEnabled ? null : n5("div", { key: V2("pointer"), class: n16.pointer, role: "presentation" }, n5("div", { class: this.classes(n16.pointerDirection, n16.shadow) }));
  }
  renderMainContainer() {
    const { dockEnabled: e21, currentAlignment: t26, currentDockPosition: i28, titleId: o27, contentId: n27, collapsible: s22, hasContent: r20, contentCollapsed: l25, visibleElements: a32 } = this, { title: c34 } = this.viewModel, d15 = "bottom-left" === t26 || "bottom-center" === t26 || "bottom-right" === t26 || "top-left" === i28 || "top-center" === i28 || "top-right" === i28, u18 = "top-left" === t26 || "top-center" === t26 || "top-right" === t26 || "bottom-left" === i28 || "bottom-center" === i28 || "bottom-right" === i28, h18 = { [n16.shadow]: e21, [n16.isCollapsible]: s22, [n16.isCollapsed]: l25 };
    return n5("div", { class: this.classes(n16.main, n16.widget, h18), tabIndex: a32.closeButton ? void 0 : -1, role: "dialog", "aria-labelledby": c34 ? o27 : "", "aria-describedby": r20 && !l25 ? n27 : "", bind: this, onkeyup: this._handleMainKeyup, afterCreate: this._mainContainerNodeUpdated, afterUpdate: this._mainContainerNodeUpdated }, d15 ? this.renderFooter() : null, d15 ? this.renderFeatureMenuContainer() : null, this.renderHeader(), this.renderContentContainer(), u18 ? this.renderFooter() : null, u18 ? this.renderFeatureMenuContainer() : null);
  }
  renderContent() {
    var _a;
    const e21 = (_a = this.viewModel) == null ? void 0 : _a.content;
    return e21 ? "string" == typeof e21 ? n5("div", { class: t13.contentNode, key: e21, innerHTML: e21 }) : this.renderNodeContent(e21) : null;
  }
  renderActionText(e21) {
    return n5("span", { key: "text", class: n16.actionText }, e21);
  }
  renderActionIcon(e21) {
    const t26 = this._getActionClass(e21), i28 = this._getActionImage(e21), o27 = { [n16.iconLoading]: e21.active, [n16.rotating]: e21.active, [n16.icon]: !!t26, [n16.actionImage]: !e21.active && !!i28 };
    return t26 && (o27[t26] = !e21.active), n5("span", { key: "icon", "aria-hidden": "true", class: this.classes(n16.icon, o27), styles: this._getIconStyles(i28) });
  }
  renderAction(e21) {
    const { action: t26, type: i28 } = e21, o27 = this._getActionTitle(t26), n27 = { [n16.action]: "toggle" !== t26.type, [n16.actionToggle]: "toggle" === t26.type, [n16.actionToggleOn]: "toggle" === t26.type && t26.value, [n16.buttonDisabled]: t26.disabled }, s22 = [this.renderActionIcon(t26), this.renderActionText(o27)], r20 = "menu-item" === i28 ? n5("li", { key: t26.uid, role: "menuitem", tabIndex: 0, title: o27, "aria-label": o27, class: this.classes(n16.button, n27), onkeyup: this._handleActionMenuItemKeyup, bind: this, "data-action-uid": t26.uid, onclick: this._triggerAction, onkeydown: this._triggerAction }, s22) : n5("div", { key: t26.uid, role: "button", tabIndex: 0, title: o27, "aria-label": o27, class: this.classes(n16.button, n27), onkeyup: this._handleActionMenuItemKeyup, bind: this, "data-action-uid": t26.uid, onclick: this._triggerAction, onkeydown: this._triggerAction }, s22);
    return t26.visible ? r20 : null;
  }
  renderFeatureMenuItem(e21, t26) {
    const { messages: i28, messagesCommon: o27 } = this, { selectedFeatureIndex: n27, selectedFeatureViewModel: s22 } = this.viewModel, r20 = e21 === s22, l25 = { [n16.featureMenuSelected]: r20 }, a32 = r20 ? n5("span", { key: V2(`feature-menu-selected-feature-${n27}`), title: i28.selectedFeature, "aria-label": i28.selectedFeature, class: n16.iconCheckMark }) : null, c34 = n5("span", { innerHTML: e21.title || o27.untitled });
    return n5("li", { role: "menuitem", tabIndex: -1, key: V2(`feature-menu-feature-${n27}`), class: this.classes(l25, n16.featureMenuItem), bind: this, "data-feature-index": t26, onblur: this._removeActiveFeature, onfocus: this._setActiveFeature, onkeyup: this._handleFeatureMenuItemKeyup, onclick: this._selectFeature, onkeydown: this._selectFeature, onmouseover: this._setActiveFeature, onmouseleave: this._removeActiveFeature }, n5("span", { class: n16.featureMenuTitle }, c34, a32));
  }
  renderFeatureMenu() {
    const { featureMenuId: e21 } = this, { featureViewModels: t26 } = this.viewModel;
    return t26.length > 1 ? n5("ol", { class: n16.featureMenuList, id: e21, bind: this, afterCreate: this._featureMenuNodeUpdated, afterUpdate: this._featureMenuNodeUpdated, onkeyup: this._handleFeatureMenuKeyup, role: "menu" }, t26.filter((e22) => !!e22.graphic).map((e22, t27) => this.renderFeatureMenuItem(e22, t27))) : null;
  }
  _storeFeatureSelectionAction(e21, t26) {
    this._featureSelectionActions.set(e21, t26), this.scheduleRender();
  }
  _storeExitRelatedRecordsAction(e21, t26) {
    this._exitRelatedRecordsActions.set(e21, t26), this.scheduleRender();
  }
  _getFlowItemDescription(e21) {
    return "featureCountDescription" in e21 ? e21.featureCountDescription : e21.viewModel.description ?? "";
  }
  async _openRelatedFeature(e21) {
    await e21.viewModel.updateGeometry();
    const t26 = e21.graphic, i28 = t26 == null ? void 0 : t26.geometry;
    if (t(i28) || t(t26)) return;
    this._destroyFlowItemWidgets(), await this.viewModel.zoomTo({ target: i28 });
    const o27 = F6(i28);
    this.open({ features: [t26], location: r(o27) ? o27 : void 0 });
  }
  _destroyFlowItemWidgets() {
    this._flowItems.removeAll().forEach((e21) => {
      "showAllEnabled" in e21.viewModel && (e21.viewModel.showAllEnabled = false), e21.viewModel = null, e21.destroy();
    });
  }
  _handleBackClick() {
    const e21 = this._flowItems.pop();
    e21 && (this._exitRelatedRecordsActions.delete(e21), this._featureSelectionActions.delete(e21), "showAllEnabled" in e21.viewModel && (e21.viewModel.showAllEnabled = false), e21 && (e21.viewModel = null, e21.destroy()));
  }
  _getActionTitle(e21) {
    const { messages: t26, selectedFeature: i28, messagesCommon: o27 } = this, { id: n27 } = e21, s22 = i28 == null ? void 0 : i28.attributes, r20 = e21.title ?? "", l25 = "zoom-to-feature" === n27 ? s6(r20, { messages: t26 }) : "remove-selected-feature" === n27 ? s6(r20, { messages: o27 }) : "zoom-to-clustered-features" === n27 || "browse-clustered-features" === n27 ? s6(r20, { messages: t26 }) : e21.title;
    return l25 && s22 ? s6(l25, s22) : l25 ?? "";
  }
  _getActionClass(e21) {
    const { selectedFeature: t26 } = this, i28 = t26 == null ? void 0 : t26.attributes, { className: o27, image: n27 } = e21, s22 = n27 || o27 ? o27 : n16.iconDefaultAction;
    return s22 && i28 ? s6(s22, i28) : s22 ?? "";
  }
  _getActionImage(e21) {
    const { selectedFeature: t26 } = this, i28 = t26 == null ? void 0 : t26.attributes, { image: o27 } = e21;
    return o27 && i28 ? s6(o27, i28) : o27 ?? "";
  }
  _createFeatureUpdatedAnimation() {
    return w5("enter", n16.hasFeatureUpdated);
  }
  _getInlineActionCount() {
    const { maxInlineActions: e21, featureNavigationVisible: t26 } = this;
    if ("number" != typeof e21) return null;
    const i28 = Math.round(e21);
    return Math.max(t26 ? i28 - 1 : i28, 0);
  }
  _watchActions() {
    const { allActions: e21 } = this.viewModel;
    this.notifyChange("dividedActions");
    const t26 = "actions";
    this._handles.remove(t26), e21 && e21.forEach((e22) => {
      this._handles.add(l3(() => [e22.uid, e22.active, e22.className, e22.disabled, e22.id, e22.title, e22.image, e22.visible], () => this.scheduleRender()), t26);
    });
  }
  _divideActions() {
    const { allActions: e21 } = this.viewModel, i28 = e21.filter((e22) => e22.visible), o27 = this._getInlineActionCount(), n27 = null === o27, s22 = 0 === o27;
    return { inlineActions: n27 ? i28.slice(0) : s22 ? new j2() : i28.slice(0, o27), menuActions: n27 ? new j2() : s22 ? i28.slice(0) : i28.slice(o27) };
  }
  _featureMenuOpenChanged(e21) {
    e21 ? this._focusFirstFeature = true : this._focusFeatureMenuButton = true;
  }
  _actionsMenuOpenChanged(e21) {
    e21 ? this._focusFirstAction = true : this._focusActionsMenuButton = true;
  }
  _setTitleFromFeatureWidget() {
    var _a, _b;
    const { selectedFeatureWidget: e21, messagesCommon: t26 } = this;
    e21 && (this.viewModel.title = "error" === ((_a = e21.viewModel) == null ? void 0 : _a.state) ? t26.errorMessage : ((_b = e21.viewModel) == null ? void 0 : _b.title) || "");
  }
  _setContentFromFeatureWidget() {
    const { selectedFeatureWidget: e21 } = this;
    e21 && (this.viewModel.content = e21);
  }
  _unobserveFeatureMenuObserver() {
    this._featureMenuIntersectionObserverNode && this._featureMenuIntersectionObserver.unobserve(this._featureMenuIntersectionObserverNode);
  }
  _featureMenuIntersectionObserverCreated(e21) {
    this._unobserveFeatureMenuObserver(), this._featureMenuIntersectionObserver.observe(e21), this._featureMenuIntersectionObserverNode = e21;
  }
  _handleFeatureMenuKeyup(e21) {
    "Escape" === i(e21) && (e21.stopPropagation(), this._focusFeatureMenuButton = true, this.featureMenuOpen = false, this.scheduleRender());
  }
  _handleActionMenuKeyup(e21) {
    "Escape" === i(e21) && (e21.stopPropagation(), this._focusActionsMenuButton = true, this.actionsMenuOpen = false, this.scheduleRender());
  }
  _setActiveFeature(e21) {
    const { viewModel: t26 } = this, i28 = e21.currentTarget["data-feature-index"];
    t26.activeFeature = t26.features[i28] || null;
  }
  _removeActiveFeature() {
    this.viewModel.activeFeature = null;
  }
  _handleFeatureMenuItemKeyup(e21) {
    const t26 = i(e21), { _featureMenuNode: o27 } = this, n27 = e21.currentTarget["data-feature-index"];
    if (!o27) return;
    const s22 = o27.querySelectorAll("li"), r20 = s22.length;
    if ("ArrowUp" !== t26) if ("ArrowDown" !== t26) if ("Home" !== t26) if ("End" !== t26) ;
    else {
      e21.stopPropagation();
      s22[s22.length - 1].focus();
    }
    else {
      e21.stopPropagation();
      s22[0].focus();
    }
    else {
      e21.stopPropagation();
      s22[(n27 + 1 + r20) % r20].focus();
    }
    else {
      e21.stopPropagation();
      s22[(n27 - 1 + r20) % r20].focus();
    }
  }
  _handleActionMenuItemKeyup(e21) {
    const t26 = i(e21), { _actionsMenuNode: o27 } = this, n27 = e21.currentTarget.dataset.actionUid, { menuActions: s22 } = this.dividedActions, r20 = s22.findIndex((e22) => e22.uid === n27);
    if (!o27) return;
    const l25 = o27.querySelectorAll("li"), a32 = l25.length;
    if ("ArrowUp" !== t26) if ("ArrowDown" !== t26) if ("Home" !== t26) if ("End" !== t26) ;
    else {
      e21.stopPropagation();
      l25[l25.length - 1].focus();
    }
    else {
      e21.stopPropagation();
      l25[0].focus();
    }
    else {
      e21.stopPropagation();
      l25[(r20 + 1 + a32) % a32].focus();
    }
    else {
      e21.stopPropagation();
      l25[(r20 - 1 + a32) % a32].focus();
    }
  }
  _handleMainKeyup(e21) {
    const t26 = i(e21);
    "ArrowLeft" === t26 && (e21.stopPropagation(), this.previous()), "ArrowRight" === t26 && (e21.stopPropagation(), this.next());
  }
  _spinnerEnabledChange(e21) {
    if (this._destroySpinner(), !e21) return;
    const t26 = this.get("viewModel.view");
    this._createSpinner(t26);
  }
  _hideSpinner() {
    const { _spinner: e21 } = this;
    e21 && (e21.location = null, e21.hide());
  }
  _displaySpinner() {
    const { _spinner: e21 } = this;
    if (!e21) return;
    const { location: t26, waitingForResult: i28 } = this.viewModel;
    i28 && t26 ? e21.show({ location: t26 }) : e21.hide();
  }
  _getIconStyles(e21) {
    return { "background-image": e21 ? `url(${e21})` : "" };
  }
  async _shouldFocus(e21) {
    (e21 == null ? void 0 : e21.shouldFocus) && (await j3(() => {
      var _a;
      return true === ((_a = this.viewModel) == null ? void 0 : _a.active);
    }), this.focus());
  }
  _addSelectedFeatureIndexHandle() {
    const e21 = l3(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.selectedFeatureIndex;
    }, (e22, t26) => this._selectedFeatureIndexUpdated(e22, t26));
    this._handles.add(e21, B4);
  }
  _selectedFeatureIndexUpdated(e21, t26) {
    const { featureCount: i28 } = this;
    i28 && e21 !== t26 && -1 !== e21 && (this._destroyFlowItemWidgets(), this.actionsMenuOpen = false, this.featureMenuOpen = false, this._mainContainerNode && (this._mainContainerNode.scrollTop = 0), this._rootFlowItemNode && this._rootFlowItemNode.scrollContentTo({ top: 0 }));
  }
  _destroySelectedFeatureWidget() {
    const { _feature: e21 } = this;
    e21 && (e21.viewModel = null, e21 && !e21.destroyed && e21.destroy()), this._feature = null;
  }
  _isScreenLocationWithinView(e21, t26) {
    return e21.x > -1 && e21.y > -1 && e21.x <= t26.width && e21.y <= t26.height;
  }
  _isOutsideView(e21) {
    const { popupHeight: t26, popupWidth: i28, screenLocation: o27, side: n27, view: s22 } = e21;
    if (isNaN(i28) || isNaN(t26) || !s22 || !o27) return false;
    const r20 = s22.padding;
    return "right" === n27 && o27.x + i28 / 2 > s22.width - r20.right || ("left" === n27 && o27.x - i28 / 2 < r20.left || ("top" === n27 && o27.y - t26 < r20.top || "bottom" === n27 && o27.y + t26 > s22.height - r20.bottom));
  }
  _calculateAutoAlignment(e21) {
    if ("auto" !== e21) return e21;
    const { _pointerOffsetInPx: t26, _containerNode: i28, _mainContainerNode: o27, viewModel: n27 } = this, { screenLocation: r20, view: l25 } = n27;
    if (t(r20) || !l25 || !i28) return "top-center";
    if (!this._isScreenLocationWithinView(r20, l25)) return this._get("currentAlignment") || "top-center";
    function a32(e22) {
      return parseInt(e22.replace(/[^-\d\.]/g, ""), 10);
    }
    const c34 = o27 ? window.getComputedStyle(o27, null) : null, d15 = c34 ? a32(c34.getPropertyValue("max-height")) : 0, u18 = c34 ? a32(c34.getPropertyValue("height")) : 0, { height: h18, width: p30 } = i28.getBoundingClientRect(), g16 = p30 + t26, m24 = Math.max(h18, d15, u18) + t26, v16 = this._isOutsideView({ popupHeight: m24, popupWidth: g16, screenLocation: r20, side: "right", view: l25 }), f17 = this._isOutsideView({ popupHeight: m24, popupWidth: g16, screenLocation: r20, side: "left", view: l25 }), _9 = this._isOutsideView({ popupHeight: m24, popupWidth: g16, screenLocation: r20, side: "top", view: l25 }), w18 = this._isOutsideView({ popupHeight: m24, popupWidth: g16, screenLocation: r20, side: "bottom", view: l25 });
    return f17 ? _9 ? "bottom-right" : "top-right" : v16 ? _9 ? "bottom-left" : "top-left" : _9 ? w18 ? "top-center" : "bottom-center" : "top-center";
  }
  _callCurrentAlignment(e21) {
    return "function" == typeof e21 ? e21.call(this) : e21;
  }
  _getCurrentAlignment() {
    const { alignment: e21, dockEnabled: t26 } = this;
    return t26 || !this.viewModel.active ? null : this._calculatePositionResult(this._calculateAutoAlignment(this._callCurrentAlignment(e21)));
  }
  _setCurrentAlignment() {
    this._set("currentAlignment", this._getCurrentAlignment());
  }
  _setCurrentDockPosition() {
    this._set("currentDockPosition", this._getCurrentDockPosition());
  }
  _calculatePositionResult(e21) {
    const t26 = ["left", "right"];
    return f4(this.container) && t26.reverse(), e21.replace(/leading/gi, t26[0]).replace(/trailing/gi, t26[1]);
  }
  _callDockPosition(e21) {
    return "function" == typeof e21 ? e21.call(this) : e21;
  }
  _getDockPosition() {
    var _a;
    return this._calculatePositionResult(this._calculateAutoDockPosition(this._callDockPosition((_a = this.dockOptions) == null ? void 0 : _a.position)));
  }
  _getCurrentDockPosition() {
    return this.dockEnabled && this.viewModel.active ? this._getDockPosition() : null;
  }
  _wouldDockTo() {
    return this.dockEnabled ? null : this._getDockPosition();
  }
  _calculateAutoDockPosition(e21) {
    var _a;
    if ("auto" !== e21) return e21;
    const t26 = (_a = this.viewModel) == null ? void 0 : _a.view, i28 = f4(this.container) ? "top-left" : "top-right";
    if (!t26) return i28;
    const o27 = t26.padding || { left: 0, right: 0, top: 0, bottom: 0 }, n27 = t26.width - o27.left - o27.right, { breakpoints: s22 } = t26;
    return s22 && n27 <= s22.xsmall ? "bottom-center" : i28;
  }
  _positionContainer(e21 = this._containerNode) {
    if (e21 && (this._containerNode = e21), !this._containerNode) return;
    const { screenLocation: t26 } = this.viewModel, { width: i28 } = this._containerNode.getBoundingClientRect(), o27 = this._calculatePositionStyle(t26, i28);
    o27 && Object.assign(this._containerNode.style, o27);
  }
  _calculateFullWidth(e21) {
    const { currentAlignment: t26, _pointerOffsetInPx: i28 } = this;
    return "top-left" === t26 || "bottom-left" === t26 || "top-right" === t26 || "bottom-right" === t26 ? e21 + i28 : e21;
  }
  _calculateAlignmentPosition(e21, t26, i28, o27) {
    const { currentAlignment: n27, _pointerOffsetInPx: s22 } = this;
    if (!i28) return;
    const { padding: r20 } = i28, l25 = o27 / 2, a32 = i28.height - t26, c34 = i28.width - e21;
    return "bottom-center" === n27 ? { top: t26 + s22 - r20.top, left: e21 - l25 - r20.left } : "top-left" === n27 ? { bottom: a32 + s22 - r20.bottom, right: c34 + s22 - r20.right } : "bottom-left" === n27 ? { top: t26 + s22 - r20.top, right: c34 + s22 - r20.right } : "top-right" === n27 ? { bottom: a32 + s22 - r20.bottom, left: e21 + s22 - r20.left } : "bottom-right" === n27 ? { top: t26 + s22 - r20.top, left: e21 + s22 - r20.left } : "top-center" === n27 ? { bottom: a32 + s22 - r20.bottom, left: e21 - l25 - r20.left } : void 0;
  }
  _calculatePositionStyle(e21, t26) {
    const { dockEnabled: i28, view: o27 } = this;
    if (!o27) return;
    if (i28) return { left: "", top: "", right: "", bottom: "" };
    if (t(e21) || !t26) return;
    const n27 = this._calculateFullWidth(t26), r20 = this._calculateAlignmentPosition(e21.x, e21.y, o27, n27);
    return r20 ? { top: void 0 !== r20.top ? `${r20.top}px` : "auto", left: void 0 !== r20.left ? `${r20.left}px` : "auto", bottom: void 0 !== r20.bottom ? `${r20.bottom}px` : "auto", right: void 0 !== r20.right ? `${r20.right}px` : "auto" } : void 0;
  }
  _viewChange(e21, t26) {
    e21 && t26 && (this.close(), this.clear());
  }
  _viewReadyChange(e21, t26) {
    if (e21) {
      const e22 = this.get("viewModel.view");
      this._wireUpView(e22);
    } else t26 && (this.close(), this.clear());
  }
  _wireUpView(e21) {
    if (this._destroySpinner(), !e21) return;
    const { spinnerEnabled: t26 } = this;
    t26 && this._createSpinner(e21), this._setDockEnabledForViewSize(this.dockOptions);
  }
  _dockingThresholdCrossed(e21, t26, i28) {
    const [o27, n27] = e21, [s22, r20] = t26, { width: l25 = 0, height: a32 = 0 } = i28 ?? {};
    return o27 <= l25 && s22 > l25 || o27 > l25 && s22 <= l25 || n27 <= a32 && r20 > a32 || n27 > a32 && r20 <= a32;
  }
  _updateDockEnabledForViewSize(e21, t26) {
    if (!e21 || !t26) return;
    const i28 = this.get("viewModel.view.padding") || { left: 0, right: 0, top: 0, bottom: 0 }, o27 = i28.left + i28.right, n27 = i28.top + i28.bottom, s22 = [], r20 = [];
    s22[0] = e21[0] - o27, s22[1] = e21[1] - n27, r20[0] = t26[0] - o27, r20[1] = t26[1] - n27;
    const { dockOptions: l25 } = this, a32 = l25.breakpoint;
    this._dockingThresholdCrossed(s22, r20, a32) && this._setDockEnabledForViewSize(l25), this._setCurrentDockPosition();
  }
  _focusDockButtonNode(e21) {
    this._focusDockButton && (this._focusDockButton = false, e21.focus());
  }
  _closeButtonNodeUpdated(e21) {
    return this._focusClose ? (this._focusClose = false, void e21.focus()) : this._blurClose ? (this._blurClose = false, void e21.blur()) : void 0;
  }
  _mainContainerNodeUpdated(e21) {
    return this._mainContainerNode = e21, this._focusContainer ? (this._focusContainer = false, void e21.focus()) : this._blurContainer ? (this._blurContainer = false, void e21.blur()) : void 0;
  }
  _featureMenuNodeUpdated(e21) {
    if (this._featureMenuNode = e21, !e21 || !this._focusFirstFeature) return;
    this._focusFirstFeature = false;
    const t26 = e21.querySelectorAll("li");
    if (t26.length) {
      t26[0].focus();
    }
  }
  _actionsMenuNodeUpdated(e21) {
    if (this._actionsMenuNode = e21, !e21 || !this._focusFirstAction) return;
    this._focusFirstAction = false;
    const t26 = e21.querySelectorAll("li");
    if (t26.length) {
      t26[0].focus();
    }
  }
  _focusFeatureMenuButtonNode(e21) {
    this._focusFeatureMenuButton && (this._focusFeatureMenuButton = false, e21.focus());
  }
  _focusActionsMenuButtonNode(e21) {
    this._focusActionsMenuButton && (this._focusActionsMenuButton = false, e21.focus());
  }
  _featureMenuViewportScrollTop() {
    this._featureMenuViewportNode && (this._featureMenuViewportNode.scrollTop = 0);
  }
  _toggleScreenLocationEnabled() {
    const { dockEnabled: e21, viewModel: t26 } = this;
    if (!t26) return;
    const i28 = t26.active && !e21;
    t26.screenLocationEnabled = i28;
  }
  _shouldDockAtCurrentViewSize(e21) {
    var _a, _b;
    const t26 = e21.breakpoint, i28 = (_b = (_a = this.viewModel) == null ? void 0 : _a.view) == null ? void 0 : _b.ui;
    if (!i28) return false;
    const { width: o27, height: n27 } = i28;
    if (isNaN(o27) || isNaN(n27)) return false;
    if (!t26) return false;
    const s22 = t26.hasOwnProperty("width") && o27 <= (t26.width ?? 0), r20 = t26.hasOwnProperty("height") && n27 <= (t26.height ?? 0);
    return s22 || r20;
  }
  _setDockEnabledForViewSize(e21) {
    e21.breakpoint && (this.dockEnabled = this._shouldDockAtCurrentViewSize(e21));
  }
  _getPageText(e21, t26) {
    return this.featureNavigationVisible ? s6(this.messages.pageText, { index: t26 + 1, total: e21 }) : null;
  }
  _destroySpinner() {
    const { _spinner: e21, view: t26 } = this;
    e21 && (t26 && t26.ui && t26.ui.remove(e21, L5), e21.destroy(), this._spinner = null);
  }
  _createSpinner(e21) {
    e21 && (this._spinner = new m14({ view: e21 }), e21.ui.add(this._spinner, { key: L5, position: "manual" }));
  }
  _toggleCollapsed() {
    this.collapsed = !this.collapsed;
  }
  _close() {
    this.close(), this.view && this.view.focus();
  }
  _toggleDockEnabled() {
    this.dockEnabled = !this.dockEnabled, this._focusDockButton = true, this.scheduleRender();
  }
  _toggleFeatureMenu() {
    const e21 = !this.featureMenuOpen;
    this._featureMenuOpenChanged(e21), this.actionsMenuOpen = false, this.featureMenuOpen = e21;
  }
  _toggleActionsMenu() {
    const e21 = !this.actionsMenuOpen;
    this._actionsMenuOpenChanged(e21), this.featureMenuOpen = false, this.actionsMenuOpen = e21;
  }
  _triggerAction(e21) {
    const t26 = e21.currentTarget.dataset.actionUid, { allActions: i28 } = this.viewModel, o27 = i28.findIndex((e22) => e22.uid === t26), n27 = i28.getItemAt(o27);
    n27 && "toggle" === n27.type && (n27.value = !n27.value), this.actionsMenuOpen = false, this.viewModel.triggerAction(o27);
  }
  _selectFeature(e21) {
    const t26 = e21.currentTarget["data-feature-index"];
    isNaN(t26) || (this.viewModel.selectedFeatureIndex = t26), this.featureMenuOpen = false, this._focusFeatureMenuButton = true, this.scheduleRender();
  }
  _next() {
    this.next();
  }
  _previous() {
    this.previous();
  }
};
e([y({ readOnly: true })], W5.prototype, "actionsMenuId", null), e([y({ readOnly: true })], W5.prototype, "actionsMenuButtonId", null), e([y({ readOnly: true })], W5.prototype, "featureMenuId", null), e([y({ readOnly: true })], W5.prototype, "titleId", null), e([y({ readOnly: true })], W5.prototype, "contentId", null), e([y({ readOnly: true })], W5.prototype, "hasContent", null), e([y({ readOnly: true })], W5.prototype, "featureNavigationVisible", null), e([y({ readOnly: true })], W5.prototype, "collapsible", null), e([y({ readOnly: true })], W5.prototype, "featureMenuVisible", null), e([y({ readOnly: true })], W5.prototype, "contentCollapsed", null), e([y({ readOnly: true })], W5.prototype, "dividedActions", null), e([y({ readOnly: true, dependsOn: ["_flowItems.length"] })], W5.prototype, "_activeFlowItemWidget", null), e([y()], W5.prototype, "actions", null), e([y()], W5.prototype, "actionsMenuOpen", null), e([y()], W5.prototype, "alignment", void 0), e([y()], W5.prototype, "autoCloseEnabled", null), e([y()], W5.prototype, "autoOpenEnabled", null), e([y()], W5.prototype, "defaultPopupTemplateEnabled", null), e([y()], W5.prototype, "content", null), e([y()], W5.prototype, "collapsed", void 0), e([y()], W5.prototype, "collapseEnabled", void 0), e([y({ readOnly: true })], W5.prototype, "currentAlignment", null), e([y({ readOnly: true })], W5.prototype, "currentDockPosition", null), e([y()], W5.prototype, "dockOptions", null), e([y()], W5.prototype, "dockEnabled", void 0), e([y({ readOnly: true })], W5.prototype, "featureCount", null), e([y()], W5.prototype, "featureMenuOpen", void 0), e([y()], W5.prototype, "features", null), e([y()], W5.prototype, "goToOverride", null), e([y()], W5.prototype, "headingLevel", void 0), e([y()], W5.prototype, "highlightEnabled", null), e([y()], W5.prototype, "location", null), e([y()], W5.prototype, "label", null), e([y()], W5.prototype, "maxInlineActions", void 0), e([y(), e4("esri/widgets/Popup/t9n/Popup")], W5.prototype, "messages", void 0), e([y(), e4("esri/t9n/common")], W5.prototype, "messagesCommon", void 0), e([y()], W5.prototype, "promises", null), e([y({ readOnly: true })], W5.prototype, "selectedFeature", null), e([y()], W5.prototype, "selectedFeatureIndex", null), e([y({ readOnly: true })], W5.prototype, "selectedFeatureWidget", null), e([y()], W5.prototype, "spinnerEnabled", void 0), e([y()], W5.prototype, "title", null), e([y()], W5.prototype, "updateLocationEnabled", null), e([y()], W5.prototype, "view", null), e([y({ type: H4 }), e5(["triggerAction", "trigger-action"])], W5.prototype, "viewModel", void 0), e([y()], W5.prototype, "visible", null), e([y()], W5.prototype, "visibleElements", void 0), e([s4("visibleElements")], W5.prototype, "castVisibleElements", null), e([t8()], W5.prototype, "_close", null), e([t8()], W5.prototype, "_toggleDockEnabled", null), e([t8()], W5.prototype, "_toggleFeatureMenu", null), e([t8()], W5.prototype, "_toggleActionsMenu", null), e([t8()], W5.prototype, "_triggerAction", null), e([t8()], W5.prototype, "_selectFeature", null), e([t8()], W5.prototype, "_next", null), e([t8()], W5.prototype, "_previous", null), W5 = e([a2("esri.widgets.Popup")], W5);
var j9 = W5;

// node_modules/@arcgis/core/views/DOMContainer.js
var m16 = [0, 0];
function f13(e21) {
  const t26 = (e21.ownerDocument || window.document).defaultView, s22 = e21.getBoundingClientRect();
  return m16[0] = s22.left + ((t26 == null ? void 0 : t26.pageXOffset) ?? 0), m16[1] = s22.top + ((t26 == null ? void 0 : t26.pageYOffset) ?? 0), m16;
}
function y11(e21) {
  e21 && (t7(e21), e21.parentNode && e21.parentNode.removeChild(e21));
}
function v13(e21) {
  const t26 = document.createElement("div");
  return e21.appendChild(t26), t26;
}
var _5 = 16;
var g11 = 750;
var w15 = 512;
var C6 = 2;
var z4 = (i28) => {
  let m24 = class extends i28 {
    constructor(...e21) {
      var _a;
      super(...e21), this._freqInfo = { freq: _5, time: g11 }, this._overlayRenderTaskHandle = null, this.height = 0, this.overlay = null, this.position = null, this.resizing = false, this.root = null, this.surface = null, this.suspended = true, this.ui = null, this.userContent = null, this.width = 0, this.widthBreakpoint = null, 0 !== e21.length && void 0 !== ((_a = e21[0]) == null ? void 0 : _a.popup) || (this.popup = new j9({ view: this })), this.handles.add([l3(() => this.cursor, (e22) => {
        const { surface: t26 } = this;
        t26 && t26.setAttribute("data-cursor", e22);
      }), l3(() => this.interacting, (e22) => {
        const { surface: t26 } = this;
        t26 && t26.setAttribute("data-interacting", e22.toString());
      })]);
    }
    initialize() {
      this.handles.add(l3(() => this.ui, (e21, t26) => this._handleUIChange(e21, t26))), this._wireUI(this.ui), this.handles.add([this.on("focus", () => this.notifyChange("focused")), this.on("blur", () => this.notifyChange("focused"))]);
    }
    destroy() {
      this.destroyed || (this.ui = a(this.ui), this.popup && !this.popup.destroyed && this.popup.destroy(), this.container = null);
    }
    get container() {
      return this._get("container") ?? null;
    }
    set container(e21) {
      const i29 = this._get("container"), r20 = e3(e21);
      if (r20 || "string" != typeof e21 || s2.getLogger(this.declaredClass).error("#container", `element with id '${e21}' not found`), i29 === r20) return;
      const l25 = "dom-size";
      if (this.handles.remove(l25), this._stopMeasuring(), i29 && (i29.classList.remove("esri-view"), this._overlayRenderTaskHandle && (this._overlayRenderTaskHandle.remove(), this._overlayRenderTaskHandle = null), this.overlay && (this.overlay.destroy(), this._set("overlay", null)), this.root && (y11(this.root), this._set("root", null)), this.userContent && (r4(this.userContent, i29), y11(this.userContent), this._set("userContent", null))), !r20) return this._set("width", 0), this._set("height", 0), this._set("position", null), this._set("suspended", true), this._set("surface", null), void this._set("container", null);
      r20.classList.add("esri-view");
      const u18 = document.createElement("div");
      u18.className = "esri-view-user-storage", r4(r20, u18), r20.appendChild(u18), this._set("userContent", u18);
      const c34 = document.createElement("div");
      c34.className = "esri-view-root", r20.insertBefore(c34, r20.firstChild), this._set("root", c34);
      const m25 = document.createElement("div");
      m25.className = "esri-view-surface", m25.setAttribute("role", "application"), m25.tabIndex = 0, c34.appendChild(m25), this._set("surface", m25);
      const f17 = new c12();
      c34.appendChild(f17.surface), this._set("overlay", f17), l3(() => f17.needsRender, (e22) => {
        e22 && !this._overlayRenderTaskHandle ? this._overlayRenderTaskHandle = A2({ render: () => {
          var _a;
          return (_a = this.overlay) == null ? void 0 : _a.render();
        } }) : this._overlayRenderTaskHandle = p(this._overlayRenderTaskHandle);
      }), this.forceDOMReadyCycle(), this.handles.add(l3(() => this.size, (e22) => {
        const [t26, s22] = e22, i30 = "esri-view-surface--inset-outline";
        t26 >= document.body.clientWidth || s22 >= document.body.clientHeight ? m25.classList.add(i30) : m25.classList.remove(i30);
      }, h), l25), this._set("container", r20), this._startMeasuring();
    }
    get focused() {
      const e21 = document.activeElement === this.surface;
      return document.hasFocus() && e21;
    }
    set popup(e21) {
      const t26 = this._get("popup");
      t26 && t26 !== e21 && t26.destroy(), this._set("popup", e21);
    }
    get size() {
      return [this.width, this.height];
    }
    blur() {
      this.surface && this.surface.blur();
    }
    focus() {
      this.surface && this.surface.focus();
    }
    pageToContainer(e21, t26, s22) {
      const i29 = this.position;
      return e21 -= i29 ? i29[0] : 0, t26 -= i29 ? i29[1] : 0, s22 ? (s22[0] = e21, s22[1] = t26) : s22 = [e21, t26], s22;
    }
    containerToPage(e21, t26, s22) {
      const i29 = this.position;
      return e21 += i29 ? i29[0] : 0, t26 += i29 ? i29[1] : 0, s22 ? (s22[0] = e21, s22[1] = t26) : s22 = [e21, t26], s22;
    }
    _handleUIChange(e21, t26) {
      t26 && (this.handles.remove("ui"), t26.destroy()), e21 && this._wireUI(e21), this._set("ui", e21);
    }
    _wireUI(e21) {
      this.handles.remove("ui"), e21 && (e21.view = this, this.handles.add([l3(() => this.root, (t26) => {
        e21.container = t26 ? v13(t26) : null;
      }, h), l3(() => this.popup, (t26, s22) => {
        const i29 = "popup", o27 = "manual";
        s22 && e21.remove(s22, i29), t26 && (t26.view = e21.view, e21.add(t26, { key: i29, position: o27 }));
      }, h)], "ui"));
    }
    _stopMeasuring() {
      this.handles.remove("measuring"), this._get("resizing") && this._set("resizing", false);
    }
    _startMeasuring() {
      const e21 = this._freqInfo;
      e21.freq = _5, e21.time = g11, this.handles.add([(() => {
        const t26 = () => {
          e21.freq = _5, e21.time = g11;
        };
        return window.addEventListener("resize", t26), { remove() {
          window.removeEventListener("resize", t26);
        } };
      })(), A2({ prepare: (e22) => {
        const t26 = this._measure(), s22 = this._freqInfo;
        if (s22.time += e22.deltaTime, t26 && (s22.freq = _5, this._get("resizing") || this._set("resizing", true)), s22.time < s22.freq) return;
        s22.time = 0;
        const i29 = this._position();
        s22.freq = i29 || t26 ? _5 : Math.min(g11, s22.freq * C6), !t26 && s22.freq >= w15 && this._get("resizing") && this._set("resizing", false);
      } })], "measuring"), this._measure(), this._position();
    }
    _measure() {
      const e21 = this.container, t26 = e21 ? e21.clientWidth : 0, s22 = e21 ? e21.clientHeight : 0;
      if (0 === t26 || 0 === s22) return this.suspended || this._set("suspended", true), false;
      const i29 = this.width, o27 = this.height;
      return t26 === i29 && s22 === o27 ? (this.suspended && this._set("suspended", false), false) : (this._set("width", t26), this._set("height", s22), this.suspended && this._set("suspended", false), this.emit("resize", { oldWidth: i29, oldHeight: o27, width: t26, height: s22 }), true);
    }
    _position() {
      const e21 = this.container, t26 = this.position, s22 = e21 && f13(e21);
      return !!s22 && ((!t26 || s22[0] !== t26[0] || s22[1] !== t26[1]) && (this._set("position", [s22[0], s22[1]]), true));
    }
    forceDOMReadyCycle() {
    }
  };
  return e([y()], m24.prototype, "container", null), e([y({ readOnly: true })], m24.prototype, "focused", null), e([y({ readOnly: true })], m24.prototype, "height", void 0), e([y({ type: j9 })], m24.prototype, "popup", null), e([y({ type: c12 })], m24.prototype, "overlay", void 0), e([y({ readOnly: true })], m24.prototype, "position", void 0), e([y({ readOnly: true })], m24.prototype, "resizing", void 0), e([y({ readOnly: true })], m24.prototype, "root", void 0), e([y({ value: null, readOnly: true })], m24.prototype, "size", null), e([y({ readOnly: true })], m24.prototype, "surface", void 0), e([y({ readOnly: true })], m24.prototype, "suspended", void 0), e([y()], m24.prototype, "ui", void 0), e([y({ readOnly: true })], m24.prototype, "userContent", void 0), e([y({ readOnly: true })], m24.prototype, "width", void 0), e([y()], m24.prototype, "widthBreakpoint", void 0), m24 = e([a2("esri.views.DOMContainer")], m24), m24;
};

// node_modules/@arcgis/core/views/PopupView.js
var a16 = (a32) => {
  let p30 = class extends a32 {
    async fetchPopupFeatures(e21, r20) {
      await this.when();
      const { location: i28, queryArea: a33, layerViewsAndGraphics: p31, clientOnlyGraphics: t26 } = await this._prepareFetchPopupFeatures(e21, r20), o27 = Promise.resolve(t26), c34 = this._queryLayerPopupFeatures(a33, p31, r20), n27 = c34.map((e22) => e22.promise);
      return { location: i28, clientOnlyGraphics: t26, allGraphicsPromise: L([o27, ...n27]).then((e22) => Array.from(new Set(e22.flat()))), promisesPerLayerView: c34 };
    }
    _queryLayerPopupFeatures(e21, s22, i28) {
      return s22.map(({ layerView: s23, graphics: a33 }) => {
        const p31 = { clientGraphics: a33, event: r(i28) ? i28.event : void 0, signal: r(i28) ? i28.signal : void 0, defaultPopupTemplateEnabled: !!r(i28) && !!i28.defaultPopupTemplateEnabled }, t26 = s23.fetchPopupFeatures(e21, p31);
        return { layerView: s23, promise: t26 };
      });
    }
    _isValidPopupGraphic(e21, s22) {
      return e21 && !!e21.getEffectivePopupTemplate(r(s22) && s22.defaultPopupTemplateEnabled);
    }
    async _prepareFetchPopupFeatures(e21, r20) {
      const { clientGraphics: s22, queryArea: i28, location: a33 } = await this._popupHitTestGraphics(e21, r20), p31 = this._getFetchPopupLayerViews(), { layerViewsAndGraphics: t26, clientOnlyGraphics: o27 } = this._graphicsPerFetchPopupLayerView(s22, p31);
      return { clientOnlyGraphics: o27, layerViewsAndGraphics: t26, queryArea: i28, location: a33 };
    }
    async _popupHitTestGraphics(e21, r20) {
      const s22 = await this.popupHitTest(e21), i28 = s22.results, a33 = s22.mapPoint, p31 = i28.filter((e22) => "graphic" === e22.type && this._isValidPopupGraphic(e22.graphic, r20)), t26 = p31.length ? p31[0].mapPoint : null;
      return { clientGraphics: p31.map((e22) => e22.graphic), queryArea: a33, location: a33 || t26 };
    }
    _getFetchPopupLayerViews() {
      const e21 = [];
      return this.allLayerViews.forEach((r20) => {
        this._isValidPopupLayerView(r20) && e21.push(r20);
      }), r(this.graphicsView) && this._isValidPopupLayerView(this.graphicsView) && e21.push(this.graphicsView), e21.reverse();
    }
    _isValidPopupLayerView(e21) {
      return r(e21) && (!("layer" in e21) || !e21.suspended) && "fetchPopupFeatures" in e21;
    }
    _graphicsPerFetchPopupLayerView(e21, r20) {
      const s22 = [], i28 = /* @__PURE__ */ new Map(), a33 = r20.map((e22) => {
        const r21 = [];
        return "layer" in e22 ? i28.set(e22.layer, r21) : i28.set(e22.graphics, r21), { layerView: e22, graphics: r21 };
      });
      for (const p31 of e21) {
        const e22 = i28.get(p31.layer) || i28.get(p31.sourceLayer) || null;
        e22 ? e22.push(p31) : s22.push(p31);
      }
      return { layerViewsAndGraphics: a33, clientOnlyGraphics: s22 };
    }
  };
  return p30 = e([a2("esri.views.PopupView")], p30), p30;
};

// node_modules/@arcgis/core/support/AnalysesCollection.js
var i15 = class extends l7 {
  constructor(e21) {
    super(e21), this.handles.add(this.on("before-add", (e22) => {
      t(e22.item) || e22.item.parent === this.owner && (s2.getLogger(this.declaredClass).warn("Analysis inside the collection must be unique. Not adding this element again."), e22.preventDefault());
    }));
  }
  _own(e21) {
    e21.parent = this.owner;
  }
  _release(e21) {
    e21.parent = null;
  }
};
i15 = e([a2("esri.support.AnalysesCollection")], i15);

// node_modules/@arcgis/core/views/BasemapView.js
var p15 = class extends v3 {
  constructor(e21) {
    super(e21), this.view = null, this.baseLayerViews = new j2(), this.referenceLayerViews = new j2(), this._loadingHandle = l3(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.map) == null ? void 0 : _b.basemap;
    }, (e22) => {
      e22 && e22.load().catch(() => {
      });
    }, h);
  }
  destroy() {
    this._set("view", null), this._loadingHandle && (this._loadingHandle.remove(), this._loadingHandle = null);
  }
  get suspended() {
    return !this.view || this.view.suspended;
  }
  get updating() {
    var _a, _b;
    if (this.view && this.view.suspended) return false;
    const e21 = (_b = (_a = this.view) == null ? void 0 : _a.map) == null ? void 0 : _b.basemap;
    return !!e21 && (!!e21.loaded && (this.baseLayerViews.some((e22) => e22.updating) || this.referenceLayerViews.some((e22) => e22.updating)));
  }
};
e([y({ constructOnly: true })], p15.prototype, "view", void 0), e([y({ readOnly: true })], p15.prototype, "baseLayerViews", void 0), e([y({ readOnly: true })], p15.prototype, "referenceLayerViews", void 0), e([y({ readOnly: true })], p15.prototype, "suspended", null), e([y({ type: Boolean, readOnly: true })], p15.prototype, "updating", null), p15 = e([a2("esri.views.BasemapView")], p15);

// node_modules/@arcgis/core/views/LayerViewManager.js
function v14(e21) {
  return "tryRecycleWith" in e21;
}
var V3 = class {
  constructor(e21, r20, t26) {
    this.layer = e21, this.view = r20, this.layerViewImporter = t26, this._controller = new AbortController(), this._deferred = A(), this._started = false, this.done = false, this.promise = this._deferred.promise, v(this._controller.signal, () => {
      const r21 = new s3("cancelled:layerview-create", "layerview creation cancelled", { layer: e21 });
      this._deferred.reject(r21);
    });
  }
  tryRecycle(e21) {
    if (!this.done || !this.layerView || !v14(this.layerView)) return null;
    const r20 = this.layer.type, i28 = this._controller.signal;
    for (let t26 = 0; t26 < e21.length; t26++) {
      const s22 = e21[t26];
      if (s22.type !== r20) continue;
      const a32 = this.layerView.tryRecycleWith(s22, { signal: i28 });
      if (a32) {
        e21.splice(t26, 1), this.layer = s22;
        const r21 = this.layerView, i29 = r21.view;
        return this.promise = Promise.race([a32.then(() => (f(this._controller.signal), s22.emit("layerview-destroy", { view: i29, layerView: r21 }), i29.emit("layerview-destroy", { view: i29, layerView: r21 }), s22.emit("layerview-create", { view: i29, layerView: r21 }), i29.emit("layerview-create", { view: i29, layerView: r21 }), r21)), new Promise((e22, r22) => v(this._controller.signal, () => r22(a3())))]), this.promise;
      }
    }
    return null;
  }
  destroy() {
    this._controller.abort();
    const { layerView: e21 } = this;
    if (!e21) return;
    const { layer: r20, view: i28 } = this;
    r20.emit("layerview-destroy", { view: i28, layerView: e21 }), i28.emit("layerview-destroy", { layer: r20, layerView: e21 }), this.done = true, this.layer = null, this.layerView = null, this.view = null, this.layerViewImporter = null;
  }
  async start() {
    var _a, _b, _c;
    if (this._started) return;
    this._started = true;
    const { _controller: { signal: e21 }, layer: r20, view: t26 } = this;
    this._map = t26.map;
    try {
      let a32, o27;
      if (await r20.load({ signal: e21 }), "prefetchResources" in r20 && await ((_a = r20.prefetchResources) == null ? void 0 : _a.call(r20, { signal: e21 })), I6(r20)) a32 = await r20.createLayerView(t26, { signal: e21 });
      else {
        if (!this.layerViewImporter.hasLayerViewModule(r20)) throw new s3("layer:view-not-supported", "No layerview implementation was found");
        const s22 = await this.layerViewImporter.importLayerView(r20);
        f(e21), a32 = "default" in s22 ? new s22.default({ layer: r20, view: t26 }) : new s22({ layer: r20, view: t26 });
      }
      const n27 = () => {
        o27 = p(o27), a32.destroyed || a32.destroy(), a32.layer = null, a32.parent = null, a32.view = null, this.done = true;
      };
      o27 = v(e21, n27), f(e21);
      try {
        await a32.when();
      } catch (s22) {
        throw n27(), s22;
      }
      const y16 = (_c = (_b = this._map) == null ? void 0 : _b.allLayers) == null ? void 0 : _c.includes(r20);
      if (!y16) return n27(), void this._deferred.reject(new s3("view:no-layerview-for-layer", "The layer has been removed from the map", { layer: r20 }));
      this.layerView = a32, r20.emit("layerview-create", { view: t26, layerView: a32 }), t26.emit("layerview-create", { layer: r20, layerView: a32 }), this.done = true, this._deferred.resolve(a32);
    } catch (s22) {
      r20.emit("layerview-create-error", { view: t26, error: s22 }), t26.emit("layerview-create-error", { layer: r20, error: s22 }), this.done = true, this._deferred.reject(new s3("layerview:create-error", "layerview creation failed", { layer: r20, error: s22 }));
    }
  }
};
var L6 = class extends v3 {
  constructor(e21) {
    super(e21), this._layerLayerViewInfoMap = /* @__PURE__ */ new Map(), this._recyclingInfoMap = /* @__PURE__ */ new Map(), this._watchUpdatingTracking = new c3(), this.supportsGround = true, this._preloadLayerViewModules = () => {
      var _a;
      const e22 = (_a = this.view.map) == null ? void 0 : _a.allLayers;
      if (e22) for (const r20 of e22) this.layerViewImporter.hasLayerViewModule(r20) && this.layerViewImporter.importLayerView(r20);
    }, this._reschedule = () => (t(this._workPromise) && (this._workPromise = A(), this._workPromise.promise.catch(() => {
    })), this.removeHandles("reschedule"), this.addHandles(v2(this._doWork), "reschedule"), this._workPromise.promise), this._doWork = () => {
      var _a, _b, _c;
      const e22 = this.view.map;
      if (this._map !== e22 && (this.clear(), this._map = e22), t(this._workPromise)) return void this.notifyChange("updating");
      this.removeHandles("reschedule"), this.removeHandles("collection-change");
      const r20 = /* @__PURE__ */ new Set(), i28 = [], t26 = this.view.ready, s22 = (e23) => {
        if (!t(e23)) {
          for (const a32 of e23) if (a32) {
            r20.add(a32);
            const e24 = this._layerLayerViewInfoMap.get(a32);
            e24 && t26 ? e24.start() : e24 || this._recyclingInfoMap.has(a32) || i28.push(a32), "layers" in a32 && a32.layers && s22(a32.layers);
          }
        }
      };
      for (const a32 of this._rootCollectionNames) s22(this.get(a32));
      for (const [a32, l25] of this._layerLayerViewInfoMap) if (!r20.has(a32)) {
        this._layerLayerViewInfoMap.delete(l25.layer);
        const e23 = l25.tryRecycle(i28);
        e23 ? (this._recyclingInfoMap.set(l25.layer, l25), e23.then(() => {
          this._recyclingInfoMap.delete(l25.layer), this._layerLayerViewInfoMap.set(l25.layer, l25), this._reschedule();
        }).catch(() => {
          this._recyclingInfoMap.delete(l25.layer), l25.destroy(), this._reschedule();
        })) : l25.destroy();
      }
      for (const [a32, l25] of this._recyclingInfoMap) r20.has(a32) || (this._recyclingInfoMap.delete(l25.layer), l25.destroy());
      for (const a32 of i28) this._createLayerView(a32);
      this._refreshCollections();
      const o27 = [(_a = e22 == null ? void 0 : e22.ground) == null ? void 0 : _a.layers, (_b = e22 == null ? void 0 : e22.basemap) == null ? void 0 : _b.baseLayers, (_c = e22 == null ? void 0 : e22.basemap) == null ? void 0 : _c.referenceLayers, e22 == null ? void 0 : e22.layers].filter((e23) => !!e23);
      r20.forEach((e23) => "layers" in e23 && o27.push(e23.layers)), this.addHandles(o27.map((e23) => this._watchUpdatingTracking.addOnCollectionChange(() => e23, this._reschedule)), "collection-change"), this._workPromise.resolve(), this._workPromise = null;
    };
  }
  initialize() {
    this.own([a6(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.map) == null ? void 0 : _b.allLayers;
    }, "change", this._preloadLayerViewModules, { onListenerAdd: this._preloadLayerViewModules }), l3(() => {
      const e21 = this.view, r20 = e21 == null ? void 0 : e21.map;
      return [r20 == null ? void 0 : r20.basemap, r20 == null ? void 0 : r20.ground, r20 == null ? void 0 : r20.layers, e21 == null ? void 0 : e21.ready];
    }, () => this._reschedule(), w4)]), this._preloadLayerViewModules(), this._reschedule();
  }
  destroy() {
    this.clear(), this._watchUpdatingTracking.destroy(), this._map = null, r(this._workPromise) && (this._workPromise.reject(a3()), this._workPromise = null);
  }
  get _layersToLayerViews() {
    const e21 = [["view.map.basemap.baseLayers", "view.basemapView.baseLayerViews"], ["view.map.layers", "view.layerViews"], ["view.map.basemap.referenceLayers", "view.basemapView.referenceLayerViews"]];
    return this.supportsGround && e21.push(["view.map.ground.layers", "view.groundView.layerViews"]), new Map(e21);
  }
  get _rootCollectionNames() {
    return Array.from(this._layersToLayerViews.keys());
  }
  get updating() {
    return r(this._workPromise) || this._watchUpdatingTracking.updating || n(this._layerLayerViewInfoMap, (e21) => !e21.done);
  }
  get updatingRemaining() {
    let e21 = 0;
    for (const r20 of this._layerLayerViewInfoMap.values()) r20.done || ++e21;
    return e21;
  }
  clear() {
    if (!this.destroyed) {
      for (const e21 of this._layerLayerViewInfoMap.values()) e21.destroy();
      this._layerLayerViewInfoMap.clear(), this._refreshCollections();
    }
  }
  async whenLayerView(e21) {
    if (await this._reschedule(), !this._layerLayerViewInfoMap.has(e21)) {
      if (this._recyclingInfoMap.has(e21)) return this._recyclingInfoMap.get(e21).promise;
      throw new s3("view:no-layerview-for-layer", "No layerview has been found for the layer", { layer: e21 });
    }
    return this._layerLayerViewInfoMap.get(e21).promise;
  }
  _refreshCollections() {
    for (const [e21, r20] of this._layersToLayerViews) this._populateLayerViewsOwners(this.get(e21), this.get(r20), this.view);
    this.notifyChange("updating"), this.notifyChange("updatingRemaining");
  }
  _populateLayerViewsOwners(e21, r20, i28) {
    if (!e21 || !r20) return void (r20 && r20.removeAll());
    let t26 = 0;
    for (const s22 of e21) {
      const e22 = this._layerLayerViewInfoMap.get(s22);
      if (!e22 || !e22.layerView) continue;
      const a32 = e22.layerView;
      a32.layer = s22, a32.parent = i28, r20.getItemAt(t26) !== a32 && r20.splice(t26, 0, a32), s22.layers && this._populateLayerViewsOwners(s22.layers, a32.layerViews, a32), t26 += 1;
    }
    t26 < r20.length && r20.splice(t26, r20.length);
  }
  _createLayerView(e21) {
    e21.load().catch(() => {
    }), this.layerViewImporter.hasLayerViewModule(e21) && this.layerViewImporter.importLayerView(e21);
    const r20 = new V3(e21, this.view, this.layerViewImporter);
    r20.promise.then(() => this._refreshCollections(), (r21) => {
      r21 && (j(r21) || "cancelled:layerview-create" === r21.name) || s2.getLogger(this.declaredClass).error(`Failed to create layerview for layer title:'${e21.title ?? "no title"}', id:'${e21.id ?? "no id"}' of type '${e21.type}'.`, { layer: e21, error: r21 }), this._refreshCollections();
    }), this._layerLayerViewInfoMap.set(e21, r20), this.view.ready && r20.start(), this.notifyChange("updating"), this.notifyChange("updatingRemaining");
  }
};
e([y()], L6.prototype, "_workPromise", void 0), e([y({ readOnly: true })], L6.prototype, "_watchUpdatingTracking", void 0), e([y({ readOnly: true })], L6.prototype, "_layersToLayerViews", null), e([y({ readOnly: true })], L6.prototype, "_rootCollectionNames", null), e([y()], L6.prototype, "layerViewImporter", void 0), e([y()], L6.prototype, "supportsGround", void 0), e([y({ readOnly: true })], L6.prototype, "updating", null), e([y({ readOnly: true })], L6.prototype, "updatingRemaining", null), e([y({ constructOnly: true })], L6.prototype, "view", void 0), L6 = e([a2("esri.views.LayerViewManager")], L6);
var M5 = L6;
function I6(e21) {
  return "createLayerView" in e21 && null != e21.createLayerView;
}

// node_modules/@arcgis/core/views/Magnifier.js
var i16 = class extends v3 {
  constructor(o27) {
    super(o27), this.factor = 1.5, this.offset = c2(0, 0), this.position = null, this.size = 120, this.maskUrl = null, this.maskEnabled = true, this.overlayUrl = null, this.overlayEnabled = true, this.visible = true;
  }
  get version() {
    return this.commitProperty("factor"), this.commitProperty("offset"), this.commitProperty("position"), this.commitProperty("visible"), this.commitProperty("size"), this.commitProperty("maskUrl"), this.commitProperty("maskEnabled"), this.commitProperty("overlayUrl"), this.commitProperty("overlayEnabled"), (this._get("version") || 0) + 1;
  }
};
e([y({ type: Number })], i16.prototype, "factor", void 0), e([y({ nonNullable: true })], i16.prototype, "offset", void 0), e([y()], i16.prototype, "position", void 0), e([y({ type: Number, range: { min: 0 } })], i16.prototype, "size", void 0), e([y()], i16.prototype, "maskUrl", void 0), e([y()], i16.prototype, "maskEnabled", void 0), e([y()], i16.prototype, "overlayUrl", void 0), e([y()], i16.prototype, "overlayEnabled", void 0), e([y({ readOnly: true })], i16.prototype, "version", null), e([y({ type: Boolean })], i16.prototype, "visible", void 0), i16 = e([a2("esri.views.Magnifier")], i16);
var p16 = i16;

// node_modules/@arcgis/core/views/3d/support/TextureCollection.js
var n17 = class extends v3 {
  constructor(e21, t26) {
    super({}), this._stage = e21, this._textureRequests = /* @__PURE__ */ new Map(), this._frameTask = (t26 == null ? void 0 : t26.registerTask(R2.TEXTURE_UNLOAD)) ?? D;
  }
  normalizeCtorArgs() {
    return {};
  }
  destroy() {
    super.destroy(), this._frameTask.remove(), this._textureRequests.forEach((e21) => this._releaseTextureRequest(e21)), this._textureRequests.clear();
  }
  get updating() {
    return this._frameTask.updating;
  }
  fromData(e21, t26, r20) {
    const s22 = this.makeUid(e21);
    let o27 = this._textureRequests.get(s22);
    if (!o27) {
      const e22 = t26();
      o27 = { referenceCount: 0, texture: e22, textureAsync: null, abortController: null, onRemove: r20 }, this._stage && (this._stage.add(e22), this._stage.loadImmediate(e22)), this._textureRequests.set(s22, o27);
    }
    return o27.referenceCount++, { uid: s22, texture: o27.texture, release: () => this._release(s22) };
  }
  _release(e21) {
    const t26 = this._textureRequests.get(e21);
    t26 ? (t26.referenceCount < 1 && console.warn("TextureCollection: reference count is < 1 for " + e21), t26.referenceCount--, t26.referenceCount < 1 && this._frameTask.schedule(() => this._releaseNow(e21))) : console.warn(`TextureCollection: texture doesn't exist: '${e21}'`);
  }
  get test() {
    return { textureRequests: this._textureRequests };
  }
  _releaseNow(e21) {
    if (!this._textureRequests) return;
    const t26 = this._textureRequests.get(e21);
    !t26 || t26.referenceCount > 0 || (this._releaseTextureRequest(t26), this._textureRequests.delete(e21));
  }
  _releaseTextureRequest(e21) {
    var _a;
    e21.onRemove && e21.onRemove(), e21.texture ? (_a = this._stage) == null ? void 0 : _a.remove(e21.texture) : e21.abortController && (e21.abortController.abort(), e21.abortController = null);
  }
  makeUid(e21, t26 = null) {
    return r(t26) ? `${e21}.${t26}px` : e21;
  }
};
e([y()], n17.prototype, "_frameTask", void 0), e([y()], n17.prototype, "updating", null), n17 = e([a2("esri.views.3d.support.TextureCollection")], n17);

// node_modules/@arcgis/core/views/input/ViewEvents.js
var r13 = ["click", "double-click", "immediate-click", "immediate-double-click", "hold", "drag", "key-down", "key-up", "pointer-down", "pointer-move", "pointer-up", "pointer-drag", "mouse-wheel", "pointer-enter", "pointer-leave", "gamepad", "focus", "blur"];
var i17 = {};
function o16(t26) {
  return !!i17[t26];
}
function s13(t26) {
  for (const e21 of t26) if (!o16(e21)) return false;
  return true;
}
r13.forEach((t26) => {
  i17[t26] = true;
});
var p17 = class {
  constructor(t26) {
    this._handlers = /* @__PURE__ */ new Map(), this._counter = 0, this._handlerCounts = /* @__PURE__ */ new Map(), this.view = t26, this.inputManager = null;
  }
  connect(t26) {
    t26 && this.disconnect(), this.inputManager = t26, this._handlers.forEach(({ handler: t27, priority: e21 }, a32) => {
      var _a;
      return (_a = this.inputManager) == null ? void 0 : _a.installHandlers(a32, [t27], e21);
    });
  }
  disconnect() {
    this.inputManager && this._handlers.forEach((t26, e21) => {
      var _a;
      return (_a = this.inputManager) == null ? void 0 : _a.uninstallHandlers(e21);
    }), this.inputManager = null;
  }
  destroy() {
    this.disconnect(), this._handlers.clear(), this.view = null;
  }
  on(t26, e21, a32, r20) {
    const i28 = Array.isArray(t26) ? t26 : t26.split(",");
    if (!s13(i28)) return i28.some(o16) && console.error("Error: registering input events and other events on the view at the same time is not supported."), null;
    let p30, l25;
    Array.isArray(e21) ? l25 = e21 : (p30 = e21, l25 = []), "function" == typeof a32 ? p30 = a32 : r20 = a32, r20 = null != r20 ? r20 : P.DEFAULT;
    const u18 = this._createUniqueGroupName(), m24 = new c24(this.view, i28, l25, p30);
    this._handlers.set(u18, { handler: m24, priority: r20 });
    for (const n27 of i28) {
      const t27 = this._handlerCounts.get(n27) || 0;
      this._handlerCounts.set(n27, t27 + 1);
    }
    return this.inputManager && this.inputManager.installHandlers(u18, [m24], r20), { remove: () => this._removeHandler(u18, i28) };
  }
  hasHandler(t26) {
    return !!this._handlerCounts.get(t26);
  }
  _removeHandler(t26, e21) {
    if (this._handlers.has(t26)) {
      this._handlers.delete(t26);
      for (const t27 of e21) {
        const e22 = this._handlerCounts.get(t27);
        void 0 === e22 ? console.error("Trying to remove handler for event that has no handlers registered: ", t27) : 1 === e22 ? this._handlerCounts.delete(t27) : this._handlerCounts.set(t27, e22 - 1);
      }
    }
    this.inputManager && this.inputManager.uninstallHandlers(t26);
  }
  _createUniqueGroupName() {
    return this._counter += 1, `viewEvents_${this._counter}`;
  }
};
var c24 = class extends i10 {
  constructor(t26, e21, a32, n27) {
    super(true), this._latestDragStart = void 0, this.view = t26;
    for (const r20 of e21) switch (r20) {
      case "click":
        this.registerIncoming("click", a32, (t27) => n27(this._wrapClick(t27)));
        break;
      case "double-click":
        this.registerIncoming("double-click", a32, (t27) => n27(this._wrapDoubleClick(t27)));
        break;
      case "immediate-click":
        this.registerIncoming("immediate-click", a32, (t27) => n27(this._wrapImmediateClick(t27)));
        break;
      case "immediate-double-click":
        this.registerIncoming("immediate-double-click", a32, (t27) => n27(this._wrapImmediateDoubleClick(t27)));
        break;
      case "hold":
        this.registerIncoming("hold", a32, (t27) => n27(this._wrapHold(t27)));
        break;
      case "drag":
        this.registerIncoming("drag", a32, (t27) => {
          const e22 = this._wrapDrag(t27);
          e22 && n27(e22);
        });
        break;
      case "key-down":
        this.registerIncoming("key-down", a32, (t27) => n27(this._wrapKeyDown(t27)));
        break;
      case "key-up":
        this.registerIncoming("key-up", a32, (t27) => n27(this._wrapKeyUp(t27)));
        break;
      case "pointer-down":
        this.registerIncoming("pointer-down", a32, (t27) => n27(this._wrapPointer(t27, "pointer-down")));
        break;
      case "pointer-move":
        this.registerIncoming("pointer-move", a32, (t27) => n27(this._wrapPointer(t27, "pointer-move")));
        break;
      case "pointer-up":
        this.registerIncoming("pointer-up", a32, (t27) => n27(this._wrapPointer(t27, "pointer-up")));
        break;
      case "pointer-drag":
        this.registerIncoming("pointer-drag", a32, (t27) => n27(this._wrapPointerDrag(t27)));
        break;
      case "mouse-wheel":
        this.registerIncoming("mouse-wheel", a32, (t27) => n27(this._wrapMouseWheel(t27)));
        break;
      case "pointer-enter":
        this.registerIncoming("pointer-enter", a32, (t27) => n27(this._wrapPointer(t27, "pointer-enter")));
        break;
      case "pointer-leave":
        this.registerIncoming("pointer-leave", a32, (t27) => n27(this._wrapPointer(t27, "pointer-leave")));
        break;
      case "gamepad":
        this.registerIncoming("gamepad", a32, (t27) => {
          n27(this._wrapGamepad(t27));
        });
        break;
      case "focus":
        this.registerIncoming("focus", a32, (t27) => {
          n27(this._wrapFocus(t27));
        });
        break;
      case "blur":
        this.registerIncoming("blur", a32, (t27) => {
          n27(this._wrapBlur(t27));
        });
    }
  }
  _wrapFocus(t26) {
    return { type: "focus", timestamp: t26.timestamp, native: t26.data.native, cancelable: t26.cancelable, stopPropagation: () => t26.stopPropagation(), async: (e21) => t26.async(e21), preventDefault: () => t26.preventDefault() };
  }
  _wrapBlur(t26) {
    return { type: "blur", timestamp: t26.timestamp, native: t26.data.native, cancelable: t26.cancelable, stopPropagation: () => t26.stopPropagation(), async: (e21) => t26.async(e21), preventDefault: () => t26.preventDefault() };
  }
  _wrapClick(t26) {
    const { pointerType: a32, button: n27, buttons: r20, x: i28, y: o27, native: s22, eventId: p30 } = t26.data, { cancelable: c34, timestamp: l25 } = t26;
    return { type: "click", pointerType: a32, button: n27, buttons: r20, x: i28, y: o27, native: s22, timestamp: l25, screenPoint: c2(i28, o27), mapPoint: this._getMapPoint(i28, o27), eventId: p30, cancelable: c34, stopPropagation: () => t26.stopPropagation(), async: (e21) => t26.async(e21), preventDefault: () => t26.preventDefault() };
  }
  _wrapDoubleClick(t26) {
    const { pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, eventId: s22 } = t26.data, { cancelable: p30, timestamp: c34 } = t26;
    return { type: "double-click", pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, timestamp: c34, mapPoint: this._getMapPoint(r20, i28), eventId: s22, cancelable: p30, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapImmediateClick(t26) {
    const { pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, eventId: s22 } = t26.data, p30 = o27.pointerId, { cancelable: c34, timestamp: l25 } = t26;
    return { type: "immediate-click", pointerId: p30, pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, timestamp: l25, mapPoint: this._getMapPoint(r20, i28), eventId: s22, cancelable: c34, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapImmediateDoubleClick(t26) {
    const { pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, eventId: s22 } = t26.data, p30 = o27.pointerId, { cancelable: c34, timestamp: l25 } = t26;
    return { type: "immediate-double-click", pointerId: p30, pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, timestamp: l25, mapPoint: this._getMapPoint(r20, i28), eventId: s22, cancelable: c34, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapHold(t26) {
    const { pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27 } = t26.data, { cancelable: s22, timestamp: p30 } = t26;
    return { type: "hold", pointerType: e21, button: a32, buttons: n27, x: r20, y: i28, native: o27, timestamp: p30, mapPoint: this._getMapPoint(r20, i28), cancelable: s22, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _getMapPoint(t26, a32) {
    return this.view.toMap(c2(t26, a32), { exclude: [] });
  }
  _wrapDrag(e21) {
    const a32 = e21.data, { x: n27, y: r20 } = a32.center, { action: i28, pointerType: o27, button: s22 } = a32;
    if ("start" === i28 && (this._latestDragStart = a32), !this._latestDragStart) return;
    const p30 = a32.pointer.native, c34 = a32.buttons, { cancelable: l25, timestamp: u18 } = e21, m24 = { x: this._latestDragStart.center.x, y: this._latestDragStart.center.y };
    return "end" === i28 && (this._latestDragStart = void 0), { type: "drag", action: i28, x: n27, y: r20, origin: m24, pointerType: o27, button: s22, buttons: c34, radius: a32.radius, angle: b2(a32.angle), native: p30, timestamp: u18, cancelable: l25, stopPropagation: () => e21.stopPropagation(), async: (t26) => e21.async(t26), preventDefault: () => e21.preventDefault() };
  }
  _wrapKeyDown(t26) {
    const { key: e21, repeat: a32, native: n27 } = t26.data, { cancelable: r20, timestamp: i28 } = t26;
    return { type: "key-down", key: e21, repeat: a32, native: n27, timestamp: i28, cancelable: r20, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapKeyUp(t26) {
    const { key: e21, native: a32 } = t26.data, { cancelable: n27, timestamp: r20 } = t26;
    return { type: "key-up", key: e21, native: a32, timestamp: r20, cancelable: n27, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapPointer(t26, e21) {
    const { x: a32, y: n27, button: r20, buttons: i28, native: o27, eventId: s22 } = t26.data, p30 = o27.pointerId, c34 = o27.pointerType, { cancelable: l25, timestamp: u18 } = t26;
    return { type: e21, x: a32, y: n27, pointerId: p30, pointerType: c34, button: r20, buttons: i28, native: o27, timestamp: u18, eventId: s22, cancelable: l25, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapPointerDrag(t26) {
    const { x: e21, y: a32, buttons: n27, native: r20, eventId: i28 } = t26.data.currentEvent, { button: o27 } = t26.data.startEvent, s22 = t26.data.startEvent.native.pointerId, p30 = t26.data.startEvent.native.pointerType, c34 = t26.data.action, l25 = { x: t26.data.startEvent.x, y: t26.data.startEvent.y }, { cancelable: u18, timestamp: m24 } = t26;
    return { type: "pointer-drag", x: e21, y: a32, pointerId: s22, pointerType: p30, button: o27, buttons: n27, action: c34, origin: l25, native: r20, timestamp: m24, eventId: i28, cancelable: u18, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapMouseWheel(t26) {
    const { cancelable: e21, data: a32, timestamp: n27 } = t26, { x: r20, y: i28, deltaY: o27, native: s22 } = a32;
    return { type: "mouse-wheel", x: r20, y: i28, deltaY: o27, native: s22, timestamp: n27, cancelable: e21, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
  _wrapGamepad(t26) {
    const { action: e21, state: a32, device: n27 } = t26.data, { cancelable: r20, timestamp: i28 } = t26, { buttons: o27, axes: s22 } = a32;
    return { type: "gamepad", device: n27, timestamp: i28, action: e21, buttons: o27, axes: s22, cancelable: r20, stopPropagation: () => t26.stopPropagation(), async: (e22) => t26.async(e22), preventDefault: () => t26.preventDefault() };
  }
};

// node_modules/@arcgis/core/views/interactive/interactiveToolUtils.js
function o17(t26) {
  return [t26.on("before-add", (o27) => {
    const i28 = o27.item;
    if (null == i28 || t26.includes(i28)) return s2.getLogger("esri.views.interactive.interactiveToolUtils").warn("Tool is either already in the list of tools or tool is `null`. Not adding tool."), void o27.preventDefault();
    i28.onAdd();
  }), t26.on("after-remove", (e21) => {
    const t27 = e21.item;
    t27.active && (t27.view.activeTool = null), t27.destroy();
  })];
}
function i18(e21) {
  return e21.visible && null != e21.getEditableFlag && e21.getEditableFlag(o7.USER) && e21.getEditableFlag(o7.MANAGER);
}

// node_modules/@arcgis/core/views/interactive/ToolViewManagerManipulatorState.js
var c25 = class {
  constructor() {
    this._pointerLocations = /* @__PURE__ */ new Map(), this._hoveredManipulators = /* @__PURE__ */ new Map(), this._grabbedManipulators = /* @__PURE__ */ new Map(), this._draggedManipulators = /* @__PURE__ */ new Map(), this._stopDrag = false, this._revertToNullActiveTool = false, this._cursor = null;
  }
  get cursor() {
    return this._cursor;
  }
  handleInputEvent(t26, e21) {
    const s22 = () => t26.stopPropagation();
    switch (t26.type) {
      case "pointer-move":
        u11(t26.pointerType) && this._pointerLocations.set(t26.pointerId, { x: t26.x, y: t26.y, pointerType: t26.pointerType });
        break;
      case "drag":
        this._grabbedManipulators.size > 0 && (this._stopDrag = true), this._stopDrag && (s22(), "end" === t26.action && (this._stopDrag = false));
        break;
      case "pointer-down": {
        if (!h12(t26)) break;
        const o27 = n9(t26), a32 = this._intersect(o27, t26.pointerType, e21.forEachTool);
        if (t(a32)) break;
        const n27 = a32.manipulator, p30 = a32.tool;
        !(r(n27) && r(p30) && n27.interactive) || n27.grabbable && n27.grabbableForEvent(t26) || !n27.grabbing || n27.dragging || this._ungrabManipulatorBeforeDragging(n27, t26, e21), r(n27) && r(p30) && n27.interactive && n27.grabbable && n27.grabbableForEvent(t26) && !n27.grabbing && (this._grabbedManipulators.set(t26.pointerId, { manipulator: n27, tool: p30, start: o27, pointerType: t26.pointerType }), 1 === this._grabbedManipulators.size && t(e21.activeTool) && (this._revertToNullActiveTool = true, e21.setActiveTool(a32.tool)), n27.grabbing = true, n27.events.emit("grab-changed", { action: "start", pointerType: t26.pointerType, screenPoint: o27 }), s22());
        break;
      }
      case "pointer-up":
        this._draggedManipulators.has(t26.pointerId) || this._handlePointerEnd(t26, e21);
        break;
      case "pointer-drag": {
        if (!h12(t26)) break;
        const r20 = this._grabbedManipulators.get(t26.pointerId), p30 = o(r20, ({ manipulator: t27 }) => t27), c34 = o(r20, ({ tool: t27 }) => t27);
        if (t(p30) || t(c34)) break;
        const u18 = n9(t26);
        u18.x = a4(u18.x, 0, e21.view.width), u18.y = a4(u18.y, 0, e21.view.height);
        const d15 = e2(r20).start, g16 = this._draggedManipulators.get(t26.pointerId);
        switch (t26.action) {
          case "start":
          case "update":
            "update" !== t26.action && 1 !== this._grabbedManipulators.size || (p30.dragging = true, g16 ? p30.events.emit("drag", { action: "update", start: d15, screenPoint: u18 }) : p30.events.emit("drag", { action: "start", start: d15, screenPoint: u18, pointerType: t26.pointerType }), this._draggedManipulators.set(t26.pointerId, { tool: c34, manipulator: p30, start: d15 }));
            break;
          case "end":
            p30.dragging = false, g16 && p30.events.emit("drag", { action: "end", start: d15, screenPoint: u18 }), this._draggedManipulators.delete(t26.pointerId), this._handlePointerEnd(t26, e21);
        }
        s22();
        break;
      }
      case "immediate-click": {
        const o27 = n9(t26), a32 = this._intersect(o27, t26.pointerType, e21.forEachTool);
        if (d10(t26) || e21.forEachTool((t27) => {
          if ((!r(a32) || a32.tool !== t27 || t27.automaticManipulatorSelection) && t27.manipulators) {
            let e22 = false;
            t27.manipulators.forEach(({ manipulator: t28 }) => {
              t28.selected && (t28.selected = false, e22 = true);
            }), e22 && t27.onManipulatorSelectionChanged && t27.onManipulatorSelectionChanged();
          }
        }), t(a32)) break;
        const { manipulator: n27, tool: p30 } = a32;
        if (!n27.interactive) break;
        n27.selectable && p30.automaticManipulatorSelection && (n27.selected = !n27.selected, p30.onManipulatorSelectionChanged && p30.onManipulatorSelectionChanged());
        const c34 = t26.native.shiftKey;
        n27.events.emit("immediate-click", { screenPoint: o27, button: t26.button, pointerType: t26.pointerType, shiftKey: c34, stopPropagation: s22 });
        break;
      }
      case "click": {
        const o27 = n9(t26), a32 = this._intersect(o27, t26.pointerType, e21.forEachTool), n27 = r(a32) ? a32.manipulator : null;
        if (t(n27) || !n27.interactive) break;
        const p30 = t26.native.shiftKey;
        n27.events.emit(t26.type, { screenPoint: o27, button: t26.button, pointerType: t26.pointerType, shiftKey: p30 }), s22();
        break;
      }
      case "double-click": {
        const o27 = n9(t26), a32 = this._intersect(o27, t26.pointerType, e21.forEachTool), n27 = r(a32) ? a32.manipulator : null;
        if (t(n27) || !n27.interactive) break;
        const p30 = t26.native.shiftKey;
        n27.events.emit("double-click", { screenPoint: o27, button: t26.button, pointerType: t26.pointerType, shiftKey: p30, stopPropagation: s22 });
        break;
      }
      case "immediate-double-click": {
        const o27 = n9(t26), a32 = this._intersect(o27, t26.pointerType, e21.forEachTool), n27 = r(a32) ? a32.manipulator : null;
        if (t(n27) || !n27.interactive) break;
        const p30 = t26.native.shiftKey;
        n27.events.emit("immediate-double-click", { screenPoint: o27, button: t26.button, pointerType: t26.pointerType, shiftKey: p30, stopPropagation: s22 });
        break;
      }
    }
    this._onFocusChange(e21.forEachTool);
  }
  _ungrabManipulatorBeforeDragging(t26, e21, o27) {
    t26.grabbing = false, t26.events.emit("grab-changed", { action: "end", pointerType: e21.pointerType, screenPoint: n9(e21) }), this._grabbedManipulators.forEach(({ manipulator: e22 }, o28) => {
      e22 === t26 && this._grabbedManipulators.delete(o28);
    }), this._afterManipulatorUngrab(o27.setActiveTool);
  }
  _handlePointerEnd(t26, e21) {
    const o27 = o(this._grabbedManipulators.get(t26.pointerId), ({ manipulator: t27 }) => t27);
    t(o27) || o27.grabbing && (o27.grabbing = false, o27.events.emit("grab-changed", { action: "end", pointerType: t26.pointerType, screenPoint: n9(t26) }), this._grabbedManipulators.delete(t26.pointerId), this._afterManipulatorUngrab(e21.setActiveTool));
  }
  _cursorFromMap(t26) {
    let o27 = null;
    return n(t26, ({ manipulator: t27 }) => !(t(t27) || !t27.interactive) && (t27.grabbing && t27.grabCursor ? (o27 = t27.grabCursor, true) : !!t27.cursor && (o27 = t27.cursor, true))), o27;
  }
  _onFocusChange(t26) {
    this._updateCursor(), this._updateFocusedManipulatorTools(t26);
  }
  _updateCursor() {
    this._grabbedManipulators.size > 0 ? this._cursor = this._cursorFromMap(this._grabbedManipulators) || "grabbing" : this._hoveredManipulators.size > 0 ? this._cursor = this._cursorFromMap(this._hoveredManipulators) || "pointer" : this._cursor = null;
  }
  _updateFocusedManipulatorTools(e21) {
    const o27 = /* @__PURE__ */ new Set(), i28 = /* @__PURE__ */ new Set();
    this._grabbedManipulators.forEach(({ tool: t26 }) => {
      o27.add(t26);
    }), this._hoveredManipulators.forEach(({ tool: t26 }) => {
      i28.add(t26);
    }), e21((e22) => {
      e22.hasGrabbedManipulators = o27.has(e22), e22.hasHoveredManipulators = i28.has(e22);
      const a32 = this._grabbedManipulators.values(), n27 = o2(a32, ({ tool: t26 }) => t26 === e22);
      e22.firstGrabbedManipulator = r(n27) ? n27.manipulator : null;
    });
  }
  clearPointers(t26, { forEachTool: e21, setActiveTool: o27 }, r20 = true, a32) {
    const n27 = (e22, o28) => e22 === t26 && (t(a32) || a32 === o28);
    this._grabbedManipulators.forEach(({ tool: t27, manipulator: e22, pointerType: o28 }, r21) => {
      n27(t27, e22) && (this._grabbedManipulators.delete(r21), e22.grabbing = false, e22.events.emit("grab-changed", { action: "end", screenPoint: null, pointerType: o28 }));
    }), this._draggedManipulators.forEach(({ tool: t27, manipulator: e22 }, o28) => {
      n27(t27, e22) && (this._draggedManipulators.delete(o28), e22.dragging = false, e22.events.emit("drag", { action: "cancel" }));
    }), r20 && this._hoveredManipulators.forEach(({ tool: t27, manipulator: e22 }, o28) => {
      n27(t27, e22) && (this._hoveredManipulators.delete(o28), e22.hovering = false);
    }), this._afterManipulatorUngrab(o27), this._onFocusChange(e21);
  }
  _intersect(t26, e21, o27) {
    let r20 = null;
    return o27((o28) => {
      if (null == o28.manipulators || !i18(o28)) return false;
      const a32 = o28.manipulators.intersect(t26, e21);
      return !t(a32) && (r20 = { tool: o28, manipulator: a32 }, true);
    }), r20;
  }
  updateHoveredStateFromKnownPointers(t26) {
    this._pointerLocations.forEach((e21, o27) => {
      this._updateHoveredStateForPointerAtScreenPosition(c2(e21.x, e21.y), o27, e21.pointerType, t26);
    });
  }
  handleHoverEvent(t26, e21) {
    "pointer-up" !== t26.type && "immediate-click" !== t26.type && "pointer-move" !== t26.type || !u11(t26.pointerType) || this._updateHoveredStateForPointerAtScreenPosition(n9(t26), t26.pointerId, t26.pointerType, e21);
  }
  _updateHoveredStateForPointerAtScreenPosition(t26, e21, o27, i28) {
    let n27 = this._intersect(t26, o27, i28);
    const s22 = o(this._hoveredManipulators.get(e21), ({ manipulator: t27 }) => t27);
    r(n27) && !n27.manipulator.interactive && (n27 = null), r(n27) && s22 === n27.manipulator || (r(s22) && (s22.hovering = false), r(n27) ? (n27.manipulator.hovering = true, this._hoveredManipulators.set(e21, n27)) : this._hoveredManipulators.delete(e21), this._onFocusChange(i28));
  }
  _afterManipulatorUngrab(t26) {
    0 === this._grabbedManipulators.size && this._revertToNullActiveTool && (t26(null), this._revertToNullActiveTool = false);
  }
};
function u11(t26) {
  return "mouse" === t26;
}
function h12(t26) {
  return "mouse" !== t26.pointerType || 0 === t26.button;
}
function d10(t26) {
  return !!t26.native.shiftKey;
}

// node_modules/@arcgis/core/views/ToolViewManager.js
var E5 = "attached";
var g12 = "tools";
var w16 = class extends d {
  constructor(t26) {
    super(t26), this._manipulatorState = new c25(), this.tools = new j2(), this.cursor = null, this._forEachTool = (t27) => {
      for (const o27 of this.tools.items) if (t27(o27)) return;
    };
  }
  initialize() {
    this.handles.add([this.view.on(r13, (t26) => {
      this._handleInputEvent(t26);
    }, P.TOOL), ...o17(this.tools), this.tools.on("before-add", ({ item: t26 }) => {
      this._updateToolEditableFlag(t26);
    }), this.tools.on("before-remove", ({ item: t26 }) => {
      this._manipulatorState.clearPointers(t26, this._manipulatorStateEventArgs), this._updateCursor();
    }), this.tools.on("change", () => {
      this._refreshToolWatchers();
    })]);
  }
  destroy() {
    this.detach(), this.handles.removeAll();
  }
  get _manipulatorStateEventArgs() {
    return { forEachTool: this._forEachTool, activeTool: this.activeTool, setActiveTool: (t26) => {
      this.activeTool = t26;
    }, view: this.view };
  }
  set activeTool(t26) {
    if (r(t26) && !this.view.ready) return void s2.getLogger(this.declaredClass).error("Cannot set active tool while view is not ready.");
    if (t26 === this.activeTool) return;
    const o27 = this.activeTool;
    this._set("activeTool", t26), r(o27) && o27.deactivate(), r(t26) && t26.activate(), this._removeIncompleteTools(t26);
    for (const e21 of this.tools) {
      this._updateToolEditableFlag(e21);
      const t27 = i18(e21);
      !t(this.activeTool) && t27 || this._manipulatorState.clearPointers(e21, this._manipulatorStateEventArgs, !t27);
    }
    this._updateCursor();
  }
  get updating() {
    var _a;
    return this.updatingHandles.updating || this.tools.some((t26) => t26.updating) || (((_a = this.textures) == null ? void 0 : _a.updating) ?? false);
  }
  attach() {
    var _a;
    "3d" === this.view.type ? (this._set("textures", new n17(this.view._stage, this.view.resourceController.scheduler)), this.handles.add([l3(() => {
      const { state: t26 } = this.view;
      return "camera" in t26 && t26.camera;
    }, () => this._forEachManipulator((t26) => t26.onViewChange())), (_a = this.view.elevationProvider) == null ? void 0 : _a.on("elevation-change", (t26) => this._forEachManipulator((o27) => o27.onElevationChange(t26))), n2(() => this._set("textures", a(this.textures)))], E5)) : this.handles.add(l3(() => this.view.extent, () => this._forEachManipulator((t26) => t26.onViewChange())));
  }
  detach() {
    r(this.activeTool) && (this.activeTool = null), this.tools.removeAll(), this.handles.remove(E5);
  }
  _forEachManipulator(t26) {
    this._forEachTool((o27) => {
      o27.manipulators && o27.manipulators.forEach(({ manipulator: e21 }) => t26(e21, o27));
    });
  }
  _handleInputEvent(t26) {
    let o27 = false;
    const e21 = { ...t26, stopPropagation: () => {
      o27 = true, t26.stopPropagation();
    } };
    r(this.activeTool) ? this.activeTool.handleInputEvent && this.activeTool.handleInputEvent(e21) : this._forEachTool((t27) => {
      !o27 && t27.visible && t27.handleInputEvent(e21);
    }), !o27 && "key-down" === t26.type && "Escape" === t26.key && this.activeTool && (t26.stopPropagation(), this.activeTool = null), this._manipulatorState.handleInputEvent(e21, this._manipulatorStateEventArgs), !o27 && r(this.activeTool) && this.activeTool.handleInputEventAfter(e21), this._manipulatorState.handleHoverEvent(e21, this._forEachTool), this._updateCursor();
  }
  _refreshToolWatchers() {
    this.handles.remove(g12), this._forEachTool((t26) => {
      if (t26 instanceof v3) {
        const o27 = l3(() => [t26.cursor, t26.visible, t26.editable], () => {
          i18(t26) || this._manipulatorState.clearPointers(t26, this._manipulatorStateEventArgs), this._updateCursor();
        });
        this.handles.add(o27, g12);
      }
      t26.manipulators && this.handles.add([t26.manipulators.on("after-remove", (o27) => {
        this._manipulatorState.clearPointers(t26, this._manipulatorStateEventArgs, true, o27.item.manipulator);
      }), t26.manipulators.on("change", () => {
        this._manipulatorState.updateHoveredStateFromKnownPointers(this._forEachTool), this._updateCursor();
      })], g12);
    }), this._manipulatorState.updateHoveredStateFromKnownPointers(this._forEachTool), this._updateCursor();
  }
  _updateToolEditableFlag(t26) {
    var _a;
    (_a = t26.setEditableFlag) == null ? void 0 : _a.call(t26, o7.MANAGER, t(this.activeTool) || t26 === this.activeTool);
  }
  _updateCursor() {
    let t26 = this._manipulatorState.cursor;
    t(t26) && this._forEachTool((o27) => !(!r(o27.cursor) || !o27.visible) && (t26 = o27.cursor, true)), this._get("cursor") !== t26 && this._set("cursor", t26);
  }
  _removeIncompleteTools(t26) {
    this.tools.filter((o27) => (t(t26) || o27 !== t26) && !o27.created && o27.removeIncompleteOnCancel).forEach((t27) => {
      this.tools.remove(t27);
    });
  }
};
e([y({ constructOnly: true, nonNullable: true })], w16.prototype, "view", void 0), e([y({ readOnly: true, nonNullable: true })], w16.prototype, "textures", void 0), e([y({ value: null })], w16.prototype, "activeTool", null), e([y({ readOnly: true, type: j2 })], w16.prototype, "tools", void 0), e([y({ readOnly: true })], w16.prototype, "cursor", void 0), e([y({ readOnly: true })], w16.prototype, "updating", null), w16 = e([a2("esri.views.ToolViewManager")], w16);
var y12 = w16;

// node_modules/@arcgis/core/views/input/gamepad/GamepadInputDevice.js
var n18 = class extends v3 {
  constructor(e21) {
    super(), this.nativeIndex = null, this._detectedDeviceType = "unknown", "standard" === e21.mapping ? this._detectedDeviceType = "standard" : o18.test(e21.id) ? this._detectedDeviceType = "spacemouse" : this._detectedDeviceType = "unknown", this.nativeIndex = e21.index;
  }
  get native() {
    const e21 = navigator.getGamepads ? navigator.getGamepads() : [];
    return null != this.nativeIndex && this.nativeIndex < e21.length ? e21[this.nativeIndex] : null;
  }
  get deviceType() {
    return this._detectedDeviceType;
  }
  get axisThreshold() {
    return i19[this.deviceType];
  }
};
e([y({ nonNullable: true, readOnly: true })], n18.prototype, "nativeIndex", void 0), e([y({ type: String, readOnly: true })], n18.prototype, "deviceType", null), e([y({ type: Number, readOnly: true })], n18.prototype, "axisThreshold", null), n18 = e([a2("esri.views.input.gamepad.GamepadInputDevice")], n18);
var o18 = new RegExp("^(3dconnexion|space(mouse|navigator|pilot|explorer))", "i");
var i19 = { standard: 0.15, spacemouse: 0.025, unknown: 0 };
var a17 = n18;

// node_modules/@arcgis/core/views/input/gamepad/GamepadSettings.js
var c26 = class extends v3 {
  constructor(...e21) {
    super(...e21), this.devices = new j2(), this.enabledFocusMode = "document";
  }
};
e([y({ type: j2.ofType(a17), readOnly: true })], c26.prototype, "devices", void 0), e([y({ type: ["document", "view", "none"] })], c26.prototype, "enabledFocusMode", void 0), c26 = e([a2("esri.views.input.gamepad.GamepadSettings")], c26);
var i20 = c26;

// node_modules/@arcgis/core/views/input/Input.js
var p18 = class extends v3 {
  constructor() {
    super(...arguments), this.gamepad = new i20();
  }
};
e([y({ readOnly: true })], p18.prototype, "gamepad", void 0), p18 = e([a2("esri.views.input.Input")], p18);
var a18 = p18;

// node_modules/@arcgis/core/views/navigation/gamepad/GamepadSettings.js
var s14 = class extends v3 {
  constructor(o27) {
    super(o27), this.enabled = true, this.device = null, this.mode = "pan", this.tiltDirection = "forward-down", this.velocityFactor = 1;
  }
};
e([y({ type: Boolean, nonNullable: true })], s14.prototype, "enabled", void 0), e([y({ type: a17 })], s14.prototype, "device", void 0), e([y({ type: ["pan", "zoom"], nonNullable: true })], s14.prototype, "mode", void 0), e([y({ type: ["forward-down", "forward-up"], nonNullable: true })], s14.prototype, "tiltDirection", void 0), e([y({ type: Number, nonNullable: true })], s14.prototype, "velocityFactor", void 0), s14 = e([a2("esri.views.navigation.gamepad.GamepadSettings")], s14);
var a19 = s14;

// node_modules/@arcgis/core/views/navigation/Navigation.js
var a20 = class extends v3 {
  constructor(o27) {
    super(o27), this.browserTouchPanEnabled = true, this.gamepad = new a19(), this.momentumEnabled = true, this.mouseWheelZoomEnabled = true;
  }
};
e([y({ type: Boolean })], a20.prototype, "browserTouchPanEnabled", void 0), e([y({ type: a19, nonNullable: true })], a20.prototype, "gamepad", void 0), e([y({ type: Boolean })], a20.prototype, "momentumEnabled", void 0), e([y({ type: Boolean })], a20.prototype, "mouseWheelZoomEnabled", void 0), a20 = e([a2("esri.views.navigation.Navigation")], a20);
var p19 = a20;

// node_modules/@arcgis/core/views/support/projectionUtils.js
var n19;
var s15 = null;
async function c27(r20) {
  s15 || (s15 = import("./geometryServiceUtils-CONSLBP6.js").then((e21) => n19 = e21)), await s15, f(r20);
}
async function p20(e21, s22, a32, m24) {
  if (!e21) return null;
  const l25 = e21.spatialReference;
  return en() || An(l25, s22) ? rn(e21, s22) : n19 ? n19.projectGeometry(e21, s22, a32, m24) : (await Promise.race([c27(m24), tn(m24)]), p20(e21, s22, a32, m24));
}

// node_modules/@arcgis/core/views/support/DefaultsFromMap.js
var h13 = class extends v3 {
  constructor(e21) {
    super(e21), this.required = { tileInfo: false, heightModelInfo: false, extent: false }, this.defaultSpatialReference = null, this.userSpatialReference = null, this.sourcePreloadCount = 10, this.priorityCollection = null, this.requiresExtentInSpatialReference = true, this.suspended = false, this._projectExtentTask = { task: null, input: null, output: null, spatialReference: null };
  }
  destroy() {
    this._projectExtentTask.task && (this._projectExtentTask.task = w(this._projectExtentTask.task)), this._set("map", null);
  }
  get ready() {
    return !this._spatialReferenceTask.updating && !this._tileInfoTask.updating && !this._extentTask.updating;
  }
  get heightModelInfoReady() {
    return !this._heightModelInfoTask.updating;
  }
  get spatialReference() {
    return r(this.userSpatialReference) ? this.userSpatialReference : e2(this._spatialReferenceTask.spatialReference);
  }
  get extent() {
    return e2(this._extentTask.extent);
  }
  get heightModelInfo() {
    return e2(this._heightModelInfoTask.heightModelInfo);
  }
  get vcsWkid() {
    return e2(this._heightModelInfoTask.vcsWkid);
  }
  get latestVcsWkid() {
    return e2(this._heightModelInfoTask.latestVcsWkid);
  }
  get viewingMode() {
    return t(this.userSpatialReference) || this.userSpatialReference.equals(e2(this._spatialReferenceTask.spatialReference)) ? e2(this._spatialReferenceTask.viewingMode) : null;
  }
  get tileInfo() {
    return e2(this._tileInfoTask.tileInfo);
  }
  get mapCollections() {
    var _a, _b, _c, _d;
    const e21 = (_a = this.map) == null ? void 0 : _a.call(this), t26 = [];
    return r(this.priorityCollection) && t26.push(this.priorityCollection), t26.push({ parent: e21 == null ? void 0 : e21.basemap, layers: (_b = e21 == null ? void 0 : e21.basemap) == null ? void 0 : _b.baseLayers }, { layers: e21 == null ? void 0 : e21.layers }, { parent: e21 == null ? void 0 : e21.ground, layers: (_c = e21 == null ? void 0 : e21.ground) == null ? void 0 : _c.layers }, { parent: e21 == null ? void 0 : e21.basemap, layers: (_d = e21 == null ? void 0 : e21.basemap) == null ? void 0 : _d.referenceLayers }), t26;
  }
  get _allLayers() {
    return this._collectLayers(this.mapCollections);
  }
  get _spatialReferenceTask() {
    if (this.suspended) return this._get("_spatialReferenceTask") ?? { updating: false };
    const { layers: e21, updating: t26 } = this._allLayers;
    let a32 = null;
    for (const i28 of e21) {
      const e22 = this._getSupportedSpatialReferences(i28);
      if (e22.length > 0) {
        const t27 = this._narrowDownSpatialReferenceCandidates(a32, e22);
        r(t27) && (a32 = t27);
      }
      if (r(a32) && 1 === a32.length) break;
    }
    if (t26 && (t(a32) || 1 !== a32.length)) return { updating: true };
    const n27 = this._pickSpatialReferenceCandidate(a32);
    return { spatialReference: r(n27) ? n27.spatialReference : null, viewingMode: r(n27) ? n27.viewingMode : null, updating: false };
  }
  get _tileInfoTask() {
    var _a, _b, _c, _d, _e2, _f, _g;
    if (!this.required.tileInfo) return this._get("_tileInfoTask") ?? { updating: false };
    if (!this.spatialReference) return { updating: this._spatialReferenceTask.updating };
    const { layers: e21, updating: t26 } = this._collectLayers([{ parent: (_b = (_a = this.map) == null ? void 0 : _a.call(this)) == null ? void 0 : _b.basemap, layers: (_e2 = (_d = (_c = this.map) == null ? void 0 : _c.call(this)) == null ? void 0 : _d.basemap) == null ? void 0 : _e2.baseLayers }, { layers: (_g = (_f = this.map) == null ? void 0 : _f.call(this)) == null ? void 0 : _g.layers }]);
    if (e21 && e21.length > 0 && "tileInfo" in e21[0]) {
      const t27 = e21[0].tileInfo;
      return { tileInfo: t27 && t27.spatialReference.equals(this.spatialReference) ? t27 : null, updating: false };
    }
    return { updating: t26 };
  }
  get _heightModelInfoTask() {
    var _a, _b, _c;
    if (!this.required.heightModelInfo || this.suspended && ((_a = this._get("_heightModelInfoTask")) == null ? void 0 : _a.heightModelInfo)) return this._get("_heightModelInfoTask") ?? { updating: false };
    const { layers: e21, updating: t26 } = this._allLayers;
    for (const a32 of e21) if (f5(a32)) {
      const e22 = h4(a32);
      if (e22) return { heightModelInfo: e22, vcsWkid: (_b = a32.spatialReference) == null ? void 0 : _b.vcsWkid, latestVcsWkid: (_c = a32.spatialReference) == null ? void 0 : _c.latestVcsWkid, updating: false };
    }
    return { updating: t26 };
  }
  get _extentCandidatesTask() {
    if (this.suspended || !this.required.extent) return this._get("_extentCandidatesTask") ?? { updating: false };
    if (!this.spatialReference) return { updating: this._spatialReferenceTask.updating };
    const e21 = this._allLayers, t26 = e21.updating, a32 = [];
    for (const n27 of e21.layers) {
      const e22 = "fullExtents" in n27 && n27.fullExtents || (r(n27.fullExtent) ? [n27.fullExtent] : []), t27 = this.requiresExtentInSpatialReference ? null : e22[0], i28 = e22.find((e23) => e23.spatialReference.equals(this.spatialReference)) ?? t27;
      if (i28) return { candidates: [{ extent: i28, layer: n27 }], updating: false };
      if (this._getSupportedSpatialReferences(n27).length > 0) for (const s22 of e22) a32.push({ extent: s22, layer: n27 });
    }
    return { candidates: a32, updating: t26 };
  }
  get _extentTask() {
    const { candidates: e21, updating: t26 } = this._extentCandidatesTask;
    if (t26) return { updating: t26 };
    if (t(e21) || 0 === e21.length) return { updating: false };
    if (!this.spatialReference) return { updating: this._spatialReferenceTask.updating };
    const i28 = this._pickExtentCandidate(e21), o27 = this.spatialReference;
    return i28.extent.equals(this._projectExtentTask.input) && o27.equals(this._projectExtentTask.spatialReference) ? { extent: this._projectExtentTask.output, updating: r(this._projectExtentTask.task) && !this._projectExtentTask.task.finished } : (r(this._projectExtentTask.task) && (this._projectExtentTask.task = w(this._projectExtentTask.task)), this._projectExtentTask = { input: i28.extent.clone(), output: null, spatialReference: o27.clone(), task: j4(async (e22) => {
      try {
        const t27 = await p20(i28.extent, o27, i28.layer.portalItem, e22);
        this._projectExtentTask = { ...this._projectExtentTask, task: null, output: t27 };
      } catch (t27) {
        if (p3(e22)) return;
        this._projectExtentTask = { ...this._projectExtentTask, task: null };
      }
    }) }, { updating: true });
  }
  _narrowDownSpatialReferenceCandidates(e21, t26) {
    if (t(e21)) return t26;
    const a32 = [], n27 = (e22, t27) => r(e22) ? r(t27) ? e22 === t27 && e22 : e22 : t27;
    for (const s22 of e21) for (const e22 of t26) {
      if (!s22.spatialReference.equals(e22.spatialReference)) continue;
      const t27 = n27(s22.viewingMode, e22.viewingMode);
      if (false !== t27) {
        a32.push({ spatialReference: s22.spatialReference, viewingMode: t27 });
        break;
      }
    }
    return a32.length > 0 ? a32 : null;
  }
  _pickSpatialReferenceCandidate(e21) {
    const t26 = this.defaultSpatialReference;
    return t(e21) || e21.length < 1 ? r(t26) ? { spatialReference: t26, viewingMode: null } : null : (r(t26) && e21.length > 1 && e21.some(({ spatialReference: e22 }) => e22.equals(t26)) && (e21 = e21.filter(({ spatialReference: e22 }) => e22.equals(t26))), e21.length > 1 && e21.some(({ viewingMode: e22 }) => e22 !== l6.Local) && (e21 = e21.filter(({ viewingMode: e22 }) => e22 !== l6.Local)), e21[0]);
  }
  _getSupportedSpatialReferences(e21) {
    const t26 = "supportedSpatialReferences" in e21 && e21.supportedSpatialReferences || (e21.spatialReference ? [e21.spatialReference] : []);
    if (0 === t26.length) return [];
    const a32 = [];
    for (const n27 of t26) {
      const t27 = this.getSpatialReferenceSupport({ spatialReference: n27, layer: e21 });
      if (r(t27)) {
        const e22 = r(t27.constraints) ? t27.constraints : [{ spatialReference: n27, viewingMode: null }];
        for (const { spatialReference: t28, viewingMode: n28 } of e22) (!this.requiresExtentInSpatialReference || t(this.userSpatialReference) || t28.equals(this.userSpatialReference)) && a32.push({ spatialReference: t28, viewingMode: n28 });
      }
    }
    return a32;
  }
  _pickExtentCandidate(e21) {
    const t26 = this.spatialReference;
    return e21.find(({ extent: e22 }) => t26.equals(e22.spatialReference)) || e21[0];
  }
  _collectLayers(e21) {
    var _a;
    if ("loaded" !== this._loadMaybe((_a = this.map) == null ? void 0 : _a.call(this))) return { layers: [], updating: true };
    const t26 = { layers: [], preloading: -1, updating: false };
    for (const a32 of e21) if (this._collectCollection(a32, t26), t26.preloading === this.sourcePreloadCount) break;
    return { layers: t26.layers, updating: t26.updating };
  }
  _collectCollection(e21, t26) {
    if (e21.layers) {
      switch (this._loadMaybe(e21.parent)) {
        case "loading":
          return t26.updating = true, void ++t26.preloading;
        case "failed":
          return;
      }
      for (const a32 of e21.layers) {
        switch (this._loadMaybe(a32)) {
          case "failed":
            continue;
          case "loading":
            t26.updating = true, ++t26.preloading;
            break;
          case "loaded":
            t26.updating || t26.layers.push(a32), "layers" in a32 && this._collectCollection({ layers: a32.layers }, t26);
        }
        if (t26.preloading === this.sourcePreloadCount) break;
      }
    }
  }
  _loadMaybe(e21) {
    return e21 && "loadStatus" in e21 && null != e21.loadStatus ? "not-loaded" === e21.loadStatus ? (e21.load().catch(() => {
    }), "loading") : e21.loadStatus : "loaded";
  }
};
e([y()], h13.prototype, "required", void 0), e([y({ constructOnly: true })], h13.prototype, "map", void 0), e([y({ constructOnly: true })], h13.prototype, "getSpatialReferenceSupport", void 0), e([y()], h13.prototype, "defaultSpatialReference", void 0), e([y()], h13.prototype, "userSpatialReference", void 0), e([y()], h13.prototype, "sourcePreloadCount", void 0), e([y()], h13.prototype, "priorityCollection", void 0), e([y()], h13.prototype, "requiresExtentInSpatialReference", void 0), e([y()], h13.prototype, "suspended", void 0), e([y({ readOnly: true })], h13.prototype, "ready", null), e([y({ readOnly: true })], h13.prototype, "heightModelInfoReady", null), e([y({ readOnly: true })], h13.prototype, "spatialReference", null), e([y({ readOnly: true })], h13.prototype, "extent", null), e([y({ readOnly: true })], h13.prototype, "heightModelInfo", null), e([y({ readOnly: true })], h13.prototype, "vcsWkid", null), e([y({ readOnly: true })], h13.prototype, "latestVcsWkid", null), e([y({ readOnly: true })], h13.prototype, "viewingMode", null), e([y({ readOnly: true })], h13.prototype, "tileInfo", null), e([y({ readOnly: true })], h13.prototype, "mapCollections", null), e([y({ readOnly: true })], h13.prototype, "_allLayers", null), e([y({ readOnly: true })], h13.prototype, "_spatialReferenceTask", null), e([y({ readOnly: true })], h13.prototype, "_tileInfoTask", null), e([y({ readOnly: true })], h13.prototype, "_heightModelInfoTask", null), e([y({ readOnly: true })], h13.prototype, "_extentCandidatesTask", null), e([y()], h13.prototype, "_extentTask", null), e([y()], h13.prototype, "_projectExtentTask", void 0), h13 = e([a2("esri.views.support.DefaultsFromMap")], h13);

// node_modules/@arcgis/core/views/View.js
var W6;
var z5 = W6 = class extends a9(n4.EventedMixin(m2(v3))) {
  constructor(e21) {
    super(e21), this._userSpatialReference = null, this._cursor = null, this.allLayerViews = new l5({ getCollections: () => {
      var _a, _b, _c;
      return [(_a = this.basemapView) == null ? void 0 : _a.baseLayerViews, (_b = this.groundView) == null ? void 0 : _b.layerViews, this.layerViews, (_c = this.basemapView) == null ? void 0 : _c.referenceLayerViews];
    }, getChildrenFunction: (e22) => e22.layerViews }), this.groundView = null, this.basemapView = null, this.fatalError = null, this.graphics = new i7(), this.analyses = new i15(), this.typeSpecificPreconditionsReady = true, this.layerViews = new j2(), this.magnifier = new p16(), this.padding = { left: 0, top: 0, right: 0, bottom: 0 }, this.ready = false, this.spatialReferenceWarningDelay = 1e3, this.supportsGround = true, this.timeExtent = null, this.timeReference = new a5(), this.type = null, this.scale = null, this.updating = false, this.initialExtentRequired = true, this.input = new a18(), this.navigation = new p19(), this.layerViewManager = null, this.analysisViewManager = null, this.isHeightModelInfoRequired = false, this.width = null, this.height = null, this.resizing = false, this.suspended = false, this.viewEvents = new p17(this), this.persistableViewModels = new j2(), this._isValid = false, this._readyCycleForced = false, this._currentSpatialReference = null, this.handles.add(l3(() => this.preconditionsReady, (e22) => {
      var _a, _b;
      e22 ? (this._currentSpatialReference = this.spatialReference, W6.views.add(this)) : (this._currentSpatialReference = null, W6.views.remove(this)), this.notifyChange("spatialReference"), !e22 && this.ready ? ((_a = this.toolViewManager) == null ? void 0 : _a.detach(), r(this.analysisViewManager) && this.analysisViewManager.detach(), (_b = this.layerViewManager) == null ? void 0 : _b.clear(), this._teardown()) : e22 && !this.ready && (this._startup(), r(this.analysisViewManager) && this.analysisViewManager.attach(), this.toolViewManager.attach());
    }, U2));
  }
  initialize() {
    this.addResolvingPromise(this.validate().then(() => (this._isValid = true, j3(() => this.ready)))), this.basemapView = new p15({ view: this }), this.layerViewManager = new M5({ view: this, layerViewImporter: { importLayerView: (e21) => this.importLayerView(e21), hasLayerViewModule: (e21) => this.hasLayerViewModule(e21) }, supportsGround: this.supportsGround }), this.toolViewManager = new y12({ view: this }), this._setupSpatialReferenceLogger(), this.handles.add([l3(() => this.initialExtentRequired, (e21) => this.defaultsFromMap.required = { ...this.defaultsFromMap.required, extent: e21 }, { sync: true, initial: true }), l3(() => this.ready, (e21) => {
      this.defaultsFromMap && (this.defaultsFromMap.suspended = e21, this.defaultsFromMap.userSpatialReference = e21 ? this.spatialReference : this._userSpatialReference);
    }, { sync: true }), l3(() => this._userSpatialReference, (e21) => {
      this.defaultsFromMap && (this.defaultsFromMap.userSpatialReference = e21);
    }, { sync: true, initial: true })]);
  }
  _setupSpatialReferenceLogger() {
    let e21 = null;
    this.handles.add([l3(() => {
      var _a;
      return (_a = this.defaultsFromMap) == null ? void 0 : _a.ready;
    }, (t26) => {
      var _a;
      const i28 = ((_a = this.map) == null ? void 0 : _a.allLayers.length) > 0;
      if (t26 && !this.spatialReference && i28) {
        if (r(e21)) return;
        const t27 = n2(() => e21 = w(e21));
        e21 = j4(async (t28) => {
          try {
            await U(this.spatialReferenceWarningDelay, null, t28);
          } catch {
            return;
          } finally {
            e21 = null;
          }
          s2.getLogger(this.declaredClass).warn("#spatialReference", "no spatial reference could be derived from the currently added map layers");
        }), this.handles.add(t27, "spatial-reference-logger-task");
      } else this.handles.remove("spatial-reference-logger-task");
    }, { sync: true })]);
  }
  destroy() {
    if (this.destroyed) return;
    this.viewEvents.destroy(), this.allLayerViews.destroy(), this.navigation && (this.navigation.destroy(), this._set("navigation", null)), this.graphics = a(this.graphics), this.analyses = a(this.analyses), this.handles.remove("defaultsFromMap"), this.defaultsFromMap.destroy(), this._set("defaultsFromMap", null), a(this.analysisViewManager), this.toolViewManager = a(this.toolViewManager), this.layerViewManager = a(this.layerViewManager), this.basemapView = a(this.basemapView), this.invalidate(), this._emitter.clear(), this.handles.removeAll();
    const e21 = this.map;
    this.map = null, e21 == null ? void 0 : e21.destroy();
  }
  _startup() {
    this._set("ready", true);
  }
  _teardown() {
    this._set("ready", false);
  }
  whenReady() {
    return Promise.resolve(this);
  }
  toMap() {
    return s2.getLogger(this.declaredClass).error("#toMap()", "Not implemented on this instance of View"), null;
  }
  get activeTool() {
    var _a;
    return (_a = this.toolViewManager) == null ? void 0 : _a.activeTool;
  }
  set activeTool(e21) {
    this.toolViewManager && (this.toolViewManager.activeTool = e21);
  }
  get animation() {
    return this._get("animation");
  }
  set animation(e21) {
    this._set("animation", e21);
  }
  get center() {
    return null;
  }
  get _defaultsFromMapSettings() {
    return {};
  }
  get defaultsFromMap() {
    return new h13({ required: { tileInfo: false, heightModelInfo: false, extent: false }, map: () => this.map, getSpatialReferenceSupport: (e21) => this.getSpatialReferenceSupport(e21), ...this._defaultsFromMapSettings });
  }
  get extent() {
    return this._get("extent");
  }
  set extent(e21) {
    this._set("extent", e21);
  }
  get heightModelInfo() {
    return this.getDefaultHeightModelInfo();
  }
  get interacting() {
    return this.navigating;
  }
  get navigating() {
    return false;
  }
  get preconditionsReady() {
    var _a;
    return !(this.fatalError || !this._isValid || this._readyCycleForced || !this.map || m5.isLoadable(this.map) && !this.map.loaded || 0 === this.width || 0 === this.height || !this.spatialReference || !this._validateSpatialReference(this.spatialReference) || !this._currentSpatialReference && !((_a = this.defaultsFromMap) == null ? void 0 : _a.ready) || !this.typeSpecificPreconditionsReady);
  }
  get resolution() {
    return 0;
  }
  set map(e21) {
    e21 !== this._get("map") && ((e21 == null ? void 0 : e21.destroyed) && (s2.getLogger(this.declaredClass).warn("#map", "The provided map is already destroyed", { map: e21 }), e21 = null), m5.isLoadable(e21) && e21.load().catch(() => {
    }), this.constructed && (this.forceReadyCycle(), this._currentSpatialReference = null), this._set("map", e21));
  }
  get spatialReference() {
    var _a, _b;
    let e21 = this._userSpatialReference || this._currentSpatialReference || this.getDefaultSpatialReference() || null;
    return e21 && ((_b = (_a = this.defaultsFromMap) == null ? void 0 : _a.required) == null ? void 0 : _b.heightModelInfo) && (e21 = e21.clone(), e21.vcsWkid = this.defaultsFromMap.vcsWkid, e21.latestVcsWkid = this.defaultsFromMap.latestVcsWkid), e21;
  }
  set spatialReference(e21) {
    const t26 = !E2(e21, this._get("spatialReference"));
    this._set("_userSpatialReference", e21), t26 && (this._set("spatialReference", e21), this._spatialReferenceChanged(e21));
  }
  _spatialReferenceChanged(e21) {
  }
  get stationary() {
    return !this.animation && !this.navigating && !this.resizing;
  }
  get tools() {
    var _a;
    return (_a = this.toolViewManager) == null ? void 0 : _a.tools;
  }
  get initialExtent() {
    var _a;
    return (_a = this.defaultsFromMap) == null ? void 0 : _a.extent;
  }
  get cursor() {
    const e21 = this.toolViewManager ? this.toolViewManager.cursor : null;
    return r(e21) ? e21 : this._cursor || "default";
  }
  set cursor(e21) {
    this._cursor = e21, this.notifyChange("cursor");
  }
  get size() {
    return [this.width, this.height];
  }
  whenLayerView(e21) {
    return this.layerViewManager.whenLayerView(e21);
  }
  getDefaultSpatialReference() {
    var _a;
    return (_a = this.defaultsFromMap) == null ? void 0 : _a.spatialReference;
  }
  getDefaultHeightModelInfo() {
    var _a;
    return (this.map && "heightModelInfo" in this.map ? this.map.heightModelInfo : void 0) ?? ((_a = this.defaultsFromMap) == null ? void 0 : _a.heightModelInfo) ?? null;
  }
  importLayerView(e21) {
    throw new s3("importLayerView() not implemented");
  }
  hasLayerViewModule(e21) {
    return false;
  }
  async validate() {
  }
  invalidate() {
    this._isValid = false;
  }
  getSpatialReferenceSupport() {
    return { constraints: null };
  }
  _validateSpatialReference(e21) {
    return r(this.getSpatialReferenceSupport({ spatialReference: e21 }));
  }
  when(e21, t26) {
    return this.isResolved() && !this.ready && s2.getLogger(this.declaredClass).warn("#when()", "Calling view.when() while the view is no longer ready but was already resolved once will resolve immediately. Use reactiveUtils.whenOnce(() => view.ready).then(...) instead."), super.when(e21, t26);
  }
  forceReadyCycle() {
    this.ready && (f3(() => false === this.preconditionsReady, () => this._readyCycleForced = false, { once: true }), this._readyCycleForced = true);
  }
  addAndActivateTool(e21) {
    this.toolViewManager.tools.add(e21), this.activeTool = e21;
  }
  tryFatalErrorRecovery() {
    this.fatalError = null;
  }
};
z5.views = new j2(), e([y()], z5.prototype, "_userSpatialReference", void 0), e([y()], z5.prototype, "activeTool", null), e([y({ readOnly: true })], z5.prototype, "allLayerViews", void 0), e([y()], z5.prototype, "groundView", void 0), e([y()], z5.prototype, "animation", null), e([y()], z5.prototype, "basemapView", void 0), e([y()], z5.prototype, "center", null), e([y({ readOnly: true })], z5.prototype, "_defaultsFromMapSettings", null), e([y()], z5.prototype, "defaultsFromMap", null), e([y()], z5.prototype, "fatalError", void 0), e([y({ type: w3 })], z5.prototype, "extent", null), e([y(a11(i7, "graphics"))], z5.prototype, "graphics", void 0), e([y(a11(i15, "analyses"))], z5.prototype, "analyses", void 0), e([y({ readOnly: true, type: v7 })], z5.prototype, "heightModelInfo", null), e([y({ readOnly: true })], z5.prototype, "interacting", null), e([y({ readOnly: true })], z5.prototype, "navigating", null), e([y({ readOnly: true, dependsOn: ["fatalError", "_isValid", "_readyCycleForced", "map", "map.loaded?", "width", "height", "spatialReference", "_currentSpatialReference", "defaultsFromMap.ready", "typeSpecificPreconditionsReady"] })], z5.prototype, "preconditionsReady", null), e([y({ readOnly: true })], z5.prototype, "typeSpecificPreconditionsReady", void 0), e([y({ type: j2, readOnly: true })], z5.prototype, "layerViews", void 0), e([y()], z5.prototype, "resolution", null), e([y({ type: p16 })], z5.prototype, "magnifier", void 0), e([y({ value: null, type: L3 })], z5.prototype, "map", null), e([y()], z5.prototype, "padding", void 0), e([y({ readOnly: true })], z5.prototype, "ready", void 0), e([y({ type: f2 })], z5.prototype, "spatialReference", null), e([y()], z5.prototype, "spatialReferenceWarningDelay", void 0), e([y()], z5.prototype, "stationary", null), e([y({ readOnly: true })], z5.prototype, "supportsGround", void 0), e([y({ type: T })], z5.prototype, "timeExtent", void 0), e([y({ type: a5, nonNullable: true })], z5.prototype, "timeReference", void 0), e([y()], z5.prototype, "tools", null), e([y()], z5.prototype, "toolViewManager", void 0), e([y({ readOnly: true })], z5.prototype, "type", void 0), e([y({ type: Number })], z5.prototype, "scale", void 0), e([y({ readOnly: true })], z5.prototype, "updating", void 0), e([y({ readOnly: true })], z5.prototype, "initialExtentRequired", void 0), e([y({ readOnly: true })], z5.prototype, "initialExtent", null), e([y()], z5.prototype, "cursor", null), e([y({ readOnly: true })], z5.prototype, "input", void 0), e([y({ type: p19, nonNullable: true })], z5.prototype, "navigation", void 0), e([y()], z5.prototype, "layerViewManager", void 0), e([y()], z5.prototype, "analysisViewManager", void 0), e([y()], z5.prototype, "width", void 0), e([y()], z5.prototype, "height", void 0), e([y({ readOnly: true })], z5.prototype, "resizing", void 0), e([y({ value: null, readOnly: true })], z5.prototype, "size", null), e([y({ readOnly: true })], z5.prototype, "suspended", void 0), e([y({ readOnly: true })], z5.prototype, "viewEvents", void 0), e([y({ readOnly: true })], z5.prototype, "persistableViewModels", void 0), e([y()], z5.prototype, "_isValid", void 0), e([y()], z5.prototype, "_readyCycleForced", void 0), e([y()], z5.prototype, "_currentSpatialReference", void 0), z5 = W6 = e([a2("esri.views.View")], z5);
var A6 = z5;

// node_modules/@arcgis/core/views/ViewAnimation.js
var n20 = class extends _ {
  constructor(t26) {
    super(t26), this.state = "running", this.target = null, this._dfd = null;
  }
  initialize() {
    this.addResolvingPromise(new Promise((t26, s22) => this._dfd = { resolve: t26, reject: s22 }));
  }
  get done() {
    return "finished" === this.state || "stopped" === this.state;
  }
  stop() {
    var _a;
    "stopped" !== this.state && "finished" !== this.state && (this._set("state", "stopped"), (_a = this._dfd) == null ? void 0 : _a.reject(new s3("ViewAnimation stopped")));
  }
  finish() {
    var _a;
    "stopped" !== this.state && "finished" !== this.state && (this._set("state", "finished"), (_a = this._dfd) == null ? void 0 : _a.resolve());
  }
  update(t26, s22) {
    s22 || (s22 = C(t26) ? "waiting-for-target" : "running"), this._set("target", t26), this._set("state", s22);
  }
};
e([y({ readOnly: true })], n20.prototype, "done", null), e([y({ readOnly: true, type: String })], n20.prototype, "state", void 0), e([y()], n20.prototype, "target", void 0), n20 = e([a2("esri.views.ViewAnimation")], n20), function(t26) {
  t26.State = { RUNNING: "running", STOPPED: "stopped", FINISHED: "finished", WAITING_FOR_TARGET: "waiting-for-target" };
}(n20 || (n20 = {}));
var p21 = n20;

// node_modules/@arcgis/core/views/2d/AnimationManager.js
var u12 = class {
  constructor(t26, i28, s22, e21) {
    const o27 = t26.targetGeometry, n27 = i28.targetGeometry;
    e21 ? "string" == typeof e21 && (e21 = r8(e21) || t10.ease) : e21 = t10.ease, this.easing = e21, this.duration = s22, this.sCenterX = o27.x, this.sCenterY = o27.y, this.sScale = t26.scale, this.sRotation = t26.rotation, this.tCenterX = n27.x, this.tCenterY = n27.y, this.tScale = i28.scale, this.tRotation = i28.rotation, this.dCenterX = this.tCenterX - this.sCenterX, this.dCenterY = this.tCenterY - this.sCenterY, this.dScale = this.tScale - this.sScale, this.dRotation = this.tRotation - this.sRotation, this.dRotation > 180 ? this.dRotation -= 360 : this.dRotation < -180 && (this.dRotation += 360);
  }
  applyRatio(t26, i28) {
    const s22 = this.easing(i28);
    let e21, o27, n27, a32;
    i28 >= 1 ? (e21 = this.tCenterX, o27 = this.tCenterY, n27 = this.tRotation, a32 = this.tScale) : (e21 = this.sCenterX + s22 * this.dCenterX, o27 = this.sCenterY + s22 * this.dCenterY, n27 = this.sRotation + s22 * this.dRotation, a32 = this.sScale + s22 * this.dScale), t26.targetGeometry.x = e21, t26.targetGeometry.y = o27, t26.scale = a32, t26.rotation = n27;
  }
};
var d11 = class extends v3 {
  constructor(t26) {
    super(t26), this.updateFunction = null, this.animation = null, this.duration = 200, this.transition = null, this.easing = t10.ease, this.view = null, this.viewpoint = new u3({ targetGeometry: new w2(), scale: 0, rotation: 0 }), this._updateTask = A2({ postRender: this._postRender.bind(this) }), this._updateTask.pause();
  }
  destroy() {
    this._updateTask = p(this._updateTask);
  }
  animate(t26, i28, s22) {
    this.stop();
    const e21 = this.viewpoint;
    Z(e21, i28), this.transition = new u12(this.viewpoint, t26.target, s22 && s22.duration || this.duration, s22 && s22.easing || this.easing);
    const o27 = () => {
      var _a, _b;
      this.animation === t26 && this._updateTask && ("finished" === t26.state && ((_a = this.transition) == null ? void 0 : _a.applyRatio(this.viewpoint, 1), ((_b = this.view) == null ? void 0 : _b.state) && (this.view.state.viewpoint = this.viewpoint.clone())), this.animation = null, this.updateFunction = null);
    };
    return t26.when(o27, o27), this._startTime = performance.now(), this._updateTask.resume(), this.animation = t26, t26;
  }
  animateContinous(t26, i28) {
    this.stop(), this.updateFunction = i28, this.viewpoint = t26;
    const s22 = new p21({ target: t26.clone() }), e21 = () => {
      this.animation === s22 && this._updateTask && (this.animation = null, this.updateFunction = null);
    };
    return s22.when(e21, e21), this._startTime = performance.now(), this._updateTask.resume(), this.animation = s22, s22;
  }
  stop() {
    this.animation && (this.animation.stop(), this.animation = null, this.updateFunction = null);
  }
  _postRender(t26) {
    var _a, _b;
    const i28 = this.animation;
    if (i28 && i28.state !== p21.State.STOPPED) {
      if (this.updateFunction) this.updateFunction(this.viewpoint, t26.deltaTime);
      else {
        const t27 = this.transition, i29 = (performance.now() - this._startTime) / t27.duration, s22 = i29 >= 1;
        t27.applyRatio(this.viewpoint, i29), s22 && ((_a = this.animation) == null ? void 0 : _a.finish());
      }
      ((_b = this.view) == null ? void 0 : _b.state) && (this.view.state.viewpoint = this.viewpoint.clone());
    } else this._updateTask.pause();
  }
};
e([y()], d11.prototype, "animation", void 0), e([y()], d11.prototype, "duration", void 0), e([y()], d11.prototype, "transition", void 0), e([y()], d11.prototype, "easing", void 0), e([y()], d11.prototype, "view", void 0), e([y()], d11.prototype, "viewpoint", void 0), d11 = e([a2("esri.views.2d.AnimationManager")], d11);
var l17 = d11;

// node_modules/@arcgis/core/views/2d/FrameTask.js
var s16 = class {
  constructor(t26) {
    this.view = t26, this._stationaryHandle = null, this._frameTaskHandle = null, this._updateParameters = null, this._updateRequested = false, this.stationary = true, this.animationInProgress = false, this.prepare = () => {
      this._updateParameters && (this._updateParameters.state = this.view.state, this._updateParameters.stationary = this.view.stationary, this._updateParameters.pixelRatio = window.devicePixelRatio, this._updateParameters.renderingOptions = this.view.renderingOptions);
    }, this.update = () => {
      var _a;
      this._updateRequested = false;
      const { basemapView: t27, graphicsView: a32, labelManager: s22, layerViews: i28, state: { id: r20 } } = this.view;
      t27 == null ? void 0 : t27.baseLayerViews.forEach(this._updateLayerView, this), i28.forEach(this._updateLayerView, this), t27 == null ? void 0 : t27.referenceLayerViews.forEach(this._updateLayerView, this), r(s22) && (s22.lastUpdateId !== r20 && (s22.viewChange(), s22.lastUpdateId = r20), s22.updateRequested && s22.processUpdate(this._updateParameters)), r(a32) && (a32.lastUpdateId !== r20 && (a32.viewChange(), a32.lastUpdateId = r20), a32.updateRequested && a32.processUpdate(this._updateParameters)), this.view.graphicsTileStore.setViewState(this._updateParameters.state), this.animationInProgress || this._updateRequested || ((_a = this._frameTaskHandle) == null ? void 0 : _a.pause());
    };
  }
  destroy() {
    this.stop();
  }
  start() {
    if (this._frameTaskHandle) return;
    const e21 = this.view;
    this.stationary = e21.stationary, this._updateParameters = { state: e21.state, pixelRatio: window.devicePixelRatio, stationary: this.stationary, renderingOptions: e21.renderingOptions }, this._stationaryHandle = l3(() => e21.stationary, (e22) => {
      this.stationary = e22, this.requestFrame();
    }), this._frameTaskHandle = A2(this), this.requestUpdate();
  }
  stop() {
    var _a;
    this._frameTaskHandle && (this._updateRequested = false, (_a = this._stationaryHandle) == null ? void 0 : _a.remove(), this._frameTaskHandle.remove(), this._updateParameters = this._stationaryHandle = this._frameTaskHandle = null, this.stationary = true, this.animationInProgress = false);
  }
  requestUpdate() {
    this._updateRequested || (this._updateRequested = true, this.requestFrame());
  }
  requestFrame() {
    this._frameTaskHandle && this._frameTaskHandle.resume();
  }
  _updateLayerView(e21) {
    if (!e21.attached) return void this.requestUpdate();
    const t26 = this.view.state, a32 = e21.lastUpdateId;
    null != a32 && (this.stationary || e21.moving) || (e21.moving = true, e21.moveStart()), a32 !== t26.id && e21.viewChange(), this.stationary && e21.moving && (e21.moving = false, e21.moveEnd()), e21.lastUpdateId = t26.id, e21.updateRequested && e21.processUpdate(this._updateParameters), "layerViews" in e21 && e21.layerViews.forEach(this._updateLayerView, this);
  }
};

// node_modules/@arcgis/core/views/2d/layerViewModuleImportUtils.js
function t18() {
  return Promise.all([import("./webglDeps-6VYOSYQV.js"), import("./mapViewDeps-IXGGSY2J.js")]);
}
var s17 = () => t18().then(() => import("./TileLayerView2D-M55W4MDC.js"));
var i21 = () => t18().then(() => import("./FeatureLayerView2D-IO2AIVGG.js"));
var l18 = { "base-dynamic": () => t18().then(() => import("./BaseDynamicLayerView2D-UR26WQR5.js")), "base-tile": s17, "bing-maps": s17, csv: i21, "geo-rss": () => t18().then(() => import("./GeoRSSLayerView2D-AAHRXOXY.js")), feature: i21, geojson: i21, graphics: () => t18().then(() => import("./GraphicsLayerView2D-UYSDAXO5.js")), group: () => t18().then(() => import("./GroupLayerView2D-WMNONEH4.js")), imagery: () => t18().then(() => import("./ImageryLayerView2D-UQH3EK3J.js")), "imagery-tile": () => t18().then(() => import("./ImageryTileLayerView2D-5S3RYB7N.js")), kml: () => t18().then(() => import("./KMLLayerView2D-HHH62UTT.js")), "knowledge-graph": () => t18().then(() => import("./KnowledgeGraphLayerView2D-EQDRARCX.js")), "link-chart": () => t18().then(() => import("./KnowledgeGraphLayerView2D-EQDRARCX.js")), "knowledge-graph-sublayer": i21, "map-image": () => t18().then(() => import("./MapImageLayerView2D-AYYNJ6LV.js")), "map-notes": () => t18().then(() => import("./MapNotesLayerView2D-HMGRRCCQ.js")), media: () => t18().then(() => import("./MediaLayerView2D-MXZT5EI4.js")), "ogc-feature": () => t18().then(() => import("./OGCFeatureLayerView2D-MZBBFCRE.js")), "open-street-map": s17, "oriented-imagery": i21, route: () => t18().then(() => import("./RouteLayerView2D-IVB2TLUH.js")), stream: () => t18().then(() => import("./StreamLayerView2D-XFY2RYEU.js")), "subtype-group": () => t18().then(() => import("./SubtypeGroupLayerView2D-GHFWJBSY.js")), tile: s17, "vector-tile": () => t18().then(() => import("./VectorTileLayerView2D-CLLW3IBY.js")), wcs: () => t18().then(() => import("./ImageryTileLayerView2D-5S3RYB7N.js")), "web-tile": s17, wfs: i21, wms: () => t18().then(() => import("./WMSLayerView2D-5QDHECIG.js")), wmts: () => t18().then(() => import("./WMTSLayerView2D-BVXHV2GE.js")), "line-of-sight": null, "base-elevation": null, "building-scene": null, dimension: null, elevation: null, "integrated-mesh": null, "point-cloud": null, voxel: null, scene: null, unknown: null, unsupported: null };
function o19(r20) {
  const a32 = r20.declaredClass ? r20.declaredClass.slice(r20.declaredClass.lastIndexOf(".") + 1) : "Unknown", t26 = a32.replace(/([a-z])([A-Z])/g, "$1-$2").toLowerCase();
  return new s3(`${t26}:view-not-supported`, `${a32} is not supported in 2D`);
}
var n21 = { hasLayerViewModule: (e21) => r(l18[e21.type]), importLayerView: (e21) => {
  const r20 = l18[e21.type];
  if (t(r20)) throw o19(e21);
  return r20(e21);
} };

// node_modules/@arcgis/core/geometry/support/near.js
function o20(o27, m24) {
  const { spatialReference: r20 } = m24, i28 = [m24.x, m24.y];
  let s22 = Number.POSITIVE_INFINITY, x4 = 0, c34 = 0;
  const a32 = [0, 0], f17 = "extent" === o27.type ? [[[o27.xmin, o27.ymin], [o27.xmin, o27.ymax], [o27.xmax, o27.ymax], [o27.xmax, o27.ymin], [o27.xmin, o27.ymin]]] : o27.rings;
  for (const n27 of f17) for (let o28 = 0; o28 < n27.length - 1; o28++) {
    o3(a32, i28, n27, o28);
    const m25 = m3(i28, a32);
    m25 < s22 && (s22 = m25, x4 = a32[0], c34 = a32[1]);
  }
  return { coordinate: new w2({ x: x4, y: c34, spatialReference: r20 }), distance: s22 };
}

// node_modules/@arcgis/core/views/2d/constraints/GeometryConstraint.js
var l19;
var y13 = l19 = class extends i4(v3) {
  constructor(e21) {
    super(e21), this.geometry = null, this.spatialReference = null;
  }
  get normalizedGeometry() {
    if (t(this.geometry) || !this.spatialReference) return null;
    if (!this.spatialReference.equals(this.geometry.spatialReference)) try {
      return rn(this.geometry, this.spatialReference);
    } catch (e21) {
      return s2.getLogger(this.declaredClass).error("#constraints.geometry", "could not project the geometry to the view's spatial reference", { geometry: this.geometry, spatialReference: this.spatialReference, error: e21 }), null;
    }
    return this.geometry;
  }
  constrain(e21, r20) {
    if (t(this.normalizedGeometry)) return e21;
    const t26 = e21.targetGeometry;
    if ("extent" === this.normalizedGeometry.type ? r3(this.normalizedGeometry, t26) : c(this.normalizedGeometry, t26)) return e21;
    const { coordinate: o27 } = o20(this.normalizedGeometry, t26);
    return o27 ? (e21.targetGeometry = o27, e21) : e21;
  }
  clone() {
    var _a, _b;
    return new l19({ geometry: (_a = this.geometry) == null ? void 0 : _a.clone(), spatialReference: (_b = this.spatialReference) == null ? void 0 : _b.clone() });
  }
};
e([y({ constructOnly: true })], y13.prototype, "geometry", void 0), e([y({ readOnly: true })], y13.prototype, "normalizedGeometry", null), e([y({ constructOnly: true })], y13.prototype, "spatialReference", void 0), y13 = l19 = e([a2("esri.views.2d.constraints.GeometryConstraint")], y13);

// node_modules/@arcgis/core/views/2d/constraints/RotationConstraint.js
var a21;
var n22 = a21 = class extends i4(v3) {
  constructor() {
    super(...arguments), this.enabled = true, this.rotationEnabled = true;
  }
  constrain(o27, r20) {
    return this.enabled && r20 ? (this.rotationEnabled || (o27.rotation = r20.rotation), o27) : o27;
  }
  clone() {
    return new a21({ enabled: this.enabled, rotationEnabled: this.rotationEnabled });
  }
};
e([y()], n22.prototype, "enabled", void 0), e([y()], n22.prototype, "rotationEnabled", void 0), n22 = a21 = e([a2("esri.views.2d.constraints.RotationConstraint")], n22);
var i22 = n22;

// node_modules/@arcgis/core/views/2d/constraints/ZoomConstraint.js
var a22;
var c28 = a22 = class extends i4(v3) {
  constructor(e21) {
    super(e21), this._lodByScale = {}, this._scales = [], this.effectiveLODs = null, this.effectiveMinZoom = -1, this.effectiveMaxZoom = -1, this.effectiveMinScale = 0, this.effectiveMaxScale = 0, this.lods = null, this.minZoom = -1, this.maxZoom = -1, this.minScale = 0, this.maxScale = 0, this.snapToZoom = true;
  }
  initialize() {
    let e21, { lods: t26, minScale: o27, maxScale: s22, minZoom: i28, maxZoom: a32 } = this, c34 = -1, r20 = -1, l25 = false, n27 = false;
    if (0 !== o27 && 0 !== s22 && o27 < s22 && ([o27, s22] = [s22, o27]), !t26 || !t26.length) return this._set("effectiveMinScale", o27), void this._set("effectiveMaxScale", s22);
    t26 = t26.map((e22) => e22.clone()), t26.sort((e22, t27) => t27.scale - e22.scale), t26.forEach((e22, t27) => e22.level = t27);
    for (const f17 of t26) !l25 && o27 > 0 && o27 >= f17.scale && (c34 = f17.level, l25 = true), !n27 && s22 > 0 && s22 >= f17.scale && (r20 = e21 ? e21.level : -1, n27 = true), e21 = f17;
    -1 === i28 && (i28 = 0 === o27 ? 0 : c34), -1 === a32 && (a32 = 0 === s22 ? t26.length - 1 : r20), i28 = Math.max(i28, 0), i28 = Math.min(i28, t26.length - 1), a32 = Math.max(a32, 0), a32 = Math.min(a32, t26.length - 1), i28 > a32 && ([i28, a32] = [a32, i28]), o27 = t26[i28].scale, s22 = t26[a32].scale, t26.splice(0, i28), t26.splice(a32 - i28 + 1, t26.length), t26.forEach((e22, t27) => {
      this._lodByScale[e22.scale] = e22, this._scales[t27] = e22.scale;
    }), this._set("effectiveLODs", t26), this._set("effectiveMinZoom", i28), this._set("effectiveMaxZoom", a32), this._set("effectiveMinScale", o27), this._set("effectiveMaxScale", s22);
  }
  constrain(e21, t26) {
    if (t26 && e21.scale === t26.scale) return e21;
    const o27 = this.effectiveMinScale, s22 = this.effectiveMaxScale, i28 = e21.targetGeometry, a32 = t26 && t26.targetGeometry, c34 = 0 !== s22 && e21.scale < s22, r20 = 0 !== o27 && e21.scale > o27;
    if (c34 || r20) {
      const c35 = r20 ? o27 : s22;
      if (t26 && a32) {
        const o28 = (c35 - t26.scale) / (e21.scale - t26.scale);
        i28.x = a32.x + (i28.x - a32.x) * o28, i28.y = a32.y + (i28.y - a32.y) * o28;
      }
      e21.scale = c35;
    }
    return this.snapToZoom && this.effectiveLODs && (e21.scale = this._getClosestScale(e21.scale)), e21;
  }
  fit(e21) {
    if (!this.effectiveLODs || !this.snapToZoom) return this.constrain(e21, null);
    const t26 = this.scaleToZoom(e21.scale), o27 = Math.abs(t26 - Math.floor(t26));
    return e21.scale = this.zoomToScale(o27 > 0.99 ? Math.round(t26) : Math.floor(t26)), e21;
  }
  zoomToScale(e21) {
    if (!this.effectiveLODs) return 0;
    e21 -= this.effectiveMinZoom, e21 = Math.max(0, e21);
    const t26 = this._scales;
    if (e21 <= 0) return t26[0];
    if (e21 >= t26.length) return t26[t26.length - 1];
    const o27 = Math.round(e21 - 0.5), s22 = Math.round(e21);
    return t26[s22] + (s22 - e21) * (t26[o27] - t26[s22]);
  }
  scaleToZoom(e21) {
    if (!this.effectiveLODs) return -1;
    const t26 = this._scales;
    let o27, s22;
    if (e21 >= t26[0]) return this.effectiveMinZoom;
    if (e21 <= t26[t26.length - 1]) return this.effectiveMaxZoom;
    for (let i28 = 0; i28 < t26.length - 1; i28++) {
      if (o27 = t26[i28], s22 = t26[i28 + 1], s22 === e21) {
        return i28 + this.effectiveMinZoom + 1;
      }
      if (o27 > e21 && s22 < e21) {
        return i28 + this.effectiveMinZoom + 1 - (e21 - s22) / (o27 - s22);
      }
    }
    return -1;
  }
  snapToClosestScale(e21) {
    if (!this.effectiveLODs) return e21;
    const t26 = this.scaleToZoom(e21);
    return this.zoomToScale(Math.round(t26));
  }
  snapToNextScale(e21, t26 = 0.5) {
    if (!this.effectiveLODs) return e21 * t26;
    const o27 = Math.round(this.scaleToZoom(e21));
    return this.zoomToScale(o27 + 1);
  }
  snapToPreviousScale(e21, t26 = 2) {
    if (!this.effectiveLODs) return e21 * t26;
    const o27 = Math.round(this.scaleToZoom(e21));
    return this.zoomToScale(o27 - 1);
  }
  clone() {
    return new a22({ lods: this.lods, minZoom: this.minZoom, maxZoom: this.maxZoom, minScale: this.minScale, maxScale: this.maxScale });
  }
  _getClosestScale(e21) {
    return this._lodByScale[e21] || (e21 = this._scales.reduce((t26, o27) => Math.abs(o27 - e21) <= Math.abs(t26 - e21) ? o27 : t26, this._scales[0])), this._lodByScale[e21].scale;
  }
};
e([y({ readOnly: true })], c28.prototype, "effectiveLODs", void 0), e([y({ readOnly: true })], c28.prototype, "effectiveMinZoom", void 0), e([y({ readOnly: true })], c28.prototype, "effectiveMaxZoom", void 0), e([y({ readOnly: true })], c28.prototype, "effectiveMinScale", void 0), e([y({ readOnly: true })], c28.prototype, "effectiveMaxScale", void 0), e([y()], c28.prototype, "lods", void 0), e([y()], c28.prototype, "minZoom", void 0), e([y()], c28.prototype, "maxZoom", void 0), e([y()], c28.prototype, "minScale", void 0), e([y()], c28.prototype, "maxScale", void 0), e([y()], c28.prototype, "snapToZoom", void 0), c28 = a22 = e([a2("esri.views.2d.constraints.ZoomConstraint")], c28);
var r14 = c28;

// node_modules/@arcgis/core/views/2d/MapViewConstraints.js
var p22 = { base: null, key: "type", typeMap: { extent: w3, polygon: v4 } };
var c29 = class extends v3 {
  constructor(o27) {
    super(o27), this.lods = null, this.minScale = 0, this.maxScale = 0, this.minZoom = -1, this.maxZoom = -1, this.rotationEnabled = true, this.snapToZoom = true;
  }
  destroy() {
    this.view = null;
  }
  get effectiveLODs() {
    return this._zoom.effectiveLODs;
  }
  get effectiveMinScale() {
    return this._zoom.effectiveMinScale;
  }
  get effectiveMaxScale() {
    return this._zoom.effectiveMaxScale;
  }
  get effectiveMinZoom() {
    return this._zoom.effectiveMinZoom;
  }
  get effectiveMaxZoom() {
    return this._zoom.effectiveMaxZoom;
  }
  set geometry(o27) {
    o27 ? this._set("geometry", o27) : this._set("geometry", null);
  }
  get version() {
    var _a, _b, _c;
    return `${(_a = this._zoom) == null ? void 0 : _a.uid}/${(_b = this._rotation) == null ? void 0 : _b.uid}/${(_c = this._geometry) == null ? void 0 : _c.uid}`;
  }
  get _defaultLODs() {
    var _a, _b, _c;
    const o27 = (_b = (_a = this.view) == null ? void 0 : _a.defaultsFromMap) == null ? void 0 : _b.tileInfo, e21 = (_c = this.view) == null ? void 0 : _c.spatialReference;
    return o27 && e21 && o27.spatialReference.equals(e21) ? o27.lods : null;
  }
  get _geometry() {
    var _a;
    return new y13({ geometry: this.geometry, spatialReference: (_a = this.view) == null ? void 0 : _a.spatialReference });
  }
  get _rotation() {
    return new i22({ rotationEnabled: this.rotationEnabled });
  }
  get _zoom() {
    const o27 = this._get("_zoom"), e21 = this.lods || this._defaultLODs, t26 = this.minZoom, r20 = this.maxZoom, s22 = this.minScale, i28 = this.maxScale, n27 = this.snapToZoom;
    return o27 && o27.lods === e21 && o27.minZoom === t26 && o27.maxZoom === r20 && o27.minScale === s22 && o27.maxScale === i28 && o27.snapToZoom === n27 ? o27 : new r14({ lods: e21, minZoom: t26, maxZoom: r20, minScale: s22, maxScale: i28, snapToZoom: n27 });
  }
  canZoomInTo(o27) {
    const e21 = this.effectiveMaxScale;
    return 0 === e21 || o27 >= e21;
  }
  canZoomOutTo(o27) {
    const e21 = this.effectiveMinScale;
    return 0 === e21 || o27 <= e21;
  }
  constrain(o27, e21) {
    return this._zoom.constrain(o27, e21), this._rotation.constrain(o27, e21), this._geometry.constrain(o27, e21), o27;
  }
  constrainByGeometry(o27) {
    return this._geometry.constrain(o27);
  }
  fit(o27) {
    return this._zoom.fit(o27);
  }
  zoomToScale(o27) {
    return this._zoom.zoomToScale(o27);
  }
  scaleToZoom(o27) {
    return this._zoom.scaleToZoom(o27);
  }
  snapScale(o27) {
    return this._zoom.snapToClosestScale(o27);
  }
  snapToNextScale(o27) {
    return this._zoom.snapToNextScale(o27);
  }
  snapToPreviousScale(o27) {
    return this._zoom.snapToPreviousScale(o27);
  }
};
e([y({ readOnly: true })], c29.prototype, "effectiveLODs", null), e([y({ readOnly: true })], c29.prototype, "effectiveMinScale", null), e([y({ readOnly: true })], c29.prototype, "effectiveMaxScale", null), e([y({ readOnly: true })], c29.prototype, "effectiveMinZoom", null), e([y({ readOnly: true })], c29.prototype, "effectiveMaxZoom", null), e([y({ types: p22, value: null })], c29.prototype, "geometry", null), e([y({ type: [p4] })], c29.prototype, "lods", void 0), e([y()], c29.prototype, "minScale", void 0), e([y()], c29.prototype, "maxScale", void 0), e([y()], c29.prototype, "minZoom", void 0), e([y()], c29.prototype, "maxZoom", void 0), e([y()], c29.prototype, "rotationEnabled", void 0), e([y()], c29.prototype, "snapToZoom", void 0), e([y()], c29.prototype, "view", void 0), e([y({ readOnly: true })], c29.prototype, "version", null), e([y()], c29.prototype, "_defaultLODs", null), e([y({ type: y13 })], c29.prototype, "_geometry", null), e([y({ type: i22 })], c29.prototype, "_rotation", null), e([y({ readOnly: true, type: r14 })], c29.prototype, "_zoom", null), c29 = e([a2("esri.views.2d.MapViewConstraints")], c29);
var u13 = c29;

// node_modules/@arcgis/core/views/2d/PaddedViewState.js
var l20;
var m17;
var w17 = l20 = class extends v3 {
  constructor() {
    super(...arguments), this.left = 0, this.top = 0, this.right = 0, this.bottom = 0;
  }
  clone() {
    return new l20({ left: this.left, top: this.top, right: this.right, bottom: this.bottom });
  }
};
e([y()], w17.prototype, "left", void 0), e([y()], w17.prototype, "top", void 0), e([y()], w17.prototype, "right", void 0), e([y()], w17.prototype, "bottom", void 0), w17 = l20 = e([a2("esri.views.2d.PaddedViewState.Padding")], w17);
var u14 = m17 = class extends U4 {
  constructor(...t26) {
    super(...t26), this.paddedViewState = new U4(), this._updateContent = (() => {
      const t27 = n7();
      return () => {
        const e21 = this._get("size"), i28 = this._get("padding");
        if (!e21 || !i28) return;
        const o27 = this.paddedViewState;
        r6(t27, i28.left + i28.right, i28.top + i28.bottom), o5(t27, e21, t27), a8(o27.size, t27);
        const s22 = o27.viewpoint;
        s22 && (this.viewpoint = s22);
      };
    })(), this.addHandles(l3(() => [this.size, this.padding], () => this._updateContent(), U2)), this.padding = new w17(), this.size = [0, 0];
  }
  set padding(t26) {
    this._set("padding", t26 || new w17());
  }
  set viewpoint(t26) {
    if (t26) {
      const e21 = t26.clone();
      this.paddedViewState.viewpoint = t26, gt(e21, t26, this._get("size"), this._get("padding"));
      const i28 = this._viewpoint2D, o27 = e21.targetGeometry;
      i28.center[0] = o27.x, i28.center[1] = o27.y, i28.rotation = e21.rotation, i28.scale = e21.scale, i28.spatialReference = o27.spatialReference, this._update();
    }
  }
  clone() {
    return new m17({ padding: this.padding.clone(), size: this.size.slice(), viewpoint: this.paddedViewState.viewpoint.clone(), pixelRatio: this.pixelRatio });
  }
};
e([y()], u14.prototype, "paddedViewState", void 0), e([y({ type: w17 })], u14.prototype, "padding", null), e([y()], u14.prototype, "viewpoint", null), u14 = m17 = e([a2("esri.views.2d.PaddedViewState")], u14);
var f14 = u14;

// node_modules/@arcgis/core/views/input/handlers/support.js
function t19(t26, r20) {
  switch (r20) {
    case "primary":
      return "touch" === t26.pointerType || 0 === t26.button;
    case "secondary":
      return "touch" !== t26.pointerType && 2 === t26.button;
    case "tertiary":
      return "touch" !== t26.pointerType && 1 === t26.button;
  }
}

// node_modules/@arcgis/core/views/2d/input/handlers/DoubleClickZoom.js
var a23 = class extends i10 {
  constructor(t26, i28) {
    super(true), this._view = t26, this.registerIncoming("double-click", i28, (t27) => this._handleDoubleClick(t27, i28));
  }
  _handleDoubleClick(t26, a32) {
    t19(t26.data, "primary") && (t26.stopPropagation(), a32 ? this._view.mapViewNavigation.zoomOut([t26.data.x, t26.data.y]) : this._view.mapViewNavigation.zoomIn([t26.data.x, t26.data.y]));
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/DoubleTapDragZoom.js
var e15 = class extends i10 {
  constructor(t26, e21, a32) {
    super(true), this.view = t26, this.pointerType = e21, this.registerIncoming("double-tap-drag", a32, (t27) => this._handleDoubleTapDrag(t27));
  }
  _handleDoubleTapDrag(t26) {
    const { data: e21 } = t26, { pointerType: a32 } = e21;
    if (a32 !== this.pointerType) return;
    t26.stopPropagation();
    const { action: s22, delta: i28 } = e21, { view: r20 } = this, { mapViewNavigation: n27 } = r20;
    switch (s22) {
      case "begin": {
        const { scale: t27 } = r20;
        this._startScale = t27, this._currentScale = t27, this._previousDelta = i28, n27.begin();
        break;
      }
      case "update": {
        if (this._previousDelta.y === i28.y) return;
        this._previousDelta = i28;
        const t27 = 1.015 ** i28.y, e22 = this._startScale * t27, a33 = e22 / this._currentScale;
        n27.setViewpointImmediate(a33), this._currentScale = e22;
        break;
      }
      case "end": {
        const { constraints: t27 } = r20, { effectiveLODs: e22, snapToZoom: a33 } = t27;
        if (!a33 || !e22) return void n27.end();
        const s23 = t27.snapScale(this._currentScale), o27 = (i28.y > 0 ? Math.max(s23, t27.snapToPreviousScale(this._startScale)) : Math.min(s23, t27.snapToNextScale(this._startScale))) / this._currentScale;
        n27.zoom(o27);
        break;
      }
    }
  }
};

// node_modules/@arcgis/core/views/input/DragEventSeparator.js
var t20 = class {
  constructor(t26) {
    this._callbacks = t26, this._currentCount = 0, this._callbacks.condition || (this._callbacks.condition = () => true);
  }
  handle(t26) {
    const s22 = t26.data, i28 = s22.pointers.size;
    switch (s22.action) {
      case "start":
        this._currentCount = i28, this._emitStart(t26);
        break;
      case "added":
        this._emitEnd(this._previousEvent), this._currentCount = i28, this._emitStart(t26);
        break;
      case "update":
        this._emitUpdate(t26);
        break;
      case "removed":
        this._startEvent && this._emitEnd(this._previousEvent), this._currentCount = i28, this._emitStart(t26);
        break;
      case "end":
        this._emitEnd(t26), this._currentCount = 0;
    }
    this._previousEvent = t26;
  }
  _emitStart(t26) {
    var _a, _b;
    this._startEvent = t26, ((_b = (_a = this._callbacks).condition) == null ? void 0 : _b.call(_a, this._currentCount, t26)) && this._callbacks.start(this._currentCount, t26, this._startEvent);
  }
  _emitUpdate(t26) {
    var _a, _b;
    ((_b = (_a = this._callbacks).condition) == null ? void 0 : _b.call(_a, this._currentCount, t26)) && this._callbacks.update(this._currentCount, t26, this._startEvent);
  }
  _emitEnd(t26) {
    var _a, _b;
    ((_b = (_a = this._callbacks).condition) == null ? void 0 : _b.call(_a, this._currentCount, t26)) && this._callbacks.end(this._currentCount, t26, this._startEvent), this._startEvent = null;
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/DragPan.js
var n23 = class extends i10 {
  constructor(t26, i28, a32) {
    super(true), this.view = t26, this.pointerAction = i28, this.registerIncoming("drag", a32, (t27) => this._handleDrag(t27)), this.registerIncoming("pointer-down", () => this.stopMomentumNavigation());
  }
  onInstall(i28) {
    super.onInstall(i28), this._dragEventSeparator = new t20({ start: (t26, i29) => {
      this.view.mapViewNavigation.pan.begin(this.view, i29.data), i29.stopPropagation();
    }, update: (t26, i29) => {
      this.view.mapViewNavigation.pan.update(this.view, i29.data), i29.stopPropagation();
    }, end: (t26, i29) => {
      this.view.mapViewNavigation.pan.end(this.view, i29.data), i29.stopPropagation();
    }, condition: (t26, i29) => 1 === t26 && t19(i29.data, this.pointerAction) });
  }
  _handleDrag(t26) {
    const i28 = this.view.mapViewNavigation;
    i28.pinch.zoomMomentum || i28.pinch.rotateMomentum ? this.stopMomentumNavigation() : this._dragEventSeparator.handle(t26);
  }
  stopMomentumNavigation() {
    this.view.mapViewNavigation.pan.stopMomentumNavigation();
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/DragRotate.js
var o21 = class extends i10 {
  constructor(a32, o27, r20) {
    super(true), this._view = a32, this.pointerAction = o27;
    const e21 = this._view.mapViewNavigation;
    this._dragEventSeparator = new t20({ start: (t26, a33) => {
      e21.rotate.begin(this._view, a33.data), a33.stopPropagation();
    }, update: (t26, a33) => {
      e21.rotate.update(this._view, a33.data), a33.stopPropagation();
    }, end: (t26, a33) => {
      e21.rotate.end(), a33.stopPropagation();
    }, condition: (t26, a33) => 1 === t26 && t19(a33.data, this.pointerAction) }), this.registerIncoming("drag", r20, (t26) => this._dragEventSeparator.handle(t26));
  }
};

// node_modules/@arcgis/core/views/navigation/gamepadAndKeyboardUtils.js
function n24(t26) {
  let n27 = t26 * t26;
  return t26 < 0 && (n27 *= -1), n27;
}
function a24(t26) {
  return t26.translation[0] = 0, t26.translation[1] = 0, t26.translation[2] = 0, t26.heading = 0, t26.tilt = 0, t26;
}
function i23(a32, i28, o27) {
  const s22 = o27, e21 = a32.state, r20 = a32.device, l25 = "forward-down" === i28.tiltDirection ? 1 : -1, c34 = 1;
  return "standard" === r20.deviceType ? (s22.translation[0] = n24(e21.axes[0]), s22.translation[1] = n24(e21.axes[1]), s22.translation[2] = n24(e21.buttons[7]) - n24(e21.buttons[6]), s22.heading = n24(e21.axes[2]), s22.tilt = n24(e21.axes[3])) : "spacemouse" === r20.deviceType && (s22.translation[0] = 1.2 * n24(e21.axes[0]), s22.translation[1] = 1.2 * n24(e21.axes[1]), s22.translation[2] = 2 * -n24(e21.axes[2]), s22.heading = 1.2 * n24(e21.axes[5]), s22.tilt = 1.2 * n24(e21.axes[3])), s22.tilt *= l25, g2(s22.translation, s22.translation, c34), s22;
}
function s18(t26) {
  return 0 === t26.translation[0] && 0 === t26.translation[1] && 0 === t26.translation[2] && 0 === t26.heading && 0 === t26.tilt && 0 === t26.zoom;
}

// node_modules/@arcgis/core/views/2d/input/handlers/GamepadNavigation.js
var d12 = class extends i10 {
  constructor(i28) {
    super(true), this._view = i28, this._frameTask = null, this._watchHandles = new t4(), this._currentDevice = null, this._transformation = { translation: [0, 0, 0], heading: 0, tilt: 0, zoom: 0 }, this._handle = this.registerIncoming("gamepad", (t26) => this._handleGamePadEvent(t26)), this._handle.pause();
  }
  onInstall(t26) {
    super.onInstall(t26), this._watchHandles.add([l3(() => {
      var _a;
      return (_a = this._view.navigation.gamepad) == null ? void 0 : _a.enabled;
    }, (t27) => {
      t27 ? (this._handle.resume(), this._frameTask || (this._frameTask = A2({ update: (t28) => this._frameUpdate(t28.deltaTime) }))) : (this._handle.pause(), this._frameTask && (this._frameTask.remove(), this._frameTask = null));
    }, h)]);
  }
  onUninstall() {
    this._watchHandles.removeAll(), this._frameTask && (this._frameTask.remove(), this._frameTask = null), super.onUninstall();
  }
  _handleGamePadEvent(t26) {
    const i28 = this._view.navigation.gamepad.device;
    i28 && t26.data.device !== i28 || this._currentDevice && this._currentDevice !== t26.data.device || ("end" === t26.data.action ? (this._currentDevice = null, a24(this._transformation)) : (this._currentDevice = t26.data.device, i23(t26.data, this._view.navigation.gamepad, this._transformation)));
  }
  _frameUpdate(t26) {
    const i28 = this._transformation;
    if (s18(i28)) return;
    const e21 = this._view.viewpoint.clone(), a32 = this._view.navigation.gamepad.velocityFactor, r20 = _6 * a32 * t26;
    St(e21, e21, [i28.translation[0] * r20, -i28.translation[1] * r20]);
    const o27 = 1 + i28.translation[2] * c30 * t26, h18 = this._view.constraints.rotationEnabled ? -i28.heading * m18 * t26 : 0, d15 = this._view.size, v16 = [d15[0] / 2, d15[1]];
    Gt(e21, e21, o27, h18, v16, d15);
    const p30 = this._view.constraints.constrain(e21, this._view.viewpoint);
    this._view.viewpoint = p30;
  }
};
var m18 = 0.06;
var _6 = 0.7;
var c30 = 6e-4;

// node_modules/@arcgis/core/views/2d/input/handlers/KeyPan.js
var i24 = class extends i10 {
  constructor(e21, i28, t26) {
    super(true), this.view = e21, this.keys = i28, this._pressed = false, this._keyMap = { [i28.left]: "left", [i28.right]: "right", [i28.up]: "up", [i28.down]: "down" }, this.registerIncoming("key-down", t26, (e22) => this._handleKeyDown(e22)), this.registerIncoming("key-up", t26, (e22) => this._handleKeyUp(e22)), this.registerIncoming("blur", t26, () => this._handleBlur());
  }
  _handleKeyDown(e21) {
    e21.data.repeat || this._handleKey(e21, true);
  }
  _handleKeyUp(e21) {
    this._handleKey(e21, false);
  }
  _handleBlur() {
    this._pressed && (this._pressed = false, this.view.mapViewNavigation.stop());
  }
  _handleKey(e21, i28) {
    const t26 = this._keyMap[e21.data.key];
    if (this._pressed = null != t26, this._pressed) {
      if (i28) switch (this.view.mapViewNavigation.begin(), t26) {
        case "left":
          this.view.mapViewNavigation.continousPanLeft();
          break;
        case "right":
          this.view.mapViewNavigation.continousPanRight();
          break;
        case "up":
          this.view.mapViewNavigation.continousPanUp();
          break;
        case "down":
          this.view.mapViewNavigation.continousPanDown();
      }
      else this._pressed = false, this.view.mapViewNavigation.stop();
      e21.stopPropagation();
    }
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/KeyRotate.js
var i25 = class extends i10 {
  constructor(e21, i28, t26) {
    super(true), this.view = e21, this.keys = i28, this._pressed = false, this._keyToDirection = { [i28.clockwiseOption1]: "clockwise", [i28.clockwiseOption2]: "clockwise", [i28.counterClockwiseOption1]: "counterClockwise", [i28.counterClockwiseOption2]: "counterClockwise", [i28.resetOption1]: "reset", [i28.resetOption2]: "reset" }, this.registerIncoming("key-down", t26, (e22) => this._handleKeyDown(e22)), this.registerIncoming("key-up", t26, (e22) => this._handleKeyUp(e22)), this.registerIncoming("blur", t26, () => this._handleBlur());
  }
  _handleKeyDown(e21) {
    e21.data.repeat || this._handleKey(e21, true);
  }
  _handleKeyUp(e21) {
    this._handleKey(e21, false);
  }
  _handleBlur() {
    this._pressed && (this._pressed = false, this.view.mapViewNavigation.stop());
  }
  _handleKey(e21, i28) {
    const t26 = e21.modifiers;
    if (t26.size > 0 && !t26.has("Shift") || !this.view.constraints.rotationEnabled) return;
    const s22 = this._keyToDirection[e21.data.key];
    if (this._pressed = null != s22, this._pressed) {
      if (i28) switch (this.view.mapViewNavigation.begin(), s22) {
        case "clockwise":
          this.view.mapViewNavigation.continousRotateClockwise();
          break;
        case "counterClockwise":
          this.view.mapViewNavigation.continousRotateCounterclockwise();
          break;
        case "reset":
          this.view.mapViewNavigation.resetRotation();
      }
      else this._pressed = false, this.view.mapViewNavigation.stop();
      e21.stopPropagation();
    }
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/KeyZoom.js
var t21;
!function(o27) {
  o27[o27.IN = 0] = "IN", o27[o27.OUT = 1] = "OUT";
}(t21 || (t21 = {}));
var e16 = class extends i10 {
  constructor(o27, e21, i28) {
    super(true), this.view = o27, this.keys = e21, this._keysToZoomAction = {}, this.registerIncoming("key-down", i28, (o28) => this._handleKeyDown(o28)), e21.zoomIn.forEach((o28) => this._keysToZoomAction[o28] = t21.IN), e21.zoomOut.forEach((o28) => this._keysToZoomAction[o28] = t21.OUT);
  }
  _handleKeyDown(o27) {
    this._handleKey(o27);
  }
  _handleKey(o27) {
    const e21 = o27.modifiers;
    if (e21.size > 0 && !e21.has("Shift")) return;
    const { key: i28 } = o27.data;
    if (!(i28 in this._keysToZoomAction)) return;
    const n27 = this._keysToZoomAction[i28], { mapViewNavigation: s22 } = this.view;
    let r20 = null;
    switch (n27) {
      case t21.IN:
        r20 = s22.zoomIn();
        break;
      case t21.OUT:
        r20 = s22.zoomOut();
        break;
      default:
        return;
    }
    s22.begin(), r20.then(() => s22.end()), o27.stopPropagation();
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/MouseWheelZoom.js
var t22 = 0.6;
var o22 = class extends i10 {
  constructor(e21, t26) {
    super(true), this._view = e21, this._canZoom = true, this.registerIncoming("mouse-wheel", t26, (e22) => this._handleMouseWheel(e22));
  }
  _handleMouseWheel(e21) {
    if (!this._view.navigation.mouseWheelZoomEnabled) return;
    if (e21.preventDefault(), e21.stopPropagation(), !this._canZoom) return;
    const o27 = this._view.mapViewNavigation, { x: n27, y: i28, deltaY: s22 } = e21.data, a32 = 1 / t22 ** (1 / 60 * s22), h18 = o27.zoom(a32, [n27, i28]);
    h18 && (this._canZoom = false, h18.catch(() => {
    }).then(() => {
      this._canZoom = true, o27.end();
    }));
  }
};

// node_modules/@arcgis/core/views/2d/input/handlers/PinchAction.js
var a25 = class extends i10 {
  constructor(i28) {
    super(true), this.view = i28, this.registerIncoming("drag", (t26) => this._handleDrag(t26)), this.registerIncoming("pointer-down", () => this._stopMomentumNavigation());
    const a32 = this.view.mapViewNavigation;
    this._dragEventSeparator = new t20({ start: (t26, i29) => {
      a32.pinch.begin(this.view, i29.data), i29.stopPropagation();
    }, update: (t26, i29) => {
      a32.pinch.update(this.view, i29.data), i29.stopPropagation();
    }, end: (t26, i29) => {
      a32.pinch.end(this.view), i29.stopPropagation();
    }, condition: (t26) => t26 >= 2 });
  }
  _handleDrag(t26) {
    this._dragEventSeparator.handle(t26);
  }
  _stopMomentumNavigation() {
    this.view.mapViewNavigation.pinch.stopMomentumNavigation();
  }
};

// node_modules/@arcgis/core/views/input/gamepad/GamepadState.js
function t23(t26) {
  const n27 = t26.native;
  return n27 ? { buttons: n27.buttons.map((t27) => t27.pressed ? t27.value ? t27.value : 1 : 0), axes: n27.axes.map((n28) => s19(n28, t26.axisThreshold)) } : { buttons: [], axes: [] };
}
function n25(t26, n27) {
  if (t26.axes.length !== n27.axes.length) return false;
  if (t26.buttons.length !== n27.buttons.length) return false;
  for (let e21 = 0; e21 < t26.axes.length; e21++) if (t26.axes[e21] !== n27.axes[e21]) return false;
  for (let e21 = 0; e21 < t26.buttons.length; e21++) if (t26.buttons[e21] !== n27.buttons[e21]) return false;
  return true;
}
function e17(t26) {
  for (let n27 = 0; n27 < t26.axes.length; n27++) if (0 !== t26.axes[n27]) return false;
  for (let n27 = 0; n27 < t26.buttons.length; n27++) if (0 !== t26.buttons[n27]) return false;
  return true;
}
function s19(t26, n27) {
  const e21 = Math.abs(t26);
  return e21 < n27 ? 0 : Math.sign(t26) * (e21 - n27) / (1 - n27);
}

// node_modules/@arcgis/core/views/input/gamepad/GamepadSource.js
var a26 = class {
  constructor(e21, t26) {
    this._element = e21, this._input = t26, this._hasEventListeners = false, this._onConnectGamepad = (e22) => {
      this._connectGamepad(e22.gamepad);
    }, this._onDisconnectGamepad = (e22) => {
      const t27 = e22.gamepad, i29 = t27.index, n28 = this._inputDevices[i29];
      n28 && (this._emitGamepadEvent(t27, t23(n28), false), this._inputDevices.splice(i29, 1), this._latestUpdate.splice(i29, 1), this._input.gamepad.devices.remove(n28), this.ensurePollingState());
    }, this._frameTask = null, this._latestUpdate = new Array(), this._inputDevices = new Array(), this._callback = null;
    const i28 = "getGamepads" in window.navigator, n27 = window.isSecureContext;
    this.supported = i28 && n27, this.supported && (this._forEachGamepad((e22) => this._connectGamepad(e22)), window.addEventListener("gamepadconnected", this._onConnectGamepad), window.addEventListener("gamepaddisconnected", this._onDisconnectGamepad), this.ensurePollingState());
  }
  destroy() {
    this.hasEventListeners = false, this.supported && (window.removeEventListener("gamepadconnected", this._onConnectGamepad), window.removeEventListener("gamepaddisconnected", this._onDisconnectGamepad));
  }
  set hasEventListeners(e21) {
    this._hasEventListeners !== e21 && (this._hasEventListeners = e21, this.ensurePollingState());
  }
  get _eventsEnabled() {
    return this.supported && this._inputDevices.length > 0 && this._hasEventListeners;
  }
  set onEvent(e21) {
    this._callback = e21;
  }
  _connectGamepad(e21) {
    const s22 = new a17(e21);
    "unknown" !== s22.deviceType && (this._inputDevices[e21.index] = s22, this._input.gamepad.devices.add(s22)), this.ensurePollingState();
  }
  ensurePollingState() {
    this._eventsEnabled ? this._startPolling() : this._stopPolling();
  }
  _startPolling() {
    null == this._frameTask && (this._frameTask = A2({ update: () => this._readGamepadState() }));
  }
  _stopPolling() {
    null != this._frameTask && (this._frameTask.remove(), this._frameTask = null, this._latestUpdate = new Array());
  }
  _readGamepadState() {
    const e21 = document.hasFocus(), t26 = this._element.contains(document.activeElement), a32 = "document" === this._input.gamepad.enabledFocusMode && !e21 || "view" === this._input.gamepad.enabledFocusMode && !t26;
    this._forEachGamepad((e22) => {
      const t27 = this._inputDevices[e22.index];
      if (!t27) return;
      const d15 = this._latestUpdate[e22.index], o27 = t23(t27), r20 = a32 || e17(o27);
      if (d15) {
        if (d15.timestamp === e22.timestamp) return;
        if (!d15.active && r20) return;
        if (n25(d15.state, o27)) return;
      }
      this._emitGamepadEvent(e22, o27, !r20);
    });
  }
  _forEachGamepad(e21) {
    const t26 = window.navigator.getGamepads();
    for (let s22 = 0; s22 < t26.length; s22++) {
      const i28 = t26[s22];
      this._validate(i28) && e21(i28);
    }
  }
  _emitGamepadEvent(e21, t26, s22) {
    const i28 = this._latestUpdate[e21.index], n27 = i28 && i28.active;
    if (!n27 && !s22) return;
    const a32 = !n27 && s22 ? "start" : n27 && s22 ? "update" : "end";
    this._latestUpdate[e21.index] = { timestamp: e21.timestamp, state: t26, active: s22 }, this._callback && this._callback({ device: this._inputDevices[e21.index], state: t26, action: a32 });
  }
  _validate(e21) {
    if (!e21) return false;
    if (!e21.connected) return false;
    for (let t26 = 0; t26 < e21.axes.length; t26++) if (isNaN(e21.axes[t26])) return false;
    return true;
  }
};

// node_modules/@arcgis/core/views/input/BrowserEventSource.js
var o23 = has("edge");
var a27 = has("chrome");
var s20 = has("ff");
var r15 = has("safari");
var h14 = "esri-view-surface";
var l21 = { touchNone: `${h14}--touch-none`, touchPan: `${h14}--touch-pan` };
var c31 = class _c {
  constructor(e21, t26) {
    this._input = t26, this._active = {}, this._callback = () => {
    }, this._activePointerCaptures = /* @__PURE__ */ new Set(), this._keyDownState = /* @__PURE__ */ new Set(), this._eventId = 1, this._browserTouchPanningEnabled = false, this._element = e21, e21.getAttribute("tabindex") || e21.setAttribute("tabindex", "0"), this._eventHandlers = { "key-down": this._handleKey, "key-up": this._handleKey, "pointer-down": this._handlePointer, "pointer-move": this._handlePointerPreventDefault, "pointer-up": this._handlePointerPreventDefault, "pointer-enter": this._handlePointer, "pointer-leave": this._handlePointer, "pointer-cancel": this._handlePointer, "mouse-wheel": this._handleMouseWheel, "pointer-capture-lost": this._handlePointerCaptureLost }, this._updateTouchAction(), this._element.addEventListener("keydown", this._preventAltKeyDefault), this._gamepadSource = new a26(e21, this._input), this._gamepadSource.onEvent = (e22) => this._callback("gamepad", e22);
  }
  destroy() {
    this._callback = () => {
    }, this.activeEvents = null, this._activePointerCaptures.forEach((e21) => {
      this._releasePointerCaptureSafe(e21);
    }), this._gamepadSource = a(this._gamepadSource), this._activePointerCaptures = null, this._removeTouchAction(), this._element.removeEventListener("keydown", this._preventAltKeyDefault);
  }
  get browserTouchPanningEnabled() {
    return this._browserTouchPanningEnabled;
  }
  set browserTouchPanningEnabled(e21) {
    this._browserTouchPanningEnabled = e21, this._updateTouchAction(), this._updateTouchEventHandling();
  }
  set onEventReceived(e21) {
    this._callback = e21;
  }
  set activeEvents(e21) {
    for (const t26 in this._active) if (!e21 || !e21.has(t26)) {
      const e22 = this._active[t26];
      this._element.removeEventListener(u15[t26], e22), delete this._active[t26];
    }
    e21 && e21.forEach((e22) => {
      if (!this._active[e22] && u15[e22]) {
        const t26 = (this._eventHandlers[e22] || this._handleDefault).bind(this, e22);
        this._element.addEventListener(u15[e22], t26), this._active[e22] = t26;
      }
    }), this._gamepadSource.hasEventListeners = (e21 == null ? void 0 : e21.has("gamepad")) ?? false;
  }
  setPointerCapture(e21, t26) {
    t26 ? (this._element.setPointerCapture(e21.pointerId), this._activePointerCaptures.add(e21.pointerId)) : (this._releasePointerCaptureSafe(e21.pointerId), this._activePointerCaptures.delete(e21.pointerId));
  }
  _updateTouchAction() {
    this._element.classList.remove(this._browserTouchPanningEnabled ? l21.touchNone : l21.touchPan), this._element.classList.add(this._browserTouchPanningEnabled ? l21.touchPan : l21.touchNone);
  }
  _updateTouchEventHandling() {
    this._browserTouchPanningEnabled ? this._element.addEventListener("touchmove", this._preventMultiTouchPanning) : this._element.removeEventListener("touchmove", this._preventMultiTouchPanning);
  }
  _removeTouchAction() {
    this._element.classList.remove(l21.touchNone), this._element.classList.remove(l21.touchPan), this._element.removeEventListener("touchmove", this._preventMultiTouchPanning);
  }
  _releasePointerCaptureSafe(e21) {
    try {
      if (this._element.hasPointerCapture && !this._element.hasPointerCapture(e21)) return;
      this._element.releasePointerCapture(e21);
    } catch (t26) {
    }
  }
  _updateNormalizedPointerLikeEvent(e21, t26) {
    const n27 = i11(this._element, e21);
    return _c.test.disableSubpixelCoordinates && (n27.x = Math.round(n27.x), n27.y = Math.round(n27.y)), t26.x = n27.x, t26.y = n27.y, t26;
  }
  _handleKey(e21, n27) {
    const i28 = o6(n27);
    i28 && "key-up" === e21 && this._keyDownState.delete(i28);
    const o27 = { native: n27, key: i28, repeat: !!i28 && this._keyDownState.has(i28) };
    i28 && "key-down" === e21 && this._keyDownState.add(o27.key), this._callback(e21, o27);
  }
  _handlePointer(e21, t26) {
    const n27 = this._updateNormalizedPointerLikeEvent(t26, { native: t26, x: 0, y: 0, pointerType: t26.pointerType, button: t26.button, buttons: t26.buttons, eventId: this._eventId++ });
    this._callback(e21, n27);
  }
  _handlePointerPreventDefault(e21, t26) {
    const n27 = this._updateNormalizedPointerLikeEvent(t26, { native: t26, x: 0, y: 0, pointerType: t26.pointerType, button: t26.button, buttons: t26.buttons, eventId: this._eventId++ });
    t26.preventDefault(), this._callback(e21, n27);
  }
  _handleMouseWheel(e21, t26) {
    let n27 = t26.deltaY;
    switch (t26.deltaMode) {
      case 0:
        o23 && (n27 = n27 / document.documentElement.clientHeight * 600);
        break;
      case 1:
        n27 *= 30;
        break;
      case 2:
        n27 *= 900;
    }
    o23 ? n27 *= 0.7 : a27 || r15 ? n27 *= 0.6 : s20 && (n27 *= 1.375);
    const i28 = 100, h18 = Math.abs(n27);
    if (h18 > i28) {
      const e22 = 0.02;
      n27 = n27 / h18 * 200 / (1 + Math.exp(-e22 * (h18 - i28)));
    }
    const l25 = this._updateNormalizedPointerLikeEvent(t26, { native: t26, x: 0, y: 0, deltaY: n27 });
    this._callback(e21, l25);
  }
  _handlePointerCaptureLost(e21, t26) {
    this._activePointerCaptures.delete(t26.pointerId), this._handleDefault(e21, t26);
  }
  _handleDefault(e21, t26) {
    const n27 = { native: t26 };
    t26.preventDefault(), this._callback(e21, n27);
  }
  _preventAltKeyDefault(e21) {
    "Alt" === e21.key && e21.preventDefault();
  }
  _preventMultiTouchPanning(e21) {
    e21.touches.length > 1 && e21.preventDefault();
  }
};
c31.test = { disableSubpixelCoordinates: false };
var u15 = { "key-down": "keydown", "key-up": "keyup", "pointer-down": "pointerdown", "pointer-up": "pointerup", "pointer-move": "pointermove", "mouse-wheel": "wheel", "pointer-capture-got": "gotpointercapture", "pointer-capture-lost": "lostpointercapture", "context-menu": "contextmenu", "pointer-enter": "pointerenter", "pointer-leave": "pointerleave", "pointer-cancel": "pointercancel", focus: "focus", blur: "blur" };

// node_modules/@arcgis/core/views/input/handlers/PreventContextMenu.js
var e18 = class extends i10 {
  constructor() {
    super(true), this.registerIncoming("context-menu", (t26) => {
      t26.data.native.preventDefault();
    });
  }
};

// node_modules/@arcgis/core/views/input/recognizers/support.js
function e19(t26, e21) {
  return Math.abs(e21.x - t26.x) + Math.abs(e21.y - t26.y);
}
function n26(t26, e21) {
  const n27 = e21.x - t26.x, r20 = e21.y - t26.y;
  return Math.sqrt(n27 * n27 + r20 * r20);
}
function r16(e21, n27) {
  if (n27 ? (n27.radius = 0, n27.center.x = 0, n27.center.y = 0) : n27 = { radius: 0, center: c2() }, 0 === e21.length) return n27;
  if (1 === e21.length) return n27.center.x = e21[0].x, n27.center.y = e21[0].y, n27;
  if (2 === e21.length) {
    const [t26, r21] = e21, [c35, o28] = [r21.x - t26.x, r21.y - t26.y];
    return n27.radius = Math.sqrt(c35 * c35 + o28 * o28) / 2, n27.center.x = (t26.x + r21.x) / 2, n27.center.y = (t26.y + r21.y) / 2, n27;
  }
  let r20 = 0, c34 = 0;
  for (let t26 = 0; t26 < e21.length; t26++) r20 += e21[t26].x, c34 += e21[t26].y;
  r20 /= e21.length, c34 /= e21.length;
  const o27 = e21.map((t26) => t26.x - r20), s22 = e21.map((t26) => t26.y - c34);
  let u18 = 0, i28 = 0, x4 = 0, y16 = 0, a32 = 0, h18 = 0, l25 = 0;
  for (let t26 = 0; t26 < o27.length; t26++) {
    const e22 = o27[t26], n28 = s22[t26], r21 = e22 * e22, c35 = n28 * n28;
    u18 += r21, i28 += c35, x4 += e22 * n28, y16 += r21 * e22, a32 += c35 * n28, h18 += e22 * c35, l25 += n28 * r21;
  }
  const f17 = 0.5 * (y16 + h18), g16 = 0.5 * (a32 + l25), p30 = u18 * i28 - x4 * x4, d15 = (f17 * i28 - g16 * x4) / p30, m24 = (u18 * g16 - x4 * f17) / p30, M6 = c2(d15 + r20, m24 + c34);
  return { radius: Math.sqrt(d15 * d15 + m24 * m24 + (u18 + i28) / e21.length), center: M6 };
}
function c32(t26) {
  const { native: e21 } = t26, { pointerId: n27, button: r20, pointerType: c34 } = e21;
  return "mouse" === c34 ? `${n27}:${r20}` : `${c34}`;
}

// node_modules/@arcgis/core/views/input/recognizers/SingleAndDoubleClick.js
var l22 = { maximumDoubleClickDelay: 250, maximumDoubleClickDistance: 10, maximumDoubleTouchDelay: 350, maximumDoubleTouchDistance: 35 };
var m19 = class extends i10 {
  constructor(t26 = l22.maximumDoubleClickDelay, i28 = l22.maximumDoubleClickDistance, o27 = l22.maximumDoubleTouchDelay, n27 = l22.maximumDoubleTouchDistance, s22 = t6) {
    super(false), this._maximumDoubleClickDelay = t26, this._maximumDoubleClickDistance = i28, this._maximumDoubleTouchDelay = o27, this._maximumDoubleTouchDistance = n27, this._clock = s22, this._pointerState = /* @__PURE__ */ new Map(), this._click = this.registerOutgoing("click"), this._doubleClick = this.registerOutgoing("double-click"), this.registerIncoming("immediate-click", this._handleImmediateClick.bind(this)), this.registerIncoming("pointer-down", this._handlePointerDown.bind(this));
  }
  onUninstall() {
    this._pointerState.forEach((e21) => e21.doubleClickTimer = p(e21.doubleClickTimer));
  }
  get hasPendingInputs() {
    return n(this._pointerState, (e21) => null != e21.doubleClickTimer);
  }
  _clearDoubleClickTimer(e21, t26) {
    const o27 = this._pointerState.get(e21);
    o27 && (o27.doubleClickTimer = p(o27.doubleClickTimer), t26 && this._click.emit(o27.event.data, void 0, o27.event.modifiers), this._pointerState.delete(e21), this.refreshHasPendingInputs());
  }
  _doubleClickTimeoutExceeded(e21) {
    const t26 = this._pointerState.get(e21);
    1 === t26.pointerDownCount && this._click.emit(t26.event.data, void 0, t26.event.modifiers), t26.doubleClickTimer = null, this._pointerState.delete(e21), this.refreshHasPendingInputs();
  }
  _getPointerId(e21) {
    const { pointerId: t26, pointerType: i28, button: o27 } = e21.native;
    return "mouse" === i28 ? `${t26}:${o27}` : `${i28}`;
  }
  _handleImmediateClick(e21) {
    const t26 = e21.data, { pointerType: i28 } = t26.native, o27 = this._getPointerId(t26);
    if (!this._pointerState.has(o27)) return void this._startClick(e21);
    const s22 = this._pointerState.get(o27), { data: l25, modifiers: m24 } = s22.event, c34 = "touch" === i28 ? this._maximumDoubleTouchDistance : this._maximumDoubleClickDistance;
    e19(l25, t26) > c34 ? (this._clearDoubleClickTimer(o27, true), this._startClick(e21)) : (this._clearDoubleClickTimer(o27, false), 2 === s22.pointerDownCount && this._doubleClick.emit(l25, void 0, m24));
  }
  _handlePointerDown(e21) {
    const t26 = c32(e21.data), i28 = this._pointerState.get(t26);
    i28 && (i28.pointerDownCount += 1);
  }
  _startClick(e21) {
    const { data: t26 } = e21, { native: { pointerType: i28 } } = t26, o27 = c32(t26), n27 = "touch" === i28 ? this._maximumDoubleTouchDelay : this._maximumDoubleClickDelay, l25 = this._clock.setTimeout(() => this._doubleClickTimeoutExceeded(o27), n27), m24 = 1;
    this._pointerState.set(o27, { event: e21, doubleClickTimer: l25, pointerDownCount: m24 }), this.refreshHasPendingInputs();
  }
};

// node_modules/@arcgis/core/views/input/recognizers/DoubleTapDrag.js
var p23 = class extends i10 {
  constructor(e21 = l22.maximumDoubleClickDelay, a32 = l22.maximumDoubleClickDistance, r20 = l22.maximumDoubleTouchDelay, n27 = l22.maximumDoubleTouchDistance, u18 = t6) {
    super(false), this._maximumDoubleClickDelay = e21, this._maximumDoubleClickDistance = a32, this._maximumDoubleTouchDelay = r20, this._maximumDoubleTouchDistance = n27, this._clock = u18, this._doubleTapDragReady = false, this._doubleTapDragActive = false, this._dragStartCenter = c2(0, 0), this._pointerState = /* @__PURE__ */ new Map(), this._doubleTapDrag = this.registerOutgoing("double-tap-drag"), this._dragEventSeparator = new t20({ start: (t26, e22) => this._dragStart(t26, e22), update: (t26, e22) => this._dragUpdate(e22), end: (t26, e22) => this._dragEnd(e22) }), this.registerIncoming("drag", (t26) => this._dragEventSeparator.handle(t26)), this.registerIncoming("pointer-down", (t26) => this._handlePointerDown(t26)), this.registerIncoming("pointer-up", () => this._handlePointerUp());
  }
  onUninstall() {
    this._pointerState.forEach((t26) => {
      t26.doubleTapTimeout = p(t26.doubleTapTimeout);
    });
  }
  get hasPendingInputs() {
    return n(this._pointerState, (t26) => null != t26.doubleTapTimeout);
  }
  _clearPointerDown(t26) {
    const e21 = this._pointerState.get(t26);
    e21 && (e21.doubleTapTimeout = p(e21.doubleTapTimeout), this._pointerState.delete(t26), this.refreshHasPendingInputs());
  }
  _createDoubleTapDragData(t26, e21, a32) {
    const { button: i28, buttons: o27, pointer: r20, pointers: s22, pointerType: n27, timestamp: u18 } = a32;
    return { action: t26, delta: e21, button: i28, buttons: o27, pointer: r20, pointers: s22, pointerType: n27, timestamp: u18 };
  }
  _dragStart(t26, e21) {
    if (!this._doubleTapDragReady || 1 !== t26) return;
    this._doubleTapDragReady = false, this._doubleTapDragActive = true;
    const { data: a32, modifiers: o27 } = e21, { center: r20 } = a32;
    this._dragStartCenter = r20;
    const s22 = this._createDoubleTapDragData("begin", c2(0, 0), a32);
    this._doubleTapDrag.emit(s22, void 0, o27), e21.stopPropagation();
  }
  _dragUpdate(t26) {
    if (!this._doubleTapDragActive) return;
    const { data: e21, modifiers: a32 } = t26, { center: o27 } = e21, r20 = c2(o27.x - this._dragStartCenter.x, o27.y - this._dragStartCenter.y), s22 = this._createDoubleTapDragData("update", r20, e21);
    this._doubleTapDrag.emit(s22, void 0, a32), t26.stopPropagation();
  }
  _dragEnd(t26) {
    if (!this._doubleTapDragActive) return;
    const { data: e21, modifiers: a32 } = t26, { center: o27 } = e21, r20 = c2(o27.x - this._dragStartCenter.x, o27.y - this._dragStartCenter.y), s22 = this._createDoubleTapDragData("end", r20, e21);
    this._doubleTapDrag.emit(s22, void 0, a32), this._doubleTapDragActive = false, t26.stopPropagation();
  }
  _handlePointerDown(t26) {
    const { data: e21 } = t26, a32 = c32(e21), i28 = this._pointerState.get(a32), { pointerType: o27 } = e21.native;
    if (i28) {
      const r20 = "touch" === o27 ? this._maximumDoubleTouchDistance : this._maximumDoubleClickDistance;
      this._clearPointerDown(a32), e19(i28.event.data, e21) > r20 ? this._storePointerDown(t26) : this._doubleTapDragReady = true;
    } else this._storePointerDown(t26);
  }
  _handlePointerUp() {
    this._doubleTapDragReady = false;
  }
  _storePointerDown(t26) {
    const { data: e21 } = t26, { pointerType: a32 } = e21.native, i28 = c32(e21), o27 = "touch" === a32 ? this._maximumDoubleTouchDelay : this._maximumDoubleClickDelay, r20 = this._clock.setTimeout(() => this._clearPointerDown(i28), o27);
    this._pointerState.set(i28, { event: t26, doubleTapTimeout: r20 }), this.refreshHasPendingInputs();
  }
};

// node_modules/@arcgis/core/views/input/recognizers/Drag.js
var s21 = class extends i10 {
  constructor(t26) {
    super(false), this._navigationTouch = t26, this._startStateModifiers = /* @__PURE__ */ new Set(), this._activePointerMap = /* @__PURE__ */ new Map(), this._isDragging = false, this._isCurrentDragSuppressed = false, this._drag = this.registerOutgoing("drag"), this.registerIncoming("pointer-drag", this._handlePointerDrag.bind(this)), this.registerIncoming("pointer-up", this._handlePointerUpAndPointerLost.bind(this)), this.registerIncoming("pointer-capture-lost", this._handlePointerUpAndPointerLost.bind(this)), this.registerIncoming("pointer-cancel", this._handlePointerUpAndPointerLost.bind(this));
  }
  _createPayload(t26, e21, i28, n27) {
    return { action: t26, pointerType: this._pointerType, button: this._mouseButton, buttons: e21.buttons, timestamp: n27, pointers: o24(this._activePointerMap), pointer: e21, angle: i28.angle, radius: i28.radius, center: i28.center };
  }
  _addPointer(t26) {
    const e21 = t26.native.pointerId, i28 = a28(this._activePointerMap).angle, n27 = { event: t26, initialAngle: 0, lastAngle: 0 };
    this._activePointerMap.set(e21, n27);
    const s22 = h15(n27, r17(this._activePointerMap));
    n27.initialAngle = s22, n27.lastAngle = s22, this._updatePointerAngles(i28);
  }
  _updatePointer(t26) {
    if (t26 && null == t26.x && null == t26.y) return;
    const e21 = t26.native.pointerId, i28 = this._activePointerMap.get(e21);
    i28 ? i28.event = t26 : this._addPointer(t26);
  }
  _removePointer(t26) {
    const e21 = a28(this._activePointerMap).angle;
    this._activePointerMap.delete(t26), this._updatePointerAngles(e21);
  }
  _updatePointerAngles(t26) {
    const e21 = a28(this._activePointerMap);
    this._activePointerMap.forEach((i28) => {
      i28.initialAngle = h15(i28, e21) - t26, i28.lastAngle = h15(i28, e21) - t26;
    });
  }
  _emitEvent(t26, e21, i28) {
    const n27 = a28(this._activePointerMap);
    this._drag.emit(this._createPayload(t26, e21, n27, i28), void 0, this._startStateModifiers);
  }
  _handlePointerUpAndPointerLost(t26) {
    const i28 = t26.data.native.pointerId, n27 = n3(t26.timestamp);
    this._activePointerMap.get(i28) && (1 === this._activePointerMap.size ? (this._updatePointer(t26.data), !this._isCurrentDragSuppressed && this._emitEvent("end", t26.data, n27), this._isDragging = false, this._isCurrentDragSuppressed = false, this._removePointer(i28)) : (this._removePointer(i28), this._emitEvent("removed", t26.data, n3(t26.timestamp))));
  }
  _handlePointerDrag(t26) {
    const i28 = t26.data, n27 = i28.currentEvent, s22 = n3(t26.timestamp);
    switch (i28.action) {
      case "start":
      case "update":
        this._isDragging ? this._activePointerMap.has(n27.native.pointerId) ? (this._updatePointer(n27), !this._isCurrentDragSuppressed && this._emitEvent("update", n27, s22)) : (this._addPointer(n27), this._emitEvent("added", n27, s22), this._isCurrentDragSuppressed = this._isSuppressed) : (this._updatePointer(n27), this._pointerType = t26.data.startEvent.pointerType, this._mouseButton = t26.data.startEvent.button, this._startStateModifiers = t26.modifiers, this._isDragging = true, this._isCurrentDragSuppressed = this._isSuppressed, !this._isCurrentDragSuppressed && this._emitEvent("start", n27, s22));
    }
  }
  get _isSuppressed() {
    return !!this._navigationTouch && !this._navigationTouch.browserTouchPanEnabled && "touch" === this._pointerType && 1 === this._activePointerMap.size;
  }
};
function r17(e21) {
  const i28 = [];
  return e21.forEach((e22) => {
    i28.push(c2(e22.event.x, e22.event.y));
  }), r16(i28);
}
function a28(t26) {
  const e21 = r17(t26);
  let i28 = 0;
  return t26.forEach((t27) => {
    let n27 = h15(t27, e21), s22 = n27 - t27.lastAngle;
    for (; s22 > Math.PI; ) s22 -= 2 * Math.PI;
    for (; s22 < -Math.PI; ) s22 += 2 * Math.PI;
    n27 = t27.lastAngle + s22, t27.lastAngle = n27;
    const r20 = n27 - t27.initialAngle;
    i28 += r20;
  }), i28 /= t26.size || 1, { angle: i28, radius: e21.radius, center: e21.center };
}
function o24(t26) {
  const e21 = /* @__PURE__ */ new Map();
  return t26.forEach((t27, i28) => e21.set(i28, t27.event)), e21;
}
function h15(t26, e21) {
  const i28 = t26.event, n27 = i28.x - e21.center.x, s22 = i28.y - e21.center.y;
  return Math.atan2(s22, n27);
}
var p24;
!function(t26) {
  t26[t26.Left = 0] = "Left", t26[t26.Middle = 1] = "Middle", t26[t26.Right = 2] = "Right", t26[t26.Back = 3] = "Back", t26[t26.Forward = 4] = "Forward", t26[t26.Undefined = -1] = "Undefined";
}(p24 || (p24 = {}));

// node_modules/@arcgis/core/views/input/recognizers/ImmediateDoubleClick.js
var m20 = class extends i10 {
  constructor(e21 = l22.maximumDoubleClickDelay, o27 = l22.maximumDoubleClickDistance, a32 = l22.maximumDoubleTouchDelay, m24 = l22.maximumDoubleTouchDistance, n27 = t6) {
    super(false), this._maximumDoubleClickDelay = e21, this._maximumDoubleClickDistance = o27, this._maximumDoubleTouchDelay = a32, this._maximumDoubleTouchDistance = m24, this._clock = n27, this._pointerState = /* @__PURE__ */ new Map(), this._immediateDoubleClick = this.registerOutgoing("immediate-double-click"), this.registerIncoming("pointer-down", this._handlePointerDown.bind(this)), this.registerIncoming("pointer-up", this._handlePointerUp.bind(this));
  }
  onUninstall() {
    this._pointerState.forEach((t26) => {
      t26.immediateDoubleClick && t26.immediateDoubleClick.timeoutHandle.remove();
    }), super.onUninstall();
  }
  _handlePointerDown(t26) {
    const e21 = t26.data, i28 = c32(e21);
    if (!this._pointerState.has(i28)) {
      const t27 = { downButton: e21.native.button, immediateDoubleClick: null };
      this._pointerState.set(i28, t27), this.startCapturingPointer(e21.native);
    }
  }
  _handlePointerUp(t26) {
    const e21 = t26.data, i28 = c32(e21), m24 = this._pointerState.get(i28);
    if (m24 && m24.downButton === e21.native.button) {
      const i29 = m24.immediateDoubleClick;
      if (i29) {
        i29.timeoutHandle.remove();
        const o27 = "touch" === t26.data.native.pointerType ? this._maximumDoubleTouchDistance : this._maximumDoubleClickDistance;
        e19(i29, t26.data) > o27 ? this._startImmediateDoubleClick(t26, m24) : (this._immediateDoubleClick.emit(t26.data, void 0, i29.modifiers), this._removeState(e21));
      } else this._startImmediateDoubleClick(t26, m24);
    }
  }
  _startImmediateDoubleClick(t26, e21) {
    const i28 = "touch" === t26.data.native.pointerType ? this._maximumDoubleTouchDelay : this._maximumDoubleClickDelay;
    e21.immediateDoubleClick = { x: t26.data.x, y: t26.data.y, modifiers: t26.modifiers, timeoutHandle: this._clock.setTimeout(() => this._removeState(t26.data), i28) };
  }
  _removeState(t26) {
    const e21 = c32(t26);
    this._pointerState.delete(e21), this.stopCapturingPointer(t26.native), this.refreshHasPendingInputs();
  }
};

// node_modules/@arcgis/core/views/input/recognizers/PointerClickHoldAndDrag.js
var o25 = { maximumClickDelay: 300, movementUntilMouseDrag: 1.5, movementUntilPenDrag: 6, movementUntilTouchDrag: 6, holdDelay: 500 };
var r18 = class extends i10 {
  constructor(e21 = o25.maximumClickDelay, i28 = o25.movementUntilMouseDrag, n27 = o25.movementUntilPenDrag, r20 = o25.movementUntilTouchDrag, s22 = o25.holdDelay, a32 = t6) {
    super(false), this._maximumClickDelay = e21, this._movementUntilMouseDrag = i28, this._movementUntilPenDrag = n27, this._movementUntilTouchDrag = r20, this._holdDelay = s22, this._clock = a32, this._pointerState = /* @__PURE__ */ new Map(), this._pointerDrag = this.registerOutgoing("pointer-drag"), this._immediateClick = this.registerOutgoing("immediate-click"), this._pointerHold = this.registerOutgoing("hold"), this.registerIncoming("pointer-down", this._handlePointerDown.bind(this)), this.registerIncoming("pointer-up", (t26) => {
      this._handlePointerLoss(t26, "pointer-up");
    }), this.registerIncoming("pointer-capture-lost", (t26) => {
      this._handlePointerLoss(t26, "pointer-capture-lost");
    }), this.registerIncoming("pointer-cancel", (t26) => {
      this._handlePointerLoss(t26, "pointer-cancel");
    }), this._moveHandle = this.registerIncoming("pointer-move", this._handlePointerMove.bind(this)), this._moveHandle.pause();
  }
  onUninstall() {
    this._pointerState.forEach((t26) => {
      t26.holdTimeout = p(t26.holdTimeout);
    }), super.onUninstall();
  }
  _handlePointerDown(t26) {
    const e21 = t26.data, i28 = e21.native.pointerId;
    let n27 = null;
    0 === this._pointerState.size && (n27 = this._clock.setTimeout(() => {
      const e22 = this._pointerState.get(i28);
      if (e22) {
        if (!e22.isDragging) {
          const i29 = e22.previousEvent;
          this._pointerHold.emit(i29, void 0, t26.modifiers), e22.holdEmitted = true;
        }
        e22.holdTimeout = null;
      }
    }, this._holdDelay));
    const o27 = { startEvent: e21, previousEvent: e21, startTimestamp: t26.timestamp, isDragging: false, downButton: e21.native.button, holdTimeout: n27, modifiers: /* @__PURE__ */ new Set() };
    this._pointerState.set(i28, o27), this.startCapturingPointer(e21.native), this._moveHandle.resume(), this._pointerState.size > 1 && this._startDragging(t26);
  }
  _createPointerDragData(t26, e21, i28) {
    return { action: t26, startEvent: e21.startEvent, previousEvent: e21.previousEvent, currentEvent: i28 };
  }
  _handlePointerMove(t26) {
    const e21 = t26.data, i28 = e21.native.pointerId, o27 = this._pointerState.get(i28);
    if (o27) {
      if (o27.isDragging) this._pointerDrag.emit(this._createPointerDragData("update", o27, e21), void 0, o27.modifiers);
      else {
        n26(e21, o27.startEvent) > this._getDragThreshold(e21.native.pointerType) && this._startDragging(t26);
      }
      o27.previousEvent = e21;
    }
  }
  _getDragThreshold(t26) {
    switch (t26) {
      case "touch":
        return this._movementUntilTouchDrag;
      case "pen":
        return this._movementUntilPenDrag;
      default:
        return this._movementUntilMouseDrag;
    }
  }
  _startDragging(t26) {
    const e21 = t26.data, i28 = e21.native.pointerId;
    this._pointerState.forEach((n27) => {
      null != n27.holdTimeout && (n27.holdTimeout.remove(), n27.holdTimeout = null), n27.isDragging || (n27.modifiers = t26.modifiers, n27.isDragging = true, i28 === n27.startEvent.native.pointerId ? this._pointerDrag.emit(this._createPointerDragData("start", n27, e21)) : this._pointerDrag.emit(this._createPointerDragData("start", n27, n27.previousEvent), t26.timestamp));
    });
  }
  _handlePointerLoss(t26, e21) {
    const i28 = t26.data, n27 = i28.native.pointerId, o27 = this._pointerState.get(n27);
    if (o27) {
      if (null != o27.holdTimeout && (o27.holdTimeout.remove(), o27.holdTimeout = null), o27.isDragging) this._pointerDrag.emit(this._createPointerDragData("end", o27, "pointer-up" === e21 ? i28 : o27.previousEvent), void 0, o27.modifiers);
      else if ("pointer-up" === e21 && o27.downButton === i28.native.button) {
        t26.timestamp - o27.startTimestamp <= this._maximumClickDelay && !o27.holdEmitted && this._immediateClick.emit(i28);
      }
      this._pointerState.delete(n27), this.stopCapturingPointer(i28.native), 0 === this._pointerState.size && this._moveHandle.pause();
    }
  }
};

// node_modules/@arcgis/core/views/2d/input/MapViewInputManager.js
var z6 = { counter: "Ctrl", pan: { left: "ArrowLeft", right: "ArrowRight", up: "ArrowUp", down: "ArrowDown" }, zoom: { zoomIn: ["=", "+"], zoomOut: ["-", "_"] }, rotate: { clockwiseOption1: "a", clockwiseOption2: "A", counterClockwiseOption1: "d", counterClockwiseOption2: "D", resetOption1: "n", resetOption2: "N" } };
var k3 = class extends v3 {
  constructor() {
    super(...arguments), this._handles = new t4();
  }
  initialize() {
    const e21 = () => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.ready;
    };
    this._handles.add([f3(() => !e21(), () => this._disconnect()), f3(e21, () => this._connect())]);
  }
  destroy() {
    this._handles = a(this._handles), this._disconnect();
  }
  get latestPointerType() {
    var _a;
    return (_a = this._inputManager) == null ? void 0 : _a.latestPointerType;
  }
  get latestPointerLocation() {
    var _a;
    return (_a = this._inputManager) == null ? void 0 : _a.latestPointerLocation;
  }
  get multiTouchActive() {
    var _a;
    return ((_a = this._inputManager) == null ? void 0 : _a.multiTouchActive) ?? false;
  }
  _disconnect() {
    this.view.viewEvents.disconnect(), this._inputManager = a(this._inputManager);
  }
  _connect() {
    const e21 = this.view.surface, t26 = new c31(e21, this.view.input), o27 = [new m20(), new r18(), new m19(), new s21(this.view.navigation), new p23()], r20 = new u4({ eventSource: t26, recognizers: o27 });
    r20.installHandlers("prevent-context-menu", [new e18()], P.INTERNAL), r20.installHandlers("navigation", [new a25(this.view), new d12(this.view), new o22(this.view), new a23(this.view), new a23(this.view, [z6.counter]), new n23(this.view, "primary"), new i24(this.view, z6.pan), new e16(this.view, z6.zoom), new i25(this.view, z6.rotate), new o21(this.view, "secondary"), new e15(this.view, "touch")], P.INTERNAL), this.view.viewEvents.connect(r20), this._source = t26, this._inputManager = r20, l3(() => {
      var _a, _b;
      return (_b = (_a = this.view) == null ? void 0 : _a.navigation) == null ? void 0 : _b.browserTouchPanEnabled;
    }, (e22) => {
      this._source && (this._source.browserTouchPanningEnabled = !e22);
    }, h);
  }
  get test() {
    return { inputManager: this._inputManager };
  }
};
e([y()], k3.prototype, "view", void 0), e([y()], k3.prototype, "latestPointerType", null), e([y()], k3.prototype, "latestPointerLocation", null), e([y()], k3.prototype, "multiTouchActive", null), k3 = e([a2("esri.views.2d.input.MapViewInputManager")], k3);
var C7 = k3;

// node_modules/@arcgis/core/views/support/createScreenshotPlan.js
function t24(t26, h18, i28, e21, l25, d15, n27, o27) {
  t26.ignorePadding && (e21 = { left: 0, right: 0, top: 0, bottom: 0 });
  let u18 = null;
  null != t26.width && null != t26.height ? u18 = [t26.width, t26.height] : null == t26.width && null != t26.height ? u18 = [t26.height, t26.height] : null != t26.width && null == t26.height ? u18 = [t26.width, t26.width] : null == t26.width && null == t26.height && (u18 = null);
  const r20 = i28[0] - (e21.left + e21.right), a32 = i28[1] - (e21.top + e21.bottom);
  let g16, w18, c34 = t26.area || { x: 0, y: 0, width: r20, height: a32 };
  if (u18) {
    const t27 = r20 / a32, h19 = u18[0] / u18[1];
    if (h19 > t27) {
      const t28 = c34.width / h19;
      c34 = { x: c34.x, y: Math.round(c34.y + (c34.height - t28) / 2), width: c34.width, height: Math.round(t28) };
    } else {
      const t28 = c34.height * h19;
      c34 = { x: Math.round(c34.x + (c34.width - t28) / 2), y: c34.y, width: Math.round(t28), height: c34.height };
    }
  } else u18 = [c34.width, c34.height];
  u18[0] > c34.width ? (g16 = Math.min(u18[0] / c34.width, h18), w18 = u18[0] / c34.width / g16) : (g16 = 1, w18 = u18[0] / c34.width);
  return { resolutionScale: g16, cropArea: { x: Math.round((c34.x + e21.left) * g16), y: Math.round((c34.y + e21.top) * g16), width: Math.round(c34.width * g16), height: Math.round(c34.height * g16) }, outputScale: w18, format: l25, quality: d15, children: n27, rotation: o27 };
}

// node_modules/@arcgis/core/views/support/WebGLRequirements.js
function i26(i28) {
  const s22 = l8(i28);
  if (!s22.available) return new s3("webgl:required", "WebGL is required but not supported.");
  if ("3d" === i28 && s22.majorPerformanceCaveat) return new s3("webgl:major-performance-caveat-detected", "Your WebGL implementation doesn't seem to support hardware accelerated rendering. Check your browser settings or if your GPU is in a blocklist.");
  if (!s22.supportsHighPrecisionFragment) return new s3("webgl:high-precision-fragment-required", "WebGL support for high precision fragment shaders is required but not supported.");
  if (!s22.supportsVertexShaderSamplers) return new s3("webgl:vertex-shader-samplers-required", "WebGL support for vertex shader samplers is required but not supported.");
  if (s22.type === r7.WEBGL1) {
    if (!s22.supportsElementIndexUint) return new s3("webgl:element-index-uint-required", "WebGL support for uint vertex indices is required but not supported.");
    if (!s22.supportsStandardDerivatives) return new s3("webgl:standard-derivatives-required", "WebGL support for standard derivatives is required but not supported.");
    if (!s22.supportsInstancedArrays) return new s3("webgl:instanced-arrays-required", "WebGL support for instanced rendering is required but not supported.");
  }
  return null;
}

// node_modules/@arcgis/core/views/ui/Component.js
function i27(t26) {
  return t26 && "nodeType" in t26;
}
function d13(t26) {
  return t26 && "function" == typeof t26.render;
}
var c33 = { component: "esri-component" };
var p25 = class extends v3 {
  constructor() {
    super(...arguments), this.widget = null;
  }
  destroy() {
    this.widget && this.widget.destroy(), this.node = null;
  }
  get id() {
    return this.get("widget.id") || this.get("node.id");
  }
  set node(t26) {
    const e21 = this._get("node");
    t26 !== e21 && (t26 && t26.classList.add(c33.component), e21 && e21.classList.remove(c33.component), this._set("node", t26));
  }
  castNode(t26) {
    return t26 ? "string" == typeof t26 || i27(t26) ? (this._set("widget", null), e3(t26)) : (d13(t26) && !t26.domNode && (t26.domNode = document.createElement("div")), this._set("widget", t26), t26.domNode) : (this._set("widget", null), null);
  }
};
e([y({ dependsOn: [] })], p25.prototype, "id", null), e([y()], p25.prototype, "node", null), e([s4("node")], p25.prototype, "castNode", null), e([y({ readOnly: true })], p25.prototype, "widget", void 0), p25 = e([a2("esri.views.ui.Component")], p25);
var l23 = p25;

// node_modules/@arcgis/core/views/ui/UI.js
var u16 = { left: 0, top: 0, bottom: 0, right: 0 };
var f15 = { bottom: 30, top: 15, right: 15, left: 15 };
var _7 = "manual";
var g13 = "esri-ui";
var y14 = { ui: g13, corner: `${g13}-corner`, innerContainer: `${g13}-inner-container`, manualContainer: `${g13}-manual-container`, cornerContainer: `${g13}-corner-container`, topLeft: `${g13}-top-left`, topRight: `${g13}-top-right`, bottomLeft: `${g13}-bottom-left`, bottomRight: `${g13}-bottom-right` };
function C8(t26) {
  return t26 && !t26._started && "function" == typeof t26.postMixInProperties && "function" == typeof t26.buildRendering && "function" == typeof t26.postCreate && "function" == typeof t26.startup;
}
function v15(t26) {
  const o27 = t26, i28 = "object" == typeof o27 && null !== o27 && Object.getPrototypeOf(o27);
  return (null === i28 || i28 === Object.prototype) && ("component" in o27 || "index" in o27 || "position" in o27) ? t26 : null;
}
function L7(t26, { top: o27, bottom: i28, left: n27, right: e21 }) {
  t26.style.top = o27, t26.style.bottom = i28, t26.style.left = n27, t26.style.right = e21;
}
var b14 = class extends n4.EventedAccessor {
  constructor(t26) {
    super(t26), this._cornerNameToContainerLookup = {}, this._positionNameToContainerLookup = {}, this._components = new Array(), this._componentToKey = /* @__PURE__ */ new Map(), this._locale = l2(), this.view = null, this._applyViewPadding = () => {
      const t27 = this.container;
      t27 && L7(t27, this._toPxPosition(this._getViewPadding()));
    }, this._applyUIPadding = () => {
      const t27 = this._innerContainer;
      t27 && L7(t27, this._toPxPosition(this.padding));
    }, this._initContainers();
  }
  initialize() {
    this.addHandles([l3(() => {
      var _a;
      return [(_a = this.view) == null ? void 0 : _a.padding, this.container];
    }, this._applyViewPadding, h), l3(() => this.padding, this._applyUIPadding, h), l3(() => [this.container, this._locale], ([t26, o27]) => {
      t26 && t26.setAttribute("lang", o27);
    }, h), s5((t26) => {
      this._locale = t26;
    })]);
  }
  destroy() {
    this.container = null;
    for (const t26 of this._components) t26.destroy();
    this._components.length = 0, this._componentToKey.clear();
  }
  set container(t26) {
    const i28 = this._get("container");
    t26 !== i28 && (t26 && (t26.classList.add(y14.ui), n6(t26), this._attachContainers(t26)), i28 && (i28.classList.remove(y14.ui), L7(i28, { top: "", bottom: "", left: "", right: "" }), t7(i28)), this._set("container", t26));
  }
  get height() {
    const t26 = this.get("view.height") || 0;
    if (0 === t26) return t26;
    const o27 = this._getViewPadding(), i28 = o27.top + o27.bottom;
    return Math.max(t26 - i28, 0);
  }
  get padding() {
    return this._get("padding");
  }
  set padding(t26) {
    this._overrideIfSome("padding", t26);
  }
  castPadding(t26) {
    return "number" == typeof t26 ? { bottom: t26, top: t26, right: t26, left: t26 } : { ...f15, ...t26 };
  }
  get width() {
    const t26 = this.get("view.width") || 0;
    if (0 === t26) return t26;
    const o27 = this._getViewPadding(), i28 = o27.left + o27.right;
    return Math.max(t26 - i28, 0);
  }
  add(t26, o27) {
    let i28, n27;
    if (Array.isArray(t26)) return void t26.forEach((t27) => this.add(t27, o27));
    const e21 = v15(t26);
    e21 && ({ index: i28, position: o27, component: t26, key: n27 } = e21), o27 && "object" == typeof o27 && ({ index: i28, key: n27, position: o27 } = o27), !t26 || o27 && !this._isValidPosition(o27) || this._add(t26, o27, i28, n27);
  }
  remove(t26, o27) {
    if (!t26) return;
    if (Array.isArray(t26)) return t26.map((t27) => this.remove(t27, o27));
    const i28 = this._find(t26);
    if (i28) {
      const n27 = this._componentToKey;
      if (n27.has(t26) && n27.get(t26) !== o27) return;
      const e21 = this._components.indexOf(i28);
      return i28.node.parentNode && i28.node.parentNode.removeChild(i28.node), this._componentToKey.delete(t26), this._components.splice(e21, 1)[0];
    }
  }
  empty(t26) {
    if (Array.isArray(t26)) return t26.map((t27) => this.empty(t27)).reduce((t27, o27) => t27.concat(o27));
    if ((t26 = t26 || _7) === _7) {
      return Array.prototype.slice.call(this._manualContainer.children).filter((t27) => !t27.classList.contains(y14.corner)).map((t27) => this.remove(t27));
    }
    return this._isValidPosition(t26) ? Array.prototype.slice.call(this._cornerNameToContainerLookup[t26].children).map(this.remove, this) : null;
  }
  move(t26, o27) {
    if (Array.isArray(t26) && t26.forEach((t27) => this.move(t27, o27)), !t26) return;
    let i28;
    const n27 = v15(t26) || v15(o27);
    if (n27 && (i28 = n27.index, o27 = n27.position, t26 = n27.component || t26), o27 && !this._isValidPosition(o27)) return;
    const e21 = this.remove(t26);
    e21 && this.add(e21, { position: o27, index: i28 });
  }
  find(t26) {
    if (!t26) return null;
    const o27 = this._findById(t26);
    return o27 && (o27.widget || o27.node);
  }
  getPosition(t26) {
    for (const o27 in this._positionNameToContainerLookup) {
      if (this._positionNameToContainerLookup[o27].contains(t26)) return o27;
    }
    return null;
  }
  _add(t26, o27, i28, n27) {
    t26 instanceof l23 || (t26 = new l23({ node: t26 })), this._place({ component: t26, position: o27, index: i28 }), this._components.push(t26), n27 && this._componentToKey.set(t26, n27);
  }
  _find(t26) {
    return t26 ? t26 instanceof l23 ? this._findByComponent(t26) : "string" == typeof t26 ? this._findById(t26) : this._findByNode(t26.domNode || t26) : null;
  }
  _getViewPadding() {
    return this.get("view.padding") || u16;
  }
  _attachContainers(t26) {
    t26.appendChild(this._innerContainer), t26.appendChild(this._manualContainer);
  }
  _initContainers() {
    const t26 = document.createElement("div");
    t26.classList.add(y14.innerContainer), t26.classList.add(y14.cornerContainer);
    const o27 = document.createElement("div");
    o27.classList.add(y14.innerContainer), o27.classList.add(y14.manualContainer);
    const i28 = document.createElement("div");
    i28.classList.add(y14.topLeft), i28.classList.add(y14.corner), t26.appendChild(i28);
    const n27 = document.createElement("div");
    n27.classList.add(y14.topRight), n27.classList.add(y14.corner), t26.appendChild(n27);
    const e21 = document.createElement("div");
    e21.classList.add(y14.bottomLeft), e21.classList.add(y14.corner), t26.appendChild(e21);
    const r20 = document.createElement("div");
    r20.classList.add(y14.bottomRight), r20.classList.add(y14.corner), t26.appendChild(r20), this._innerContainer = t26, this._manualContainer = o27;
    const s22 = f4();
    this._cornerNameToContainerLookup = { "top-left": i28, "top-right": n27, "bottom-left": e21, "bottom-right": r20, "top-leading": s22 ? n27 : i28, "top-trailing": s22 ? i28 : n27, "bottom-leading": s22 ? r20 : e21, "bottom-trailing": s22 ? e21 : r20 }, this._positionNameToContainerLookup = { manual: o27, ...this._cornerNameToContainerLookup };
  }
  _isValidPosition(t26) {
    return !!this._positionNameToContainerLookup[t26];
  }
  _place(t26) {
    const o27 = t26.component, n27 = t26.position || _7, e21 = t26.index, r20 = this._positionNameToContainerLookup[n27], s22 = null != e21 && e21 > -1;
    if (C8(o27.widget) && o27.widget.startup(), !s22) return void r20.appendChild(o27.node);
    const a32 = Array.prototype.slice.call(r20.children);
    if (0 === e21) return void (r20.firstChild ? o4(o27.node, r20.firstChild) : r20.appendChild(o27.node));
    e21 >= a32.length ? r20.appendChild(o27.node) : o4(o27.node, a32[e21]);
  }
  _toPxPosition(t26) {
    return { top: this._toPxUnit(t26.top), left: this._toPxUnit(t26.left), right: this._toPxUnit(t26.right), bottom: this._toPxUnit(t26.bottom) };
  }
  _toPxUnit(t26) {
    return 0 === t26 ? "0" : t26 + "px";
  }
  _findByComponent(t26) {
    let o27, i28 = null;
    return this._components.some((n27) => (o27 = n27 === t26, o27 && (i28 = n27), o27)), i28;
  }
  _findById(t26) {
    let o27, i28 = null;
    return this._components.some((n27) => (o27 = n27.id === t26, o27 && (i28 = n27), o27)), i28;
  }
  _findByNode(t26) {
    let o27, i28 = null;
    return this._components.some((n27) => (o27 = n27.node === t26, o27 && (i28 = n27), o27)), i28;
  }
};
e([y()], b14.prototype, "_locale", void 0), e([y()], b14.prototype, "container", null), e([y()], b14.prototype, "height", null), e([y({ value: f15 })], b14.prototype, "padding", null), e([s4("padding")], b14.prototype, "castPadding", null), e([y()], b14.prototype, "view", void 0), e([y()], b14.prototype, "width", null), b14 = e([a2("esri.views.ui.UI")], b14);
var P3 = b14;

// node_modules/@arcgis/core/widgets/Attribution/AttributionViewModel.js
function d14(t26, e21) {
  return t26 && "copyright" in t26 && (!e21 || "function" == typeof t26.originOf && "user" === t26.originOf("copyright"));
}
function m21(t26, e21) {
  return t26.length !== e21.length || t26.some((t27, i28) => t27.text !== e21[i28].text);
}
function f16(t26, e21, i28) {
  if (!i28 || !e21) return;
  t26.find((t27) => t27.layerView === e21 && t27.text === i28) || t26.push({ text: i28, layerView: e21 });
}
function y15(t26) {
  return "bing-maps" === t26.type;
}
var b15 = [];
var g14 = class extends d {
  constructor(t26) {
    super(t26), this._clear = () => {
      this._fetchedAttributionData.clear(), this._pendingAttributions.clear(), this.handles.remove("suspension"), this.notifyChange("state");
    }, this._pendingAttributions = /* @__PURE__ */ new Set(), this._fetchedAttributionData = /* @__PURE__ */ new Map(), this.items = new j2(), this.view = null, this._allLayerViewsChange = (t27) => {
      this.handles.remove("suspension");
      const e21 = this.get("view.allLayerViews");
      e21 && this.handles.add(e21.map((t28) => l3(() => {
        var _a;
        return [t28.suspended, (_a = t28.layer) == null ? void 0 : _a.attributionVisible];
      }, () => this._updateAttributionItems())), "suspension"), t27 && t27.removed && t27.removed.forEach((t28) => {
        this._pendingAttributions.delete(t28), this._fetchedAttributionData.delete(t28);
      }), this._updateAttributionItems();
    }, this.handles.add([a6(() => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.allLayerViews;
    }, "change", (t27) => this._allLayerViewsChange(t27), { onListenerAdd: () => this._allLayerViewsChange(), onListenerRemove: this._clear }), f3(() => {
      var _a;
      return true === ((_a = this.view) == null ? void 0 : _a.stationary);
    }, () => this._updateAttributionItems())]);
  }
  destroy() {
    this.view = null, this._fetchedAttributionData.clear(), this._pendingAttributions.clear(), this.items.removeAll();
  }
  get state() {
    return this.get("view.ready") ? this._pendingAttributions.size > 0 ? "loading" : "ready" : "disabled";
  }
  _updateAttributionItems() {
    const t26 = this.view, e21 = t26 == null ? void 0 : t26.allLayerViews;
    b15.length = 0, t26 && e21 ? (e21.forEach((e22) => {
      var _a;
      if (e22.suspended || !((_a = e22.layer) == null ? void 0 : _a.attributionVisible)) return;
      const i28 = e22.layer;
      if (d14(i28, "user")) return void f16(b15, e22, i28.copyright);
      if (i28.hasAttributionData) {
        if (this._fetchedAttributionData.has(e22)) {
          const r21 = this._fetchedAttributionData.get(e22);
          return void (r21 ? f16(b15, e22, this._getDynamicAttribution(r21, t26, i28)) : d14(i28) && f16(b15, e22, i28.copyright));
        }
        return void this._fetchAttributionData(e22);
      }
      const r20 = i28.get("portalItem.accessInformation");
      f16(b15, e22, r20 || i28.copyright);
    }), m21(this.items, b15) && (this.items.removeAll(), this.items.addMany(b15)), b15.length = 0, this.notifyChange("state")) : this._clear();
  }
  async _fetchAttributionData(t26) {
    if (this._pendingAttributions.has(t26)) return;
    this._pendingAttributions.add(t26);
    const i28 = await b3(t26.layer.fetchAttributionData());
    if (this._pendingAttributions.has(t26)) {
      const e21 = i28.ok ? this._createContributionIndex(i28.value, y15(t26.layer)) : null;
      this._pendingAttributions.delete(t26), this._fetchedAttributionData.set(t26, e21);
    }
    this._updateAttributionItems();
  }
  _createContributionIndex(t26, e21) {
    const i28 = t26.contributors, r20 = {};
    if (!i28) return r20;
    for (let o27 = 0; o27 < i28.length; o27++) {
      const t27 = i28[o27], s22 = t27.coverageAreas;
      if (!s22) return;
      for (const i29 of s22) {
        const s23 = i29.bbox, n27 = i29.zoomMin - (e21 && i29.zoomMin ? 1 : 0), a32 = i29.zoomMax - (e21 && i29.zoomMax ? 1 : 0), c34 = { xmin: s23[1], ymin: s23[0], xmax: s23[3], ymax: s23[2], spatialReference: f2.WGS84 }, u18 = { extent: R(c34), attribution: t27.attribution || "", score: null != i29.score ? i29.score : 100, id: o27 };
        for (let t28 = n27; t28 <= a32; t28++) r20[t28] = r20[t28] || [], r20[t28].push(u18);
      }
    }
    return r20.maxKey = Math.max.apply(null, Object.keys(r20)), r20;
  }
  _getDynamicAttribution(t26, e21, i28) {
    var _a;
    const { extent: r20, scale: o27 } = e21;
    let s22 = ((_a = i28.tileInfo) == null ? void 0 : _a.scaleToZoom(o27)) ?? 0;
    if (s22 = Math.min(t26.maxKey ?? 0, Math.round(s22)), !r20 || null == s22 || s22 <= -1) return "";
    const n27 = t26[s22], a32 = M(r20.center.clone().normalize(), e21.spatialReference), c34 = {};
    return n27 ? n27.filter((t27) => {
      const e22 = t27.id, i29 = !c34[e22] && a32 && t27.extent && r3(t27.extent, a32);
      return i29 && (c34[e22] = true), i29;
    }).sort((t27, e22) => e22.score - t27.score || t27.objectId - e22.objectId).map((t27) => t27.attribution).join(", ") : "";
  }
};
e([y({ readOnly: true, type: j2 })], g14.prototype, "items", void 0), e([y({ readOnly: true })], g14.prototype, "state", null), e([y()], g14.prototype, "view", void 0), g14 = e([a2("esri.widgets.Attribution.AttributionViewModel")], g14);
var _8 = g14;

// node_modules/@arcgis/core/widgets/Attribution.js
var l24 = "esri-attribution";
var p26 = { base: `${l24} esri-widget`, poweredBy: `${l24}__powered-by`, sources: `${l24}__sources`, open: `${l24}--open`, sourcesOpen: `${l24}__sources--open`, link: `${l24}__link`, widgetIcon: "esri-icon-description", interactive: "esri-interactive" };
var a29 = class extends W {
  constructor(e21, t26) {
    super(e21, t26), this._isOpen = false, this._attributionTextOverflowed = false, this._prevSourceNodeHeight = 0, this._resizeObserver = new ResizeObserver((e22) => e22.forEach(({ target: e23 }) => this._checkSourceTextOverflow(e23))), this.iconClass = p26.widgetIcon, this.itemDelimiter = " | ", this.messages = null, this.viewModel = new _8();
  }
  initialize() {
    this.addHandles(a6(() => {
      var _a;
      return (_a = this.viewModel) == null ? void 0 : _a.items;
    }, "change", () => this.scheduleRender()));
  }
  destroy() {
    var _a;
    (_a = this._resizeObserver) == null ? void 0 : _a.disconnect();
  }
  get _isInteractive() {
    return this._isOpen || this._attributionTextOverflowed;
  }
  get attributionText() {
    return this.viewModel.items.reduce((e21, t26) => (e21.includes(t26.text) || e21.push(t26.text), e21), []).join(this.itemDelimiter);
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(e21) {
    this._overrideIfSome("label", e21);
  }
  get view() {
    return this.viewModel.view;
  }
  set view(e21) {
    this.viewModel.view = e21;
  }
  render() {
    const e21 = { [p26.open]: this._isOpen };
    return n5("div", { bind: this, class: this.classes(p26.base, e21), dir: "ltr", onclick: this._toggleState, onkeydown: this._toggleState }, this.renderSourcesNode(), this.renderPoweredBy());
  }
  renderPoweredBy() {
    return n5("div", { class: p26.poweredBy }, "Powered by", " ", n5("a", { class: p26.link, href: "http://www.esri.com/", target: "_blank", rel: "noreferrer" }, "Esri"));
  }
  renderSourcesNode() {
    const e21 = this._isOpen, t26 = this._isInteractive, i28 = t26 ? "0" : "", { attributionText: r20 } = this, s22 = { [p26.sourcesOpen]: e21, [p26.interactive]: t26 };
    return n5("div", { afterCreate: this._afterSourcesNodeCreate, bind: this, class: this.classes(p26.sources, s22), innerHTML: r20, tabindex: i28 });
  }
  _afterSourcesNodeCreate(e21) {
    this._prevSourceNodeHeight = e21.clientWidth, this._resizeObserver.observe(e21);
  }
  _checkSourceTextOverflow(e21) {
    let t26 = false;
    const { clientHeight: i28, clientWidth: r20, scrollWidth: s22 } = e21, o27 = s22 > r20, n27 = this._attributionTextOverflowed !== o27;
    if (this._attributionTextOverflowed = o27, n27 && (t26 = true), this._isOpen) {
      const e22 = i28 < this._prevSourceNodeHeight;
      this._prevSourceNodeHeight = i28, e22 && (this._isOpen = false, t26 = true);
    }
    t26 && this.scheduleRender();
  }
  _toggleState() {
    this._isInteractive && (this._isOpen = !this._isOpen);
  }
};
e([y()], a29.prototype, "_isOpen", void 0), e([y()], a29.prototype, "_isInteractive", null), e([y()], a29.prototype, "_attributionTextOverflowed", void 0), e([y()], a29.prototype, "_prevSourceNodeHeight", void 0), e([y({ readOnly: true, dependsOn: ["viewModel.items.length", "itemDelimiter"] })], a29.prototype, "attributionText", null), e([y()], a29.prototype, "iconClass", void 0), e([y()], a29.prototype, "itemDelimiter", void 0), e([y()], a29.prototype, "label", null), e([y(), e4("esri/widgets/Attribution/t9n/Attribution")], a29.prototype, "messages", void 0), e([y()], a29.prototype, "view", null), e([y({ type: _8 })], a29.prototype, "viewModel", void 0), e([t8()], a29.prototype, "_toggleState", null), a29 = e([a2("esri.widgets.Attribution")], a29);
var h16 = a29;

// node_modules/@arcgis/core/widgets/NavigationToggle/css.js
var t25 = "esri-navigation-toggle";
var o26 = { base: `${t25} esri-widget`, button: `${t25}__button esri-widget--button`, activeButton: `${t25}__button--active`, panButton: `${t25}__button--pan`, rotateButton: `${t25}__button--rotate`, isLayoutHorizontal: `${t25}--horizontal`, rotationIcon: "esri-icon-rotate", panIcon: "esri-icon-pan", widgetIcon: "esri-icon-pan2", disabled: "esri-disabled" };

// node_modules/@arcgis/core/widgets/NavigationToggle/NavigationToggleViewModel.js
var r19 = class extends v3 {
  constructor(t26) {
    super(t26), this.navigationMode = "pan", this.view = null;
  }
  initialize() {
    this.own(f3(() => {
      var _a;
      return (_a = this.view) == null ? void 0 : _a.inputManager;
    }, () => this._setNavigationMode()));
  }
  destroy() {
    this.view = null;
  }
  get state() {
    var _a;
    return this.get("view.ready") && "3d" === ((_a = this.view) == null ? void 0 : _a.type) ? "ready" : "disabled";
  }
  toggle() {
    "disabled" !== this.state && (this.navigationMode = "pan" !== this.navigationMode ? "pan" : "rotate", this._setNavigationMode());
  }
  _setNavigationMode() {
    this.get("view.inputManager").primaryDragAction = "pan" === this.navigationMode ? "pan" : "rotate";
  }
};
e([y({ readOnly: true })], r19.prototype, "state", null), e([y()], r19.prototype, "navigationMode", void 0), e([y()], r19.prototype, "view", void 0), r19 = e([a2("esri.widgets.NavigationToggleViewModel")], r19);
var a30 = r19;

// node_modules/@arcgis/core/widgets/NavigationToggle.js
var p27 = class extends W {
  constructor(t26, e21) {
    super(t26, e21), this.iconClass = o26.widgetIcon, this.messages = null, this.viewModel = new a30();
  }
  get label() {
    var _a;
    return ((_a = this.messages) == null ? void 0 : _a.widgetLabel) ?? "";
  }
  set label(t26) {
    this._overrideIfSome("label", t26);
  }
  set layout(t26) {
    "horizontal" !== t26 && (t26 = "vertical"), this._set("layout", t26);
  }
  get view() {
    return this.viewModel.view;
  }
  set view(t26) {
    this.viewModel.view = t26;
  }
  toggle() {
    return this.viewModel.toggle();
  }
  render() {
    const t26 = "disabled" === this.get("viewModel.state"), e21 = "pan" === this.get("viewModel.navigationMode"), o27 = { [o26.disabled]: t26, [o26.isLayoutHorizontal]: "horizontal" === this.layout }, s22 = { [o26.activeButton]: e21 }, r20 = { [o26.activeButton]: !e21 }, a32 = t26 ? -1 : 0, l25 = this.messages.toggle;
    return n5("div", { bind: this, class: this.classes(o26.base, o27), onclick: this._toggle, onkeydown: this._toggle, tabIndex: a32, "aria-label": l25, title: l25 }, n5("div", { class: this.classes(o26.button, o26.panButton, s22) }, n5("span", { class: o26.panIcon })), n5("div", { class: this.classes(o26.button, o26.rotateButton, r20) }, n5("span", { class: o26.rotationIcon })));
  }
  _toggle() {
    this.toggle();
  }
};
e([y()], p27.prototype, "iconClass", void 0), e([y()], p27.prototype, "label", null), e([y({ value: "vertical" })], p27.prototype, "layout", null), e([y(), e4("esri/widgets/NavigationToggle/t9n/NavigationToggle")], p27.prototype, "messages", void 0), e([y()], p27.prototype, "view", null), e([y({ type: a30 })], p27.prototype, "viewModel", void 0), e([t8()], p27.prototype, "_toggle", null), p27 = e([a2("esri.widgets.NavigationToggle")], p27);
var g15 = p27;

// node_modules/@arcgis/core/views/ui/DefaultUI.js
function m22(t26) {
  return t26 && void 0 !== t26.view;
}
var h17 = class extends P3 {
  constructor(t26) {
    super(t26), this._defaultPositionLookup = { attribution: "manual", compass: "top-left", "navigation-toggle": "top-left", zoom: "top-left" }, this.components = [];
  }
  initialize() {
    this.addHandles([l3(() => this.components, this._componentsWatcher.bind(this), h), l3(() => this.view, this._updateViewAwareWidgets.bind(this), h)]);
  }
  _add(t26, o27, e21, i28) {
    let s22 = t26;
    if ("string" == typeof t26 && this._defaultPositionLookup[t26]) {
      if (this._find(t26)) return;
      s22 = this._createComponent(t26);
    }
    super._add(s22, o27, e21, i28);
  }
  _removeComponents(t26) {
    t26.forEach((t27) => {
      const o27 = this._find(t27);
      o27 && (this.remove(o27), o27.destroy());
    });
  }
  _updateViewAwareWidgets(t26) {
    this.components.forEach((o27) => {
      const e21 = this._find(o27), i28 = e21 && e21.widget;
      m22(i28) && (i28.view = t26);
    });
  }
  _componentsWatcher(t26, o27) {
    this._removeComponents(o27), this._addComponents(t26), this._adjustPadding(t26);
  }
  _adjustPadding(t26) {
    if (!t26.includes("attribution") && !this._isOverridden("padding")) {
      const { top: t27 } = this.padding;
      this.padding = t27;
    }
  }
  _addComponents(t26) {
    this.constructed && t26.forEach((t27) => this.add(this._createComponent(t27), this._defaultPositionLookup[t27]));
  }
  _createComponent(t26) {
    const o27 = this._createWidget(t26);
    if (o27) return new l23({ id: t26, node: o27 });
  }
  _createWidget(t26) {
    return "attribution" === t26 ? this._createAttribution() : "compass" === t26 ? this._createCompass() : "navigation-toggle" === t26 ? this._createNavigationToggle() : "zoom" === t26 ? this._createZoom() : void 0;
  }
  _createAttribution() {
    return new h16({ view: this.view });
  }
  _createCompass() {
    return new d4({ view: this.view });
  }
  _createNavigationToggle() {
    return new g15({ view: this.view });
  }
  _createZoom() {
    return new c11({ view: this.view });
  }
};
e([y()], h17.prototype, "components", void 0), h17 = e([a2("esri.views.ui.DefaultUI")], h17);
var u17 = h17;

// node_modules/@arcgis/core/views/ui/2d/DefaultUI2D.js
var e20 = class extends u17 {
  constructor(o27) {
    super(o27), this.components = ["attribution", "zoom"];
  }
};
e([y()], e20.prototype, "components", void 0), e20 = e([a2("esri.views.ui.2d.DefaultUI2D")], e20);
var p28 = e20;

// node_modules/@arcgis/core/webmap/background/ColorBackground.js
var p29;
var a31 = p29 = class extends l {
  constructor(o27) {
    super(o27), this.color = new l4([0, 0, 0, 1]);
  }
  clone() {
    return new p29(p2({ color: this.color }));
  }
};
e([y({ type: l4, json: { write: true } })], a31.prototype, "color", void 0), a31 = p29 = e([a2("esri.webmap.background.ColorBackground")], a31);
var m23 = a31;

// node_modules/@arcgis/core/views/MapView.js
var pe2;
var he3;
var ce2;
var de2;
var ue2;
var ge2;
async function me2() {
  const [, { GraphicsView2D: e21, GraphicContainer: t26, LabelManager: i28, MapViewNavigation: s22, MagnifierView2D: r20, Stage: n27 }] = await Promise.all([import("./webglDeps-6VYOSYQV.js"), import("./mapViewDeps-IXGGSY2J.js")]);
  he3 = e21, ce2 = t26, de2 = i28, ue2 = s22, ge2 = r20, pe2 = n27;
}
var ye2 = 160;
function we(e21) {
  return e21 && "esri.Viewpoint" === e21.declaredClass;
}
var fe2 = class extends w7(z4(a16(A6))) {
  constructor(e21) {
    super(e21), this._magnifierView = null, this._stage = null, this._resolveWhenReady = [], this.rootLayerViews = new l5({ getCollections: () => {
      var _a, _b;
      return [(_a = this.basemapView) == null ? void 0 : _a.baseLayerViews, this.layerViews, (_b = this.basemapView) == null ? void 0 : _b.referenceLayerViews];
    }, getChildrenFunction: () => null }), this.featuresTilingScheme = null, this.fullOpacity = 1, this.graphicsView = null, this.labelManager = null, this.mapViewNavigation = null, this.renderingOptions = { samplingMode: "dynamic", edgeLabelsVisible: true, labelsAnimationTime: 125, labelCollisionsEnabled: true }, this.rendering = false, this.supersampleScreenshotsEnabled = true, this.supportsGround = false, this._stationaryTimer = null, this._gotoTask = null, this.frameTask = new s16(this), this._pePromise = null, this.floors = new j2(), this.highlightOptions = new c10(), this.inputManager = new C7({ view: this }), this.map = null, this.resizeAlign = "center", this.spatialReferenceLocked = false, this.timeline = new e8(), this.type = "2d", this.ui = new p28(), this._pixelFormat = { flipY: true, premultipliedAlpha: true }, this.constraints = new u13(), this.padding = { top: 0, right: 0, bottom: 0, left: 0 }, this.handles.add([l3(() => this.viewpoint, () => {
      this._lastStationaryEventTimestamp = performance.now(), this._flipStationary(ye2);
    }, U2), this.on("resize", (e22) => this._resizeHandler(e22)), l3(() => {
      var _a;
      return (_a = this.animationManager) == null ? void 0 : _a.animation;
    }, (e22) => {
      this.animation = e22;
    })]), c8();
  }
  destroy() {
    this._set("preconditionsReady", false), this._gotoTask = this.frameTask = null, this.rootLayerViews.destroy(), this.inputManager.destroy(), this._set("inputManager", null);
  }
  get graphicsTileStore() {
    return new d3(this.featuresTilingScheme);
  }
  get initialExtentRequired() {
    var _a;
    const { scale: e21, constraints: t26, center: i28, viewpoint: s22, extent: r20 } = this;
    let n27 = this.zoom;
    return !(this.map && "initialViewProperties" in this.map && ((_a = this.map.initialViewProperties) == null ? void 0 : _a.viewpoint)) && (!r20 && ((t26 == null ? void 0 : t26.effectiveLODs) || (n27 = -1), (!i28 || 0 === e21 && -1 === n27) && (!s22 || !r(s22.targetGeometry) || "extent" !== s22.targetGeometry.type && !s22.scale)));
  }
  get resourceManager() {
    return this._stage.resourceManager;
  }
  get textureManager() {
    return this._stage.painter.textureManager;
  }
  get _defaultsFromMapSettings() {
    return { required: { tileInfo: true, heightModelInfo: false, extent: false }, requiresExtentInSpatialReference: this.spatialReferenceLocked };
  }
  get _projectionEngineLoaded() {
    return !!en() || (this._pePromise || (this._pePromise = tn().finally(() => {
      this._pePromise = null;
    })), false);
  }
  get typeSpecificPreconditionsReady() {
    const e21 = this._getDefaultViewpoint();
    if (!e21) return false;
    const t26 = this.spatialReference, i28 = e2(e21.targetGeometry);
    return !!An(i28.spatialReference, t26) || this._projectionEngineLoaded;
  }
  set animation(e21) {
    const t26 = this._get("animation");
    if (e21 === t26) return;
    if (t26 && t26.stop(), !e21 || e21.isFulfilled()) return void this._set("animation", null);
    this._set("animation", e21), this.frameTask.animationInProgress = true;
    const i28 = () => {
      var _a;
      e21 === this._get("animation") && (this._set("animation", null), (_a = this.frameTask) == null ? void 0 : _a.requestFrame()), this.frameTask && (this.frameTask.animationInProgress = false);
    };
    e21.when(i28, i28);
  }
  get background() {
    return Te(this.map) ? this.map.initialViewProperties.background : null;
  }
  set background(e21) {
    this._override("background", e21);
  }
  get center() {
    if (!this.ready) return this._get("center");
    const { center: e21, spatialReference: t26 } = this.state.paddedViewState;
    return new w2({ x: e21[0], y: e21[1], spatialReference: t26 });
  }
  set center(e21) {
    if (null == e21) return;
    if (!this.ready) return this._set("center", e21), void this.notifyChange("initialExtentRequired");
    let t26;
    try {
      t26 = this._project(e21, this.spatialReference);
    } catch (s22) {
      return void s2.getLogger(this.declaredClass).error(new s3("mapview:invalid-center", "could not project the value in the view's spatial reference", { input: e21, error: s22 }));
    }
    const i28 = this.viewpoint;
    xt(i28, i28, t26), this.viewpoint = i28;
  }
  set constraints(e21) {
    const t26 = this._get("constraints");
    t26 && (this.handles.remove("map-view-constraints"), t26.destroy()), this._set("constraints", e21), e21 && (e21.view = this, this.ready && (this.state.viewpoint = e21.fit(this.state.paddedViewState.viewpoint)), this.handles.add(l3(() => e21.version, () => {
      this.ready && this.state && (this.state.viewpoint = e21.fit(this.state.paddedViewState.viewpoint));
    }, U2), "map-view-constraints"));
  }
  get extent() {
    return this.ready ? this.state.paddedViewState.extent.clone() : this._get("extent");
  }
  set extent(e21) {
    if (null == e21) return;
    if (!e21.width || !e21.height) return void s2.getLogger(this.declaredClass).error(new s3("mapview:invalid-extent", "invalid extent size"));
    if (!this.ready) return this._set("extent", e21), this._set("center", null), this._set("viewpoint", null), this._set("scale", 0), this._set("zoom", -1), void this.notifyChange("initialExtentRequired");
    let t26;
    try {
      t26 = this._project(e21, this.spatialReference);
    } catch (s22) {
      return void s2.getLogger(this.declaredClass).error(new s3("mapview:invalid-extent", "could not project the value in the view's spatial reference", { error: s22 }));
    }
    const i28 = this.viewpoint;
    tt(i28, i28, t26, this.size, { constraints: this.constraints }), this.viewpoint = i28;
  }
  get padding() {
    return this.ready ? this.state.padding : this._get("padding");
  }
  set padding(e21) {
    this.ready ? (this.state.padding = e21, this._set("padding", this.state.padding)) : this._set("padding", e21);
  }
  get resolution() {
    return this.state ? this.state.resolution : 0;
  }
  get rotation() {
    return this.ready ? this.state.rotation : this._get("rotation");
  }
  set rotation(e21) {
    if (isNaN(e21)) return;
    if (!this.ready) return void this._set("rotation", e21);
    const t26 = this.viewpoint;
    wt(t26, t26, e21), this.viewpoint = t26;
  }
  get scale() {
    return this.ready ? this.state.scale : this._get("scale");
  }
  set scale(e21) {
    if (!e21 || isNaN(e21)) return;
    if (!this.ready) {
      this._set("scale", e21), this._set("zoom", -1);
      const t27 = this._get("extent");
      return t27 && (this._set("extent", null), this._set("center", t27.center)), void this.notifyChange("initialExtentRequired");
    }
    const t26 = this.viewpoint;
    jt(t26, t26, e21), this.viewpoint = t26;
  }
  get stationary() {
    return !(this.animation || this.navigating || this.resizing || this._stationaryTimer);
  }
  get updating() {
    var _a, _b, _c;
    const e21 = !this.destroyed && (!this.layerViewManager || !this.labelManager || !this.graphicsView || true === this.layerViewManager.updating || true === this.labelManager.updating || true === this.graphicsView.updating || this.allLayerViews.some((e22) => !e22.destroyed && !("layerViews" in e22) && true === e22.updating));
    if (has("esri-2d-log-updating")) {
      const t26 = this.allLayerViews.reduce((e22, t27) => ({ ...e22, [t27.layer.id]: !t27.destroyed && !("layerViews" in t27) && t27.updating }), {});
      console.log(`Updating MapView: ${e21}
-> Null LayerViewManager: ${!this.layerViewManager}
-> Null LabelManager: ${!this.labelManager}
-> Null GraphicsView: ${!this.graphicsView}
-> layerViewManager.updating: ${(_a = this.layerViewManager) == null ? void 0 : _a.updating}
-> labelManager.updating: ${(_b = this.labelManager) == null ? void 0 : _b.updating}
-> graphicsView.updating: ${(_c = this.graphicsView) == null ? void 0 : _c.updating}
-> allLayerViews: ${JSON.stringify(t26)}
`);
    }
    return e21;
  }
  get viewpoint() {
    if (!this.ready) return this._get("viewpoint");
    const e21 = this.state.paddedViewState;
    return e21 && e21.viewpoint.clone();
  }
  set viewpoint(e21) {
    if (null == e21) return;
    if (!this.ready) return this._set("viewpoint", e21), this._set("extent", null), this._set("center", null), this._set("zoom", -1), this._set("scale", 0), void this.notifyChange("initialExtentRequired");
    let t26, s22;
    try {
      t26 = this._project(e21, this.spatialReference), !e21.scale || isNaN(e21.scale) ? s22 = new s3("mapview:invalid-viewpoint", `invalid scale value of ${e21.scale}`) : t(e21.targetGeometry) && (s22 = new s3("mapview:invalid-viewpoint", "geometry not defined"));
    } catch (a32) {
      s22 = new s3("mapview:invalid-viewpoint", "could not project the value in the view's spatial reference", { error: a32 });
    }
    if (s22) return void s2.getLogger(this.declaredClass).error(s22);
    this._scaleBeforeChangingSpatialReference = null;
    const r20 = new u3({ targetGeometry: new w2(), scale: 0, rotation: 0 });
    Z(r20, t26), this.constraints.constrain(r20, this.state.paddedViewState.viewpoint), this.state.viewpoint = r20, this.frameTask.requestFrame(), this._set("viewpoint", r20);
  }
  get zoom() {
    return this.ready ? this.constraints.scaleToZoom(this.scale) : this._get("zoom");
  }
  set zoom(e21) {
    if (null == e21) return;
    if (!this.ready) {
      this._set("zoom", e21), this._set("scale", 0);
      const t27 = this._get("extent");
      return t27 && (this._set("extent", null), this._set("center", t27.center)), void this.notifyChange("initialExtentRequired");
    }
    if (!this.constraints.effectiveLODs) return void this._set("zoom", -1);
    const t26 = this.viewpoint;
    jt(t26, t26, this.constraints.zoomToScale(e21)), this.viewpoint = t26, this._set("zoom", this.constraints.scaleToZoom(this.scale));
  }
  get navigating() {
    return !(!this.mapViewNavigation || !this.mapViewNavigation.interacting);
  }
  goTo(e21, t26) {
    if (e21) return this.animation && (this.animation = null), this._createAnimation(), j3(() => this.ready, t26).then(() => {
      var _a;
      const i28 = { animate: true, ...t26 }, s22 = Y(e21, this);
      return (_a = this.animation) == null ? void 0 : _a.update(s22), this._gotoTask = {}, i28.animate ? this._gotoAnimated(s22, i28) : this._gotoImmediate(s22, i28);
    });
    s2.getLogger(this.declaredClass).error("#goTo()", "target cannot be null or undefined");
  }
  async hitTest(e21, t26) {
    const i28 = o8(e21) ? r9(this, e21) : e21;
    if (!this.ready || isNaN(i28.x) || isNaN(i28.y)) return { screenPoint: i28, results: [] };
    let s22 = /* @__PURE__ */ new Set(), r20 = false, n27 = null, a32 = null;
    (t26 == null ? void 0 : t26.include) ? Se(t26.include, Ve(this, (e22) => s22.add(e22), (e22) => {
      n27 || (n27 = /* @__PURE__ */ new Set()), n27.add(e22);
    }, (e22) => s22.add(e22), () => r20 = true)) : (r20 = true, s22 = new Set(this.allLayerViews)), (t26 == null ? void 0 : t26.exclude) && Se(t26.exclude, Ve(this, (e22) => s22.delete(e22), (e22) => {
      a32 || (a32 = /* @__PURE__ */ new Set()), a32.add(e22);
    }));
    const o27 = this.allLayerViews.filter((e22) => !e22.suspended && s22.has(e22)).reverse(), p30 = this.toMap(i28);
    let h18 = [...r20 ? this.graphicsView.hitTest(p30).map((e22) => ({ type: "graphic", graphic: e22, layer: null, mapPoint: p30 })) : [], ...await Promise.all(o27.map((e22) => e22.hitTest(p30, i28)).toArray())].filter(r).flat().filter(r);
    return n27 && (h18 = h18.filter((e22) => !("graphic" in e22) || !e22.graphic || (n27 == null ? void 0 : n27.has(ve(e22.graphic))))), a32 && (h18 = h18.filter((e22) => !("graphic" in e22) || !e22.graphic || (a32 == null ? void 0 : a32.has(ve(e22.graphic))))), { screenPoint: i28, results: h18 };
  }
  async takeScreenshot(e21) {
    const t26 = this._createScreenshotPlan(e21), i28 = await this._stage.takeScreenshot(t26);
    return r10(i28, { format: t26.format, quality: t26.quality, rotation: 0, disableDecorations: false }, this._pixelFormat);
  }
  async _takeScreenshot(e21) {
    const t26 = this._createScreenshotPlan(e21), i28 = await this._stage.takeScreenshot(t26);
    return l9(i28, this._pixelFormat);
  }
  _createScreenshotPlan(e21) {
    e21 = e21 ?? {};
    const t26 = this.supersampleScreenshotsEnabled ? Math.min(4, w6(this.size, Math.min(4096, this._stage.context.parameters.maxTextureSize))) : 1;
    let i28;
    e21.layers ? (i28 = [], e21.layers.forEach((e22) => {
      const t27 = this.allLayerViews.find((t28) => t28.layer.id === e22.id);
      t27 && "container" in t27 && t27.container && i28.push(t27.container);
    })) : i28 = this._stage.children;
    const { format: s22, quality: r20 } = s9(e21.format, e21.quality);
    return t24(e21, t26, this.size, this.padding, s22, r20, i28, e21.rotation);
  }
  get test() {
    return { takeScreenshot: (e21) => this._takeScreenshot(e21) };
  }
  toMap(e21) {
    if (!this.ready) return null;
    const t26 = o8(e21) ? r9(this, e21) : e21, i28 = [0, 0], [s22, r20] = this.state.toMap(i28, [t26.x, t26.y]), n27 = this.spatialReference;
    return new w2({ x: s22, y: r20, spatialReference: n27 });
  }
  toScreen(e21) {
    if (!this.ready) return null;
    const t26 = this._project(e21, this.spatialReference), i28 = [t26.x, t26.y];
    return this.state.toScreen(i28, i28), c2(i28[0], i28[1]);
  }
  on(e21, t26, i28, s22) {
    const r20 = this.inputManager && this.viewEvents.on(e21, t26, i28, s22);
    return r20 || super.on(e21, t26);
  }
  hasEventListener(e21) {
    return super.hasEventListener(e21) || this.viewEvents.hasHandler(e21);
  }
  whenLayerView(e21) {
    return super.whenLayerView(e21);
  }
  graphicChanged(e21) {
    if (this.graphicsView) {
      this.graphicsView.graphicUpdateHandler(e21);
    }
  }
  whenReady() {
    return new Promise((e21) => {
      this.ready ? e21(this) : this._resolveWhenReady.push(e21);
    });
  }
  forceDOMReadyCycle() {
    this.forceReadyCycle();
  }
  getDefaultSpatialReference() {
    var _a, _b, _c;
    return this.map && "initialViewProperties" in this.map && ((_b = (_a = this.map) == null ? void 0 : _a.initialViewProperties) == null ? void 0 : _b.spatialReference) || ((_c = this.defaultsFromMap) == null ? void 0 : _c.spatialReference) || null;
  }
  hasLayerViewModule(e21) {
    return n21.hasLayerViewModule(e21);
  }
  importLayerView(e21) {
    return n21.importLayerView(e21);
  }
  pixelSizeAt() {
    return this.ready ? this.state.resolution : (s2.getLogger(this.declaredClass).error("#pixelSizeAt()", "Map view cannot be used before it is ready"), null);
  }
  popupHitTest(e21) {
    return this.hitTest(e21).then((t26) => ({ ...t26, mapPoint: this.toMap(e21) }));
  }
  requestUpdate() {
    this.ready && this.frameTask.requestUpdate();
  }
  validate() {
    let e21 = i26(this.type);
    return has("safari") && has("safari") < 9 && (e21 = new s3("mapview:browser-not-supported", "This browser is not supported by MapView (Safari < 9)", { type: "safari", requiredVersion: 9, detectedVersion: has("safari") })), r(e21) ? (s2.getLogger(this.declaredClass).warn("#validate()", e21.message), Promise.reject(e21)) : me2();
  }
  _createAnimation() {
    return this.animation && !this.animation.done || (this.animation = new p21()), this.animation;
  }
  _cancellableGoTo(e21, t26, i28) {
    const s22 = () => e21 === this._gotoTask, r20 = i28.then(() => {
      s22() && (this.animation = null);
    }).catch((e22) => {
      throw s22() && (t26 && !t26.done && (t26.stop(), this.frameTask.animationInProgress = false), this.animation = null), e22;
    }), n27 = new Promise((e22) => e22(r20));
    return t26.when().catch(() => {
      s22() && n27.cancel && n27.cancel();
    }), n27;
  }
  _gotoImmediate(e21, t26) {
    const i28 = this._gotoTask, s22 = this.animation, r20 = e21.then((e22) => {
      if (f(t26), i28 !== this._gotoTask) throw new s3("view:goto-interrupted", "Goto was interrupted");
      this.viewpoint = s22.target = e22, s22.finish();
    });
    return this._cancellableGoTo(i28, s22, r20);
  }
  _flipStationary(e21) {
    return null !== this._stationaryTimer || (this._stationaryTimer = setTimeout(() => {
      this._stationaryTimer = null;
      const e22 = performance.now() - this._lastStationaryEventTimestamp;
      e22 < ye2 && (this._stationaryTimer = this._flipStationary(e22));
    }, e21)), this._stationaryTimer;
  }
  _getDefaultViewpoint() {
    var _a, _b, _c, _d;
    const { constraints: e21, initialExtent: t26, map: s22, padding: r20, size: n27 } = this;
    if (!e21) return null;
    const a32 = s22 && "initialViewProperties" in s22 ? s22.initialViewProperties : void 0, o27 = { zoom: this._get("zoom"), scale: this._get("scale"), center: this._get("center"), extent: this._get("extent"), rotation: this._get("rotation"), viewpoint: this._get("viewpoint"), spatialReference: this._userSpatialReference };
    e21.effectiveLODs ? -1 !== o27.zoom && (o27.scale = e21.zoomToScale(o27.zoom)) : o27.zoom = -1;
    let h18 = null, c34 = null, d15 = 0;
    const u18 = o27.viewpoint && o27.viewpoint.rotation, g16 = o27.viewpoint && o27.viewpoint.targetGeometry;
    r(g16) && ("extent" === g16.type ? h18 = g16 : "point" === g16.type && (c34 = g16, d15 = o27.viewpoint.scale));
    const m24 = o27.extent || h18 || ((_b = e2((_a = a32 == null ? void 0 : a32.viewpoint) == null ? void 0 : _a.targetGeometry)) == null ? void 0 : _b.extent) || t26, y16 = o27.center || c34 || (m24 == null ? void 0 : m24.center), w18 = o27.scale || d15 || ((_c = a32 == null ? void 0 : a32.viewpoint) == null ? void 0 : _c.scale) || m24 && H(m24, [n27[0] - r20.left - r20.right, n27[1] - r20.top - r20.bottom]), f17 = o27.rotation || u18 || ((_d = a32 == null ? void 0 : a32.viewpoint) == null ? void 0 : _d.rotation) || 0;
    return y16 && w18 ? new u3({ targetGeometry: y16, scale: w18, rotation: f17 }) : null;
  }
  _gotoAnimated(e21, t26) {
    const i28 = this._gotoTask, s22 = this.animation;
    if (!s22) return Promise.resolve();
    const r20 = e21.then((e22) => {
      if (f(t26), i28 !== this._gotoTask) throw new s3("view:goto-interrupted", "Goto was interrupted");
      return s22.update(e22), this.animationManager.animate(s22, this.viewpoint, t26), s22.when().then(() => {
      }, () => {
      });
    });
    return this._cancellableGoTo(i28, s22, r20);
  }
  _project(e21, t26) {
    var _a;
    const i28 = e21 && e21.targetGeometry || e21;
    if (!t26) return e21;
    if (!i28) return null;
    if (t26.imageCoordinateSystem || ((_a = i28.spatialReference) == null ? void 0 : _a.imageCoordinateSystem)) return e21;
    if (E2(t26, i28.spatialReference)) return e21;
    const s22 = rn(i28, t26);
    if (!s22) throw new s3("mapview:projection-not-possible", "projecting input geometry to target spatial reference returned a null value", { geometry: i28, spatialReference: t26 });
    return we(e21) ? (e21.targetGeometry = s22, e21) : s22;
  }
  _resizeHandler(e21) {
    if (!this.ready) return;
    const t26 = this.state;
    let i28 = this.state.paddedViewState.viewpoint;
    const s22 = this.state.paddedViewState.size.concat();
    t26.size = [e21.width, e21.height], ht(i28, i28, s22, this.state.paddedViewState.size, this.resizeAlign), i28 = this.constraints.constrain(i28, void 0), this.state.viewpoint = i28;
  }
  _startup() {
    var _a, _b;
    this.timeline.begin("MapView Startup");
    const e21 = this._getDefaultViewpoint(), t26 = e21.targetGeometry;
    try {
      this._project(e21, this.spatialReference);
    } catch (l25) {
      s2.getLogger(this.declaredClass).warn(new t2("mapview:startup-projection-error", "projection of initial viewpoint to the view's spatial reference, defaulting to the initial viewpoint.", { center: t26.toJSON(), spatialReference: this.spatialReference, error: l25 })), e21.targetGeometry = ((_a = this.defaultsFromMap.extent) == null ? void 0 : _a.center) || new w2({ x: 0, y: 0, spatialReference: this.spatialReference });
    }
    (_b = this.constraints) == null ? void 0 : _b.fit(e21), this._set("state", new f14({ padding: this._get("padding"), size: this.size, viewpoint: e21 })), this.graphics.owner = this;
    const i28 = new pe2(this.surface, { canvas: this.renderCanvas, supersampleScreenshots: this.supersampleScreenshotsEnabled, contextOptions: { disabledExtensions: this.deactivatedWebGLExtensions, debugWebGLExtensions: this.debugWebGLExtensions }, renderingOptions: this.renderingOptions, timeline: this.timeline });
    this._stage = i28, this._magnifierView = new ge2(), this._magnifierView.magnifier = this.magnifier;
    const s22 = new de2({ view: this });
    this._set("labelManager", s22);
    const r20 = new l17({ view: this });
    this._set("animationManager", r20);
    const n27 = new ue2({ view: this, animationManager: r20 });
    this._set("mapViewNavigation", n27), this._setupSpatialReferenceDependentProperties(), this.handles.add([this.rootLayerViews.on("change", () => this._updateStageChildren()), i28.on("post-render", () => this._set("rendering", i28.renderRequested)), i28.on("will-render", () => this._set("rendering", i28.renderRequested)), i28.on("webgl-error", (e22) => this.fatalError = e22.error), l3(() => this.stationary, (e22) => i28.stationary = e22, w4), l3(() => this.background, (e22) => {
      i28.background = e22, this._magnifierView.background = e22;
    }, w4), l3(() => this.magnifier, (e22) => this._magnifierView.magnifier = e22, w4), l3(() => this.renderingOptions, (e22) => i28.renderingOptions = e22, w4), l3(() => this.highlightOptions, (e22) => i28.highlightOptions = e22, w4), l3(() => this.state.id, () => i28.state = this.state, w4)], "map-view"), this._updateStageChildren();
    const a32 = this._resolveWhenReady;
    this._resolveWhenReady = [], a32.forEach((e22) => e22(this)), this.timeline.end("MapView Startup"), this.frameTask && this.frameTask.start(), this._set("ready", true);
  }
  _teardown() {
    this._destroySpatialReferenceDependentProperties(), this.handles.remove("map-view"), this.mapViewNavigation.destroy(), this._set("mapViewNavigation", null), this.animationManager.destroy(), this._set("animationManager", null), this.layerViewManager.clear(), this.labelManager.destroy(), this._magnifierView.destroy(), this._stage.destroy(), this._stage = null, this._set("graphicsView", null), this._magnifierView = null, this._set("labelManager", null), this._set("mapViewNavigation", null), this.graphics.owner = null, this.frameTask && this.frameTask.stop(), this._stationaryTimer && (clearTimeout(this._stationaryTimer), this._stationaryTimer = null), this._set("ready", false);
    const { center: [e21, t26], spatialReference: i28, rotation: s22, scale: r20 } = this.state.paddedViewState, n27 = new w2({ x: e21, y: t26, spatialReference: i28 });
    this._set("viewpoint", null), this._set("extent", null), this._set("center", n27), this._set("zoom", -1), this._set("rotation", s22), this._set("scale", r20), this._set("spatialReference", i28), this._set("state", null), this.animation = null;
  }
  _updateStageChildren() {
    this._stage.removeAllChildren(), this.rootLayerViews.forEach((e22) => {
      this._stage.addChild(e22.container);
    });
    const e21 = this.graphicsView;
    this._stage.addChild(e21.container), this._stage.addChild(this._magnifierView);
  }
  _setupSpatialReferenceDependentProperties() {
    const e21 = new h3(j5.create({ spatialReference: this.spatialReference, size: 512, numLODs: 36 }));
    this._set("featuresTilingScheme", e21);
    const t26 = new he3({ view: this, graphics: this.graphics, requestUpdateCallback: () => this.requestUpdate(), container: new ce2(e21) });
    this.frameTask.graphicsView = t26, this._set("graphicsView", t26);
  }
  _destroySpatialReferenceDependentProperties() {
    const e21 = this.graphicsView;
    this._set("graphicsView", null), this.frameTask.graphicsView = null, e21.destroy(), this._set("featuresTilingScheme", null);
  }
  _spatialReferenceChanged(e21) {
    if (!this.ready) return;
    this.frameTask.stop();
    for (const i28 of this.allLayerViews) i28.processDetach();
    this._destroySpatialReferenceDependentProperties();
    const t26 = this.state.paddedViewState.clone();
    if (t(this._scaleBeforeChangingSpatialReference)) this._scaleBeforeChangingSpatialReference = t26.scale;
    else {
      const e22 = t26.viewpoint.clone();
      e22.scale = this._scaleBeforeChangingSpatialReference, t26.viewpoint = e22;
    }
    const s22 = t26.clone(), [r20, n27] = t26.center;
    let a32 = null;
    try {
      a32 = this._project(new w2({ x: r20, y: n27, spatialReference: t26.spatialReference }), e21);
    } catch (p30) {
      en() || s2.getLogger(this.declaredClass).warn(new t2("mapview:spatial-reference-change", "could not project the view's center to the new spatial reference", { center: a32 == null ? void 0 : a32.toJSON(), spatialReference: e21, error: p30 }));
    }
    a32 || (a32 = new w2({ x: 0, y: 0, spatialReference: e21 }));
    const l25 = xt(new u3({ targetGeometry: new w2(), scale: 0, rotation: 0 }), t26.viewpoint, a32);
    s22.viewpoint = l25;
    try {
      const i28 = 20, r21 = [t26.size[0] / 2, t26.size[1] / 2], n28 = [r21[0] + i28, r21[1]], a33 = t26.toMap([0, 0], n28), { x: o27, y: p30 } = this._project(new w2({ x: a33[0], y: a33[1], spatialReference: t26.spatialReference }), e21);
      a33[0] = o27, a33[1] = p30, s22.toScreen(a33, a33);
      const h18 = pt(r21, a33, n28), c34 = Math.hypot(a33[0] - r21[0], a33[1] - r21[1]) / i28;
      !Number.isFinite(c34) || Math.abs(c34) > 4 ? (l25.rotation = 0, l25.targetGeometry = new w2({ x: 0, y: 0, spatialReference: e21 })) : (l25.scale *= c34, l25.scale > has("mapview-srswitch-adjust-rotation-scale-threshold") ? l25.rotation = 0 : l25.rotation += Number.isFinite(h18) ? h18 : 0);
    } catch {
    }
    this._get("constraints").constrain(l25, void 0), this._get("state").viewpoint = l25, this._stage.state = this.state, this._setupSpatialReferenceDependentProperties();
    for (const i28 of this.allLayerViews) i28.processAttach();
    this.frameTask.requestFrame(), this.frameTask.start(), this._updateStageChildren();
  }
};
fe2.type = "2d", e([y({ readOnly: true })], fe2.prototype, "animationManager", void 0), e([y({ constructOnly: true })], fe2.prototype, "deactivatedWebGLExtensions", void 0), e([y({ constructOnly: true })], fe2.prototype, "debugWebGLExtensions", void 0), e([y({ readOnly: true })], fe2.prototype, "featuresTilingScheme", void 0), e([y({ readOnly: true })], fe2.prototype, "fullOpacity", void 0), e([y({ readOnly: true })], fe2.prototype, "graphicsTileStore", null), e([y()], fe2.prototype, "graphicsView", void 0), e([y({ readOnly: true })], fe2.prototype, "state", void 0), e([y()], fe2.prototype, "initialExtentRequired", null), e([y()], fe2.prototype, "labelManager", void 0), e([y({ readOnly: true })], fe2.prototype, "resourceManager", null), e([y({ readOnly: true })], fe2.prototype, "textureManager", null), e([y({ readOnly: true })], fe2.prototype, "mapViewNavigation", void 0), e([y({ constructOnly: true })], fe2.prototype, "renderCanvas", void 0), e([y()], fe2.prototype, "renderingOptions", void 0), e([y({ readOnly: true })], fe2.prototype, "rendering", void 0), e([y({ constructOnly: true })], fe2.prototype, "supersampleScreenshotsEnabled", void 0), e([y({ readOnly: true })], fe2.prototype, "supportsGround", void 0), e([y()], fe2.prototype, "_stationaryTimer", void 0), e([y()], fe2.prototype, "_defaultsFromMapSettings", null), e([y()], fe2.prototype, "_pePromise", void 0), e([y({ readOnly: true })], fe2.prototype, "typeSpecificPreconditionsReady", null), e([y()], fe2.prototype, "animation", null), e([y({ type: m23 })], fe2.prototype, "background", null), e([y({ value: null, type: w2, dependsOn: ["state.id", "ready"] })], fe2.prototype, "center", null), e([y({ type: u13 })], fe2.prototype, "constraints", null), e([y({ value: null, type: w3, dependsOn: ["state.id", "ready"] })], fe2.prototype, "extent", null), e([y()], fe2.prototype, "floors", void 0), e([y({ type: c10 })], fe2.prototype, "highlightOptions", void 0), e([y({ readOnly: true })], fe2.prototype, "inputManager", void 0), e([y()], fe2.prototype, "map", void 0), e([y({ value: { top: 0, right: 0, bottom: 0, left: 0 }, cast: (e21) => ({ top: 0, right: 0, bottom: 0, left: 0, ...e21 }) })], fe2.prototype, "padding", null), e([y()], fe2.prototype, "resizeAlign", void 0), e([y({ readOnly: true, dependsOn: ["state.id"] })], fe2.prototype, "resolution", null), e([y({ value: 0, type: Number, dependsOn: ["state.id", "ready"] })], fe2.prototype, "rotation", null), e([y({ value: 0, type: Number, dependsOn: ["state.id", "ready"] })], fe2.prototype, "scale", null), e([y({ constructOnly: true })], fe2.prototype, "spatialReferenceLocked", void 0), e([y()], fe2.prototype, "stationary", null), e([y({ type: e8, readOnly: true })], fe2.prototype, "timeline", void 0), e([y({ readOnly: true })], fe2.prototype, "type", void 0), e([y({ readOnly: true })], fe2.prototype, "updating", null), e([y({ value: null, type: u3, dependsOn: ["state.id", "ready"] })], fe2.prototype, "viewpoint", null), e([y({ value: -1, dependsOn: ["state.id", "ready"] })], fe2.prototype, "zoom", null), e([y({ readOnly: true })], fe2.prototype, "navigating", null), e([y({ type: p28 })], fe2.prototype, "ui", void 0), fe2 = e([a2("esri.views.MapView")], fe2);
var _e = fe2;
function ve(e21) {
  var _a, _b;
  const t26 = e21.getObjectId();
  return t26 ? `${((_a = e21.layer) == null ? void 0 : _a.uid) ?? ((_b = e21.sourceLayer) == null ? void 0 : _b.uid) ?? "MapView"}/${t26}` : `"MapView/${e21.uid}`;
}
function Ve(e21, i28, s22, r20, n27) {
  return (a32) => {
    if (a32 instanceof g3) {
      if (a32.layer === e21) n27 == null ? void 0 : n27();
      else {
        const t26 = e21.allLayerViews.find((e22) => e22.layer === a32.layer);
        t26 && (r20 == null ? void 0 : r20(t26));
      }
      s22(ve(a32));
    } else {
      const t26 = e21.allLayerViews.find((e22) => e22.layer === a32);
      t26 && i28(t26);
    }
  };
}
function Se(e21, t26) {
  if (e21) if (t3(e21)) for (const i28 of e21) if (t3(i28)) for (const e22 of i28) t26(e22);
  else t26(i28);
  else t26(e21);
}
function Te(e21) {
  return "esri.WebMap" === (e21 == null ? void 0 : e21.declaredClass);
}

export {
  _e
};
//# sourceMappingURL=chunk-QXRMISVH.js.map
