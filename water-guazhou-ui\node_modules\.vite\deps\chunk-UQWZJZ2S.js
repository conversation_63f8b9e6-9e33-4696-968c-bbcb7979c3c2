import {
  t as t3
} from "./chunk-5S4W3ME5.js";
import {
  E,
  g,
  h
} from "./chunk-CDZ24ELJ.js";
import {
  M
} from "./chunk-VHLK35TF.js";
import {
  P
} from "./chunk-HURTVQSL.js";
import {
  l,
  w
} from "./chunk-QUHG7NMD.js";
import {
  p
} from "./chunk-MQAXMQFG.js";
import {
  r as r2,
  t as t2
} from "./chunk-36FLFRUE.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/interactive/snapping/SnappingPoint.js
function l2(n) {
  return n;
}
function f(n) {
  return t2(n);
}
function s(n, r3, e) {
  return r2(n, r3, e);
}
function c(r3, t4, e) {
  return t(r3) ? null : a(e.coordinateHelper.vectorToDehydratedPoint(r3, d), t4, e);
}
function a(r3, e, i) {
  if (t(r3)) return null;
  if (t(e)) return l2(r2(r3.x, r3.y, r3.z ?? 0));
  if ("2d" === e.type) return l2(r2(r3.x, r3.y, 0));
  const { elevationInfo: f3 } = i, s2 = g(e, r3, f3, E) ?? 0;
  return l2(r2(r3.x, r3.y, s2));
}
function m(r3, t4, { z: o, m: l4, spatialReference: f3, elevationInfo: s2 }) {
  if (null == o && null == l4) {
    const n = M(r3[0], r3[1], void 0, f3);
    return null != l4 && (n.m = l4, n.hasM = true), n;
  }
  if (t(t4) || "2d" === t4.type) {
    const n = M(r3[0], r3[1], o, f3);
    return null != l4 && (n.m = l4, n.hasM = true), n;
  }
  const c3 = h(t4, r3, f3, E, s2) ?? 0, a2 = M(r3[0], r3[1], c3, f3);
  return null != l4 && (a2.m = l4, a2.hasM = true), a2;
}
function p2(n, r3) {
  return M(n[0], n[1], n[2], r3);
}
var d = M(0, 0, 0, null);

// node_modules/@arcgis/core/views/interactive/snapping/snappingUtils.js
function c2(e, t4) {
  const n = e.x - t4.x, o = e.y - t4.y;
  return n * n + o * o;
}
function g2(e, t4) {
  return Math.sqrt(c2(e, t4));
}
function d2(e, t4) {
  t4.sort((t5, n) => p(t5.targetPoint, e) - p(n.targetPoint, e));
}
var u;
function l3({ point: t4, distance: n, types: o, coordinateHelper: { spatialReference: i } }, s2, a2) {
  return { point: M(t4[0], t4[1], t4[2], i.toJSON()), mode: s2, distance: n, types: o, query: r(a2) ? a2.toJSON() : { where: "1=1" } };
}
function E2(e, t4, n) {
  return { left: c(e.leftVertex.pos, t4, n), right: c(e.rightVertex.pos, t4, n) };
}
function f2(e) {
  return e.createQuery();
}
function m2(o, r3 = () => {
}) {
  const a2 = l(() => ({ view: o.view, snappingOptions: o.snappingOptions }), ({ view: t4, snappingOptions: n }) => {
    const a3 = "snapping-toggle", p3 = P.TOOL;
    if (o.removeHandles(a3), t4 && r(n)) {
      const e = [t4.on("key-down", (e2) => {
        e2.key !== t3.toggle || e2.repeat || (n.enabledToggled = true, r3());
      }, p3), t4.on("key-up", (e2) => {
        e2.key === t3.toggle && (n.enabledToggled = false, r3());
      }, p3), t4.on("pointer-move", (e2) => {
        const t5 = e2.native.ctrlKey;
        n.enabledToggled !== t5 && (n.enabledToggled = t5, r3());
      }, p3)];
      o.addHandles(e, a3);
    }
  }, w);
  o.addHandles(a2);
}
!function(e) {
  e[e.TARGET = 0] = "TARGET", e[e.REFERENCE = 1] = "REFERENCE", e[e.REFERENCE_EXTENSION = 2] = "REFERENCE_EXTENSION";
}(u || (u = {}));

export {
  l2 as l,
  f,
  s,
  c,
  a,
  m,
  p2 as p,
  c2,
  g2 as g,
  d2 as d,
  u,
  l3 as l2,
  E2 as E,
  f2,
  m2
};
//# sourceMappingURL=chunk-UQWZJZ2S.js.map
