import {
  BandwidthService,
  <PERSON><PERSON>er,
  ERR,
  ERR_CODE,
  EVENT,
  FMP4Remuxer,
  FlvDemuxer,
  GapService,
  Logger,
  Logger2,
  MSE,
  MediaStatsService,
  NetLoader,
  SeiService,
  StreamingError,
  WarningType,
  concatUint8Array,
  getVideoPlaybackQuality,
  isMediaPlaying
} from "./chunk-PUJ4YNC5.js";
import {
  BasePlugin,
  Errors,
  events_exports
} from "./chunk-THNJ4Y4A.js";
import {
  __commonJS,
  __toESM
} from "./chunk-H3AJBOWU.js";

// node_modules/xgplayer-flv/node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/xgplayer-flv/node_modules/eventemitter3/index.js"(exports, module) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__) prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
      else emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0) emitter._events = new Events();
      else delete emitter._events[evt];
    }
    function EventEmitter2() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter2.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0) return names;
      for (name in events = this._events) {
        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter2.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers) return [];
      if (handlers.fn) return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter2.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners) return 0;
      if (listeners.fn) return 1;
      return listeners.length;
    };
    EventEmitter2.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once) this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once) this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {
                args[j - 1] = arguments[j];
              }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter2.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter2.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter2.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
        else clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter2.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt]) clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter2.prototype.off = EventEmitter2.prototype.removeListener;
    EventEmitter2.prototype.addListener = EventEmitter2.prototype.on;
    EventEmitter2.prefixed = prefix;
    EventEmitter2.EventEmitter = EventEmitter2;
    if ("undefined" !== typeof module) {
      module.exports = EventEmitter2;
    }
  }
});

// node_modules/xgplayer-flv/es/_virtual/_rollupPluginBabelHelpers.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = null != arguments[i] ? arguments[i] : {};
    i % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _regeneratorRuntime() {
  _regeneratorRuntime = function() {
    return exports;
  };
  var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function(obj, key, desc) {
    obj[key] = desc.value;
  }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag";
  function define(obj, key, value) {
    return Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    }), obj[key];
  }
  try {
    define({}, "");
  } catch (err) {
    define = function(obj, key, value) {
      return obj[key] = value;
    };
  }
  function wrap(innerFn, outerFn, self, tryLocsList) {
    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []);
    return defineProperty(generator, "_invoke", {
      value: makeInvokeMethod(innerFn, self, context)
    }), generator;
  }
  function tryCatch(fn, obj, arg) {
    try {
      return {
        type: "normal",
        arg: fn.call(obj, arg)
      };
    } catch (err) {
      return {
        type: "throw",
        arg: err
      };
    }
  }
  exports.wrap = wrap;
  var ContinueSentinel = {};
  function Generator() {
  }
  function GeneratorFunction() {
  }
  function GeneratorFunctionPrototype() {
  }
  var IteratorPrototype = {};
  define(IteratorPrototype, iteratorSymbol, function() {
    return this;
  });
  var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([])));
  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);
  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);
  function defineIteratorMethods(prototype) {
    ["next", "throw", "return"].forEach(function(method) {
      define(prototype, method, function(arg) {
        return this._invoke(method, arg);
      });
    });
  }
  function AsyncIterator(generator, PromiseImpl) {
    function invoke(method, arg, resolve, reject) {
      var record = tryCatch(generator[method], generator, arg);
      if ("throw" !== record.type) {
        var result = record.arg, value = result.value;
        return value && "object" == typeof value && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function(value2) {
          invoke("next", value2, resolve, reject);
        }, function(err) {
          invoke("throw", err, resolve, reject);
        }) : PromiseImpl.resolve(value).then(function(unwrapped) {
          result.value = unwrapped, resolve(result);
        }, function(error) {
          return invoke("throw", error, resolve, reject);
        });
      }
      reject(record.arg);
    }
    var previousPromise;
    defineProperty(this, "_invoke", {
      value: function(method, arg) {
        function callInvokeWithMethodAndArg() {
          return new PromiseImpl(function(resolve, reject) {
            invoke(method, arg, resolve, reject);
          });
        }
        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();
      }
    });
  }
  function makeInvokeMethod(innerFn, self, context) {
    var state = "suspendedStart";
    return function(method, arg) {
      if ("executing" === state)
        throw new Error("Generator is already running");
      if ("completed" === state) {
        if ("throw" === method)
          throw arg;
        return doneResult();
      }
      for (context.method = method, context.arg = arg; ; ) {
        var delegate = context.delegate;
        if (delegate) {
          var delegateResult = maybeInvokeDelegate(delegate, context);
          if (delegateResult) {
            if (delegateResult === ContinueSentinel)
              continue;
            return delegateResult;
          }
        }
        if ("next" === context.method)
          context.sent = context._sent = context.arg;
        else if ("throw" === context.method) {
          if ("suspendedStart" === state)
            throw state = "completed", context.arg;
          context.dispatchException(context.arg);
        } else
          "return" === context.method && context.abrupt("return", context.arg);
        state = "executing";
        var record = tryCatch(innerFn, self, context);
        if ("normal" === record.type) {
          if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel)
            continue;
          return {
            value: record.arg,
            done: context.done
          };
        }
        "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg);
      }
    };
  }
  function maybeInvokeDelegate(delegate, context) {
    var methodName = context.method, method = delegate.iterator[methodName];
    if (void 0 === method)
      return context.delegate = null, "throw" === methodName && delegate.iterator.return && (context.method = "return", context.arg = void 0, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel;
    var record = tryCatch(method, delegate.iterator, context.arg);
    if ("throw" === record.type)
      return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel;
    var info = record.arg;
    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = void 0), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel);
  }
  function pushTryEntry(locs) {
    var entry = {
      tryLoc: locs[0]
    };
    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);
  }
  function resetTryEntry(entry) {
    var record = entry.completion || {};
    record.type = "normal", delete record.arg, entry.completion = record;
  }
  function Context(tryLocsList) {
    this.tryEntries = [{
      tryLoc: "root"
    }], tryLocsList.forEach(pushTryEntry, this), this.reset(true);
  }
  function values(iterable) {
    if (iterable) {
      var iteratorMethod = iterable[iteratorSymbol];
      if (iteratorMethod)
        return iteratorMethod.call(iterable);
      if ("function" == typeof iterable.next)
        return iterable;
      if (!isNaN(iterable.length)) {
        var i = -1, next = function next2() {
          for (; ++i < iterable.length; )
            if (hasOwn.call(iterable, i))
              return next2.value = iterable[i], next2.done = false, next2;
          return next2.value = void 0, next2.done = true, next2;
        };
        return next.next = next;
      }
    }
    return {
      next: doneResult
    };
  }
  function doneResult() {
    return {
      value: void 0,
      done: true
    };
  }
  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", {
    value: GeneratorFunctionPrototype,
    configurable: true
  }), defineProperty(GeneratorFunctionPrototype, "constructor", {
    value: GeneratorFunction,
    configurable: true
  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function(genFun) {
    var ctor = "function" == typeof genFun && genFun.constructor;
    return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name));
  }, exports.mark = function(genFun) {
    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun;
  }, exports.awrap = function(arg) {
    return {
      __await: arg
    };
  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function() {
    return this;
  }), exports.AsyncIterator = AsyncIterator, exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {
    void 0 === PromiseImpl && (PromiseImpl = Promise);
    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);
    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function(result) {
      return result.done ? result.value : iter.next();
    });
  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function() {
    return this;
  }), define(Gp, "toString", function() {
    return "[object Generator]";
  }), exports.keys = function(val) {
    var object = Object(val), keys = [];
    for (var key in object)
      keys.push(key);
    return keys.reverse(), function next() {
      for (; keys.length; ) {
        var key2 = keys.pop();
        if (key2 in object)
          return next.value = key2, next.done = false, next;
      }
      return next.done = true, next;
    };
  }, exports.values = values, Context.prototype = {
    constructor: Context,
    reset: function(skipTempReset) {
      if (this.prev = 0, this.next = 0, this.sent = this._sent = void 0, this.done = false, this.delegate = null, this.method = "next", this.arg = void 0, this.tryEntries.forEach(resetTryEntry), !skipTempReset)
        for (var name in this)
          "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = void 0);
    },
    stop: function() {
      this.done = true;
      var rootRecord = this.tryEntries[0].completion;
      if ("throw" === rootRecord.type)
        throw rootRecord.arg;
      return this.rval;
    },
    dispatchException: function(exception) {
      if (this.done)
        throw exception;
      var context = this;
      function handle(loc, caught) {
        return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = void 0), !!caught;
      }
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i], record = entry.completion;
        if ("root" === entry.tryLoc)
          return handle("end");
        if (entry.tryLoc <= this.prev) {
          var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc");
          if (hasCatch && hasFinally) {
            if (this.prev < entry.catchLoc)
              return handle(entry.catchLoc, true);
            if (this.prev < entry.finallyLoc)
              return handle(entry.finallyLoc);
          } else if (hasCatch) {
            if (this.prev < entry.catchLoc)
              return handle(entry.catchLoc, true);
          } else {
            if (!hasFinally)
              throw new Error("try statement without catch or finally");
            if (this.prev < entry.finallyLoc)
              return handle(entry.finallyLoc);
          }
        }
      }
    },
    abrupt: function(type, arg) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) {
          var finallyEntry = entry;
          break;
        }
      }
      finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);
      var record = finallyEntry ? finallyEntry.completion : {};
      return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);
    },
    complete: function(record, afterLoc) {
      if ("throw" === record.type)
        throw record.arg;
      return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;
    },
    finish: function(finallyLoc) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.finallyLoc === finallyLoc)
          return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;
      }
    },
    catch: function(tryLoc) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.tryLoc === tryLoc) {
          var record = entry.completion;
          if ("throw" === record.type) {
            var thrown = record.arg;
            resetTryEntry(entry);
          }
          return thrown;
        }
      }
      throw new Error("illegal catch attempt");
    },
    delegateYield: function(iterable, resultName, nextLoc) {
      return this.delegate = {
        iterator: values(iterable),
        resultName,
        nextLoc
      }, "next" === this.method && (this.arg = void 0), ContinueSentinel;
    }
  }, exports;
}
function _typeof(obj) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(obj2) {
    return typeof obj2;
  } : function(obj2) {
    return obj2 && "function" == typeof Symbol && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
  }, _typeof(obj);
}
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
  try {
    var info = gen[key](arg);
    var value = info.value;
  } catch (error) {
    reject(error);
    return;
  }
  if (info.done) {
    resolve(value);
  } else {
    Promise.resolve(value).then(_next, _throw);
  }
}
function _asyncToGenerator(fn) {
  return function() {
    var self = this, args = arguments;
    return new Promise(function(resolve, reject) {
      var gen = fn.apply(self, args);
      function _next(value) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
      }
      function _throw(err) {
        asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
      }
      _next(void 0);
    });
  };
}
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}
function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor)
      descriptor.writable = true;
    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);
  }
}
function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps)
    _defineProperties(Constructor.prototype, protoProps);
  if (staticProps)
    _defineProperties(Constructor, staticProps);
  Object.defineProperty(Constructor, "prototype", {
    writable: false
  });
  return Constructor;
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _inherits(subClass, superClass) {
  if (typeof superClass !== "function" && superClass !== null) {
    throw new TypeError("Super expression must either be null or a function");
  }
  subClass.prototype = Object.create(superClass && superClass.prototype, {
    constructor: {
      value: subClass,
      writable: true,
      configurable: true
    }
  });
  Object.defineProperty(subClass, "prototype", {
    writable: false
  });
  if (superClass)
    _setPrototypeOf(subClass, superClass);
}
function _getPrototypeOf(o) {
  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf2(o2) {
    return o2.__proto__ || Object.getPrototypeOf(o2);
  };
  return _getPrototypeOf(o);
}
function _setPrototypeOf(o, p) {
  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf2(o2, p2) {
    o2.__proto__ = p2;
    return o2;
  };
  return _setPrototypeOf(o, p);
}
function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !Reflect.construct)
    return false;
  if (Reflect.construct.sham)
    return false;
  if (typeof Proxy === "function")
    return true;
  try {
    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
    return true;
  } catch (e) {
    return false;
  }
}
function _assertThisInitialized(self) {
  if (self === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self;
}
function _possibleConstructorReturn(self, call) {
  if (call && (typeof call === "object" || typeof call === "function")) {
    return call;
  } else if (call !== void 0) {
    throw new TypeError("Derived constructors may only return object or undefined");
  }
  return _assertThisInitialized(self);
}
function _createSuper(Derived) {
  var hasNativeReflectConstruct = _isNativeReflectConstruct();
  return function _createSuperInternal() {
    var Super = _getPrototypeOf(Derived), result;
    if (hasNativeReflectConstruct) {
      var NewTarget = _getPrototypeOf(this).constructor;
      result = Reflect.construct(Super, arguments, NewTarget);
    } else {
      result = Super.apply(this, arguments);
    }
    return _possibleConstructorReturn(this, result);
  };
}
function _toPrimitive(input, hint) {
  if (typeof input !== "object" || input === null)
    return input;
  var prim = input[Symbol.toPrimitive];
  if (prim !== void 0) {
    var res = prim.call(input, hint || "default");
    if (typeof res !== "object")
      return res;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (hint === "string" ? String : Number)(input);
}
function _toPropertyKey(arg) {
  var key = _toPrimitive(arg, "string");
  return typeof key === "symbol" ? key : String(key);
}

// node_modules/xgplayer-flv/es/flv/index.js
var import_eventemitter3 = __toESM(require_eventemitter3());

// node_modules/xgplayer-flv/es/flv/services/transfer-cost.js
var TransferCost = function() {
  function TransferCost2() {
    _classCallCheck(this, TransferCost2);
    _defineProperty(this, "_ttfb", 0);
    _defineProperty(this, "_demuxStart", 0);
    _defineProperty(this, "_demuxEnd", 0);
    _defineProperty(this, "_demuxCost", 0);
    _defineProperty(this, "_remuxStart", 0);
    _defineProperty(this, "_remuxEnd", 0);
    _defineProperty(this, "_remuxCost", 0);
    _defineProperty(this, "_appendStart", 0);
    _defineProperty(this, "_appendEnd", 0);
    _defineProperty(this, "_appendCost", 0);
  }
  _createClass(TransferCost2, [{
    key: "set",
    value: function set(event, value) {
      this["_".concat(event)] = value;
    }
  }, {
    key: "start",
    value: function start(event) {
      this["_".concat(event, "Start")] = Date.now();
    }
  }, {
    key: "end",
    value: function end(event) {
      this["_".concat(event, "End")] = Date.now();
      this["_".concat(event, "Cost")] = this["_".concat(event, "Cost")] + (this["_".concat(event, "End")] - this["_".concat(event, "Start")]);
    }
  }, {
    key: "transferCost",
    get: function get() {
      return {
        ttfbCost: this._ttfb,
        demuxCost: this._demuxCost,
        remuxCost: this._remuxCost,
        appendCost: this._appendCost
      };
    }
  }]);
  return TransferCost2;
}();
var TRANSFER_EVENT = {
  TTFB: "ttfb",
  DEMUX: "demux",
  REMUX: "remux",
  APPEND: "append"
};

// node_modules/xgplayer-flv/es/flv/services/buffer-service.js
var logger = new Logger("BufferService");
var BufferService = function() {
  function BufferService2(flv, softVideo) {
    var opts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
    _classCallCheck(this, BufferService2);
    _defineProperty(this, "flv", null);
    _defineProperty(this, "_demuxer", new FlvDemuxer());
    _defineProperty(this, "_remuxer", null);
    _defineProperty(this, "_mse", null);
    _defineProperty(this, "_softVideo", null);
    _defineProperty(this, "_sourceCreated", false);
    _defineProperty(this, "_needInitSegment", true);
    _defineProperty(this, "_discontinuity", true);
    _defineProperty(this, "_contiguous", false);
    _defineProperty(this, "_initSegmentId", "");
    _defineProperty(this, "_cachedBuffer", null);
    _defineProperty(this, "_demuxStartTime", 0);
    _defineProperty(this, "_opts", null);
    this.flv = flv;
    this._opts = opts;
    if (softVideo) {
      this._softVideo = softVideo;
    } else {
      this._remuxer = new FMP4Remuxer(this._demuxer.videoTrack, this._demuxer.audioTrack);
      this._mse = new MSE(null, {
        preferMMS: typeof opts.preferMMS === "boolean" ? opts.preferMMS : !!opts.perferMMS
      });
      this._mse.bindMedia(flv.media);
    }
  }
  _createClass(BufferService2, [{
    key: "baseDts",
    get: function get() {
      var _this$_demuxer, _this$_demuxer$_fixer;
      return (_this$_demuxer = this._demuxer) === null || _this$_demuxer === void 0 ? void 0 : (_this$_demuxer$_fixer = _this$_demuxer._fixer) === null || _this$_demuxer$_fixer === void 0 ? void 0 : _this$_demuxer$_fixer._baseDts;
    }
  }, {
    key: "blobUrl",
    get: function get() {
      var _this$_mse;
      return (_this$_mse = this._mse) === null || _this$_mse === void 0 ? void 0 : _this$_mse.url;
    }
  }, {
    key: "isFull",
    value: function isFull() {
      var mediaType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : MSE.VIDEO;
      return this._mse.isFull(mediaType);
    }
  }, {
    key: "seamlessSwitch",
    value: function seamlessSwitch() {
      this._needInitSegment = true;
      this._discontinuity = true;
      this._contiguous = true;
      this._initSegmentId = "";
    }
  }, {
    key: "unContiguous",
    value: function unContiguous(startTime) {
      this._contiguous = false;
      this._demuxStartTime = startTime;
    }
  }, {
    key: "reset",
    value: function() {
      var _reset = _asyncToGenerator(_regeneratorRuntime().mark(function _callee() {
        var reuseMse, _args = arguments;
        return _regeneratorRuntime().wrap(function _callee$(_context) {
          while (1)
            switch (_context.prev = _context.next) {
              case 0:
                reuseMse = _args.length > 0 && _args[0] !== void 0 ? _args[0] : false;
                if (!(this._mse && !reuseMse)) {
                  _context.next = 6;
                  break;
                }
                _context.next = 4;
                return this._mse.unbindMedia();
              case 4:
                _context.next = 6;
                return this._mse.bindMedia(this.flv.media);
              case 6:
                this._needInitSegment = true;
                this._discontinuity = true;
                this._contiguous = false;
                this._sourceCreated = false;
                this._initSegmentId = "";
                this.resetSeamlessSwitchStats();
              case 12:
              case "end":
                return _context.stop();
            }
        }, _callee, this);
      }));
      function reset() {
        return _reset.apply(this, arguments);
      }
      return reset;
    }()
  }, {
    key: "resetSeamlessSwitchStats",
    value: function resetSeamlessSwitchStats() {
      this.seamlessLoadingSwitch = null;
      this.seamlessLoadingSwitching = false;
      if (this._demuxer) {
        this._demuxer.seamlessLoadingSwitching = false;
      }
    }
  }, {
    key: "endOfStream",
    value: function() {
      var _endOfStream = _asyncToGenerator(_regeneratorRuntime().mark(function _callee2() {
        return _regeneratorRuntime().wrap(function _callee2$(_context2) {
          while (1)
            switch (_context2.prev = _context2.next) {
              case 0:
                if (!this._mse) {
                  _context2.next = 7;
                  break;
                }
                if (!this._sourceCreated) {
                  _context2.next = 5;
                  break;
                }
                _context2.next = 4;
                return this._mse.endOfStream();
              case 4:
                this.flv.emit(EVENT.BUFFEREOS);
              case 5:
                _context2.next = 8;
                break;
              case 7:
                if (this._softVideo) {
                  this._softVideo.endOfStream();
                }
              case 8:
              case "end":
                return _context2.stop();
            }
        }, _callee2, this);
      }));
      function endOfStream() {
        return _endOfStream.apply(this, arguments);
      }
      return endOfStream;
    }()
  }, {
    key: "updateDuration",
    value: function() {
      var _updateDuration = _asyncToGenerator(_regeneratorRuntime().mark(function _callee3(duration) {
        return _regeneratorRuntime().wrap(function _callee3$(_context3) {
          while (1)
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!this._mse) {
                  _context3.next = 7;
                  break;
                }
                if (this._mse.isOpened) {
                  _context3.next = 4;
                  break;
                }
                _context3.next = 4;
                return this._mse.open();
              case 4:
                logger.debug("update duration", duration);
                _context3.next = 7;
                return this._mse.updateDuration(duration);
              case 7:
              case "end":
                return _context3.stop();
            }
        }, _callee3, this);
      }));
      function updateDuration(_x) {
        return _updateDuration.apply(this, arguments);
      }
      return updateDuration;
    }()
  }, {
    key: "destroy",
    value: function() {
      var _destroy = _asyncToGenerator(_regeneratorRuntime().mark(function _callee4() {
        return _regeneratorRuntime().wrap(function _callee4$(_context4) {
          while (1)
            switch (_context4.prev = _context4.next) {
              case 0:
                if (!this._mse) {
                  _context4.next = 3;
                  break;
                }
                _context4.next = 3;
                return this._mse.unbindMedia();
              case 3:
                this._mse = null;
                this._softVideo = null;
                this._demuxer = null;
                this._remuxer = null;
              case 7:
              case "end":
                return _context4.stop();
            }
        }, _callee4, this);
      }));
      function destroy() {
        return _destroy.apply(this, arguments);
      }
      return destroy;
    }()
  }, {
    key: "appendBuffer",
    value: function() {
      var _appendBuffer = _asyncToGenerator(_regeneratorRuntime().mark(function _callee5(chunk) {
        var _this = this;
        var switchingNoReset, demuxer, videoTrack, audioTrack, metadataTrack, idx, videoExist, audioExist, duration, track, videoType, audioType, mse, afterAppend, newId, remuxResult, p;
        return _regeneratorRuntime().wrap(function _callee5$(_context5) {
          while (1)
            switch (_context5.prev = _context5.next) {
              case 0:
                switchingNoReset = false;
                if (this._cachedBuffer) {
                  chunk = concatUint8Array(this._cachedBuffer, chunk);
                  this._cachedBuffer = null;
                }
                demuxer = this._demuxer;
                if (!(!chunk || !chunk.length || !demuxer)) {
                  _context5.next = 5;
                  break;
                }
                return _context5.abrupt("return");
              case 5:
                _context5.prev = 5;
                this.flv._transferCost.start(TRANSFER_EVENT.DEMUX);
                demuxer.demuxAndFix(chunk, this.seamlessLoadingSwitching || this._discontinuity, this._contiguous, this._demuxStartTime, this.seamlessLoadingSwitching);
                this.seamlessLoadingSwitching = false;
                this.flv._transferCost.end(TRANSFER_EVENT.DEMUX);
                _context5.next = 15;
                break;
              case 12:
                _context5.prev = 12;
                _context5.t0 = _context5["catch"](5);
                throw new StreamingError(ERR.DEMUX, ERR.SUB_TYPES.FLV, _context5.t0);
              case 15:
                videoTrack = demuxer.videoTrack, audioTrack = demuxer.audioTrack, metadataTrack = demuxer.metadataTrack;
                if (!this.seamlessLoadingSwitch) {
                  _context5.next = 25;
                  break;
                }
                idx = videoTrack.samples.findIndex(function(sample) {
                  return sample.originDts === videoTrack.lastKeyFrameDts;
                });
                if (!(idx >= 0)) {
                  _context5.next = 25;
                  break;
                }
                videoTrack.samples.splice(idx);
                _context5.next = 22;
                return this.seamlessLoadingSwitch();
              case 22:
                this.seamlessLoadingSwitch = null;
                chunk = null;
                switchingNoReset = true;
              case 25:
                videoExist = videoTrack.exist();
                audioExist = audioTrack.exist();
                if (this._opts.onlyAudio) {
                  videoExist = false;
                  videoTrack.present = false;
                }
                if (this._opts.onlyVideo) {
                  audioExist = false;
                  audioTrack.present = false;
                }
                if (!(!videoExist && videoTrack.present || !audioExist && audioTrack.present)) {
                  _context5.next = 42;
                  break;
                }
                duration = 0;
                track = videoExist ? videoTrack : audioExist ? audioTrack : void 0;
                if (track && track.samples.length) {
                  duration = (track.samples[track.samples.length - 1].originPts - track.samples[0].originPts) / track.timescale * 1e3;
                }
                if (!(duration > this._opts.analyzeDuration)) {
                  _context5.next = 40;
                  break;
                }
                logger.warn("analyze duration exceeded, ".concat(duration, "ms"), track);
                videoTrack.present = videoExist;
                audioTrack.present = audioExist;
                this.flv.emit(EVENT.ANALYZE_DURATION_EXCEEDED, {
                  duration
                });
                _context5.next = 42;
                break;
              case 40:
                this._cachedBuffer = chunk;
                return _context5.abrupt("return");
              case 42:
                videoType = videoTrack.type;
                audioType = audioTrack.type;
                this._fireEvents(videoTrack, audioTrack, metadataTrack);
                if (!switchingNoReset) {
                  this._discontinuity = false;
                  this._contiguous = true;
                  this._demuxStartTime = 0;
                }
                mse = this._mse;
                afterAppend = function afterAppend2() {
                  var _this$flv;
                  if ((_this$flv = _this.flv) !== null && _this$flv !== void 0 && _this$flv.emit) {
                    var _this$flv2;
                    (_this$flv2 = _this.flv) === null || _this$flv2 === void 0 ? void 0 : _this$flv2.emit(EVENT.APPEND_BUFFER, {});
                  }
                };
                this.flv.emit(EVENT.DEMUXED_TRACK, {
                  videoTrack
                });
                newId = "".concat(videoTrack.codec, "/").concat(videoTrack.width, "/").concat(videoTrack.height, "/").concat(audioTrack.codec, "/").concat(audioTrack.config);
                if (newId !== this._initSegmentId) {
                  this._needInitSegment = true;
                  this._initSegmentId = newId;
                  this._emitMetaParsedEvent(videoTrack, audioTrack);
                }
                if (!mse) {
                  _context5.next = 81;
                  break;
                }
                if (this._sourceCreated) {
                  _context5.next = 59;
                  break;
                }
                _context5.next = 55;
                return mse.open();
              case 55:
                if (videoExist) {
                  logger.log("codec: video/mp4;codecs=".concat(videoTrack.codec));
                  mse.createSource(videoType, "video/mp4;codecs=".concat(videoTrack.codec));
                }
                if (audioExist) {
                  logger.log("codec: audio/mp4;codecs=".concat(audioTrack.codec));
                  mse.createSource(audioType, "audio/mp4;codecs=".concat(audioTrack.codec));
                }
                this._sourceCreated = true;
                this.flv.emit(EVENT.SOURCEBUFFER_CREATED);
              case 59:
                _context5.prev = 59;
                if (this._needInitSegment && !this._opts.mseLowLatency) {
                  videoTrack.duration = this._opts.durationForMSELowLatencyOff * videoTrack.timescale;
                  audioTrack.duration = this._opts.durationForMSELowLatencyOff * audioExist.timescale;
                }
                this.flv._transferCost.start(TRANSFER_EVENT.REMUX);
                remuxResult = this._remuxer.remux(this._needInitSegment);
                this.flv._transferCost.end(TRANSFER_EVENT.REMUX);
                _context5.next = 69;
                break;
              case 66:
                _context5.prev = 66;
                _context5.t1 = _context5["catch"](59);
                throw new StreamingError(ERR.REMUX, ERR.SUB_TYPES.FMP4, _context5.t1);
              case 69:
                if (!(this._needInitSegment && !remuxResult.videoInitSegment && !remuxResult.audioInitSegment)) {
                  _context5.next = 71;
                  break;
                }
                return _context5.abrupt("return");
              case 71:
                this._needInitSegment = false;
                p = [];
                if (remuxResult.videoInitSegment)
                  p.push(mse.append(videoType, remuxResult.videoInitSegment));
                if (remuxResult.audioInitSegment)
                  p.push(mse.append(audioType, remuxResult.audioInitSegment));
                if (remuxResult.videoSegment)
                  p.push(mse.append(videoType, remuxResult.videoSegment));
                if (remuxResult.audioSegment)
                  p.push(mse.append(audioType, remuxResult.audioSegment));
                this.flv._transferCost.start(TRANSFER_EVENT.APPEND);
                return _context5.abrupt("return", Promise.all(p).then(afterAppend).then(function() {
                  _this.flv._transferCost.end(TRANSFER_EVENT.APPEND);
                  afterAppend();
                }));
              case 81:
                if (this._softVideo) {
                  this._softVideo.appendBuffer(videoTrack, audioTrack);
                  afterAppend();
                }
              case 82:
              case "end":
                return _context5.stop();
            }
        }, _callee5, this, [[5, 12], [59, 66]]);
      }));
      function appendBuffer(_x2) {
        return _appendBuffer.apply(this, arguments);
      }
      return appendBuffer;
    }()
  }, {
    key: "evictBuffer",
    value: function() {
      var _evictBuffer = _asyncToGenerator(_regeneratorRuntime().mark(function _callee6(bufferBehind) {
        var _this2 = this;
        var media, currentTime, removeEnd, start;
        return _regeneratorRuntime().wrap(function _callee6$(_context6) {
          while (1)
            switch (_context6.prev = _context6.next) {
              case 0:
                media = this.flv.media;
                if (!(!this._mse || !this._demuxer || !media || !bufferBehind || bufferBehind < 0)) {
                  _context6.next = 3;
                  break;
                }
                return _context6.abrupt("return");
              case 3:
                currentTime = media.currentTime;
                removeEnd = currentTime - bufferBehind;
                if (!(removeEnd <= 0)) {
                  _context6.next = 7;
                  break;
                }
                return _context6.abrupt("return");
              case 7:
                start = Buffer.start(Buffer.get(media));
                if (!(start + 1 >= removeEnd)) {
                  _context6.next = 10;
                  break;
                }
                return _context6.abrupt("return");
              case 10:
                return _context6.abrupt("return", this._mse.clearBuffer(0, removeEnd).then(function() {
                  return _this2.flv.emit(EVENT.REMOVE_BUFFER, {
                    removeEnd
                  });
                }));
              case 11:
              case "end":
                return _context6.stop();
            }
        }, _callee6, this);
      }));
      function evictBuffer(_x3) {
        return _evictBuffer.apply(this, arguments);
      }
      return evictBuffer;
    }()
  }, {
    key: "_emitMetaParsedEvent",
    value: function _emitMetaParsedEvent(videoTrack, audioTrack) {
      if (videoTrack.exist()) {
        this.flv.emit(EVENT.METADATA_PARSED, {
          type: "video",
          track: videoTrack,
          meta: {
            codec: videoTrack.codec,
            timescale: videoTrack.timescale,
            width: videoTrack.width,
            height: videoTrack.height,
            sarRatio: videoTrack.sarRatio,
            baseDts: videoTrack.baseDts
          }
        });
      }
      if (audioTrack.exist()) {
        this.flv.emit(EVENT.METADATA_PARSED, {
          type: "audio",
          track: audioTrack,
          meta: {
            codec: audioTrack.codec,
            channelCount: audioTrack.channelCount,
            sampleRate: audioTrack.sampleRate,
            timescale: audioTrack.timescale,
            baseDts: audioTrack.baseDts
          }
        });
      }
      logger.debug("track parsed", videoTrack, audioTrack);
    }
  }, {
    key: "_fireEvents",
    value: function _fireEvents(videoTrack, audioTrack, metadataTrack) {
      var _this3 = this;
      logger.debug("videoTrack samples count: ".concat(videoTrack.samples.length, ", audioTrack samples count: ").concat(audioTrack.samples.length));
      metadataTrack.flvScriptSamples.forEach(function(sample) {
        _this3.flv.emit(EVENT.FLV_SCRIPT_DATA, sample);
        logger.debug("flvScriptData", sample);
      });
      videoTrack.samples.forEach(function(sample) {
        if (sample.keyframe) {
          _this3.flv.emit(EVENT.KEYFRAME, {
            pts: sample.originPts
          });
        }
      });
      videoTrack.warnings.forEach(function(warn) {
        var type;
        switch (warn.type) {
          case WarningType.LARGE_AV_SHIFT:
            type = EVENT.LARGE_AV_FIRST_FRAME_GAP_DETECT;
            break;
          case WarningType.LARGE_VIDEO_GAP:
            type = EVENT.LARGE_VIDEO_DTS_GAP_DETECT;
            break;
          case WarningType.LARGE_VIDEO_GAP_BETWEEN_CHUNK:
            type = EVENT.MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT;
            break;
        }
        if (type)
          _this3.flv.emit(EVENT.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {
            type
          }));
        logger.warn("video exception", warn);
      });
      audioTrack.warnings.forEach(function(warn) {
        var type;
        switch (warn.type) {
          case WarningType.LARGE_AUDIO_GAP:
            type = EVENT.LARGE_AUDIO_DTS_GAP_DETECT;
            break;
          case WarningType.AUDIO_FILLED:
            type = EVENT.AUDIO_GAP_DETECT;
            break;
          case WarningType.AUDIO_DROPPED:
            type = EVENT.AUDIO_OVERLAP_DETECT;
            break;
        }
        if (type)
          _this3.flv.emit(EVENT.STREAM_EXCEPTION, _objectSpread2(_objectSpread2({}, warn), {}, {
            type
          }));
        logger.warn("audio exception", warn);
      });
      metadataTrack.seiSamples.forEach(function(sei) {
        _this3.flv.emit(EVENT.SEI, _objectSpread2(_objectSpread2({}, sei), {}, {
          sei: {
            code: sei.data.type,
            content: sei.data.payload,
            dts: sei.pts
          }
        }));
      });
    }
  }]);
  return BufferService2;
}();

// node_modules/xgplayer-flv/es/flv/options.js
function getOption(opts) {
  var ret = _objectSpread2({
    retryCount: 3,
    retryDelay: 1e3,
    disconnectRetryCount: 0,
    loadTimeout: 1e4,
    maxReaderInterval: 5e3,
    preloadTime: 5,
    defaultVodLoadSize: 1e7,
    isLive: false,
    softDecode: false,
    bufferBehind: 10,
    maxJumpDistance: 3,
    analyzeDuration: 2e4,
    seamlesslyReload: false,
    keepStatusAfterSwitch: true,
    onlyVideo: false,
    onlyAudio: false,
    preferMMS: false,
    mseLowLatency: true,
    durationForMSELowLatencyOff: 6,
    chunkCountForSpeed: 50,
    skipChunkSize: 1e3,
    longtimeNoReceived: 3e3,
    enableStartGapJump: true
  }, opts);
  if (ret.isLive) {
    if (ret.preloadTime) {
      if (!ret.maxLatency) {
        ret.maxLatency = ret.preloadTime * 2;
      }
      if (!ret.targetLatency) {
        ret.targetLatency = ret.preloadTime;
      }
      if (ret.disconnectTime === null || ret.disconnectTime === void 0) {
        ret.disconnectTime = ret.maxLatency;
      }
    }
  }
  return ret;
}

// node_modules/xgplayer-flv/es/flv/utils.js
function searchKeyframeIndex(list, value) {
  var idx = 0;
  var last = list.length - 1;
  var mid = 0;
  var lbound = 0;
  var ubound = last;
  if (value < list[0]) {
    idx = 0;
    lbound = ubound + 1;
  }
  while (lbound <= ubound) {
    mid = lbound + Math.floor((ubound - lbound) / 2);
    if (mid === last || value >= list[mid] && value < list[mid + 1]) {
      idx = mid;
      break;
    } else if (list[mid] < value) {
      lbound = mid + 1;
    } else {
      ubound = mid - 1;
    }
  }
  return idx;
}

// node_modules/xgplayer-flv/es/flv/index.js
var logger2 = new Logger("flv");
var MAX_HOLE = 0.1;
var MAX_START_GAP = 0.3;
var Flv = function(_EventEmitter) {
  _inherits(Flv2, _EventEmitter);
  var _super = _createSuper(Flv2);
  function Flv2(_opts) {
    var _this;
    _classCallCheck(this, Flv2);
    _this = _super.call(this);
    _defineProperty(_assertThisInitialized(_this), "media", null);
    _defineProperty(_assertThisInitialized(_this), "_loading", false);
    _defineProperty(_assertThisInitialized(_this), "_opts", null);
    _defineProperty(_assertThisInitialized(_this), "_bufferService", null);
    _defineProperty(_assertThisInitialized(_this), "_gapService", null);
    _defineProperty(_assertThisInitialized(_this), "_stats", null);
    _defineProperty(_assertThisInitialized(_this), "_mediaLoader", null);
    _defineProperty(_assertThisInitialized(_this), "_maxChunkWaitTimer", null);
    _defineProperty(_assertThisInitialized(_this), "_tickTimer", null);
    _defineProperty(_assertThisInitialized(_this), "_tickInterval", 500);
    _defineProperty(_assertThisInitialized(_this), "_urlSwitching", false);
    _defineProperty(_assertThisInitialized(_this), "_seamlessSwitching", false);
    _defineProperty(_assertThisInitialized(_this), "_disconnectRetryCount", 0);
    _defineProperty(_assertThisInitialized(_this), "_preLoadEndPoint", 0);
    _defineProperty(_assertThisInitialized(_this), "_keyframes", null);
    _defineProperty(_assertThisInitialized(_this), "_acceptRanges", true);
    _defineProperty(_assertThisInitialized(_this), "_onProgress", function() {
      var _ref2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee(chunk, done, _ref, response) {
        var startTime, endTime, st, firstByteTime, _this$_mediaLoader, headers, elapsed, _this$_bufferService, remaining, maxReaderInterval;
        return _regeneratorRuntime().wrap(function _callee$(_context) {
          while (1)
            switch (_context.prev = _context.next) {
              case 0:
                startTime = _ref.startTime, endTime = _ref.endTime, st = _ref.st, firstByteTime = _ref.firstByteTime;
                _this._loading = !done;
                if (_this._firstProgressEmit) {
                  _context.next = 13;
                  break;
                }
                if (_this.media) {
                  _context.next = 6;
                  break;
                }
                (_this$_mediaLoader = _this._mediaLoader) === null || _this$_mediaLoader === void 0 ? void 0 : _this$_mediaLoader.cancel();
                return _context.abrupt("return");
              case 6:
                headers = response.headers;
                elapsed = st ? firstByteTime - st : endTime - startTime;
                _this.emit(EVENT.TTFB, {
                  url: _this._opts.url,
                  responseUrl: response.url,
                  elapsed
                });
                _this.emit(EVENT.LOAD_RESPONSE_HEADERS, {
                  headers
                });
                _this._transferCost.set(TRANSFER_EVENT.TTFB, elapsed);
                _this._acceptRanges = !!(headers !== null && headers !== void 0 && headers.get("Accept-Ranges")) || !!(headers !== null && headers !== void 0 && headers.get("Content-Range"));
                _this._firstProgressEmit = true;
              case 13:
                if (_this._bufferService) {
                  _context.next = 15;
                  break;
                }
                return _context.abrupt("return");
              case 15:
                clearTimeout(_this._maxChunkWaitTimer);
                _this._bandwidthService.addChunkRecord(chunk === null || chunk === void 0 ? void 0 : chunk.byteLength, endTime - startTime);
                _context.prev = 17;
                _context.next = 20;
                return _this._bufferService.appendBuffer(chunk);
              case 20:
                (_this$_bufferService = _this._bufferService) === null || _this$_bufferService === void 0 ? void 0 : _this$_bufferService.evictBuffer(_this._opts.bufferBehind);
                _context.next = 33;
                break;
              case 23:
                _context.prev = 23;
                _context.t0 = _context["catch"](17);
                if (!(!_this.isLive && _this._bufferService.isFull())) {
                  _context.next = 32;
                  break;
                }
                _context.next = 28;
                return _this._mediaLoader.cancel();
              case 28:
                _this._loading = false;
                remaining = _this.bufferInfo().remaining;
                _this._opts.preloadTime = parseInt(remaining) / 2;
                return _context.abrupt("return");
              case 32:
                return _context.abrupt("return", _this._emitError(StreamingError.create(_context.t0)));
              case 33:
                if (_this._urlSwitching) {
                  _this._urlSwitching = false;
                  _this.emit(EVENT.SWITCH_URL_SUCCESS, {
                    url: _this._opts.url
                  });
                }
                if (_this._seamlessSwitching) {
                  _this._seamlessSwitching = false;
                  _this._tick();
                }
                if (!(done && !_this.media.seeking)) {
                  _context.next = 40;
                  break;
                }
                _this.emit(EVENT.LOAD_COMPLETE);
                logger2.debug("load done");
                if (_this.isLive && _this._disconnectRetryCount <= 0) {
                  _this._end();
                }
                return _context.abrupt("return");
              case 40:
                if (_this.isLive) {
                  _context.next = 42;
                  break;
                }
                return _context.abrupt("return");
              case 42:
                maxReaderInterval = _this._opts.maxReaderInterval;
                if (maxReaderInterval && _this._firstProgressEmit) {
                  clearTimeout(_this._maxChunkWaitTimer);
                  _this._maxChunkWaitTimer = setTimeout(function() {
                    if (_this._disconnectRetryCount) {
                      _this._disconnectRetryCount--;
                      _this.load();
                      return;
                    }
                    logger2.debug("onMaxChunkWait", maxReaderInterval);
                    _this._end();
                  }, maxReaderInterval);
                }
              case 44:
              case "end":
                return _context.stop();
            }
        }, _callee, null, [[17, 23]]);
      }));
      return function(_x, _x2, _x3, _x4) {
        return _ref2.apply(this, arguments);
      };
    }());
    _defineProperty(_assertThisInitialized(_this), "_onRetryError", function(error, retryTime) {
      logger2.debug("load retry", error, retryTime);
      _this.emit(EVENT.LOAD_RETRY, {
        error: StreamingError.network(error),
        retryTime
      });
    });
    _defineProperty(_assertThisInitialized(_this), "_end", function() {
      _this._clear();
      if (_this._bufferService) {
        _this._bufferService.endOfStream();
      }
      logger2.debug("end stream");
    });
    _defineProperty(_assertThisInitialized(_this), "_resetDisconnectCount", function() {
      _this._disconnectRetryCount = _this._opts.disconnectRetryCount;
    });
    _defineProperty(_assertThisInitialized(_this), "_tick", function() {
      clearTimeout(_this._tickTimer);
      var _assertThisInitialize = _assertThisInitialized(_this), media = _assertThisInitialize.media;
      if (!media)
        return;
      _this._tickTimer = setTimeout(_this._tick, _this._tickInterval);
      var bufferEnd = Buffer.end(Buffer.get(media));
      if (bufferEnd < MAX_HOLE || !media.readyState)
        return;
      var opts = _this._opts;
      if (isMediaPlaying(media) && media.currentTime) {
        if (_this._gapService) {
          _this._gapService.do(media, opts.maxJumpDistance, _this.isLive, 3);
        }
      } else {
        if (!media.currentTime && _this._gapService && opts.enableStartGapJump) {
          var gapJump = _this._opts.mseLowLatency || _this._opts.mseLowLatency === false && _this.bufferInfo(MAX_START_GAP).nextStart;
          if (gapJump) {
            _this._gapService.do(media, opts.maxJumpDistance, _this.isLive, 3);
          }
          return;
        }
        if (opts.isLive && media.readyState === 4 && bufferEnd - media.currentTime > opts.disconnectTime) {
          _this.disconnect();
        }
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_onPlay", function() {
      var _this$media, _this$media$buffered;
      var canReplay = _this._opts.softDecode || ((_this$media = _this.media) === null || _this$media === void 0 ? void 0 : (_this$media$buffered = _this$media.buffered) === null || _this$media$buffered === void 0 ? void 0 : _this$media$buffered.length);
      if (_this.isLive) {
        if (!_this._loading && canReplay) {
          _this.replay(void 0, true);
        }
        return;
      }
      var info = _this.bufferInfo();
      if ((info.start || info.nextStart) > MAX_HOLE) {
        _this._tick();
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_onLoadeddata", function() {
      if (_this.isLive && !_this._opts.mseLowLatency) {
        if (_this.media.duration !== Infinity) {
          _this._bufferService.updateDuration(Infinity).catch(function(e) {
          });
        }
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_onSeeking", _asyncToGenerator(_regeneratorRuntime().mark(function _callee2() {
      return _regeneratorRuntime().wrap(function _callee2$(_context2) {
        while (1)
          switch (_context2.prev = _context2.next) {
            case 0:
              if (!_this.isLive && _this.seekable) {
                _this._preLoadEndPoint = -1;
                _this._checkPreload();
              }
            case 1:
            case "end":
              return _context2.stop();
          }
      }, _callee2);
    })));
    _defineProperty(_assertThisInitialized(_this), "_onTimeupdate", function() {
      if (!_this.media)
        return;
      var opts = _this._opts;
      var currentTime = _this.media.currentTime;
      if (opts.isLive && opts.maxLatency && opts.targetLatency) {
        var bufferEnd = Buffer.end(Buffer.get(_this.media));
        var latency = bufferEnd - currentTime;
        if (latency >= opts.maxLatency) {
          _this.media.currentTime = bufferEnd - opts.targetLatency;
          _this.emit(EVENT.CHASEFRAME, {
            currentTime: _this.media.currentTime,
            latency: opts.targetLatency
          });
        }
      }
      _this._seiService.throw(currentTime, true);
      if (opts.isLive || !_this.seekable || _this._loading)
        return;
      _this._checkPreload();
    });
    _defineProperty(_assertThisInitialized(_this), "_onWaiting", function() {
      if (_this.isLive && !_this._loading && _this._disconnectRetryCount) {
        _this._disconnectRetryCount--;
        _this.load();
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_onBufferUpdate", function() {
      if (_this._opts.isLive)
        return;
      var _this$bufferInfo = _this.bufferInfo(), end = _this$bufferInfo.end, nextEnd = _this$bufferInfo.nextEnd;
      if (Math.abs((end || nextEnd) - _this.media.duration) < 1) {
        _this._end();
        if (_this.media.readyState <= 2) {
          _this._tick();
        }
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_checkPreload", _asyncToGenerator(_regeneratorRuntime().mark(function _callee3() {
      var _this$bufferInfo2, _this$bufferInfo2$rem, remainingBuffer, opts, filepositions, times, currentTime, i, end, startByte;
      return _regeneratorRuntime().wrap(function _callee3$(_context3) {
        while (1)
          switch (_context3.prev = _context3.next) {
            case 0:
              _this$bufferInfo2 = _this.bufferInfo(), _this$bufferInfo2$rem = _this$bufferInfo2.remaining, remainingBuffer = _this$bufferInfo2$rem === void 0 ? 0 : _this$bufferInfo2$rem;
              opts = _this._opts;
              filepositions = _this._keyframes.filepositions;
              times = _this._keyframes.times;
              currentTime = _this.media.currentTime;
              if (!(remainingBuffer < opts.preloadTime)) {
                _context3.next = 19;
                break;
              }
              i = searchKeyframeIndex(_this._keyframes.times, currentTime + remainingBuffer + MAX_HOLE);
              end = searchKeyframeIndex(_this._keyframes.times, currentTime + remainingBuffer + _this._opts.preloadTime);
              if (end === i) {
                end = i + 1;
              }
              if (!(_this._preLoadEndPoint === end)) {
                _context3.next = 11;
                break;
              }
              return _context3.abrupt("return");
            case 11:
              startByte = filepositions[i];
              if (!(startByte === null || startByte === void 0)) {
                _context3.next = 14;
                break;
              }
              return _context3.abrupt("return");
            case 14:
              _context3.next = 16;
              return _this._mediaLoader.cancel();
            case 16:
              _this._loadData(null, [startByte, filepositions[end]]);
              _this._preLoadEndPoint = end;
              _this._bufferService.unContiguous(times[i]);
            case 19:
            case "end":
              return _context3.stop();
          }
      }, _callee3);
    })));
    _defineProperty(_assertThisInitialized(_this), "_onFlvScriptData", function(sample) {
      var _sample$data, _sample$data$onMetaDa, _sample$data2, _sample$data2$onMetaD;
      var keyframes = (_sample$data = sample.data) === null || _sample$data === void 0 ? void 0 : (_sample$data$onMetaDa = _sample$data.onMetaData) === null || _sample$data$onMetaDa === void 0 ? void 0 : _sample$data$onMetaDa.keyframes;
      var duration = (_sample$data2 = sample.data) === null || _sample$data2 === void 0 ? void 0 : (_sample$data2$onMetaD = _sample$data2.onMetaData) === null || _sample$data2$onMetaD === void 0 ? void 0 : _sample$data2$onMetaD.duration;
      if (keyframes) {
        _this._keyframes = keyframes;
      }
      if (!_this._opts.isLive && duration) {
        _this._bufferService.updateDuration(duration);
      }
    });
    _this._opts = getOption(_opts);
    _this.media = _this._opts.media || document.createElement("video");
    _this._opts.media = null;
    _this._firstProgressEmit = false;
    _this._mediaLoader = new NetLoader(_objectSpread2(_objectSpread2({}, _this._opts.fetchOptions), {}, {
      retry: _this._opts.retryCount,
      retryDelay: _this._opts.retryDelay,
      timeout: _this._opts.loadTimeout,
      onRetryError: _this._onRetryError,
      onProgress: _this._onProgress,
      responseType: "arraybuffer"
    }));
    _this._disconnectRetryCount = _this._opts.disconnectRetryCount;
    _this._transferCost = new TransferCost();
    _this._bufferService = new BufferService(_assertThisInitialized(_this), _this._opts.softDecode ? _this.media : void 0, _this._opts);
    _this._seiService = new SeiService(_assertThisInitialized(_this));
    _this._bandwidthService = new BandwidthService({
      chunkCountForSpeed: _this._opts.chunkCountForSpeed,
      skipChunkSize: _this._opts.skipChunkSize,
      longtimeNoReceived: _this._opts.longtimeNoReceived
    });
    _this._stats = new MediaStatsService(_assertThisInitialized(_this));
    if (!_this._opts.softDecode) {
      _this._gapService = new GapService();
    }
    _this.media.addEventListener("play", _this._onPlay);
    _this.media.addEventListener("loadeddata", _this._onLoadeddata);
    _this.media.addEventListener("seeking", _this._onSeeking);
    _this.media.addEventListener("timeupdate", _this._onTimeupdate);
    _this.media.addEventListener("progress", _this._onBufferUpdate);
    _this.media.addEventListener("waiting", _this._onWaiting);
    _this.on(EVENT.FLV_SCRIPT_DATA, _this._onFlvScriptData);
    return _this;
  }
  _createClass(Flv2, [{
    key: "version",
    get: function get() {
      return "3.0.20";
    }
  }, {
    key: "isLive",
    get: function get() {
      return this._opts.isLive;
    }
  }, {
    key: "baseDts",
    get: function get() {
      var _this$_bufferService2;
      return (_this$_bufferService2 = this._bufferService) === null || _this$_bufferService2 === void 0 ? void 0 : _this$_bufferService2.baseDts;
    }
  }, {
    key: "seekable",
    get: function get() {
      return !!this._keyframes && this._acceptRanges;
    }
  }, {
    key: "loader",
    get: function get() {
      return this._mediaLoader;
    }
  }, {
    key: "blobUrl",
    get: function get() {
      var _this$_bufferService3;
      return (_this$_bufferService3 = this._bufferService) === null || _this$_bufferService3 === void 0 ? void 0 : _this$_bufferService3.blobUrl;
    }
  }, {
    key: "speedInfo",
    value: function speedInfo() {
      return {
        speed: this._bandwidthService.getLatestSpeed(),
        avgSpeed: this._bandwidthService.getAvgSpeed(),
        totalSize: this._bandwidthService.getTotalSize(),
        totalCost: this._bandwidthService.getTotalCost()
      };
    }
  }, {
    key: "getStats",
    value: function getStats() {
      return this._stats.getStats();
    }
  }, {
    key: "bufferInfo",
    value: function bufferInfo() {
      var _this$media2;
      var maxHole = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : MAX_HOLE;
      return Buffer.info(Buffer.get(this.media), (_this$media2 = this.media) === null || _this$media2 === void 0 ? void 0 : _this$media2.currentTime, maxHole);
    }
  }, {
    key: "playbackQuality",
    value: function playbackQuality() {
      return getVideoPlaybackQuality(this.media);
    }
  }, {
    key: "load",
    value: function() {
      var _load = _asyncToGenerator(_regeneratorRuntime().mark(function _callee4(url) {
        var reuseMse, _args4 = arguments;
        return _regeneratorRuntime().wrap(function _callee4$(_context4) {
          while (1)
            switch (_context4.prev = _context4.next) {
              case 0:
                reuseMse = _args4.length > 1 && _args4[1] !== void 0 ? _args4[1] : false;
                if (this._bufferService) {
                  _context4.next = 3;
                  break;
                }
                return _context4.abrupt("return");
              case 3:
                _context4.next = 5;
                return this._reset(reuseMse);
              case 5:
                this._loadData(url, this._opts.isLive ? [] : [0, this._opts.defaultVodLoadSize]);
                clearTimeout(this._tickTimer);
                this._tickTimer = setTimeout(this._tick, this._tickInterval);
              case 8:
              case "end":
                return _context4.stop();
            }
        }, _callee4, this);
      }));
      function load(_x5) {
        return _load.apply(this, arguments);
      }
      return load;
    }()
  }, {
    key: "replay",
    value: function() {
      var _replay = _asyncToGenerator(_regeneratorRuntime().mark(function _callee5() {
        var _this2 = this;
        var seamlesslyReload, isPlayEmit, _args5 = arguments;
        return _regeneratorRuntime().wrap(function _callee5$(_context5) {
          while (1)
            switch (_context5.prev = _context5.next) {
              case 0:
                seamlesslyReload = _args5.length > 0 && _args5[0] !== void 0 ? _args5[0] : this._opts.seamlesslyReload;
                isPlayEmit = _args5.length > 1 ? _args5[1] : void 0;
                if (this.media) {
                  _context5.next = 4;
                  break;
                }
                return _context5.abrupt("return");
              case 4:
                this._resetDisconnectCount();
                if (!seamlesslyReload) {
                  _context5.next = 11;
                  break;
                }
                _context5.next = 8;
                return this._clear();
              case 8:
                setTimeout(function() {
                  _this2._seamlessSwitching = true;
                  _this2._loadData(_this2._opts.url);
                  _this2._bufferService.seamlessSwitch();
                });
                _context5.next = 13;
                break;
              case 11:
                _context5.next = 13;
                return this.load();
              case 13:
                return _context5.abrupt("return", this.media.play(!isPlayEmit).catch(function() {
                }));
              case 14:
              case "end":
                return _context5.stop();
            }
        }, _callee5, this);
      }));
      function replay() {
        return _replay.apply(this, arguments);
      }
      return replay;
    }()
  }, {
    key: "disconnect",
    value: function disconnect() {
      var _this$_bufferService4;
      logger2.debug("disconnect!");
      (_this$_bufferService4 = this._bufferService) === null || _this$_bufferService4 === void 0 ? void 0 : _this$_bufferService4.resetSeamlessSwitchStats();
      return this._clear();
    }
  }, {
    key: "switchURL",
    value: function() {
      var _switchURL = _asyncToGenerator(_regeneratorRuntime().mark(function _callee7(url, seamless) {
        var _this3 = this;
        return _regeneratorRuntime().wrap(function _callee7$(_context7) {
          while (1)
            switch (_context7.prev = _context7.next) {
              case 0:
                if (this._bufferService) {
                  _context7.next = 2;
                  break;
                }
                return _context7.abrupt("return");
              case 2:
                this._resetDisconnectCount();
                if (!(this._loading && seamless)) {
                  _context7.next = 6;
                  break;
                }
                this._bufferService.seamlessLoadingSwitch = function() {
                  var _ref5 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee6(pts) {
                    return _regeneratorRuntime().wrap(function _callee6$(_context6) {
                      while (1)
                        switch (_context6.prev = _context6.next) {
                          case 0:
                            _context6.next = 2;
                            return _this3._clear();
                          case 2:
                            _this3._bufferService.seamlessLoadingSwitching = true;
                            _this3._urlSwitching = true;
                            _this3._seamlessSwitching = true;
                            _this3._bufferService.seamlessSwitch();
                            _this3._loadData(url);
                          case 7:
                          case "end":
                            return _context6.stop();
                        }
                    }, _callee6);
                  }));
                  return function(_x8) {
                    return _ref5.apply(this, arguments);
                  };
                }();
                return _context7.abrupt("return");
              case 6:
                if (!(!seamless || !this._opts.isLive)) {
                  _context7.next = 11;
                  break;
                }
                _context7.next = 9;
                return this.load(url);
              case 9:
                this._urlSwitching = true;
                return _context7.abrupt("return", this.media.play(true).catch(function() {
                }));
              case 11:
                _context7.next = 13;
                return this._clear();
              case 13:
                setTimeout(function() {
                  _this3._urlSwitching = true;
                  _this3._seamlessSwitching = true;
                  _this3._loadData(url);
                  _this3._bufferService.seamlessSwitch();
                });
              case 14:
              case "end":
                return _context7.stop();
            }
        }, _callee7, this);
      }));
      function switchURL(_x6, _x7) {
        return _switchURL.apply(this, arguments);
      }
      return switchURL;
    }()
  }, {
    key: "destroy",
    value: function() {
      var _destroy = _asyncToGenerator(_regeneratorRuntime().mark(function _callee8() {
        return _regeneratorRuntime().wrap(function _callee8$(_context8) {
          while (1)
            switch (_context8.prev = _context8.next) {
              case 0:
                if (this.media) {
                  _context8.next = 2;
                  break;
                }
                return _context8.abrupt("return");
              case 2:
                this.removeAllListeners();
                this._seiService.reset();
                this.media.removeEventListener("play", this._onPlay);
                this.media.removeEventListener("loadeddata", this._onLoadeddata);
                this.media.removeEventListener("seeking", this._onSeeking);
                this.media.removeEventListener("timeupdate", this._onTimeupdate);
                this.media.removeEventListener("waiting", this._onWaiting);
                this.media.removeEventListener("progress", this._onBufferUpdate);
                _context8.next = 12;
                return Promise.all([this._clear(), this._bufferService.destroy()]);
              case 12:
                this.media = null;
                this._bufferService = null;
              case 14:
              case "end":
                return _context8.stop();
            }
        }, _callee8, this);
      }));
      function destroy() {
        return _destroy.apply(this, arguments);
      }
      return destroy;
    }()
  }, {
    key: "_emitError",
    value: function _emitError(error) {
      var _this$media3;
      var endOfStream = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
      logger2.table(error);
      logger2.error(error);
      logger2.error((_this$media3 = this.media) === null || _this$media3 === void 0 ? void 0 : _this$media3.error);
      if (this._urlSwitching) {
        this._urlSwitching = false;
        this._seamlessSwitching = false;
        this.emit(EVENT.SWITCH_URL_FAILED, error);
      }
      this.emit(EVENT.ERROR, error);
      if (endOfStream) {
        this._seiService.reset();
        this._end();
      }
    }
  }, {
    key: "_reset",
    value: function() {
      var _reset2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee9() {
        var reuseMse, _args9 = arguments;
        return _regeneratorRuntime().wrap(function _callee9$(_context9) {
          while (1)
            switch (_context9.prev = _context9.next) {
              case 0:
                reuseMse = _args9.length > 0 && _args9[0] !== void 0 ? _args9[0] : false;
                this._seiService.reset();
                this._bandwidthService.reset();
                this._stats.reset();
                _context9.next = 6;
                return this._clear();
              case 6:
                _context9.next = 8;
                return this._bufferService.reset(reuseMse);
              case 8:
              case "end":
                return _context9.stop();
            }
        }, _callee9, this);
      }));
      function _reset() {
        return _reset2.apply(this, arguments);
      }
      return _reset;
    }()
  }, {
    key: "_loadData",
    value: function() {
      var _loadData2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee10(url, range) {
        var finnalUrl;
        return _regeneratorRuntime().wrap(function _callee10$(_context10) {
          while (1)
            switch (_context10.prev = _context10.next) {
              case 0:
                if (url)
                  this._opts.url = url;
                finnalUrl = url = this._opts.url;
                if (url) {
                  _context10.next = 4;
                  break;
                }
                throw new Error("Source url is missing");
              case 4:
                if (this._opts.preProcessUrl) {
                  finnalUrl = this._opts.preProcessUrl(url).url;
                }
                this._mediaLoader.finnalUrl = finnalUrl;
                this.emit(EVENT.LOAD_START, {
                  url: finnalUrl,
                  seamlessSwitching: this._seamlessSwitching
                });
                logger2.debug("load data, loading:", this._loading, finnalUrl);
                if (!this._loading) {
                  _context10.next = 11;
                  break;
                }
                _context10.next = 11;
                return this._mediaLoader.cancel();
              case 11:
                this._loading = true;
                _context10.prev = 12;
                _context10.next = 15;
                return this._mediaLoader.load({
                  url: finnalUrl,
                  range
                });
              case 15:
                _context10.next = 21;
                break;
              case 17:
                _context10.prev = 17;
                _context10.t0 = _context10["catch"](12);
                this._loading = false;
                return _context10.abrupt("return", this._emitError(StreamingError.network(_context10.t0), false));
              case 21:
              case "end":
                return _context10.stop();
            }
        }, _callee10, this, [[12, 17]]);
      }));
      function _loadData(_x9, _x10) {
        return _loadData2.apply(this, arguments);
      }
      return _loadData;
    }()
  }, {
    key: "_clear",
    value: function() {
      var _clear2 = _asyncToGenerator(_regeneratorRuntime().mark(function _callee11() {
        return _regeneratorRuntime().wrap(function _callee11$(_context11) {
          while (1)
            switch (_context11.prev = _context11.next) {
              case 0:
                if (!this._mediaLoader) {
                  _context11.next = 3;
                  break;
                }
                _context11.next = 3;
                return this._mediaLoader.cancel();
              case 3:
                clearTimeout(this._maxChunkWaitTimer);
                clearTimeout(this._tickTimer);
                this._loading = false;
                this._firstProgressEmit = false;
              case 7:
              case "end":
                return _context11.stop();
            }
        }, _callee11, this);
      }));
      function _clear() {
        return _clear2.apply(this, arguments);
      }
      return _clear;
    }()
  }], [{
    key: "isSupported",
    value: function isSupported(mediaType) {
      if (!mediaType || mediaType === "video" || mediaType === "audio") {
        return MSE.isSupported();
      }
      return typeof WebAssembly !== "undefined";
    }
  }, {
    key: "enableLogger",
    value: function enableLogger() {
      Logger.enable();
      Logger2.enable();
    }
  }, {
    key: "disableLogger",
    value: function disableLogger() {
      Logger.disable();
      Logger2.disable();
    }
  }]);
  return Flv2;
}(import_eventemitter3.default);
try {
  if (localStorage.getItem("xgd")) {
    Flv.enableLogger();
  } else {
    Flv.disableLogger();
  }
} catch (error) {
}

// node_modules/xgplayer-flv/es/plugin-extension.js
var PluginExtension = function() {
  function PluginExtension2(opts, plugin) {
    var _this = this;
    _classCallCheck(this, PluginExtension2);
    _defineProperty(this, "_onLowDecode", function() {
      var _this$_plugin, _this$_plugin$player, _this$_plugin2, _this$_plugin2$player;
      var _this$_opts = _this._opts, media = _this$_opts.media, innerDegrade = _this$_opts.innerDegrade, backupURL = _this$_opts.backupURL;
      (_this$_plugin = _this._plugin) === null || _this$_plugin === void 0 ? void 0 : (_this$_plugin$player = _this$_plugin.player) === null || _this$_plugin$player === void 0 ? void 0 : _this$_plugin$player.emit("lowdecode", media.degradeInfo);
      (_this$_plugin2 = _this._plugin) === null || _this$_plugin2 === void 0 ? void 0 : (_this$_plugin2$player = _this$_plugin2.player) === null || _this$_plugin2$player === void 0 ? void 0 : _this$_plugin2$player.emit("core_event", _objectSpread2(_objectSpread2({}, media.degradeInfo), {}, {
        eventName: EVENT.LOWDECODE
      }));
      if ((innerDegrade === 1 || innerDegrade === 3) && backupURL) {
        _this._degrade(backupURL);
      }
    });
    _defineProperty(this, "_degrade", function(url) {
      var player = _this._plugin.player;
      var originVideo = player.video;
      if ((originVideo === null || originVideo === void 0 ? void 0 : originVideo.TAG) !== "MVideo")
        return;
      var newVideo = player.video.degradeVideo;
      player.video = newVideo;
      originVideo.degrade(url);
      if (url) {
        player.config.url = url;
      }
      var firstChild = player.root.firstChild;
      if (firstChild.TAG === "MVideo") {
        player.root.replaceChild(newVideo, firstChild);
      }
      var flvPlugin = _this._plugin.constructor.pluginName.toLowerCase();
      player.unRegisterPlugin(flvPlugin);
      player.once("canplay", function() {
        player.play();
      });
    });
    _defineProperty(this, "forceDegradeToVideo", function(url) {
      var innerDegrade = _this._opts.innerDegrade;
      if (innerDegrade === 1 || innerDegrade === 3) {
        _this._degrade(url);
      }
    });
    this._opts = opts;
    this._plugin = plugin;
    this._init();
  }
  _createClass(PluginExtension2, [{
    key: "_init",
    value: function _init() {
      var _this$_opts2 = this._opts, media = _this$_opts2.media, isLive = _this$_opts2.isLive, preloadTime = _this$_opts2.preloadTime, innerDegrade = _this$_opts2.innerDegrade, decodeMode = _this$_opts2.decodeMode;
      if (!media)
        return;
      if (!isLive && media.setPlayMode) {
        media.setPlayMode("VOD");
        return;
      }
      if (innerDegrade) {
        media.setAttribute("innerdegrade", innerDegrade);
      }
      if (preloadTime) {
        media.setAttribute("preloadtime", preloadTime);
      }
      if (media.setDecodeMode) {
        media.setDecodeMode(decodeMode);
      }
      this._bindEvents();
    }
  }, {
    key: "_bindEvents",
    value: function _bindEvents() {
      var media = this._opts.media;
      media.addEventListener("lowdecode", this._onLowDecode);
    }
  }, {
    key: "destroy",
    value: function destroy() {
      var _this$_opts3, _this$_opts3$media;
      (_this$_opts3 = this._opts) === null || _this$_opts3 === void 0 ? void 0 : (_this$_opts3$media = _this$_opts3.media) === null || _this$_opts3$media === void 0 ? void 0 : _this$_opts3$media.removeEventListener("lowdecode", this._onLowDecode);
      this._plugin = null;
    }
  }]);
  return PluginExtension2;
}();

// node_modules/xgplayer-flv/es/plugin.js
var FlvPlugin = function(_BasePlugin) {
  _inherits(FlvPlugin2, _BasePlugin);
  var _super = _createSuper(FlvPlugin2);
  function FlvPlugin2() {
    var _this;
    _classCallCheck(this, FlvPlugin2);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _super.call.apply(_super, [this].concat(args));
    _defineProperty(_assertThisInitialized(_this), "logger", logger2);
    _defineProperty(_assertThisInitialized(_this), "flv", null);
    _defineProperty(_assertThisInitialized(_this), "pluginExtension", null);
    _defineProperty(_assertThisInitialized(_this), "getStats", function() {
      var _this$flv;
      return (_this$flv = _this.flv) === null || _this$flv === void 0 ? void 0 : _this$flv.getStats();
    });
    _defineProperty(_assertThisInitialized(_this), "destroy", function() {
      var _this$pluginExtension;
      if (_this.flv) {
        _this.flv.destroy();
        _this.flv = null;
      }
      (_this$pluginExtension = _this.pluginExtension) === null || _this$pluginExtension === void 0 ? void 0 : _this$pluginExtension.destroy();
      _this.pluginExtension = null;
    });
    _defineProperty(_assertThisInitialized(_this), "_onSwitchURL", function(url, seamless) {
      if (_this.flv) {
        var _this$player$config, _this$player$config$f;
        _this.player.config.url = url;
        if (_typeof(seamless) === "object") {
          seamless = seamless.seamless;
        }
        _this.flv.switchURL(url, seamless);
        if (!seamless && (_this$player$config = _this.player.config) !== null && _this$player$config !== void 0 && (_this$player$config$f = _this$player$config.flv) !== null && _this$player$config$f !== void 0 && _this$player$config$f.keepStatusAfterSwitch) {
          _this._keepPauseStatus();
        }
      }
    });
    _defineProperty(_assertThisInitialized(_this), "_keepPauseStatus", function() {
      var paused = _this.player.paused;
      if (!paused)
        return;
      _this.player.once("canplay", function() {
        _this.player.pause();
      });
    });
    _defineProperty(_assertThisInitialized(_this), "_onDefinitionChange", function(_ref) {
      var to = _ref.to;
      if (_this.flv)
        _this.flv.switchURL(to);
    });
    return _this;
  }
  _createClass(FlvPlugin2, [{
    key: "core",
    get: function get() {
      return this.flv;
    }
  }, {
    key: "version",
    get: function get() {
      var _this$flv2;
      return (_this$flv2 = this.flv) === null || _this$flv2 === void 0 ? void 0 : _this$flv2.version;
    }
  }, {
    key: "softDecode",
    get: function get() {
      var _this$player, _this$player$config2;
      var mediaType = (_this$player = this.player) === null || _this$player === void 0 ? void 0 : (_this$player$config2 = _this$player.config) === null || _this$player$config2 === void 0 ? void 0 : _this$player$config2.mediaType;
      return !!mediaType && mediaType !== "video" && mediaType !== "audio";
    }
  }, {
    key: "loader",
    get: function get() {
      var _this$flv3;
      return (_this$flv3 = this.flv) === null || _this$flv3 === void 0 ? void 0 : _this$flv3.loader;
    }
  }, {
    key: "transferCost",
    get: function get() {
      return this.flv._transferCost.transferCost;
    }
  }, {
    key: "beforePlayerInit",
    value: function beforePlayerInit() {
      var _this2 = this;
      var config = this.player.config;
      var mediaElem = this.player.media || this.player.video;
      if (!config.url)
        return;
      if (this.flv)
        this.flv.destroy();
      this.player.switchURL = this._onSwitchURL;
      var flvOpts = config.flv || {};
      if (flvOpts.disconnectTime === null || flvOpts.disconnectTime === void 0) {
        flvOpts.disconnectTime = 0;
      }
      this.flv = new Flv(_objectSpread2({
        softDecode: this.softDecode,
        isLive: config.isLive,
        media: mediaElem,
        preProcessUrl: function preProcessUrl(url, ext) {
          var _this2$player, _this2$player$preProc;
          return ((_this2$player = _this2.player) === null || _this2$player === void 0 ? void 0 : (_this2$player$preProc = _this2$player.preProcessUrl) === null || _this2$player$preProc === void 0 ? void 0 : _this2$player$preProc.call(_this2$player, url, ext)) || {
            url,
            ext
          };
        }
      }, flvOpts));
      if (!this.softDecode) {
        BasePlugin.defineGetterOrSetter(this.player, {
          url: {
            get: function get() {
              var _this2$flv;
              return (_this2$flv = _this2.flv) === null || _this2$flv === void 0 ? void 0 : _this2$flv.blobUrl;
            },
            configurable: true
          }
        });
      }
      if (this.softDecode) {
        this.pluginExtension = new PluginExtension(_objectSpread2({
          media: this.player.video,
          isLive: config.isLive
        }, config.flv), this);
        this.player.forceDegradeToVideo = function() {
          var _this2$pluginExtensio;
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          return (_this2$pluginExtensio = _this2.pluginExtension) === null || _this2$pluginExtensio === void 0 ? void 0 : _this2$pluginExtensio.forceDegradeToVideo.apply(_this2$pluginExtensio, args);
        };
      }
      if (config.isLive) {
        var _this$player2;
        (_this$player2 = this.player) === null || _this$player2 === void 0 ? void 0 : _this$player2.useHooks("replay", function() {
          var _this2$flv2;
          return (_this2$flv2 = _this2.flv) === null || _this2$flv2 === void 0 ? void 0 : _this2$flv2.replay();
        });
      }
      this.on(events_exports.URL_CHANGE, this._onSwitchURL);
      this.on(events_exports.DESTROY, this.destroy);
      this._transError();
      this._transCoreEvent(EVENT.TTFB);
      this._transCoreEvent(EVENT.LOAD_START);
      this._transCoreEvent(EVENT.LOAD_RESPONSE_HEADERS);
      this._transCoreEvent(EVENT.LOAD_COMPLETE);
      this._transCoreEvent(EVENT.LOAD_RETRY);
      this._transCoreEvent(EVENT.SOURCEBUFFER_CREATED);
      this._transCoreEvent(EVENT.ANALYZE_DURATION_EXCEEDED);
      this._transCoreEvent(EVENT.APPEND_BUFFER);
      this._transCoreEvent(EVENT.REMOVE_BUFFER);
      this._transCoreEvent(EVENT.BUFFEREOS);
      this._transCoreEvent(EVENT.KEYFRAME);
      this._transCoreEvent(EVENT.CHASEFRAME);
      this._transCoreEvent(EVENT.METADATA_PARSED);
      this._transCoreEvent(EVENT.SEI);
      this._transCoreEvent(EVENT.SEI_IN_TIME);
      this._transCoreEvent(EVENT.FLV_SCRIPT_DATA);
      this._transCoreEvent(EVENT.STREAM_EXCEPTION);
      this._transCoreEvent(EVENT.SWITCH_URL_SUCCESS);
      this._transCoreEvent(EVENT.SWITCH_URL_FAILED);
      this.flv.load(config.url, true);
    }
  }, {
    key: "_transError",
    value: function _transError() {
      var _this3 = this;
      this.flv.on(EVENT.ERROR, function(err) {
        if (_this3.player) {
          _this3.player.emit(events_exports.ERROR, new Errors(_this3.player, err));
        }
      });
    }
  }, {
    key: "_transCoreEvent",
    value: function _transCoreEvent(eventName) {
      var _this4 = this;
      this.flv.on(eventName, function(e) {
        if (_this4.player) {
          _this4.player.emit("core_event", _objectSpread2(_objectSpread2({}, e), {}, {
            eventName
          }));
        }
      });
    }
  }], [{
    key: "pluginName",
    get: function get() {
      return "flv";
    }
  }, {
    key: "isSupported",
    value: function isSupported(mediaType, codec) {
      return Flv.isSupported(mediaType, codec);
    }
  }, {
    key: "isSupportedMMS",
    value: function isSupportedMMS() {
      return typeof ManagedMediaSource !== "undefined";
    }
  }]);
  return FlvPlugin2;
}(BasePlugin);
_defineProperty(FlvPlugin, "Flv", Flv);
export {
  ERR,
  ERR_CODE,
  EVENT,
  Flv,
  FlvPlugin,
  StreamingError,
  FlvPlugin as default,
  logger2 as logger
};
//# sourceMappingURL=xgplayer-flv.js.map
