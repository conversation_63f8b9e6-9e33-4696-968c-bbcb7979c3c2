{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/webgl/TileContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{WGLDrawPhase as e}from\"./enums.js\";import r from\"./WGLContainer.js\";import s from\"./brushes/WGLBrushInfo.js\";import t from\"./brushes/WGLBrushStencil.js\";const n=(e,r)=>e.key.level-r.key.level!=0?e.key.level-r.key.level:e.key.row-r.key.row!=0?e.key.row-r.key.row:e.key.col-r.key.col;class i extends r{constructor(e){super(),this._tileInfoView=e}get requiresDedicatedFBO(){return!1}renderChildren(e){this.sortChildren(n),this.setStencilReference(e),super.renderChildren(e)}createRenderParams(e){const{state:r}=e,s=super.createRenderParams(e);return s.requiredLevel=this._tileInfoView.getClosestInfoForScale(r.scale).level,s.displayLevel=this._tileInfoView.tileInfo.scaleToZoom(r.scale),s}prepareRenderPasses(r){const n=super.prepareRenderPasses(r);return n.push(r.registerRenderPass({name:\"stencil\",brushes:[t],drawPhase:e.DEBUG|e.MAP|e.HIGHLIGHT,target:()=>this.getStencilTarget()})),has(\"esri-tiles-debug\")&&n.push(r.registerRenderPass({name:\"tileInfo\",brushes:[s],drawPhase:e.DEBUG,target:()=>this.children})),n}getStencilTarget(){return this.children}setStencilReference(e){let r=1;for(const s of this.children)s.stencilRef=r++}}export{i as default};\n"], "mappings": ";;;;;;;;;;;;;AAIyM,IAAM,IAAE,CAAC,GAAE,MAAI,EAAE,IAAI,QAAM,EAAE,IAAI,SAAO,IAAE,EAAE,IAAI,QAAM,EAAE,IAAI,QAAM,EAAE,IAAI,MAAI,EAAE,IAAI,OAAK,IAAE,EAAE,IAAI,MAAI,EAAE,IAAI,MAAI,EAAE,IAAI,MAAI,EAAE,IAAI;AAAI,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,GAAE,KAAK,gBAAc;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,eAAe,GAAE;AAAC,SAAK,aAAa,CAAC,GAAE,KAAK,oBAAoB,CAAC,GAAE,MAAM,eAAe,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAE;AAAC,UAAK,EAAC,OAAM,EAAC,IAAE,GAAE,IAAE,MAAM,mBAAmB,CAAC;AAAE,WAAO,EAAE,gBAAc,KAAK,cAAc,uBAAuB,EAAE,KAAK,EAAE,OAAM,EAAE,eAAa,KAAK,cAAc,SAAS,YAAY,EAAE,KAAK,GAAE;AAAA,EAAC;AAAA,EAAC,oBAAoB,GAAE;AAAC,UAAMA,KAAE,MAAM,oBAAoB,CAAC;AAAE,WAAOA,GAAE,KAAK,EAAE,mBAAmB,EAAC,MAAK,WAAU,SAAQ,CAAC,CAAC,GAAE,WAAU,EAAE,QAAM,EAAE,MAAI,EAAE,WAAU,QAAO,MAAI,KAAK,iBAAiB,EAAC,CAAC,CAAC,GAAE,IAAI,kBAAkB,KAAGA,GAAE,KAAK,EAAE,mBAAmB,EAAC,MAAK,YAAW,SAAQ,CAAC,CAAC,GAAE,WAAU,EAAE,OAAM,QAAO,MAAI,KAAK,SAAQ,CAAC,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,oBAAoB,GAAE;AAAC,QAAI,IAAE;AAAE,eAAU,KAAK,KAAK,SAAS,GAAE,aAAW;AAAA,EAAG;AAAC;", "names": ["n"]}