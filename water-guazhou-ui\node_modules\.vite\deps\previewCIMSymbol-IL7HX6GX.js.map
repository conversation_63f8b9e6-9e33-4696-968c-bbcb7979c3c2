{"version": 3, "sources": ["../../@arcgis/core/symbols/support/previewCIMSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{px2pt as e,pt2px as t}from\"../../core/screenUtils.js\";import{OverrideHelper as i,CIMSymbolHelper as o}from\"../cim/CIMSymbolHelper.js\";import{CIMSymbolRasterizer as l,GeometryStyle as n}from\"../cim/CIMSymbolRasterizer.js\";import{mapCIMSymbolToGeometryType as r}from\"../cim/utils.js\";import{SymbolSizeDefaults as s}from\"./previewUtils.js\";import{renderSymbol as a}from\"./renderUtils.js\";const h=new l(null,!0),m=e(s.size),c=e(s.maxSize),u=e(s.lineWidth),f=1;function y(e){const t=e?.size;if(\"number\"==typeof t)return{width:t,height:t};return{width:null!=t&&\"object\"==typeof t&&\"width\"in t?t.width:null,height:null!=t&&\"object\"==typeof t&&\"height\"in t?t.height:null}}async function d(e,l={}){const{node:s,opacity:d,symbolConfig:p}=l,g=\"object\"==typeof p&&\"isSquareFill\"in p&&p.isSquareFill,w=l.cimOptions||l,b=w.geometryType||r(e?.data?.symbol),M=y(l),{feature:j,fieldMap:v}=w;if(null==M.width||null==M.height){const t=await i.resolveSymbolOverrides(e.data,j,null,v,b);if(!t)return null;(e=e.clone()).data={type:\"CIMSymbolReference\",symbol:t},e.data.primitiveOverrides=void 0;const l=[];o.fetchResources(t,h.resourceManager,l),l.length>0&&await Promise.all(l);const n=o.getEnvelope(t,null,h.resourceManager),r=n?.width,s=n?.height;M.width=\"esriGeometryPolygon\"===b?m:\"esriGeometryPolyline\"===b?u:null!=r&&isFinite(r)?Math.min(r,c):m,M.height=\"esriGeometryPolygon\"===b?m:null!=s&&isFinite(s)?Math.max(Math.min(s,c),f):m}const S=await h.rasterizeCIMSymbolAsync(e,j,M,g||\"esriGeometryPolygon\"!==b?n.Preview:n.Legend,v,b);if(!S)return null;const{width:C,height:I}=S,P=document.createElement(\"canvas\");P.width=C,P.height=I;P.getContext(\"2d\").putImageData(S,0,0);const x=t(M.width),z=t(M.height),F=new Image(x,z);F.src=P.toDataURL(),null!=d&&(F.style.opacity=`${d}`);let G=F;if(null!=l.effectView){const e={shape:{type:\"image\",x:0,y:0,width:x,height:z,src:F.src},fill:null,stroke:null,offset:[0,0]};G=a([[e]],[x,z],{effectView:l.effectView})}return s&&G&&s.appendChild(G),G}export{d as previewCIMSymbol};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwY,IAAM,IAAE,IAAI,EAAE,MAAK,IAAE;AAArB,IAAuB,IAAE,EAAE,EAAE,IAAI;AAAjC,IAAmC,IAAE,EAAE,EAAE,OAAO;AAAhD,IAAkDA,KAAE,EAAE,EAAE,SAAS;AAAjE,IAAmE,IAAE;AAAE,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAED,MAAA,gBAAAA,GAAG;AAAK,MAAG,YAAU,OAAOC,GAAE,QAAM,EAAC,OAAMA,IAAE,QAAOA,GAAC;AAAE,SAAM,EAAC,OAAM,QAAMA,MAAG,YAAU,OAAOA,MAAG,WAAUA,KAAEA,GAAE,QAAM,MAAK,QAAO,QAAMA,MAAG,YAAU,OAAOA,MAAG,YAAWA,KAAEA,GAAE,SAAO,KAAI;AAAC;AAAC,eAAe,EAAED,IAAEE,KAAE,CAAC,GAAE;AAJvrB;AAIwrB,QAAK,EAAC,MAAK,GAAE,SAAQC,IAAE,cAAa,EAAC,IAAED,IAAE,IAAE,YAAU,OAAO,KAAG,kBAAiB,KAAG,EAAE,cAAa,IAAEA,GAAE,cAAYA,IAAE,IAAE,EAAE,gBAAc,GAAE,KAAAF,MAAA,gBAAAA,GAAG,SAAH,mBAAS,MAAM,GAAEI,KAAE,EAAEF,EAAC,GAAE,EAAC,SAAQ,GAAE,UAAS,EAAC,IAAE;AAAE,MAAG,QAAME,GAAE,SAAO,QAAMA,GAAE,QAAO;AAAC,UAAMH,KAAE,MAAM,GAAE,uBAAuBD,GAAE,MAAK,GAAE,MAAK,GAAE,CAAC;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,KAACD,KAAEA,GAAE,MAAM,GAAG,OAAK,EAAC,MAAK,sBAAqB,QAAOC,GAAC,GAAED,GAAE,KAAK,qBAAmB;AAAO,UAAME,KAAE,CAAC;AAAE,OAAE,eAAeD,IAAE,EAAE,iBAAgBC,EAAC,GAAEA,GAAE,SAAO,KAAG,MAAM,QAAQ,IAAIA,EAAC;AAAE,UAAM,IAAE,GAAE,YAAYD,IAAE,MAAK,EAAE,eAAe,GAAE,IAAE,uBAAG,OAAMI,KAAE,uBAAG;AAAO,IAAAD,GAAE,QAAM,0BAAwB,IAAE,IAAE,2BAAyB,IAAEL,KAAE,QAAM,KAAG,SAAS,CAAC,IAAE,KAAK,IAAI,GAAE,CAAC,IAAE,GAAEK,GAAE,SAAO,0BAAwB,IAAE,IAAE,QAAMC,MAAG,SAASA,EAAC,IAAE,KAAK,IAAI,KAAK,IAAIA,IAAE,CAAC,GAAE,CAAC,IAAE;AAAA,EAAC;AAAC,QAAM,IAAE,MAAM,EAAE,wBAAwBL,IAAE,GAAEI,IAAE,KAAG,0BAAwB,IAAE,EAAE,UAAQ,EAAE,QAAO,GAAE,CAAC;AAAE,MAAG,CAAC,EAAE,QAAO;AAAK,QAAK,EAAC,OAAM,GAAE,QAAO,EAAC,IAAE,GAAE,IAAE,SAAS,cAAc,QAAQ;AAAE,IAAE,QAAM,GAAE,EAAE,SAAO;AAAE,IAAE,WAAW,IAAI,EAAE,aAAa,GAAE,GAAE,CAAC;AAAE,QAAM,IAAE,EAAEA,GAAE,KAAK,GAAEE,KAAE,EAAEF,GAAE,MAAM,GAAE,IAAE,IAAI,MAAM,GAAEE,EAAC;AAAE,IAAE,MAAI,EAAE,UAAU,GAAE,QAAMH,OAAI,EAAE,MAAM,UAAQ,GAAGA,EAAC;AAAI,MAAI,IAAE;AAAE,MAAG,QAAMD,GAAE,YAAW;AAAC,UAAMF,KAAE,EAAC,OAAM,EAAC,MAAK,SAAQ,GAAE,GAAE,GAAE,GAAE,OAAM,GAAE,QAAOM,IAAE,KAAI,EAAE,IAAG,GAAE,MAAK,MAAK,QAAO,MAAK,QAAO,CAAC,GAAE,CAAC,EAAC;AAAE,QAAE,EAAE,CAAC,CAACN,EAAC,CAAC,GAAE,CAAC,GAAEM,EAAC,GAAE,EAAC,YAAWJ,GAAE,WAAU,CAAC;AAAA,EAAC;AAAC,SAAO,KAAG,KAAG,EAAE,YAAY,CAAC,GAAE;AAAC;", "names": ["u", "e", "t", "l", "d", "M", "s", "z"]}