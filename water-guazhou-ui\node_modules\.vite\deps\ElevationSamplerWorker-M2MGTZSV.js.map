{"version": 3, "sources": ["../../@arcgis/core/geometry/support/meshUtils/ElevationSamplerWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../core/has.js\";import{isSome as e}from\"../../../core/maybe.js\";import{PooledRBush as t}from\"../../../core/libs/rbush/PooledRBush.js\";import{georeferenceApplyTransform as r}from\"./georeference.js\";class n{async createIndex(r,n){const o=new Array;if(!r.vertexAttributes||!r.vertexAttributes.position)return new t;const s=this._createMeshData(r),a=e(n)?await n.invoke(\"createIndexThread\",s,{transferList:o}):this.createIndexThread(s).result;return this._createPooledRBush().fromJSON(a)}createIndexThread(e){const t=new Float64Array(e.position),r=this._createPooledRBush();return e.components?this._createIndexComponentsThread(r,t,e.components.map((e=>new Uint32Array(e)))):this._createIndexAllThread(r,t)}_createIndexAllThread(e,t){const r=new Array(t.length/9);let n=0;for(let s=0;s<t.length;s+=9)r[n++]=o(t,s+0,s+3,s+6);return e.load(r),{result:e.toJSON()}}_createIndexComponentsThread(e,t,r){let n=0;for(const o of r)n+=o.length/3;const s=new Array(n);let a=0;for(const i of r)for(let e=0;e<i.length;e+=3)s[a++]=o(t,3*i[e+0],3*i[e+1],3*i[e+2]);return e.load(s),{result:e.toJSON()}}_createMeshData(e){const t=(e.transform?r({position:e.vertexAttributes.position,normal:null,tangent:null},e.transform,e.spatialReference).position:e.vertexAttributes.position).buffer;return!e.components||e.components.some((e=>!e.faces))?{position:t}:{position:t,components:e.components.map((e=>e.faces))}}_createPooledRBush(){return new t(9,has(\"esri-csp-restrictions\")?e=>e:[\".minX\",\".minY\",\".maxX\",\".maxY\"])}}function o(e,t,r,n){return{minX:Math.min(e[t+0],e[r+0],e[n+0]),maxX:Math.max(e[t+0],e[r+0],e[n+0]),minY:Math.min(e[t+1],e[r+1],e[n+1]),maxY:Math.max(e[t+1],e[r+1],e[n+1]),p0:[e[t+0],e[t+1],e[t+2]],p1:[e[r+0],e[r+1],e[r+2]],p2:[e[n+0],e[n+1],e[n+2]]}}export{n as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2N,IAAM,IAAN,MAAO;AAAA,EAAC,MAAM,YAAYA,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAM,QAAG,CAACF,GAAE,oBAAkB,CAACA,GAAE,iBAAiB,SAAS,QAAO,IAAI;AAAE,UAAM,IAAE,KAAK,gBAAgBA,EAAC,GAAE,IAAE,EAAEC,EAAC,IAAE,MAAMA,GAAE,OAAO,qBAAoB,GAAE,EAAC,cAAaC,GAAC,CAAC,IAAE,KAAK,kBAAkB,CAAC,EAAE;AAAO,WAAO,KAAK,mBAAmB,EAAE,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkB,GAAE;AAAC,UAAM,IAAE,IAAI,aAAa,EAAE,QAAQ,GAAEF,KAAE,KAAK,mBAAmB;AAAE,WAAO,EAAE,aAAW,KAAK,6BAA6BA,IAAE,GAAE,EAAE,WAAW,IAAK,CAAAG,OAAG,IAAI,YAAYA,EAAC,CAAE,CAAC,IAAE,KAAK,sBAAsBH,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsB,GAAE,GAAE;AAAC,UAAMA,KAAE,IAAI,MAAM,EAAE,SAAO,CAAC;AAAE,QAAIC,KAAE;AAAE,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAG,EAAE,CAAAD,GAAEC,IAAG,IAAE,EAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC;AAAE,WAAO,EAAE,KAAKD,EAAC,GAAE,EAAC,QAAO,EAAE,OAAO,EAAC;AAAA,EAAC;AAAA,EAAC,6BAA6B,GAAE,GAAEA,IAAE;AAAC,QAAIC,KAAE;AAAE,eAAUC,MAAKF,GAAE,CAAAC,MAAGC,GAAE,SAAO;AAAE,UAAM,IAAE,IAAI,MAAMD,EAAC;AAAE,QAAI,IAAE;AAAE,eAAU,KAAKD,GAAE,UAAQG,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAG,EAAE,GAAE,GAAG,IAAE,EAAE,GAAE,IAAE,EAAEA,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC,GAAE,IAAE,EAAEA,KAAE,CAAC,CAAC;AAAE,WAAO,EAAE,KAAK,CAAC,GAAE,EAAC,QAAO,EAAE,OAAO,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgB,GAAE;AAAC,UAAM,KAAG,EAAE,YAAU,EAAE,EAAC,UAAS,EAAE,iBAAiB,UAAS,QAAO,MAAK,SAAQ,KAAI,GAAE,EAAE,WAAU,EAAE,gBAAgB,EAAE,WAAS,EAAE,iBAAiB,UAAU;AAAO,WAAM,CAAC,EAAE,cAAY,EAAE,WAAW,KAAM,CAAAA,OAAG,CAACA,GAAE,KAAM,IAAE,EAAC,UAAS,EAAC,IAAE,EAAC,UAAS,GAAE,YAAW,EAAE,WAAW,IAAK,CAAAA,OAAGA,GAAE,KAAM,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAI,EAAE,GAAE,IAAI,uBAAuB,IAAE,OAAG,IAAE,CAAC,SAAQ,SAAQ,SAAQ,OAAO,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEH,IAAEC,IAAE;AAAC,SAAM,EAAC,MAAK,KAAK,IAAI,EAAE,IAAE,CAAC,GAAE,EAAED,KAAE,CAAC,GAAE,EAAEC,KAAE,CAAC,CAAC,GAAE,MAAK,KAAK,IAAI,EAAE,IAAE,CAAC,GAAE,EAAED,KAAE,CAAC,GAAE,EAAEC,KAAE,CAAC,CAAC,GAAE,MAAK,KAAK,IAAI,EAAE,IAAE,CAAC,GAAE,EAAED,KAAE,CAAC,GAAE,EAAEC,KAAE,CAAC,CAAC,GAAE,MAAK,KAAK,IAAI,EAAE,IAAE,CAAC,GAAE,EAAED,KAAE,CAAC,GAAE,EAAEC,KAAE,CAAC,CAAC,GAAE,IAAG,CAAC,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,CAAC,GAAE,IAAG,CAAC,EAAED,KAAE,CAAC,GAAE,EAAEA,KAAE,CAAC,GAAE,EAAEA,KAAE,CAAC,CAAC,GAAE,IAAG,CAAC,EAAEC,KAAE,CAAC,GAAE,EAAEA,KAAE,CAAC,GAAE,EAAEA,KAAE,CAAC,CAAC,EAAC;AAAC;", "names": ["r", "n", "o", "e"]}