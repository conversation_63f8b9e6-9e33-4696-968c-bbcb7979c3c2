import {
  M,
  z
} from "./chunk-QFJ7EHPS.js";
import "./chunk-A2EXCZFA.js";
import "./chunk-EDV64J6E.js";
import "./chunk-OBW4AQOU.js";
import {
  ie,
  se
} from "./chunk-VXAO6YJP.js";
import "./chunk-BI4P4NAQ.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-MDHXGN24.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-DUEDINK5.js";
import {
  l
} from "./chunk-5VENLL23.js";
import {
  t
} from "./chunk-Q7KDWWJV.js";
import "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import {
  O
} from "./chunk-J4YX6DLU.js";
import "./chunk-O2BYTJI4.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-RURSJOSG.js";
import "./chunk-77E52HT5.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-P37TUI4J.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  e,
  u
} from "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/symbols/support/previewCIMSymbol.js
var h = new z(null, true);
var m = e(t.size);
var c = e(t.maxSize);
var u2 = e(t.lineWidth);
var f = 1;
function y(e2) {
  const t2 = e2 == null ? void 0 : e2.size;
  if ("number" == typeof t2) return { width: t2, height: t2 };
  return { width: null != t2 && "object" == typeof t2 && "width" in t2 ? t2.width : null, height: null != t2 && "object" == typeof t2 && "height" in t2 ? t2.height : null };
}
async function d(e2, l2 = {}) {
  var _a;
  const { node: s, opacity: d2, symbolConfig: p } = l2, g = "object" == typeof p && "isSquareFill" in p && p.isSquareFill, w = l2.cimOptions || l2, b = w.geometryType || O((_a = e2 == null ? void 0 : e2.data) == null ? void 0 : _a.symbol), M2 = y(l2), { feature: j, fieldMap: v } = w;
  if (null == M2.width || null == M2.height) {
    const t2 = await se.resolveSymbolOverrides(e2.data, j, null, v, b);
    if (!t2) return null;
    (e2 = e2.clone()).data = { type: "CIMSymbolReference", symbol: t2 }, e2.data.primitiveOverrides = void 0;
    const l3 = [];
    ie.fetchResources(t2, h.resourceManager, l3), l3.length > 0 && await Promise.all(l3);
    const n = ie.getEnvelope(t2, null, h.resourceManager), r = n == null ? void 0 : n.width, s2 = n == null ? void 0 : n.height;
    M2.width = "esriGeometryPolygon" === b ? m : "esriGeometryPolyline" === b ? u2 : null != r && isFinite(r) ? Math.min(r, c) : m, M2.height = "esriGeometryPolygon" === b ? m : null != s2 && isFinite(s2) ? Math.max(Math.min(s2, c), f) : m;
  }
  const S = await h.rasterizeCIMSymbolAsync(e2, j, M2, g || "esriGeometryPolygon" !== b ? M.Preview : M.Legend, v, b);
  if (!S) return null;
  const { width: C, height: I } = S, P = document.createElement("canvas");
  P.width = C, P.height = I;
  P.getContext("2d").putImageData(S, 0, 0);
  const x = u(M2.width), z2 = u(M2.height), F = new Image(x, z2);
  F.src = P.toDataURL(), null != d2 && (F.style.opacity = `${d2}`);
  let G = F;
  if (null != l2.effectView) {
    const e3 = { shape: { type: "image", x: 0, y: 0, width: x, height: z2, src: F.src }, fill: null, stroke: null, offset: [0, 0] };
    G = l([[e3]], [x, z2], { effectView: l2.effectView });
  }
  return s && G && s.appendChild(G), G;
}
export {
  d as previewCIMSymbol
};
//# sourceMappingURL=previewCIMSymbol-6JSLL3EW.js.map
