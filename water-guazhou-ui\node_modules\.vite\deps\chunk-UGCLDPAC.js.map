{"version": 3, "sources": ["../../@arcgis/core/core/workers/WorkerHandle.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{remove as e}from\"../arrayUtils.js\";import{makeHandle as t}from\"../handleUtils.js\";import r from\"../Logger.js\";import{isSome as s}from\"../maybe.js\";import{throwIfAborted as i}from\"../promiseUtils.js\";import{open as o}from\"./workers.js\";class h{constructor(e,t,s,i,h={}){this._mainMethod=t,this._transferLists=s,this._listeners=[],this._promise=o(e,{...h,schedule:i}).then((e=>{if(void 0===this._thread){this._thread=e,this._promise=null,h.hasInitialize&&this.broadcast({},\"initialize\");for(const e of this._listeners)this._connectListener(e)}else e.close()})),this._promise.catch((t=>r.getLogger(\"esri.core.workers.WorkerHandle\").error(`Failed to initialize ${e} worker: ${t}`)))}on(r,i){const o={removed:!1,eventName:r,callback:i,threadHandle:null};return this._listeners.push(o),this._connectListener(o),t((()=>{o.removed=!0,e(this._listeners,o),this._thread&&s(o.threadHandle)&&o.threadHandle.remove()}))}destroy(){this._thread&&(this._thread.close(),this._thread=null),this._promise=null}invoke(e,t){return this.invokeMethod(this._mainMethod,e,t)}invokeMethod(e,t,r){if(this._thread){const s=this._transferLists[e],i=s?s(t):[];return this._thread.invoke(e,t,{transferList:i,signal:r})}return this._promise?this._promise.then((()=>(i(r),this.invokeMethod(e,t,r)))):Promise.reject(null)}broadcast(e,t){return this._thread?Promise.all(this._thread.broadcast(t,e)).then((()=>{})):this._promise?this._promise.then((()=>this.broadcast(e,t))):Promise.reject()}get promise(){return this._promise}_connectListener(e){this._thread&&this._thread.on(e.eventName,e.callback).then((t=>{e.removed||(e.threadHandle=t)}))}}export{h as WorkerHandle};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIkP,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAE,GAAEA,IAAE,GAAEC,KAAE,CAAC,GAAE;AAAC,SAAK,cAAY,GAAE,KAAK,iBAAeD,IAAE,KAAK,aAAW,CAAC,GAAE,KAAK,WAAS,EAAE,GAAE,EAAC,GAAGC,IAAE,UAAS,EAAC,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAG,WAAS,KAAK,SAAQ;AAAC,aAAK,UAAQA,IAAE,KAAK,WAAS,MAAKD,GAAE,iBAAe,KAAK,UAAU,CAAC,GAAE,YAAY;AAAE,mBAAUC,MAAK,KAAK,WAAW,MAAK,iBAAiBA,EAAC;AAAA,MAAC,MAAM,CAAAA,GAAE,MAAM;AAAA,IAAC,CAAE,GAAE,KAAK,SAAS,MAAO,CAAAC,OAAG,EAAE,UAAU,gCAAgC,EAAE,MAAM,wBAAwB,CAAC,YAAYA,EAAC,EAAE,CAAE;AAAA,EAAC;AAAA,EAAC,GAAGC,IAAE,GAAE;AAAC,UAAM,IAAE,EAAC,SAAQ,OAAG,WAAUA,IAAE,UAAS,GAAE,cAAa,KAAI;AAAE,WAAO,KAAK,WAAW,KAAK,CAAC,GAAE,KAAK,iBAAiB,CAAC,GAAE,EAAG,MAAI;AAAC,QAAE,UAAQ,MAAG,EAAE,KAAK,YAAW,CAAC,GAAE,KAAK,WAAS,EAAE,EAAE,YAAY,KAAG,EAAE,aAAa,OAAO;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,YAAU,KAAK,QAAQ,MAAM,GAAE,KAAK,UAAQ,OAAM,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,OAAO,GAAE,GAAE;AAAC,WAAO,KAAK,aAAa,KAAK,aAAY,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAa,GAAE,GAAEA,IAAE;AAAC,QAAG,KAAK,SAAQ;AAAC,YAAMJ,KAAE,KAAK,eAAe,CAAC,GAAE,IAAEA,KAAEA,GAAE,CAAC,IAAE,CAAC;AAAE,aAAO,KAAK,QAAQ,OAAO,GAAE,GAAE,EAAC,cAAa,GAAE,QAAOI,GAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,WAAS,KAAK,SAAS,KAAM,OAAK,EAAEA,EAAC,GAAE,KAAK,aAAa,GAAE,GAAEA,EAAC,EAAG,IAAE,QAAQ,OAAO,IAAI;AAAA,EAAC;AAAA,EAAC,UAAU,GAAE,GAAE;AAAC,WAAO,KAAK,UAAQ,QAAQ,IAAI,KAAK,QAAQ,UAAU,GAAE,CAAC,CAAC,EAAE,KAAM,MAAI;AAAA,IAAC,CAAE,IAAE,KAAK,WAAS,KAAK,SAAS,KAAM,MAAI,KAAK,UAAU,GAAE,CAAC,CAAE,IAAE,QAAQ,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,iBAAiB,GAAE;AAAC,SAAK,WAAS,KAAK,QAAQ,GAAG,EAAE,WAAU,EAAE,QAAQ,EAAE,KAAM,OAAG;AAAC,QAAE,YAAU,EAAE,eAAa;AAAA,IAAE,CAAE;AAAA,EAAC;AAAC;", "names": ["s", "h", "e", "t", "r"]}