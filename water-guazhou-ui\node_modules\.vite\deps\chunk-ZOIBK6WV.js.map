{"version": 3, "sources": ["../../@arcgis/core/views/draw/support/drawUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{rad2deg as t}from\"../../../core/mathUtils.js\";import{create as n}from\"../../../geometry/support/aaBoundingRect.js\";import{getRingsOrPathsBounds as e}from\"../../../geometry/support/boundsUtils.js\";import{geometryToCoordinates as o}from\"../../../geometry/support/coordsUtils.js\";function i(t,n,e,i){if(null==i||t.hasZ||(i=void 0),\"point\"===t.type)return t.x+=n,t.y+=e,t.hasZ&&null!=i&&(t.z+=i),t;if(\"multipoint\"===t.type){const o=t.points;for(let t=0;t<o.length;t++)o[t]=l(o[t],n,e,i);return t}if(\"extent\"===t.type)return t.xmin+=n,t.xmax+=n,t.ymin+=e,t.ymax+=e,null!=i&&(t.zmin??(t.zmin=0),t.zmin+=i,t.zmax??(t.zmax=0),t.zmax+=i),t;const r=o(t),s=\"polyline\"===t.type?t.paths:t.rings;for(let o=0;o<r.length;o++){const t=r[o];for(let o=0;o<t.length;o++)t[o]=l(t[o],n,e,i)}return\"paths\"in t?t.paths=s:t.rings=s,t}function r(t,n,e,r,s){const m=t.clone(),a=r.resolution;if(\"point\"===m.type){if(s)i(m,n*a,-e*a);else{const t=r.state.transform,o=r.state.inverseTransform,i=t[0]*m.x+t[2]*m.y+t[4],s=t[1]*m.x+t[3]*m.y+t[5];m.x=o[0]*(i+n)+o[2]*(s+e)+o[4],m.y=o[1]*(i+n)+o[3]*(s+e)+o[5]}return m}if(\"multipoint\"===m.type){if(s)i(m,n*a,-e*a);else{const t=m.points,o=r.state.transform,i=r.state.inverseTransform;for(let r=0;r<t.length;r++){const s=t[r],m=o[0]*s[0]+o[2]*s[1]+o[4],a=o[1]*s[0]+o[3]*s[1]+o[5],l=i[0]*(m+n)+i[2]*(a+e)+i[4],p=i[1]*(m+n)+i[3]*(a+e)+i[5];t[r]=x(s,l,p,void 0)}}return m}if(\"extent\"===m.type){if(s)i(m,n*a,-e*a);else{const t=r.state.transform,o=r.state.inverseTransform,i=t[0]*m.xmin+t[2]*m.ymin+t[4],s=t[1]*m.xmin+t[3]*m.ymin+t[5],a=t[0]*m.xmax+t[2]*m.ymax+t[4],l=t[1]*m.xmax+t[3]*m.ymax+t[5];m.xmin=o[0]*(i+n)+o[2]*(s+e)+o[4],m.ymin=o[1]*(i+n)+o[3]*(s+e)+o[5],m.xmax=o[0]*(a+n)+o[2]*(l+e)+o[4],m.ymax=o[1]*(a+n)+o[3]*(l+e)+o[5]}return m}if(s)i(m,n*a,-e*a);else{const t=o(m),i=\"polyline\"===m.type?m.paths:m.rings,s=r.state.transform,a=r.state.inverseTransform;for(let o=0;o<t.length;o++){const i=t[o];for(let t=0;t<i.length;t++){const o=i[t],r=s[0]*o[0]+s[2]*o[1]+s[4],m=s[1]*o[0]+s[3]*o[1]+s[5],l=a[0]*(r+n)+a[2]*(m+e)+a[4],p=a[1]*(r+n)+a[3]*(m+e)+a[5];i[t]=x(o,l,p,void 0)}}\"paths\"in m?m.paths=i:m.rings=i}return m}function s(t,i,r,s){if(\"point\"===t.type){const{x:n,y:e}=t,o=s?s[0]:n,m=s?s[1]:e,a=t.clone(),l=(n-o)*i+o,x=(e-m)*r+m;return a.x=l,a.y=x,a}if(\"multipoint\"===t.type){const m=o(t),a=n(),[l,p,y,f]=e(a,[m]),u=s?s[0]:(l+y)/2,c=s?s[1]:(f+p)/2,h=t.clone(),g=h.points;for(let t=0;t<g.length;t++){const n=g[t],[e,o]=n,s=(e-u)*i+u,m=(o-c)*r+c;g[t]=x(n,s,m,void 0)}return h}if(\"extent\"===t.type){const{xmin:n,xmax:e,ymin:o,ymax:m}=t,a=s?s[0]:(n+e)/2,l=s?s[1]:(m+o)/2,x=t.clone();if(x.xmin=(n-a)*i+a,x.ymax=(m-l)*r+l,x.xmax=(e-a)*i+a,x.ymin=(o-l)*r+l,x.xmin>x.xmax){const t=x.xmin,n=x.xmax;x.xmin=n,x.xmax=t}if(x.ymin>x.ymax){const t=x.ymin,n=x.ymax;x.ymin=n,x.ymax=t}return x}const m=o(t),a=n(),[l,p,y,f]=e(a,m),u=s?s[0]:(l+y)/2,c=s?s[1]:(f+p)/2,h=t.clone(),g=\"polyline\"===h.type?h.paths:h.rings;for(let n=0;n<m.length;n++){const t=m[n];for(let e=0;e<t.length;e++){const o=t[e],[s,m]=o,a=(s-u)*i+u,l=(m-c)*r+c;g[n][e]=x(o,a,l,void 0)}}return\"paths\"in h?h.paths=g:h.rings=g,h}function m(t,n,e,o,i,r){const s=Math.sqrt((e-t)*(e-t)+(o-n)*(o-n));return Math.sqrt((i-t)*(i-t)+(r-n)*(r-n))/s}function a(n,e,o,i=!1){const r=Math.atan2(e.y-o.y,e.x-o.x)-Math.atan2(n.y-o.y,n.x-o.x),s=Math.atan2(Math.sin(r),Math.cos(r));return i?s:t(s)}function l(t,n,e,o){return x(t,t[0]+n,t[1]+e,null!=t[2]&&null!=o?t[2]+o:void 0)}function x(t,n,e,o){const i=[n,e];return t.length>2&&i.push(null!=o?o:t[2]),t.length>3&&i.push(t[3]),i}export{r as cloneMove,a as getRotationAngle,m as getScaleRatio,i as move,s as scale};\n"], "mappings": ";;;;;;;;;;;;;;AAI4R,SAAS,EAAE,GAAE,GAAEA,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,EAAE,SAAOA,KAAE,SAAQ,YAAU,EAAE,KAAK,QAAO,EAAE,KAAG,GAAE,EAAE,KAAGD,IAAE,EAAE,QAAM,QAAMC,OAAI,EAAE,KAAGA,KAAG;AAAE,MAAG,iBAAe,EAAE,MAAK;AAAC,UAAM,IAAE,EAAE;AAAO,aAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,GAAEA,EAAC,IAAE,EAAE,EAAEA,EAAC,GAAE,GAAEF,IAAEC,EAAC;AAAE,WAAO;AAAA,EAAC;AAAC,MAAG,aAAW,EAAE,KAAK,QAAO,EAAE,QAAM,GAAE,EAAE,QAAM,GAAE,EAAE,QAAMD,IAAE,EAAE,QAAMA,IAAE,QAAMC,OAAI,EAAE,SAAO,EAAE,OAAK,IAAG,EAAE,QAAMA,IAAE,EAAE,SAAO,EAAE,OAAK,IAAG,EAAE,QAAMA,KAAG;AAAE,QAAME,KAAE,EAAE,CAAC,GAAEC,KAAE,eAAa,EAAE,OAAK,EAAE,QAAM,EAAE;AAAM,WAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,KAAI;AAAC,UAAMD,KAAEC,GAAE,CAAC;AAAE,aAAQE,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,CAAAH,GAAEG,EAAC,IAAE,EAAEH,GAAEG,EAAC,GAAE,GAAEL,IAAEC,EAAC;AAAA,EAAC;AAAC,SAAM,WAAU,IAAE,EAAE,QAAMG,KAAE,EAAE,QAAMA,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEJ,IAAEG,IAAEC,IAAE;AAAC,QAAME,KAAE,EAAE,MAAM,GAAEC,KAAEJ,GAAE;AAAW,MAAG,YAAUG,GAAE,MAAK;AAAC,QAAGF,GAAE,GAAEE,IAAE,IAAEC,IAAE,CAACP,KAAEO,EAAC;AAAA,SAAM;AAAC,YAAML,KAAEC,GAAE,MAAM,WAAU,IAAEA,GAAE,MAAM,kBAAiBF,KAAEC,GAAE,CAAC,IAAEI,GAAE,IAAEJ,GAAE,CAAC,IAAEI,GAAE,IAAEJ,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,IAAEI,GAAE,IAAEJ,GAAE,CAAC,IAAEI,GAAE,IAAEJ,GAAE,CAAC;AAAE,MAAAI,GAAE,IAAE,EAAE,CAAC,KAAGL,KAAE,KAAG,EAAE,CAAC,KAAGG,KAAEJ,MAAG,EAAE,CAAC,GAAEM,GAAE,IAAE,EAAE,CAAC,KAAGL,KAAE,KAAG,EAAE,CAAC,KAAGG,KAAEJ,MAAG,EAAE,CAAC;AAAA,IAAC;AAAC,WAAOM;AAAA,EAAC;AAAC,MAAG,iBAAeA,GAAE,MAAK;AAAC,QAAGF,GAAE,GAAEE,IAAE,IAAEC,IAAE,CAACP,KAAEO,EAAC;AAAA,SAAM;AAAC,YAAML,KAAEI,GAAE,QAAO,IAAEH,GAAE,MAAM,WAAUF,KAAEE,GAAE,MAAM;AAAiB,eAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAMC,KAAEF,GAAEC,EAAC,GAAEG,KAAE,EAAE,CAAC,IAAEF,GAAE,CAAC,IAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,CAAC,GAAEG,KAAE,EAAE,CAAC,IAAEH,GAAE,CAAC,IAAE,EAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,CAAC,GAAEI,KAAEP,GAAE,CAAC,KAAGK,KAAE,KAAGL,GAAE,CAAC,KAAGM,KAAEP,MAAGC,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,KAAGK,KAAE,KAAGL,GAAE,CAAC,KAAGM,KAAEP,MAAGC,GAAE,CAAC;AAAE,QAAAC,GAAEC,EAAC,IAAE,EAAEC,IAAEI,IAAE,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAC,MAAG,aAAWA,GAAE,MAAK;AAAC,QAAGF,GAAE,GAAEE,IAAE,IAAEC,IAAE,CAACP,KAAEO,EAAC;AAAA,SAAM;AAAC,YAAML,KAAEC,GAAE,MAAM,WAAU,IAAEA,GAAE,MAAM,kBAAiBF,KAAEC,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,GAAEM,KAAEN,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC,IAAEI,GAAE,OAAKJ,GAAE,CAAC;AAAE,MAAAI,GAAE,OAAK,EAAE,CAAC,KAAGL,KAAE,KAAG,EAAE,CAAC,KAAGG,KAAEJ,MAAG,EAAE,CAAC,GAAEM,GAAE,OAAK,EAAE,CAAC,KAAGL,KAAE,KAAG,EAAE,CAAC,KAAGG,KAAEJ,MAAG,EAAE,CAAC,GAAEM,GAAE,OAAK,EAAE,CAAC,KAAGC,KAAE,KAAG,EAAE,CAAC,KAAGC,KAAER,MAAG,EAAE,CAAC,GAAEM,GAAE,OAAK,EAAE,CAAC,KAAGC,KAAE,KAAG,EAAE,CAAC,KAAGC,KAAER,MAAG,EAAE,CAAC;AAAA,IAAC;AAAC,WAAOM;AAAA,EAAC;AAAC,MAAGF,GAAE,GAAEE,IAAE,IAAEC,IAAE,CAACP,KAAEO,EAAC;AAAA,OAAM;AAAC,UAAML,KAAE,EAAEI,EAAC,GAAEL,KAAE,eAAaK,GAAE,OAAKA,GAAE,QAAMA,GAAE,OAAMF,KAAED,GAAE,MAAM,WAAUI,KAAEJ,GAAE,MAAM;AAAiB,aAAQ,IAAE,GAAE,IAAED,GAAE,QAAO,KAAI;AAAC,YAAMD,KAAEC,GAAE,CAAC;AAAE,eAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAMG,KAAEJ,GAAEC,EAAC,GAAEC,KAAEC,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,IAAEC,GAAE,CAAC,IAAED,GAAE,CAAC,GAAEI,KAAED,GAAE,CAAC,KAAGJ,KAAE,KAAGI,GAAE,CAAC,KAAGD,KAAEN,MAAGO,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,KAAGJ,KAAE,KAAGI,GAAE,CAAC,KAAGD,KAAEN,MAAGO,GAAE,CAAC;AAAE,QAAAN,GAAEC,EAAC,IAAE,EAAEG,IAAEG,IAAE,GAAE,MAAM;AAAA,MAAC;AAAA,IAAC;AAAC,eAAUF,KAAEA,GAAE,QAAML,KAAEK,GAAE,QAAML;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAAS,EAAE,GAAEL,IAAEE,IAAEC,IAAE;AAAC,MAAG,YAAU,EAAE,MAAK;AAAC,UAAK,EAAC,GAAE,GAAE,GAAEJ,GAAC,IAAE,GAAE,IAAEI,KAAEA,GAAE,CAAC,IAAE,GAAEE,KAAEF,KAAEA,GAAE,CAAC,IAAEJ,IAAEO,KAAE,EAAE,MAAM,GAAEC,MAAG,IAAE,KAAGP,KAAE,GAAEQ,MAAGT,KAAEM,MAAGH,KAAEG;AAAE,WAAOC,GAAE,IAAEC,IAAED,GAAE,IAAEE,IAAEF;AAAA,EAAC;AAAC,MAAG,iBAAe,EAAE,MAAK;AAAC,UAAMD,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,GAAE,CAACC,IAAEE,IAAEC,IAAEC,EAAC,IAAE,EAAEL,IAAE,CAACD,EAAC,CAAC,GAAEO,KAAET,KAAEA,GAAE,CAAC,KAAGI,KAAEG,MAAG,GAAEG,KAAEV,KAAEA,GAAE,CAAC,KAAGQ,KAAEF,MAAG,GAAEK,KAAE,EAAE,MAAM,GAAEC,KAAED,GAAE;AAAO,aAAQb,KAAE,GAAEA,KAAEc,GAAE,QAAOd,MAAI;AAAC,YAAM,IAAEc,GAAEd,EAAC,GAAE,CAACF,IAAE,CAAC,IAAE,GAAEI,MAAGJ,KAAEa,MAAGZ,KAAEY,IAAEP,MAAG,IAAEQ,MAAGX,KAAEW;AAAE,MAAAE,GAAEd,EAAC,IAAE,EAAE,GAAEE,IAAEE,IAAE,MAAM;AAAA,IAAC;AAAC,WAAOS;AAAA,EAAC;AAAC,MAAG,aAAW,EAAE,MAAK;AAAC,UAAK,EAAC,MAAK,GAAE,MAAKf,IAAE,MAAK,GAAE,MAAKM,GAAC,IAAE,GAAEC,KAAEH,KAAEA,GAAE,CAAC,KAAG,IAAEJ,MAAG,GAAEQ,KAAEJ,KAAEA,GAAE,CAAC,KAAGE,KAAE,KAAG,GAAEG,KAAE,EAAE,MAAM;AAAE,QAAGA,GAAE,QAAM,IAAEF,MAAGN,KAAEM,IAAEE,GAAE,QAAMH,KAAEE,MAAGL,KAAEK,IAAEC,GAAE,QAAMT,KAAEO,MAAGN,KAAEM,IAAEE,GAAE,QAAM,IAAED,MAAGL,KAAEK,IAAEC,GAAE,OAAKA,GAAE,MAAK;AAAC,YAAMP,KAAEO,GAAE,MAAKQ,KAAER,GAAE;AAAK,MAAAA,GAAE,OAAKQ,IAAER,GAAE,OAAKP;AAAA,IAAC;AAAC,QAAGO,GAAE,OAAKA,GAAE,MAAK;AAAC,YAAMP,KAAEO,GAAE,MAAKQ,KAAER,GAAE;AAAK,MAAAA,GAAE,OAAKQ,IAAER,GAAE,OAAKP;AAAA,IAAC;AAAC,WAAOO;AAAA,EAAC;AAAC,QAAMH,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,GAAE,CAACC,IAAE,GAAE,GAAE,CAAC,IAAE,EAAED,IAAED,EAAC,GAAEO,KAAET,KAAEA,GAAE,CAAC,KAAGI,KAAE,KAAG,GAAE,IAAEJ,KAAEA,GAAE,CAAC,KAAG,IAAE,KAAG,GAAEW,KAAE,EAAE,MAAM,GAAE,IAAE,eAAaA,GAAE,OAAKA,GAAE,QAAMA,GAAE;AAAM,WAAQ,IAAE,GAAE,IAAET,GAAE,QAAO,KAAI;AAAC,UAAMJ,KAAEI,GAAE,CAAC;AAAE,aAAQN,KAAE,GAAEA,KAAEE,GAAE,QAAOF,MAAI;AAAC,YAAM,IAAEE,GAAEF,EAAC,GAAE,CAACI,IAAEE,EAAC,IAAE,GAAEC,MAAGH,KAAES,MAAGZ,KAAEY,IAAEL,MAAGF,KAAE,KAAGH,KAAE;AAAE,QAAE,CAAC,EAAEH,EAAC,IAAE,EAAE,GAAEO,IAAEC,IAAE,MAAM;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,WAAUO,KAAEA,GAAE,QAAM,IAAEA,GAAE,QAAM,GAAEA;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEf,IAAE,GAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAE,KAAK,MAAMJ,KAAE,MAAIA,KAAE,MAAI,IAAE,MAAI,IAAE,EAAE;AAAE,SAAO,KAAK,MAAMC,KAAE,MAAIA,KAAE,MAAIE,KAAE,MAAIA,KAAE,EAAE,IAAEC;AAAC;AAAC,SAAS,EAAE,GAAEJ,IAAE,GAAEC,KAAE,OAAG;AAAC,QAAME,KAAE,KAAK,MAAMH,GAAE,IAAE,EAAE,GAAEA,GAAE,IAAE,EAAE,CAAC,IAAE,KAAK,MAAM,EAAE,IAAE,EAAE,GAAE,EAAE,IAAE,EAAE,CAAC,GAAEI,KAAE,KAAK,MAAM,KAAK,IAAID,EAAC,GAAE,KAAK,IAAIA,EAAC,CAAC;AAAE,SAAOF,KAAEG,KAAE,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEJ,IAAE,GAAE;AAAC,SAAO,EAAE,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,CAAC,IAAEA,IAAE,QAAM,EAAE,CAAC,KAAG,QAAM,IAAE,EAAE,CAAC,IAAE,IAAE,MAAM;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,GAAE;AAAC,QAAMC,KAAE,CAAC,GAAED,EAAC;AAAE,SAAO,EAAE,SAAO,KAAGC,GAAE,KAAK,QAAM,IAAE,IAAE,EAAE,CAAC,CAAC,GAAE,EAAE,SAAO,KAAGA,GAAE,KAAK,EAAE,CAAC,CAAC,GAAEA;AAAC;", "names": ["e", "i", "t", "r", "s", "o", "m", "a", "l", "x", "p", "y", "f", "u", "c", "h", "g", "n"]}