import {
  a,
  d
} from "./chunk-WJKHSSMC.js";

// node_modules/@arcgis/core/statistics/utils.js
var t = "<Null>";
var l = "equal-interval";
var i = 1;
var a2 = 5;
var r = 10;
var o = /\s*(\+|-)?((\d+(\.\d+)?)|(\.\d+))\s*/gi;
var u = /* @__PURE__ */ new Set(["esriFieldTypeInteger", "esriFieldTypeSmallInteger", "esriFieldTypeSingle", "esriFieldTypeDouble"]);
var s = ["min", "max", "avg", "stddev", "count", "sum", "variance", "nullcount", "median"];
function c(n) {
  return null == n || "string" == typeof n && !n ? t : n;
}
function m(n) {
  const e = null != n.normalizationField || null != n.normalizationType, t2 = null != n.minValue || null != n.maxValue, l2 = !!n.sqlExpression && n.supportsSQLExpression;
  return !e && !t2 && !l2;
}
function f(n) {
  const e = n.returnDistinct ? [...new Set(n.values)] : n.values, t2 = e.filter((n2) => null != n2).length, l2 = { count: t2 };
  return n.supportsNullCount && (l2.nullcount = e.length - t2), n.percentileParams && (l2.median = p(e, n.percentileParams)), l2;
}
function d2(n) {
  const { values: e, useSampleStdDev: t2, supportsNullCount: l2 } = n;
  let i2 = Number.POSITIVE_INFINITY, a3 = Number.NEGATIVE_INFINITY, r2 = null, o2 = null, u2 = null, s2 = null, c2 = 0;
  const m2 = null == n.minValue ? -1 / 0 : n.minValue, f2 = null == n.maxValue ? 1 / 0 : n.maxValue;
  for (const p2 of e) Number.isFinite(p2) ? p2 >= m2 && p2 <= f2 && (r2 = null === r2 ? p2 : r2 + p2, i2 = Math.min(i2, p2), a3 = Math.max(a3, p2), c2++) : "string" == typeof p2 && c2++;
  if (c2 && null != r2) {
    o2 = r2 / c2;
    let n2 = 0;
    for (const t3 of e) Number.isFinite(t3) && t3 >= m2 && t3 <= f2 && (n2 += (t3 - o2) ** 2);
    s2 = t2 ? c2 > 1 ? n2 / (c2 - 1) : 0 : c2 > 0 ? n2 / c2 : 0, u2 = Math.sqrt(s2);
  } else i2 = null, a3 = null;
  const d3 = { avg: o2, count: c2, max: a3, min: i2, stddev: u2, sum: r2, variance: s2 };
  return l2 && (d3.nullcount = e.length - c2), n.percentileParams && (d3.median = p(e, n.percentileParams)), d3;
}
function p(n, e) {
  const { fieldType: t2, value: l2, orderBy: i2, isDiscrete: a3 } = e, r2 = v(t2, "desc" === i2);
  if (0 === (n = [...n].filter((n2) => null != n2).sort((n2, e2) => r2(n2, e2))).length) return null;
  if (l2 <= 0) return n[0];
  if (l2 >= 1) return n[n.length - 1];
  const o2 = (n.length - 1) * l2, u2 = Math.floor(o2), s2 = u2 + 1, c2 = o2 % 1, m2 = n[u2], f2 = n[s2];
  return s2 >= n.length || a3 || "string" == typeof m2 || "string" == typeof f2 ? m2 : m2 * (1 - c2) + f2 * c2;
}
function v(n, e) {
  const t2 = e ? 1 : -1, l2 = g(e), i2 = h(e);
  if (!(!!n && ["esriFieldTypeDate", "esriFieldTypeString", "esriFieldTypeGUID", "esriFieldTypeGlobalID", ...u].includes(n))) return (n2, e2) => "number" == typeof n2 && "number" == typeof e2 ? l2(n2, e2) : "string" == typeof n2 && "string" == typeof e2 ? i2(n2, e2) : t2;
  if ("esriFieldTypeDate" === n) return (n2, e2) => {
    const i3 = new Date(n2).getTime(), a3 = new Date(e2).getTime();
    return isNaN(i3) || isNaN(a3) ? t2 : l2(i3, a3);
  };
  if (u.has(n)) return (n2, e2) => l2(n2, e2);
  if ("esriFieldTypeString" === n) return (n2, e2) => i2(n2, e2);
  if ("esriFieldTypeGUID" === n || "esriFieldTypeGlobalID" === n) {
    const n2 = h(e);
    return (e2, t3) => n2(V(e2), V(t3));
  }
  return e ? (n2, e2) => 1 : (n2, e2) => -1;
}
function b(n, e, t2) {
  if (t2) {
    if (null == n) return null == e ? 0 : 1;
    if (null == e) return -1;
  } else {
    if (null == n) return null == e ? 0 : -1;
    if (null == e) return 1;
  }
  return null;
}
function h(n) {
  return n ? (n2, e) => {
    const t2 = b(n2, e, true);
    return null != t2 ? t2 : (n2 = n2.toUpperCase()) > (e = e.toUpperCase()) ? -1 : n2 < e ? 1 : 0;
  } : (n2, e) => {
    const t2 = b(n2, e, false);
    return null != t2 ? t2 : (n2 = n2.toUpperCase()) < (e = e.toUpperCase()) ? -1 : n2 > e ? 1 : 0;
  };
}
function g(n) {
  return n ? (n2, e) => {
    const t2 = b(n2, e, true);
    return null != t2 ? t2 : e - n2;
  } : (n2, e) => {
    const t2 = b(n2, e, false);
    return null != t2 ? t2 : n2 - e;
  };
}
function V(n) {
  return n.substr(24, 12) + n.substr(19, 4) + n.substr(16, 2) + n.substr(14, 2) + n.substr(11, 2) + n.substr(9, 2) + n.substr(6, 2) + n.substr(4, 2) + n.substr(2, 2) + n.substr(0, 2);
}
function T(n, e) {
  let t2;
  for (t2 in n) s.includes(t2) && (Number.isFinite(n[t2]) || (n[t2] = null));
  return e ? (["avg", "stddev", "variance"].forEach((e2) => {
    null != n[e2] && (n[e2] = Math.ceil(n[e2]));
  }), n) : n;
}
function y(n) {
  const e = {};
  for (let t2 of n) (null == t2 || "string" == typeof t2 && "" === t2.trim()) && (t2 = null), null == e[t2] ? e[t2] = { count: 1, data: t2 } : e[t2].count++;
  return { count: e };
}
function F(n) {
  return "coded-value" !== (n == null ? void 0 : n.type) ? [] : n.codedValues.map((n2) => n2.code);
}
function x(n, e, t2, l2) {
  const i2 = n.count, a3 = [];
  if (t2 && e) {
    const n2 = [], t3 = F(e[0]);
    for (const i3 of t3) if (e[1]) {
      const t4 = F(e[1]);
      for (const a4 of t4) if (e[2]) {
        const t5 = F(e[2]);
        for (const e2 of t5) n2.push(`${c(i3)}${l2}${c(a4)}${l2}${c(e2)}`);
      } else n2.push(`${c(i3)}${l2}${c(a4)}`);
    } else n2.push(i3);
    for (const e2 of n2) i2.hasOwnProperty(e2) || (i2[e2] = { data: e2, count: 0 });
  }
  for (const r2 in i2) {
    const n2 = i2[r2];
    a3.push({ value: n2.data, count: n2.count, label: n2.label });
  }
  return { uniqueValueInfos: a3 };
}
function D(n, e, t2, l2) {
  let i2 = null;
  switch (e) {
    case "log":
      0 !== n && (i2 = Math.log(n) * Math.LOG10E);
      break;
    case "percent-of-total":
      Number.isFinite(l2) && 0 !== l2 && (i2 = n / l2 * 100);
      break;
    case "field":
      Number.isFinite(t2) && 0 !== t2 && (i2 = n / t2);
      break;
    case "natural-log":
      n > 0 && (i2 = Math.log(n));
      break;
    case "square-root":
      n > 0 && (i2 = n ** 0.5);
  }
  return i2;
}
function z(n, t2) {
  const l2 = N({ field: t2.field, normalizationType: t2.normalizationType, normalizationField: t2.normalizationField, classificationMethod: t2.classificationMethod, standardDeviationInterval: t2.standardDeviationInterval, breakCount: t2.numClasses || a2 });
  return n = I(n, t2.minValue, t2.maxValue), a({ definition: l2, values: n, normalizationTotal: t2.normalizationTotal });
}
function I(n, e, t2) {
  const l2 = e ?? -1 / 0, i2 = t2 ?? 1 / 0;
  return n.filter((n2) => Number.isFinite(n2) && n2 >= l2 && n2 <= i2);
}
function N(e) {
  const { breakCount: t2, field: a3, normalizationField: r2, normalizationType: o2 } = e, u2 = e.classificationMethod || l, s2 = "standard-deviation" === u2 ? e.standardDeviationInterval || i : void 0;
  return new d({ breakCount: t2, classificationField: a3, classificationMethod: u2, normalizationField: "field" === o2 ? r2 : void 0, normalizationType: o2, standardDeviationInterval: s2 });
}
function S(n, e) {
  let t2 = n.classBreaks;
  const l2 = t2.length, i2 = t2[0].minValue, a3 = t2[l2 - 1].maxValue, r2 = "standard-deviation" === e, u2 = o;
  return t2 = t2.map((n2) => {
    var _a;
    const e2 = n2.label, t3 = { minValue: n2.minValue, maxValue: n2.maxValue, label: e2 };
    if (r2 && e2) {
      const n3 = ((_a = e2.match(u2)) == null ? void 0 : _a.map((n4) => +n4.trim())) ?? [];
      2 === n3.length ? (t3.minStdDev = n3[0], t3.maxStdDev = n3[1], n3[0] < 0 && n3[1] > 0 && (t3.hasAvg = true)) : 1 === n3.length && (e2.includes("<") ? (t3.minStdDev = null, t3.maxStdDev = n3[0]) : e2.includes(">") && (t3.minStdDev = n3[0], t3.maxStdDev = null));
    }
    return t3;
  }), { minValue: i2, maxValue: a3, classBreakInfos: t2, normalizationTotal: n.normalizationTotal };
}
function M(n, e) {
  const t2 = C(n, e), l2 = t2.intervals, i2 = t2.min ?? 0, a3 = t2.max ?? 0, r2 = l2.map((n2, e2) => ({ minValue: l2[e2][0], maxValue: l2[e2][1], count: 0 }));
  for (const o2 of n) if (null != o2 && o2 >= i2 && o2 <= a3) {
    const n2 = k(l2, o2);
    n2 > -1 && r2[n2].count++;
  }
  return { bins: r2, minValue: i2, maxValue: a3, normalizationTotal: e.normalizationTotal };
}
function C(n, e) {
  const { field: t2, classificationMethod: l2, standardDeviationInterval: i2, normalizationType: a3, normalizationField: o2, normalizationTotal: u2, minValue: s2, maxValue: c2 } = e, f2 = e.numBins || r;
  let p2 = null, v2 = null, b2 = null;
  if ((!l2 || "equal-interval" === l2) && !a3) {
    if (null != s2 && null != c2) p2 = s2, v2 = c2;
    else {
      const e2 = d2({ values: n, minValue: s2, maxValue: c2, useSampleStdDev: !a3, supportsNullCount: m({ normalizationType: a3, normalizationField: o2, minValue: s2, maxValue: c2 }) });
      p2 = e2.min ?? null, v2 = e2.max ?? null;
    }
    b2 = $(p2 ?? 0, v2 ?? 0, f2);
  } else {
    const { classBreaks: e2 } = z(n, { field: t2, normalizationType: a3, normalizationField: o2, normalizationTotal: u2, classificationMethod: l2, standardDeviationInterval: i2, minValue: s2, maxValue: c2, numClasses: f2 });
    p2 = e2[0].minValue, v2 = e2[e2.length - 1].maxValue, b2 = e2.map((n2) => [n2.minValue, n2.maxValue]);
  }
  return { min: p2, max: v2, intervals: b2 };
}
function k(n, e) {
  let t2 = -1;
  for (let l2 = n.length - 1; l2 >= 0; l2--) {
    if (e >= n[l2][0]) {
      t2 = l2;
      break;
    }
  }
  return t2;
}
function $(n, e, t2) {
  const l2 = (e - n) / t2, i2 = [];
  let a3, r2 = n;
  for (let o2 = 1; o2 <= t2; o2++) a3 = r2 + l2, a3 = Number(a3.toFixed(16)), i2.push([r2, o2 === t2 ? e : a3]), r2 = a3;
  return i2;
}

export {
  c,
  m,
  f,
  d2 as d,
  p,
  v,
  T,
  y,
  x,
  D,
  z,
  S,
  M
};
//# sourceMappingURL=chunk-M4ZUXRA3.js.map
