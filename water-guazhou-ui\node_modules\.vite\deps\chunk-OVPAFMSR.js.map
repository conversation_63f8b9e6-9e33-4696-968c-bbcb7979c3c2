{"version": 3, "sources": ["../../@arcgis/core/layers/support/lazyLayerLoader.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst a={BingMapsLayer:async()=>(await import(\"../BingMapsLayer.js\")).default,BuildingSceneLayer:async()=>(await import(\"../BuildingSceneLayer.js\")).default,CSVLayer:async()=>(await import(\"../CSVLayer.js\")).default,DimensionLayer:async()=>(await import(\"../DimensionLayer.js\")).default,ElevationLayer:async()=>(await import(\"../ElevationLayer.js\")).default,FeatureLayer:async()=>(await import(\"../FeatureLayer.js\")).default,GeoJSONLayer:async()=>(await import(\"../GeoJSONLayer.js\")).default,GeoRSSLayer:async()=>(await import(\"../GeoRSSLayer.js\")).default,GroupLayer:async()=>(await import(\"../GroupLayer.js\")).default,ImageryLayer:async()=>(await import(\"../ImageryLayer.js\")).default,ImageryTileLayer:async()=>(await import(\"../ImageryTileLayer.js\")).default,IntegratedMeshLayer:async()=>(await import(\"../IntegratedMeshLayer.js\")).default,KMLLayer:async()=>(await import(\"../KMLLayer.js\")).default,LineOfSightLayer:async()=>(await import(\"../LineOfSightLayer.js\")).default,MapImageLayer:async()=>(await import(\"../MapImageLayer.js\")).default,MapNotesLayer:async()=>(await import(\"../MapNotesLayer.js\")).default,MediaLayer:async()=>(await import(\"../MediaLayer.js\")).default,OGCFeatureLayer:async()=>(await import(\"../OGCFeatureLayer.js\")).default,OpenStreetMapLayer:async()=>(await import(\"../OpenStreetMapLayer.js\")).default,OrientedImageryLayer:async()=>(await import(\"../OrientedImageryLayer.js\")).default,PointCloudLayer:async()=>(await import(\"../PointCloudLayer.js\")).default,RouteLayer:async()=>(await import(\"../RouteLayer.js\")).default,SceneLayer:async()=>(await import(\"../SceneLayer.js\")).default,StreamLayer:async()=>(await import(\"../StreamLayer.js\")).default,SubtypeGroupLayer:async()=>(await import(\"../SubtypeGroupLayer.js\")).default,TileLayer:async()=>(await import(\"../TileLayer.js\")).default,UnknownLayer:async()=>(await import(\"../UnknownLayer.js\")).default,UnsupportedLayer:async()=>(await import(\"../UnsupportedLayer.js\")).default,VectorTileLayer:async()=>(await import(\"../VectorTileLayer.js\")).default,VoxelLayer:async()=>(await import(\"../VoxelLayer.js\")).default,WFSLayer:async()=>(await import(\"../WFSLayer.js\")).default,WMSLayer:async()=>(await import(\"../WMSLayer.js\")).default,WMTSLayer:async()=>(await import(\"../WMTSLayer.js\")).default,WebTileLayer:async()=>(await import(\"../WebTileLayer.js\")).default};export{a as layerLookupMap};\n"], "mappings": ";AAIA,IAAM,IAAE,EAAC,eAAc,aAAU,MAAM,OAAO,6BAAqB,GAAG,SAAQ,oBAAmB,aAAU,MAAM,OAAO,kCAA0B,GAAG,SAAQ,UAAS,aAAU,MAAM,OAAO,wBAAgB,GAAG,SAAQ,gBAAe,aAAU,MAAM,OAAO,8BAAsB,GAAG,SAAQ,gBAAe,aAAU,MAAM,OAAO,8BAAsB,GAAG,SAAQ,cAAa,aAAU,MAAM,OAAO,2CAAoB,GAAG,SAAQ,cAAa,aAAU,MAAM,OAAO,4BAAoB,GAAG,SAAQ,aAAY,aAAU,MAAM,OAAO,2BAAmB,GAAG,SAAQ,YAAW,aAAU,MAAM,OAAO,qCAAkB,GAAG,SAAQ,cAAa,aAAU,MAAM,OAAO,4BAAoB,GAAG,SAAQ,kBAAiB,aAAU,MAAM,OAAO,gCAAwB,GAAG,SAAQ,qBAAoB,aAAU,MAAM,OAAO,mCAA2B,GAAG,SAAQ,UAAS,aAAU,MAAM,OAAO,wBAAgB,GAAG,SAAQ,kBAAiB,aAAU,MAAM,OAAO,gCAAwB,GAAG,SAAQ,eAAc,aAAU,MAAM,OAAO,wCAAqB,GAAG,SAAQ,eAAc,aAAU,MAAM,OAAO,6BAAqB,GAAG,SAAQ,YAAW,aAAU,MAAM,OAAO,0BAAkB,GAAG,SAAQ,iBAAgB,aAAU,MAAM,OAAO,+BAAuB,GAAG,SAAQ,oBAAmB,aAAU,MAAM,OAAO,kCAA0B,GAAG,SAAQ,sBAAqB,aAAU,MAAM,OAAO,oCAA4B,GAAG,SAAQ,iBAAgB,aAAU,MAAM,OAAO,+BAAuB,GAAG,SAAQ,YAAW,aAAU,MAAM,OAAO,0BAAkB,GAAG,SAAQ,YAAW,aAAU,MAAM,OAAO,0BAAkB,GAAG,SAAQ,aAAY,aAAU,MAAM,OAAO,2BAAmB,GAAG,SAAQ,mBAAkB,aAAU,MAAM,OAAO,iCAAyB,GAAG,SAAQ,WAAU,aAAU,MAAM,OAAO,wCAAiB,GAAG,SAAQ,cAAa,aAAU,MAAM,OAAO,4BAAoB,GAAG,SAAQ,kBAAiB,aAAU,MAAM,OAAO,gCAAwB,GAAG,SAAQ,iBAAgB,aAAU,MAAM,OAAO,+BAAuB,GAAG,SAAQ,YAAW,aAAU,MAAM,OAAO,0BAAkB,GAAG,SAAQ,UAAS,aAAU,MAAM,OAAO,mCAAgB,GAAG,SAAQ,UAAS,aAAU,MAAM,OAAO,uCAAgB,GAAG,SAAQ,WAAU,aAAU,MAAM,OAAO,yBAAiB,GAAG,SAAQ,cAAa,aAAU,MAAM,OAAO,4BAAoB,GAAG,QAAO;", "names": []}