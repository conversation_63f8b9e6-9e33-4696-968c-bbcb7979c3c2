import {
  $ as $2,
  $2 as $3,
  A as A9,
  C as C2,
  E as E11,
  Ot,
  a as a17,
  at,
  b as b7,
  c as c8,
  c2 as c9,
  c3 as c10,
  ce,
  ct,
  d as d7,
  e as e13,
  e3 as e14,
  f4 as f13,
  ft,
  h as h12,
  h2 as h13,
  i3 as i11,
  l as l15,
  lt,
  nt,
  ot,
  p3 as p13,
  pt,
  q2 as q5,
  r as r15,
  ut,
  x as x10,
  z as z3
} from "./chunk-WHHWXCFL.js";
import {
  i as i12,
  p as p15
} from "./chunk-KTKCLSEG.js";
import {
  a as a19,
  c as c11,
  l as l16,
  n as n12,
  p as p14,
  r as r16
} from "./chunk-JHFCUMR2.js";
import "./chunk-OHIQTOTV.js";
import "./chunk-LGTBT7TM.js";
import {
  h as h14
} from "./chunk-NXCE4GJJ.js";
import {
  w as w6
} from "./chunk-OASQDZRK.js";
import "./chunk-ILZ6UF5N.js";
import {
  g as g9
} from "./chunk-RXASCFTD.js";
import "./chunk-7GO2KORS.js";
import {
  A as A8,
  B as B2,
  G as G3,
  H as H2,
  M as M6,
  S as S5,
  Y as Y2
} from "./chunk-4LJHCRF5.js";
import {
  i as i10
} from "./chunk-UWLG66KQ.js";
import {
  D as D3,
  a as a15,
  a2 as a16,
  c as c7,
  d as d6,
  i as i9,
  j as j5,
  l as l14,
  m as m6,
  m2 as m7,
  t as t13,
  v as v7,
  x as x8,
  x2 as x9,
  y as y6
} from "./chunk-VCDD3IVD.js";
import {
  j as j6
} from "./chunk-WJFUUMLN.js";
import {
  w as w5
} from "./chunk-F36GRXKV.js";
import {
  C as C3,
  H as H3
} from "./chunk-Y26TXFHQ.js";
import "./chunk-TFXOTB3E.js";
import "./chunk-J2CRYUH3.js";
import "./chunk-A4XIY547.js";
import {
  a as a18,
  o as o12
} from "./chunk-M3F5NDOQ.js";
import "./chunk-5HSEQ7GU.js";
import "./chunk-V57FPENY.js";
import "./chunk-XDK46HA6.js";
import {
  W as W2,
  Z as Z4
} from "./chunk-U7VYWSSV.js";
import "./chunk-4W7HU754.js";
import {
  r as r13,
  t as t12
} from "./chunk-JXO7W6XW.js";
import "./chunk-VYWZHTOQ.js";
import {
  a as a13,
  a2 as a14,
  g as g7,
  p as p8,
  y as y5
} from "./chunk-PTYT4A5P.js";
import {
  g as g5
} from "./chunk-VD4TYNIV.js";
import "./chunk-XX6IKIRW.js";
import {
  E as E6,
  S as S4,
  e as e10,
  f as f9,
  g as g4,
  o2 as o10,
  r2 as r11,
  v as v6
} from "./chunk-NEOCII5B.js";
import "./chunk-7GPM2ZU5.js";
import {
  s as s10
} from "./chunk-FQZM46ZM.js";
import {
  G as G2
} from "./chunk-DXGQBULN.js";
import "./chunk-KETK5W7H.js";
import "./chunk-PI5OC2DP.js";
import {
  A as A6,
  E as E5,
  S as S3,
  _ as _4,
  a as a12,
  c as c4,
  l as l12
} from "./chunk-LGZKVOWE.js";
import "./chunk-SKYLCTPX.js";
import {
  B,
  M as M3,
  b as b5,
  h as h7,
  l as l10,
  m as m3,
  v as v5
} from "./chunk-FQMXSCOG.js";
import "./chunk-MNPAHSMX.js";
import {
  e as e9,
  o as o9,
  t as t8
} from "./chunk-QLHJUIIZ.js";
import "./chunk-TDGDXUFC.js";
import "./chunk-C2ZE76VJ.js";
import "./chunk-F26DNX7C.js";
import {
  r as r12,
  t as t9
} from "./chunk-KVEZ26WH.js";
import {
  A as A5,
  E as E3,
  P as P2,
  c as c3,
  e as e8,
  f as f8,
  h as h10,
  r2 as r10,
  u as u3,
  x as x6
} from "./chunk-TOYJMVHA.js";
import "./chunk-Q6BEUTMN.js";
import "./chunk-BOT4BSSB.js";
import {
  h as h9,
  t as t7
} from "./chunk-XVTFHFM3.js";
import "./chunk-QB6AUIQ2.js";
import "./chunk-REGYRSW7.js";
import "./chunk-PIQKEGGB.js";
import "./chunk-Y424ZXTG.js";
import "./chunk-UB5FTTH5.js";
import "./chunk-6GW7M2AQ.js";
import "./chunk-ND2RJTSZ.js";
import {
  h as h8
} from "./chunk-L4Y6W6Y5.js";
import "./chunk-IRHOIB3A.js";
import "./chunk-N3S5O3YO.js";
import "./chunk-JETZLJ6M.js";
import "./chunk-32BGXH4N.js";
import "./chunk-VK7XO5DN.js";
import {
  W,
  _ as _3,
  a as a11,
  h as h11,
  l as l11,
  s as s9
} from "./chunk-WKBMFG6J.js";
import {
  o as o8
} from "./chunk-BPRRRPC3.js";
import {
  E2 as E4
} from "./chunk-6G2NLXT7.js";
import "./chunk-3DGZE3NI.js";
import "./chunk-SARDHCB4.js";
import "./chunk-YKLDBJ7V.js";
import "./chunk-HTESBPT2.js";
import "./chunk-GXMOAZWH.js";
import "./chunk-WL2F66AK.js";
import "./chunk-6OHWWYET.js";
import "./chunk-TUB4N6LD.js";
import "./chunk-NLDHTNKF.js";
import "./chunk-YV4RKNU4.js";
import "./chunk-LHO3WKNH.js";
import {
  n as n10
} from "./chunk-RFTQI4ZD.js";
import {
  s as s6
} from "./chunk-IUNR7SKI.js";
import "./chunk-UHA44FM7.js";
import "./chunk-OWSDEANX.js";
import {
  n as n8
} from "./chunk-6ZZUUGXX.js";
import {
  n as n7
} from "./chunk-OMPEYGMV.js";
import {
  o as o5
} from "./chunk-BWWOCIFU.js";
import {
  T
} from "./chunk-QW426QEA.js";
import "./chunk-IEBU4QQL.js";
import {
  O as O3
} from "./chunk-CPQSD22U.js";
import "./chunk-MNYWPBDW.js";
import "./chunk-ZVJXF3ML.js";
import "./chunk-SKIEIN3S.js";
import {
  g as g3,
  l as l7
} from "./chunk-6ESVG4YL.js";
import "./chunk-IKOX2HGY.js";
import {
  i as i5
} from "./chunk-3KCCETWY.js";
import "./chunk-IMGALXF5.js";
import "./chunk-UYVDPJKH.js";
import {
  s as s8
} from "./chunk-EM4JSU7Z.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import {
  E as E2,
  F as F3,
  I,
  R as R3
} from "./chunk-4M3AMTD4.js";
import "./chunk-XXXEFHEN.js";
import "./chunk-I2B245QS.js";
import "./chunk-MN2LGVDI.js";
import {
  t as t10
} from "./chunk-TSAYJ7XS.js";
import "./chunk-OY3C7FMJ.js";
import "./chunk-DUEDINK5.js";
import "./chunk-QCTKOQ44.js";
import {
  a as a9
} from "./chunk-ST2RRB55.js";
import {
  w as w4
} from "./chunk-6KZTVN32.js";
import "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import {
  p as p7,
  q as q3,
  y as y4
} from "./chunk-O2BYTJI4.js";
import {
  r as r9,
  s as s7
} from "./chunk-5ZZCQR67.js";
import "./chunk-NQQSL2QK.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-K4QGLA2K.js";
import "./chunk-5XZZKPPL.js";
import "./chunk-PCLDCFRI.js";
import "./chunk-WFNEQMBV.js";
import {
  C,
  D as D2,
  E as E10,
  M as M5,
  R as R4,
  U as U3,
  X,
  Z as Z3,
  b as b6,
  c2 as c6,
  g as g8,
  k as k2,
  m as m4,
  o as o11,
  p as p12,
  q as q4,
  x as x7
} from "./chunk-S3MOIHQ7.js";
import {
  M as M4,
  f as f11,
  k
} from "./chunk-TL5Y53I4.js";
import {
  A as A7,
  E as E9,
  P as P3,
  V,
  e as e11,
  e2 as e12,
  r as r14
} from "./chunk-PTI7U6FU.js";
import "./chunk-ZOIBK6WV.js";
import "./chunk-FAMLZKHJ.js";
import {
  J,
  Y,
  _ as _2,
  p2 as p6,
  x as x5
} from "./chunk-6IU6DQRF.js";
import {
  b as b4,
  c as c2,
  d as d4,
  e as e7,
  f as f7,
  h as h6,
  i as i6,
  o as o7,
  p as p5,
  t as t6
} from "./chunk-YELYN22P.js";
import {
  Q
} from "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import {
  e as e6,
  o as o6,
  t as t5
} from "./chunk-QYOAH6AO.js";
import {
  e as e5
} from "./chunk-A7PY25IH.js";
import {
  t as t11,
  u as u5
} from "./chunk-EM6CPBT6.js";
import {
  l as l9
} from "./chunk-JJ3NE6DY.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import {
  P as P4,
  f as f12,
  i as i8,
  l as l13,
  m as m5
} from "./chunk-X6S7G5WH.js";
import "./chunk-B4YFVQZH.js";
import "./chunk-4RJYWSAT.js";
import "./chunk-2RO3UJ2R.js";
import {
  c as c5
} from "./chunk-T6GIT4YI.js";
import {
  p as p9
} from "./chunk-PHEIXDVR.js";
import {
  p as p10
} from "./chunk-WX7B7OKM.js";
import {
  p as p11,
  s as s12,
  u as u6
} from "./chunk-UQWZJZ2S.js";
import {
  E as E8,
  n as n11
} from "./chunk-5S4W3ME5.js";
import {
  E as E7,
  Z as Z2,
  d as d5,
  f as f10,
  g as g6,
  i as i7,
  s as s11,
  u as u4
} from "./chunk-CDZ24ELJ.js";
import {
  M as M2
} from "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import {
  A as A4,
  a as a8,
  b as b3,
  l as l6,
  o as o4,
  r as r8,
  s as s4,
  v as v4
} from "./chunk-SROTSYJS.js";
import {
  n as n9
} from "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import "./chunk-4YSFMXMT.js";
import "./chunk-56K7OMWB.js";
import "./chunk-IU22XAFH.js";
import {
  u as u2
} from "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import {
  j as j4
} from "./chunk-P37TUI4J.js";
import {
  An,
  Xn,
  gn,
  jn,
  pe,
  pn,
  xn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  a as a10,
  d as d3
} from "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import {
  A as A3,
  R as R2,
  b as b2,
  c,
  f as f6,
  g as g2,
  h as h5,
  i as i4,
  l as l5,
  n as n5,
  p as p4,
  q as q2,
  r as r7,
  s as s3,
  x as x3
} from "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-4GVJIP3E.js";
import {
  e as e4,
  r as r6
} from "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import {
  x as x4
} from "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  U as U2,
  a as a7,
  f as f5,
  h as h4,
  l as l3,
  w as w3
} from "./chunk-QUHG7NMD.js";
import {
  n as n6
} from "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import {
  E,
  M,
  R,
  S as S2,
  a as a6,
  j as j3
} from "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import {
  F as F2,
  w as w2
} from "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import {
  s as s5
} from "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n as n3
} from "./chunk-SGIJIEHB.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  h as h3,
  z
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  $,
  O,
  Z,
  f2 as f3
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import {
  s
} from "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import {
  d as d2,
  i as i3,
  l as l4,
  p as p3,
  x as x2,
  y as y3
} from "./chunk-G5KX4JSG.js";
import {
  l as l8
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  a as a5,
  b,
  i as i2,
  m as m2
} from "./chunk-EIGTETCG.js";
import {
  A as A2,
  F,
  G,
  H,
  O as O2,
  P,
  S,
  U,
  _,
  e as e3,
  g,
  j as j2,
  o as o3,
  p as p2,
  q,
  r as r4,
  s as s2,
  u,
  v as v3,
  x,
  z as z2
} from "./chunk-MQAXMQFG.js";
import {
  f as f4,
  n as n4,
  r as r3,
  t as t4
} from "./chunk-36FLFRUE.js";
import {
  D,
  a as a4,
  r as r5
} from "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import {
  l as l2
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2 as t3,
  v as v2
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a3,
  y as y2
} from "./chunk-JN4FSB7Y.js";
import {
  a as a2
} from "./chunk-HP475EI3.js";
import {
  A
} from "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  h as h2,
  n as n2,
  o as o2,
  r as r2,
  t as t2
} from "./chunk-2CM7MIII.js";
import {
  f as f2
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  v
} from "./chunk-GZGAQUSK.js";
import {
  a,
  d,
  e as e2,
  f,
  h,
  l,
  n,
  o,
  p,
  r,
  t,
  y
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/libs/maquette/h.js
var e15 = (e20) => ({ vnodeSelector: "", properties: void 0, children: void 0, text: e20.toString(), domNode: null });
var r17 = (t24, o17, n21) => {
  for (let l22 = 0, i19 = o17.length; l22 < i19; l22++) {
    let i20 = o17[l22];
    Array.isArray(i20) ? r17(t24, i20, n21) : null != i20 && false !== i20 && ("string" == typeof i20 && (i20 = e15(i20)), n21.push(i20));
  }
};
function t14(e20, t24, o17) {
  if (Array.isArray(t24)) o17 = t24, t24 = void 0;
  else if (t24 && ("string" == typeof t24 || t24.hasOwnProperty("vnodeSelector")) || o17 && ("string" == typeof o17 || o17.hasOwnProperty("vnodeSelector"))) throw new Error("h called with invalid arguments");
  let n21, l22;
  return o17 && 1 === o17.length && "string" == typeof o17[0] ? n21 = o17[0] : o17 && (l22 = [], r17(e20, o17, l22), 0 === l22.length && (l22 = void 0)), { vnodeSelector: e20, properties: t24, children: l22, text: "" === n21 ? void 0 : n21, domNode: null };
}

// node_modules/@arcgis/core/views/overlay/TextOverlayItem.js
var l17 = { bottom: "esri-text-overlay-item-anchor-bottom", "bottom-right": "esri-text-overlay-item-anchor-bottom-right", "bottom-left": "esri-text-overlay-item-anchor-bottom-left", top: "esri-text-overlay-item-anchor-top", "top-right": "esri-text-overlay-item-anchor-top-right", "top-left": "esri-text-overlay-item-anchor-top-left", center: "esri-text-overlay-item-anchor-center", right: "esri-text-overlay-item-anchor-right", left: "esri-text-overlay-item-anchor-left" };
var n13 = class extends v2 {
  get position() {
    return [this.x, this.y];
  }
  set position(t24) {
    this._set("x", t24[0]), this._set("y", t24[1]);
  }
  get _textShadowColor() {
    return this.backgroundColor;
  }
  get _textShadow() {
    const t24 = this._textShadowColor.toCss(false);
    return `0 0 ${this._textShadowSize}px ${t24}`;
  }
  get _padding() {
    return 0.5 * this.fontSize;
  }
  get _borderRadius() {
    return this._padding;
  }
  constructor(t24) {
    super(t24), this.x = 0, this.y = 0, this.text = "-", this.fontSize = 14, this.anchor = "center", this.visible = true, this.backgroundColor = new l8([0, 0, 0, 0.6]), this.textColor = new l8([255, 255, 255]), this._textShadowSize = 1;
  }
  render() {
    return t14("div", { classes: this._cssClasses(), styles: { left: Math.floor(this.x) + "px", top: Math.floor(this.y) + "px", visibility: this.visible ? "visible" : "hidden", fontSize: this.fontSize + "px", lineHeight: this.fontSize + "px", backgroundColor: this.backgroundColor.toCss(true), color: this.textColor.toCss(true), padding: this._padding + "px", borderRadius: this._borderRadius + "px", textShadow: this._textShadow } }, [this.text]);
  }
  renderCanvas(t24) {
    if (!this.visible) return;
    const e20 = t24.font.replace(/^(.*?)px/, "");
    t24.font = `${this.fontSize}px ${e20}`;
    const o17 = this._padding, i19 = this._borderRadius, r24 = t24.measureText(this.text).width, s19 = this.fontSize, l22 = h15[this.anchor];
    t24.textAlign = "center", t24.textBaseline = "middle";
    const n21 = r24 + 2 * o17, a27 = s19 + 2 * o17, x24 = this.x + l22.x * n21, p25 = this.y + l22.y * a27;
    this._roundedRect(t24, x24, p25, n21, a27, i19), t24.fillStyle = this.backgroundColor.toCss(true), t24.fill();
    const d15 = this.x + (l22.x + 0.5) * n21, c21 = this.y + (l22.y + 0.5) * a27;
    this._renderTextShadow(t24, this.text, d15, c21), t24.fillStyle = this.textColor.toCss(true), t24.fillText(this.text, d15, c21);
  }
  _renderTextShadow(t24, e20, o17, i19) {
    t24.lineJoin = "miter", t24.fillStyle = `rgba(${this._textShadowColor.r}, ${this._textShadowColor.g}, ${this._textShadowColor.b}, ${1 / a20.length})`;
    const r24 = this._textShadowSize;
    for (const [s19, l22] of a20) t24.fillText(e20, o17 + r24 * s19, i19 + r24 * l22);
  }
  _roundedRect(t24, e20, o17, i19, r24, s19) {
    t24.beginPath(), t24.moveTo(e20, o17 + s19), t24.arcTo(e20, o17, e20 + s19, o17, s19), t24.lineTo(e20 + i19 - s19, o17), t24.arcTo(e20 + i19, o17, e20 + i19, o17 + s19, s19), t24.lineTo(e20 + i19, o17 + r24 - s19), t24.arcTo(e20 + i19, o17 + r24, e20 + i19 - s19, o17 + r24, s19), t24.lineTo(e20 + s19, o17 + r24), t24.arcTo(e20, o17 + r24, e20, o17 + r24 - s19, s19), t24.closePath();
  }
  _cssClasses() {
    const t24 = { "esri-text-overlay-item": true };
    for (const e20 in l17) t24[l17[e20]] = this.anchor === e20;
    return t24;
  }
};
e([y2()], n13.prototype, "x", void 0), e([y2()], n13.prototype, "y", void 0), e([y2()], n13.prototype, "position", null), e([y2()], n13.prototype, "text", void 0), e([y2()], n13.prototype, "fontSize", void 0), e([y2()], n13.prototype, "anchor", void 0), e([y2()], n13.prototype, "visible", void 0), e([y2()], n13.prototype, "backgroundColor", void 0), e([y2()], n13.prototype, "textColor", void 0), e([y2()], n13.prototype, "_textShadowSize", void 0), e([y2()], n13.prototype, "_textShadowColor", null), e([y2()], n13.prototype, "_textShadow", null), e([y2()], n13.prototype, "_padding", null), e([y2()], n13.prototype, "_borderRadius", null), n13 = e([a3("esri.views.overlay.TextOverlayItem")], n13);
var h15 = { bottom: { x: -0.5, y: -1, textAlign: "center", textBaseline: "bottom" }, "bottom-left": { x: 0, y: -1, textAlign: "left", textBaseline: "bottom" }, "bottom-right": { x: -1, y: -1, textAlign: "right", textBaseline: "bottom" }, center: { x: -0.5, y: -0.5, textAlign: "center", textBaseline: "middle" }, left: { x: 0, y: -0.5, textAlign: "left", textBaseline: "middle" }, right: { x: -1, y: -0.5, textAlign: "right", textBaseline: "middle" }, top: { x: -0.5, y: 0, textAlign: "center", textBaseline: "top" }, "top-left": { x: 0, y: 0, textAlign: "left", textBaseline: "top" }, "top-right": { x: -1, y: 0, textAlign: "right", textBaseline: "top" } };
var a20 = [];
{
  const t24 = 16;
  for (let e20 = 0; e20 < 360; e20 += 360 / t24) a20.push([Math.cos(Math.PI * e20 / 180), Math.sin(Math.PI * e20 / 180)]);
}
var x11 = n13;

// node_modules/@arcgis/core/views/interactive/SegmentLabels.js
var S6 = 3025;
var w7 = { default: 15, far: 25 };
var V2 = class extends v2 {
  constructor(e20) {
    super(e20), this.context = null, this.stagedVertex = null, this.visible = true, this.edgeDistance = "default", this._messagesUnits = null, this._labelInfos = [], this._nextLabelIndex = 0;
  }
  initialize() {
    const e20 = j4(async (e21) => {
      const t25 = await u2("esri/core/t9n/Units");
      f2(e21), this._messagesUnits = t25;
    }), t24 = () => o(this.context, (e21) => e21.editGeometryOperations);
    this.addHandles([l3(() => [r(this.context) && this.getCameraOrExtent(this.context), this.visible, this._edgeDistancePixels, this.stagedVertex, this._messagesUnits], () => this._update()), ...["vertex-add", "vertex-update", "vertex-remove"].map((e21) => a7(t24, e21, () => this._update())), n2(() => e20.abort())]);
  }
  destroy() {
    for (this._nextLabelIndex = 0; this._labelInfos.length; ) this._destroyLabel(this._labelInfos.pop());
  }
  get updating() {
    return t(this._messagesUnits);
  }
  get test() {
    return { labelContents: this._labelInfos.slice(0, this._nextLabelIndex).map((e20) => e20.label.text) };
  }
  get _edgeDistancePixels() {
    return w7[this.edgeDistance];
  }
  _update() {
    this._nextLabelIndex = 0;
    const e20 = this.context;
    if (t(e20)) return void this._destroyUnusedLabels();
    const { components: t24, geometry: s19, coordinateHelper: o17 } = e20.editGeometryOperations.data;
    if (!s19) return void this._destroyUnusedLabels();
    const a27 = t24.length;
    for (let i19 = 0; i19 < a27; ++i19) {
      const n21 = [];
      if (t24[i19].iterateVertices((e21) => {
        n21.push(o17.toXYZ(e21.pos));
      }), 0 === i19 && r(this.stagedVertex) && n21.push(o17.toXYZ(this.stagedVertex)), n21.length < 2) continue;
      const l22 = n21[0], p25 = n21[n21.length - 1];
      "polygon" === s19.type && n21.length > 2 && !G(l22, p25) && n21.push(l22);
      const c21 = 1 === a27 && !h3(n21, false, false);
      let h25 = C4, d15 = G4;
      this.toScreenPointArray(e20, l22, h25);
      for (let t25 = 1; t25 < n21.length; ++t25) {
        const s20 = n21[t25 - 1], o18 = n21[t25];
        this.toScreenPointArray(e20, o18, d15), this._addLabel(e20, s20, h25, o18, d15, c21), [h25, d15] = [d15, h25];
      }
    }
    this._destroyUnusedLabels();
  }
  _addLabel(e20, t24, s19, o17, i19, a27) {
    const { label: n21 } = this._getOrCreateLabel(e20);
    if (!this.visible || b3(s19, i19) < S6) return void (n21.visible = false);
    const p25 = r(e20.graphicState) ? e20.graphicState.isDraped ? "on-the-ground" : "absolute-height" : i7(e20.editGeometryOperations.data.geometry, e20.elevationInfo), { spatialReference: c21 } = e20.editGeometryOperations.data, h25 = d6(t24, o17, c21, p25), d15 = this._messagesUnits, m17 = i9(e20.view);
    n21.text = r(d15) && r(h25) ? x8(d15, h25, m17) : "", n21.visible = true;
    const y12 = i19[0] - s19[0], v15 = i19[1] - s19[1];
    a27 ? r8(P5, -v15, y12) : r8(P5, v15, -y12), v4(P5, P5), l6(P5, P5, this._edgeDistancePixels), A4(k3, s19, i19, 0.5), s4(k3, k3, P5), n21.position = [k3[0], k3[1]], Math.abs(P5[0]) > Math.abs(P5[1]) ? n21.anchor = P5[0] > 0 ? "left" : "right" : n21.anchor = -P5[1] < 0 ? "top" : "bottom";
  }
  _getOrCreateLabel(e20) {
    var _a;
    if (this._labelInfos.length > this._nextLabelIndex) return this._labelInfos[this._nextLabelIndex++];
    const t24 = new x11({ fontSize: 10, anchor: "center" });
    (_a = e20.view.overlay) == null ? void 0 : _a.items.add(t24);
    const s19 = { label: t24 };
    return this._labelInfos.push(s19), this._nextLabelIndex = this._labelInfos.length, s19;
  }
  _destroyUnusedLabels() {
    for (; this._labelInfos.length > this._nextLabelIndex; ) this._destroyLabel(this._labelInfos.pop());
  }
  _destroyLabel({ label: e20 }) {
    o(this.context, (t24) => {
      var _a;
      return (_a = t24.view.overlay) == null ? void 0 : _a.items.remove(e20);
    }), e20.destroy();
  }
};
e([y2()], V2.prototype, "context", void 0), e([y2()], V2.prototype, "stagedVertex", void 0), e([y2()], V2.prototype, "visible", void 0), e([y2()], V2.prototype, "edgeDistance", void 0), e([y2()], V2.prototype, "updating", null), e([y2()], V2.prototype, "_messagesUnits", void 0), e([y2()], V2.prototype, "_edgeDistancePixels", null), V2 = e([a3("esri.views.interactive")], V2);
var P5 = n9();
var k3 = n9();
var C4 = i3();
var G4 = i3();

// node_modules/@arcgis/core/views/3d/interactive/SegmentLabels3D.js
var a21 = class extends V2 {
  getCameraOrExtent({ view: e20 }) {
    return e20.state.camera;
  }
  toScreenPointArray({ view: e20, elevationInfo: t24, editGeometryOperations: o17 }, s19, a27 = i3()) {
    const { spatialReference: m17 } = o17.data.coordinateHelper;
    return f12(s19, m17, t24, e20, c12), e20.state.camera.projectToScreen(c12, a27), a27;
  }
};
a21 = e([a3("esri.views.3d.interactive.SegmentLabels3D")], a21);
var c12 = n4();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/DrapedVisualElementResources.js
var _5 = class {
  constructor(e20) {
    this._resourceFactory = e20, this._resources = null, this._visible = true, this._attached = false, this._renderGroup = a17.Outline;
  }
  destroy() {
    this._destroyResources();
  }
  get resources() {
    return r(this._resources) ? this._resources.external : null;
  }
  get visible() {
    return this._visible;
  }
  set visible(e20) {
    e20 !== this._visible && (this._visible = e20, this._syncGeometriesToRenderer());
  }
  get attached() {
    return this._attached;
  }
  set attached(e20) {
    e20 !== this._attached && (this._attached = e20, this._createOrDestroyResources());
  }
  get renderGroup() {
    return this._renderGroup;
  }
  set renderGroup(e20) {
    var _a;
    this._renderGroup = e20;
    const r24 = (_a = f(this._resources)) == null ? void 0 : _a.layerView;
    r24 && (r24.renderGroup = e20);
  }
  recreate() {
    this.attached && this._createResources();
  }
  recreateGeometry() {
    this._resourceFactory.recreateGeometry ? t(this._resources) || (this._ensureRenderGeometriesRemoved(), this._resourceFactory.recreateGeometry(this._resources.external), this._syncGeometriesToRenderer()) : this.recreate();
  }
  _createOrDestroyResources() {
    this._attached ? t(this._resources) && this._createResources() : this._destroyResources();
  }
  _createResources() {
    var _a;
    this._destroyResources();
    const e20 = this._resourceFactory.createResources(), r24 = new p16({ view: this._resourceFactory.view, renderGroup: this._renderGroup }), s19 = (_a = this._resourceFactory.view.basemapTerrain) == null ? void 0 : _a.overlayManager;
    this._resources = { layerView: new p16({ view: this._resourceFactory.view, renderGroup: this._renderGroup }), external: e20, geometriesAdded: false }, s19 && (this._resources.drapeSourceRenderer = s19.registerGeometryDrapeSource(r24)), this._syncGeometriesToRenderer();
  }
  _destroyResources() {
    var _a;
    if (t(this._resources)) return;
    this._ensureRenderGeometriesRemoved();
    const e20 = (_a = this._resourceFactory.view.basemapTerrain) == null ? void 0 : _a.overlayManager;
    e20 && e20.unregisterDrapeSource(this._resources.layerView), this._resourceFactory.destroyResources(this._resources.external), this._resources = null;
  }
  _syncGeometriesToRenderer() {
    this._visible ? this._ensureRenderGeometriesAdded() : this._ensureRenderGeometriesRemoved();
  }
  _ensureRenderGeometriesRemoved() {
    if (t(this._resources) || t(this._resources.drapeSourceRenderer)) return;
    if (!this._resources.geometriesAdded) return;
    this._resources.drapeSourceRenderer.removeGeometries(this._resources.external.geometries, E11.UPDATE), this._resources.geometriesAdded = false;
  }
  _ensureRenderGeometriesAdded() {
    if (t(this._resources) || t(this._resources.drapeSourceRenderer)) return;
    if (this._resources.geometriesAdded) return;
    this._resources.drapeSourceRenderer.addGeometries(this._resources.external.geometries, E11.UPDATE), this._resources.geometriesAdded = true;
  }
};
var p16 = class extends s5(v2) {
  constructor(e20) {
    super(e20), this.drapeSourceType = e13.Features, this.updatePolicy = C2.SYNC, this.renderGroup = a17.Outline;
  }
};
e([y2({ constructOnly: true })], p16.prototype, "view", void 0), e([y2({ readOnly: true })], p16.prototype, "drapeSourceType", void 0), e([y2()], p16.prototype, "renderGroup", void 0), p16 = e([a3("DrapedVisualElementLayerView")], p16);

// node_modules/@arcgis/core/views/3d/interactive/visualElements/VisualElement.js
var t15 = class {
  constructor(t24) {
    this._attached = false, this._resourcesCreated = false, this._visible = true, this.view = t24, this._handle = l3(() => this.view.ready, (e20) => {
      this._resourcesCreated && (e20 ? this._createResources() : this._destroyResources());
    });
  }
  applyProps(e20) {
    let t24 = false;
    for (const s19 in e20) s19 in this ? "attached" === s19 ? t24 = e20[s19] : this[s19] = e20[s19] : console.error("Cannot set unknown property", s19);
    this.attached = t24;
  }
  destroy() {
    this.attached = false, this._handle.remove();
  }
  get attached() {
    return this._attached;
  }
  set attached(e20) {
    e20 !== this._attached && this.view._stage && (this._attached = e20, this._attached && !this._resourcesCreated ? this._createResources() : !this._attached && this._resourcesCreated && this._destroyResources(), this.onAttachedChange(e20));
  }
  onAttachedChange(e20) {
  }
  get visible() {
    return this._visible;
  }
  set visible(e20) {
    e20 !== this._visible && (this._visible = e20, this.attached && this.updateVisibility(e20));
  }
  _createResources() {
    this.createResources(), this._resourcesCreated = true, this.updateVisibility(this.visible);
  }
  _destroyResources() {
    this.destroyResources(), this._resourcesCreated = false;
  }
};

// node_modules/@arcgis/core/views/3d/interactive/visualElements/VisualElementResources.js
var u7 = class {
  constructor(e20) {
    this._resourceFactory = e20, this._resources = null, this._visible = true, this._attached = false;
  }
  destroy() {
    this._destroyResources();
  }
  get object() {
    return r(this._resources) ? this._resources.object : null;
  }
  get resources() {
    return r(this._resources) ? this._resources.external : null;
  }
  get visible() {
    return this._visible;
  }
  set visible(e20) {
    e20 !== this._visible && (this._visible = e20, this._syncVisible());
  }
  get attached() {
    return this._attached;
  }
  set attached(e20) {
    e20 !== this._attached && (this._attached = e20, this._createOrDestroyResources());
  }
  recreate() {
    this.attached && this._createResources();
  }
  recreateGeometry() {
    if (!this._resourceFactory.recreateGeometry) return void this.recreate();
    const e20 = this._resourceFactory.view._stage;
    if (t(this._resources) || !e20) return;
    const r24 = this._resources.object;
    this._resources.external.forEach((s19) => {
      s19.type !== e8.Mesh && s19.type !== e8.Line && s19.type !== e8.Point || e20.remove(s19);
    }), r24.removeAllGeometries(), this._resourceFactory.recreateGeometry(this._resources.external, r24, this._resources.layer), this._resources.external.forEach((s19) => {
      s19.type !== e8.Mesh && s19.type !== e8.Line && s19.type !== e8.Point || e20.add(s19);
    });
  }
  _createOrDestroyResources() {
    this._attached ? this._resources || this._createResources() : this._destroyResources();
  }
  _createResources() {
    this._destroyResources();
    const e20 = this._resourceFactory, s19 = e20.view, o17 = s19._stage;
    if (!o17) return;
    const u13 = new l15({ pickable: false, updatePolicy: C2.SYNC });
    o17.add(u13);
    const _9 = new x10({ castShadow: false }), n21 = e20.createResources(_9, u13);
    n21.forEach((e21) => {
      o17.add(e21), e21 instanceof G2 && o17.loadImmediate(e21);
    }), o17.add(_9), u13.add(_9);
    const l22 = e20.cameraChanged, y12 = l22 ? l3(() => s19.state.camera, (e21) => l22(e21), h4) : null;
    this._resources = { layer: u13, object: _9, external: n21, cameraHandle: y12 }, this._syncVisible();
  }
  _destroyResources() {
    var _a;
    if (t(this._resources)) return;
    const e20 = this._resourceFactory.view._stage;
    e20 && (e20.remove(this._resources.object), e20.remove(this._resources.layer), this._resources.external.forEach((s19) => e20.remove(s19))), this._resources.object.dispose(), (_a = this._resources.cameraHandle) == null ? void 0 : _a.remove(), this._resourceFactory.destroyResources(this._resources.external), this._resources = null;
  }
  _syncVisible() {
    t(this._resources) || (this._resources.object.visible = this._visible);
  }
};

// node_modules/@arcgis/core/views/3d/interactive/visualElements/EngineVisualElement.js
var t16 = class extends t15 {
  constructor({ view: s19, isDraped: t24 }) {
    super(s19), this._isDraped = false, this.object3dResources = new u7(this.createObject3DResourceFactory(s19)), this.drapedResources = new _5(this.createDrapedResourceFactory(s19)), this.isDraped = t24 ?? false;
  }
  get isDraped() {
    return this._isDraped;
  }
  set isDraped(e20) {
    e20 !== this._isDraped && (this._isDraped = e20, this.object3dResources.attached = this.attached && !e20, this.drapedResources.attached = this.attached && e20);
  }
  get renderGroup() {
    return this.drapedResources.renderGroup;
  }
  set renderGroup(e20) {
    this.drapedResources.renderGroup = e20;
  }
  createResources() {
    this.object3dResources.attached = !this._isDraped, this.drapedResources.attached = this._isDraped;
  }
  destroyResources() {
    this.object3dResources.attached = false, this.drapedResources.attached = false;
  }
  recreate() {
    this.object3dResources.recreate(), this.drapedResources.recreate();
  }
  recreateGeometry() {
    this.object3dResources.recreateGeometry(), this.drapedResources.recreateGeometry();
  }
  destroy() {
    this.object3dResources.destroy(), this.drapedResources.destroy(), super.destroy();
  }
  updateVisibility(e20) {
    this.object3dResources.visible = e20, this.drapedResources.visible = e20;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LaserlinePathTechnique.js
var m8 = class _m extends e9 {
  initializeProgram(e20) {
    return new o9(e20.rctx, _m.shader.get().build(this.configuration), h16);
  }
  initializePipeline() {
    return W({ blending: s9(R3.ONE, R3.ONE_MINUS_SRC_ALPHA), colorWrite: _3 });
  }
};
m8.shader = new t8(w5, () => import("./LaserlinePath.glsl-CVMIQAQG.js"));
var h16 = /* @__PURE__ */ new Map([[O3.START, 0], [O3.END, 1], [O3.UP, 2], [O3.EXTRUDE, 3]]);

// node_modules/@arcgis/core/views/3d/support/LaserlinePathData.js
var d8 = class {
  constructor(e20) {
    this._renderCoordsHelper = e20, this._buffers = null, this._origin = n4(), this._dirty = false, this._count = 0, this._vao = null;
  }
  set vertices(e20) {
    const t24 = n7(3 * e20.length);
    let r24 = 0;
    for (const s19 of e20) t24[r24++] = s19[0], t24[r24++] = s19[1], t24[r24++] = s19[2];
    this.buffers = [t24];
  }
  set buffers(e20) {
    if (this._buffers = e20, this._buffers.length > 0) {
      const e21 = this._buffers[0], t24 = 3 * Math.floor(e21.length / 3 / 2);
      o3(this._origin, e21[t24 + 0], e21[t24 + 1], e21[t24 + 2]);
    } else o3(this._origin, 0, 0, 0);
    this._dirty = true;
  }
  get origin() {
    return this._origin;
  }
  draw(t24) {
    const r24 = this._ensureVAO(t24);
    r(r24) && (t24.bindVAO(r24), t24.drawArrays(E2.TRIANGLES, 0, this._count));
  }
  dispose() {
    r(this._vao) && this._vao.dispose();
  }
  _ensureVAO(e20) {
    return t(this._buffers) ? null : (t(this._vao) && (this._vao = this._createVAO(e20, this._buffers)), this._ensureVertexData(this._vao, this._buffers), this._vao);
  }
  _createVAO(e20, t24) {
    const r24 = this._createDataBuffer(t24);
    return this._dirty = false, new r10(e20, h16, { data: o5(V3) }, { data: E4.createVertex(e20, F3.STATIC_DRAW, r24) });
  }
  _ensureVertexData(e20, t24) {
    var _a;
    if (!this._dirty) return;
    const r24 = this._createDataBuffer(t24);
    (_a = e20.vertexBuffers.data) == null ? void 0 : _a.setData(r24), this._dirty = false;
  }
  _numberOfRenderVertices(e20) {
    return 3 * (2 * (e20.length / 3 - 1));
  }
  _createDataBuffer(e20) {
    const t24 = e20.reduce((e21, t25) => e21 + this._numberOfRenderVertices(t25), 0);
    this._count = t24;
    const i19 = V3.createBuffer(t24), o17 = this._origin;
    let n21 = 0, f19 = 0;
    for (const u13 of e20) {
      for (let e21 = 0; e21 < u13.length; e21 += 3) {
        const t25 = o3(m9, u13[e21 + 0], u13[e21 + 1], u13[e21 + 2]);
        0 === e21 ? f19 = this._renderCoordsHelper.getAltitude(t25) : this._renderCoordsHelper.setAltitude(t25, f19);
        const a27 = this._renderCoordsHelper.worldUpAtPosition(t25, b8), c21 = n21 + 2 * e21, h25 = e3(m9, t25, o17);
        if (e21 < u13.length - 3) {
          i19.up.setVec(c21, a27), i19.up.setVec(c21 + 3, a27), i19.up.setVec(c21 + 5, a27);
          for (let e22 = 0; e22 < 6; e22++) i19.start.setVec(c21 + e22, h25);
          i19.extrude.setValues(c21 + 0, 0, -1), i19.extrude.setValues(c21 + 1, 1, -1), i19.extrude.setValues(c21 + 2, 1, 1), i19.extrude.setValues(c21 + 3, 0, -1), i19.extrude.setValues(c21 + 4, 1, 1), i19.extrude.setValues(c21 + 5, 0, 1);
        }
        if (e21 > 0) {
          i19.up.setVec(c21 - 2, a27), i19.up.setVec(c21 - 4, a27), i19.up.setVec(c21 - 5, a27);
          for (let e22 = -6; e22 < 0; e22++) i19.end.setVec(c21 + e22, h25);
        }
      }
      n21 += this._numberOfRenderVertices(u13);
    }
    return i19.buffer;
  }
};
var b8 = n4();
var m9 = n4();
var V3 = T().vec3f(O3.START).vec3f(O3.END).vec3f(O3.UP).vec2f(O3.EXTRUDE);

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LaserlinePathTechniqueConfiguration.js
var e16 = class extends t9 {
  constructor() {
    super(...arguments), this.contrastControlEnabled = false;
  }
};
e([r12()], e16.prototype, "contrastControlEnabled", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LaserlineTechnique.js
var f14 = class extends n10 {
  constructor() {
    super(...arguments), this.innerColor = r3(1, 1, 1), this.innerWidth = 1, this.glowColor = r3(1, 0.5, 0), this.glowWidth = 8, this.glowFalloff = 8, this.globalAlpha = 0.75, this.globalAlphaContrastBoost = 2, this.angleCutoff = m2(6), this.pointDistanceOrigin = n4(), this.pointDistanceTarget = n4(), this.lineVerticalPlaneSegment = v5(), this.intersectsLineSegment = v5(), this.intersectsLineRadius = 3, this.heightManifoldTarget = n4(), this.lineStartWorld = n4(), this.lineEndWorld = n4();
  }
};
var p17 = class _p extends e9 {
  initializeProgram(e20) {
    return new o9(e20.rctx, _p.shader.get().build(this.configuration), E3);
  }
  initializePipeline() {
    return W({ blending: s9(R3.ONE, R3.ONE_MINUS_SRC_ALPHA), colorWrite: _3 });
  }
};
p17.shader = new t8(H3, () => import("./Laserlines.glsl-POWDH6XU.js"));

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/LaserlineTechniqueConfiguration.js
var n14 = class extends e16 {
  constructor() {
    super(...arguments), this.heightManifoldEnabled = false, this.pointDistanceEnabled = false, this.lineVerticalPlaneEnabled = false, this.intersectsLineEnabled = false, this.spherical = false;
  }
};
e([r12()], n14.prototype, "heightManifoldEnabled", void 0), e([r12()], n14.prototype, "pointDistanceEnabled", void 0), e([r12()], n14.prototype, "lineVerticalPlaneEnabled", void 0), e([r12()], n14.prototype, "intersectsLineEnabled", void 0), e([r12()], n14.prototype, "spherical", void 0);

// node_modules/@arcgis/core/views/3d/support/LaserLineRenderer.js
var D4 = class {
  constructor(e20, t24 = { contrastControlEnabled: false }) {
    this._config = t24, this._technique = null, this._heightManifoldEnabled = false, this._pointDistanceEnabled = false, this._lineVerticalPlaneEnabled = false, this._intersectsLineEnabled = false, this._intersectsLineInfinite = false, this._viewingMode = l9.Local, this._pathVerticalPlaneEnabled = false, this._pathVerticalPlaneData = null, this._pathTechnique = null, this.canRender = true, this._passParameters = A5(e20, new f14());
  }
  get renderSlots() {
    return [this._config.contrastControlEnabled ? E6.LASERLINES_CONTRAST_CONTROL : E6.LASERLINES];
  }
  get needsLinearDepth() {
    return true;
  }
  get heightManifoldEnabled() {
    return this._heightManifoldEnabled;
  }
  set heightManifoldEnabled(e20) {
    this._heightManifoldEnabled !== e20 && (this._heightManifoldEnabled = e20, this._requestRender());
  }
  get heightManifoldTarget() {
    return this._passParameters.heightManifoldTarget;
  }
  set heightManifoldTarget(e20) {
    r4(this._passParameters.heightManifoldTarget, e20), this._requestRender();
  }
  get pointDistanceEnabled() {
    return this._pointDistanceEnabled;
  }
  set pointDistanceEnabled(e20) {
    e20 !== this._pointDistanceEnabled && (this._pointDistanceEnabled = e20, this._requestRender());
  }
  get pointDistanceTarget() {
    return this._passParameters.pointDistanceTarget;
  }
  set pointDistanceTarget(e20) {
    r4(this._passParameters.pointDistanceTarget, e20), this._requestRender();
  }
  get pointDistanceOrigin() {
    return this._passParameters.pointDistanceOrigin;
  }
  set pointDistanceOrigin(e20) {
    r4(this._passParameters.pointDistanceOrigin, e20), this._requestRender();
  }
  get lineVerticalPlaneEnabled() {
    return this._lineVerticalPlaneEnabled;
  }
  set lineVerticalPlaneEnabled(e20) {
    e20 !== this._lineVerticalPlaneEnabled && (this._lineVerticalPlaneEnabled = e20, this._requestRender());
  }
  get lineVerticalPlaneSegment() {
    return this._passParameters.lineVerticalPlaneSegment;
  }
  set lineVerticalPlaneSegment(e20) {
    h7(e20, this._passParameters.lineVerticalPlaneSegment), this._requestRender();
  }
  get intersectsLineEnabled() {
    return this._intersectsLineEnabled;
  }
  set intersectsLineEnabled(e20) {
    e20 !== this._intersectsLineEnabled && (this._intersectsLineEnabled = e20, this._requestRender());
  }
  get intersectsLineSegment() {
    return this._passParameters.intersectsLineSegment;
  }
  set intersectsLineSegment(e20) {
    h7(e20, this._passParameters.intersectsLineSegment), this._requestRender();
  }
  get intersectsLineRadius() {
    return this._passParameters.intersectsLineRadius;
  }
  set intersectsLineRadius(e20) {
    e20 !== this._passParameters.intersectsLineRadius && (this._passParameters.intersectsLineRadius = e20, this._requestRender());
  }
  get intersectsLineInfinite() {
    return this._intersectsLineInfinite;
  }
  set intersectsLineInfinite(e20) {
    e20 !== this._intersectsLineInfinite && (this._intersectsLineInfinite = e20, this._requestRender());
  }
  get viewingMode() {
    return this._viewingMode;
  }
  set viewingMode(e20) {
    e20 !== this._viewingMode && (this._viewingMode = e20, this._requestRender());
  }
  get pathVerticalPlaneEnabled() {
    return this._pathVerticalPlaneEnabled;
  }
  set pathVerticalPlaneEnabled(t24) {
    t24 !== this._pathVerticalPlaneEnabled && (this._pathVerticalPlaneEnabled = t24, r(this._pathVerticalPlaneData) && this._requestRender());
  }
  set pathVerticalPlaneVertices(e20) {
    t(this._pathVerticalPlaneData) && (this._pathVerticalPlaneData = new d8(this._passParameters.renderCoordsHelper)), this._pathVerticalPlaneData.vertices = e20, this.pathVerticalPlaneEnabled && this._requestRender();
  }
  set pathVerticalPlaneBuffers(e20) {
    t(this._pathVerticalPlaneData) && (this._pathVerticalPlaneData = new d8(this._passParameters.renderCoordsHelper)), this._pathVerticalPlaneData.buffers = e20, this.pathVerticalPlaneEnabled && this._requestRender();
  }
  setParameters(e20) {
    P2(this._passParameters, e20) && this._requestRender();
  }
  initializeRenderContext(e20) {
    this._context = e20;
    const t24 = e20.renderContext.rctx;
    this._quadVAO = f8(t24), this._techniqueRepository = e20.techniqueRepository, this._techniqueConfig = new n14();
    const i19 = new e16();
    i19.contrastControlEnabled = this._config.contrastControlEnabled, this._pathTechnique = this._techniqueRepository.acquire(m8, i19);
  }
  uninitializeRenderContext() {
    this._quadVAO = h(this._quadVAO), this._technique = y(this._technique), this._pathVerticalPlaneData = h(this._pathVerticalPlaneData), this._pathTechnique = y(this._pathTechnique);
  }
  prepareTechnique() {
    return this.heightManifoldEnabled || this.pointDistanceEnabled || this.lineVerticalPlaneSegment || this.intersectsLineEnabled ? (this._techniqueConfig.heightManifoldEnabled = this.heightManifoldEnabled, this._techniqueConfig.lineVerticalPlaneEnabled = this.lineVerticalPlaneEnabled, this._techniqueConfig.pointDistanceEnabled = this.pointDistanceEnabled, this._techniqueConfig.intersectsLineEnabled = this.intersectsLineEnabled, this._techniqueConfig.contrastControlEnabled = this._config.contrastControlEnabled, this._techniqueConfig.spherical = this._viewingMode === l9.Global, this._technique = this._techniqueRepository.releaseAndAcquire(p17, this._techniqueConfig, this._technique), this._technique) : this._pathTechnique;
  }
  render(e20, t24) {
    (this.heightManifoldEnabled || this.pointDistanceEnabled || this.lineVerticalPlaneSegment || this.intersectsLineEnabled) && this._renderUnified(e20, t24), this.pathVerticalPlaneEnabled && this._renderPath(e20);
  }
  _renderUnified(e20, t24) {
    const i19 = e20.rctx;
    this._updatePassParameters(e20), i19.bindTechnique(t24, this._passParameters, e20.bindParameters), i19.bindVAO(this._quadVAO), i19.drawArrays(E2.TRIANGLE_STRIP, 0, 4);
  }
  _renderPath(e20) {
    if (t(this._pathVerticalPlaneData) || t(this._pathTechnique)) return;
    const i19 = e20.rctx, n21 = this._pathTechnique;
    i19.bindTechnique(n21, { ...this._passParameters, origin: this._pathVerticalPlaneData.origin }, e20.bindParameters), this._pathVerticalPlaneData.draw(e20.rctx);
  }
  _updatePassParameters(e20) {
    if (!this._intersectsLineEnabled) return;
    const t24 = e20.bindParameters.camera;
    if (this._intersectsLineInfinite) {
      if (y5(p5(this._passParameters.intersectsLineSegment.origin, this._passParameters.intersectsLineSegment.vector), T2), T2.c0 = -Number.MAX_VALUE, !a14(t24.frustum, T2)) return;
      p8(T2, this._passParameters.lineStartWorld), g7(T2, this._passParameters.lineEndWorld);
    } else r4(this._passParameters.lineStartWorld, this._passParameters.intersectsLineSegment.origin), u(this._passParameters.lineEndWorld, this._passParameters.intersectsLineSegment.origin, this._passParameters.intersectsLineSegment.vector);
  }
  _requestRender() {
    this._context && this._context.requestRender();
  }
};
var T2 = a13();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/LaserlineVisualElement.js
var d9 = class extends t15 {
  constructor(e20) {
    super(e20.view), this._angleCutoff = C3, this._style = {}, this._heightManifoldTarget = n4(), this._heightManifoldEnabled = false, this._intersectsLine = v5(), this._intersectsLineEnabled = false, this._intersectsLineInfinite = false, this._lineVerticalPlaneSegment = null, this._pathVerticalPlaneBuffers = null, this._pointDistanceLine = null, this.applyProps(e20);
  }
  get testData() {
    return this._renderer;
  }
  createResources() {
    this._ensureRenderer();
  }
  destroyResources() {
    this._disposeRenderer();
  }
  updateVisibility() {
    this._syncRenderer(), this._syncHeightManifold(), this._syncIntersectsLine(), this._syncPathVerticalPlane(), this._syncLineVerticalPlane(), this._syncPointDistance();
  }
  get angleCutoff() {
    return this._angleCutoff;
  }
  set angleCutoff(e20) {
    this._angleCutoff !== e20 && (this._angleCutoff = e20, this._syncAngleCutoff());
  }
  get style() {
    return this._style;
  }
  set style(e20) {
    this._style = e20, this._syncStyle();
  }
  get heightManifoldTarget() {
    return this._heightManifoldEnabled ? this._heightManifoldTarget : null;
  }
  set heightManifoldTarget(t24) {
    r(t24) ? (r4(this._heightManifoldTarget, t24), this._heightManifoldEnabled = true) : this._heightManifoldEnabled = false, this._syncRenderer(), this._syncHeightManifold();
  }
  set intersectsWorldUpAtLocation(e20) {
    if (t(e20)) return void (this.intersectsLine = null);
    const i19 = this.view.renderCoordsHelper.worldUpAtPosition(e20, o13);
    this.intersectsLine = m3(e20, i19), this.intersectsLineInfinite = true;
  }
  get intersectsLine() {
    return this._intersectsLineEnabled ? this._intersectsLine : null;
  }
  set intersectsLine(t24) {
    r(t24) ? (h7(t24, this._intersectsLine), this._intersectsLineEnabled = true) : this._intersectsLineEnabled = false, this._syncIntersectsLine(), this._syncRenderer();
  }
  get intersectsLineInfinite() {
    return this._intersectsLineInfinite;
  }
  set intersectsLineInfinite(e20) {
    this._intersectsLineInfinite = e20, this._syncIntersectsLineInfinite();
  }
  get lineVerticalPlaneSegment() {
    return this._lineVerticalPlaneSegment;
  }
  set lineVerticalPlaneSegment(t24) {
    this._lineVerticalPlaneSegment = r(t24) ? h7(t24) : null, this._syncLineVerticalPlane(), this._syncRenderer();
  }
  get pathVerticalPlane() {
    return this._pathVerticalPlaneBuffers;
  }
  set pathVerticalPlane(e20) {
    this._pathVerticalPlaneBuffers = e20, this._syncPathVerticalPlane(), this._syncLineVerticalPlane(), this._syncPointDistance(), this._syncRenderer();
  }
  get pointDistanceLine() {
    return this._pointDistanceLine;
  }
  set pointDistanceLine(t24) {
    this._pointDistanceLine = r(t24) ? { origin: t4(t24.origin), target: t24.target ? t4(t24.target) : null } : null, this._syncPointDistance(), this._syncRenderer();
  }
  _syncRenderer() {
    this.attached && this.visible && (this._intersectsLineEnabled || this._heightManifoldEnabled || r(this._pointDistanceLine) || r(this._pathVerticalPlaneBuffers)) ? this._ensureRenderer() : this._disposeRenderer();
  }
  _ensureRenderer() {
    r(this._renderer) || (this._renderer = new D4({ renderCoordsHelper: this.view.renderCoordsHelper }, { contrastControlEnabled: true }), this._renderer.viewingMode = this.view.state.viewingMode, this._syncStyle(), this._syncHeightManifold(), this._syncIntersectsLine(), this._syncIntersectsLineInfinite(), this._syncPathVerticalPlane(), this._syncLineVerticalPlane(), this._syncPointDistance(), this._syncAngleCutoff(), this.view._stage && this.view._stage.addRenderPlugin(this._renderer.renderSlots, this._renderer));
  }
  _syncStyle() {
    t(this._renderer) || (this._renderer.setParameters(this._style), null != this._style.intersectsLineRadius && (this._renderer.intersectsLineRadius = this._style.intersectsLineRadius));
  }
  _syncAngleCutoff() {
    t(this._renderer) || this._renderer.setParameters({ angleCutoff: this._angleCutoff });
  }
  _syncHeightManifold() {
    t(this._renderer) || (this._renderer.heightManifoldEnabled = this._heightManifoldEnabled && this.visible, this._heightManifoldEnabled && (this._renderer.heightManifoldTarget = this._heightManifoldTarget));
  }
  _syncIntersectsLine() {
    t(this._renderer) || (this._renderer.intersectsLineEnabled = this._intersectsLineEnabled && this.visible, this._intersectsLineEnabled && (this._renderer.intersectsLineSegment = this._intersectsLine));
  }
  _syncIntersectsLineInfinite() {
    t(this._renderer) || (this._renderer.intersectsLineInfinite = this._intersectsLineInfinite);
  }
  _syncPathVerticalPlane() {
    t(this._renderer) || (this._renderer.pathVerticalPlaneEnabled = r(this._pathVerticalPlaneBuffers) && this.visible, r(this._pathVerticalPlaneBuffers) && (this._renderer.pathVerticalPlaneBuffers = this._pathVerticalPlaneBuffers));
  }
  _syncLineVerticalPlane() {
    t(this._renderer) || (this._renderer.lineVerticalPlaneEnabled = r(this._lineVerticalPlaneSegment) && this.visible, r(this._lineVerticalPlaneSegment) && (this._renderer.lineVerticalPlaneSegment = this._lineVerticalPlaneSegment));
  }
  _syncPointDistance() {
    if (t(this._renderer)) return;
    const i19 = this._pointDistanceLine, n21 = r(i19);
    this._renderer.pointDistanceEnabled = n21 && null != i19.target && this.visible, n21 && (this._renderer.pointDistanceOrigin = i19.origin, null != i19.target && (this._renderer.pointDistanceTarget = i19.target));
  }
  _disposeRenderer() {
    r(this._renderer) && this.view._stage && (this.view._stage.removeRenderPlugin(this._renderer), this._renderer = null);
  }
};
var o13 = n4();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/ExtendedLineVisualElement.js
var v8 = class extends t16 {
  constructor(e20) {
    super(e20), this._ray = d4(), this._isWorldDown = false, this._start = n4(), this._end = r3(1, 0, 0), this._width = 1, this._color = r13(1, 0, 1, 1), this._polygonOffset = false, this._writeDepthEnabled = true, this._innerWidth = 0, this._innerColor = r13(1, 1, 1, 1), this._stipplePattern = null, this._stippleOffColor = null, this._stipplePreferContinuous = true, this._falloff = 0, this._extensionType = W3.LINE, this._laserlineStyle = null, this._laserlineEnabled = false, this._renderOccluded = c3.OccludeAndTransparent, this._fadedExtensions = F4, this._laserline = new d9({ view: this.view }), this.applyProps(e20);
  }
  destroy() {
    this._laserline.destroy(), super.destroy();
  }
  createObject3DResourceFactory(e20) {
    return { view: e20, createResources: (e21) => this._createObject3DResources(e21), destroyResources: (e21) => this._destroyExternalResources(e21), recreateGeometry: (e21, t24) => this._recreateObject3DGeometry(e21, t24), cameraChanged: () => this._updateGeometry() };
  }
  createDrapedResourceFactory(e20) {
    return { view: e20, createResources: () => this._createDrapedResources(), destroyResources: (e21) => this._destroyExternalResources(e21), recreateGeometry: (e21) => this._recreateDrapedGeometry(e21) };
  }
  updateVisibility(e20) {
    super.updateVisibility(e20), this._laserline.visible = e20;
  }
  onAttachedChange() {
    this._laserline.attached = this._laserlineAttached;
  }
  setStartEndFromWorldDownAtLocation(e20) {
    this._isWorldDown = true, r4(this._start, e20), this.view.renderCoordsHelper.worldUpAtPosition(e20, this._end), e3(this._end, e20, this._end), h6(this._start, this._end, this._ray), this._updateGeometry();
  }
  get start() {
    return this._start;
  }
  set start(e20) {
    this._isWorldDown = false, F(this._start, e20) || (r4(this._start, e20), h6(this._start, this._end, this._ray), this._updateGeometry());
  }
  get end() {
    return this._end;
  }
  set end(e20) {
    this._isWorldDown = false, F(this._end, e20) || (r4(this._end, e20), h6(this._start, this._end, this._ray), this._updateGeometry());
  }
  get width() {
    return this._width;
  }
  set width(e20) {
    e20 !== this._width && (this._width = e20, this._updateMaterial());
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    D(e20, this._color) || (a4(this._color, e20), this._updateMaterial());
  }
  get polygonOffset() {
    return this._polygonOffset;
  }
  set polygonOffset(e20) {
    e20 !== this._polygonOffset && (this._polygonOffset = e20, this._updateMaterial());
  }
  get writeDepthEnabled() {
    return this._writeDepthEnabled;
  }
  set writeDepthEnabled(e20) {
    this._writeDepthEnabled !== e20 && (this._writeDepthEnabled = e20, this._updateMaterial());
  }
  get innerWidth() {
    return this._innerWidth;
  }
  set innerWidth(e20) {
    e20 !== this._innerWidth && (this._innerWidth = e20, this._updateMaterial());
  }
  get innerColor() {
    return this._innerColor;
  }
  set innerColor(e20) {
    D(e20, this._innerColor) || (a4(this._innerColor, e20), this._updateMaterial());
  }
  get stipplePattern() {
    return this._stipplePattern;
  }
  set stipplePattern(t24) {
    const r24 = r(t24) !== r(this._stipplePattern);
    this._stipplePattern = t24, r24 ? this.recreate() : this._updateMaterial();
  }
  get stippleOffColor() {
    return this._stippleOffColor;
  }
  set stippleOffColor(r24) {
    (t(r24) || t(this._stippleOffColor) || !D(r24, this._stippleOffColor)) && (this._stippleOffColor = r(r24) ? t12(r24) : null, this._updateMaterial());
  }
  get stipplePreferContinuous() {
    return this._stipplePreferContinuous;
  }
  set stipplePreferContinuous(e20) {
    e20 !== this._stipplePreferContinuous && (this._stipplePreferContinuous = e20, this._updateMaterial());
  }
  get falloff() {
    return this._falloff;
  }
  set falloff(e20) {
    e20 !== this._falloff && (this._falloff = e20, this._updateMaterial());
  }
  get extensionType() {
    return this._extensionType;
  }
  set extensionType(e20) {
    e20 !== this._extensionType && (this._extensionType = e20, this.recreateGeometry());
  }
  get _laserlineAttached() {
    return this._laserlineEnabled && r(this._laserlineStyle) && this.attached && !this.isDraped;
  }
  get laserlineStyle() {
    return this._laserlineStyle;
  }
  set laserlineStyle(t24) {
    this._laserlineStyle = t24, this._laserline.attached = this._laserlineAttached, r(t24) && (this._laserline.style = t24);
  }
  get laserlineEnabled() {
    return this._laserlineEnabled;
  }
  set laserlineEnabled(e20) {
    this._laserlineEnabled !== e20 && (this._laserlineEnabled = e20, this._laserline.attached = this._laserlineAttached);
  }
  get renderOccluded() {
    return this._renderOccluded;
  }
  set renderOccluded(e20) {
    e20 !== this._renderOccluded && (this._renderOccluded = e20, this._updateMaterial());
  }
  get _normalizedRenderOccluded() {
    return this.isDraped && this._renderOccluded === c3.OccludeAndTransparentStencil ? c3.OccludeAndTransparent : this._renderOccluded;
  }
  get fadedExtensions() {
    return this._fadedExtensions;
  }
  set fadedExtensions(e20) {
    this._fadedExtensions = l(e20, F4), this.recreateGeometry();
  }
  _updateMaterial() {
    var _a, _b;
    const { materialParameters: e20 } = this;
    (_a = f(this.object3dResources.resources)) == null ? void 0 : _a.material.setParameters(e20), (_b = f(this.drapedResources.resources)) == null ? void 0 : _b.material.setParameters(e20);
  }
  get materialParameters() {
    return { width: this._width, color: this._color, stippleOffColor: this._stippleOffColor, stipplePattern: this._stipplePattern, stipplePreferContinuous: this._stipplePreferContinuous, innerWidth: this._innerWidth, innerColor: this._innerColor, falloff: this._falloff, hasPolygonOffset: this._polygonOffset, renderOccluded: this._normalizedRenderOccluded, writeDepth: this._writeDepthEnabled };
  }
  _createObject3DResources(e20) {
    const t24 = new z3(this.materialParameters), r24 = new Array();
    return this._createObject3DGeometry(t24, e20, r24), { material: t24, geometries: r24, forEach: (e21) => {
      e21(t24), r24.forEach(e21);
    } };
  }
  _destroyExternalResources(e20) {
    e20.geometries = [], e20.material.dispose();
  }
  _recreateObject3DGeometry(e20, t24) {
    e20.geometries.length = 0, this._createObject3DGeometry(e20.material, t24, e20.geometries);
  }
  _createObject3DGeometry(e20, t24, r24) {
    const s19 = this._createGeometry(e20);
    r24.push(s19), t24.addGeometry(s19), this._updateVerticesObject3D(t24);
  }
  _createDrapedResources() {
    const e20 = new z3(this.materialParameters);
    return { material: e20, geometries: [this._createDrapedGeometry(e20)] };
  }
  _recreateDrapedGeometry(e20) {
    e20.geometries = [this._createDrapedGeometry(e20.material)];
  }
  _createDrapedGeometry(e20) {
    const t24 = this._createGeometry(e20);
    this._updateVerticesDraped(t24);
    const r24 = new h12(t24, { boundingInfo: t24.boundingInfo });
    return r24.computeBoundingSphere(r24.transformation, r24.boundingSphere), r24;
  }
  _createGeometry(e20) {
    const t24 = this.extensionType === W3.FADED, r24 = t24 ? [n4(), n4(), n4(), n4()] : [n4(), n4()];
    return ft(e20, r24, null, t24 ? [1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0] : null);
  }
  _updateGeometry() {
    if (this.isDraped) this.drapedResources.recreateGeometry();
    else {
      const e20 = f(this.object3dResources.object);
      e20 && this._updateVerticesObject3D(e20);
    }
  }
  _updateVerticesObject3D(e20) {
    const t24 = this._lineSegment;
    this._updateVertexAttributesObject3D(e20, t24), this._laserline.intersectsLine = t24;
  }
  _updateVerticesDraped(e20) {
    this._updateVertexAttributesDraped(e20, this._lineSegment);
  }
  get _lineSegment() {
    return this._extensionType === W3.FADED ? this._updateLineSegmentFinite(L) : this._updateLineSegmentInfinite(this._extensionType, L);
  }
  _updateLineSegmentFinite(e20) {
    return b5(this._start, this._end, e20);
  }
  _updateLineSegmentInfinite(e20, t24) {
    const s19 = this.view.state.camera;
    switch (y5(this._ray, V4), e20) {
      case W3.LINE:
        V4.c0 = -Number.MAX_VALUE;
        break;
      case W3.RAY:
      case W3.GROUND_RAY: {
        const e21 = this._ray.origin, t25 = l(this.view.elevationProvider.getElevation(e21[0], e21[1], e21[2], this.view.renderCoordsHelper.spatialReference, "ground"), 0), s20 = this.view.renderCoordsHelper.getAltitude(e21);
        this._isWorldDown && s20 < t25 && j2(V4.ray.direction, V4.ray.direction), this._extensionType === W3.GROUND_RAY && null != t25 && (V4.c1 = Math.abs(s20 - t25));
        break;
      }
    }
    if (!a14(s19.frustum, V4)) return this._updateLineSegmentFinite(t24);
    const i19 = p8(V4, M7), n21 = g7(V4, T3);
    return b5(i19, n21, t24);
  }
  _updateVertexAttributesObject3D(e20, t24) {
    var _a;
    const r24 = (_a = e20.geometries[0].getMutableAttribute(O3.POSITION)) == null ? void 0 : _a.data;
    if (!r24) return;
    let s19 = 0;
    for (const i19 of this._lineVertices(t24)) r24[s19++] = i19[0], r24[s19++] = i19[1], r24[s19++] = i19[2];
    e20.geometryVertexAttrsUpdated(e20.geometries[0]);
  }
  _updateVertexAttributesDraped(e20, t24) {
    var _a;
    const r24 = (_a = e20.getMutableAttribute(O3.POSITION)) == null ? void 0 : _a.data;
    if (!r24) return;
    let s19 = 0;
    for (const i19 of this._lineVertices(t24)) r24[s19++] = i19[0], r24[s19++] = i19[1], r24[s19++] = ce;
    e20.invalidateBoundingInfo();
  }
  *_lineVertices(e20) {
    this.extensionType === W3.FADED ? (yield l10(e20, -this.fadedExtensions.start, M7), yield l10(e20, 0, M7), yield l10(e20, 1, M7), yield l10(e20, 1 + this.fadedExtensions.end, M7)) : (yield l10(e20, 0, M7), yield l10(e20, 1, M7));
  }
};
var V4 = a13();
var M7 = n4();
var T3 = n4();
var L = v5();
var W3;
!function(e20) {
  e20[e20.LINE = 0] = "LINE", e20[e20.RAY = 1] = "RAY", e20[e20.GROUND_RAY = 2] = "GROUND_RAY", e20[e20.FADED = 3] = "FADED";
}(W3 || (W3 = {}));
var I2 = 1 / 3;
var F4 = { start: I2, end: I2 };

// node_modules/@arcgis/core/views/3d/interactive/visualElements/ParallelLineVisualElement.js
var P6 = class extends t16 {
  constructor(e20) {
    super(e20), this._location = n4(), this._direction = r3(1, 0, 0), this._width = 1, this._offset = 1, this._length = 18, this._color = r13(1, 0, 1, 1), this._renderOccluded = c3.OccludeAndTransparent, this.applyProps(e20);
  }
  createObject3DResourceFactory(e20) {
    return { view: e20, createResources: (e21) => this._createObject3DResources(e21), destroyResources: (e21) => this._destroyObject3DResources(e21), recreateGeometry: (e21, t24) => this._recreateObject3DGeometry(e21, t24), cameraChanged: () => this._updateGeometry() };
  }
  createDrapedResourceFactory(e20) {
    return { view: e20, createResources: () => this._createDrapedResources(), destroyResources: (e21) => this._destroyDrapedResources(e21), recreateGeometry: (e21) => this._recreateDrapedGeometry(e21) };
  }
  get location() {
    return this._location;
  }
  set location(e20) {
    F(this._location, e20) || (r4(this._location, e20), this._updateGeometry());
  }
  get direction() {
    return this._direction;
  }
  set direction(e20) {
    F(this._direction, e20) || (r4(this._direction, e20), this._updateGeometry());
  }
  setDirectionFromPoints(e20, t24) {
    z2(this._direction, e3(this._direction, t24, e20)), this._updateGeometry();
  }
  get width() {
    return this._width;
  }
  set width(e20) {
    e20 !== this._width && (this._width = e20, this._updateMaterial());
  }
  get offset() {
    return this._offset;
  }
  set offset(e20) {
    e20 !== this._offset && (this._offset = e20, this._updateGeometry());
  }
  get length() {
    return this._length;
  }
  set length(e20) {
    e20 !== this._length && (this._length = e20, this._updateGeometry());
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    D(e20, this._color) || (a4(this._color, e20), this._updateMaterial());
  }
  get renderOccluded() {
    return this._renderOccluded;
  }
  set renderOccluded(e20) {
    e20 !== this._renderOccluded && (this._renderOccluded = e20, this._updateMaterial());
  }
  _createObject3DResources(e20) {
    const t24 = new z3(this.materialParameters), r24 = new Array();
    return this._createObject3DGeometry(t24, e20, r24), { material: t24, geometries: r24, forEach: (e21) => {
      e21(t24), r24.forEach(e21);
    } };
  }
  _destroyObject3DResources(e20) {
    e20.geometries.length = 0, e20.material.dispose();
  }
  _recreateObject3DGeometry(e20, t24) {
    e20.geometries.length = 0, this._createObject3DGeometry(e20.material, t24, e20.geometries);
  }
  _createObject3DGeometry(e20, t24, r24) {
    const [s19, i19] = this._createGeometries(e20);
    t24.addGeometry(s19), t24.addGeometry(i19), r24.push(s19), r24.push(i19), this._updateVerticesObject3D(t24);
  }
  _createDrapedResources() {
    const e20 = new z3(this.materialParameters), t24 = l3(() => this.view.state.contentPixelRatio, () => {
      this.drapedResources.recreateGeometry();
    });
    return { material: e20, geometries: this._createDrapedGeometry(e20), pixelRatioHandle: t24 };
  }
  _destroyDrapedResources(e20) {
    e20.pixelRatioHandle.remove(), e20.geometries = [], e20.material.dispose();
  }
  _recreateDrapedGeometry(e20) {
    e20.geometries = this._createDrapedGeometry(e20.material);
  }
  _createDrapedGeometry(e20) {
    const t24 = this._createGeometries(e20);
    this._updateVerticesDraped(t24);
    const r24 = t24.map((e21) => new h12(e21, { boundingInfo: e21.boundingInfo }));
    for (const s19 of r24) s19.computeBoundingSphere(s19.transformation, s19.boundingSphere);
    return r24;
  }
  _createGeometries(e20) {
    return [ft(e20, [n4(), n4()]), ft(e20, [n4(), n4()])];
  }
  _updateMaterial() {
    var _a, _b;
    const { materialParameters: t24 } = this;
    (_a = f(this.object3dResources.resources)) == null ? void 0 : _a.material.setParameters(t24), (_b = f(this.drapedResources.resources)) == null ? void 0 : _b.material.setParameters(t24);
  }
  get materialParameters() {
    return { width: this._width, color: this._color, renderOccluded: this._renderOccluded };
  }
  _updateGeometry() {
    if (this.isDraped) this.drapedResources.recreateGeometry();
    else {
      const t24 = f(this.object3dResources.object);
      t24 && this._updateVerticesObject3D(t24);
    }
  }
  _updateVerticesObject3D(e20) {
    const t24 = this.view.state.camera;
    t24.projectToScreen(this.location, k4), u(A10, this.location, this.direction), t24.projectToScreen(A10, E12), v4(E12, o4(E12, E12, k4)), this._updateVertexAttributesObject3D(t24, e20, 0, k4, E12, 1), this._updateVertexAttributesObject3D(t24, e20, 1, k4, E12, -1);
  }
  _updateVertexAttributesObject3D(e20, t24, r24, i19, o17, a27) {
    var _a;
    const c21 = t24.geometries[r24], n21 = (_a = c21.getMutableAttribute(O3.POSITION)) == null ? void 0 : _a.data;
    if (!n21) return;
    const { start: d15, end: h25 } = this._computeStartEnd(o17, i19, a27, this.offset, this.width, this.length);
    e20.unprojectFromScreen(l4(d15), A10), n21[0] = A10[0], n21[1] = A10[1], n21[2] = A10[2], e20.unprojectFromScreen(l4(h25), A10), n21[3] = A10[0], n21[4] = A10[1], n21[5] = A10[2], t24.geometryVertexAttrsUpdated(c21);
  }
  _updateVerticesDraped(e20) {
    const { view: { basemapTerrain: { overlayManager: r24 }, state: { contentPixelRatio: s19 } } } = this, { location: i19, width: o17, length: a27, offset: c21 } = this, n21 = F5;
    n21.spatialReference = e2(r24.renderer.spatialReference), n21.x = i19[0], n21.y = i19[1];
    const d15 = r24.overlayPixelSizeInMapUnits(n21) * s19, h25 = o17 * d15, m17 = a27 * d15, u13 = c21 * d15;
    this._updateVertexAttributesDraped(e20[0], h25, m17, u13, -1), this._updateVertexAttributesDraped(e20[1], h25, m17, u13, 1);
  }
  _updateVertexAttributesDraped(e20, t24, r24, s19, i19) {
    var _a;
    const o17 = (_a = e20.getMutableAttribute(O3.POSITION)) == null ? void 0 : _a.data;
    if (!o17) return;
    const { location: a27, direction: c21 } = this, { start: n21, end: d15 } = this._computeStartEnd(c21, a27, i19, s19, t24, r24);
    o17[0] = n21[0], o17[1] = n21[1], o17[2] = ce, o17[3] = d15[0], o17[4] = d15[1], o17[5] = ce, e20.invalidateBoundingInfo();
  }
  _computeStartEnd(e20, t24, r24, s19, i19, o17) {
    const a27 = l6(S7, r8(S7, e20[1] * r24, e20[0] * -r24), s19 + i19 / 2), h25 = s4(M8, s4(M8, s4(M8, t24, l6(M8, e20, o17 / 2)), a27), a27);
    return { start: h25, end: s4(I3, h25, l6(I3, e20, -o17)) };
  }
};
var A10 = n4();
var S7 = n9();
var M8 = n9();
var I3 = n9();
var k4 = i3();
var E12 = i3();
var F5 = M2(0, 0, void 0, null);

// node_modules/@arcgis/core/views/3d/interactive/visualElements/PointVisualElement.js
var S8 = class {
  constructor(e20) {
    this.view = null, this._geometry = null, this._size = 3, this._color = r6(1, 0, 1, 1), this._pixelSnappingEnabled = true, this._primitive = "square", this._outlineSize = 1, this._outlineColor = r6(1, 1, 1, 1), this._elevationInfo = null, this._resources = new u7({ view: e20.view, createResources: (e21) => this._createResources(e21), destroyResources: (e21) => this._destroyResources(e21), recreateGeometry: (e21, t25) => {
      e21.geometry = this._recreateGeometry(t25, e21.material);
    } });
    let t24 = true;
    for (const r24 in e20) r24 in this ? "attached" === r24 ? t24 = e20[r24] ?? false : this[r24] = e20[r24] : console.error("Cannot set unknown property", r24);
    this.attached = t24;
  }
  destroy() {
    this._resources.destroy();
  }
  get visible() {
    return this._resources.visible;
  }
  set visible(e20) {
    this._resources.visible = e20;
  }
  get attached() {
    return this._resources.attached;
  }
  set attached(e20) {
    this._resources.attached = e20;
  }
  get geometry() {
    return this._geometry;
  }
  set geometry(e20) {
    this._geometry = e20, this._resources.recreateGeometry();
  }
  get size() {
    return this._size;
  }
  set size(e20) {
    if (e20 !== this._size) {
      const t24 = this._preferredTextureSize;
      this._size = e20, t24 < this._preferredTextureSize ? r(this._resources) && this._resources.recreate() : this._updateSizeAttribute();
    }
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    D(e20, this._color) || (a4(this._color, e20), this._updateMaterial());
  }
  get pixelSnappingEnabled() {
    return this._pixelSnappingEnabled;
  }
  set pixelSnappingEnabled(e20) {
    this._pixelSnappingEnabled !== e20 && (this._pixelSnappingEnabled = e20, this._updateMaterial());
  }
  get primitive() {
    return this._primitive;
  }
  set primitive(e20) {
    this._primitive !== e20 && (this._primitive = e20, r(this._resources) && this._resources.recreate());
  }
  get outlineSize() {
    return this._outlineSize;
  }
  set outlineSize(e20) {
    e20 !== this._outlineSize && (this._outlineSize = e20, this._updateMaterial());
  }
  get outlineColor() {
    return this._outlineColor;
  }
  set outlineColor(e20) {
    D(e20, this._outlineColor) || (a4(this._outlineColor, e20), this._updateMaterial());
  }
  get elevationInfo() {
    return this._elevationInfo;
  }
  set elevationInfo(e20) {
    this._elevationInfo = e20, this._resources && this._resources.recreateGeometry();
  }
  _updateMaterial() {
    const e20 = this._resources.resources;
    t(e20) || e20.material.setParameters(this._materialParameters(e20.texture.id));
  }
  _updateSizeAttribute() {
    const e20 = this._resources.resources, t24 = this._resources.object;
    if (t(e20) || t(t24)) return;
    const r24 = e20.geometry;
    if (t(r24)) return;
    const s19 = r24.getMutableAttribute(O3.SIZE).data, o17 = this._geometrySize;
    s19[0] = o17, s19[1] = o17, t24.geometryVertexAttrsUpdated(t24.geometries[0]);
  }
  _materialParameters(e20) {
    return { color: this._color, textureIsSignedDistanceField: true, distanceFieldBoundingBox: x12, occlusionTest: false, outlineColor: this._outlineColor, outlineSize: this._outlineSize, textureId: e20, polygonOffset: false, shaderPolygonOffset: 0, drawInSecondSlot: true, depthEnabled: false, pixelSnappingEnabled: this.pixelSnappingEnabled };
  }
  get _geometrySize() {
    return this._size / b9;
  }
  _recreateGeometry(e20, t24) {
    const i19 = this._createRenderGeometry(t24);
    return r(i19) && e20.addGeometry(i19), i19;
  }
  _createResources(e20) {
    const t24 = o12(this._primitive, this._preferredTextureSize), i19 = new $3(this._materialParameters(t24.id)), s19 = this._recreateGeometry(e20, i19);
    return { material: i19, texture: t24, geometry: s19, forEach(e21) {
      e21(t24), e21(i19), r(this.geometry) && e21(this.geometry);
    } };
  }
  _destroyResources(e20) {
    e20.geometry = null, e20.material.dispose(), e20.texture.dispose();
  }
  get _preferredTextureSize() {
    return a5(i2(2 * this._geometrySize), 16, 128);
  }
  calculateMapBounds(e20) {
    if (t(this._resources.resources) || t(this._resources.resources.geometry)) return false;
    const t24 = this._resources.resources.geometry.vertexAttributes.get(O3.POSITION);
    return jn(t24.data, this.view.renderCoordsHelper.spatialReference, z4, this.view.spatialReference), M(e20, z4), true;
  }
  _createRenderGeometry(e20) {
    const t24 = this.geometry;
    if (t(t24)) return null;
    const { renderCoordsHelper: r24, elevationProvider: o17 } = this.view, n21 = d7(t24, o17, h13.fromElevationInfo(this.elevationInfo), r24), a27 = o3(c2.get(), t24.x, t24.y, n21), l22 = c2.get();
    jn(a27, t24.spatialReference, l22, r24.spatialReference);
    const c21 = this._geometrySize;
    return ot(e20, null, l22, null, [c21, c21], [0, 0, 0, 1]);
  }
};
var b9 = a18;
var x12 = [b9 / 2, b9 / 2, 1 - b9 / 2, 1 - b9 / 2];
var z4 = n4();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/RightAngleQuadVisualElement.js
var S9 = class extends t16 {
  constructor(e20) {
    super(e20), this._maxSize = 0, this._position = n4(), this._up = n4(), this._right = n4(), this._renderOccluded = c3.OccludeAndTransparent, this._color = r6(1, 0, 0, 1), this._outlineColor = r6(0, 0, 0, 1), this._outlineSize = 0, this._size = 32, this._outlineRenderOccluded = c3.Opaque, this.applyProps(e20);
  }
  createObject3DResourceFactory(e20) {
    return { view: e20, createResources: (e21) => this._createObject3DResources(e21), destroyResources: (e21) => this._destroyObject3DResources(e21), cameraChanged: () => this._updateTransformObject3D() };
  }
  createDrapedResourceFactory(e20) {
    return { view: e20, createResources: () => this._createDrapedResources(), destroyResources: (e21) => this._destroyDrapedResources(e21) };
  }
  get renderOccluded() {
    return this._renderOccluded;
  }
  set renderOccluded(e20) {
    e20 !== this._renderOccluded && (this._renderOccluded = e20, this._updateQuadMaterial());
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    a4(this._color, e20), this._updateQuadMaterial();
  }
  get outlineColor() {
    return this._outlineColor;
  }
  set outlineColor(e20) {
    a4(this._outlineColor, e20), this._updateOutlineMaterial();
  }
  get outlineSize() {
    return this._outlineSize;
  }
  set outlineSize(e20) {
    const t24 = 0 === this._outlineSize != (0 === e20);
    this._outlineSize = e20, t24 ? this.recreateGeometry() : this._updateOutlineMaterial();
  }
  get size() {
    return this._size;
  }
  set size(e20) {
    e20 !== this._size && (this._size = e20, this._updateTransform());
  }
  get outlineRenderOccluded() {
    return this._outlineRenderOccluded;
  }
  set outlineRenderOccluded(e20) {
    this._outlineRenderOccluded = e20, this._updateOutlineMaterial();
  }
  set geometry({ previous: e20, center: t24, next: r24 }) {
    this._maxSize = Math.min(x(t24, e20), x(t24, r24)) / 3, z2(this._up, e3(this._up, e20, t24)), z2(this._right, e3(this._right, r24, t24)), r4(this._position, t24), this.recreateGeometry();
  }
  _createObject3DResources(e20) {
    const t24 = new f13(this._quadMaterialParameters), r24 = 0 === this._outlineSize ? void 0 : new z3(this._outlineMaterialParameters);
    return this._createObject3DGeometries(e20, t24, r24), { quadMaterial: t24, outlineMaterial: r24, forEach: (e21) => {
      e21(t24), r24 && e21(r24);
    } };
  }
  _destroyObject3DResources(e20) {
    var _a;
    e20.quadMaterial.dispose(), (_a = e20.outlineMaterial) == null ? void 0 : _a.dispose();
  }
  _createObject3DGeometries(e20, t24, r24) {
    if (F(this._up, f4) && F(this._right, f4)) return;
    const i19 = this._createGeometries(t24, r24);
    for (const s19 of i19) e20.addGeometry(s19);
    this._updateTransformObject3D(e20);
  }
  _createDrapedResources() {
    const e20 = new f13(this._quadMaterialParameters), t24 = 0 === this._outlineSize ? void 0 : new z3(this._outlineMaterialParameters), i19 = this._createGeometries(e20, t24).map((e21) => new h12(e21, { boundingInfo: e21.boundingInfo }));
    this._setTransformDraped(i19);
    return { quadMaterial: e20, outlineMaterial: t24, geometries: i19, pixelRatioHandle: l3(() => this.view.state.contentPixelRatio, () => {
      this.drapedResources.recreateGeometry();
    }) };
  }
  _destroyDrapedResources(e20) {
    var _a;
    e20.pixelRatioHandle.remove(), e20.geometries = [], (_a = e20.outlineMaterial) == null ? void 0 : _a.dispose(), e20.quadMaterial.dispose();
  }
  _createGeometries(e20, t24) {
    const { up: r24, right: i19, corner: s19 } = this._getVertices(), a27 = this._quadGeometryData(r24, i19, s19, e20);
    if (!t24) return [a27];
    return [a27, ft(t24, [r24, s19, i19])];
  }
  _getVertices() {
    let e20 = this._up, t24 = this._right;
    const r24 = u(c2.get(), e20, t24);
    return this.isDraped && (e20 = r4(c2.get(), e20), t24 = r4(c2.get(), t24), e20[2] = 0, t24[2] = 0, r24[2] = 0), { up: e20, right: t24, corner: r24 };
  }
  _updateTransform() {
    this.isDraped ? this.drapedResources.recreateGeometry() : this._updateTransformObject3D();
  }
  _updateTransformObject3D(t24 = f(this.object3dResources.object)) {
    if (!t24) return;
    const r24 = this.view.state.camera, a27 = this._size * r24.computeScreenPixelSizeAt(this._position), o17 = Math.min(this._maxSize, a27);
    q2(x13, this._position), f6(x13, x13, [o17, o17, o17]), t24.transformation = x13;
  }
  _setTransformDraped(e20) {
    if (0 === e20.length) return;
    const { view: { basemapTerrain: { overlayManager: r24 }, state: { contentPixelRatio: o17 } } } = this, { _position: n21, _size: u13 } = this, c21 = r4(c2.get(), n21);
    c21[2] = ce;
    const d15 = G5;
    d15.spatialReference = e2(r24.renderer.spatialReference), d15.x = c21[0], d15.y = c21[1];
    const h25 = u13 * (r24.overlayPixelSizeInMapUnits(d15) * o17), m17 = Math.min(this._maxSize, h25);
    q2(x13, c21), f6(x13, x13, [m17, m17, 1]);
    for (const t24 of e20) t24.updateTransformation((e21) => {
      n5(e21, x13);
    });
  }
  _quadGeometryData(e20, t24, r24, i19) {
    return new v6(i19, [[O3.POSITION, new s6([0, 0, 0, ...t24, ...e20, ...r24], 3, true)]], [[O3.POSITION, [0, 1, 2, 1, 2, 3]]]);
  }
  get _quadMaterialParameters() {
    return { color: this._color, transparent: true, writeDepth: false, polygonOffset: true, renderOccluded: this._renderOccluded };
  }
  _updateQuadMaterial() {
    var _a, _b;
    (_a = f(this.object3dResources.resources)) == null ? void 0 : _a.quadMaterial.setParameters(this._quadMaterialParameters), (_b = f(this.drapedResources.resources)) == null ? void 0 : _b.quadMaterial.setParameters(this._quadMaterialParameters);
  }
  get _outlineMaterialParameters() {
    return { color: this._outlineColor, width: this._outlineSize, renderOccluded: this._outlineRenderOccluded };
  }
  _updateOutlineMaterial() {
    var _a, _b, _c, _d;
    (_b = (_a = f(this.object3dResources.resources)) == null ? void 0 : _a.outlineMaterial) == null ? void 0 : _b.setParameters(this._outlineMaterialParameters), (_d = (_c = f(this.drapedResources.resources)) == null ? void 0 : _c.outlineMaterial) == null ? void 0 : _d.setParameters(this._outlineMaterialParameters);
  }
};
var x13 = e6();
var G5 = M2(0, 0, void 0, null);

// node_modules/@arcgis/core/views/3d/interactive/SnappingVisualizer3D.js
var x14 = class extends r14 {
  sortUniqueHints(e20) {
    return e20.sort((e21, n21) => (n21 instanceof n11 ? n21.length : 0) - (e21 instanceof n11 ? e21.length : 0));
  }
  visualizeIntersectionPoint(i19, t24) {
    const { spatialReference: r24, view: a27 } = t24;
    return t2(new S8({ view: a27, primitive: "circle", geometry: p11(i19.intersectionPoint, r24), elevationInfo: i19.isDraped ? Z2 : E7, size: 20, outlineSize: 2, color: [0, 0, 0, 0], outlineColor: l8.toUnitRGBA(p9.orange), pixelSnappingEnabled: false }));
  }
  visualizePoint(i19, t24) {
    const { view: r24, spatialReference: a27 } = t24, o17 = this._alignPoint(i19.point, i19.domain, t24);
    return t2(new S8({ view: r24, primitive: "circle", geometry: p11(o17, a27), elevationInfo: this._hintElevationInfo(i19, t24), size: 20, outlineSize: 2, color: [0, 0, 0, 0], outlineColor: l8.toUnitRGBA(p9.orange), pixelSnappingEnabled: false }));
  }
  visualizeLine(e20, i19) {
    const { view: t24, spatialReference: r24 } = i19, a27 = this._alignPoint(e20.lineStart, e20.domain, i19), o17 = this._alignPoint(e20.lineEnd, e20.domain, i19);
    return t2(this._createLineSegmentHint(e20.type, a27, o17, r24, this._hintElevationInfo(e20, i19), t24, e20.isDraped, e20.fadeLeft, e20.fadeRight));
  }
  visualizeParallelSign(i19, t24) {
    const { view: r24, spatialReference: o17 } = t24, { isDraped: s19 } = i19, l22 = this._hintElevationInfo(i19, t24), p25 = this._alignPoint(i19.lineStart, i19.domain, t24), d15 = this._alignPoint(i19.lineEnd, i19.domain, t24), c21 = A11(p25, o17, l22, r24, s19), g16 = A11(d15, o17, l22, r24, s19), u13 = A2(g16, c21, g16, 0.5), E15 = new P6({ view: r24, attached: false, offset: p9.parallelLineHintOffset, length: p9.parallelLineHintLength, width: p9.parallelLineHintWidth, color: l8.toUnitRGBA(p9.orange), location: u13, renderOccluded: s19 ? c3.OccludeAndTransparent : c3.Opaque, isDraped: s19, renderGroup: a17.SnappingHint });
    return E15.setDirectionFromPoints(c21, u13), E15.attached = true, t2(E15);
  }
  visualizeRightAngleQuad(i19, t24) {
    const { view: r24, spatialReference: a27 } = t24, o17 = this._hintElevationInfo(i19, t24), { isDraped: s19 } = i19, l22 = this._alignPoint(i19.previousVertex, i19.domain, t24), p25 = this._alignPoint(i19.centerVertex, i19.domain, t24), d15 = this._alignPoint(i19.nextVertex, i19.domain, t24), c21 = A11(l22, a27, o17, r24, s19), m17 = A11(p25, a27, o17, r24, s19), g16 = A11(d15, a27, o17, r24, s19);
    return t2(new S9({ view: r24, attached: true, color: s19 ? l8.toUnitRGBA(p9.orangeTransparent) : l8.toUnitRGBA(p9.orange), renderOccluded: s19 ? c3.OccludeAndTransparent : c3.Transparent, outlineRenderOccluded: s19 ? c3.OccludeAndTransparent : c3.Opaque, outlineColor: l8.toUnitRGBA(p9.orange), outlineSize: p9.rightAngleHintOutlineSize, size: p9.rightAngleHintSize, isDraped: s19, geometry: { previous: c21, center: m17, next: g16 }, renderGroup: a17.SnappingHint }));
  }
  _createLineSegmentHint(n21, i19, t24, r24, a27, o17, s19 = false, l22 = true, p25 = true) {
    const m17 = A11(i19, r24, a27, o17, s19), g16 = A11(t24, r24, a27, o17, s19), u13 = new v8({ view: o17, extensionType: W3.FADED, start: m17, end: g16, isDraped: s19, color: l8.toUnitRGBA(p9.orange), renderOccluded: s19 ? c3.OccludeAndTransparent : c3.Opaque, renderGroup: a17.SnappingHint });
    switch (n21) {
      case u6.TARGET:
        u13.width = p9.lineHintWidthTarget, u13.fadedExtensions = { start: 0, end: p9.lineHintFadedExtensions };
        break;
      case u6.REFERENCE_EXTENSION:
        u13.width = p9.lineHintWidthReference, u13.fadedExtensions = { start: 0, end: 0 };
        break;
      case u6.REFERENCE:
        u13.width = p9.lineHintWidthReference, u13.fadedExtensions = { start: l22 ? p9.lineHintFadedExtensions : 0, end: p25 ? p9.lineHintFadedExtensions : 0 };
    }
    return u13.attached = true, u13;
  }
  _alignPoint(e20, n21, t24) {
    const r24 = this._getSelfSnappingZ(n21, t24);
    return t(r24) ? e20 : s12(e20[0], e20[1], r24);
  }
  _hintElevationInfo(e20, n21) {
    return r(this._getSelfSnappingZ(e20.domain, n21)) ? e2(n21.selfSnappingZ).elevationInfo : e20.isDraped ? Z2 : E7;
  }
  _getSelfSnappingZ(e20, { selfSnappingZ: n21 }) {
    return e20 === E8.SELF && r(n21) ? n21.value : null;
  }
};
function A11(e20, n21, i19, t24, r24, a27 = n4()) {
  if (r24) {
    const i20 = t24.basemapTerrain.overlayManager.renderer.spatialReference;
    jn(e20, n21, a27, i20);
  } else f12(e20, n21, i19, t24, a27);
  return a27;
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/settings.js
var r18 = { main: new l8([255, 127, 0]), selected: new l8([255, 255, 255]), staged: new l8([12, 207, 255]), outline: new l8([0, 0, 0, 0.5]), selectedOutline: new l8([255, 255, 255]) };
var h17 = 0.3;
function c13(t24, e20) {
  const i19 = t24.clone();
  return i19.a *= e20, i19;
}
function a22(t24, l22) {
  const o17 = t24.clone(), s19 = y4(o17);
  s19.s *= l22;
  const n21 = p7(s19);
  return o17.r = n21.r, o17.g = n21.g, o17.b = n21.b, o17;
}
function d10(t24, e20) {
  if (e20) for (const i19 in e20) t24[i19] = e20[i19];
}
var u8 = class {
  constructor(t24) {
    this.color = r18.main, this.height = 90, this.coneHeight = 40, this.coneWidth = 23, this.width = 3, this.renderOccluded = c3.Opaque, d10(this, t24);
  }
};
var p18 = class {
  constructor(t24) {
    this.size = 11, this.outlineSize = 1, this.collisionPadding = 9, this.color = r18.main, this.outlineColor = r18.outline, this.renderOccluded = c3.Opaque, this.hoverOutlineColor = r18.selectedOutline, d10(this, t24);
  }
  apply(t24, e20) {
    const i19 = this[t24];
    e20.setParameters({ color: P7(i19), transparent: "color" !== t24 || i19.a < 1, renderOccluded: this.renderOccluded });
  }
};
var g10 = class {
  constructor(t24) {
    this.size = 40, this.height = 0.2, this.offset = 0.25, this.collisionPadding = 2, this.color = c13(r18.main, 0.5), this.hoverColor = r18.main, this.renderOccluded = c3.Transparent, this.minSquaredEdgeLength = 900, d10(this, t24);
  }
  apply(t24, e20) {
    const i19 = this[t24];
    e20.setParameters({ color: P7(i19), transparent: i19.a < 1, renderOccluded: this.renderOccluded });
  }
};
var f15 = class {
  constructor(t24) {
    this.vertex = new p18({ color: r18.main, outlineColor: r18.outline }), this.edge = new p18({ color: a22(c13(r18.main, 2 / 3), 0.5), outlineColor: c13(r18.outline, 0.5), size: 8, collisionPadding: 8 }), this.selected = new p18({ color: r18.selected, outlineColor: r18.outline }), this.edgeOffset = new g10(), d10(this, t24);
  }
};
var w8 = class {
  constructor(t24) {
    this.color = r18.selected, this.width = 1.5, this.stipplePattern = r15(5), this.stippleOffColor = r18.outline, this.falloff = 0, this.innerWidth = 1.5, this.innerColor = r18.selected, this.renderOccluded = c3.OccludeAndTransparent, d10(this, t24);
  }
  apply(t24) {
    t24.color = P7(this.color), t24.width = this.width, t24.stipplePattern = this.stipplePattern, t24.stippleOffColor = P7(this.stippleOffColor), t24.falloff = this.falloff, t24.innerWidth = this.innerWidth, t24.innerColor = P7(this.innerColor), t24.renderOccluded = this.renderOccluded;
  }
};
var C5 = class {
  constructor(t24) {
    this.color = r18.selected, this.size = 4, this.outlineSize = 1, this.outlineColor = r18.outline, this.shape = "square", d10(this, t24);
  }
  apply(t24) {
    t24.color = P7(this.color), t24.size = this.size, t24.outlineSize = this.outlineSize, t24.outlineColor = P7(this.outlineColor), t24.primitive = this.shape;
  }
};
var m10 = class {
  constructor(t24) {
    this.innerColor = r18.selected, this.innerWidth = 1, this.glowColor = r18.main, this.glowWidth = 8, this.glowFalloff = 8, this.globalAlpha = h17, this.globalAlphaContrastBoost = 1.5, this.radius = 3, this.heightFillColor = r18.main, d10(this, t24);
  }
  apply(e20, i19 = 1) {
    const l22 = { glowColor: l8.toUnitRGB(this.glowColor), glowFalloff: this.glowFalloff, glowWidth: this.glowWidth, innerColor: l8.toUnitRGB(this.innerColor), innerWidth: this.innerWidth, globalAlpha: this.globalAlpha * i19, globalAlphaContrastBoost: this.globalAlphaContrastBoost, intersectsLineRadius: this.radius };
    "style" in e20 ? e20.style = l22 : e20.laserlineStyle = l22;
  }
};
var O4 = class {
  constructor(t24) {
    this.outline = new w8({ color: r18.outline, renderOccluded: c3.OccludeAndTransparentStencil, stippleOffColor: r18.selected, stipplePattern: r15(5), width: 1.5, innerWidth: 0 }), this.staged = new w8({ color: r18.selected, renderOccluded: c3.OccludeAndTransparentStencil, innerColor: r18.staged, stippleOffColor: r18.outline, stipplePattern: r15(5), width: 1.5 }), this.shadowStyle = new m10({ globalAlpha: h17, glowColor: r18.main, glowFalloff: 8, glowWidth: 8, innerColor: r18.selected, innerWidth: 1 }), d10(this, t24);
  }
};
var A12 = class {
  constructor(t24) {
    this.outline = new C5({ color: r18.selected, outlineColor: r18.outline, outlineSize: 1, shape: "circle", size: 4 }), this.shadowStyle = new m10({ globalAlpha: h17, glowColor: r18.main, glowFalloff: 1.5, glowWidth: 6, innerColor: r18.selected, innerWidth: 1, radius: 2 }), d10(this, t24);
  }
};
var W4 = class extends w8 {
  constructor(t24) {
    super(), this.extensionType = W3.GROUND_RAY, d10(this, t24);
  }
};
var b10 = class {
  constructor(t24) {
    this.lineGraphics = new O4(), this.pointGraphics = new A12(), this.heightPlane = new m10({ globalAlpha: h17, glowColor: r18.main, glowFalloff: 8, glowWidth: 8, innerColor: r18.selected, innerWidth: 1 }), this.heightBox = new m10({ globalAlpha: h17, glowColor: r18.main, glowFalloff: 8, glowWidth: 8, innerColor: r18.selected, innerWidth: 0, heightFillColor: r18.main }), this.zVerticalLine = new W4({ color: c13(r18.main, 5 * h17 / 3), falloff: 2, innerColor: c13(r18.selected, 0), renderOccluded: c3.OccludeAndTransparent, stipplePattern: null, width: 5, extensionType: W3.GROUND_RAY }), this.laserlineAlphaMultiplier = 1.5, this.heightPlaneAngleCutoff = 20, d10(this, t24);
  }
};
var z5 = class {
  constructor(t24) {
    this.visualElements = new b10(), this.reshapeManipulators = new f15(), this.zManipulator = new u8(), d10(this, t24);
  }
  colorToVec4(t24) {
    return P7(t24);
  }
};
function P7(e20) {
  return e4(l8.toUnitRGBA(e20));
}
var y7 = new z5();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/OutlineVisualElement.js
var I4 = class extends t16 {
  constructor(t24) {
    super(t24), this._attachmentOrigin = M2(0, 0, 0, null), this._attachmentOriginDirty = true, this.events = new n3(), this._geometry = null, this._width = 1, this._color = r13(1, 0, 1, 1), this._innerWidth = 0, this._innerColor = r13(1, 1, 1, 1), this._stipplePattern = null, this._stippleOffColor = null, this._falloff = 0, this._elevationInfo = null, this._laserlineStyle = null, this._laserlineEnabled = false, this._renderOccluded = c3.OccludeAndTransparentStencil, this._attachmentOrigin.spatialReference = t24.view.spatialReference, this._laserline = new d9({ view: t24.view }), this.applyProps(t24), this.attached = t24.attached ?? true;
  }
  destroy() {
    this._laserline.destroy(), super.destroy();
  }
  createObject3DResourceFactory(e20) {
    return { view: e20, createResources: (e21) => this._createObject3DResources(e21), destroyResources: (e21) => this._destroyExternalResources(e21), recreateGeometry: (e21, t24) => {
      e21.geometries.length = 0, this._recreateGeometry(t24, e21.material, e21.geometries);
    } };
  }
  createDrapedResourceFactory(e20) {
    return { view: e20, createResources: () => this._createDrapedResources(), destroyResources: (e21) => this._destroyExternalResources(e21), recreateGeometry: (e21) => {
      e21.geometries = this._createRenderGeometriesDraped(e21.material), this._attachmentOriginChanged();
    } };
  }
  get _laserlineAttached() {
    return this.attached && this.visible && r(this._laserlineStyle) && !this.isDraped && this.laserlineEnabled;
  }
  onAttachedChange(e20) {
    this._laserline.attached = this._laserlineAttached, e20 && this._attachmentOriginChanged();
  }
  get geometry() {
    return this._geometry;
  }
  set geometry(e20) {
    this._geometry = e20, this.recreateGeometry();
  }
  get width() {
    return this._width;
  }
  set width(e20) {
    e20 !== this._width && (this._width = e20, this._updateMaterial());
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    D(e20, this._color) || (a4(this._color, e20), this._updateMaterial());
  }
  get innerWidth() {
    return this._innerWidth;
  }
  set innerWidth(e20) {
    e20 !== this._innerWidth && (this._innerWidth = e20, this._updateMaterial());
  }
  get innerColor() {
    return this._innerColor;
  }
  set innerColor(e20) {
    D(e20, this._innerColor) || (a4(this._innerColor, e20), this._updateMaterial());
  }
  get stipplePattern() {
    return this._stipplePattern;
  }
  set stipplePattern(e20) {
    const r24 = r(e20) !== r(this._stipplePattern);
    this._stipplePattern = e20, r24 ? this.recreate() : this._updateMaterial();
  }
  get stippleOffColor() {
    return this._stippleOffColor;
  }
  set stippleOffColor(e20) {
    e20 && this._stippleOffColor && D(e20, this._stippleOffColor) || (this._stippleOffColor = e20 ? t12(e20) : null, this._updateMaterial());
  }
  get falloff() {
    return this._falloff;
  }
  set falloff(e20) {
    e20 !== this._falloff && (this._falloff = e20, this._updateMaterial());
  }
  get elevationInfo() {
    return this._elevationInfo;
  }
  set elevationInfo(e20) {
    this._elevationInfo = e20, this.recreateGeometry();
  }
  get laserlineStyle() {
    return this._laserlineStyle;
  }
  set laserlineStyle(e20) {
    this._laserlineStyle = e20, this._laserline.attached = this._laserlineAttached, r(e20) && (this._laserline.style = e20);
  }
  get laserlineEnabled() {
    return this._laserlineEnabled;
  }
  set laserlineEnabled(e20) {
    this._laserlineEnabled !== e20 && (this._laserlineEnabled = e20, this._laserline.attached = this._laserlineAttached);
  }
  get renderOccluded() {
    return this._renderOccluded;
  }
  set renderOccluded(e20) {
    e20 !== this._renderOccluded && (this._renderOccluded = e20, this._updateMaterial());
  }
  get attachmentOrigin() {
    var _a;
    if (!this._attachmentOriginDirty) return this._attachmentOrigin;
    const e20 = (_a = f(this.object3dResources.resources)) == null ? void 0 : _a.geometries;
    if (!e20 || 0 === e20.length) return null;
    o3(x15, 0, 0, 0);
    let t24 = 0;
    for (const r24 of e20) r24.computeAttachmentOrigin(S10) && (u(x15, x15, S10), t24++);
    return 0 === t24 ? null : (g(x15, x15, 1 / t24), this.view.renderCoordsHelper.fromRenderCoords(x15, this._attachmentOrigin), this._attachmentOriginDirty = false, this._attachmentOrigin);
  }
  _updateMaterial() {
    r(this.object3dResources.resources) && this.object3dResources.resources.material.setParameters(this._materialParameters), r(this.drapedResources.resources) && this.drapedResources.resources.material.setParameters(this._materialParameters);
  }
  get _isClosed() {
    return r(this.geometry) && "polygon" === this.geometry.type;
  }
  get _materialParameters() {
    return { width: this._width, color: this._color, stippleOffColor: this._stippleOffColor, stipplePattern: this._stipplePattern, stipplePreferContinuous: false, isClosed: this._isClosed, falloff: this._falloff, innerColor: this._innerColor, innerWidth: this._innerWidth, join: "round", hasPolygonOffset: true, renderOccluded: this._normalizedRenderOccluded };
  }
  get _normalizedRenderOccluded() {
    return this.isDraped && this._renderOccluded === c3.OccludeAndTransparentStencil ? c3.OccludeAndTransparent : this._renderOccluded;
  }
  _recreateGeometry(e20, t24, r24) {
    this._createRenderGeometries(t24, r24);
    for (const i19 of r24) e20.addGeometry(i19);
    this._attachmentOriginChanged();
  }
  _attachmentOriginChanged() {
    this._attachmentOriginDirty = true, this.events.emit("attachment-origin-changed");
  }
  _destroyExternalResources(e20) {
    e20.geometries = [], e20.material.dispose();
  }
  _createObject3DResources(e20) {
    const t24 = new z3(this._materialParameters), r24 = new Array();
    return this._recreateGeometry(e20, t24, r24), { material: t24, geometries: r24, forEach: (e21) => {
      e21(t24), r24.forEach(e21);
    } };
  }
  _createDrapedResources() {
    const e20 = new z3(this._materialParameters);
    return { material: e20, geometries: this._createRenderGeometriesDraped(e20) };
  }
  _createRenderGeometriesDraped(e20) {
    const t24 = this.geometry;
    if (t(t24) || t(this.view.basemapTerrain.spatialReference)) return [];
    const r24 = c10(t24, this.view.basemapTerrain.spatialReference), s19 = [];
    for (const { position: i19 } of r24.lines) {
      const t25 = { overlayInfo: { spatialReference: this.view.basemapTerrain.spatialReference, renderCoordsHelper: this.view.renderCoordsHelper }, attributeData: { position: i19 }, removeDuplicateStartEnd: this._isClosed }, r25 = new h12(b7(e20, t25)), a27 = S2(M9);
      M(a27, i19), r5(r25.boundingSphere, 0.5 * (a27[0] + a27[3]), 0.5 * (a27[1] + a27[4]), 0, 0.5 * Math.sqrt((a27[3] - a27[0]) * (a27[3] - a27[0]) + (a27[4] - a27[1]) * (a27[4] - a27[1]))), s19.push(r25);
    }
    return s19;
  }
  calculateMapBounds(e20) {
    if (t(this.object3dResources.resources)) return false;
    const t24 = this.view.renderCoordsHelper;
    for (const r24 of this.object3dResources.resources.geometries) {
      const i19 = r24.vertexAttributes.get(O3.POSITION), s19 = n7(i19.data.length);
      xn(i19.data, t24.spatialReference, 0, s19, this.view.spatialReference, 0, i19.data.length / 3), M(e20, s19);
    }
    return true;
  }
  _createRenderGeometries(e20, t24) {
    const r24 = this.geometry;
    if (t(r24)) return;
    const s19 = p13(r24, this.view.elevationProvider, this.view.renderCoordsHelper, h13.fromElevationInfo(this.elevationInfo ?? new x4({ mode: i7(r24, null) }))), a27 = new Array();
    for (const { position: i19, mapPositions: n21 } of s19.lines) {
      const r25 = { mapPositions: n21, attributeData: { position: i19 }, removeDuplicateStartEnd: this._isClosed };
      t24.push(b7(e20, r25)), a27.push(i19);
    }
    this._laserline.pathVerticalPlane = a27;
  }
};
var M9 = a6();
var S10 = n4();
var x15 = n4();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/Object3DVisualElement.js
var n15 = class extends t15 {
  constructor(e20) {
    super(e20.view), this._resources = null, this._transform = e6();
  }
  get object() {
    return r(this._resources) ? this._resources.object : null;
  }
  get transform() {
    return this._transform;
  }
  set transform(s19) {
    n5(this._transform, s19), r(this._resources) && (this._resources.object.transformation = this._transform);
  }
  recreate() {
    this.attached && this.createResources();
  }
  recreateGeometry() {
    if (t(this._resources)) return;
    const e20 = this._resources.object, r24 = this.view._stage;
    r24.removeMany(e20.geometries), e20.removeAllGeometries(), this.createGeometries(e20), e20.visible = this.visible, r24.addMany(e20.geometries);
  }
  createResources() {
    this.destroyResources();
    const e20 = this.view._stage;
    if (!e20) return;
    const s19 = new l15({ pickable: false, updatePolicy: C2.SYNC });
    e20.add(s19);
    const r24 = new x10({ castShadow: false });
    r24.transformation = this._transform, this.createExternalResources(), this.createGeometries(r24), e20.addMany(r24.geometries), this.forEachExternalMaterial((s20) => e20.add(s20)), e20.add(r24), s19.add(r24), r24.visible = this.visible, this._resources = { layer: s19, object: r24 };
  }
  destroyResources() {
    const e20 = this.view._stage;
    !t(this._resources) && e20 && (e20.remove(this._resources.object), e20.remove(this._resources.layer), this.forEachExternalMaterial((s19) => {
      e20.remove(s19), s19.dispose();
    }), e20.removeMany(this._resources.object.geometries), this._resources.object.dispose(), this.destroyExternalResources(), this._resources = null);
  }
  updateVisibility(e20) {
    t(this._resources) || (this._resources.object.visible = e20);
  }
};

// node_modules/@arcgis/core/views/3d/support/renderInfoUtils/point.js
function t17(t24, o17, i19, l22, r24) {
  const s19 = n7(3 * t24.length), c21 = n7(s19.length);
  t24.forEach((e20, n21) => {
    s19[3 * n21 + 0] = e20[0], s19[3 * n21 + 1] = e20[1], s19[3 * n21 + 2] = e20.length > 2 ? e20[2] : 0;
  });
  const g16 = c9(s19, o17, 0, c21, 0, s19, 0, s19.length / 3, i19, l22, r24), a27 = null != g16;
  return { numVertices: t24.length, position: s19, mapPositions: c21, projectionSuccess: a27, sampledElevation: g16 };
}

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/ShadedColorMaterialTechnique.js
var g11 = class _g extends e9 {
  initializeProgram(e20) {
    return new o9(e20.rctx, _g.shader.get().build(this.configuration), v9);
  }
  _setPipelineState(e20) {
    const r24 = this.configuration, o17 = e20 === o8.NONE, i19 = e20 === o8.FrontFace;
    return W({ blending: r24.output !== h8.Color && r24.output !== h8.Alpha || !r24.transparent ? null : o17 ? c4 : A6(e20), culling: h11(r24.cullFace), depthTest: { func: i19 ? I.LESS : r24.shadingEnabled ? I.LEQUAL : I.LESS }, depthWrite: o17 ? r24.writeDepth ? a11 : null : E5(e20), colorWrite: _3, polygonOffset: o17 || i19 ? null : _4 });
  }
  initializePipeline() {
    return this._setPipelineState(this.configuration.transparencyPassType);
  }
};
g11.shader = new t8(h14, () => import("./ShadedColorMaterial.glsl-JHFYHJ4P.js"));
var j7 = class extends s10 {
  constructor() {
    super(...arguments), this.output = h8.Color, this.cullFace = n8.None, this.transparencyPassType = o8.NONE, this.hasSlicePlane = false, this.transparent = false, this.writeDepth = true, this.screenSizeEnabled = true, this.shadingEnabled = true, this.hasMultipassTerrain = false, this.cullAboveGround = false;
  }
};
e([r12({ count: h8.COUNT })], j7.prototype, "output", void 0), e([r12({ count: n8.COUNT })], j7.prototype, "cullFace", void 0), e([r12({ count: o8.COUNT })], j7.prototype, "transparencyPassType", void 0), e([r12()], j7.prototype, "hasSlicePlane", void 0), e([r12()], j7.prototype, "transparent", void 0), e([r12()], j7.prototype, "writeDepth", void 0), e([r12()], j7.prototype, "screenSizeEnabled", void 0), e([r12()], j7.prototype, "shadingEnabled", void 0), e([r12()], j7.prototype, "hasMultipassTerrain", void 0), e([r12()], j7.prototype, "cullAboveGround", void 0);
var v9 = /* @__PURE__ */ new Map([[O3.POSITION, 0], [O3.NORMAL, 1], [O3.OFFSET, 2]]);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/ShadedColorMaterial.js
var v10 = class extends h10 {
  constructor(e20) {
    super(e20, new x16()), this.supportsEdges = true, this._configuration = new j7(), this._vertexAttributeLocations = v9;
  }
  getConfiguration(e20, t24) {
    return this._configuration.output = e20, this._configuration.cullFace = this.parameters.cullFace, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.transparent = this.parameters.transparent, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.screenSizeEnabled = this.parameters.screenSizeEnabled, this._configuration.shadingEnabled = this.parameters.shadingEnabled, this._configuration.transparencyPassType = t24.transparencyPassType, this._configuration.hasMultipassTerrain = t24.multipassTerrain.enabled, this._configuration.cullAboveGround = t24.multipassTerrain.cullAboveGround, this._configuration;
  }
  intersect(e20, s19, a27, c21, l22, u13) {
    if (this.parameters.screenSizeEnabled) {
      const s20 = e20.vertexAttributes.get(O3.OFFSET), h25 = { applyToVertex: (e21, n21, o17, c22) => {
        const l23 = o3(L2, s20.data[3 * c22 + 0], s20.data[3 * c22 + 1], s20.data[3 * c22 + 2]), u14 = o3(w9, e21, n21, o17);
        return g(l23, l23, this.parameters.screenSizeScale * a27.camera.computeRenderPixelSizeAt(l23)), u(u14, u14, l23), [u14[0], u14[1], u14[2]];
      }, applyToAabb: (e21) => {
        const t24 = E(e21, L2);
        return j3(e21, this.parameters.screenSizeScale * a27.camera.computeRenderPixelSizeAt(t24));
      } };
      x6(e20, a27, c21, l22, h25, u13);
    } else x6(e20, a27, c21, l22, void 0, u13);
  }
  requiresSlot(e20, t24) {
    if (t24 === h8.Highlight) return e20 === E6.OPAQUE_MATERIAL;
    if (t24 === h8.Color || t24 === h8.Alpha || t24 === h8.ObjectAndLayerIdColor) {
      let t25 = E6.OPAQUE_MATERIAL;
      return this.parameters.transparent && (t25 = this.parameters.writeDepth ? E6.TRANSPARENT_MATERIAL : E6.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL), e20 === t25 || e20 === E6.DRAPED_MATERIAL;
    }
    return false;
  }
  createGLMaterial(e20) {
    return new j8(e20);
  }
  createBufferWriter() {
    return new O5(this.parameters.screenSizeEnabled);
  }
};
var j8 = class extends t7 {
  beginSlot(e20) {
    return this.ensureTechnique(g11, e20);
  }
};
var x16 = class extends u3 {
  constructor() {
    super(...arguments), this.color = r6(1, 1, 1, 1), this.shadingTint = r6(0, 0, 0, 0.25), this.shadingDirection = z2(n4(), [0.5, -0.5, -0.5]), this.screenSizeScale = 14, this.transparent = false, this.writeDepth = true, this.hasSlicePlane = false, this.cullFace = n8.None, this.screenSizeEnabled = false, this.shadingEnabled = true;
  }
};
var O5 = class {
  constructor(e20) {
    this.screenSizeEnabled = e20;
    const t24 = T().vec3f(O3.POSITION).vec3f(O3.NORMAL);
    this.screenSizeEnabled && t24.vec3f(O3.OFFSET), this.vertexBufferLayout = t24;
  }
  allocate(e20) {
    return this.vertexBufferLayout.createBuffer(e20);
  }
  elementCount(e20) {
    return e20.indices.get(O3.POSITION).length;
  }
  write(e20, t24, r24, i19, s19) {
    if (S4(r24, this.vertexBufferLayout, e20, t24, i19, s19), this.screenSizeEnabled) {
      if (!r24.vertexAttributes.has(O3.OFFSET)) throw new Error(`${O3.OFFSET} vertex attribute required for screenSizeEnabled ShadedColorMaterial`);
      {
        const e21 = r24.vertexAttributes.get(O3.OFFSET), a27 = r24.indices.get(O3.OFFSET);
        s8(3 === e21.size);
        const n21 = i19.getField(O3.OFFSET, i5);
        if (!n21) throw new Error("unable to acquire view for " + O3.OFFSET);
        g4(a27, e21.data, t24, n21, s19);
      }
    }
  }
};
var L2 = n4();
var w9 = n4();

// node_modules/@arcgis/core/views/3d/interactive/visualElements/VerticesVisualElement.js
var _6 = class extends n15 {
  constructor(e20) {
    super(e20), this.view = null, this._renderOccluded = c3.OccludeAndTransparent, this._vertices = null, this._spatialReference = null, this._color = y7.colorToVec4(y7.reshapeManipulators.vertex.color), this._size = y7.reshapeManipulators.vertex.size, this._outlineColor = y7.colorToVec4(y7.reshapeManipulators.vertex.outlineColor), this._outlineSize = y7.reshapeManipulators.vertex.outlineSize, this._elevationInfo = null, this.applyProps(e20);
  }
  get renderOccluded() {
    return this._renderOccluded;
  }
  set renderOccluded(e20) {
    e20 !== this._renderOccluded && (this._renderOccluded = e20, this._updateMaterial(), this._updateOutlineMaterial());
  }
  get vertices() {
    return this._vertices;
  }
  set vertices(e20) {
    this._vertices = e20, this.recreateGeometry();
  }
  get spatialReference() {
    return this._spatialReference;
  }
  set spatialReference(e20) {
    this._spatialReference = e20, this.recreateGeometry();
  }
  get color() {
    return this._color;
  }
  set color(e20) {
    D(e20, this._color) || (a4(this._color, e20), this._updateMaterial());
  }
  get size() {
    return this._size;
  }
  set size(e20) {
    e20 !== this._size && (this._size = e20, this._updateMaterial());
  }
  get outlineColor() {
    return this._outlineColor;
  }
  set outlineColor(e20) {
    D(e20, this._outlineColor) || (a4(this._outlineColor, e20), this._updateOutlineMaterial());
  }
  get outlineSize() {
    return this._outlineSize;
  }
  set outlineSize(e20) {
    e20 !== this._outlineSize && (this._outlineSize = e20, this._updateOutlineMaterial());
  }
  get elevationInfo() {
    return this._elevationInfo;
  }
  set elevationInfo(e20) {
    this._elevationInfo = e20, this.recreateGeometry();
  }
  get _vertexMaterialParameters() {
    return { color: this._color, transparent: this._color[3] < 1, screenSizeScale: this.size, renderOccluded: this._renderOccluded };
  }
  get _vertexOutlineMaterialParameters() {
    return { color: this._outlineColor, transparent: this._outlineColor[3] < 1, screenSizeScale: this.size + 2 * this.outlineSize, renderOccluded: this._renderOccluded };
  }
  _updateMaterial() {
    this.attached && this._vertexMaterial.setParameters(this._vertexMaterialParameters);
  }
  _updateOutlineMaterial() {
    this.attached && this._vertexOutlineMaterial.setParameters(this._vertexOutlineMaterialParameters);
  }
  _createRenderGeometries() {
    const r24 = this.vertices;
    if (t(r24) || 0 === r24.length) return [];
    const i19 = 0.5, s19 = 0.5, a27 = t17(r24, this.spatialReference, this.view.elevationProvider, this.view.renderCoordsHelper, h13.fromElevationInfo(this.elevationInfo)), o17 = [], c21 = a27.numVertices, h25 = a27.position;
    for (let e20 = 0; e20 < c21; ++e20) {
      const r25 = o3(p19, h25[3 * e20 + 0], h25[3 * e20 + 1], h25[3 * e20 + 2]), a28 = m11(this._vertexMaterial, i19, r25), l22 = m11(this._vertexOutlineMaterial, s19, r25);
      o17.push({ vertexGeometry: a28, vertexOutlineGeometry: l22 });
    }
    return o17;
  }
  createGeometries(e20) {
    const t24 = this._createRenderGeometries();
    for (const { vertexGeometry: r24, vertexOutlineGeometry: i19 } of t24) e20.addGeometry(r24), e20.addGeometry(i19);
  }
  createExternalResources() {
    this._vertexMaterial = new v10({ ...this._vertexMaterialParameters, writeDepth: true, cullFace: n8.Back, screenSizeEnabled: true }), this._vertexOutlineMaterial = new v10({ ...this._vertexOutlineMaterialParameters, transparent: true, writeDepth: true, cullFace: n8.Front, screenSizeEnabled: true, shadingEnabled: false });
  }
  destroyExternalResources() {
    this._vertexMaterial = null, this._vertexOutlineMaterial = null;
  }
  forEachExternalMaterial(e20) {
    e20(this._vertexMaterial), e20(this._vertexOutlineMaterial);
  }
};
var p19 = n4();
function m11(e20, t24, r24) {
  return nt(e20, t24, 16, 16, { offset: r24 });
}

// node_modules/@arcgis/core/views/3d/layers/graphics/GraphicState.js
var e17 = class extends n3.EventedAccessor {
  constructor(r24) {
    super(r24), this.tracking = false, this.displaying = false, this.isDraped = false;
  }
};
e([y2({ constructOnly: true })], e17.prototype, "graphic", void 0), e([y2()], e17.prototype, "tracking", void 0), e([y2()], e17.prototype, "displaying", void 0), e([y2()], e17.prototype, "isDraped", void 0), e17 = e([a3("esri.views.3d.layers.graphics.GraphicState")], e17);

// node_modules/@arcgis/core/views/3d/interactive/editingTools/draw/DrawGraphicTool3D.js
var T4 = class extends H2 {
  constructor(e20) {
    super(e20), this._activeVertexVisualElement = null, this._createGraphicState = null, this._outlineVisualElement = null, this._verticesVisualElement = null, this._verticalLineVisualElement = null, this.geometryType = null, this.type = "draw-3d";
  }
  initialize() {
    const { mode: e20, offset: t24 } = this.elevationInfo;
    this.internalGraphicsLayer.elevationInfo = new x4({ mode: e20, offset: t24 });
  }
  normalizeCtorArgs(e20) {
    if (!e20.elevationInfo) {
      const t24 = e20.hasZ ?? true;
      return { ...e20, elevationInfo: s11(t24) };
    }
    return e20;
  }
  initializeGraphic(e20) {
    const i19 = this._createGraphicState = new e17({ graphic: e20 });
    return r2([this.view.maskOccludee(e20), this.view.trackGraphicState(i19), l3(() => ({ element: this._outlineVisualElement, isDraped: i19.isDraped }), ({ element: e21, isDraped: t24 }) => {
      o(e21, (e22) => e22.isDraped = t24);
    }, h4)]);
  }
  makeDrawOperation() {
    const { geometryType: e20 } = this, t24 = "circle" !== e20 && "rectangle" !== e20;
    return new k2({ view: this.view, manipulators: this.manipulators, geometryType: B2(e20), drawingMode: this.mode, hasZ: this.hasZ, defaultZ: this.defaultZ, snapToSceneEnabled: this.snapToScene, drawSurface: new c6(this.view, this.elevationInfo, [this.internalGraphicsLayer]), elevationDrawSurface: new o11(this.elevationInfo, this.defaultZ, this.view, this.internalGraphicsLayer), hasM: false, elevationInfo: this.elevationInfo, snappingManager: this.snappingManager, snappingVisualizer: new x14(), segmentLabels: t24 ? new a21() : null, labelOptions: this.labelOptions, tooltipOptions: this.tooltipOptions, isDraped: r(this._createGraphicState) ? this._createGraphicState.isDraped : "on-the-ground" === u4(this.hasZ, this.elevationInfo) });
  }
  onActiveVertexChanged(e20) {
    const { view: t24 } = this;
    return r(this._activeVertexVisualElement) ? (this._activeVertexVisualElement.vertices = [e20], this._updateVerticalLineVisualElement(e20), null) : (this._activeVertexVisualElement = new _6({ view: t24, spatialReference: t24.spatialReference, vertices: [e20], elevationInfo: this.internalGraphicsLayer.elevationInfo, renderOccluded: y7.reshapeManipulators.vertex.renderOccluded, attached: false }), this._activeVertexVisualElement.color = y7.colorToVec4(y7.reshapeManipulators.selected.color), this._activeVertexVisualElement.attached = true, this._verticalLineVisualElement = new v8({ view: t24, extensionType: y7.visualElements.zVerticalLine.extensionType, innerWidth: 1, attached: false, writeDepthEnabled: false, renderOccluded: c3.OccludeAndTransparent }), y7.visualElements.zVerticalLine.apply(this._verticalLineVisualElement), n2(() => {
      this._activeVertexVisualElement = a(this._activeVertexVisualElement), this._verticalLineVisualElement = a(this._verticalLineVisualElement);
    }));
  }
  _updateVerticalLineVisualElement(e20) {
    const t24 = this._verticalLineVisualElement;
    if (t(t24)) return;
    const { renderCoordsHelper: i19, elevationProvider: s19 } = this.view;
    o3(M10, e20[0], e20[1], e20[2]), b11.setFromElevationInfo(this.elevationInfo), M10[2] = d7(M10, s19, b11, i19);
    i19.toRenderCoords(M10, this.view.spatialReference, M10) ? (t24.setStartEndFromWorldDownAtLocation(M10), t24.attached = true) : t24.attached = false;
  }
  onOutlineChanged(e20) {
    if (r(this._outlineVisualElement)) return this._outlineVisualElement.geometry = e20, null;
    const t24 = this.internalGraphicsLayer.elevationInfo;
    return this._outlineVisualElement = new I4({ view: this.view, geometry: e20, elevationInfo: t24, isDraped: r(this._createGraphicState) ? this._createGraphicState.isDraped : "on-the-ground" === u4(this.hasZ, t24), attached: false }), y7.visualElements.lineGraphics.outline.apply(this._outlineVisualElement), y7.visualElements.lineGraphics.shadowStyle.apply(this._outlineVisualElement), this._outlineVisualElement.attached = true, this._outlineVisualElement.laserlineEnabled = true, n2(() => {
      this._outlineVisualElement = a(this._outlineVisualElement);
    });
  }
  onRegularVerticesChanged(e20) {
    return r(this._verticesVisualElement) ? (this._verticesVisualElement.vertices = e20, null) : (this._verticesVisualElement = new _6({ view: this.view, spatialReference: this.view.spatialReference, vertices: e20, elevationInfo: this.internalGraphicsLayer.elevationInfo, renderOccluded: y7.reshapeManipulators.vertex.renderOccluded, attached: false }), this._verticesVisualElement.attached = true, n2(() => {
      this._verticesVisualElement = a(this._verticesVisualElement);
    }));
  }
};
e([y2({ constructOnly: true })], T4.prototype, "elevationInfo", void 0), e([y2({ constructOnly: true })], T4.prototype, "geometryType", void 0), e([y2()], T4.prototype, "type", void 0), e([y2({ constructOnly: true })], T4.prototype, "view", void 0), T4 = e([a3("esri.views.3d.interactive.editingTools.draw.DrawGraphicTool3D")], T4);
var b11 = new h13();
var M10 = n4();

// node_modules/@arcgis/core/views/3d/support/geometryUtils/ray.js
function s13(e20, n21, o17) {
  return u9(e20, e20.screenToRender(n21, p3(c2.get())), o17);
}
function u9(t24, c21, s19) {
  const u13 = p3(a8(c2.get(), c21));
  if (u13[2] = 0, !t24.unprojectFromRenderScreen(u13, s19.origin)) return null;
  const m17 = p3(a8(c2.get(), c21));
  m17[2] = 1;
  const g16 = t24.unprojectFromRenderScreen(m17, c2.get());
  return t(g16) ? null : (e3(s19.direction, g16, s19.origin), s19);
}

// node_modules/@arcgis/core/views/3d/interactive/Manipulator3D.js
var ie = class {
  constructor(e20) {
    var _a;
    this.metadata = void 0, this._camera = new $2(), this._elevation = { offset: 0, override: null }, this.collisionType = { type: "point" }, this.collisionPriority = 0, this._renderObjects = new Array(), this.autoScaleRenderObjects = true, this._available = true, this._noDisplayCount = 0, this._radius = 10, this._worldSized = false, this.focusMultiplier = 2, this.touchMultiplier = 2.5, this.worldOriented = false, this._modelTransform = e6(), this._worldFrame = null, this._renderLocation = n4(), this._renderLocationDirty = true, this._location = new w({ x: 0, y: 0, z: 0 }), this._elevationAlignedLocation = new w(), this._elevationAlignedLocationDirty = true, this.interactive = true, this.selectable = false, this.grabbable = true, this.cursor = null, this.grabCursor = null, this._grabbing = false, this.dragging = false, this._hovering = false, this._selected = false, this._state = u5.None, this._focused = false, this.events = new n3.EventEmitter(), this._screenLocation = { screenPointArray: i3(), renderScreenPointArray: x2(), pixelSize: 0 }, this._screenLocationDirty = true, this._engineResourcesAddedToStage = false, this._attached = false, this._location.spatialReference = e20.view.spatialReference;
    for (const t24 in e20) this[t24] = e20[t24];
    const i19 = (_a = this.view.state) == null ? void 0 : _a.camera;
    i19 && this._camera.copyFrom(i19);
  }
  destroy() {
    this._removeResourcesFromStage(), this._engineResources = null, this.view = null, this._camera = null;
  }
  get _stage() {
    var _a;
    return (_a = this.view) == null ? void 0 : _a._stage;
  }
  get elevationInfo() {
    return this._elevationInfo;
  }
  set elevationInfo(e20) {
    this._elevationInfo = e20, this._elevationAlignedLocationDirty = true, this._renderLocationDirty = true, this._updateEngineObject();
  }
  get renderObjects() {
    return this._renderObjects;
  }
  set renderObjects(e20) {
    this._removeResourcesFromStage(), this._engineResources = null, this._renderObjects = e20.slice(), this._updateEngineObject();
  }
  set available(e20) {
    e20 !== this._available && (this._available = e20, this._updateEngineObject());
  }
  get available() {
    return this._available;
  }
  disableDisplay() {
    return this._noDisplayCount++, 1 === this._noDisplayCount && this._updateEngineObject(), { remove: h2(() => {
      this._noDisplayCount--, 0 === this._noDisplayCount && this._updateEngineObject();
    }) };
  }
  set radius(e20) {
    e20 !== this._radius && (this._radius = e20, this._updateEngineObject());
  }
  get radius() {
    return this._radius;
  }
  set worldSized(e20) {
    e20 !== this._worldSized && (this._worldSized = e20, this._updateEngineObject());
  }
  get worldSized() {
    return this._worldSized;
  }
  get modelTransform() {
    return this._modelTransform;
  }
  set modelTransform(e20) {
    se(e20) && (this._screenLocationDirty = true), n5(this._modelTransform, e20), this._updateEngineObject();
  }
  get renderLocation() {
    return this._renderLocationDirty && (this._renderLocationDirty = false, this.view.renderCoordsHelper.toRenderCoords(this.elevationAlignedLocation, this._renderLocation), this.worldOriented ? (this._worldFrame || (this._worldFrame = e6()), oe(this.view, this._renderLocation, this._worldFrame)) : this._worldFrame && (this._worldFrame = null)), this._renderLocation;
  }
  set renderLocation(e20) {
    this.view.renderCoordsHelper.fromRenderCoords(e20, this._location), this.elevationAlignedLocation = this._location;
  }
  get location() {
    return this._location;
  }
  set location(e20) {
    M4(e20, this._location), this._notifyLocationChanged();
  }
  _notifyLocationChanged() {
    this._renderLocationDirty = true, this._screenLocationDirty = true, this._elevationAlignedLocationDirty = true, this._updateEngineObject(), this.events.emit("location-update", { location: this._location });
  }
  get elevationAlignedLocation() {
    return this._elevationAlignedLocationDirty ? (this._evaluateElevationAlignment(), this._updateElevationAlignedLocation(), this._elevationAlignedLocation) : this._elevationAlignedLocation;
  }
  set elevationAlignedLocation(e20) {
    M4(e20, this._location), this._evaluateElevationAlignment(), this._location.z -= this._elevation.offset, this._updateElevationAlignedLocation(), this._updateEngineObject(), this.events.emit("location-update", { location: this._location });
  }
  _updateElevationAlignedLocation() {
    const e20 = r(this._elevation.override) ? this._elevation.override : this.location.z || 0;
    this._elevationAlignedLocation.x = this.location.x, this._elevationAlignedLocation.y = this.location.y, this._elevationAlignedLocation.z = e20 + this._elevation.offset, this._elevationAlignedLocation.spatialReference = k(this.location.spatialReference), this._renderLocationDirty = true, this._screenLocationDirty = true, this._elevationAlignedLocationDirty = false;
  }
  grabbableForEvent() {
    return true;
  }
  get grabbing() {
    return this._grabbing;
  }
  set grabbing(e20) {
    e20 !== this._grabbing && (this._grabbing = e20, this._setFocused(this._hovering || this._grabbing), this._updateEngineObject());
  }
  get hovering() {
    return this._hovering;
  }
  set hovering(e20) {
    e20 !== this._hovering && (this._hovering = e20, this._setFocused(this._hovering || this._grabbing), this._updateEngineObject());
  }
  get selected() {
    return this._selected;
  }
  set selected(e20) {
    e20 !== this._selected && (this._selected = e20, this._updateEngineObject(), this.events.emit("select-changed", { action: e20 ? "select" : "deselect" }));
  }
  get state() {
    return this._state;
  }
  set state(e20) {
    e20 !== this._state && (this._state = e20, this._updateEngineObject());
  }
  updateStateEnabled(e20, t24) {
    t24 ? this.state |= e20 : this.state &= ~e20;
  }
  _setFocused(e20) {
    e20 !== this._focused && (this._focused = e20, this.events.emit("focus-changed", { action: true === e20 ? "focus" : "unfocus" }));
  }
  get focused() {
    return this._focused;
  }
  get screenLocation() {
    return this._ensureScreenLocation(), this._screenLocation;
  }
  _ensureScreenLocation() {
    if (!this._screenLocationDirty) return;
    this._screenLocation.pixelSize = this._camera.computeScreenPixelSizeAt(this.renderLocation), this._screenLocationDirty = false;
    let e20;
    if (se(this._modelTransform)) {
      const t24 = this._calculateModelTransformOffset(pe2);
      e20 = u(t24, t24, this.renderLocation);
    } else e20 = this.renderLocation;
    this._camera.projectToRenderScreen(e20, this._screenLocation.renderScreenPointArray), this._camera.renderToScreen(this._screenLocation.renderScreenPointArray, this._screenLocation.screenPointArray);
  }
  get applyObjectTransform() {
    return this._applyObjectTransform;
  }
  set applyObjectTransform(e20) {
    this._applyObjectTransform = e20, this._screenLocationDirty = true, this._updateEngineObject();
  }
  get attached() {
    return this._attached;
  }
  intersectionDistance(t24, i19) {
    if (!this.available) return null;
    const s19 = d2(t24, ne), n21 = this._getCollisionRadius(i19), r24 = -1 * this.collisionPriority;
    switch (this.collisionType.type) {
      case "point":
        if (b3(this.screenLocation.screenPointArray, s19) < n21 * n21) return this.screenLocation.renderScreenPointArray[2] + r24;
        break;
      case "line": {
        const e20 = this.collisionType.paths, t25 = this._getWorldToScreenObjectScale(), i20 = this._calculateObjectTransform(t25, he), a27 = n21 * this.screenLocation.pixelSize, c21 = s13(this._camera, s19, ae);
        if (t(c21)) return null;
        for (const s20 of e20) {
          if (0 === s20.length) continue;
          const e21 = O2(_e, s20[0], i20);
          for (let t26 = 1; t26 < s20.length; t26++) {
            const o17 = O2(ue, s20[t26], i20), n22 = B(b5(e21, o17, re), c21);
            if (null != n22 && n22 < a27 * a27) {
              const t27 = u(c2.get(), e21, o17);
              g(t27, t27, 0.5);
              const i21 = y3(c2.get());
              return this._camera.projectToRenderScreen(t27, i21), i21[2] + r24;
            }
            r4(e21, o17);
          }
        }
        break;
      }
      case "disc": {
        const e20 = this.collisionType.direction, t25 = this.collisionType.offset ?? f4, i20 = this._getWorldToScreenObjectScale(), a27 = this._calculateObjectTransform(i20, he), c21 = n21 * this.screenLocation.pixelSize, l22 = s13(this._camera, s19, ae);
        if (t(l22)) return null;
        const h25 = a9(ce2, a27), d15 = S(me, e20, h25), u13 = O2(fe, t25, a27);
        _2(u13, d15, de);
        const g16 = ge;
        if (x5(de, l22, g16) && p2(g16, u13) < c21 * c21) return this.screenLocation.renderScreenPointArray[2] + r24;
        break;
      }
      case "ribbon": {
        const { paths: e20, direction: t25 } = this.collisionType, i20 = this._getWorldToScreenObjectScale(), a27 = this._calculateObjectTransform(i20, he), c21 = n21 * this._camera.computeScreenPixelSizeAt(this.renderLocation), l22 = s13(this._camera, s19, ae);
        if (t(l22)) return null;
        const d15 = a9(ce2, a27), u13 = S(me, t25, d15), g16 = this._calculateModelTransformPosition(fe);
        _2(g16, u13, de);
        const m17 = ge;
        if (!x5(de, l22, m17)) break;
        for (const s20 of e20) {
          if (0 === s20.length) continue;
          const e21 = O2(_e, s20[0], a27);
          for (let t26 = 1; t26 < s20.length; t26++) {
            const i21 = O2(ue, s20[t26], a27), o17 = M3(b5(e21, i21, re), m17);
            if (null != o17 && o17 < c21 * c21) {
              const t27 = u(c2.get(), e21, i21);
              g(t27, t27, 0.5);
              const s21 = y3(c2.get());
              return this._camera.projectToRenderScreen(t27, s21), s21[2] + r24;
            }
            r4(e21, i21);
          }
        }
        break;
      }
      default:
        n6(this.collisionType);
    }
    return null;
  }
  attach(e20 = { manipulator3D: {} }) {
    const t24 = this._stage;
    if (!t24) return;
    const i19 = e20.manipulator3D;
    if (t(i19.engineLayerId)) {
      const e21 = new l15({ pickable: false, updatePolicy: C2.SYNC });
      t24.add(e21), i19.engineLayerId = e21.id, this._engineLayer = e21;
    } else (t24 == null ? void 0 : t24.getObject) && (this._engineLayer = t24.getObject(i19.engineLayerId));
    i19.engineLayerReferences = (i19.engineLayerReferences || 0) + 1, this._materialIdReferences = i19.materialIdReferences, t(this._materialIdReferences) && (this._materialIdReferences = /* @__PURE__ */ new Map(), i19.materialIdReferences = this._materialIdReferences), this._camera.copyFrom(this.view.state.camera), this._attached = true, this._updateEngineObject(), An(this._location.spatialReference, this.view.spatialReference) || (this.location = new w({ x: 0, y: 0, z: 0, spatialReference: this.view.spatialReference }));
  }
  detach(e20 = { manipulator3D: {} }) {
    const t24 = e20.manipulator3D;
    t24.engineLayerReferences--;
    const i19 = 0 === t24.engineLayerReferences;
    i19 && (t24.engineLayerId = null), this._removeResourcesFromStage(i19), this._engineResources = null, this._engineLayer = null, this._materialIdReferences = null, this._attached = false;
  }
  onViewChange() {
    this._camera.copyFrom(this.view.state.camera), this._screenLocationDirty = true, this._updateEngineObject();
  }
  onElevationChange(e20) {
    pn(this.location, be, e20.spatialReference) && F2(e20.extent, be) && this._notifyLocationChanged();
  }
  _evaluateElevationAlignment() {
    if (t(this.elevationInfo)) return;
    let e20 = null, t24 = 0;
    const i19 = l(this.elevationInfo.offset, 0);
    switch (this.elevationInfo.mode) {
      case "on-the-ground":
        e20 = l(i11(this.view.elevationProvider, this.location, "ground"), 0);
        break;
      case "relative-to-ground":
        t24 = l(i11(this.view.elevationProvider, this.location, "ground"), 0) + i19;
        break;
      case "relative-to-scene":
        t24 = l(i11(this.view.elevationProvider, this.location, "scene"), 0) + i19;
        break;
      case "absolute-height":
        t24 = i19;
    }
    return t24 !== this._elevation.offset || e20 !== this._elevation.override ? (this._elevation.offset = t24, void (this._elevation.override = e20)) : void 0;
  }
  _updateEngineObject() {
    if (!this._attached) return;
    if (!this.available) return void this._removeResourcesFromStage();
    const e20 = this._getWorldToScreenObjectScale(), t24 = he;
    if (true === this.autoScaleRenderObjects) {
      const i20 = this._getFocusedSize(this._radius, this.focused) * e20;
      this._calculateObjectTransform(i20, t24);
    } else this._calculateObjectTransform(e20, t24);
    const { objectsByState: i19 } = this._ensureEngineResources(), s19 = (this.focused ? t11.Focused : t11.Unfocused) | (this.selected ? t11.Selected : t11.Unselected), o17 = this._noDisplayCount > 0;
    for (const { stateMask: n21, objects: r24 } of i19) {
      if (o17) {
        for (const e22 of r24) e22.visible = false;
        continue;
      }
      const e21 = (n21 & t11.All) !== t11.None, i20 = (n21 & u5.All) !== u5.None, a27 = !e21 || (s19 & n21) == (n21 & t11.All), c21 = !i20 || (this.state & n21) == (n21 & u5.All);
      if (a27 && c21) for (const s20 of r24) s20.visible = true, s20.transformation = t24;
      else for (const t25 of r24) t25.visible = false;
    }
  }
  _ensureEngineResources() {
    if (t(this._engineResources)) {
      const e20 = e2(this._engineLayer), t24 = [], i19 = /* @__PURE__ */ new Set();
      this.renderObjects.forEach(({ geometry: { material: e21 } }) => {
        i19.has(e21) || (t24.push(e21), i19.add(e21));
      });
      const s19 = /* @__PURE__ */ new Map();
      this._renderObjects.forEach((e21) => {
        const t25 = new x10({ castShadow: false, geometries: [e21.geometry] }), i20 = s19.get(e21.stateMask) || [];
        i20.push(t25), s19.set(e21.stateMask, i20);
      });
      const o17 = [];
      s19.forEach((e21, t25) => o17.push({ stateMask: t25, objects: e21 })), this._engineResources = { objectsByState: o17, layer: e20, materials: t24 };
    }
    return this._addResourcesToStage(), this._engineResources;
  }
  _addResourcesToStage() {
    const e20 = this._stage;
    if (this._engineResourcesAddedToStage || t(this._engineResources) || !e20) return;
    const { objectsByState: t24, layer: i19, materials: s19 } = this._engineResources;
    s19.forEach((t25) => {
      const i20 = e2(this._materialIdReferences), s20 = i20.get(t25.id) || 0;
      0 === s20 && e20.add(t25), i20.set(t25.id, s20 + 1);
    }), t24.forEach(({ objects: t25 }) => {
      i19.addMany(t25), e20.addMany(t25);
    }), this._engineResourcesAddedToStage = true;
  }
  _removeResourcesFromStage(e20 = false) {
    const t24 = this._stage;
    if (!this._engineResourcesAddedToStage || t(this._engineResources) || !t24) return;
    const { objectsByState: i19, layer: s19, materials: n21 } = this._engineResources;
    i19.forEach(({ objects: e21 }) => {
      s19.removeMany(e21), t24.removeMany(e21);
    }), n21.forEach((e21) => {
      const i20 = e2(this._materialIdReferences), s20 = i20.get(e21.id);
      1 === s20 ? (t24.remove(e21), i20.delete(e21.id)) : i20.set(e21.id, s20 - 1);
    }), e20 && t24.remove(s19), this._engineResourcesAddedToStage = false;
  }
  _getCollisionRadius(e20) {
    return this._getFocusedSize(this.radius, true) * ("touch" === e20 ? this.touchMultiplier : 1);
  }
  _getFocusedSize(e20, t24) {
    return e20 * (t24 ? this.focusMultiplier : 1);
  }
  _getWorldToScreenObjectScale() {
    return this._worldSized ? 1 : this.screenLocation.pixelSize;
  }
  _calculateModelTransformPosition(e20) {
    const t24 = this._getWorldToScreenObjectScale(), i19 = this._calculateObjectTransform(t24, le);
    return o3(e20, i19[12], i19[13], i19[14]);
  }
  _calculateModelTransformOffset(e20) {
    const t24 = this._calculateModelTransformPosition(e20);
    return e3(e20, t24, this.renderLocation);
  }
  _calculateObjectTransform(e20, t24) {
    return s3(t24, e20, 0, 0, 0, 0, e20, 0, 0, 0, 0, e20, 0, 0, 0, 0, 1), this._worldFrame && c(t24, t24, this._worldFrame), c(t24, t24, this._modelTransform), t24[12] += this.renderLocation[0], t24[13] += this.renderLocation[1], t24[14] += this.renderLocation[2], t24[15] = 1, r(this._applyObjectTransform) && this._applyObjectTransform(t24), t24;
  }
  get test() {
    let e20 = false;
    if (r(this._engineResources)) for (const t24 in this._engineResources.objectsByState) {
      const i19 = this._engineResources.objectsByState[t24];
      for (const t25 of i19.objects) if (t25.visible) {
        e20 = true;
        break;
      }
      if (e20) break;
    }
    return { areAnyResourcesVisible: e20 };
  }
};
function se(e20) {
  return 0 !== e20[12] || 0 !== e20[13] || 0 !== e20[14];
}
function oe(e20, t24, s19) {
  switch (e20.viewingMode) {
    case "local":
      return r7(s19), true;
    case "global": {
      const o17 = O(e20.renderCoordsHelper.spatialReference);
      return pe(t24, 0, _e, 0, o17.radius), Xn(m2(_e[0]), m2(_e[1]), s19), true;
    }
  }
}
var ne = i3();
var re = v5();
var ae = d4();
var ce2 = e5();
var le = e6();
var he = e6();
var de = p6();
var _e = n4();
var ue = n4();
var ge = n4();
var me = n4();
var fe = n4();
var pe2 = n4();
var be = new w({ x: 0, y: 0, z: 0, spatialReference: null });

// node_modules/@arcgis/core/views/3d/interactive/RenderObject.js
var e18 = class {
  constructor(e20, s19 = t11.None) {
    this.geometry = e20, this.stateMask = s19;
  }
};

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/ImageMaterialTechnique.js
var _7 = class __ extends e9 {
  initializeProgram(e20) {
    return new o9(e20.rctx, __.shader.get().build(this.configuration), E3);
  }
  _setPipelineState(e20, r24) {
    const i19 = this.configuration, s19 = e20 === o8.NONE, o17 = e20 === o8.FrontFace;
    return W({ blending: i19.output !== h8.Color && i19.output !== h8.Alpha || !i19.transparent ? null : s19 ? C6 : A6(e20), culling: h11(i19.cullFace), depthTest: { func: l12(e20) }, depthWrite: s19 ? i19.writeDepth ? a11 : null : E5(e20), colorWrite: _3, stencilWrite: i19.hasOccludees ? e10 : null, stencilTest: i19.hasOccludees ? r24 ? o10 : f9 : null, polygonOffset: s19 || o17 ? null : a12(i19.enableOffset) });
  }
  initializePipeline() {
    return this._occludeePipelineState = this._setPipelineState(this.configuration.transparencyPassType, true), this._setPipelineState(this.configuration.transparencyPassType, false);
  }
  getPipelineState(e20, t24) {
    return t24 ? this._occludeePipelineState : super.getPipelineState(e20, t24);
  }
};
_7.shader = new t8(w6, () => import("./ImageMaterial.glsl-RWT2I6G5.js"));
var C6 = s9(R3.ONE, R3.ONE_MINUS_SRC_ALPHA);
var w10 = class extends s10 {
  constructor() {
    super(...arguments), this.output = h8.Color, this.cullFace = n8.None, this.hasSlicePlane = false, this.transparent = false, this.enableOffset = true, this.writeDepth = true, this.hasOccludees = false, this.transparencyPassType = o8.NONE, this.hasMultipassTerrain = false, this.cullAboveGround = false;
  }
};
e([r12({ count: h8.COUNT })], w10.prototype, "output", void 0), e([r12({ count: n8.COUNT })], w10.prototype, "cullFace", void 0), e([r12()], w10.prototype, "hasSlicePlane", void 0), e([r12()], w10.prototype, "transparent", void 0), e([r12()], w10.prototype, "enableOffset", void 0), e([r12()], w10.prototype, "writeDepth", void 0), e([r12()], w10.prototype, "hasOccludees", void 0), e([r12({ count: o8.COUNT })], w10.prototype, "transparencyPassType", void 0), e([r12()], w10.prototype, "hasMultipassTerrain", void 0), e([r12()], w10.prototype, "cullAboveGround", void 0);

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/ImageMaterial.js
var c14 = class extends e14 {
  constructor(e20) {
    super(e20, new m12()), this.supportsEdges = true, this._configuration = new w10();
  }
  getConfiguration(e20, t24) {
    return this._configuration.output = e20, this._configuration.cullFace = this.parameters.cullFace, this._configuration.hasSlicePlane = this.parameters.hasSlicePlane, this._configuration.transparent = this.parameters.transparent, this._configuration.writeDepth = this.parameters.writeDepth, this._configuration.hasOccludees = this.parameters.hasOccludees, this._configuration.transparencyPassType = t24.transparencyPassType, this._configuration.enableOffset = t24.camera.relativeElevation < S3, this._configuration.hasMultipassTerrain = t24.multipassTerrain.enabled, this._configuration.cullAboveGround = t24.multipassTerrain.cullAboveGround, this._configuration;
  }
  requiresSlot(t24, r24) {
    if (r24 === h8.Color || r24 === h8.Alpha || r24 === h8.Highlight) {
      if (t24 === E6.DRAPED_MATERIAL) return true;
      if (r24 === h8.Highlight) return t24 === E6.OPAQUE_MATERIAL;
      return t24 === (this.parameters.transparent ? this.parameters.writeDepth ? E6.TRANSPARENT_MATERIAL : E6.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL : E6.OPAQUE_MATERIAL);
    }
    return false;
  }
  createGLMaterial(e20) {
    return new p20(e20);
  }
  createBufferWriter() {
    return new r11(c8);
  }
};
var p20 = class extends h9 {
  constructor(e20) {
    super({ ...e20, ...e20.material.parameters });
  }
  _updateParameters(e20) {
    return this.updateTexture(this._material.parameters.textureId), this._material.setParameters(this.textureBindParameters), this.ensureTechnique(_7, e20);
  }
  _updateOccludeeState(e20) {
    e20.hasOccludees !== this._material.parameters.hasOccludees && (this._material.setParameters({ hasOccludees: e20.hasOccludees }), this._updateParameters(e20));
  }
  beginSlot(t24) {
    return this._output !== h8.Color && this._output !== h8.Alpha || this._updateOccludeeState(t24), this._updateParameters(t24);
  }
};
var m12 = class extends u3 {
  constructor() {
    super(...arguments), this.transparent = false, this.writeDepth = true, this.hasSlicePlane = false, this.cullFace = n8.None, this.hasOccludees = false, this.opacity = 1, this.textureId = null, this.initTextureTransparent = true;
  }
};

// node_modules/@arcgis/core/views/3d/interactive/manipulatorUtils.js
function R5(e20, t24 = c3.OccludeAndTransparent, r24 = true) {
  const n21 = r6(e20[0], e20[1], e20[2], e20.length > 3 ? e20[3] : 1), o17 = e20[3] < 1;
  return r24 ? new v10({ color: n21, transparent: o17, writeDepth: true, cullFace: n8.Back, renderOccluded: t24 }) : new f13({ color: n21, transparent: o17, writeDepth: true, cullFace: n8.Back, renderOccluded: t24 });
}
function L3(e20, t24 = c3.OccludeAndTransparent) {
  const r24 = r6(e20[0], e20[1], e20[2], 4 === e20.length ? e20[3] : 1);
  return new f13({ color: r24, transparent: true, writeDepth: true, cullFace: n8.Front, renderOccluded: t24 });
}
var T5 = Object.freeze({ calloutLength: 40, calloutWidth: 1, discRadius: 27, focusMultiplier: 1.1, calloutColor: r3(1, 0.5, 0) });
function U4(e20, t24) {
  const r24 = new ie({ view: e20, autoScaleRenderObjects: false, collisionPriority: 1, metadata: t24.metadata });
  return D5(r24, t24), r24;
}
function D5(e20, t24) {
  var _a;
  const r24 = t24.material ?? new c14({ transparent: true, writeDepth: false, textureId: (_a = t24.texture) == null ? void 0 : _a.id, renderOccluded: c3.Opaque }), n21 = t24.focusMultiplier ?? T5.focusMultiplier, o17 = t24.calloutLength ?? T5.calloutLength, i19 = T5.discRadius * (t24.discScale ?? 1), a27 = i19 * n21, c21 = (e21, t25) => {
    const r25 = [0, 1, 2, 2, 3, 0];
    return new v6(t25, [[O3.POSITION, new s6([o17 - e21, -e21, 0, o17 + e21, -e21, 0, o17 + e21, e21, 0, o17 - e21, e21, 0], 3, true)], [O3.UV0, new s6([0, 0, 1, 0, 1, 1, 0, 1], 2, true)]], [[O3.POSITION, r25], [O3.UV0, r25]]);
  }, s19 = T5.calloutColor, u13 = t24.calloutWidth ?? T5.calloutWidth, m17 = new (u13 > 1 ? z3 : q5)({ width: u13, color: r6(s19[0], s19[1], s19[2], t24.calloutOpacity ?? 1), renderOccluded: c3.OccludeAndTransparent }), d15 = ft(m17, [[0, 0, 0], [o17 - i19, 0, 0]]), f19 = ft(m17, [[0, 0, 0], [o17 - a27, 0, 0]]), w15 = t24.customStateMask ?? u5.None;
  e20.collisionType = { type: "disc", direction: [0, 0, 1], offset: [o17, 0, 0] }, e20.focusMultiplier = n21, e20.metadata = t24.metadata, e20.radius = i19, e20.renderObjects = [new e18(c21(i19, r24), t11.Unfocused | w15), new e18(d15, t11.Unfocused | w15), new e18(c21(a27, r24), t11.Focused | w15), new e18(f19, t11.Focused | w15)];
}
var x17 = Object.freeze({ autoScaleRenderObjects: false, worldSized: true });
function C7(e20, t24, a27, c21) {
  const l22 = e3(c2.get(), e20, a27), m17 = V5(l22, _(c2.get(), c21, l22), a27, f7.get());
  h5(m17, m17);
  const d15 = O2(c2.get(), t24, m17);
  return Math.atan2(d15[1], d15[0]);
}
function V5(e20, t24, r24, n21) {
  const i19 = z2(c2.get(), e20), c21 = z2(c2.get(), t24), l22 = _(c2.get(), i19, c21);
  return n21[0] = i19[0], n21[1] = i19[1], n21[2] = i19[2], n21[3] = 0, n21[4] = c21[0], n21[5] = c21[1], n21[6] = c21[2], n21[7] = 0, n21[8] = l22[0], n21[9] = l22[1], n21[10] = l22[2], n21[11] = 0, n21[12] = r24[0], n21[13] = r24[1], n21[14] = r24[2], n21[15] = 1, n21;
}
function z6(t24, r24) {
  const n21 = t24.getViewForGraphic(r24);
  return r(n21) && "computeAttachmentOrigin" in n21 ? n21.computeAttachmentOrigin(r24, t24.spatialReference) : null;
}
function G6(t24, r24, n21) {
  const o17 = z6(t24, n21);
  r(o17) ? r24.elevationAlignedLocation = o17 : P8(r24, n21.geometry);
}
function P8(e20, r24) {
  if (t(r24)) return;
  const n21 = "mesh" === r24.type ? r24.anchor : A9(r24);
  t(n21) || (e20.location = f11(n21));
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulatorUtils.js
function t18(o17, t24 = f10(o17)) {
  return "on-the-ground" !== t24.mode && !(t(o17.geometry) || !o17.geometry.hasZ);
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/GrabbingState.js
var N;
!function(N4) {
  N4[N4.NONE = 0] = "NONE", N4[N4.ANY = 1] = "ANY", N4[N4.Z = 2] = "Z", N4[N4.XY = 4] = "XY";
}(N || (N = {}));

// node_modules/@arcgis/core/views/3d/interactive/editingTools/ManipulatorType.js
var A13;
!function(A17) {
  A17[A17.TRANSLATE_Z = 0] = "TRANSLATE_Z", A17[A17.TRANSLATE_XY = 1] = "TRANSLATE_XY", A17[A17.SCALE = 2] = "SCALE", A17[A17.ROTATE = 3] = "ROTATE", A17[A17.SCALE_ROTATE = 4] = "SCALE_ROTATE";
}(A13 || (A13 = {}));

// node_modules/@arcgis/core/views/3d/interactive/editingTools/ManipulatorState.js
var a23 = class {
  constructor() {
    this.grabbingState = N.NONE, this.zManipulator = null, this.firstSelected = null, this.numSelected = 0, this.firstGrabbedXY = null;
  }
  update(a27) {
    this.grabbingState = N.NONE, this.zManipulator = null, this.numSelected = 0, this.firstSelected = null, this.firstGrabbedXY = null, a27.forEachManipulator((a28, s19) => {
      if (s19 === A13.TRANSLATE_Z && (this.zManipulator = a28), a28 instanceof ie && (a28.selected && (0 === this.numSelected && (this.firstSelected = a28), this.numSelected++), t(this.firstGrabbedXY) && a28.grabbing && s19 === A13.TRANSLATE_XY && (this.firstGrabbedXY = a28)), a28.grabbing) switch (this.grabbingState |= N.ANY, s19) {
        case A13.TRANSLATE_Z:
          this.grabbingState |= N.Z;
          break;
        case A13.TRANSLATE_XY:
          this.grabbingState |= N.XY;
      }
    });
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/lineGraphicVisualElementUtils.js
function b12(t24) {
  const { view: a27, graphic: n21 } = t24, i19 = new e17({ graphic: n21 }), l22 = j9(t24, i19), r24 = [l22, S11(t24, l22.visualElement, i19), a27.maskOccludee(n21), a27.trackGraphicState(i19)];
  return { visualElement: l22.visualElement, remove: () => r2(r24).remove() };
}
function j9(a27, n21) {
  const { view: i19, graphic: r24 } = a27, o17 = new I4({ view: i19, geometry: L4(r24) ? r24.geometry : null, elevationInfo: f10(r24), attached: false });
  y7.visualElements.lineGraphics.shadowStyle.apply(o17);
  const s19 = () => {
    o17.attached = n21.displaying;
  };
  y7.visualElements.lineGraphics.outline.apply(o17);
  const p25 = [l3(() => n21.displaying, s19), l3(() => n21.isDraped, (e20) => {
    o17.isDraped = e20;
  }), n21.on("changed", () => o17.geometry = L4(r24) ? r24.geometry : null), t2(o17)];
  return s19(), { visualElement: o17, remove: () => r2(p25).remove() };
}
function S11(i19, r24, o17) {
  const { graphic: s19, view: p25 } = i19, m17 = [], d15 = f10(s19), y12 = "on-the-ground" === d15.mode || !d15.offset && "absolute-height" !== d15.mode, E15 = new a23(), b16 = new v8({ view: p25, extensionType: y7.visualElements.zVerticalLine.extensionType, innerWidth: 1, attached: false, writeDepthEnabled: false, renderOccluded: c3.OccludeAndTransparent });
  y7.visualElements.pointGraphics.shadowStyle.apply(b16);
  const j15 = m2(y7.visualElements.heightPlaneAngleCutoff), S14 = new d9({ view: p25, attached: false, angleCutoff: j15 });
  y7.visualElements.heightPlane.apply(S14);
  let T9 = 1, A17 = 1;
  const D11 = () => {
    if (E15.update(i19), !o17.displaying || y12 && (o17.isDraped || !L4(s19) || !s19.geometry.hasZ)) return r24.laserlineEnabled = false, b16.attached = false, void (S14.attached = false);
    r24.laserlineEnabled = true;
    const e20 = E15.grabbingState & N.XY ? y7.visualElements.laserlineAlphaMultiplier : 1;
    e20 !== T9 && (T9 = e20, y7.visualElements.lineGraphics.shadowStyle.apply(r24, e20), y7.visualElements.pointGraphics.shadowStyle.apply(b16, e20));
    const t24 = E15.grabbingState & N.Z ? y7.visualElements.laserlineAlphaMultiplier : 1;
    t24 !== A17 && (A17 = t24, y7.visualElements.heightPlane.apply(S14, t24)), M11(b16, E15), G7(i19, r24, S14, E15);
  };
  y7.visualElements.zVerticalLine.apply(b16), m17.push(o17.on("changed", D11), l3(() => o17.displaying, D11), r24.events.on("attachment-origin-changed", D11), t2(b16), t2(S14));
  const C12 = [], O8 = () => {
    r2(C12).remove(), C12.length = 0, i19.forEachManipulator((e20) => C12.push(e20.events.on("grab-changed", D11))), i19.forEachManipulator((e20) => C12.push(e20.events.on("select-changed", D11))), D11();
  };
  return O8(), m17.push(i19.onManipulatorsChanged(O8), o2(() => r2(C12))), r2(m17);
}
function M11(e20, t24) {
  const a27 = 1 === t24.numSelected ? t24.firstSelected : t24.numSelected > 1 && r(t24.firstGrabbedXY) ? t24.firstGrabbedXY : null;
  r(a27) ? (e20.setStartEndFromWorldDownAtLocation(a27.renderLocation), e20.attached = true) : e20.attached = false;
}
function G7(e20, t24, a27, n21) {
  if (n21.numSelected > 0) {
    o3(T6, 0, 0, 0);
    let t25 = 0;
    e20.forEachManipulator((e21, a28) => {
      a28 === A13.TRANSLATE_XY && e21.selected && e21 instanceof ie && (u(T6, T6, e21.renderLocation), t25++);
    }), t25 > 0 ? (a27.heightManifoldTarget = g(T6, T6, 1 / t25), a27.attached = true) : a27.attached = false;
  } else {
    const n22 = t24.attachmentOrigin;
    r(n22) && e20.view.renderCoordsHelper.toRenderCoords(n22, T6) ? (a27.heightManifoldTarget = T6, a27.attached = true) : a27.attached = false;
  }
}
function L4(e20) {
  return r(e20.geometry) && ("polygon" === e20.geometry.type || "polyline" === e20.geometry.type);
}
var T6 = n4();

// node_modules/@arcgis/core/views/3d/interactive/editingTools/originGraphicVisualElementUtils.js
function M12(t24) {
  const { view: n21, graphic: i19 } = t24, o17 = new e17({ graphic: i19 }), a27 = [], r24 = C8(t24, o17, a27);
  return P9(t24, o17, a27, r24), a27.push(n21.trackGraphicState(o17)), { visualElement: r24, remove() {
    r2(a27).remove();
  } };
}
function P9(e20, n21, s19, p25) {
  const { view: m17, graphic: u13 } = e20, f19 = new v8({ view: m17, extensionType: y7.visualElements.zVerticalLine.extensionType, innerWidth: 1, attached: false, writeDepthEnabled: false, renderOccluded: c3.OccludeAndTransparent });
  y7.visualElements.zVerticalLine.apply(f19);
  const b16 = new d9({ view: m17, intersectsLineInfinite: true, attached: false });
  y7.visualElements.pointGraphics.shadowStyle.apply(b16);
  const A17 = m2(y7.visualElements.heightPlaneAngleCutoff), M16 = new d9({ view: m17, attached: false, angleCutoff: A17 });
  y7.visualElements.heightPlane.apply(M16);
  const P13 = f10(e20.graphic), C12 = h13.fromElevationInfo(P13), R10 = "on-the-ground" === P13.mode || !P13.offset && "absolute-height" !== P13.mode, U8 = new a23();
  let D11 = 1, I7 = 1;
  const T9 = () => {
    U8.update(e20);
    const t24 = G8(u13), i19 = R10 && (n21.isDraped || t(t24) || !t24.hasZ);
    let r24 = true;
    if (!i19 && r(t24)) {
      const e21 = d7(t24, m17.elevationProvider, C12, m17.renderCoordsHelper);
      o3(V6, t24.x, t24.y, e21), jn(V6, t24.spatialReference, V6, m17.renderCoordsHelper.spatialReference), f19.setStartEndFromWorldDownAtLocation(V6), b16.intersectsWorldUpAtLocation = V6;
    } else r24 = false;
    const s20 = U8.grabbingState & N.Z ? y7.visualElements.laserlineAlphaMultiplier : 1;
    s20 !== D11 && (D11 = s20, y7.visualElements.heightPlane.apply(M16, s20));
    const g16 = S2(B3);
    !i19 && n21.displaying && p25.calculateMapBounds(g16) && jn(R(g16, V6), m17.spatialReference, V6, m17.renderCoordsHelper.spatialReference) ? (M16.heightManifoldTarget = V6, M16.attached = true) : M16.attached = false;
    const y12 = U8.grabbingState & N.XY ? y7.visualElements.laserlineAlphaMultiplier : 1;
    y12 !== I7 && (I7 = y12, y7.visualElements.pointGraphics.shadowStyle.apply(b16, y12));
    const j15 = r24 && n21.displaying && !i19;
    b16.attached = j15, f19.attached = j15;
  };
  s19.push(l3(() => [n21.displaying, n21.isDraped], T9), n21.on("changed", T9)), e20.forEachManipulator((e21) => {
    s19.push(e21.events.on("grab-changed", T9));
  }), s19.push(t2(b16)), s19.push(t2(f19)), s19.push(t2(M16)), T9();
}
function C8(e20, n21, i19) {
  const { view: o17, graphic: a27 } = e20, r24 = new S8({ view: o17, geometry: G8(a27), elevationInfo: f10(a27), attached: false });
  return R6(e20, r24, n21, i19), i19.push(t2(r24)), r24;
}
function G8(e20) {
  const t24 = e20.geometry;
  return t(t24) ? null : "point" === t24.type ? t24 : "mesh" === t24.type ? t24.anchor.clone() : null;
}
function R6(e20, t24, n21, i19) {
  const o17 = () => t24.attached = n21.displaying;
  U5(e20, t24, n21, i19), y7.visualElements.pointGraphics.outline.apply(t24), i19.push(l3(() => n21.displaying, o17, h4));
}
function U5(e20, t24, i19, o17) {
  const { view: r24, graphic: s19 } = e20;
  let l22 = null;
  const p25 = (e21) => {
    r(l22) && (l22.remove(), l22 = null), i19.isDraped && r(e21) && (l22 = D6(r24, e21, () => {
      t24.geometry = e21;
    }));
  }, c21 = () => {
    const e21 = G8(s19);
    p25(e21), t24.geometry = e21;
  };
  o17.push(i19.on("changed", c21), o2(() => l22)), c21();
}
function D6(e20, t24, n21) {
  const i19 = e20.elevationProvider.spatialReference;
  gn(t24, V6, i19);
  const o17 = V6[0], a27 = V6[1];
  return e20.elevationProvider.on("elevation-change", (e21) => {
    w2(e21.extent, o17, a27) && n21();
  });
}
var V6 = n4();
var B3 = a6();

// node_modules/@arcgis/core/views/3d/interactive/editingTools/visualElementUtils.js
function i13(i19) {
  switch (e2(i19.graphic.geometry).type) {
    case "point":
    case "mesh":
      return M12(i19);
    case "polygon":
    case "polyline":
      return b12(i19);
    default:
      return null;
  }
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/config.js
var t19 = [1, 0.5, 0];
var a24 = 128;
var e19 = 70;
var h18 = 80;
var M13 = 0.02;
var c15 = 54;
var o14 = 100;
var I5 = Math.ceil(e19 / 3 * 2);
var P10 = 160;
var i14 = 0.5;
var l18 = 24;
var n16 = 9;
var p21 = P10 + 30;
var r19 = P10 + 53;
var s14 = 60;
var x18 = 23;
var b13 = 5 * Math.PI / 12;
var d11 = 1 * Math.PI / 3;
var f16 = 10;
var g12 = 0.2;
var j10 = 30;
var k5 = 53;
var m13 = 0.2;
var q6 = 0.3;
var u10 = 200;
var v11 = 3;
var w11 = 1e6;

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/Manipulation.js
var t20 = class {
  constructor() {
    this._available = true;
  }
  set location(a27) {
    this._forEachManipulator3D((t24) => t24.location = a27);
  }
  set elevationAlignedLocation(a27) {
    this._forEachManipulator3D((t24) => t24.elevationAlignedLocation = a27);
  }
  set elevationInfo(a27) {
    this._forEachManipulator3D((t24) => t24.elevationInfo = a27);
  }
  get renderLocation() {
    let a27;
    return this._forEachManipulator3D((t24) => {
      a27 || (a27 = t24.renderLocation);
    }), a27;
  }
  get available() {
    return this._available;
  }
  set available(a27) {
    this._available = a27, this._forEachManipulator3D((t24) => t24.available = a27);
  }
  get hovering() {
    return this.someManipulator((a27) => a27.hovering);
  }
  get grabbing() {
    return this.someManipulator((a27) => a27.grabbing);
  }
  get dragging() {
    return this.someManipulator((a27) => a27.dragging);
  }
  hasManipulator(a27) {
    return this.someManipulator((t24) => t24 === a27);
  }
  someManipulator(a27) {
    let t24 = false;
    return this.forEachManipulator((i19) => {
      !t24 && a27(i19) && (t24 = true);
    }), t24;
  }
  _forEachManipulator3D(t24) {
    this.forEachManipulator((i19, o17) => {
      i19 instanceof ie && t24(i19, o17);
    });
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/dragEventPipeline3D.js
function C9(e20, r24) {
  return D7(e20, () => r24);
}
function T7(e20) {
  return D7(e20, (e21) => e21.plane);
}
function D7(t24, s19) {
  const c21 = n4(), l22 = n4();
  let i19 = false;
  return (a27) => {
    const u13 = s19(a27);
    if ("start" === a27.action) {
      const r24 = d2(a27.screenStart, l4(t6.get())), s20 = s13(t24.state.camera, r24, L5);
      r(s20) && (i19 = x5(u13, s20, c21));
    }
    if (!i19) return null;
    const p25 = d2(a27.screenEnd, l4(t6.get())), m17 = s13(t24.state.camera, p25, L5);
    return t(m17) ? null : x5(u13, m17, l22) ? { ...a27, renderStart: c21, renderEnd: l22, plane: u13, ray: m17 } : null;
  };
}
function M14(t24, n21, o17 = 0, c21 = null, l22 = null) {
  let i19 = null;
  return (a27) => {
    if ("start" === a27.action && (i19 = t24.sceneIntersectionHelper.intersectElevationFromScreen(i3(a27.screenStart.x, a27.screenStart.y), n21, o17, l22), r(i19) && r(c21) && !pn(i19, i19, c21))) return null;
    if (t(i19)) return null;
    const u13 = t24.sceneIntersectionHelper.intersectElevationFromScreen(i3(a27.screenEnd.x, a27.screenEnd.y), n21, o17, l22);
    return r(u13) ? r(c21) && !pn(u13, u13, c21) ? null : { ...a27, mapStart: i19, mapEnd: u13 } : null;
  };
}
function N2(e20, r24, t24, n21 = null, o17 = null) {
  return M14(e20, t24, d5(r24, e20, t24), n21, o17);
}
function P11(e20, r24, t24, n21 = null, o17 = null) {
  return N2(e20, t24, f10(r24), n21, o17);
}
function h19(r24, t24, n21, o17) {
  const s19 = t24.toMap(r24.screenStart, { include: [n21] });
  return r(s19) ? P11(t24, n21, s19, o17) : null;
}
function k6(e20, r24) {
  const t24 = J2, n21 = K, o17 = p6();
  e20.renderCoordsHelper.worldUpAtPosition(r24, t24);
  const s19 = _(o17, t24, e3(n21, r24, e20.state.camera.eye));
  return _(s19, s19, t24), _2(r24, s19, o17);
}
function G9(e20, r24, t24) {
  let n21 = null;
  const o17 = new U3();
  return o17.next(C9(e20, k6(e20, r24))).next(A14(e20, r24)).next(F6(e20, t24)).next((e21) => {
    e21.mapEnd.x = e21.mapStart.x, e21.mapEnd.y = e21.mapStart.y, n21 = e21;
  }), (e21) => (n21 = null, o17.execute(e21), n21);
}
function A14(e20, r24) {
  const t24 = n4(), n21 = s2(r24);
  e20.renderCoordsHelper.worldUpAtPosition(r24, t24);
  const o17 = n4(), s19 = n4(), i19 = (o18) => {
    if (e3(o18, o18, r24), e7(t24, o18, o18), "global" === e20.viewingMode) {
      s2(o18) * Math.sign(P(t24, o18)) < 1e-3 - n21 && e3(o18, g(o18, t24, 1e-3), r24);
    }
    return u(o18, o18, r24), o18;
  };
  return (e21) => (e21.renderStart = i19(r4(o17, e21.renderStart)), e21.renderEnd = i19(r4(s19, e21.renderEnd)), e21);
}
function F6(r24, t24) {
  const n21 = r24.renderCoordsHelper;
  return (r25) => {
    const o17 = n21.fromRenderCoords(r25.renderStart, t24), s19 = n21.fromRenderCoords(r25.renderEnd, t24);
    return r(o17) && r(s19) ? { ...r25, mapStart: o17, mapEnd: s19 } : null;
  };
}
var q7;
!function(e20) {
  e20[e20.GROUND = 0] = "GROUND", e20[e20.OTHER = 1] = "OTHER";
}(q7 || (q7 = {}));
var J2 = n4();
var K = n4();
var L5 = d4();

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/moveUtils.js
function r20(t24, r24, a27, c21) {
  const i19 = t24.graphic, s19 = (t25, e20) => r24({ action: t25, graphic: i19, dxScreen: e20.screenDeltaX, dyScreen: e20.screenDeltaY });
  return a27((t25, r25, a28) => (r25.next((t26) => ("start" === t26.action && s19("start", t26), t26)).next(E10(i19, c21)).next((t26) => {
    switch (t26.action) {
      case "start":
      case "update":
        (t26.translationX || t26.translationY || t26.translationZ) && s19("update", t26);
        break;
      case "end":
        s19("end", t26);
    }
    return t26;
  }), { steps: r25, cancel: a28 = a28.next(M5(i19)).next((t26) => (s19("end", { screenDeltaX: 0, screenDeltaY: 0 }), t26)) }));
}
function a25(e20) {
  if (t(e20) || "polyline" !== e20.type && "polygon" !== e20.type) return 0;
  const n21 = ("polyline" === e20.type ? e20.paths : e20.rings)[0];
  if (!n21 || n21.length < 2) return 0;
  const r24 = n21[0], a27 = n21[1];
  return Math.atan2(a27[1] - r24[1], a27[0] - r24[0]);
}
function c16(e20) {
  if (t(e20) || t(e20.axis)) return 1;
  const { mapStart: n21, mapEnd: r24, axis: a27 } = e20, c21 = [r24.x - n21.x, r24.y - n21.y];
  return c21[0] * a27[0] + c21[1] * a27[1] > 0 ? 1 : -1;
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/MoveXYAxisManipulation.js
var B4 = class extends t20 {
  constructor(t24) {
    super(), this._handles = new t3(), this._arrowManipulatorInfos = new Array(), this._opaqueMaterial = this._createMaterial(), this._transparentMaterial = this._createMaterial(0.5), this._angle = 0, this._scale = 1, this._radius = e19, this._updateAfterDrag = false, this.events = new n3(), this._tool = t24.tool, this._view = t24.view, null != t24.radius && (this._radius = t24.radius), this._createManipulators(), this.forEachManipulator((t25) => this._tool.manipulators.add(t25));
  }
  set orthogonalAvailable(t24) {
    this._arrowManipulatorInfos[1].manipulator.available = t24, this._arrowManipulatorInfos[3].manipulator.available = t24;
  }
  destroy() {
    this._handles = a(this._handles), this.forEachManipulator((t24) => {
      this._tool.manipulators.remove(t24), t24.destroy();
    }), this._tool = null, this._view = null, this._arrowManipulatorInfos.length = 0;
  }
  forEachManipulator(t24) {
    for (const { manipulator: a27 } of this._arrowManipulatorInfos) t24(a27, A13.TRANSLATE_XY);
  }
  createGraphicDragPipeline(t24, a27, r24) {
    const e20 = a27.graphic, i19 = f10(e20), s19 = e2(e20.geometry).spatialReference;
    return r20(a27, r24, (a28) => this.createDragPipeline((r25, e21, i20, o17, s20) => ({ steps: e21, cancel: i20 } = t24(r25, e21, i20, o17, s20), a28(r25, e21, i20)), i19, s19, e20), this._view.state.viewingMode);
  }
  createDragPipeline(t24, a27, r24, i19) {
    return r2(this._arrowManipulatorInfos.map(({ manipulator: e20 }, o17) => x7(e20, (e21, s19, n21, l22, p25) => {
      const u13 = s19.next((t25) => ({ ...t25, manipulatorType: A13.TRANSLATE_XY })).next(C(this._view, e21.elevationAlignedLocation)).next(N2(this._view, e21.elevationAlignedLocation, a27, r24, i19)).next(g8(e21.location, this.angle + (o17 + 1) * Math.PI * 0.5)).next(b6());
      t24(e21, u13, n21, l22, p25);
    })));
  }
  get angle() {
    return this._angle;
  }
  set angle(t24) {
    this._angle = t24, this.dragging ? this._updateAfterDrag = true : this._updateManipulatorTransform();
  }
  get displayScale() {
    return this._scale;
  }
  set displayScale(t24) {
    this._scale = t24, this._updateManipulatorTransform();
  }
  get radius() {
    return this._radius;
  }
  set radius(t24) {
    this._radius !== t24 && (this._radius = t24, this._updateManipulators());
  }
  _updateManipulators() {
    for (let t24 = 0; t24 < this._arrowManipulatorInfos.length; t24++) this._updateArrowManipulator(this._arrowManipulatorInfos[t24], t24);
    this._updateManipulatorTransform();
  }
  _updateArrowManipulator({ manipulator: t24, transform: a27 }, r24) {
    const e20 = this._radius / e19, i19 = c15 * e20, o17 = i19 * Math.sqrt(3) / 2, s19 = pt(this._opaqueMaterial, o17, i19 / 2, i19 / 2, M13);
    Ot(s19, q2(f7.get(), o3(c2.get(), 0, -o17 / 3, 0))), t24.renderObjects = [new e18(s19, t11.Focused), new e18(s19.instantiate({ material: this._transparentMaterial }), t11.Unfocused)], t24.radius = o17 / 3 * 2 * 1.2;
    const u13 = R2(f7.get(), r24 * Math.PI / 2), h25 = q2(f7.get(), o3(c2.get(), 0, o14 * e20, 0));
    c(a27, u13, h25);
  }
  _createManipulators() {
    for (let t24 = 0; t24 < 4; t24++) {
      const a27 = this._createArrowManipulator(t24);
      this._arrowManipulatorInfos.push(a27);
    }
    this._updateManipulatorTransform();
  }
  _updateManipulatorTransform() {
    const t24 = this.angle, a27 = p4(f7.get(), t24, r3(0, 0, 1));
    if (t(a27)) return;
    const r24 = g2(f7.get(), o3(c2.get(), this.displayScale, this.displayScale, this.displayScale)), e20 = c(f7.get(), r24, a27);
    for (const i19 of this._arrowManipulatorInfos) {
      const t25 = c(f7.get(), e20, i19.transform);
      i19.manipulator.modelTransform = t25;
    }
  }
  _createArrowManipulator(t24) {
    const a27 = new ie({ view: this._view, autoScaleRenderObjects: false, worldOriented: true, focusMultiplier: 1, touchMultiplier: 1, collisionType: { type: "disc", direction: r3(0, 0, 1) } }), r24 = { manipulator: a27, transform: e6() };
    return this._updateArrowManipulator(r24, t24), this._handles.add(a27.events.on("drag", (t25) => {
      this._updateAfterDrag && "end" === t25.action && !this.dragging && (this._updateManipulatorTransform(), this._updateAfterDrag = false);
    })), r24;
  }
  _createMaterial(a27 = 1) {
    const r24 = l8.toUnitRGBA(r18.main);
    return r24[3] *= a27, new f13({ color: r24, transparent: 1 !== a27, cullFace: n8.Back, renderOccluded: c3.Transparent });
  }
  get test() {
    return { arrowManipulators: this._arrowManipulatorInfos.map(({ manipulator: t24 }) => t24) };
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/snapping/SnapToScene.js
var s15 = class {
  constructor() {
    this._view = null, this._elevationInfo = null, this._lastDragEvent = null, this._next = null, this._enabled = false;
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(e20) {
    if (this._enabled !== e20 && r(this._lastDragEvent) && r(this._next)) {
      const n21 = this._lastDragEvent.mapEnd, s19 = this._snap(this._lastDragEvent.screenEnd);
      if (r(s19)) {
        const t24 = { action: "update", mapStart: this._lastDragEvent.mapStart, mapEnd: true === e20 ? s19 : n21, screenStart: this._lastDragEvent.screenEnd, screenEnd: this._lastDragEvent.screenEnd };
        this._next.execute(t24);
      }
    }
    this._enabled = e20;
  }
  _snap(n21) {
    const s19 = r(this._view) ? this._view.toMap(n21, { exclude: [] }) : null;
    return r(s19) && r(this._view) && (s19.z = d5(s19, this._view, this._elevationInfo)), s19;
  }
  createDragEventPipelineStep(e20, s19) {
    this._view = e20, this._elevationInfo = s19, this._lastDragEvent = null;
    const a27 = new U3();
    this._next = a27;
    return [(e21) => {
      if (this._lastDragEvent = "end" !== e21.action ? { ...e21 } : null, this._enabled) {
        const n21 = this._snap(e21.screenEnd);
        return r(n21) ? { action: e21.action, mapStart: e21.mapStart, mapEnd: n21, screenStart: e21.screenStart, screenEnd: e21.screenEnd } : null;
      }
      return { action: e21.action, mapStart: e21.mapStart, mapEnd: e21.mapEnd, screenStart: e21.screenStart, screenEnd: e21.screenEnd };
    }, a27];
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/MoveXYDiscManipulation.js
var k7 = class extends t20 {
  constructor(t24) {
    super(), this._snapToScene = new s15(), this._discMaterial = this._createMaterial(), this._discMaterialTransparent = this._createMaterial(0.5), this._scale = 1, this._radius = e19, this._view = t24.view, this._tool = t24.tool, null != t24.snapToScene && (this.snapToScene = t24.snapToScene), null != t24.radius && (this._radius = t24.radius), this._createManipulator(), this.forEachManipulator((t25) => this._tool.manipulators.add(t25));
  }
  destroy() {
    this.forEachManipulator((t24) => {
      this._tool.manipulators.remove(t24), t24.destroy();
    }), this._tool = null, this._view = null, this._manipulator = null;
  }
  forEachManipulator(t24) {
    t24(this._manipulator, A13.TRANSLATE_XY);
  }
  get displayScale() {
    return this._scale;
  }
  set displayScale(t24) {
    this._scale = t24, this._updateManipulatorTransform();
  }
  get snapToScene() {
    return this._snapToScene.enabled;
  }
  set snapToScene(t24) {
    this._snapToScene.enabled = t24;
  }
  get radius() {
    return this._radius;
  }
  set radius(t24) {
    t24 !== this._radius && (this._radius = t24, this._updateManipulator());
  }
  createGraphicDragPipeline(t24, i19, a27) {
    const r24 = i19.graphic, s19 = f10(r24), o17 = e2(r24.geometry).spatialReference;
    return r20(i19, a27, (e20) => this.createDragPipeline((i20, a28, r25, s20, o18) => ({ steps: a28, cancel: r25 } = t24(i20, a28, r25, s20, o18), e20(i20, a28, r25)), s19, o17, r24), this._view.state.viewingMode);
  }
  createDragPipeline(t24, e20, i19, a27) {
    const r24 = this._view;
    return x7(this._manipulator, (s19, o17, n21, l22, p25) => {
      const c21 = o17.next(C(r24, s19.elevationAlignedLocation)).next(N2(r24, s19.elevationAlignedLocation, e20, i19, a27)).next(...this._snapToScene.createDragEventPipelineStep(r24, e20)).next((t25) => ({ ...t25, manipulatorType: A13.TRANSLATE_XY })).next(b6());
      t24(s19, c21, n21, l22, p25);
    });
  }
  _updateManipulatorTransform() {
    const t24 = g2(f7.get(), o3(c2.get(), this.displayScale, this.displayScale, this.displayScale));
    this._manipulator.modelTransform = t24;
  }
  _createManipulator() {
    const t24 = this._view;
    this._manipulator = new ie({ view: t24, worldSized: false, autoScaleRenderObjects: false, focusMultiplier: 1, touchMultiplier: 1, collisionType: { type: "disc", direction: r3(0, 0, 1) }, worldOriented: true }), this._updateManipulator();
  }
  _updateManipulator() {
    const t24 = ut(this._discMaterial, M13, 1, a24, r3(0, 0, 1), r3(0, 0, 0));
    t24.transformation = g2(e6(), r3(this._radius, this._radius, this._radius)), this._manipulator.renderObjects = [new e18(t24, t11.Focused), new e18(t24.instantiate({ material: this._discMaterialTransparent }), t11.Unfocused)], this._manipulator.radius = h18 * (this._radius / e19);
  }
  _createMaterial(e20 = 1) {
    const i19 = l8.toUnitRGBA(r18.main);
    return i19[3] *= e20, new f13({ color: i19, transparent: 1 !== e20, cullFace: n8.Back, renderOccluded: c3.Transparent });
  }
  get test() {
    return { discManipulator: this._manipulator };
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/MoveZManipulation.js
var R7 = class extends t20 {
  constructor(t24) {
    super(), this._radius = e19, this.events = new n3(), this._tool = t24.tool, this._view = t24.view, null != t24.radius && (this._radius = t24.radius), this._createManipulator(), this.forEachManipulator((t25) => this._tool.manipulators.add(t25));
  }
  destroy() {
    this.forEachManipulator((t24) => {
      this._tool.manipulators.remove(t24), t24.destroy();
    });
  }
  forEachManipulator(t24) {
    t24(this._manipulator, A13.TRANSLATE_Z);
  }
  createGraphicDragPipeline(t24, e20, i19) {
    const a27 = e2(e20.graphic.geometry).spatialReference;
    return r20(e20, i19, (e21) => this.createDragPipeline((i20, r24, a28, o17, s19) => ({ steps: r24, cancel: a28 } = t24(i20, r24, a28, o17, s19), e21(i20, r24, a28)), a27), this._view.state.viewingMode);
  }
  createDragPipeline(t24, e20) {
    const i19 = this._view;
    return x7(this._manipulator, (r24, a27, o17, s19, n21) => {
      const l22 = a27.next((t25) => ({ ...t25, manipulatorType: A13.TRANSLATE_Z })).next(G9(i19, r24.renderLocation, e20)).next(b6());
      t24(r24, l22, o17, s19, n21);
    });
  }
  get radius() {
    return this._radius;
  }
  set radius(t24) {
    t24 !== this._radius && (this._radius = t24, this._updateManipulator());
  }
  _updateManipulator() {
    const e20 = this._radius / e19, i19 = y7.zManipulator.height * e20, r24 = y7.zManipulator.coneHeight * e20, l22 = y7.zManipulator.coneWidth * e20, c21 = y7.zManipulator.width * e20, p25 = [r3(0, 0, 0), r3(0, 0, i19)], u13 = [r3(0, 0, 0), r3(0, 0, i19 + r24)], m17 = (t24) => {
      const e21 = e6();
      if (i4(e21, e21, [0, 0, i19]), l5(e21, e21, Math.PI / 2), t24) {
        const i20 = 1 + 2 * t24 / l22;
        f6(e21, e21, [i20, i20, i20]);
      }
      return e21;
    }, d15 = m17(0), h25 = (e21, i20) => {
      const r25 = q3(y7.zManipulator.color, i20);
      return [r25.r / 255, r25.g / 255, r25.b / 255, y7.zManipulator.color.a * e21];
    }, _9 = R5(h25(1, 0.25), c3.Occlude), M16 = R5(h25(1, 0), c3.Occlude), v15 = R5(h25(0.7, 0), y7.zManipulator.renderOccluded), y12 = R5(h25(0.85, 0), y7.zManipulator.renderOccluded), z8 = ct(_9, p25, c21 / 2, 16, false), E15 = lt(_9, r24, l22 / 2, 16, false);
    E15.transformation = d15, this._manipulator.renderObjects = [new e18(E15, t11.Unfocused), new e18(z8, t11.Unfocused), new e18(E15.instantiate({ material: M16 }), t11.Focused), new e18(z8.instantiate({ material: M16 }), t11.Focused), new e18(E15.instantiate({ material: v15 }), t11.Unfocused), new e18(z8.instantiate({ material: v15 }), t11.Unfocused), new e18(E15.instantiate({ material: y12 }), t11.Focused), new e18(z8.instantiate({ material: y12 }), t11.Focused)], this._manipulator.radius = c21 / 2 + 2, this._manipulator.collisionType = { type: "line", paths: [u13] };
  }
  _createManipulator() {
    const t24 = new ie({ view: this._view, autoScaleRenderObjects: false, worldSized: false, selectable: false, cursor: "ns-resize", elevationInfo: this.elevationInfo, worldOriented: true, collisionPriority: 1.6 });
    t24.applyObjectTransform = (t25) => {
      const e20 = this._view.state.camera, r24 = x20;
      this._view.renderCoordsHelper.toRenderCoords(this._manipulator.elevationAlignedLocation, r24);
      const a27 = U(e20.eye, r24), o17 = e20.computeRenderPixelSizeAtDist(a27), s19 = e3(D8, r24, e20.eye);
      z2(s19, s19);
      const n21 = k8;
      this._view.renderCoordsHelper.worldUpAtPosition(x20, n21);
      const h25 = Math.abs(P(s19, n21)), f19 = _(D8, s19, n21), _9 = _(D8, f19, n21), w15 = a5(h25, 0.01, 1), j15 = 1 - Math.sqrt(1 - w15 * w15) / w15 / e20.fullWidth, M16 = this._radius / e19, v15 = y7.zManipulator.width * M16;
      g(_9, z2(_9, _9), (1 / j15 - 1) * a27 + o17 * v15), t25[12] -= D8[0], t25[13] -= D8[1], t25[14] -= D8[2];
    }, this._manipulator = t24, this._updateManipulator();
  }
  get test() {
    return { manipulator: this._manipulator };
  }
};
var x20 = n4();
var D8 = n4();
var k8 = n4();

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/MoveManipulation.js
var u11 = class extends t20 {
  constructor(a27) {
    super(), this._handles = new t3(), this._interactive = true;
    const { tool: t24, view: n21, snapToScene: e20, radius: s19 } = a27;
    this._view = n21, this.xyManipulation = new k7({ tool: t24, view: n21, snapToScene: e20, radius: s19 }), this.xyAxisManipulation = new B4({ tool: t24, view: n21, radius: s19 }), this.zManipulation = new R7({ tool: t24, view: n21, radius: s19 }), this.xyManipulation.available = a27.xyAvailable, this.xyAxisManipulation.available = a27.xyAxisAvailable, this.zManipulation.available = a27.zAvailable, this._autoHideXYAxis(), this.forEachManipulator((i19) => this._handles.add(i19.events.on("grab-changed", () => this._updateManipulatorInteractivity())));
  }
  destroy() {
    this._handles.destroy(), this.xyManipulation.destroy(), this.xyAxisManipulation.destroy(), this.zManipulation.destroy();
  }
  createGraphicDragPipeline(i19, t24, n21) {
    return r2([this.xyManipulation.createGraphicDragPipeline((a27, t25, n22, e20, s19) => i19(M15.XY, a27, t25, n22, e20, s19), t24, n21), this.xyAxisManipulation.createGraphicDragPipeline((a27, t25, n22, e20, s19) => i19(M15.XY_AXIS, a27, t25, n22, e20, s19), t24, n21), this.zManipulation.createGraphicDragPipeline((a27, t25, n22, e20, s19) => i19(M15.Z, a27, t25, n22, e20, s19), t24, n21)]);
  }
  createDragPipeline(i19, t24, n21, e20) {
    return r2([this.xyManipulation.createDragPipeline((a27, t25, n22, e21, s19) => i19(M15.XY, a27, t25, n22, e21, s19), t24, n21, e20), this.xyAxisManipulation.createDragPipeline((a27, t25, n22, e21, s19) => i19(M15.XY_AXIS, a27, t25, n22, e21, s19), t24, n21, e20), this.zManipulation.createDragPipeline((a27, t25, n22, e21, s19) => i19(M15.Z, a27, t25, n22, e21, s19), n21)]);
  }
  set snapToScene(i19) {
    this.xyManipulation.snapToScene = i19;
  }
  set angle(i19) {
    this.xyAxisManipulation.angle = i19;
  }
  set interactive(i19) {
    this._interactive !== i19 && (this._interactive = i19, this._updateManipulatorInteractivity());
  }
  set radius(i19) {
    this.xyAxisManipulation.radius = i19, this.xyManipulation.radius = i19, this.zManipulation.radius = i19;
  }
  set displayScale(i19) {
    this.xyManipulation.displayScale = i19, this.xyAxisManipulation.displayScale = i19;
  }
  forEachManipulator(i19) {
    this.xyManipulation.forEachManipulator((a27) => i19(a27, A13.TRANSLATE_XY)), this.xyAxisManipulation.forEachManipulator((a27) => i19(a27, A13.TRANSLATE_XY)), this.zManipulation.forEachManipulator((a27) => i19(a27, A13.TRANSLATE_Z));
  }
  get _xyAxisVisible() {
    const i19 = this.xyManipulation.someManipulator((i20) => i20.focused) || this.xyAxisManipulation.someManipulator((i20) => i20.focused);
    return this._view.inputManager && "touch" === this._view.inputManager.latestPointerType || i19;
  }
  _autoHideXYAxis() {
    const i19 = this.xyAxisManipulation, a27 = this.xyManipulation;
    if (has("esri-mobile")) return;
    const t24 = [];
    a27.forEachManipulator((i20) => t24.push(i20)), i19.forEachManipulator((i20) => t24.push(i20));
    const e20 = () => {
      const a28 = [];
      this._xyAxisVisible || i19.forEachManipulator((i20) => a28.push(i20.disableDisplay())), this._handles.remove(c17), this._handles.add(a28, c17);
    };
    for (const n21 of t24) this._handles.add(n21.events.on("focus-changed", e20));
    this._view.inputManager && this._handles.add(f5(() => {
      var _a;
      return (_a = this._view.inputManager) == null ? void 0 : _a.latestPointerType;
    }, e20)), e20();
  }
  _updateManipulatorInteractivity() {
    const i19 = this.grabbing;
    this.forEachManipulator((a27) => {
      a27.interactive = !i19 && this._interactive || a27.grabbing;
    });
  }
  static radiusForSymbol(i19) {
    const a27 = r(i19) && "point-3d" === i19.type && i19.symbolLayers;
    return !!a27 && a27.length > 0 && a27.some((i20) => "icon" === i20.type) ? I5 : e19;
  }
};
var c17 = "disable-xy-axis-display";
var M15;
!function(i19) {
  i19[i19.XY = 0] = "XY", i19[i19.XY_AXIS = 1] = "XY_AXIS", i19[i19.Z = 2] = "Z";
}(M15 || (M15 = {}));

// node_modules/@arcgis/core/views/3d/interactive/editingTools/manipulations/MoveXYGraphicManipulation.js
var l19 = class extends t20 {
  constructor(t24) {
    super(), this._view = t24.view, this._tool = t24.tool, this._graphicState = t24.graphicState, this._createManipulator(), this.forEachManipulator((t25) => this._tool.manipulators.add(t25));
  }
  destroy() {
    this.forEachManipulator((t24) => {
      this._tool.manipulators.remove(t24), t24.destroy();
    }), this._tool = null, this._view = null, this._manipulator = null, this._graphicState = null;
  }
  forEachManipulator(t24) {
    t24(this._manipulator, A13.TRANSLATE_XY);
  }
  createGraphicDragPipeline(t24) {
    return r20(this._graphicState, t24, (t25) => this.createDragPipeline(t25), this._view.state.viewingMode);
  }
  createDragPipeline(e20) {
    const r24 = this._view, a27 = this._graphicState.graphic, p25 = r(a27.geometry) ? a27.geometry.spatialReference : null;
    return x7(this._manipulator, (t24, o17, l22, h25, c21) => {
      const m17 = o17.next(h19(c21, r24, a27, p25)).next(D2()).next(b6());
      e20(t24, m17, l22, h25, c21);
    });
  }
  _createManipulator() {
    const t24 = this._view, i19 = this._graphicState.graphic;
    this._manipulator = new j6({ graphic: i19, view: t24, selectable: true, cursor: "move" });
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/moveGraphic/GraphicMoveTool.js
var L6 = class {
  constructor(t24) {
    this.allGraphics = t24, this.type = "graphic-move-start";
  }
};
var N3 = class {
  constructor(t24, i19, e20) {
    this.dx = t24, this.dy = i19, this.allGraphics = e20, this.type = "graphic-move";
  }
};
var q8 = class {
  constructor(t24) {
    this.allGraphics = t24, this.type = "graphic-move-stop";
  }
};
var B5 = class extends a10(n3.EventedMixin(p12)) {
  constructor(t24) {
    super(t24), this.graphics = new j(), this.enableZ = true, this.tooltipOptions = new p10(), this.type = "move-3d", this._tooltip = null;
  }
  initialize() {
    const { graphics: t24, view: i19 } = this;
    this.addHandles([t24.on("change", () => this._refreshManipulators()), l3(() => this.tooltipOptions.enabled, (t25) => {
      this._tooltip = t25 ? new m6({ view: i19 }) : a(this._tooltip);
    }, w3)]), this._refreshManipulators(), this.finishToolCreation();
  }
  destroy() {
    this._tooltip = a(this._tooltip), this._moveManipulation = a(this._moveManipulation), this.graphics.removeAll(), this._set("view", null);
  }
  get updating() {
    return this.updatingHandles.updating;
  }
  reset() {
  }
  _refreshManipulators() {
    var _a;
    this.handles.removeAll(), (_a = this._moveManipulation) == null ? void 0 : _a.destroy(), this.manipulators.removeAll();
    const t24 = this.graphics.toArray().filter((t25) => i8(t25) === P4.SUPPORTED).map((t25) => new J3(t25));
    t24.length && (this._createManipulators(t24), this._createVisualElements(t24), this.handles.add(t24.map((t25) => this.view.trackGraphicState(t25.state))), this._updateMoveManipulation(t24));
  }
  _createManipulators(t24) {
    for (const i19 of t24) {
      const e20 = i19.state;
      i19.manipulationXY = new l19({ tool: this, view: this.view, graphicState: e20 }), i19.manipulationXY.forEachManipulator((t25) => this.handles.add([t25.events.on("immediate-click", (t26) => {
        this.emit("immediate-click", { ...t26, graphic: e20.graphic }), t26.stopPropagation();
      }), t25.events.on("grab-changed", ({ action: t26 }) => {
        const { tooltipOptions: i20, _tooltip: e21 } = this;
        t(e21) || ("start" === t26 ? e21.info = new r16({ tooltipOptions: i20 }) : e21.clear());
      })])), this.handles.add(i19.manipulationXY.createDragPipeline((i20, e21, o17, a27) => this._buildDragEventPipeline(t24, M15.XY, i20, e21, o17, a27)));
    }
    this._createMoveManipulation(t24);
  }
  _createMoveManipulation(t24) {
    const i19 = new u11({ tool: this, view: this.view, snapToScene: false, xyAvailable: true, xyAxisAvailable: true, zAvailable: true, radius: 1 === t24.length ? u11.radiusForSymbol(t24[0].graphic.symbol) : e19 });
    this._moveManipulation = i19, i19.elevationInfo = { mode: "absolute-height", offset: 0 }, i19.forEachManipulator((t25) => {
      this.handles.add(t25.events.on("immediate-click", (e21) => {
        i19.zManipulation.hasManipulator(t25) || 1 !== this.graphics.length || this.emit("immediate-click", { ...e21, graphic: this.graphics.getItemAt(0) }), e21.stopPropagation();
      }));
    });
    const e20 = (i20) => (e21) => {
      this.handles.add(e21.events.on("focus-changed", ({ action: e22 }) => {
        const o18 = this._tooltip;
        t(o18) || ("focus" === e22 ? this._updateMoveTooltip(t24, i20) : o18.clear());
      }));
    };
    this._moveManipulation.xyManipulation.forEachManipulator(e20(M15.XY)), this._moveManipulation.xyAxisManipulation.forEachManipulator(e20(M15.XY_AXIS)), this._moveManipulation.zManipulation.forEachManipulator(e20(M15.Z));
    const o17 = () => this._updateMoveManipulation(t24);
    for (const n21 of t24) this.handles.add([n21.state.on("changed", o17), l3(() => n21.state.displaying, o17)]);
    const a27 = t24[t24.length - 1];
    this.handles.add(a27.state.on("changed", () => this._updateMoveManipulationAngle(a27))), this.handles.add(i19.createDragPipeline((i20, e21, o18, a28, n21) => this._buildDragEventPipeline(t24, i20, e21, o18, a28, n21), f10(a27.graphic), e2(a27.graphic.geometry).spatialReference, a27.graphic)), this._updateMoveManipulationAngle(a27);
  }
  _createVisualElements(t24) {
    for (const i19 of t24) {
      const e20 = i19.graphic, o17 = i13({ view: this.view, graphic: e20, forEachManipulator: (t25) => {
        var _a;
        (_a = i19.manipulationXY) == null ? void 0 : _a.forEachManipulator(t25), this._moveManipulation.forEachManipulator(t25);
      }, onManipulatorsChanged: () => n2() });
      t(o17) || (i19.geometryRepresentation = o17.visualElement, i19.geometryRepresentation instanceof I4 && this.handles.add([i19.geometryRepresentation.events.on("attachment-origin-changed", () => {
        i19.state.isDraped || this._updateMoveManipulation(t24);
      }), l3(() => i19.state.isDraped, () => this._updateMoveManipulation(t24))]), this.handles.add(o17));
    }
  }
  _updateMoveManipulationAngle(t24) {
    this._moveManipulation.angle = a25(t24.graphic.geometry);
  }
  _updateMoveManipulation(t24) {
    const i19 = M2(0, 0, 0, this.view.spatialReference);
    let e20 = 0, o17 = false;
    const a27 = this._moveManipulation;
    for (const n21 of t24) {
      if (!n21.state.displaying) continue;
      const t25 = n21.state.graphic;
      this.enableZ && t18(t25) && (o17 = true);
      const a28 = n21.geometryRepresentation instanceof I4 && !n21.state.isDraped ? n21.geometryRepresentation.attachmentOrigin : z6(this.view, t25);
      if (r(a28)) {
        const { x: t26, y: o18, z: n22 } = a28;
        i19.x += t26, i19.y += o18, n22 && (i19.z ?? (i19.z = 0), i19.z += n22), e20++;
      }
    }
    e20 > 0 ? (i19.x /= e20, i19.y /= e20, i19.z ?? (i19.z = 0), i19.z /= e20, a27.location = i19, a27.xyManipulation.available = true, a27.xyAxisManipulation.available = true, a27.zManipulation.available = o17) : a27.available = false;
  }
  _buildDragEventPipeline(t24, i19, e20, o17, a27, n21) {
    const s19 = [], r24 = [];
    let p25 = null, l22 = null;
    const c21 = () => {
      for (const t25 of s19) t25.dragging = false;
      s19.length = 0, r24.length = 0, p25 = null, l22 = null, this._moveManipulation.interactive = true;
    };
    if (1 === t24.length && i19 === M15.XY) {
      const i20 = t24[0].graphic;
      ({ steps: o17, cancel: a27 } = this._buildSnappingPipelineSteps(i20, f10(i20), o17, a27, n21));
    }
    return a27 = a27.next((t25) => l22 == null ? void 0 : l22(t25)).next(() => (this.emit("graphic-move-stop", new q8(r24)), this.destroyed || c21(), null)), { steps: o17 = o17.next((i20) => {
      var _a, _b;
      if ("start" === i20.action) {
        s19.length = 0, r24.length = 0;
        for (const i21 of t24) i21.dragging || !((_a = i21.manipulationXY) == null ? void 0 : _a.hasManipulator(e20)) && ((_b = i21.manipulationXY) == null ? void 0 : _b.grabbing) || (s19.push(i21), r24.push(i21.graphic), i21.dragging = true);
        if (0 !== r24.length && (this._moveManipulation.interactive = false, p25 = R4(r24, this.view.state.viewingMode), l22 = q4(r24), this.emit("graphic-move-start", new L6(r24)), this.destroyed)) return null;
      }
      return 0 !== r24.length ? i20 : null;
    }).next((t25) => p25 == null ? void 0 : p25(t25)).next((e21) => (this._updateMoveTooltip(t24, i19, e21), e21)).next((t25) => {
      switch (t25.action) {
        case "start":
        case "update":
          if (t25.translationX || t25.translationY || t25.translationZ) {
            const i20 = this.view.toScreen(t25.mapStart), e21 = this.view.toScreen(t25.mapEnd), o18 = e21.x - i20.x, a28 = e21.y - i20.y;
            if (this.emit("graphic-move", new N3(o18, a28, r24)), this.destroyed) return null;
          }
          break;
        case "end":
          if (this.emit("graphic-move-stop", new q8(r24)), this.destroyed) return null;
          c21();
      }
      return null;
    }), cancel: a27 };
  }
  _updateMoveTooltip(t24, i19, e20) {
    const { tooltipOptions: o17, _tooltip: a27 } = this;
    if (t(a27)) return;
    a27.clear();
    const n21 = 0 === t24.length ? "absolute-height" : t24[0].state.isDraped ? "on-the-ground" : "absolute-height";
    switch (i19) {
      case M15.XY:
        a27.info = new r16({ tooltipOptions: o17 }), this._updateMoveTooltipDistance(a27.info, e20, (t25, i20) => a16(t25, i20, n21));
        break;
      case M15.XY_AXIS:
        a27.info = new a19({ tooltipOptions: o17 }), this._updateMoveTooltipDistance(a27.info, e20, (t25, i20) => {
          const o18 = a16(t25, i20, n21);
          return y6(o18, c16(e20));
        });
        break;
      case M15.Z:
        a27.info = new p14({ tooltipOptions: o17 }), this._updateMoveTooltipDistance(a27.info, e20, v7);
    }
  }
  _updateMoveTooltipDistance(t24, i19, e20) {
    if (r(i19) && "end" !== i19.action) {
      const { mapStart: o17, mapEnd: a27 } = i19, n21 = e20(o17, a27);
      t24.distance = r(n21) ? n21 : j5;
    }
  }
  _buildSnappingPipelineSteps(t24, i19, e20, o17, a27) {
    const n21 = t24.geometry;
    if (t(n21) || "point" !== n21.type && "mesh" !== n21.type) return { steps: e20, cancel: o17 };
    const r24 = ("point" === n21.type ? n21 : n21.anchor).clone(), p25 = new e12({ elevationInfo: i19, pointer: a27, editGeometryOperations: V.fromGeometry(r24, this.view.state.viewingMode), visualizer: new x14(), excludeFeature: t24 }), l22 = this.snappingManager, { snappingStep: c21, cancelSnapping: h25 } = m4({ snappingContext: p25, snappingManager: l22, updatingHandles: this.updatingHandles });
    return o17 = o17.next(h25), { steps: e20 = e20.next((i20) => {
      r24.z = g6(this.view, r24, f10(t24), { mode: "absolute-height", offset: 0 });
      return { ...i20, snapOrigin: p25.coordinateHelper.pointToVector(r24) };
    }).next(...c21), cancel: o17 };
  }
  get test() {
    return { tooltip: this._tooltip };
  }
};
e([y2({ constructOnly: true, nonNullable: true })], B5.prototype, "view", void 0), e([y2()], B5.prototype, "graphics", void 0), e([y2({ constructOnly: true, nonNullable: true })], B5.prototype, "enableZ", void 0), e([y2({ constructOnly: true, type: p10 })], B5.prototype, "tooltipOptions", void 0), e([y2({ constructOnly: true })], B5.prototype, "snappingManager", void 0), e([y2()], B5.prototype, "type", void 0), e([y2()], B5.prototype, "updating", null), B5 = e([a3("esri.views.3d.interactive.editingTools.graphicMove3D.GraphicMoveTool")], B5);
var J3 = class {
  constructor(t24) {
    this.geometryRepresentation = null, this.manipulationXY = null, this.dragging = false, this.state = new e17({ graphic: t24 });
  }
  get graphic() {
    return this.state.graphic;
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/reshapeGraphic/edgeOffsetUtils.js
function j12(e20, r24, o17) {
  const n21 = "on-the-ground" === o17.mode ? A7.XY : A7.XYZ;
  return new P3(e20, n21, r24, 0);
}
function h20(r24, o17, n21) {
  const s19 = n4();
  if (!r24.renderCoordsHelper.toRenderCoords(o17, s19)) return null;
  const c21 = y8(r24, o17, Y(n21.plane)), i19 = y8(r24, o17, n21.edgeDirection);
  if (t(c21) || t(i19)) return null;
  const u13 = _(n4(), c21, i19);
  return _2(s19, u13, p6());
}
function y8(e20, r24, o17) {
  const n21 = M2(r24.x + o17[0], r24.y + o17[1], r24.z + o17[2], r24.spatialReference), t24 = n4(), c21 = n4();
  return e20.renderCoordsHelper.toRenderCoords(r24, t24) && e20.renderCoordsHelper.toRenderCoords(n21, c21) ? H(c21, t24, c21) : null;
}
function C10(e20, r24, o17) {
  const c21 = Y(e20), i19 = H(n4(), r24, o17), p25 = _(n4(), i19, c21), m17 = _(n4(), i19, p25);
  return t5(i19[0], i19[1], i19[2], 0, p25[0], p25[1], p25[2], 0, m17[0], m17[1], m17[2], 0, 0, 0, 0, 1);
}
function R8(e20, n21, t24) {
  const s19 = t24.projectToRenderScreen(e20, x2()), d15 = t24.projectToRenderScreen(n21, x2());
  return r(s19) && r(d15) ? v3(e3(s19, s19, d15)) : 0;
}

// node_modules/@arcgis/core/views/interactive/tooltip/ReshapeTooltipInfos.js
var p22 = class extends t13 {
  constructor(o17) {
    super(o17), this.type = "reshape-edge-offset", this.distance = j5, this.area = null, this.totalLength = null;
  }
};
e([y2()], p22.prototype, "type", void 0), e([y2()], p22.prototype, "distance", void 0), e([y2()], p22.prototype, "area", void 0), e([y2()], p22.prototype, "totalLength", void 0), p22 = e([a3("esri.views.interactive.tooltip.ReshapeEdgeOffsetTooltipInfo")], p22);

// node_modules/@arcgis/core/views/3d/interactive/editingTools/reshapeGraphic/ReshapeOperation.js
var Re = class extends n3.EventedMixin(d3) {
  constructor(e20) {
    super(e20), this._vertexManipulatorMaterial = R5(y7.colorToVec4(y7.reshapeManipulators.vertex.color), y7.reshapeManipulators.vertex.renderOccluded), this._vertexManipulatorOutlineMaterial = L3(y7.colorToVec4(y7.reshapeManipulators.vertex.outlineColor), y7.reshapeManipulators.vertex.renderOccluded), this._vertexManipulatorHoverOutlineMaterial = L3(y7.colorToVec4(y7.reshapeManipulators.vertex.hoverOutlineColor), y7.reshapeManipulators.vertex.renderOccluded), this._edgeManipulatorMaterial = R5(y7.colorToVec4(y7.reshapeManipulators.edge.color), y7.reshapeManipulators.edge.renderOccluded), this._edgeManipulatorOutlineMaterial = L3(y7.colorToVec4(y7.reshapeManipulators.edge.outlineColor), y7.reshapeManipulators.edge.renderOccluded), this._edgeOffsetManipulatorMaterial = R5(y7.colorToVec4(y7.reshapeManipulators.edgeOffset.color), y7.reshapeManipulators.edgeOffset.renderOccluded, false), this._edgeOffsetManipulatorHoverMaterial = R5(y7.colorToVec4(y7.reshapeManipulators.edgeOffset.hoverColor), y7.reshapeManipulators.edgeOffset.renderOccluded, false), this._selectedManipulatorMaterial = R5(y7.colorToVec4(y7.reshapeManipulators.selected.color), y7.reshapeManipulators.selected.renderOccluded), this._selectedManipulatorOutlineMaterial = L3(y7.colorToVec4(y7.reshapeManipulators.selected.outlineColor), y7.reshapeManipulators.selected.renderOccluded), this._selectedManipulatorHoverOutlineMaterial = L3(y7.colorToVec4(y7.reshapeManipulators.selected.hoverOutlineColor), y7.reshapeManipulators.selected.renderOccluded), this._selectedIndex = 0, this._manipulatorHandles = new t3(), this._manipulatorInfos = [], this._numGrabbing = 0, this._numDragging = 0, this._reshapeEventState = qe.NONE, this._recreatingManipulators = false, this.outputGeometry = null, this._vertexLaserLineVisualElement = null;
  }
  initialize() {
    const { graphic: e20, view: t24 } = this, i19 = this._graphicState = new e17({ graphic: e20 });
    this._tooltip = new m6({ view: t24 }), this.addHandles([l3(() => i19.displaying, (e21) => {
      for (const t25 of this._manipulatorInfos) t25.manipulator.available = e21;
    }), l3(() => ({ labels: this._segmentLabels, enabled: this._labelOptions.enabled, edgeOffsetEnabled: this.enableEdgeOffset }), ({ labels: e21, enabled: t25, edgeOffsetEnabled: i20 }) => {
      r(e21) && (e21.visible = t25, e21.edgeDistance = i20 ? "far" : "default");
    }, h4), f5(() => !this._tooltipOptions.enabled, () => this._tooltip.clear(), h4), this.view.trackGraphicState(i19)]);
  }
  destroy() {
    this._segmentLabels = a(this._segmentLabels), this._tooltip = a(this._tooltip), this._removeManipulators();
  }
  get inputGeometry() {
    return r(this._editGeometryOperations) ? this._editGeometryOperations.data.geometry : null;
  }
  set inputGeometry(e20) {
    this._recreateEditGeometryAndManipulators(e20);
  }
  get updating() {
    return this.updatingHandles.updating;
  }
  get manipulators() {
    return this.tool.manipulators;
  }
  get view() {
    return this.tool.view;
  }
  get graphic() {
    return this.tool.graphic;
  }
  get enableZShape() {
    return this.tool.enableZShape;
  }
  get enableZVertex() {
    return this.tool.enableZVertex;
  }
  get enableMoveGraphic() {
    return this.tool.enableMoveGraphic;
  }
  get enableMidpoints() {
    return this.tool.enableMidpoints;
  }
  get enableEdgeOffset() {
    return this.tool.enableEdgeOffset;
  }
  get _labelOptions() {
    return this.tool.labelOptions;
  }
  get _tooltipOptions() {
    return this.tool.tooltipOptions;
  }
  removeSelectedVertices() {
    const e20 = this._manipulatorInfos.filter((e21) => e21.manipulator.selected && "vertex" === e21.type);
    this._removeVertices(e20);
  }
  onManipulatorSelectionChanged() {
    this.emit("manipulators-changed");
  }
  _removeManipulators() {
    this._manipulatorHandles.removeAll(), this._moveManipulation = a(this._moveManipulation), this._graphicMoveManipulation = a(this._graphicMoveManipulation), this.manipulators.removeAll(), this._manipulatorInfos = [], this._numGrabbing = 0, this._numDragging = 0;
  }
  _createManipulators(e20) {
    if (t(this._editGeometryOperations)) return;
    const t24 = f10(this.graphic);
    for (const i19 of this._editGeometryOperations.data.components) {
      const a27 = e20 == null ? void 0 : e20.byComponentIndex.get(i19.index);
      for (const e21 of i19.vertices) {
        const i20 = a27 == null ? void 0 : a27.has(e21.index);
        this._createVertexOrEdgeManipulator(e21, t24, i20);
      }
      for (const e21 of i19.edges) this._createVertexOrEdgeManipulator(e21, t24);
    }
    this._createGraphicMoveManipulation(), this._createMoveManipulation(t24), this._createVisualElements();
  }
  get canRedo() {
    return r(this._editGeometryOperations) && this._editGeometryOperations.canRedo;
  }
  get canUndo() {
    return r(this._editGeometryOperations) && this._editGeometryOperations.canUndo;
  }
  redo() {
    if (t(this._editGeometryOperations)) return null;
    const e20 = this._editGeometryOperations.redo();
    return r(e20) && (this.outputGeometry = this._editGeometryOperations.data.geometry, this._recreateManipulators()), e20;
  }
  undo() {
    if (t(this._editGeometryOperations)) return null;
    this.emit("undo");
    const e20 = this._editGeometryOperations.undo();
    return r(e20) && (this.outputGeometry = this._editGeometryOperations.data.geometry, this._recreateManipulators()), e20;
  }
  _recreateManipulators() {
    this._recreatingManipulators || (this._recreatingManipulators = true, this._removeManipulators(), this._tooltip.clear(), this._createManipulators(), this._recreatingManipulators = false);
  }
  _recreateEditGeometryAndManipulators(e20) {
    const t24 = { byComponentIndex: /* @__PURE__ */ new Map() };
    if (r(e20) && r(this.inputGeometry) && z(e20, this.inputGeometry)) {
      for (const i19 of this._manipulatorInfos) if ("vertex" === i19.type && i19.manipulator.selected) {
        const { index: e21, component: { index: a27 } } = i19.handle, { byComponentIndex: o17 } = t24, n21 = o17.get(a27) || /* @__PURE__ */ new Set();
        n21.add(e21), o17.set(a27, n21);
      }
    }
    this._recreatingManipulators = true, this._removeManipulators(), this._tooltip.clear(), this._editGeometryOperations = a(this._editGeometryOperations), this._segmentLabels = a(this._segmentLabels), t(e20) || (this._editGeometryOperations = V.fromGeometry(e20, this.view.state.viewingMode), this._createManipulators(t24), this._segmentLabels = new a21({ context: { view: this.view, editGeometryOperations: this._editGeometryOperations, elevationInfo: f10(this.graphic), labelOptions: this._labelOptions, graphic: this.graphic, graphicState: this._graphicState }, visible: this._labelOptions.enabled })), this._recreatingManipulators = false;
  }
  _perGraphicManipulatorDragAction(e20, t24) {
    if ("end" === t24.action) return t24;
    let i19 = 0;
    const a27 = [], o17 = this._manipulatorInfos.some((e21) => "vertex" === e21.type && e21.manipulator.selected), n21 = e20 === We.SELECTED_OR_ALL && o17;
    for (const r24 of this._manipulatorInfos) "vertex" === r24.type && (r24.manipulator.grabbing || n21 && !r24.manipulator.selected || a27.push(r24), i19++);
    if (0 === a27.length) return t24;
    this._moveVertices(a27, t24);
    if (a27.length === i19) {
      if (this._updateEventState(qe.MOVING), this.destroyed) return t24;
      this.emit("move", { type: "move", dx: t24.screenDeltaX, dy: t24.screenDeltaY, mover: this.graphic });
    } else {
      if (this._updateEventState(qe.RESHAPING), this.destroyed) return t24;
      this.emit("reshape", { type: "reshape", mover: this.graphic });
    }
    return t24;
  }
  _isMultiVertexSelection() {
    return this._manipulatorInfos.reduce((e20, t24) => "vertex" === t24.type && t24.manipulator.selected ? e20 + 1 : e20, 0) > 1;
  }
  _perVertexManipulatorDragAction(e20) {
    if (this._updateEventState(qe.RESHAPING), this.destroyed) return;
    const { mapDeltaX: t24, mapDeltaY: i19, mapDeltaZ: a27 } = e20;
    if (!t24 && !i19 && !a27) return;
    const o17 = [];
    for (const n21 of this._manipulatorInfos) "vertex" === n21.type && (n21.manipulator.selected && !n21.manipulator.grabbing || n21 === e20.info) && o17.push(n21);
    this._moveVertices(o17, e20, E9.ACCUMULATE_STEPS), this.emit("reshape", { type: "reshape", mover: this.graphic });
  }
  _updateEventState(e20) {
    if (e20 === this._reshapeEventState) return false;
    switch (e20) {
      case qe.NONE:
        if (0 !== this._numGrabbing || 0 !== this._numDragging) return false;
        switch (this._reshapeEventState) {
          case qe.MOVING:
            this.emit("move", { type: "move-stop", dx: 0, dy: 0, mover: this.graphic });
            break;
          case qe.RESHAPING:
            this.emit("reshape", { type: "reshape-stop", mover: this.graphic });
        }
        break;
      case qe.MOVING:
        switch (this._reshapeEventState) {
          case qe.NONE:
            this.emit("move", { type: "move-start", dx: 0, dy: 0, mover: this.graphic });
            break;
          case qe.RESHAPING:
            this.emit("reshape", { type: "reshape-stop", mover: this.graphic }), this.destroyed || this.emit("move", { type: "move-start", dx: 0, dy: 0, mover: this.graphic });
        }
        break;
      case qe.RESHAPING:
        switch (this._reshapeEventState) {
          case qe.NONE:
            this.emit("reshape", { type: "reshape-start", mover: this.graphic });
            break;
          case qe.MOVING:
            this.emit("move", { type: "move-stop", dx: 0, dy: 0, mover: this.graphic }), this.destroyed || this.emit("reshape", { type: "reshape-start", mover: this.graphic });
        }
    }
    if (this.destroyed) return false;
    const t24 = this._reshapeEventState !== e20;
    return this._reshapeEventState = e20, t24;
  }
  _createGraphicMoveManipulation() {
    const { tool: e20, view: t24 } = this, i19 = this._graphicState;
    if (this._graphicMoveManipulation = new l19({ tool: e20, view: t24, graphicState: i19 }), this.enableMoveGraphic) {
      let e21 = null;
      this._manipulatorHandles.add(this._graphicMoveManipulation.createDragPipeline((t25, i20, a27) => {
        i20.next((e22) => this._trackNumDragging(e22)).next((t26) => ("start" === t26.action && (e21 = e2(this._editGeometryOperations).createUndoGroup()), t26)).next((e22) => this._perGraphicManipulatorDragAction(We.ALL, e22)).next((e22) => (this._updateTranslateGraphicTooltip(M15.XY, e22), e22)).next((t26) => {
          "end" === t26.action && (this._tooltip.clear(), e21 = p(e21));
        }), a27.next(() => this._onDragCancel(true, () => e21 = p(e21)));
      })), this._graphicMoveManipulation.forEachManipulator((e22) => this._manipulatorHandles.add(this._watchAndUpdateGrabState(e22, false)));
    } else this._graphicMoveManipulation.forEachManipulator((e21) => {
      e21.grabbable = false, e21.cursor = null;
    });
    this._graphicMoveManipulation.forEachManipulator((e21) => this._manipulatorHandles.add(e21.events.on("immediate-click", (e22) => {
      this._manipulatorInfos.some((e23) => e23.manipulator.selected) ? this._clearSelection() : this.emit("immediate-click", { ...e22, graphic: this.graphic }), e22.stopPropagation();
    })));
  }
  _createMoveManipulation(e20) {
    const { graphic: t24, handles: i19, tool: a27, view: o17 } = this, n21 = this._graphicState;
    this._moveManipulation = new u11({ tool: a27, view: o17, xyAvailable: true, xyAxisAvailable: true, zAvailable: this.enableZShape && t18(t24), snapToScene: false, radius: u11.radiusForSymbol(t24.symbol) }), this._moveManipulation.forEachManipulator((e21) => i19.add([e21.events.on("immediate-click", (i20) => {
      this._moveManipulation.zManipulation.hasManipulator(e21) || this._manipulatorInfos.some((e22) => e22.manipulator.selected) || this.emit("immediate-click", { ...i20, graphic: t24 }), i20.stopPropagation();
    }), this._watchAndUpdateGrabState(e21, false)]));
    const r24 = (e21) => (t25) => {
      i19.add(t25.events.on("focus-changed", ({ action: t26 }) => {
        "focus" === t26 && this._tooltipOptions.enabled ? this._updateTranslateTooltip(e21) : this._tooltip.clear();
      }));
    };
    this._moveManipulation.xyManipulation.forEachManipulator(r24(M15.XY)), this._moveManipulation.xyAxisManipulation.forEachManipulator(r24(M15.XY_AXIS)), this._moveManipulation.zManipulation.forEachManipulator(r24(M15.Z)), this._moveManipulation.elevationInfo = { mode: "absolute-height", offset: 0 };
    const s19 = e2(t24.geometry).spatialReference;
    i19.add([this._moveManipulation.createDragPipeline((i20, o18, n22, r25, s20) => {
      const { snappingStep: l22, cancelSnapping: p25 } = m4({ predicate: (e21) => !!e21.info, snappingManager: a27.snappingManager, snappingContext: new e12({ editGeometryOperations: e2(this._editGeometryOperations), elevationInfo: e20, pointer: s20, excludeFeature: t24, visualizer: new x14() }), updatingHandles: this.updatingHandles, useZ: false });
      return r25 = r25.next((e21) => (this._onDragCancel(), e21)).next(p25), { steps: n22 = n22.next((e21) => this._trackNumDragging(e21)).next((e21) => {
        const t25 = this._manipulatorInfos.filter((e22) => "vertex" === e22.type && e22.manipulator.selected);
        return e21.manipulatorType === A13.TRANSLATE_XY && 1 === t25.length ? { ...e21, info: t25[0], snapOrigin: t25[0].handle.pos } : e21;
      }).next(X(this.view, e20, t24)).next(...l22).next(D2()).next((e21) => this._perGraphicManipulatorDragAction(We.SELECTED_OR_ALL, e21)).next((e21) => (this._updateTranslateTooltip(i20, e21), e21)), cancel: r25 };
    }, e20, s19, t24), l3(() => n21.displaying, () => this._updateMoveManipulationPosition(), h4), n21.on("changed", () => {
      this._recreatingManipulators || this._updateMoveManipulationPosition();
    }), l3(() => n21.isDraped, (e21) => {
      this._updateMoveManipulationPosition();
      const t25 = "align-move-manipulation";
      e21 ? i19.add(this.view.elevationProvider.on("elevation-change", () => this._updateMoveManipulationPosition()), t25) : i19.remove(t25);
    }, h4)]);
  }
  _createVisualElements() {
    const { graphic: e20, view: t24 } = this, i19 = i13({ view: t24, graphic: e20, forEachManipulator: (e21) => {
      if (!this.destroyed && !this._recreatingManipulators) {
        this._graphicMoveManipulation.forEachManipulator(e21), this._moveManipulation.forEachManipulator(e21);
        for (const t25 of this._manipulatorInfos) e21(t25.manipulator, A13.TRANSLATE_XY);
      }
    }, onManipulatorsChanged: (e21) => this.on("manipulators-changed", e21) });
    r(i19) && (this._outlineVisualElement = i19.visualElement instanceof I4 ? i19.visualElement : null), r(this._outlineVisualElement) && this._manipulatorHandles.add(this._outlineVisualElement.events.on("attachment-origin-changed", () => {
      this._graphicState.isDraped || this._updateMoveManipulationPosition();
    })), this._manipulatorHandles.add(i19);
  }
  _createEdgeOffsetManipulator(e20, t24 = f10(this.graphic)) {
    var _a, _b;
    const i19 = y7.reshapeManipulators.edgeOffset, a27 = i19.size / 2, o17 = a27 + i19.collisionPadding, r24 = a27 / o17, s19 = r24 * Math.sqrt(3) / 2;
    t(this._edgeOffsetManipulatorGeometryInside) && (this._edgeOffsetManipulatorGeometryInside = pt(this._edgeOffsetManipulatorMaterial, s19, r24 / 2, r24 / 2, i19.height, i19.offset)), t(this._edgeOffsetManipulatorGeometryOutside) && (this._edgeOffsetManipulatorGeometryOutside = pt(this._edgeOffsetManipulatorMaterial, -s19, r24 / 2, r24 / 2, i19.height, -i19.offset));
    const h25 = [new e18(this._edgeOffsetManipulatorGeometryInside.instantiate(), t11.Unfocused), new e18(this._edgeOffsetManipulatorGeometryInside.instantiate({ material: this._edgeOffsetManipulatorHoverMaterial }), t11.Focused), new e18(this._edgeOffsetManipulatorGeometryOutside.instantiate(), t11.Unfocused), new e18(this._edgeOffsetManipulatorGeometryOutside.instantiate({ material: this._edgeOffsetManipulatorHoverMaterial }), t11.Focused)], d15 = new ie({ view: this.view, renderObjects: h25, elevationInfo: "on-the-ground" !== t24.mode || w4(this.graphic.symbol) ? { mode: "absolute-height", offset: 0 } : t24, worldOriented: false, focusMultiplier: 1, radius: o17, available: !(!this.graphic.visible || !((_a = this.graphic.layer) == null ? void 0 : _a.visible)), collisionType: { type: "disc", direction: r3(0, 0, 1) }, collisionPriority: 1, metadata: { deleting: false } }), u13 = new ie({ view: this.view, worldSized: true, worldOriented: false, available: !(!this.graphic.visible || !((_b = this.graphic.layer) == null ? void 0 : _b.visible)), collisionPriority: -10, cursor: this.enableMoveGraphic ? "move" : "default", metadata: { deleting: false } }), c21 = { manipulator: d15, handle: e20, locationUpdateHandle: null, type: "edge", selectedIndex: 0, edgeManipulator: u13, elevationInfo: t24, visibilityHandle: null };
    this._autoHideEdgeOffsetManipulator(c21, i19.minSquaredEdgeLength), this._updateEdgeOffsetManipulator(c21);
    const m17 = [];
    for (const n21 of [c21.handle.leftVertex, c21.handle.rightVertex]) {
      const e21 = this._getManipulatorInfoFromHandle(n21);
      r(e21) && m17.push(e21.manipulator.events.on("location-update", () => this._updateEdgeOffsetManipulator(c21)));
    }
    c21.locationUpdateHandle = r2(m17), this._manipulatorHandles.add(c21.locationUpdateHandle, d15), this._manipulatorHandles.add([this._watchAndUpdateGrabState(d15, true), this._watchAndUpdateGrabState(u13, true)], d15), this._manipulatorHandles.add(x7(d15, this._createEdgeOffsetPipeline(c21, t24)), d15), this._manipulatorHandles.add(x7(u13, (e21, i20, a28, o18) => {
      if ("touch" === o18) {
        this._createEdgeOffsetPipeline(c21, t24)(e21, i20, a28);
      } else if (this.enableMoveGraphic) {
        const o19 = this.graphic, n21 = r(o19.geometry) ? o19.geometry.spatialReference : null;
        i20.next((e22) => this._trackNumDragging(e22)).next(C(this.view, e21.elevationAlignedLocation)).next(N2(this.view, e21.elevationAlignedLocation, t24, n21, o19)).next(b6()).next(D2()).next((e22) => this._perGraphicManipulatorDragAction(We.ALL, e22)).next((e22) => (this._updateTranslateGraphicTooltip(M15.XY, e22), e22)).next((e22) => {
          "end" === e22.action && this._tooltip.clear();
        }), a28.next(() => this._onDragCancel(!e21.metadata.deleting));
      }
    }), d15);
    const g16 = (e21) => {
      this._manipulatorInfos.some((e22) => e22.manipulator.selected) ? this._clearSelection() : this.emit("immediate-click", { ...e21, graphic: this.graphic }), e21.stopPropagation();
    };
    return this._manipulatorHandles.add([d15.events.on("immediate-click", g16), u13.events.on("immediate-click", g16), d15.events.on("focus-changed", ({ action: e21 }) => {
      const t25 = this._tooltipOptions;
      if ("focus" === e21 && t25.enabled) {
        const e22 = this._tooltip.info = new p22({ tooltipOptions: t25 });
        this._updateTooltipAreaOrTotalLength(e22);
      } else this._tooltip.clear();
    })], d15), this._manipulatorInfos.push(c21), this.manipulators.add(d15), this.manipulators.add(u13), this.emit("manipulators-changed"), c21;
  }
  _autoHideEdgeOffsetManipulator(e20, t24) {
    const i19 = e20.manipulator, a27 = e20.edgeManipulator, o17 = () => {
      e20.visibilityHandle = p(e20.visibilityHandle);
      const o18 = this._getManipulatorInfoFromHandle(e20.handle.leftVertex), r24 = this._getManipulatorInfoFromHandle(e20.handle.rightVertex), s19 = r(o18) && r(r24) && R8(o18.manipulator.renderLocation, r24.manipulator.renderLocation, this.view.state.camera) < t24;
      (!i19.focused && !a27.focused || s19) && (i19.grabbable = !s19, a27.grabbable = !s19, e20.visibilityHandle = r2([i19.disableDisplay(), { remove: () => {
        i19.grabbable = true, a27.grabbable = this.enableMoveGraphic;
      } }]));
    };
    this._manipulatorHandles.add([i19.events.on("focus-changed", o17), a27.events.on("focus-changed", o17), { remove: () => {
      p(e20.visibilityHandle), a27.metadata.deleting = true, this.manipulators.remove(a27);
    } }], i19), o17();
  }
  _updateEdgeOffsetManipulator(e20) {
    this._updateManipulatorPosition(e20);
    const { coordinateHelper: t24 } = e2(this._editGeometryOperations).data, i19 = h20(this.view, e20.manipulator.elevationAlignedLocation, j12(t24, e20.handle, e2(e20.manipulator.elevationInfo))), a27 = this._getManipulatorInfoFromHandle(e20.handle.leftVertex), o17 = this._getManipulatorInfoFromHandle(e20.handle.rightVertex);
    if (t(a27) || t(o17)) return;
    const n21 = a27.manipulator.renderLocation, r24 = o17.manipulator.renderLocation, s19 = r(i19) ? C10(i19, n21, r24) : o6;
    e20.manipulator.modelTransform = s19, e20.edgeManipulator.elevationAlignedLocation = e20.manipulator.elevationAlignedLocation, e20.edgeManipulator.modelTransform = s19;
    const d15 = s2(e3(Xe, n21, r24)) / 2;
    e20.edgeManipulator.collisionType = { type: "line", paths: [[[-d15, 0, 0], [d15, 0, 0]]] };
  }
  _createEdgeOffsetPipeline(e20, t24) {
    return (i19, a27, o17) => {
      this._clearSelection();
      const { step: n21, cleanup: r24 } = this._initializeEdgeOffset(e20, t24);
      a27.next((e21) => this._trackNumDragging(e21)).next(C(this.view, i19.elevationAlignedLocation)).next(n21).next(T7(this.view)).next(F6(this.view, e2(this._editGeometryOperations).data.spatialReference)).next(D2()).next(this._applyComputeEdgeOffsetDistanceStep()).next(this._applyEdgeOffsetStep(e20)).next(this._showEdgeOffsetTooltip()).next((e21) => {
        "end" === e21.action && r24();
      }), o17.next(() => {
        i19.metadata.deleting || (r24(), this._onDragCancel());
      });
    };
  }
  _initializeEdgeOffset(e20, t24) {
    const i19 = e2(this._editGeometryOperations), a27 = j12(i19.data.coordinateHelper, e20.handle, t24), o17 = i19.createUndoGroup(), s19 = h20(this.view, e20.manipulator.elevationAlignedLocation, a27);
    if (a27.requiresSplitEdgeLeft) {
      const t25 = this._getManipulatorInfoFromHandle(e20.handle.leftVertex.leftEdge);
      r(t25) && this._splitEdgeManipulator(t25, 1);
    }
    if (a27.requiresSplitEdgeRight) {
      const t25 = this._getManipulatorInfoFromHandle(e20.handle.rightVertex.rightEdge);
      r(t25) && this._splitEdgeManipulator(t25, 0);
    }
    const u13 = () => new m({ paths: [[e20.handle.leftVertex.pos, e20.handle.rightVertex.pos]], spatialReference: i19.data.spatialReference }), c21 = new I4({ view: this.view, isDraped: this._graphicState.isDraped, geometry: u13(), elevationInfo: e20.elevationInfo, width: y7.visualElements.lineGraphics.outline.width, color: y7.colorToVec4(r18.main), attached: true });
    let m17;
    const _9 = () => {
      this._cleanEdgeOffsetCollapsedEdges(e20), m17 = p(m17);
    }, f19 = this.on("undo", _9);
    return m17 = r2([t2(c21), l3(() => this._graphicState.isDraped, (e21) => c21.isDraped = e21), this._graphicState.on("changed", () => c21.geometry = u13()), o17, f19]), { step: (e21) => t(a27) || t(s19) ? (_9(), null) : { ...e21, operation: a27, plane: s19 }, cleanup: _9 };
  }
  _applyEdgeOffsetStep(e20) {
    return (t24) => {
      if (this.destroyed || t(t24.operation)) return t24;
      this._updateEventState(qe.RESHAPING);
      const { mapDeltaX: i19, mapDeltaY: a27, mapDeltaZ: o17 } = t24;
      return (i19 || a27 || o17) && (this._offsetEdge(e20, t24), this.emit("reshape", { type: "reshape", mover: this.graphic })), t24;
    };
  }
  _applyComputeEdgeOffsetDistanceStep() {
    return (e20) => {
      const { operation: t24, mapEnd: i19 } = e20;
      return t(t24) || t(i19) ? e20 : ("start" === e20.action && t24.selectArrowFromStartPoint(i19), { ...e20, signedDistance: t24.signedDistanceToPoint(i19) });
    };
  }
  _showEdgeOffsetTooltip() {
    return (e20) => {
      const { mapEnd: t24, signedDistance: i19, operation: a27 } = e20, o17 = this._tooltip, n21 = this._tooltipOptions;
      if (!n21.enabled || t(i19)) return o17.clear(), e20;
      let r24 = o17.info;
      (t(r24) || "reshape-edge-offset" !== r24.type) && (r24 = o17.info = new p22({ tooltipOptions: n21 }));
      const { coordinateHelper: s19 } = e2(this._editGeometryOperations).data;
      return r24.distance = "end" === e20.action ? j5 : Fe(this._graphicState.isDraped, i19 * a27.selectedArrow, t24, a27.plane, s19), this._updateTooltipAreaOrTotalLength(r24), e20;
    };
  }
  _cleanEdgeOffsetCollapsedEdges(e20) {
    var _a, _b;
    const t24 = (_a = e20.handle.leftVertex.leftEdge) == null ? void 0 : _a.leftVertex, i19 = e20.handle.leftVertex, a27 = (_b = e20.handle.rightVertex.rightEdge) == null ? void 0 : _b.rightVertex, o17 = e20.handle.rightVertex, n21 = e2(this._editGeometryOperations).data.coordinateHelper, r24 = [];
    if (t24 && n21.distance(t24.pos, i19.pos) < Ze) {
      const e21 = this._getManipulatorInfoFromHandle(i19);
      r(e21) && r24.push(e21);
    }
    if (n21.distance(i19.pos, o17.pos) < Ze || a27 && n21.distance(a27.pos, o17.pos) < Ze) {
      const e21 = this._getManipulatorInfoFromHandle(o17);
      r(e21) && r24.push(e21);
    }
    r24.length && this._removeVertices(r24);
  }
  _computeVertexManipulatorSizeAndOutline(e20) {
    const t24 = e20.size / 2, i19 = t24 + e20.collisionPadding;
    return { size: t24 / i19, outlineSize: (t24 + e20.outlineSize) / i19 };
  }
  _createVertexOrEdgeManipulator(e20, t24 = f10(this.graphic), i19 = false) {
    var _a;
    if ("edge" === e20.type) {
      if (this.enableEdgeOffset) return this._createEdgeOffsetManipulator(e20, t24);
      if (!this.enableMidpoints) return null;
    }
    if (t(this._vertexManipulatorGeometry) || t(this._vertexManipulatorOutlineGeometry)) {
      const { size: e21, outlineSize: t25 } = this._computeVertexManipulatorSizeAndOutline(y7.reshapeManipulators.vertex);
      this._vertexManipulatorGeometry = nt(this._vertexManipulatorMaterial, e21, 16, 16), this._vertexManipulatorOutlineGeometry = nt(this._vertexManipulatorOutlineMaterial, t25, 16, 16);
    }
    if (t(this._edgeManipulatorGeometry) || t(this._edgeManipulatorOutlineGeometry)) {
      const { size: e21, outlineSize: t25 } = this._computeVertexManipulatorSizeAndOutline(y7.reshapeManipulators.edge);
      this._edgeManipulatorGeometry = nt(this._edgeManipulatorMaterial, e21, 16, 16), this._edgeManipulatorOutlineGeometry = nt(this._edgeManipulatorOutlineMaterial, t25, 16, 16);
    }
    const a27 = r(this.graphic.geometry) && "point" === this.graphic.geometry.type ? [] : [new e18(this._vertexManipulatorGeometry.instantiate(), Ye.Vertex | t11.Unselected), new e18(this._vertexManipulatorOutlineGeometry.instantiate(), Ye.Vertex | t11.Unfocused | t11.Unselected), new e18(this._vertexManipulatorOutlineGeometry.instantiate({ material: this._vertexManipulatorHoverOutlineMaterial }), Ye.Vertex | t11.Focused | t11.Unselected), new e18(this._vertexManipulatorGeometry.instantiate({ material: this._selectedManipulatorMaterial }), t11.Selected), new e18(this._vertexManipulatorOutlineGeometry.instantiate({ material: this._selectedManipulatorOutlineMaterial }), t11.Selected | t11.Unfocused), new e18(this._vertexManipulatorOutlineGeometry.instantiate({ material: this._selectedManipulatorHoverOutlineMaterial }), t11.Selected | t11.Focused)];
    this.enableMidpoints && a27.push(new e18(this._edgeManipulatorGeometry.instantiate({ material: this._vertexManipulatorMaterial }), Ye.Edge | t11.Focused | t11.Unselected), new e18(this._edgeManipulatorOutlineGeometry.instantiate({ material: this._vertexManipulatorHoverOutlineMaterial }), Ye.Edge | t11.Focused | t11.Unselected), new e18(this._edgeManipulatorGeometry.instantiate(), Ye.Edge | t11.Unfocused | t11.Unselected), new e18(this._edgeManipulatorOutlineGeometry.instantiate(), Ye.Edge | t11.Unfocused | t11.Unselected));
    const o17 = new ie({ view: this.view, renderObjects: a27, elevationInfo: t24, focusMultiplier: 1, touchMultiplier: 1, available: !(!this.graphic.visible || !((_a = this.graphic.layer) == null ? void 0 : _a.visible)), metadata: { deleting: false } });
    o17.selected = i19, this._setTypeSpecificManipulatorSettings(o17, e20, t24);
    const r24 = "edge" === e20.type ? { manipulator: o17, handle: e20, locationUpdateHandle: null, type: "edge", selectedIndex: 0 } : { manipulator: o17, handle: e20, type: "vertex", selectedIndex: 0 };
    if (this._manipulatorInfos.push(r24), this.manipulators.add(o17), this._updateManipulatorPosition(r24), "edge" === r24.type) {
      const e21 = [];
      for (const t25 of [r24.handle.leftVertex, r24.handle.rightVertex]) {
        const i20 = this._getManipulatorInfoFromHandle(t25);
        r(i20) && e21.push(i20.manipulator.events.on("location-update", () => this._updateManipulatorPosition(r24)));
      }
      r24.locationUpdateHandle = r2(e21), this._manipulatorHandles.add(r24.locationUpdateHandle, o17);
    }
    this._manipulatorHandles.add(this._watchAndUpdateGrabState(o17, true), o17);
    const s19 = x7(o17, (e21, i20, a28, o18) => {
      let n21 = null;
      const { snappingStep: s20, cancelSnapping: l22 } = m4({ predicate: () => !this._isMultiVertexSelection(), snappingManager: this.tool.snappingManager, snappingContext: new e12({ editGeometryOperations: e2(this._editGeometryOperations), elevationInfo: t24, pointer: o18, excludeFeature: this.graphic, visualizer: new x14() }), updatingHandles: this.updatingHandles, useZ: false });
      a28 = a28.next((t25) => (this._onDragCancel(!e21.metadata.deleting, () => n21 = p(n21)), t25)).next(l22), i20.next((e22) => this._trackNumDragging(e22)).next((e22) => {
        if ("start" === e22.action && (n21 = e2(this._editGeometryOperations).createUndoGroup()), "edge" === r24.type) {
          const t25 = this._splitEdgeManipulator(r24);
          return { ...e22, info: t25, snapOrigin: t25.handle.pos };
        }
        return { ...e22, info: r24, snapOrigin: r24.handle.pos };
      }).next(C(this.view, e21.elevationAlignedLocation)).next(P11(this.view, this.graphic, e21.elevationAlignedLocation, e21.location.spatialReference, this.graphic)).next(X(this.view, t24, this.graphic)).next(...s20).next(D2()).next((t25) => {
        this._perVertexManipulatorDragAction(t25), "end" === t25.action && (n21 = p(n21)), this._updateTranslateVertexTooltip(e21, M15.XY, t25);
      });
    });
    return this._manipulatorHandles.add([s19, o17.events.on("immediate-click", (e21) => this._manipulatorClickCallback(e21, r24)), o17.events.on("select-changed", () => {
      r24.selectedIndex = ++this._selectedIndex, this._updateMoveManipulationPosition();
    }), o17.events.on("focus-changed", ({ action: e21 }) => {
      "focus" === e21 && "edge" !== r24.type ? this._updateTranslateVertexTooltip(o17, M15.XY) : this._tooltip.clear();
    })], o17), this.emit("manipulators-changed"), r24;
  }
  _trackNumDragging(e20) {
    switch (e20.action) {
      case "start":
        this._numDragging++;
        break;
      case "end":
        this._numDragging--;
    }
    return e20;
  }
  _onDragCancel(e20 = true, t24) {
    switch (this._numDragging--, e20 && (this.undo(), this.outputGeometry = r(this._editGeometryOperations) ? this._editGeometryOperations.data.geometry : null), r(this.tool.snappingManager) && this.tool.snappingManager.doneSnapping(), this._tooltip.clear(), this._reshapeEventState) {
      case qe.NONE:
        break;
      case qe.MOVING:
        this.emit("move", { type: "move", dx: 0, dy: 0, mover: this.graphic });
        break;
      case qe.RESHAPING:
        this.emit("reshape", { type: "reshape", mover: this.graphic });
    }
    t24 && t24(), this.destroyed || this._updateEventState(qe.NONE);
  }
  _setTypeSpecificManipulatorSettings(e20, t24, i19) {
    switch (t24.type) {
      case "vertex":
        e20.state = Ye.Vertex, e20.selectable = true, e20.cursor = "move", e20.collisionPriority = 2, e20.radius = y7.reshapeManipulators.vertex.size / 2 + y7.reshapeManipulators.vertex.collisionPadding, e20.elevationInfo = i19, e20.interactive = r(this.graphic.geometry) && "point" !== this.graphic.geometry.type;
        break;
      case "edge":
        e20.state = Ye.Edge, e20.selectable = false, e20.cursor = "copy", e20.collisionPriority = -1, e20.radius = y7.reshapeManipulators.edge.size / 2 + y7.reshapeManipulators.edge.collisionPadding, e20.elevationInfo = "on-the-ground" !== i19.mode || w4(this.graphic.symbol) ? { mode: "absolute-height", offset: 0 } : i19;
    }
  }
  _watchAndUpdateGrabState(e20, t24) {
    return e20.events.on("grab-changed", (i19) => this._onGrabStateChanged(e20, t24, i19.action, i19.pointerType));
  }
  _onGrabStateChanged(e20, t24, i19, a27 = "mouse") {
    if (!this._recreatingManipulators) {
      if ("start" === i19) t24 && this._updateSelection(e20), this._numGrabbing++;
      else if (this._numGrabbing--, this._updateEventState(qe.NONE), this.destroyed) return;
      this._moveManipulation.interactive = !this._numGrabbing, ("touch" !== a27 || this.enableEdgeOffset) && (this._manipulatorInfos.forEach((e21) => {
        e21.manipulator.interactive = e21.manipulator.grabbing || !this._numGrabbing && r(this.graphic.geometry) && "point" !== this.graphic.geometry.type, "edgeManipulator" in e21 && (e21.edgeManipulator.interactive = e21.edgeManipulator.grabbing || !this._numGrabbing);
      }), this._graphicMoveManipulation.forEachManipulator((e21) => {
        e21.interactive = e21.grabbing || !this._numGrabbing;
      }));
    }
  }
  _clearSelection() {
    for (const e20 of this._manipulatorInfos) e20.manipulator.grabbing || (e20.manipulator.selected = false);
  }
  _updateSelection(e20) {
    e20.grabbing && !e20.selected && e20.selectable && (this._clearSelection(), e20.selected = true, this.emit("manipulators-changed"));
  }
  _removeManipulator(e20) {
    t(e20) || (e20.manipulator.metadata.deleting = true, this.manipulators.remove(e20.manipulator), this._manipulatorHandles.remove(e20.manipulator), v(this._manipulatorInfos, e20), this.emit("manipulators-changed"));
  }
  _getManipulatorInfoFromHandle(e20) {
    if (e20) {
      for (const t24 of this._manipulatorInfos) if (e20 === t24.handle) return t24;
    }
    return null;
  }
  _updateManipulatorPosition(e20) {
    if (t(e20)) return;
    const t24 = e2(this._editGeometryOperations);
    if ("vertex" === e20.type) e20.manipulator.location = t24.data.coordinateHelper.vectorToDehydratedPoint(e20.handle.pos, ze), e20.manipulator.grabbing && r(this._vertexLaserLineVisualElement) && (this._vertexLaserLineVisualElement.visualElement.intersectsWorldUpAtLocation = e20.manipulator.renderLocation);
    else if ("edge" === e20.type) {
      const i19 = this._getManipulatorInfoFromHandle(e20.handle.leftVertex), a27 = this._getManipulatorInfoFromHandle(e20.handle.rightVertex);
      if (t(i19) || t(a27)) return;
      const o17 = i19.manipulator, n21 = a27.manipulator;
      if (r(e20.manipulator.elevationInfo) && "on-the-ground" === e20.manipulator.elevationInfo.mode) {
        const i20 = o17.location, a28 = n21.location, r24 = 0.5, s19 = i20.x + r24 * (a28.x - i20.x), l22 = i20.y + r24 * (a28.y - i20.y), p25 = i20.hasZ && a28.hasZ ? 0 : void 0;
        e20.manipulator.location = M2(s19, l22, p25, t24.data.spatialReference);
      } else A2(Xe, o17.renderLocation, n21.renderLocation, 0.5), e20.manipulator.renderLocation = Xe;
    }
  }
  _splitEdgeManipulator(e20, t24 = 0.5) {
    const i19 = e2(this._editGeometryOperations), a27 = e2(i19.splitEdge(e20.handle, t24).createdVertex);
    e20.locationUpdateHandle = p(e20.locationUpdateHandle);
    const o17 = f10(this.graphic);
    let n21;
    this.enableEdgeOffset ? (this._removeManipulator(e20), n21 = this._createVertexOrEdgeManipulator(a27)) : (n21 = e20, n21.handle = a27, n21.type = "vertex", this._setTypeSpecificManipulatorSettings(e20.manipulator, e20.handle, o17)), a27.leftEdge && this._createVertexOrEdgeManipulator(a27.leftEdge), a27.rightEdge && this._createVertexOrEdgeManipulator(a27.rightEdge), this.outputGeometry = i19.data.geometry, this._updateManipulatorPosition(n21), this.enableEdgeOffset || this._updateTranslateVertexTooltip(n21.manipulator, M15.XY), this._updateSelection(e20.manipulator);
    const r24 = this._updateEventState(qe.RESHAPING), s19 = i19.data.coordinateHelper.vectorToArray(n21.handle.pos), l22 = i19.data.components.indexOf(a27.component);
    return this.emit("vertex-add", { type: "vertex-add", vertices: [{ coordinates: s19, componentIndex: l22, vertexIndex: e2(a27.index) }], added: s19 }), r24 && this._updateEventState(qe.NONE), n21;
  }
  _updateMoveManipulationPosition() {
    const e20 = o3(Xe, 0, 0, 0);
    let t24 = 0, i19 = false, a27 = null, o17 = null;
    for (const n21 of this._manipulatorInfos) "vertex" === n21.type && (n21.manipulator.selected ? (t24++, u(e20, e20, n21.manipulator.renderLocation), t(a27) || n21.selectedIndex > a27.selectedIndex ? (o17 = a27, a27 = n21) : (t(o17) || n21.selectedIndex > o17.selectedIndex) && (o17 = n21)) : i19 = true);
    if (0 === t24) {
      const e21 = this._graphicState.displaying && this.enableMoveGraphic;
      this._moveManipulation.xyManipulation.available = e21, this._moveManipulation.xyAxisManipulation.available = e21, this._moveManipulation.xyAxisManipulation.orthogonalAvailable = e21, this._moveManipulation.zManipulation.available = e21 && this.enableZShape && t18(this.graphic), this._moveManipulation.angle = a25(e2(this.graphic.geometry)), this._moveManipulation.radius = u11.radiusForSymbol(this.graphic.symbol);
    } else {
      const e21 = this._graphicState.displaying;
      this._moveManipulation.xyManipulation.available = e21, this._moveManipulation.xyAxisManipulation.available = e21, this._moveManipulation.zManipulation.available = e21 && this.enableZVertex && t18(this.graphic), this._moveManipulation.xyAxisManipulation.orthogonalAvailable = e21 && 1 !== t24;
      let i20 = 0;
      if (r(a27)) {
        const e22 = a27.handle.pos, t25 = r(o17) ? o17.handle.pos : a27.handle.leftEdge && a27.handle.leftEdge.leftVertex ? a27.handle.leftEdge.leftVertex.pos : null, n21 = t(o17) && a27.handle.rightEdge && a27.handle.rightEdge.rightVertex ? a27.handle.rightEdge.rightVertex.pos : null;
        t25 && n21 ? this._moveManipulation.xyAxisManipulation.available = false : t25 ? i20 = ke(t25, e22) : n21 && (i20 = ke(e22, n21));
      }
      this._moveManipulation.angle = i20, this._moveManipulation.radius = I5;
    }
    0 !== t24 && i19 ? (g(e20, e20, 1 / t24), ze.spatialReference = e2(this._editGeometryOperations).data.spatialReference, ze.hasZ = true, this.view.renderCoordsHelper.fromRenderCoords(e20, ze), this._moveManipulation.elevationAlignedLocation = ze) : r(this._outlineVisualElement) && !this._graphicState.isDraped && r(this._outlineVisualElement.attachmentOrigin) ? this._moveManipulation.elevationAlignedLocation = this._outlineVisualElement.attachmentOrigin : G6(this.view, this._moveManipulation, this.graphic);
  }
  _removeVertices(e20) {
    var _a;
    const t24 = new Array(), i19 = e2(this._editGeometryOperations);
    for (const a27 of e20) if ("vertex" === a27.type && i19.canRemoveVertex()) {
      t24.push(a27.handle), this._removeManipulator(a27), this._removeManipulator(this._getManipulatorInfoFromHandle(a27.handle.leftEdge)), this._removeManipulator(this._getManipulatorInfoFromHandle(a27.handle.rightEdge));
      const e21 = i19.removeVertices([a27.handle]), o17 = e2((_a = e21.removedVertices) == null ? void 0 : _a[0].createdEdge);
      o17 && this._createVertexOrEdgeManipulator(o17);
    }
    if (t24.length > 0) {
      const e21 = t24.map((e22) => {
        const t25 = i19.data.components.indexOf(e22.component);
        return { coordinates: i19.data.coordinateHelper.vectorToArray(e22.pos), componentIndex: t25, vertexIndex: e2(e22.index) };
      });
      this.outputGeometry = i19.data.geometry;
      const a27 = this._updateEventState(qe.RESHAPING);
      if (this.destroyed) return;
      if (this.emit("vertex-remove", { type: "vertex-remove", removed: e21.map((e22) => e22.coordinates), vertices: e21 }), this.destroyed) return;
      if (a27 && (this._updateEventState(qe.NONE), this.destroyed)) return;
      this._updateMoveManipulationPosition();
    }
  }
  _moveVertices(e20, t24, i19 = "start" === t24.action ? E9.NEW_STEP : E9.ACCUMULATE_STEPS) {
    const a27 = e2(this._editGeometryOperations);
    a27.moveVertices(e20.map((e21) => e21.handle), t24.mapDeltaX, t24.mapDeltaY, t24.mapDeltaZ, i19), this.outputGeometry = a27.data.geometry;
    for (const o17 of e20) this._updateManipulatorPosition(o17);
  }
  _offsetEdge(e20, t24) {
    if (t(t24.operation) || t(t24.signedDistance)) return;
    const i19 = e2(this._editGeometryOperations), a27 = t24.operation.clone();
    a27.distance = t24.signedDistance, i19.updateVertices([e20.handle.leftVertex, e20.handle.rightVertex], a27), this.outputGeometry = i19.data.geometry, this._updateManipulatorPosition(this._getManipulatorInfoFromHandle(e20.handle.leftVertex)), this._updateManipulatorPosition(this._getManipulatorInfoFromHandle(e20.handle.rightVertex));
  }
  _manipulatorClickCallback(e20, t24) {
    e20.shiftKey || this._clearSelection(), "vertex" === t24.type && (t24.manipulator.selected = !t24.manipulator.selected, e20.button === t10.Right && this._removeVertices([t24])), "edge" === t24.type && e20.button === t10.Left && this._splitEdgeManipulator(t24), e20.stopPropagation();
  }
  _updateTranslateTooltip(e20, t24) {
    const i19 = this._manipulatorInfos.filter((e21) => "vertex" === e21.type && e21.manipulator.selected);
    1 === i19.length ? this._updateTranslateVertexTooltip(i19[0].manipulator, e20, t24) : this._updateTranslateGraphicTooltip(e20, t24);
  }
  _updateTranslateGraphicTooltip(e20, t24) {
    const i19 = this._tooltipOptions;
    if (!i19.enabled) return;
    const a27 = this._graphicState.isDraped ? "on-the-ground" : "absolute-height";
    switch (e20) {
      case M15.XY:
        this._tooltip.info = new r16({ tooltipOptions: i19 }), this._updateTranslateTooltipDistance(this._tooltip.info, t24, (e21, t25) => a16(e21, t25, a27));
        break;
      case M15.XY_AXIS:
        this._tooltip.info = new a19({ tooltipOptions: i19 }), this._updateTranslateTooltipDistance(this._tooltip.info, t24, (e21, i20) => {
          const o17 = a16(e21, i20, a27);
          return y6(o17, c16(t24));
        });
        break;
      case M15.Z:
        this._tooltip.info = new p14({ tooltipOptions: i19 }), this._updateTranslateTooltipDistance(this._tooltip.info, t24, v7);
    }
  }
  _updateTranslateVertexTooltip(e20, t24, i19) {
    const a27 = this._tooltipOptions;
    if (!a27.enabled) return;
    let o17;
    const n21 = this._graphicState.isDraped ? "on-the-ground" : "absolute-height";
    switch (t24) {
      case M15.XY:
        o17 = new n12({ tooltipOptions: a27 }), this._updateTranslateTooltipDistance(o17, i19, (e21, t25) => a16(e21, t25, n21)), this._updateTooltipAreaOrTotalLength(o17);
        break;
      case M15.XY_AXIS:
        o17 = new c11({ tooltipOptions: a27 }), this._updateTranslateTooltipDistance(o17, i19, (e21, t25) => {
          const a28 = a16(e21, t25, n21);
          return y6(a28, c16(i19));
        }), this._updateTooltipAreaOrTotalLength(o17);
        break;
      case M15.Z:
        o17 = new l16({ tooltipOptions: a27 }), this._updateTranslateTooltipDistance(o17, i19, v7);
    }
    const r24 = x9(e20.elevationAlignedLocation);
    r(r24) && (o17.elevation = { mode: "absolute-height", ...r24 }), this._tooltip.info = o17;
  }
  _updateTranslateTooltipDistance(e20, t24, i19) {
    if (r(t24) && "end" !== t24.action) {
      const { mapStart: a27, mapEnd: o17 } = t24, n21 = i19(a27, o17);
      e20.distance = r(n21) ? n21 : j5;
    }
  }
  _updateTooltipAreaOrTotalLength(e20) {
    const { geometry: t24 } = this.graphic;
    if (t(t24)) return;
    const i19 = this._graphicState.isDraped ? "on-the-ground" : "absolute-height";
    e20.area = "polygon" === t24.type ? i10(t24, i19) : null, e20.totalLength = "polyline" === t24.type ? m7(t24, i19) : null;
  }
  get test() {
    return { segmentLabels: this._segmentLabels, tooltip: this._tooltip };
  }
};
function ke(e20, t24) {
  return Math.atan2(t24[1] - e20[1], t24[0] - e20[0]) + Math.PI / 2;
}
function Fe(e20, t24, i19, a27, o17) {
  if (e20) {
    const e21 = o17.toXYZ(o17.pointToVector(i19)), n21 = J(a27, e21, c2.get()), r24 = l14(n21, e21, o17.spatialReference);
    if (r(r24)) return a15(r24.value * Math.sign(t24), r24.unit);
  }
  return a15(t24 * $(i19.spatialReference), "meters");
}
e([y2()], Re.prototype, "_editGeometryOperations", void 0), e([y2()], Re.prototype, "_segmentLabels", void 0), e([y2({ constructOnly: true })], Re.prototype, "tool", void 0), e([y2()], Re.prototype, "_tooltip", void 0), e([y2()], Re.prototype, "inputGeometry", null), e([y2()], Re.prototype, "outputGeometry", void 0), e([y2({ readOnly: true })], Re.prototype, "updating", null), e([y2()], Re.prototype, "manipulators", null), e([y2()], Re.prototype, "view", null), e([y2()], Re.prototype, "graphic", null), e([y2()], Re.prototype, "enableZShape", null), e([y2()], Re.prototype, "enableZVertex", null), e([y2()], Re.prototype, "enableMoveGraphic", null), e([y2()], Re.prototype, "enableMidpoints", null), e([y2()], Re.prototype, "enableEdgeOffset", null), e([y2()], Re.prototype, "_labelOptions", null), e([y2()], Re.prototype, "_tooltipOptions", null), Re = e([a3("esri.views.3d.interactive.editingTools.reshapeGraphic.ReshapeOperation")], Re);
var ze = M2(0, 0, void 0, f3.WGS84);
var Xe = n4();
var Ze = 1e-6;
var Ye;
var qe;
var We;
!function(e20) {
  e20.Vertex = u5.Custom1, e20.Edge = u5.Custom2;
}(Ye || (Ye = {})), function(e20) {
  e20[e20.NONE = 0] = "NONE", e20[e20.MOVING = 1] = "MOVING", e20[e20.RESHAPING = 2] = "RESHAPING";
}(qe || (qe = {})), function(e20) {
  e20[e20.ALL = 0] = "ALL", e20[e20.SELECTED_OR_ALL = 1] = "SELECTED_OR_ALL";
}(We || (We = {}));

// node_modules/@arcgis/core/views/3d/interactive/editingTools/reshapeGraphic/GraphicReshapeTool.js
var v12 = class extends n3.EventedMixin(p12) {
  constructor(e20) {
    super(e20), this._handles = new t3(), this._internalGeometryUpdate = false, this.enableZShape = true, this.enableZVertex = true, this.enableMoveGraphic = true, this.enableMidpoints = true, this.enableEdgeOffset = false, this.type = "reshape-3d", this.labelOptions = new c5(), this.tooltipOptions = new p10(), this.snappingManager = null, this.automaticManipulatorSelection = false;
  }
  initialize() {
    const e20 = this._reshapeOperation = new Re({ tool: this });
    this.addHandles([e20.on("reshape", (e21) => {
      "reshape" === e21.type && this._onReshapeGeometryChanged(), this.emit("reshape", e21);
    }), e20.on("move", (e21) => {
      "move" === e21.type && this._onReshapeGeometryChanged(), this.emit("move", e21);
    }), e20.on("vertex-add", (e21) => {
      this._onReshapeGeometryChanged(), this.emit("vertex-add", e21);
    }), e20.on("vertex-remove", (e21) => {
      this._onReshapeGeometryChanged(), this.emit("vertex-remove", e21);
    }), e20.on("immediate-click", (e21) => this.emit("immediate-click", e21)), this.view.on("pointer-down", ["Shift"], (e21) => e21.stopPropagation()), l3(() => this.graphic, () => this._updateGraphic(), w3)]), this.finishToolCreation();
  }
  destroy() {
    this._handles = a(this._handles), this._reshapeOperation = a(this._reshapeOperation);
  }
  get updating() {
    var _a;
    return ((_a = this._reshapeOperation) == null ? void 0 : _a.updating) ?? false;
  }
  _updateGeometry() {
    const e20 = m5(this.graphic);
    this._reshapeOperation.inputGeometry = r(e20) ? e20.clone() : null;
  }
  _updateGraphic() {
    if (this._handles.remove("onGraphicGeometryChange"), this._updateGeometry(), l13(this.graphic) !== P4.SUPPORTED) return;
    const e20 = l3(() => {
      var _a;
      return (_a = this.graphic) == null ? void 0 : _a.geometry;
    }, () => {
      false === this._internalGeometryUpdate && this._updateGeometry();
    }, U2);
    this._handles.add(e20, "onGraphicGeometryChange");
  }
  onManipulatorSelectionChanged() {
    this._reshapeOperation && this._reshapeOperation.onManipulatorSelectionChanged();
  }
  _updateGeometryInternally(e20) {
    this._internalGeometryUpdate = true, this.graphic.geometry = e20, this._internalGeometryUpdate = false;
  }
  _onReshapeGeometryChanged() {
    const { outputGeometry: e20 } = this._reshapeOperation;
    !t(this.graphic) && e20 && this._updateGeometryInternally(e20.clone());
  }
  get canUndo() {
    return this._reshapeOperation.canUndo ?? false;
  }
  undo() {
    r(this.snappingManager) && this.snappingManager.doneSnapping();
    const e20 = this._reshapeOperation.undo(), { outputGeometry: t24 } = this._reshapeOperation;
    e20 && t24 && this._updateGeometryInternally(t24.clone());
  }
  get canRedo() {
    return this._reshapeOperation.canRedo ?? false;
  }
  redo() {
    r(this.snappingManager) && this.snappingManager.doneSnapping();
    const e20 = this._reshapeOperation.redo(), { outputGeometry: t24 } = this._reshapeOperation;
    e20 && t24 && this._updateGeometryInternally(t24.clone());
  }
  onInputEvent(e20) {
    "key-down" !== e20.type || "Delete" !== e20.key && "Backspace" !== e20.key || this._reshapeOperation.removeSelectedVertices();
  }
  reset() {
  }
  get test() {
    return { snappingManager: this.snappingManager, reshapeOperation: this._reshapeOperation };
  }
};
e([y2()], v12.prototype, "_reshapeOperation", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "view", void 0), e([y2({ constructOnly: true })], v12.prototype, "graphic", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "enableZShape", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "enableZVertex", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "enableMoveGraphic", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "enableMidpoints", void 0), e([y2({ constructOnly: true, nonNullable: true })], v12.prototype, "enableEdgeOffset", void 0), e([y2()], v12.prototype, "type", void 0), e([y2({ constructOnly: true, type: c5 })], v12.prototype, "labelOptions", void 0), e([y2({ constructOnly: true, type: p10 })], v12.prototype, "tooltipOptions", void 0), e([y2({ constructOnly: true })], v12.prototype, "snappingManager", void 0), e([y2()], v12.prototype, "updating", null), e([y2()], v12.prototype, "automaticManipulatorSelection", void 0), v12 = e([a3("esri.views.3d.interactive.editingTools.graphicReshape3D.GraphicReshapeTool")], v12);

// node_modules/@arcgis/core/views/interactive/tooltip/TransformTooltipInfos.js
var r21 = class extends t13 {
  constructor(o17) {
    super(o17), this.type = "transform-rotate", this.rotationType = "geographic";
  }
};
e([y2()], r21.prototype, "type", void 0), e([y2()], r21.prototype, "rotation", void 0), e([y2()], r21.prototype, "rotationPrecision", void 0), e([y2()], r21.prototype, "orientation", void 0), e([y2()], r21.prototype, "orientationPrecision", void 0), e([y2()], r21.prototype, "rotationType", void 0), r21 = e([a3("esri.views.interactive.tooltip.TransformRotateTooltipInfo")], r21);
var s16 = class extends t13 {
  constructor(o17) {
    super(o17), this.type = "transform-scale", this.sizeUnit = null, this.sizePrecision = null;
  }
};
e([y2()], s16.prototype, "type", void 0), e([y2()], s16.prototype, "scale", void 0), e([y2()], s16.prototype, "size", void 0), e([y2()], s16.prototype, "sizeUnit", void 0), e([y2()], s16.prototype, "sizePrecision", void 0), s16 = e([a3("esri.views.interactive.tooltip.TransformScaleTooltipInfo")], s16);
var p23 = class extends t13 {
  constructor(o17) {
    super(o17), this.type = "transform-absolute", this.orientationEnabled = true, this.orientationPrecision = null, this.rotationType = "geographic", this.sizeUnit = null, this.sizeEnabled = true, this.sizePrecision = null;
  }
};
e([y2()], p23.prototype, "type", void 0), e([y2()], p23.prototype, "orientation", void 0), e([y2()], p23.prototype, "orientationEnabled", void 0), e([y2()], p23.prototype, "orientationPrecision", void 0), e([y2()], p23.prototype, "rotationType", void 0), e([y2()], p23.prototype, "size", void 0), e([y2()], p23.prototype, "sizeUnit", void 0), e([y2()], p23.prototype, "sizeEnabled", void 0), e([y2()], p23.prototype, "sizePrecision", void 0), p23 = e([a3("esri.views.interactive.tooltip.TransformAbsoluteTooltipInfo")], p23);

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/GraphicScaleRotateTransform.js
var bt;
var wt;
!function(t24) {
  t24.ScaleIn = u5.Custom2, t24.ScaleOut = u5.Custom3, t24.RotateLeft = u5.Custom4, t24.RotateRight = u5.Custom5, t24.Unlocked = u5.Custom7, t24.DelayedFocused = u5.Custom8, t24.TouchInput = u5.Custom12;
}(bt || (bt = {}));
var Tt = class {
  get angle() {
    return this._adapter.angle;
  }
  get scale() {
    return this._adapter.scale;
  }
  set location(t24) {
    this._ringManipulator.location = t24;
  }
  set elevationAlignedLocation(t24) {
    this._ringManipulator.elevationAlignedLocation = t24;
  }
  get grabbing() {
    return this._ringManipulator.grabbing;
  }
  set interactive(t24) {
    this._ringManipulator.interactive = t24;
  }
  constructor({ adapter: t24, tooltipOptions: i19, mode: o17, tool: n21 }) {
    this._mode = null, this._handles = new t3(), this._scaleRotateDragData = null, this._activeAnimation = null, this._ringIndicatorDelayMs = u10, this.events = new n3(), this.getFocused = () => this._ringManipulator.focused, this.getScale = () => r(this._scaleRotateDragData) && "scale" === this._scaleRotateDragData.mode ? this._adapter.scale : 1, this.tool = n21, this._mode = o17, this._adapter = t24, this._tooltipOptions = i19, this._tooltip = new m6({ view: n21.view }), this._createManipulator(), this._updateDragState(), this._updateManipulatorTransform(), this._handles.add(f5(() => !this._tooltipOptions.enabled, () => this._tooltip.clear(), h4));
  }
  destroy() {
    r(this._activeAnimation) && (this._activeAnimation.frameTask.remove(), this._activeAnimation = null), this._handles = a(this._handles), this.tool.manipulators.remove(this._ringManipulator), this._ringManipulator = null, this._tooltip = a(this._tooltip);
  }
  startAnimation(t24) {
    this.cancelActiveAnimation(), t24.start();
    const e20 = A({ update: ({ deltaTime: e21 }) => {
      t24.update(e21) && this.cancelActiveAnimation();
    } });
    this._activeAnimation = { ...t24, frameTask: e20 };
  }
  cancelActiveAnimation() {
    r(this._activeAnimation) && (this._activeAnimation.frameTask.remove(), this._activeAnimation = a(this._activeAnimation));
  }
  forEachManipulator(t24) {
    t24(this._ringManipulator, A13.SCALE_ROTATE);
  }
  _createManipulator() {
    const e20 = this._createRingManipulator();
    this._ringManipulator = e20, this.tool.manipulators.add(e20);
    const a27 = this.tool.graphicState.graphic, n21 = x7(e20, (e21, n22, r24) => {
      this._scaleRotateDragData = null;
      const l22 = this._adapter.startInteraction(), c21 = { mode: "none", origin: t4(e21.renderLocation), initialAngle: this._adapter.angle, angle: 0, angleDir: 0, scaleDir: 0 };
      this._scaleRotateDragData = c21, this._updateDragState();
      const h25 = c2.get();
      this.tool.view.renderCoordsHelper.worldUpAtPosition(e21.renderLocation, h25), n22.next(C9(this.tool.view, _2(e21.renderLocation, h25, p6()))).next((e22) => {
        const n23 = Y(e22.plane), r25 = C7(e22.renderStart, e22.renderEnd, c21.origin, n23), h26 = r9.shortestSignedDiff(c21.angle, r25);
        c21.angleDir = a5(c21.angleDir + h26, -q6, q6), c21.angle = r25;
        const p25 = Rt(c21, e22), d15 = p25 - this._adapter.scale;
        if (c21.scaleDir = a5(c21.scaleDir + d15, -m13, m13), this._onScaleChanged(), "none" === c21.mode) {
          const t24 = this._mode || jt(e22, e22.plane, c21.origin, this.tool.view.state.camera);
          if (r(t24)) {
            switch (t24) {
              case "rotate":
                this.tool.emit("graphic-rotate-start", { graphic: a27, angle: 0 }), this.tool.emit("record-undo", { record: this._adapter.createUndoRecord() });
                break;
              case "scale":
                this.tool.emit("graphic-scale-start", { graphic: a27, xScale: 1, yScale: 1 }), this.tool.emit("record-undo", { record: this._adapter.createUndoRecord() });
            }
            c21.mode = t24;
          }
        }
        switch (c21.mode) {
          case "rotate":
            l22.state.angle = c21.initialAngle + r25;
            break;
          case "scale":
            l22.state.scale = p25, this._onScaleChanged();
        }
        switch (this._updateDragState(), this._updateManipulatorTransform(), e22.action) {
          case "start":
          case "update":
            switch (c21.mode) {
              case "rotate":
                this.tool.emit("graphic-rotate", { graphic: a27, angle: b(c21.angle) });
                break;
              case "scale":
                this.tool.emit("graphic-scale", { graphic: a27, xScale: p25, yScale: p25 });
            }
            break;
          case "end":
            switch (c21.mode) {
              case "rotate":
                this.tool.emit("graphic-rotate-stop", { graphic: a27, angle: b(c21.angle) });
                break;
              case "scale":
                this.tool.emit("graphic-scale-stop", { graphic: a27, xScale: p25, yScale: p25 });
            }
        }
        return "end" === e22.action && (this.startAnimation(It(this, () => this._onScaleChanged())), this._scaleRotateDragData = null, this._updateDragState(), l22.done()), e22;
      }).next(this._updateTooltipPipelineStep(c21)), r24.next(() => {
        if (l22.cancel(), r(this._scaleRotateDragData)) {
          switch (this._scaleRotateDragData.mode) {
            case "none":
              break;
            case "rotate":
              this.tool.emit("graphic-rotate-stop", { graphic: a27, angle: 0 });
              break;
            case "scale":
              this.tool.emit("graphic-scale-stop", { graphic: a27, xScale: 1, yScale: 1 });
          }
          this.startAnimation(It(this, () => this._onScaleChanged())), this._scaleRotateDragData = null, this._updateDragState();
        }
        this._updateFocusTooltip();
      });
    });
    this._handles.add([n21, e20.events.on("focus-changed", (t24) => {
      "focus" === t24.action ? this.startAnimation(At(this, () => this._updateDelayedFocusedState(), { delayMs: this._ringIndicatorDelayMs })) : this._updateDelayedFocusedState();
    }), e20.events.on("immediate-click", (t24) => {
      t24.stopPropagation();
    }), l3(() => {
      var _a;
      return (_a = this.tool.graphicState) == null ? void 0 : _a.displaying;
    }, (t24) => this._ringManipulator.available = t24, h4)]);
  }
  _updateTooltipPipelineStep(t24) {
    return (e20) => {
      const a27 = this._tooltipOptions;
      if (!a27.enabled) return e20;
      if ("end" === e20.action) return this._updateFocusTooltip(), e20;
      const i19 = this._tooltip, o17 = this._tooltipOptions.visualVariables, n21 = r(o17) ? o17.size : null, l22 = r(o17) ? o17.rotation : null;
      switch (t24.mode) {
        case "scale":
          i19.info = new s16({ tooltipOptions: a27, scale: { value: this._adapter.scale }, size: a15(l(this._adapter.size, -1), "meters"), sizeUnit: r(n21) ? n21.unit : null, sizePrecision: r(n21) ? kt(n21.valueType) : null });
          break;
        case "rotate": {
          const t25 = r(l22) ? kt(l22.valueType) : null, e21 = r(l22) ? l22.rotationType : "geographic";
          i19.info = new r21({ tooltipOptions: a27, rotation: c7(l(-this._adapter.relativeAngle, 0), "radians", "geographic"), rotationPrecision: t25, orientation: c7(-this._adapter.angle, "radians", "geographic"), orientationPrecision: t25, rotationType: e21 });
        }
      }
      return e20;
    };
  }
  _updateFocusTooltip() {
    if (!this._tooltipOptions.enabled) return;
    if (this.getFocused()) {
      const t24 = this._tooltipOptions.visualVariables, e20 = r(t24) ? t24.rotation : null, a27 = r(t24) ? t24.size : null;
      this._tooltip.info = new p23({ tooltipOptions: this._tooltipOptions, orientation: c7(-this._adapter.angle, "radians", "geographic"), orientationEnabled: null == this._mode || "rotate" === this._mode, orientationPrecision: r(e20) ? kt(e20.valueType) : null, rotationType: r(e20) ? e20.rotationType : "geographic", size: a15(l(this._adapter.size, -1), "meters"), sizeUnit: r(a27) ? a27.unit : null, sizeEnabled: null == this._mode || "scale" === this._mode, sizePrecision: r(a27) ? kt(a27.valueType) : null });
    } else this._tooltip.clear();
  }
  _onScaleChanged() {
    this.events.emit("scale-changed"), this._updateManipulatorTransform();
  }
  _updateDelayedFocusedState() {
    this._ringManipulator.updateStateEnabled(bt.DelayedFocused, this.getFocused()), this._updateFocusTooltip();
  }
  _updateDragState() {
    if (this._ringManipulator.updateStateEnabled(bt.Unlocked, !(r(this._scaleRotateDragData) && "none" !== this._scaleRotateDragData.mode)), r(this._scaleRotateDragData)) switch (this._scaleRotateDragData.mode) {
      case "rotate":
        this._ringManipulator.updateStateEnabled(bt.ScaleIn | bt.ScaleOut, false), this._ringManipulator.updateStateEnabled(bt.RotateLeft, this._scaleRotateDragData.angleDir < 0), this._ringManipulator.updateStateEnabled(bt.RotateRight, this._scaleRotateDragData.angleDir >= 0);
        break;
      case "scale":
        this._ringManipulator.updateStateEnabled(bt.RotateLeft | bt.RotateRight, false), this._ringManipulator.updateStateEnabled(bt.ScaleIn, this._scaleRotateDragData.scaleDir < 0), this._ringManipulator.updateStateEnabled(bt.ScaleOut, this._scaleRotateDragData.scaleDir >= 0);
    }
    else this._ringManipulator.updateStateEnabled(bt.ScaleIn | bt.ScaleOut | bt.RotateLeft | bt.RotateRight, false);
  }
  _updateManipulatorTransform() {
    const t24 = p4(f7.get(), this._adapter.angle, r3(0, 0, 1));
    if (t(t24)) return;
    const e20 = this.getScale(), a27 = g2(f7.get(), o3(c2.get(), e20, e20, e20));
    this._ringManipulator.modelTransform = c(f7.get(), a27, t24);
  }
  _createRingManipulator() {
    const t24 = (t25, e21, a28) => {
      const i20 = [], o18 = Math.ceil(a24 * (e21 - t25) / (2 * Math.PI));
      for (let s20 = 0; s20 < o18 + 1; s20++) {
        const n22 = t25 + s20 * (e21 - t25) / o18;
        i20.push(r3(a28 * Math.cos(n22), a28 * Math.sin(n22), 0));
      }
      return i20;
    }, e20 = (e21) => t24(0, 2 * Math.PI, e21), a27 = (t25) => [[-t25 / 2, 0], [t25 / 2, 0], [t25 / 2, i14 / 2], [-t25 / 2, i14 / 2]], i19 = this._createMaterial(1), o17 = (t25, e21, o18 = i19) => at(o18, a27(e21), t25, [], [], false), s19 = e20(P10), n21 = o17(s19, l18), r24 = { left: new Array(), right: new Array() }, l22 = [];
    for (let R10 = 0; R10 < 2; R10++) {
      const e21 = R10 * Math.PI - Math.PI / 4, a28 = Math.PI / 2 - b13, s20 = e21 + a28, n22 = e21 + Math.PI / 2 - a28, c22 = t24(s20, n22, p21), h26 = o17(c22, n16);
      l22.push(c22), l22.push(t24(s20, n22, r19 - l18 / 2)), r24.left.push(h26), r24.right.push(h26.instantiate());
      for (let t25 = 0; t25 < 2; t25++) {
        const e22 = 0 === t25, a29 = e6();
        if (e22) {
          f6(a29, a29, [1, -1, 1]), b2(a29, a29, -s20, [0, 0, 1]);
          const t26 = Math.round(g12 * (c22.length - 1));
          a29[12] = c22[t26][0], a29[13] = c22[t26][1], a29[14] = c22[t26][2];
        } else {
          b2(a29, a29, n22, [0, 0, 1]);
          const t26 = Math.round((1 - g12) * (c22.length - 1));
          a29[12] = c22[t26][0], a29[13] = c22[t26][1], a29[14] = c22[t26][2];
        }
        const o18 = pt(i19, s14, 0, x18, i14);
        Ot(o18, a29), (e22 ? r24.left : r24.right).push(o18);
      }
    }
    const c21 = [];
    for (let S14 = 0; S14 < 2; S14++) {
      const e21 = S14 * Math.PI - Math.PI / 4, a28 = Math.PI / 2 - d11, i20 = e21 + a28, s20 = e21 + Math.PI / 2 - a28, n22 = t24(i20, s20, r19);
      c21.push(o17(n22, n16));
    }
    const h25 = this._createMaterial(0.66), p25 = this._createMaterial(0.5), d15 = this._createMaterial(0.33), u13 = e20(P10 + j10), g16 = e20(P10 + k5), m17 = o17(u13, n16, h25), _9 = o17(g16, n16, d15), f19 = e20(P10 - j10), D11 = e20(P10 - k5), y12 = o17(f19, n16, h25), b16 = o17(D11, n16, d15);
    let w15 = [new e18(n21, bt.DelayedFocused), new e18(n21.instantiate({ material: p25 }), t11.None)];
    this._mode && "scale" !== this._mode || (w15 = w15.concat([...c21.map((t25) => new e18(t25, bt.DelayedFocused | bt.Unlocked)), new e18(m17, bt.DelayedFocused | bt.ScaleIn), new e18(_9, bt.DelayedFocused | bt.ScaleIn), new e18(y12, bt.DelayedFocused | bt.ScaleOut), new e18(b16, bt.DelayedFocused | bt.ScaleOut)])), this._mode && "rotate" !== this._mode || (w15 = w15.concat([...r24.right.map((t25) => new e18(t25.instantiate(), bt.DelayedFocused | bt.Unlocked)), ...r24.left.map((t25) => new e18(t25, bt.DelayedFocused | bt.RotateLeft)), ...r24.right.map((t25) => new e18(t25, bt.DelayedFocused | bt.RotateRight))]));
    const T9 = [s19, ...l22];
    return new ie({ view: this.tool.view, renderObjects: w15, autoScaleRenderObjects: false, worldOriented: true, radius: l18, focusMultiplier: 1, touchMultiplier: 1.5, elevationInfo: f10(this.tool.graphicState.graphic), collisionType: { type: "ribbon", paths: T9, direction: r3(0, 0, 1) } });
  }
  _createMaterial(t24) {
    const e20 = e4([...t19, t24]);
    return new f13({ color: e20, transparent: 1 !== t24, cullFace: n8.Back, renderOccluded: c3.Transparent });
  }
  get test() {
    return { ringManipulator: this._ringManipulator, setRingIndicatorDelayMs: (t24) => this._ringIndicatorDelayMs = t24, tooltip: this._tooltip };
  }
};
function Rt(t24, e20) {
  const a27 = e3(c2.get(), e20.renderStart, t24.origin), i19 = e3(c2.get(), e20.renderEnd, t24.origin), o17 = s2(a27), s19 = s2(i19);
  return 0 === o17 ? 0 : s19 / o17;
}
function jt(t24, e20, a27, i19) {
  const { renderStart: o17, renderEnd: s19 } = t24, n21 = Ot2(o17, i19, c2.get()), r24 = Ot2(s19, i19, c2.get());
  if (p2(n21, r24) < f16 * f16) return null;
  const l22 = e3(c2.get(), o17, a27), c21 = _(c2.get(), l22, Y(e20)), h25 = o17, p25 = u(c2.get(), h25, c21), d15 = Ot2(a27, i19, c2.get()), u13 = n21, g16 = Ot2(p25, i19, c2.get()), m17 = e3(c2.get(), g16, u13), _9 = e3(c2.get(), n21, d15), f19 = p5(u13, m17), D11 = p5(d15, _9);
  return b4(f19, r24) < b4(D11, r24) ? "rotate" : "scale";
}
function Ot2(t24, e20, a27) {
  return e20.projectToScreen(t24, Et), o3(a27, Et[0], Et[1], 0);
}
function It(t24, e20) {
  let a27 = null, i19 = 1;
  const o17 = () => i19;
  return { start: () => {
    i19 = t24.getScale(), a27 = t24.getScale, t24.getScale = o17, e20();
  }, update: (t25) => (i19 += ((i19 + 1) / 2 - i19) * Math.min(t25 * v11, 1), e20(), Math.abs(i19 - 1) < 0.01 ? wt.STOP : wt.CONTINUE), destroy: () => {
    a27 && (t24.getScale = a27), e20();
  } };
}
function At(t24, e20, a27) {
  let i19 = 0, o17 = null;
  const s19 = () => false;
  return { start: () => {
    o17 = t24.getFocused, t24.getFocused = s19, i19 = 0, e20();
  }, update: (t25) => (i19 += t25, !(o17 == null ? void 0 : o17()) || i19 >= a27.delayMs ? wt.STOP : wt.CONTINUE), destroy: () => {
    o17 && (t24.getFocused = o17), e20();
  } };
}
function kt(t24) {
  switch (t24) {
    case "integer":
    case "long":
      return 0;
    default:
      return null;
  }
}
!function(t24) {
  t24[t24.CONTINUE = 0] = "CONTINUE", t24[t24.STOP = 1] = "STOP";
}(wt || (wt = {}));
var Et = i3();

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/undoRecords.js
function r22(t24) {
  return r(t24.geometry) && "mesh" === t24.geometry.type ? o15(t24.geometry) : n17(t24);
}
function o15(t24) {
  return r(t24.transform) ? m14(t24, t24.transform) : i16(t24);
}
function n17(e20) {
  let r24 = e20.geometry, o17 = n;
  return { undo(e21) {
    o17 = e21.geometry, e21.geometry = r24;
  }, redo(e21) {
    r24 = e21.geometry, e21.geometry = o17;
  } };
}
function m14(t24, r24) {
  let o17 = r24.clone(), n21 = null;
  return { undo: (r25) => {
    n21 = r(t24.transform) ? t24.transform.clone() : null, t24.transform = o17, r25.notifyGeometryChanged();
  }, redo: (r25) => {
    o17 = r(t24.transform) ? t24.transform.clone() : null, t24.transform = n21, r25.notifyGeometryChanged();
  } };
}
function i16(e20) {
  let t24, r24 = e20.vertexAttributes.clonePositional();
  return { undo: (o17) => {
    t24 = e20.vertexAttributes.clonePositional(), e20.vertexAttributes = r24, o17.notifyGeometryChanged();
  }, redo: (o17) => {
    r24 = e20.vertexAttributes.clonePositional(), e20.vertexAttributes = t24, o17.notifyGeometryChanged();
  } };
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/ScaleRotateMeshAdapter.js
var d12 = class extends d3 {
  constructor(t24) {
    super(t24), this._interactionState = null;
  }
  initialize() {
    this.addHandles([f5(() => r(this._interactionState) && this._interactionState.angle !== this._interactionState.previousAngle ? { interactionState: this._interactionState, angle: this._interactionState.state.angle } : null, ({ interactionState: t24 }) => {
      this._updateMeshRotation(t24);
    }, U2), f5(() => r(this._interactionState) && this._interactionState.scale !== this._interactionState.previousScale ? { interactionState: this._interactionState, scale: this._interactionState.state.scale } : null, ({ interactionState: t24 }) => {
      this._updateMeshSize(t24);
    }, U2)]);
  }
  get angle() {
    const t24 = this.geometry.transform;
    if (t(t24)) return 0;
    const e20 = g3(t24.rotation)[2];
    return Math.abs(e20) > 0.999999 ? m2(l7(t24.rotation)) * Math.sign(e20) : 0;
  }
  get scale() {
    return r(this._interactionState) ? this._interactionState.scale : 1;
  }
  startInteraction() {
    const t24 = new y9({ angle: this.angle });
    this._interactionState = t24;
    const e20 = () => {
      this._interactionState = null;
    };
    return { state: t24, done: e20, cancel: () => {
      t24.cancel(), e20();
    } };
  }
  createUndoRecord() {
    return r22(this.graphic);
  }
  _updateMeshRotation(t24) {
    const e20 = this.geometry.anchor, i19 = this.viewingMode === l9.Global, { angle: o17, previousAngle: a27 } = t24;
    this.geometry.rotate(0, 0, b(o17 - a27), { origin: e20, geographic: i19 }), t24.previousAngle = o17, r(this.geometry.transform) && this.graphic.notifyMeshTransformChanged(), this.graphic.notifyGeometryChanged();
  }
  _updateMeshSize(t24) {
    const e20 = this.geometry.anchor, i19 = this.viewingMode === l9.Global, { scale: o17, previousScale: r24 } = t24;
    this.geometry.scale(o17 / r24, { origin: e20, geographic: i19 }), t24.previousScale = o17, r(this.geometry.transform) && this.graphic.notifyMeshTransformChanged(), this.graphic.notifyGeometryChanged();
  }
};
e([y2({ constructOnly: true })], d12.prototype, "graphic", void 0), e([y2({ constructOnly: true })], d12.prototype, "geometry", void 0), e([y2({ constructOnly: true })], d12.prototype, "viewingMode", void 0), e([y2()], d12.prototype, "angle", null), e([y2()], d12.prototype, "scale", null), e([y2()], d12.prototype, "_interactionState", void 0), d12 = e([a3("esri.views.3d.interactive.editingTools.transformGraphic.ScaleRotateMeshAdapter")], d12);
var y9 = class extends v2 {
  get state() {
    const { angle: t24, scale: e20 } = this;
    return { angle: t24, scale: e20 };
  }
  constructor(t24) {
    super(t24), this.angle = 0, this.initialAngle = 0, this.previousAngle = 0, this.previousScale = 1, this.scale = 1, this.initialAngle = t24.angle, this.previousAngle = t24.angle;
  }
  cancel() {
    this.angle = this.initialAngle, this.scale = 1;
  }
};
e([y2()], y9.prototype, "angle", void 0), e([y2()], y9.prototype, "initialAngle", void 0), e([y2()], y9.prototype, "previousAngle", void 0), e([y2()], y9.prototype, "previousScale", void 0), e([y2()], y9.prototype, "scale", void 0), e([y2()], y9.prototype, "state", null), y9 = e([a3("InteractionState")], y9);

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/ScaleRotateObjectSymbol3DAdapter.js
var y10 = class extends d3 {
  constructor(t24) {
    super(t24), this.sizeAxis = null, this._interactionState = null;
  }
  initialize() {
    this.addHandles(f5(() => r(this._interactionState) ? this._interactionState.state : null, (t24) => {
      this._updateSymbol(t24);
    }, U2));
  }
  get angle() {
    return r(this._interactionState) ? this._interactionState.angle : r(this._orientationReferenceSymbolLayer) ? g13(this._orientationReferenceSymbolLayer.heading ?? 0) : 0;
  }
  get scale() {
    return r(this._interactionState) ? this._interactionState.scale : 1;
  }
  get relativeAngle() {
    return this.angle - this.initialAngle;
  }
  get initialAngle() {
    return r(this._interactionState) ? this._interactionState.initialAngle : 0;
  }
  get size() {
    const t24 = this._sizeReferenceSymbolLayer;
    if (t(t24)) return null;
    const e20 = this.findLayerView(), i19 = this._graphicSymbol;
    if (t(e20) || t(i19) || "point-3d" !== i19.type) return null;
    const o17 = e20.getSymbolLayerSize(i19, t24);
    if ("size" in o17 && r(o17.size)) return o17.size;
    const r24 = this.sizeAxis;
    return "width" in o17 && r(o17.width) && (t(r24) || "width" === r24 || "all" === r24 || "width-and-depth" === r24) ? o17.width : "depth" in o17 && r(t24.depth) && (t(r24) || "depth" === r24 || "all" === r24 || "width-and-depth" === r24) ? o17.depth : "height" in o17 && r(t24.height) && (t(r24) || "height" === r24 || "all" === r24) ? o17.height : null;
  }
  get _sizeReferenceSymbolLayer() {
    const t24 = this._graphicSymbol;
    return t(t24) || 0 === t24.symbolLayers.length ? null : t24.symbolLayers.find((t25) => "object" === t25.type);
  }
  get _orientationReferenceSymbolLayer() {
    const t24 = this._graphicSymbol;
    return t(t24) || 0 === t24.symbolLayers.length ? null : t24.symbolLayers.find((t25) => "object" === t25.type && r(t25.heading));
  }
  get _graphicSymbol() {
    return r(this.graphic) && r(this.graphic.symbol) && "point-3d" === this.graphic.symbol.type ? this.graphic.symbol : null;
  }
  set _graphicSymbol(t24) {
    this.graphic.symbol = t24;
  }
  startInteraction() {
    const t24 = this._graphicSymbol, e20 = this.findLayerView();
    if (r(this._interactionState) || t(t24) || t(e20)) return m15;
    const i19 = t24.symbolLayers.map((i20) => "object" === i20.type ? e20.getSymbolLayerSize(t24, i20) : null).toArray(), o17 = t24.clone(), r24 = this.angle, s19 = new d13({ originalSymbol: o17, angle: r24, initialSizes: i19 });
    this._interactionState = s19;
    const a27 = () => {
      this._interactionState = null;
    };
    return { state: s19, done: a27, cancel: () => {
      this._graphicSymbol = o17, a27();
    } };
  }
  createUndoRecord() {
    let t24 = this.graphic.symbol, e20 = null;
    return { undo: (i19) => {
      e20 = i19.symbol, i19.symbol = t24;
    }, redo: (i19) => {
      t24 = i19.symbol, i19.symbol = e20;
    } };
  }
  _updateSymbol({ scale: t24, angle: e20, originalSymbol: i19, initialSizes: r24 }) {
    const a27 = this._graphicSymbol;
    if (t(a27) || "point-3d" !== a27.type) return;
    const c21 = a27.clone(), h25 = -b(e20 - this.initialAngle);
    let p25 = false;
    this._forEachObjectSymbolLayerPair(i19, c21, (e21, i20, o17) => {
      const l22 = l(e21.heading, 0) + h25;
      i20.heading !== l22 && (i20.heading = l22, p25 = true);
      const a28 = r24[o17];
      if (r(a28) && "width" in a28) {
        a28.width = this.sizeFilter(a28.width), a28.height = this.sizeFilter(a28.height), a28.depth = this.sizeFilter(a28.depth);
        const e22 = a28.width * t24;
        i20.width !== e22 && (i20.width = e22, p25 = true);
        const o18 = a28.depth * t24;
        i20.depth !== o18 && (i20.depth = o18, p25 = true);
        const r25 = a28.height * t24;
        i20.height !== r25 && (i20.height = r25, p25 = true);
      }
    }), p25 && (this._graphicSymbol = c21);
  }
  _forEachObjectSymbolLayerPair(t24, e20, i19) {
    t24.symbolLayers.forEach((t25, o17) => {
      const r24 = e20.symbolLayers.getItemAt(o17);
      "object" === t25.type && "object" === r24.type && i19(t25, r24, o17);
    });
  }
};
function g13(t24) {
  return -m2(t24);
}
e([y2()], y10.prototype, "angle", null), e([y2()], y10.prototype, "scale", null), e([y2()], y10.prototype, "relativeAngle", null), e([y2()], y10.prototype, "initialAngle", null), e([y2()], y10.prototype, "size", null), e([y2()], y10.prototype, "sizeAxis", void 0), e([y2({ constructOnly: true })], y10.prototype, "graphic", void 0), e([y2()], y10.prototype, "_interactionState", void 0), e([y2({ constructOnly: true })], y10.prototype, "findLayerView", void 0), e([y2({ constructOnly: true })], y10.prototype, "sizeFilter", void 0), e([y2()], y10.prototype, "_sizeReferenceSymbolLayer", null), e([y2()], y10.prototype, "_orientationReferenceSymbolLayer", null), e([y2()], y10.prototype, "_graphicSymbol", null), y10 = e([a3("esri.views.3d.interactive.editingTools.transformGraphic.ScaleRotateObjectSymbol3DAdapter")], y10);
var m15 = { state: { angle: 0, scale: 0 }, done: () => {
}, cancel: () => {
} };
var d13 = class extends v2 {
  get state() {
    const { originalSymbol: t24, angle: e20, initialAngle: i19, scale: o17, initialSizes: r24 } = this;
    return { originalSymbol: t24, angle: e20, initialAngle: i19, scale: o17, initialSizes: r24 };
  }
  constructor(t24) {
    super(t24), this.angle = 0, this.initialAngle = 0, this.scale = 1, this.initialAngle = t24.angle;
  }
};
e([y2()], d13.prototype, "originalSymbol", void 0), e([y2()], d13.prototype, "angle", void 0), e([y2()], d13.prototype, "initialAngle", void 0), e([y2()], d13.prototype, "initialSizes", void 0), e([y2()], d13.prototype, "scale", void 0), e([y2()], d13.prototype, "state", null), d13 = e([a3("InteractionState")], d13);

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/GraphicTransformTool.js
var H4 = class extends a10(n3.EventedMixin(p12)) {
  constructor(t24) {
    super(t24), this.enableZ = true, this.enableRotation = true, this.enableScaling = true, this.tooltipOptions = new p10(), this.type = "transform-3d", this._scaleRotate = null, this._tooltip = null;
  }
  initialize() {
    const { graphic: t24, view: e20 } = this;
    this.graphicState = new e17({ graphic: t24 }), this.addHandles(l3(() => this.tooltipOptions.enabled, (t25) => {
      this._tooltip = t25 ? new m6({ view: e20 }) : a(this._tooltip);
    }, w3)), this._moveManipulation = new u11({ tool: this, view: e20, snapToScene: this.snapToScene, xyAvailable: true, xyAxisAvailable: true, zAvailable: this.enableZ && t18(t24), radius: u11.radiusForSymbol(t24.symbol) }), this._moveManipulation.forEachManipulator((e21) => this.handles.add(e21.events.on("immediate-click", (e22) => {
      this.emit("immediate-click", { ...e22, graphic: t24 }), e22.stopPropagation();
    })));
    const i19 = (t25) => (e21) => {
      this.handles.add(e21.events.on("focus-changed", ({ action: e22 }) => {
        const i20 = this._tooltip;
        t(i20) || ("focus" === e22 ? this._updateMoveTooltip(t25) : i20.clear());
      }));
    };
    this._moveManipulation.xyManipulation.forEachManipulator(i19(M15.XY)), this._moveManipulation.xyAxisManipulation.forEachManipulator(i19(M15.XY_AXIS)), this._moveManipulation.zManipulation.forEachManipulator(i19(M15.Z));
    const r24 = f10(t24);
    this._moveManipulation.elevationInfo = r24;
    const l22 = t24.geometry;
    if (this._moveManipulation.createGraphicDragPipeline((i20, a27, s19, n21, p25) => {
      if (r(l22) && i20 === M15.XY) {
        const { snappingStep: i21, cancelSnapping: a28 } = m4({ snappingContext: new e12({ elevationInfo: r24, pointer: p25, editGeometryOperations: V.fromGeometry(new w({ spatialReference: l22.spatialReference }), e20.state.viewingMode), visualizer: new x14(), excludeFeature: t24 }), snappingManager: this.snappingManager, updatingHandles: this.updatingHandles, useZ: false });
        n21 = n21.next(a28), s19 = s19.next(Z3(this.view, r24)).next(...i21);
      }
      return { steps: s19 = s19.next((t25) => (this._updateMoveTooltip(i20, t25), t25)), cancel: n21 };
    }, this.graphicState, (t25) => {
      const { action: e21, graphic: i20, dxScreen: a27, dyScreen: o17 } = t25, s19 = { graphic: i20, dxScreen: a27, dyScreen: o17 };
      switch (e21) {
        case "start":
          this.emit("graphic-translate-start", s19), this.emit("record-undo", { record: this._createGeometryUndoRecord() });
          break;
        case "update":
          this.emit("graphic-translate", s19);
          break;
        case "end":
          this.emit("graphic-translate-stop", s19);
      }
    }), this._moveManipulation.angle = r(this._scaleRotate) ? this._scaleRotate.angle : 0, this._scaleRotateAdapter = this._createScaleRotateAdapter(), this.handles.add(l3(() => this._scaleRotateAdapter.angle, () => this._updateMoveAngle())), this.enableScaling || this.enableRotation) {
      const t25 = this.enableScaling && this.enableRotation ? null : this.enableScaling ? "scale" : "rotate";
      this._scaleRotate = new Tt({ tool: this, mode: t25, adapter: this._scaleRotateAdapter, tooltipOptions: this.tooltipOptions }), this.handles.add(this._scaleRotate.events.on("scale-changed", () => this._onScaleChanged()));
    }
    this.handles.add([i13({ view: this.view, graphic: this.graphic, forEachManipulator: (t25) => this._forEachManipulator(t25), onManipulatorsChanged: () => n2() }), this.graphicState.on("changed", () => this._onGeometryChanged()), this._hideManipulatorsForGraphicState(), l3(() => e20.scale, () => this._updateMoveAngle())]), this.handles.add(this.view.trackGraphicState(this.graphicState)), this._onGeometryChanged(), this._updateMoveAngle(), this._forEachManipulator((t25) => {
      t25 instanceof ie && this.handles.add(t25.events.on("grab-changed", () => this._updateManipulatorsInteractive()));
    }), this.finishToolCreation();
  }
  destroy() {
    this._tooltip = a(this._tooltip), this._moveManipulation.destroy(), this._scaleRotate = a(this._scaleRotate), this._scaleRotateAdapter = a(this._scaleRotateAdapter), this._set("view", null), this._set("graphic", null);
  }
  _updateManipulatorsInteractive() {
    t(this._scaleRotate) || (this._scaleRotate.interactive = !this._moveManipulation.grabbing, this._moveManipulation.interactive = !this._scaleRotate.grabbing);
  }
  _createScaleRotateAdapter() {
    return r(this.graphic.geometry) && "mesh" === this.graphic.geometry.type ? new d12({ graphic: this.graphic, geometry: this.graphic.geometry, viewingMode: this.view.state.viewingMode }) : new y10({ graphic: this.graphic, sizeFilter: (t24) => this._enforceNonZeroSize(t24), findLayerView: () => this.view.allLayerViews.find((t24) => t24.layer === this.graphic.layer), sizeAxis: r(this.tooltipOptions.visualVariables) && r(this.tooltipOptions.visualVariables.size) ? this.tooltipOptions.visualVariables.size.axis : null });
  }
  _forEachManipulator(t24) {
    this._moveManipulation.forEachManipulator(t24), r(this._scaleRotate) && this._scaleRotate.forEachManipulator(t24);
  }
  _hideManipulatorsForGraphicState() {
    return l3(() => this.graphicState.displaying, (t24) => {
      this._forEachManipulator((e20) => e20.available = t24), this._moveManipulation.zManipulation.available = t24 && this.enableZ && t18(this.graphic);
    });
  }
  _createGeometryUndoRecord() {
    return r22(this.graphic);
  }
  set snapToScene(t24) {
    this._moveManipulation && (this._moveManipulation.snapToScene = t24), this._set("snapToScene", t24);
  }
  get updating() {
    return this.updatingHandles.updating;
  }
  set location(t24) {
    this._moveManipulation.location = t24, r(this._scaleRotate) && (this._scaleRotate.location = t24);
  }
  set elevationAlignedLocation(t24) {
    this._moveManipulation.elevationAlignedLocation = t24, r(this._scaleRotate) && (this._scaleRotate.elevationAlignedLocation = t24);
  }
  reset() {
  }
  onHide() {
    r(this._scaleRotate) && this._scaleRotate.cancelActiveAnimation();
  }
  _onScaleChanged() {
    if (t(this._scaleRotate)) return;
    const t24 = this._scaleRotate.getScale();
    this._moveManipulation.displayScale = t24;
  }
  _updateMoveAngle() {
    this.view.state.viewingMode === l9.Local || this.view.scale < w11 ? this._moveManipulation.angle = this._scaleRotateAdapter.angle : this._moveManipulation.angle = 0;
  }
  _onGeometryChanged() {
    G6(this.view, this, this.graphic);
  }
  _enforceNonZeroSize(t24) {
    return t24 || this.view.state.camera.computeRenderPixelSizeAt(this._moveManipulation.renderLocation);
  }
  _updateMoveTooltip(t24, e20) {
    const { tooltipOptions: i19, _tooltip: a27 } = this;
    if (t(a27)) return;
    a27.clear();
    const o17 = this.graphicState.isDraped ? "on-the-ground" : "absolute-height";
    switch (t24) {
      case M15.XY:
        a27.info = new r16({ tooltipOptions: i19 }), this._updateMoveTooltipDistance(a27.info, e20, (t25, e21) => a16(t25, e21, o17));
        break;
      case M15.XY_AXIS:
        a27.info = new a19({ tooltipOptions: i19 }), this._updateMoveTooltipDistance(a27.info, e20, (t25, i20) => {
          const a28 = a16(t25, i20, o17);
          return y6(a28, c16(e20));
        });
        break;
      case M15.Z:
        a27.info = new p14({ tooltipOptions: i19 }), this._updateMoveTooltipDistance(a27.info, e20, v7);
    }
  }
  _updateMoveTooltipDistance(t24, e20, i19) {
    if (r(e20) && "end" !== e20.action) {
      const { mapStart: a27, mapEnd: s19 } = e20, n21 = i19(a27, s19);
      t24.distance = r(n21) ? n21 : j5;
    }
  }
  get test() {
    return { discManipulator: this._moveManipulation.xyManipulation.test.discManipulator, zManipulator: this._moveManipulation.zManipulation.test.manipulator, ringManipulator: r(this._scaleRotate) ? this._scaleRotate.test.ringManipulator : null, arrowManipulators: this._moveManipulation.xyAxisManipulation.test.arrowManipulators, setRingIndicatorDelayMs: (t24) => r(this._scaleRotate) ? this._scaleRotate.test.setRingIndicatorDelayMs(t24) : null, scaleRotateAdapter: this._scaleRotateAdapter, scaleRotateTransform: this._scaleRotate, tooltip: this._tooltip };
  }
};
e([y2({ constructOnly: true, nonNullable: true })], H4.prototype, "view", void 0), e([y2({ constructOnly: true, nonNullable: true })], H4.prototype, "graphic", void 0), e([y2({ constructOnly: true, nonNullable: true })], H4.prototype, "enableZ", void 0), e([y2()], H4.prototype, "enableRotation", void 0), e([y2()], H4.prototype, "enableScaling", void 0), e([y2({ constructOnly: true, type: p10 })], H4.prototype, "tooltipOptions", void 0), e([y2()], H4.prototype, "graphicState", void 0), e([y2({ value: false })], H4.prototype, "snapToScene", null), e([y2({ constructOnly: true })], H4.prototype, "snappingManager", void 0), e([y2({ readOnly: true })], H4.prototype, "type", void 0), e([y2({ readOnly: true })], H4.prototype, "updating", null), H4 = e([a3("esri.views.3d.interactive.editingTools.graphicTransform3D.GraphicTransformTool")], H4);

// node_modules/@arcgis/core/analysis/SlicePlane.js
var c18 = class extends i(l2) {
  constructor(o17) {
    super(o17), this.type = "plane", this.position = null, this.heading = 0, this.tilt = 0, this.width = 10, this.height = 10;
  }
  equals(o17) {
    return this.heading === o17.heading && this.tilt === o17.tilt && d(this.position, o17.position) && this.width === o17.width && this.height === o17.height;
  }
};
e([y2({ readOnly: true, json: { read: false, write: true } })], c18.prototype, "type", void 0), e([y2({ type: w }), g5()], c18.prototype, "position", void 0), e([y2({ type: Number, nonNullable: true, range: { min: 0, max: 360 } }), g5(), s((o17) => s7.normalize(a2(o17), 0, true))], c18.prototype, "heading", void 0), e([y2({ type: Number, nonNullable: true, range: { min: 0, max: 360 } }), g5(), s((o17) => s7.normalize(a2(o17), 0, true))], c18.prototype, "tilt", void 0), e([y2({ type: Number, nonNullable: true }), g5()], c18.prototype, "width", void 0), e([y2({ type: Number, nonNullable: true }), g5()], c18.prototype, "height", void 0), c18 = e([a3("esri.analysis.SlicePlane")], c18);

// node_modules/@arcgis/core/views/3d/analysis/Slice/sliceToolConfig.js
var c19 = has("mac") ? "Meta" : "Control";
var a26 = 2;
var f17 = 1.15;
var h21 = 1.15;
var p24 = Math.cos(m2(45));
var j13 = Math.cos(m2(5));
var k9 = 0.7;
var l20 = r3(1, 0.5, 0);
var b14 = e4([...l20, k9]);
var C11 = e4([...l20, 0.5]);
var S12 = r6(1, 1, 1, 1);
var U6 = r6(1, 0.8, 0.6, 1);
var d14 = r6(1, 0.93, 0.86, 1);
var g14 = e4([...l20, 1]);
var q9 = e4([...l20, 1]);
var w12 = 3;
var y11 = 11;
var z7 = 22.5;
var A15 = 40;
var B6 = 48;
var D9 = 2.25;
var E13 = e4([...l20, 1]);
var F7 = 4;
var G10 = 1;
var H5 = 0.3;
var I6 = 6;
var J4 = 4;

// node_modules/@arcgis/core/views/3d/interactive/visualElements/LineVisualElement.js
var _8 = e6();

// node_modules/@arcgis/core/views/3d/webgl-engine/shaders/SlicePlaneMaterialTechnique.js
var u12 = class _u extends e9 {
  initializeProgram(e20) {
    return new o9(e20.rctx, _u.shader.get().build(this.configuration), E3);
  }
  initializePipeline() {
    return W({ blending: l11(R3.ONE, R3.ONE, R3.ONE_MINUS_SRC_ALPHA, R3.ONE_MINUS_SRC_ALPHA), depthTest: { func: I.LESS }, colorWrite: _3 });
  }
};
u12.shader = new t8(g9, () => import("./SlicePlaneMaterial.glsl-77JLC6MR.js"));

// node_modules/@arcgis/core/geometry/support/coordinateSystem.js
var x21 = 2 ** 50;
var w13 = n4();
var H7 = n4();

// node_modules/@arcgis/core/views/3d/analysis/Slice/sliceToolUtils.js
function Be(e20, t24) {
  return V5(e20.basis1, e20.basis2, e20.origin, t24);
}
function qe2(e20, t24, n21, i19) {
  const o17 = t24.worldUpAtPosition(e20.origin, c2.get()), r24 = c2.get();
  switch (n21) {
    case It2.HEADING:
      r4(r24, o17);
      break;
    case It2.TILT:
      r4(r24, e20.basis1);
  }
  return _2(e20.origin, r24, i19);
}
function Je(e20, t24, n21, i19) {
  const o17 = s2(i19.basis1), r24 = s2(i19.basis2), s19 = it(i19), a27 = ot2(i19), d15 = o3(c2.get(), 0, 0, 0);
  u(d15, g(c2.get(), i19.basis1, t24.direction[0]), g(c2.get(), i19.basis2, t24.direction[1])), u(d15, i19.origin, d15);
  let m17 = 0, p25 = 1;
  if (rt(t24)) 1 === t24.direction[0] && -1 === t24.direction[1] ? m17 = Lt : 1 === t24.direction[0] && 1 === t24.direction[1] ? m17 = Math.PI : -1 === t24.direction[0] && 1 === t24.direction[1] && (m17 = 3 * Math.PI / 2), p25 = a27;
  else {
    const e21 = 0 !== t24.direction[0] ? 1 : 2;
    m17 = 1 === e21 ? Lt : 0, p25 = (1 === e21 ? r24 : o17) - s19;
  }
  const g16 = R2(f7.get(), m17);
  f6(g16, g16, o3(c2.get(), p25, p25, p25)), c(g16, n21, g16), g16[12] = 0, g16[13] = 0, g16[14] = 0, e20.modelTransform = g16, e20.renderLocation = d15;
}
function Ke(e20, t24, n21, i19) {
  const o17 = i19.worldUpAtPosition(n21.origin, c2.get()), r24 = tt(n21, et.POSITIVE_X), s19 = R2(f7.get(), r24.edge * Math.PI / 2);
  l5(s19, s19, -bt2(n21, o17)), c(s19, t24, s19), s19[12] = 0, s19[13] = 0, s19[14] = 0, e20.modelTransform = s19, e20.renderLocation = r24.position;
}
var et;
function tt(e20, t24) {
  switch (t24) {
    case et.POSITIVE_X:
      return { basis: e20.basis1, direction: 1, position: u(c2.get(), e20.origin, e20.basis1), edge: t24 };
    case et.POSITIVE_Y:
      return { basis: e20.basis2, direction: 1, position: u(c2.get(), e20.origin, e20.basis2), edge: t24 };
    case et.NEGATIVE_X:
      return { basis: e20.basis1, direction: -1, position: e3(c2.get(), e20.origin, e20.basis1), edge: t24 };
    case et.NEGATIVE_Y:
      return { basis: e20.basis2, direction: -1, position: e3(c2.get(), e20.origin, e20.basis2), edge: t24 };
  }
}
function it(e20) {
  const t24 = s2(e20.basis1), n21 = s2(e20.basis2);
  return H5 * Math.min(t24, n21);
}
function ot2(e20) {
  return it(e20);
}
function rt(e20) {
  return 0 !== e20.direction[0] && 0 !== e20.direction[1];
}
function st(e20, t24 = Nt.CENTER_ON_ARROW) {
  const n21 = t24 === Nt.CENTER_ON_CALLOUT ? A15 : 0, i19 = [r3(n21, 0, -B6 / 2), r3(n21, 0, B6 / 2)], o17 = pt2(i19, true), r24 = (e21, t25) => dt(i19, e21, t25), s19 = r24(0, false), a27 = r24(D9, true), c21 = new q5({ color: g14, renderOccluded: c3.OccludeAndTransparent }), l22 = ft(c21, [[n21, 0, 0], [n21 - A15, 0, 0]]), u13 = ft(c21, [[n21, 0, 0], [n21 - A15, 0, 0]]);
  return new ie({ view: e20, renderObjects: [...s19.normal.map((e21) => new e18(e21, t11.Unfocused | Et2)), ...a27.normal.map((e21) => new e18(e21, t11.Unfocused | Et2)), new e18(l22, t11.Unfocused | Et2 | jt2), ...s19.focused.map((e21) => new e18(e21, t11.Focused | Et2)), ...a27.focused.map((e21) => new e18(e21, t11.Focused | Et2)), new e18(u13, t11.Focused | Et2 | jt2)], autoScaleRenderObjects: false, collisionType: { type: "line", paths: [o17] }, collisionPriority: 1, radius: y11, state: Et2 });
}
function ut2(e20, t24) {
  const n21 = rt(t24), i19 = n21 ? [r3(1, 0, 0), r3(0, 0, 0), r3(0, 1, 0)] : [r3(1, 0, 0), r3(-1, 0, 0)], o17 = E13, r24 = (e21) => new z3({ color: o17, width: e21, renderOccluded: c3.OccludeAndTransparent }), s19 = () => new q5({ color: o17, renderOccluded: c3.OccludeAndTransparent }), a27 = n21 ? F7 : G10, c21 = a27 * a26, l22 = G10, u13 = (e21) => e21 > 1 ? r24(e21) : s19(), d15 = [new e18(ft(u13(a27), i19), t11.Unfocused | _t), new e18(ft(u13(c21), i19), t11.Focused | _t), new e18(ft(u13(l22), i19), Ct)], m17 = new ie({ view: e20, renderObjects: d15, collisionType: { type: "line", paths: [i19] }, radius: n21 ? I6 : J4, ...x17 });
  return m17.state = _t, m17;
}
function dt(e20, t24, n21) {
  const i19 = new f13({ color: S12, cullFace: n8.Back, renderOccluded: c3.Opaque }), o17 = new f13({ color: U6, cullFace: n8.Back, renderOccluded: c3.Opaque }), r24 = new f13({ color: d14, cullFace: n8.Back, renderOccluded: c3.Opaque }), s19 = new f13({ color: q9, transparent: true, writeDepth: false, cullFace: n8.Front, renderOccluded: c3.Transparent }), a27 = (a28) => {
    e20 = e20.slice(0);
    const c21 = e3(c2.get(), e20[0], e20[1]);
    z2(c21, c21);
    const d15 = e3(c2.get(), e20[e20.length - 1], e20[e20.length - 2]);
    if (z2(d15, d15), t24 > 0) {
      const n22 = g(n4(), d15, -t24);
      e20[e20.length - 1] = u(n22, n22, e20[e20.length - 1]);
      const i20 = g(n4(), c21, -t24);
      e20[0] = u(i20, i20, e20[0]);
    }
    const T9 = a28 ? h21 : 1, w15 = z7 * T9, O8 = y11 * T9, R10 = r7(f7.get());
    if (t24 > 0) {
      const e21 = w15 / 4, n22 = o3(c2.get(), 0, e21, 0), i20 = 1 + t24 / e21;
      i4(R10, R10, n22), f6(R10, R10, o3(c2.get(), i20, i20, i20)), i4(R10, R10, g(n22, n22, -1 / i20));
    }
    const L8 = r7(e6()), _9 = r3(0, 1, 0), C12 = A3(e6(), Q(o7.get(), _9, d15));
    C12[12] = e20[e20.length - 1][0], C12[13] = e20[e20.length - 1][1], C12[14] = e20[e20.length - 1][2], c(C12, C12, R10);
    const M16 = mt(w12 * (a28 ? f17 : 1) + t24, e20, n21 ? s19 : r24);
    M16.transformation = L8;
    const v15 = [M16], y12 = lt(n21 ? s19 : i19, w15, O8, 24, false, false, true);
    y12.transformation = C12, v15.push(y12);
    const U8 = lt(n21 ? s19 : o17, w15, O8, 24, false, true, false);
    U8.transformation = C12, v15.push(U8);
    const V7 = A3(e6(), Q(o7.get(), _9, c21));
    return V7[12] = e20[0][0], V7[13] = e20[0][1], V7[14] = e20[0][2], c(V7, V7, R10), v15.push(y12.instantiate({ transformation: V7 })), v15.push(U8.instantiate({ transformation: V7 })), v15;
  };
  return { normal: a27(false), focused: a27(true) };
}
function mt(e20, t24, n21) {
  const i19 = [], o17 = 12;
  for (let r24 = 0; r24 < o17; r24++) {
    const t25 = r24 / o17 * 2 * Math.PI;
    i19.push([Math.cos(t25) * e20, Math.sin(t25) * e20]);
  }
  return at(n21, i19, t24, [], [], false);
}
function pt2(e20, t24) {
  const n21 = e3(n4(), e20[e20.length - 1], e20[e20.length - 2]);
  if (z2(n21, n21), g(n21, n21, z7), u(n21, n21, e20[e20.length - 1]), t24) {
    const t25 = e3(n4(), e20[0], e20[1]);
    return z2(t25, t25), g(t25, t25, z7), u(t25, t25, e20[0]), [t25, ...e20, n21];
  }
  return [...e20, n21];
}
function bt2(e20, t24) {
  return i6(t24, e20.basis2, e20.basis1) + Lt;
}
!function(e20) {
  e20[e20.POSITIVE_X = 0] = "POSITIVE_X", e20[e20.POSITIVE_Y = 1] = "POSITIVE_Y", e20[e20.NEGATIVE_X = 2] = "NEGATIVE_X", e20[e20.NEGATIVE_Y = 3] = "NEGATIVE_Y";
}(et || (et = {}));
var Et2 = u5.Custom1;
var It2;
var Rt2;
!function(e20) {
  e20[e20.HEADING = 1] = "HEADING", e20[e20.TILT = 2] = "TILT";
}(It2 || (It2 = {})), function(e20) {
  e20[e20.HORIZONTAL_OR_VERTICAL = 0] = "HORIZONTAL_OR_VERTICAL", e20[e20.HORIZONTAL = 1] = "HORIZONTAL", e20[e20.VERTICAL = 2] = "VERTICAL", e20[e20.TILTED = 3] = "TILTED";
}(Rt2 || (Rt2 = {}));
var jt2 = u5.Custom2;
var At2 = d4();
var Lt = Math.PI / 2;
var _t = u5.Custom1;
var Ct = u5.Custom2;
var Nt;
!function(e20) {
  e20[e20.CENTER_ON_CALLOUT = 0] = "CENTER_ON_CALLOUT", e20[e20.CENTER_ON_ARROW = 1] = "CENTER_ON_ARROW";
}(Nt || (Nt = {}));

// node_modules/@arcgis/core/views/3d/analysis/Slice/images/heading-rotate-png.js
var A16 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAnFBMVEUAAAD/gAD/gAD/cAD/gAD/eAD/gAD/eQD/gAD/egD/gAD/ewD/gAD/fAD/gAD/fAD/gAD/fAD/fQD/fQD/fQD/fQD/fQD/fgD/jR7/mjn/mjf/p1H/plH/smf/sWb/vHr/u3n/xYz/xIv/zZz/zJv/zJr/1Kv/1Kr/06r/06n/27n/27f/4cX/4cT/59D/7dr/8uX/9u7/+/f////u2EN0AAAAM3RSTlMACBAQICAoKDAwODhAQEhIUFBYYGhweICIkJCXmJ+gp6ivsLe4uL+/wMDHx8/P19/n7/cWvjXwAAACeUlEQVR42tWX3XqiMBCGY2pbtbrUnzhhdak/lHWliJD7v7fdJ+KG5AMhh30P8zCTmS+TycDaeBoHi5Wgf4jVYvbKmRfPgSAHMX9mPRnM1tSIGHM/c2QddLp4c8wxCvYIvqROBPfbHlm/sRYC6smMNTKn3sxZAyvyYNW1v38MM/IkcPQnZHPMLtciz9P9hhqwzoLD+cnfpTIUaYinyZlBkE2YKZcMXCyN/YhsPkuFlMfWJLiwo89VMxfpJDForMCwuG+Zx7ttGO2S/w4LJ42ZURDty5M0a4dqsZAQAihQfXqWdlhnpcmdEPAI0tv2EbnsbsKmdgi6/1GN7T1XJLx5sF0P9SWABMC+co5JBE4Ge/1NTM3EGIJgjFONXCdAbeQYwhN7pRrRV20LJNIhWOczdu+xPFzIBiQ62iIsyIOTvlZUY+HXySLQaMUEeSC1CPYxENIlwk+q8e0clFAIfiKG+qpaIvod4wfU8sqvkDLda+xCCqgDaAk7uyeNqD+feFlfGCcg3Hzsk+xS7Nz1Aq4CcauhhMc0uxaqIgcFsF0J+1WQyoCN7Y9ezeCVH5LhSxmyRvsihKbK1m7LafpSpkpj6yJgtsiVBh6AX5UyCVmMbrNpcwj5/h6DPN79JjAiQAhXVeN6SZI0q5bQnn4wBiHEqpUybp1ZJzWxStVCHhKhAhVLp/Emh6trHpGLaB6yZHk7wu3Z+ChOxhwUNEmYYjpUvqJDksSHraQmJm2DdqQK6sGUObybYtpSN+8Phm3pN2xjDH33R6b0CKxAZNLvl8foD3BBnSw5e8RI+G2P8GD9wHw6YN3wkfA0R4Zz8CGCIfOCv8zM738walXuLw6nXBvPr8wvAAAAAElFTkSuQmCC";

// node_modules/@arcgis/core/views/3d/analysis/Slice/images/Factory.js
var n20 = { mipmap: true, preMultiplyAlpha: true, width: 64, height: 64 };
function o16(r24) {
  return r24.fromData(A16, () => new G2(A16, n20));
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/PreserveAspectRatio.js
var s18 = class {
  constructor() {
    this._lastDragEvent = null, this._next = null, this._enabled = false;
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(a27) {
    if (this._enabled !== a27 && r(this._lastDragEvent) && r(this._next)) {
      const t24 = { ...this._lastDragEvent, action: "update" };
      a27 && this._adjustScaleFactors(t24), this._next.execute(t24);
    }
    this._enabled = a27;
  }
  createDragEventPipelineStep() {
    this._lastDragEvent = null;
    const t24 = new U3();
    return this._next = t24, [(t25) => (this._lastDragEvent = "end" !== t25.action ? { ...t25 } : null, this._enabled && this._adjustScaleFactors(t25), t25), t24];
  }
  _adjustScaleFactors(t24) {
    const e20 = rt(t24.handle) ? Math.max(Math.abs(t24.factor1), Math.abs(t24.factor2)) : 0 === t24.handle.direction[0] ? Math.abs(t24.factor2) : Math.abs(t24.factor1);
    t24.factor1 = t24.factor1 < 0 ? -e20 : e20, t24.factor2 = t24.factor2 < 0 ? -e20 : e20;
  }
  get test() {
    return { _adjustScaleFactors: (t24) => this._adjustScaleFactors(t24) };
  }
};

// node_modules/@arcgis/core/views/3d/interactive/editingTools/transformGraphic/ExtentTransformTool.js
var Zt = class extends n3.EventedMixin(p12) {
  constructor(t24) {
    super(t24), this.enableZ = true, this.enableRotation = true, this.enableScaling = true, this.tooltipOptions = new p10(), this._preserveAspectRatio = new s18(), this.grabbing = false, this.inputState = null, this.type = "transform-3d", this._handles = new t3(), this._attachmentOrigin = null, this._outlineVisualElement = null, this._mapBounds = W2(), this._mapBoundsStart = W2(), this._zmax = 0, this._sizeStart = null, this._displayBounds = W2(), this._displayBoundsStart = W2(), this._displayBoundsMarginStart = 0, this._resizeHandles = [{ direction: [1, 0] }, { direction: [1, 1] }, { direction: [0, 1] }, { direction: [-1, 1] }, { direction: [-1, 0] }, { direction: [-1, -1] }, { direction: [0, -1] }, { direction: [1, -1] }], this._moveXYTooltipInfo = null, this._moveZTooltipInfo = null, this._rotateTooltipInfo = null, this._scaleTooltipInfo = null, this._startAngle = 0, this._endAngle = 0, this._startScale = n9(), this._endScale = n9();
  }
  initialize() {
    const { view: t24, graphic: e20, manipulators: i19, _handles: s19 } = this, r24 = this._graphicState = new e17({ graphic: e20 }), n21 = e20.geometry;
    this._editGeometryOperations = V.fromGeometry(n21, t24.state.viewingMode), this._graphicMoveManipulation = new l19({ tool: this, view: t24, graphicState: r24 }), this._moveZManipulator = st(t24, Nt.CENTER_ON_CALLOUT), this._moveZManipulator.state |= jt2, s19.add([this._createMoveXYGraphicDragPipeline(), l3(() => this.enableZ, () => this._updateManipulatorAvailability(this._moveZManipulator, A13.TRANSLATE_Z)), this._createMoveZDragPipeline()]), i19.add(this._moveZManipulator), this._resizeManipulators = this._resizeHandles.map((e21) => {
      const i20 = ut2(t24, e21);
      return s19.add([l3(() => this.enableScaling, () => this._updateManipulatorAvailability(i20, A13.SCALE)), i20.events.on("grab-changed", (t25) => this._onResizeGrab(t25)), this._createResizeDragPipeline(i20, e21)]), i20;
    }), i19.addMany(this._resizeManipulators), this._rotateManipulatorTexture = o16(t24.toolViewManager.textures), this._rotateManipulator = U4(t24, { texture: this._rotateManipulatorTexture.texture }), s19.add([l3(() => this.enableRotation, () => this._updateManipulatorAvailability(this._rotateManipulator, A13.ROTATE)), this._rotateManipulator.events.on("grab-changed", (t25) => {
      this._onRotateGrab(t25);
    }), this._createRotateDragPipeline(this._rotateManipulator)]), i19.add(this._rotateManipulator), this._calculateMapBounds(), this._updateDisplayBounds();
    const p25 = i13({ view: t24, graphic: e20, forEachManipulator: (t25) => this._forEachManipulator(t25), onManipulatorsChanged: () => n2() });
    r(p25) && (this._outlineVisualElement = p25.visualElement instanceof I4 ? p25.visualElement : null), r(this._outlineVisualElement) && s19.add(this._outlineVisualElement.events.on("attachment-origin-changed", () => this._updateDisplayBounds())), s19.add(p25), s19.add([r24.on("changed", () => this._onGeometryChanged()), l3(() => r24.displaying, () => this._updateAllManipulatorAvailability()), l3(() => r24.isDraped, () => this._graphicDrapedChanged(), h4), t24.trackGraphicState(r24)]);
    const l22 = t24.pointsOfInterest;
    s19.add(l3(() => l22 == null ? void 0 : l22.centerOnSurfaceFrequent.location, () => this._updateDisplayBounds()));
    const d15 = (t25) => {
      s19.add(t25.events.on("grab-changed", () => {
        this.grabbing = t25.grabbing, this._updateAllManipulatorAvailability();
      }));
    };
    this._forEachManipulator(d15);
    const u13 = (t25, i20) => {
      s19.add(t25.events.on("immediate-click", (t26) => {
        i20 === A13.TRANSLATE_XY && this.emit("immediate-click", { ...t26, graphic: e20 }), t26.stopPropagation();
      }));
    };
    this._forEachManipulator(u13), this._onGeometryChanged(), this._updateAllManipulatorAvailability(), this._initializeTooltip(), this.finishToolCreation();
  }
  destroy() {
    this._mapBounds = null, this._displayBounds = null, this._rotateManipulatorTexture.release(), this._handles.destroy(), this._graphicMoveManipulation.destroy(), this._editGeometryOperations.destroy(), this._tooltip.destroy(), this._set("view", null), this._set("graphic", null);
  }
  _initializeTooltip() {
    const { _handles: t24, view: e20 } = this, i19 = this._tooltip = new m6({ view: e20 }), a27 = () => {
      i19.info = this._getUpdatedTooltipInfo();
    };
    t24.add([this.on("graphic-translate-start", a27), this.on("graphic-translate", a27), this.on("graphic-translate-stop", () => {
      this._moveXYTooltipInfo = null, this._moveZTooltipInfo = null, this._tooltip.clear();
    }), this.on("graphic-rotate-start", (t25) => {
      this._startAngle = t25.angle, a27();
    }), this.on("graphic-rotate", (t25) => {
      this._endAngle = t25.angle, a27();
    }), this.on("graphic-rotate-stop", () => {
      this._startAngle = 0, this._endAngle = 0, a27();
    }), this.on("graphic-scale-start", (t25) => {
      r8(this._startScale, t25.xScale, t25.yScale), r8(this._endScale, t25.xScale, t25.yScale), a27();
    }), this.on("graphic-scale", (t25) => {
      r8(this._endScale, t25.xScale, t25.yScale), a27();
    }), this.on("graphic-scale-stop", () => {
      r8(this._startScale, 0, 0), r8(this._endScale, 0, 0), a27();
    })]), this._forEachManipulator((e21) => {
      t24.add([e21.events.on("focus-changed", a27), e21.events.on("grab-changed", a27), e21.events.on("drag", (t25) => {
        "cancel" === t25.action ? this._tooltip.clear() : a27();
      })]);
    });
  }
  _getUpdatedTooltipInfo() {
    return this.tooltipOptions.enabled ? this._graphicMoveManipulation.grabbing || this._graphicMoveManipulation.dragging ? this._computeMoveXYTooltipInfo() : this._moveZManipulator.focused ? this._computeMoveZTooltipInfo() : this._rotateManipulator.focused ? this._computeRotateTooltipInfo() : this._resizeManipulators.some((t24) => t24.focused) ? this._computeScaleTooltipInfo() : null : null;
  }
  _computeMoveXYTooltipInfo() {
    return this._moveXYTooltipInfo = l(this._moveXYTooltipInfo, () => new r16({ tooltipOptions: this.tooltipOptions }));
  }
  _computeMoveZTooltipInfo() {
    const t24 = this._moveZTooltipInfo = l(this._moveZTooltipInfo, () => new p14({ tooltipOptions: this.tooltipOptions })), e20 = this._moveUnit;
    if (this._moveZManipulator.dragging) {
      const e21 = this._mapBoundsStart.origin, i19 = this._mapBounds.origin, a27 = D3(e21, i19, this.view.spatialReference);
      if (t(a27)) return null;
      t24.distance = a27;
    } else t24.distance = a15(0, e20);
    return t24;
  }
  _computeRotateTooltipInfo() {
    const t24 = this._rotateTooltipInfo = l(this._rotateTooltipInfo, () => new i12({ tooltipOptions: this.tooltipOptions }));
    return t24.angle = this._startAngle - this._endAngle, t24;
  }
  _computeScaleTooltipInfo() {
    const t24 = this.graphic.geometry;
    if (t(t24)) return null;
    const e20 = this._scaleTooltipInfo = l(this._scaleTooltipInfo, () => new p15({ tooltipOptions: this.tooltipOptions })), i19 = M6(this._mapBounds, this._zmax, t24.spatialReference, this._graphicState.isDraped);
    return t(i19) ? null : (e20.xSize = i19[0], e20.ySize = i19[1], r(this._sizeStart) && this._resizeManipulators.some((t25) => t25.dragging) ? (e20.xScale = i19[0].value / this._sizeStart[0].value, e20.yScale = i19[1].value / this._sizeStart[1].value) : (e20.xScale = 1, e20.yScale = 1), e20);
  }
  _graphicDrapedChanged() {
    this._handles.remove(Ct2), this._updateDisplayBounds(), this._graphicState.isDraped && this._handles.add(this.view.elevationProvider.on("elevation-change", (t24) => {
      r(this._attachmentOrigin) && w2(t24.extent, this._attachmentOrigin.x, this._attachmentOrigin.y) && this._updateDisplayBounds();
    }), Ct2);
  }
  _updateAllManipulatorAvailability() {
    this._forEachManipulator((t24, e20) => this._updateManipulatorAvailability(t24, e20));
  }
  _updateManipulatorAvailability(t24, e20) {
    const i19 = this.grabbing && !t24.grabbing;
    if (t24.interactive = !i19, t24 instanceof ie) {
      const a27 = this._graphicState.displaying, s19 = this.enableZ && t18(this.graphic);
      switch (e20) {
        case A13.ROTATE:
          t24.available = a27 && this.enableRotation;
          break;
        case A13.SCALE:
          t24.available = a27 && (this.enableScaling || this.enableRotation || s19), t24.interactive = !i19 && this.enableScaling, t24.state = this.enableScaling ? _t : Ct;
          break;
        case A13.TRANSLATE_Z:
          t24.available = a27 && s19;
          break;
        default:
          t24.available = a27;
      }
    }
  }
  _forEachManipulator(t24) {
    this._graphicMoveManipulation.forEachManipulator(t24), this._resizeManipulators.forEach((e20) => t24(e20, A13.SCALE)), t24(this._rotateManipulator, A13.ROTATE), t24(this._moveZManipulator, A13.TRANSLATE_Z);
  }
  get preserveAspectRatio() {
    return this._preserveAspectRatio.enabled;
  }
  set preserveAspectRatio(t24) {
    this._preserveAspectRatio.enabled = t24, this._set("preserveAspectRatio", t24);
  }
  get _moveUnit() {
    return l(Z(this.view.spatialReference), "meters");
  }
  reset() {
  }
  _onGeometryChanged() {
    this._updateDisplayBounds();
  }
  _calculateMapBounds() {
    const t24 = this.graphic.geometry, e20 = this._editGeometryOperations.data, i19 = e20.components[0].edges[0], a27 = o4(t6.get(), i19.leftVertex.pos, i19.rightVertex.pos);
    v4(a27, a27);
    const s19 = l(z6(this.view, this.graphic), () => {
      var _a;
      return (_a = t24.extent) == null ? void 0 : _a.center;
    });
    let o17 = s19 ? Xt * this.view.pixelSizeAt(s19) : 0;
    const n21 = this.view.spatialReference, p25 = t24.spatialReference;
    n21.equals(p25) || (o17 *= $(n21) / $(p25)), A8(a27, e20, o17, this._mapBounds), this._updateZMax();
  }
  _updateZMax() {
    const t24 = this._editGeometryOperations.data;
    if (!t24.geometry.hasZ) return void (this._zmax = 0);
    const e20 = t24.coordinateHelper;
    let i19 = Number.NEGATIVE_INFINITY;
    for (const a27 of t24.components) for (const t25 of a27.vertices) {
      const a28 = e20.getZ(t25.pos) ?? 0;
      i19 = Math.max(a28, i19);
    }
    this._zmax = i19;
  }
  _updateDisplayBounds() {
    const { geometry: t24 } = this.graphic;
    if (t(t24)) return;
    const { extent: e20 } = t24;
    if (!e20) return;
    const i19 = r(this._outlineVisualElement) && !this._graphicState.isDraped && r(this._outlineVisualElement.attachmentOrigin) ? this._outlineVisualElement.attachmentOrigin : z6(this.view, this.graphic);
    this._attachmentOrigin = l(i19, e20.center);
    const a27 = r(i19) ? i19.z : d7(this._mapBounds.origin, this.view.elevationProvider, h13.fromElevationInfo(f10(this.graphic)), this.view.renderCoordsHelper), s19 = Z4(this._mapBounds);
    s19.origin[2] = a27 ?? 0, G3(s19, this.view.renderCoordsHelper, t24.spatialReference, this._displayBoundsMargin, this._displayBounds), this._updateManipulators();
  }
  get _displayBoundsMargin() {
    var _a;
    const t24 = this.view.pointsOfInterest, e20 = t24 ? t24.centerOnSurfaceFrequent.location : (_a = this._editGeometryOperations.data.geometry.extent) == null ? void 0 : _a.center;
    return e20 ? kt2 * this.view.pixelSizeAt(e20) : 0;
  }
  _createMoveXYGraphicDragPipeline() {
    return this._graphicMoveManipulation.createDragPipeline((t24, e20, i19) => this._applyGraphicMoveSteps(e20, i19, M15.XY));
  }
  _createMoveZDragPipeline() {
    const t24 = this.view, e20 = this._editGeometryOperations.data.spatialReference;
    return x7(this._moveZManipulator, (i19, a27, s19) => {
      const o17 = t4(i19.renderLocation), r24 = a27.next(G9(t24, o17, e20)).next(b6());
      this._applyGraphicMoveSteps(r24, s19, M15.Z);
    });
  }
  _applyGraphicMoveSteps(t24, e20, i19) {
    const a27 = t24.next((t25) => ("start" === t25.action && (this.inputState = { type: "move" }, this._updateOperationStartProperties(), this.emit("graphic-translate-start", { graphic: this.graphic, dxScreen: t25.screenDeltaX, dyScreen: t25.screenDeltaY })), t25)).next(D2()).next(this._moveDragUpdateGeometry()).next((t25) => {
      const e21 = { graphic: this.graphic, dxScreen: t25.screenDeltaX, dyScreen: t25.screenDeltaY };
      switch (t25.action) {
        case "start":
        case "update":
          (t25.mapEnd.x - t25.mapStart.x || t25.mapEnd.y - t25.mapStart.y || (t25.mapEnd.z ?? 0) - (t25.mapStart.z ?? 0)) && this.emit("graphic-translate", e21);
          break;
        case "end":
          this.inputState = null, this.emit("graphic-translate-stop", e21);
      }
      return t25;
    }).next((t25) => this._updateMoveTooltip(t25, i19));
    return e20.next(() => {
      r(this.inputState) && this.emit("graphic-translate-stop", { graphic: this.graphic, dxScreen: 0, dyScreen: 0 }), this._cancel();
    }), a27;
  }
  _updateOperationStartProperties() {
    Z4(this._displayBounds, this._displayBoundsStart), Z4(this._mapBounds, this._mapBoundsStart), t(this.graphic.geometry) ? this._sizeStart = null : this._sizeStart = M6(this._mapBoundsStart, this._zmax, this.graphic.geometry.spatialReference, this._graphicState.isDraped);
  }
  _moveDragUpdateGeometry() {
    return (t24) => {
      if (t(this.inputState) || "move" !== this.inputState.type) return t24;
      const e20 = [];
      for (const s19 of this._editGeometryOperations.data.components) e20.push(...s19.vertices);
      const i19 = "start" === t24.action ? E9.NEW_STEP : E9.ACCUMULATE_STEPS, a27 = this._editGeometryOperations.moveVertices(e20, t24.mapDeltaX, t24.mapDeltaY, t24.mapDeltaZ, i19);
      return S5(a27, this._mapBounds), this.graphic.geometry = this._editGeometryOperations.data.geometry, t24;
    };
  }
  _updateMoveTooltip(t24, e20) {
    if (e20 === M15.XY || e20 === M15.XY_AXIS) {
      const e21 = a16(t24.mapStart, t24.mapEnd, this._graphicState.isDraped ? "on-the-ground" : "absolute-height");
      r(e21) && r(this._moveXYTooltipInfo) && (this._moveXYTooltipInfo.distance = e21);
    }
    return t24;
  }
  _onResizeGrab({ action: t24, screenPoint: e20 }) {
    if ("start" !== t24 || !e20) return;
    const i19 = this._calculatePickRay(e20);
    x5(this._displayBounds.plane, i19, c2.get()) && (this._updateOperationStartProperties(), this._displayBoundsMarginStart = this._displayBoundsMargin, this.inputState = { type: "resize" });
  }
  _createResizeDragPipeline(t24, e20) {
    return x7(t24, (t25, i19, a27) => {
      t(this.inputState) || (i19.next((t26) => ("start" === t26.action && this.emit("graphic-scale-start", { graphic: this.graphic, xScale: 1, yScale: 1 }), t26)).next(C9(this.view, this._displayBoundsStart.plane)).next((t26) => ({ ...t26, handle: e20 })).next(this._resizeDragRenderPlaneToFactors()).next(...this._preserveAspectRatio.createDragEventPipelineStep()).next(this._resizeDragUpdateGeometry()).next((t26) => {
        const e21 = { graphic: this.graphic, xScale: t26.factor1, yScale: t26.factor2 };
        switch (t26.action) {
          case "start":
          case "update":
            this.emit("graphic-scale", e21);
            break;
          case "end":
            this.inputState = null, this.emit("graphic-scale-stop", e21);
        }
        return t26;
      }), a27.next(() => {
        r(this.inputState) && this.emit("graphic-scale-stop", { graphic: this.graphic, xScale: 1, yScale: 1 }), this._cancel();
      }));
    });
  }
  _resizeDragRenderPlaneToFactors() {
    return (t24) => {
      const e20 = this._displayBoundsStart, i19 = t24.handle.direction, a27 = this._displayBoundsMargin, s19 = this._displayBoundsMarginStart, o17 = r4(c2.get(), e20.origin);
      q(o17, o17, e20.basis1, -i19[0]), q(o17, o17, e20.basis2, -i19[1]);
      const r24 = e3(c2.get(), t24.renderEnd, o17), n21 = e3(c2.get(), t24.renderStart, o17), p25 = rt(t24.handle), l22 = ot2(e20), h25 = ot2(this._displayBounds) / l22, c21 = (t25, e21) => {
        if (0 === t25) return 1;
        let i20 = s2(e21), o18 = 0.5 * t25 * P(e21, r24) / i20;
        const l23 = o18 < 0 ? -1 : 1;
        if (p25) {
          o18 += (i20 - 0.5 * t25 * P(e21, n21) / i20) * l23 * h25;
        }
        const c22 = i20 < 1.5 * s19 ? 1 : Yt;
        return i20 = Math.max(i20 - s19, Yt), l23 > 0 && (o18 -= a27), l23 * Math.max(l23 * (o18 / i20), c22);
      };
      return { ...t24, factor1: c21(i19[0], e20.basis1), factor2: c21(i19[1], e20.basis2) };
    };
  }
  _resizeDragUpdateGeometry() {
    return (t24) => {
      const e20 = r4(n4(), this._mapBoundsStart.origin);
      q(e20, e20, this._mapBoundsStart.basis1, -t24.handle.direction[0]), q(e20, e20, this._mapBoundsStart.basis2, -t24.handle.direction[1]);
      const i19 = r8(n9(), this._mapBoundsStart.basis1[0], this._mapBoundsStart.basis1[1]);
      v4(i19, i19);
      const a27 = [];
      for (const r24 of this._editGeometryOperations.data.components) a27.push(...r24.vertices);
      const s19 = "start" === t24.action ? E9.NEW_STEP : E9.ACCUMULATE_STEPS, o17 = this._editGeometryOperations.scaleVertices(a27, e20, i19, t24.factor1, t24.factor2, s19, e11.REPLACE);
      return Z4(this._mapBoundsStart, this._mapBounds), S5(o17, this._mapBounds), this.graphic.geometry = this._editGeometryOperations.data.geometry, t24;
    };
  }
  _onRotateGrab({ action: t24, screenPoint: e20 }) {
    if ("start" !== t24 || !e20) return;
    const i19 = qe2(this._displayBounds, this.view.renderCoordsHelper, It2.HEADING, p6()), a27 = this._calculatePickRay(e20);
    x5(i19, a27, c2.get()) && (this._updateOperationStartProperties(), this.inputState = { type: "rotate", rotatePlane: i19 });
  }
  _createRotateDragPipeline(t24) {
    return x7(t24, (t25, e20, i19) => {
      const a27 = this.inputState;
      t(a27) || (e20.next((t26) => ("start" === t26.action && this.emit("graphic-rotate-start", { graphic: this.graphic, angle: 0 }), t26)).next(C9(this.view, a27.rotatePlane)).next(this._rotateDragRenderPlaneToRotate(a27)).next(this._rotateDragUpdateGeometry()).next((t26) => {
        const e21 = { graphic: this.graphic, angle: b(t26.rotateAngle) };
        switch (t26.action) {
          case "start":
          case "update":
            this.emit("graphic-rotate", e21);
            break;
          case "end":
            this.inputState = null, this.emit("graphic-rotate-stop", e21);
        }
        return t26;
      }), i19.next(() => {
        r(this.inputState) && this.emit("graphic-rotate-stop", { graphic: this.graphic, angle: 0 }), this._cancel();
      }));
    });
  }
  _rotateDragRenderPlaneToRotate(t24) {
    return (e20) => {
      const i19 = Y(t24.rotatePlane), a27 = C7(e20.renderStart, e20.renderEnd, this._displayBounds.origin, i19);
      return { ...e20, rotateAxis: i19, rotateAngle: a27 };
    };
  }
  _rotateDragUpdateGeometry() {
    return (t24) => {
      const e20 = r4(n4(), this._mapBoundsStart.origin), i19 = [];
      for (const o17 of this._editGeometryOperations.data.components) i19.push(...o17.vertices);
      const a27 = "start" === t24.action ? E9.NEW_STEP : E9.ACCUMULATE_STEPS, s19 = this._editGeometryOperations.rotateVertices(i19, e20, t24.rotateAngle, a27, e11.REPLACE);
      return Z4(this._mapBoundsStart, this._mapBounds), S5(s19, this._mapBounds), this.graphic.geometry = this._editGeometryOperations.data.geometry, t24;
    };
  }
  _calculatePickRay(t24) {
    const e20 = d4(), i19 = d2(t24);
    return s13(this.view.state.camera, i19, e20), z2(e20.direction, e20.direction), e20;
  }
  _updateManipulators() {
    if (!this.visible) return;
    const t24 = Be(this._displayBounds, f7.get());
    Ke(this._rotateManipulator, t24, this._displayBounds, this.view.renderCoordsHelper), this._updateZMoveHandle(this._moveZManipulator, t24), this._resizeManipulators.forEach((e20, i19) => {
      Je(e20, this._resizeHandles[i19], t24, this._displayBounds);
    });
  }
  _updateZMoveHandle(t24, e20) {
    const i19 = this._displayBounds, a27 = { basis: i19.basis1, direction: -1, position: e3(c2.get(), i19.origin, i19.basis1), edge: 2 }, s19 = f7.get();
    x3(s19, e20, a27.edge * Math.PI / 2), s19[12] = 0, s19[13] = 0, s19[14] = 0, t24.modelTransform = s19, t24.renderLocation = a27.position;
  }
  _cancel() {
    const t24 = this._editGeometryOperations.lastOperation;
    t(t24) || (this._editGeometryOperations.undo(), this.graphic.geometry = this._editGeometryOperations.data.geometry, Y2(t24, this._mapBounds), this._updateDisplayBounds(), this.inputState = null);
  }
  get canUndo() {
    return this._editGeometryOperations.canUndo;
  }
  undo() {
    if (r(this.inputState)) this.view.activeTool = null;
    else if (this.canUndo) {
      const t24 = this._editGeometryOperations.undo();
      this.graphic.geometry = this._editGeometryOperations.data.geometry, Y2(e2(t24), this._mapBounds), this._updateDisplayBounds();
    }
  }
  get canRedo() {
    return this._editGeometryOperations.canRedo;
  }
  redo() {
    if (this.canRedo) {
      const t24 = this._editGeometryOperations.redo();
      this.graphic.geometry = this._editGeometryOperations.data.geometry, S5(e2(t24), this._mapBounds), this._updateDisplayBounds();
    }
  }
  get test() {
    return { resizeManipulators: this._resizeManipulators, rotateManipulator: this._rotateManipulator, moveZManipulator: this._moveZManipulator, tooltip: this._tooltip };
  }
};
e([y2({ constructOnly: true, nonNullable: true })], Zt.prototype, "view", void 0), e([y2({ constructOnly: true, nonNullable: true })], Zt.prototype, "graphic", void 0), e([y2({ constructOnly: true, nonNullable: true })], Zt.prototype, "enableZ", void 0), e([y2()], Zt.prototype, "enableRotation", void 0), e([y2()], Zt.prototype, "enableScaling", void 0), e([y2({ constructOnly: true, type: p10 })], Zt.prototype, "tooltipOptions", void 0), e([y2()], Zt.prototype, "preserveAspectRatio", null), e([y2()], Zt.prototype, "grabbing", void 0), e([y2()], Zt.prototype, "inputState", void 0), e([y2({ readOnly: true })], Zt.prototype, "type", void 0), e([y2()], Zt.prototype, "_moveUnit", null), Zt = e([a3("esri.views.3d.interactive.editingTools.graphicTransform3D.ExtentTransformTool")], Zt);
var Ct2 = "draped-elevation-changes";
var kt2 = 10;
var Xt = 80;
var Yt = 1e-6;
export {
  T4 as DrawGraphicTool3D,
  Zt as ExtentTransformTool,
  B5 as GraphicMoveTool,
  v12 as GraphicReshapeTool,
  H4 as GraphicTransformTool
};
//# sourceMappingURL=editingTools-APT3JWTM.js.map
