{"version": 3, "sources": ["../../@arcgis/core/views/interactive/sketch/SketchTooltipElevationOptions.js", "../../@arcgis/core/views/interactive/sketch/SketchTooltipVisibleElements.js", "../../@arcgis/core/views/interactive/sketch/SketchTooltipOptions.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import r from\"../../../core/Accessor.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";let t=class extends r{constructor(o){super(o),this.mode=\"absolute-height\"}toJSON(){return{mode:this.mode}}};o([s({type:String,nonNullable:!0})],t.prototype,\"mode\",void 0),t=o([e(\"esri.widgets.Sketch.SketchTooltipOptions.ElevationOptions\")],t);export{t as SketchTooltipElevationOptions};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Accessor.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";let i=class extends t{constructor(){super(...arguments),this.distance=!0,this.elevation=!0,this.size=!0,this.scale=!0,this.rotation=!0,this.orientation=!0,this.totalLength=!0,this.area=!0,this.radius=!0,this.helpMessage=!1}toJSON(){return{distance:this.distance,size:this.size,scale:this.scale,rotation:this.rotation,orientation:this.orientation,totalLength:this.totalLength,area:this.area,radius:this.radius}}};o([e({type:Boolean,nonNullable:!0})],i.prototype,\"distance\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"elevation\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"size\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"scale\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"rotation\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"orientation\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"totalLength\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"area\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"radius\",void 0),o([e({type:Boolean,nonNullable:!0})],i.prototype,\"helpMessage\",void 0),i=o([s(\"esri.widgets.Sketch.SketchTooltipOptions.VisibleElements\")],i);export{i as SketchTooltipVisibleElements};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import o from\"../../../core/Accessor.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import{SketchTooltipElevationOptions as r}from\"./SketchTooltipElevationOptions.js\";import{SketchTooltipVisibleElements as i}from\"./SketchTooltipVisibleElements.js\";let l=class extends o{constructor(e){super(e),this.enabled=!1,this.elevation=new r,this.visibleElements=new i,this.visualVariables=null}toJSON(){return{enabled:this.enabled,elevation:this.elevation,visibleElements:this.visibleElements}}};e([t({type:Boolean,nonNullable:!0})],l.prototype,\"enabled\",void 0),e([t({type:r,nonNullable:!0})],l.prototype,\"elevation\",void 0),e([t({type:i,nonNullable:!0})],l.prototype,\"visibleElements\",void 0),e([t()],l.prototype,\"visualVariables\",void 0),l=e([s(\"esri.widgets.Sketch.SketchTooltipOptions\")],l);const p=l;export{p as default};\n"], "mappings": ";;;;;;;;;;AAIkV,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK;AAAA,EAAiB;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,MAAK,KAAK,KAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAE,CAAC;;;ACAjP,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAAS,MAAG,KAAK,YAAU,MAAG,KAAK,OAAK,MAAG,KAAK,QAAM,MAAG,KAAK,WAAS,MAAG,KAAK,cAAY,MAAG,KAAK,cAAY,MAAG,KAAK,OAAK,MAAG,KAAK,SAAO,MAAG,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,UAAS,KAAK,UAAS,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,UAAS,KAAK,UAAS,aAAY,KAAK,aAAY,aAAY,KAAK,aAAY,MAAK,KAAK,MAAK,QAAO,KAAK,OAAM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,0DAA0D,CAAC,GAAE,CAAC;;;ACAl+B,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,OAAG,KAAK,YAAU,IAAI,KAAE,KAAK,kBAAgB,IAAI,KAAE,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,SAAQ,KAAK,SAAQ,WAAU,KAAK,WAAU,iBAAgB,KAAK,gBAAe;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e"]}