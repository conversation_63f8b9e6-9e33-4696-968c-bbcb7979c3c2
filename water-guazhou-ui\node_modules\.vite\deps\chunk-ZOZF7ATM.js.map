{"version": 3, "sources": ["../../@arcgis/core/symbols/support/previewSymbol3D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getAssetUrl as e}from\"../../assets.js\";import{toHSV as t,toRGB as s}from\"../../core/colorUtils.js\";import\"../../core/has.js\";import r from\"../../core/Error.js\";import a from\"../../core/Logger.js\";import{isNone as o,unwrapOrThrow as n,isSome as l}from\"../../core/maybe.js\";import{eachAlways as i}from\"../../core/promiseUtils.js\";import{pt2px as c}from\"../../core/screenUtils.js\";import{defaultThematicColor as p,getPatternUrlWithColor as u,dekebabifyLineStyle as h}from\"./gfxUtils.js\";import{defaultPrimitive as m}from\"./IconSymbol3DLayerResource.js\";import{defaultPrimitive as f}from\"./ObjectSymbol3DLayerResource.js\";import{SymbolSizeDefaults as y,adjustColorComponentBrightness as d,getPathSymbolShapes as b,shapes as v,getExtrudeSymbolShapes as g,getTetrahedronShapes as k,getDiamondShapes as w,getCylinderShapes as x,getCubeShapes as M,getInvertedConeShapes as j,getConeShapes as L,getWaterSymbolShapes as z}from\"./previewUtils.js\";import{tintImageWithColor as S,renderSymbol as P}from\"./renderUtils.js\";import{isVolumetricSymbol as U,getIconHref as D}from\"./utils.js\";import{resolveWebStyleSymbol as O}from\"./webStyleSymbolUtils.js\";const C=y.size,I=y.maxSize,R=y.maxOutlineSize,E=y.lineWidth,Z=y.tallSymbolWidth;function q(e){const t=e.outline,s=l(e.material)?e.material.color:null,r=l(s)?s.toHex():null;if(o(t)||\"pattern\"in t&&l(t.pattern)&&\"style\"===t.pattern.type&&\"none\"===t.pattern.style)return\"fill\"===e.type&&\"#ffffff\"===r?{color:\"#bdc3c7\",width:.75}:null;const a=c(t.size)||0;return{color:\"rgba(\"+(l(t.color)?t.color.toRgba():\"255,255,255,1\")+\")\",width:Math.min(a,R),style:\"pattern\"in t&&l(t.pattern)&&\"style\"===t.pattern.type?h(t.pattern.style):null,join:\"butt\",cap:\"patternCap\"in t?t.patternCap:\"butt\"}}function A(t,s){const r=s&&s.resource,a=r&&r.href;if(t.thumbnail&&t.thumbnail.url)return Promise.resolve(t.thumbnail.url);if(a&&\"object\"!==s.type)return Promise.resolve(D(s));const o=e(\"esri/images/Legend/legend3dsymboldefault.png\");return t.styleOrigin&&(t.styleOrigin.styleName||t.styleOrigin.styleUrl)?O(t.styleOrigin,{portal:t.styleOrigin.portal},\"webRef\").catch((e=>e)).then((e=>e?.thumbnail?.url||o)):Promise.resolve(o)}function H(e,r=1){const a=e.a,o=t(e),n=o.h,l=o.s/r,i=100-(100-o.v)/r,{r:c,g:p,b:u}=s({h:n,s:l,v:i});return[c,p,u,a]}function N(e){return\"water\"===e.type?o(e.color)?null:e.color:o(e.material)||o(e.material.color)?null:e.material.color}function T(e,t=0){const s=N(e);if(!s){if(\"fill\"===e.type)return null;const s=p.r,r=d(s,t);return[r,r,r,100]}const r=s.toRgba();for(let a=0;a<3;a++)r[a]=d(r[a],t);return r}async function W(t,s){const r=t.style;if(\"none\"===r)return null;return{type:\"pattern\",x:0,y:0,src:await u(e(`esri/symbols/patterns/${r}.png`),s.toCss(!0)),width:5,height:5}}function $(e){return e.outline?q(e):{color:\"rgba(0, 0, 0, 1)\",width:1.5}}function B(e,t){const s=N(e);if(!s)return null;let r=\"rgba(\";return r+=d(s.r,t)+\",\",r+=d(s.g,t)+\",\",r+=d(s.b,t)+\",\",r+s.a+\");\"}function F(e,t){const s=B(e,t);if(!s)return{};if(\"pattern\"in e&&l(e.pattern)&&\"style\"===e.pattern.type&&\"none\"===e.pattern.style)return null;return{color:s,width:Math.min(e.size?c(e.size):.75,R),style:\"pattern\"in e&&l(e.pattern)&&\"style\"===e.pattern.type?h(e.pattern.style):null,cap:\"cap\"in e?e.cap:null,join:\"join\"in e?\"miter\"===e.join?c(2):e.join:null}}function G(e,t,s){const r=null!=s?.75*s:0;return{type:\"linear\",x1:r?.25*r:0,y1:r?.5*r:0,x2:r||4,y2:r?.5*r:4,colors:[{color:e,offset:0},{color:t,offset:1}]}}function J(e){const t=e.depth,s=e.height,r=e.width;return 0!==r&&0!==t&&0!==s&&r===t&&null!=r&&null!=s&&r<s}function K(e,t,s){const r=[];if(!e)return r;switch(e.type){case\"icon\":{const s=0,a=0,o=t,n=t;switch(e.resource&&e.resource.primitive||m){case\"circle\":r.push({shape:{type:\"circle\",cx:0,cy:0,r:.5*t},fill:T(e,0),stroke:q(e)});break;case\"square\":r.push({shape:{type:\"path\",path:[{command:\"M\",values:[s,n]},{command:\"L\",values:[s,a]},{command:\"L\",values:[o,a]},{command:\"L\",values:[o,n]},{command:\"Z\",values:[]}]},fill:T(e,0),stroke:q(e)});break;case\"triangle\":r.push({shape:{type:\"path\",path:[{command:\"M\",values:[s,n]},{command:\"L\",values:[.5*o,a]},{command:\"L\",values:[o,n]},{command:\"Z\",values:[]}]},fill:T(e,0),stroke:q(e)});break;case\"cross\":r.push({shape:{type:\"path\",path:[{command:\"M\",values:[.5*o,a]},{command:\"L\",values:[.5*o,n]},{command:\"M\",values:[s,.5*n]},{command:\"L\",values:[o,.5*n]}]},stroke:$(e)});break;case\"x\":r.push({shape:{type:\"path\",path:[{command:\"M\",values:[s,a]},{command:\"L\",values:[o,n]},{command:\"M\",values:[o,a]},{command:\"L\",values:[s,n]}]},stroke:$(e)});break;case\"kite\":r.push({shape:{type:\"path\",path:[{command:\"M\",values:[s,.5*n]},{command:\"L\",values:[.5*o,a]},{command:\"L\",values:[o,.5*n]},{command:\"L\",values:[.5*o,n]},{command:\"Z\",values:[]}]},fill:T(e,0),stroke:q(e)})}break}case\"object\":switch(e.resource&&e.resource.primitive||f){case\"cone\":{const a=G(T(e,0),T(e,-.6),s?Z:t),o=L(t,s);r.push({shape:o[0],fill:a}),r.push({shape:o[1],fill:a});break}case\"inverted-cone\":{const s=T(e,0),a=G(s,T(e,-.6),t),o=j(t);r.push({shape:o[0],fill:a}),r.push({shape:o[1],fill:s});break}case\"cube\":{const a=M(t,s);r.push({shape:a[0],fill:T(e,0)}),r.push({shape:a[1],fill:T(e,-.3)}),r.push({shape:a[2],fill:T(e,-.5)});break}case\"cylinder\":{const a=G(T(e,0),T(e,-.6),s?Z:t),o=x(t,s);r.push({shape:o[0],fill:a}),r.push({shape:o[1],fill:a}),r.push({shape:o[2],fill:T(e,0)});break}case\"diamond\":{const s=w(t);r.push({shape:s[0],fill:T(e,-.3)}),r.push({shape:s[1],fill:T(e,0)}),r.push({shape:s[2],fill:T(e,-.3)}),r.push({shape:s[3],fill:T(e,-.7)});break}case\"sphere\":{const s=G(T(e,0),T(e,-.6));s.x1=0,s.y1=0,s.x2=.25*t,s.y2=.25*t,r.push({shape:{type:\"circle\",cx:0,cy:0,r:.5*t},fill:s});break}case\"tetrahedron\":{const s=k(t);r.push({shape:s[0],fill:T(e,-.3)}),r.push({shape:s[1],fill:T(e,0)}),r.push({shape:s[2],fill:T(e,-.6)});break}}break}return r}function Q(e){const t=\"number\"==typeof e?.size?e?.size:null;return t?c(t):null}function V(e){return\"icon\"===e.type?\"multiply\":\"tint\"}function X(e,t){const s=Q(t),r=t?.maxSize?c(t.maxSize):null,o=t?.disableUpsampling??!1,n=e.symbolLayers,l=[];let p=0,u=0;const h=n.getItemAt(n.length-1);let m;return h&&\"icon\"===h.type&&(m=h.size&&c(h.size)),n.forEach((a=>{if(\"icon\"!==a.type&&\"object\"!==a.type)return;const n=\"icon\"===a.type?a.size&&c(a.size):0,i=s||n?Math.ceil(Math.min(s||n,r||I)):C;if(a&&a.resource&&a.resource.href){const t=A(e,a).then((e=>{const t=a.get(\"material.color\"),s=V(a);return S(e,i,t,s,o)})).then((e=>{const t=e.width,s=e.height;return p=Math.max(p,t),u=Math.max(u,s),[{shape:{type:\"image\",x:0,y:0,width:t,height:s,src:e.url},fill:null,stroke:null}]}));l.push(t)}else{let e=i;\"icon\"===a.type&&m&&s&&(e=i*(n/m));const r=\"tall\"===t?.symbolConfig||t?.symbolConfig?.isTall||\"object\"===a.type&&J(a);p=Math.max(p,r?Z:e),u=Math.max(u,e),l.push(Promise.resolve(K(a,e,r)))}})),i(l).then((e=>{const s=[];return e.forEach((e=>{e.value?s.push(e.value):e.error&&a.getLogger(\"esri.symbols.support.previewSymbol3D\").warn(\"error while building swatchInfo!\",e.error)})),P(s,[p,u],{node:t&&t.node,scale:!1,opacity:t&&t.opacity})}))}function Y(e,t){const s=e.symbolLayers,r=[],a=U(e),n=Q(t),l=(t&&t.maxSize?c(t.maxSize):null)||R;let i,p=0,u=0;return s.forEach(((e,t)=>{if(!e)return;if(\"line\"!==e.type&&\"path\"!==e.type)return;const s=[];switch(e.type){case\"line\":{const r=F(e,0);if(o(r))break;const a=r&&r.width||0;0===t&&(i=a);const c=Math.min(n||a,l),h=0===t?c:n?c*(a/i):c,m=h>E/2?2*h:E;u=Math.max(u,h),p=Math.max(p,m),r.width=h,s.push({shape:{type:\"path\",path:[{command:\"M\",values:[0,.5*u]},{command:\"L\",values:[p,.5*u]}]},stroke:r});break}case\"path\":{const t=Math.min(n||C,l),r=T(e,0),a=T(e,-.2),o=B(e,-.4),i=o?{color:o,width:1}:{};if(\"quad\"===e.profile){const t=e.width,o=e.height,n=b(t&&o?t/o:1,0===o,0===t),l={...i,join:\"bevel\"};s.push({shape:n[0],fill:a,stroke:l}),s.push({shape:n[1],fill:a,stroke:l}),s.push({shape:n[2],fill:r,stroke:l})}else s.push({shape:v.pathSymbol3DLayer[0],fill:a,stroke:i}),s.push({shape:v.pathSymbol3DLayer[1],fill:r,stroke:i});u=Math.max(u,t),p=u}}r.push(s)})),Promise.resolve(P(r,[p,u],{node:t&&t.node,scale:a,opacity:t&&t.opacity}))}async function _(e,t){const s=\"mesh-3d\"===e.type,r=e.symbolLayers,a=Q(t),i=t&&t.maxSize?c(t.maxSize):null,p=a||C,u=[];let h=0,m=0,f=!1;for(let c=0;c<r.length;c++){const e=r.getItemAt(c),t=[];if(s&&\"fill\"!==e.type)continue;const a=v.fill[0];switch(e.type){case\"fill\":{const r=q(e),o=Math.min(p,i||I);h=Math.max(h,o),m=Math.max(m,o),f=!0;let n=T(e,0);const c=\"pattern\"in e?e.pattern:null,u=N(e);!s&&l(c)&&\"style\"===c.type&&\"solid\"!==c.style&&u&&(n=await W(c,u)),t.push({shape:a,fill:n,stroke:r});break}case\"line\":{const s=F(e,0);if(o(s))break;const r={stroke:s,shape:a};h=Math.max(h,C),m=Math.max(m,C),t.push(r);break}case\"extrude\":{const s={join:\"round\",width:1,...F(e,-.4)},r=T(e,0),a=T(e,-.2),o=Math.min(p,i||I),n=g(o);s.width=1,t.push({shape:n[0],fill:a,stroke:s}),t.push({shape:n[1],fill:a,stroke:s}),t.push({shape:n[2],fill:r,stroke:s});const l=C,c=.7*C+.5*o;h=Math.max(h,l),m=Math.max(m,c);break}case\"water\":{const s=n(N(e)),r=H(s),a=H(s,2),o=H(s,3),l=z();f=!0,t.push({shape:l[0],fill:r}),t.push({shape:l[1],fill:a}),t.push({shape:l[2],fill:o});const c=Math.min(p,i||I);h=Math.max(h,c),m=Math.max(m,c);break}}u.push(t)}return P(u,[h,m],{node:t&&t.node,scale:f,opacity:t&&t.opacity})}function ee(e,t){if(0===e.symbolLayers.length)return Promise.reject(new r(\"symbolPreview: renderPreviewHTML3D\",\"No symbolLayers in the symbol.\"));switch(e.type){case\"point-3d\":return X(e,t);case\"line-3d\":return Y(e,t);case\"polygon-3d\":case\"mesh-3d\":return _(e,t)}return Promise.reject(new r(\"symbolPreview: swatchInfo3D\",\"symbol not supported.\"))}export{W as getPatternDescriptor,Q as getSizeFromOptions,T as getSymbolLayerFill,ee as previewSymbol3D};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIynC,IAAM,IAAEA,GAAE;AAAV,IAAe,IAAEA,GAAE;AAAnB,IAA2B,IAAEA,GAAE;AAA/B,IAA8CC,KAAED,GAAE;AAAlD,IAA4D,IAAEA,GAAE;AAAgB,SAAS,EAAEE,IAAE;AAAC,QAAMF,KAAEE,GAAE,SAAQC,KAAE,EAAED,GAAE,QAAQ,IAAEA,GAAE,SAAS,QAAM,MAAKE,KAAE,EAAED,EAAC,IAAEA,GAAE,MAAM,IAAE;AAAK,MAAG,EAAEH,EAAC,KAAG,aAAYA,MAAG,EAAEA,GAAE,OAAO,KAAG,YAAUA,GAAE,QAAQ,QAAM,WAASA,GAAE,QAAQ,MAAM,QAAM,WAASE,GAAE,QAAM,cAAYE,KAAE,EAAC,OAAM,WAAU,OAAM,KAAG,IAAE;AAAK,QAAMC,KAAE,EAAEL,GAAE,IAAI,KAAG;AAAE,SAAM,EAAC,OAAM,WAAS,EAAEA,GAAE,KAAK,IAAEA,GAAE,MAAM,OAAO,IAAE,mBAAiB,KAAI,OAAM,KAAK,IAAIK,IAAE,CAAC,GAAE,OAAM,aAAYL,MAAG,EAAEA,GAAE,OAAO,KAAG,YAAUA,GAAE,QAAQ,OAAKM,GAAEN,GAAE,QAAQ,KAAK,IAAE,MAAK,MAAK,QAAO,KAAI,gBAAeA,KAAEA,GAAE,aAAW,OAAM;AAAC;AAAC,SAAS,EAAEA,IAAEG,IAAE;AAAC,QAAMC,KAAED,MAAGA,GAAE,UAASE,KAAED,MAAGA,GAAE;AAAK,MAAGJ,GAAE,aAAWA,GAAE,UAAU,IAAI,QAAO,QAAQ,QAAQA,GAAE,UAAU,GAAG;AAAE,MAAGK,MAAG,aAAWF,GAAE,KAAK,QAAO,QAAQ,QAAQG,GAAEH,EAAC,CAAC;AAAE,QAAMI,KAAE,EAAE,8CAA8C;AAAE,SAAOP,GAAE,gBAAcA,GAAE,YAAY,aAAWA,GAAE,YAAY,YAAU,EAAEA,GAAE,aAAY,EAAC,QAAOA,GAAE,YAAY,OAAM,GAAE,QAAQ,EAAE,MAAO,CAAAE,OAAGA,EAAE,EAAE,KAAM,CAAAA,OAAC;AAJ5jE;AAI8jE,kBAAAA,MAAA,gBAAAA,GAAG,cAAH,mBAAc,QAAKK;AAAA,GAAE,IAAE,QAAQ,QAAQA,EAAC;AAAC;AAAC,SAAS,EAAEL,IAAEE,KAAE,GAAE;AAAC,QAAMC,KAAEH,GAAE,GAAEK,KAAE,EAAEL,EAAC,GAAEM,KAAED,GAAE,GAAEE,KAAEF,GAAE,IAAEH,IAAE,IAAE,OAAK,MAAIG,GAAE,KAAGH,IAAE,EAAC,GAAEM,IAAE,GAAEC,IAAE,GAAEC,GAAC,IAAE,EAAE,EAAC,GAAEJ,IAAE,GAAEC,IAAE,GAAE,EAAC,CAAC;AAAE,SAAM,CAACC,IAAEC,IAAEC,IAAEP,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,SAAM,YAAUA,GAAE,OAAK,EAAEA,GAAE,KAAK,IAAE,OAAKA,GAAE,QAAM,EAAEA,GAAE,QAAQ,KAAG,EAAEA,GAAE,SAAS,KAAK,IAAE,OAAKA,GAAE,SAAS;AAAK;AAAC,SAAS,EAAEA,IAAEF,KAAE,GAAE;AAAC,QAAMG,KAAE,EAAED,EAAC;AAAE,MAAG,CAACC,IAAE;AAAC,QAAG,WAASD,GAAE,KAAK,QAAO;AAAK,UAAMC,KAAE,EAAE,GAAEC,KAAES,GAAEV,IAAEH,EAAC;AAAE,WAAM,CAACI,IAAEA,IAAEA,IAAE,GAAG;AAAA,EAAC;AAAC,QAAMA,KAAED,GAAE,OAAO;AAAE,WAAQE,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAD,GAAEC,EAAC,IAAEQ,GAAET,GAAEC,EAAC,GAAEL,EAAC;AAAE,SAAOI;AAAC;AAAC,eAAe,EAAEJ,IAAEG,IAAE;AAAC,QAAMC,KAAEJ,GAAE;AAAM,MAAG,WAASI,GAAE,QAAO;AAAK,SAAM,EAAC,MAAK,WAAU,GAAE,GAAE,GAAE,GAAE,KAAI,MAAMS,GAAE,EAAE,yBAAyBT,EAAC,MAAM,GAAED,GAAE,MAAM,IAAE,CAAC,GAAE,OAAM,GAAE,QAAO,EAAC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAOA,GAAE,UAAQ,EAAEA,EAAC,IAAE,EAAC,OAAM,oBAAmB,OAAM,IAAG;AAAC;AAAC,SAAS,EAAEA,IAAEF,IAAE;AAAC,QAAMG,KAAE,EAAED,EAAC;AAAE,MAAG,CAACC,GAAE,QAAO;AAAK,MAAIC,KAAE;AAAQ,SAAOA,MAAGS,GAAEV,GAAE,GAAEH,EAAC,IAAE,KAAII,MAAGS,GAAEV,GAAE,GAAEH,EAAC,IAAE,KAAII,MAAGS,GAAEV,GAAE,GAAEH,EAAC,IAAE,KAAII,KAAED,GAAE,IAAE;AAAI;AAAC,SAAS,EAAED,IAAEF,IAAE;AAAC,QAAMG,KAAE,EAAED,IAAEF,EAAC;AAAE,MAAG,CAACG,GAAE,QAAM,CAAC;AAAE,MAAG,aAAYD,MAAG,EAAEA,GAAE,OAAO,KAAG,YAAUA,GAAE,QAAQ,QAAM,WAASA,GAAE,QAAQ,MAAM,QAAO;AAAK,SAAM,EAAC,OAAMC,IAAE,OAAM,KAAK,IAAID,GAAE,OAAK,EAAEA,GAAE,IAAI,IAAE,MAAI,CAAC,GAAE,OAAM,aAAYA,MAAG,EAAEA,GAAE,OAAO,KAAG,YAAUA,GAAE,QAAQ,OAAKI,GAAEJ,GAAE,QAAQ,KAAK,IAAE,MAAK,KAAI,SAAQA,KAAEA,GAAE,MAAI,MAAK,MAAK,UAASA,KAAE,YAAUA,GAAE,OAAK,EAAE,CAAC,IAAEA,GAAE,OAAK,KAAI;AAAC;AAAC,SAAS,EAAEA,IAAEF,IAAEG,IAAE;AAAC,QAAMC,KAAE,QAAMD,KAAE,OAAIA,KAAE;AAAE,SAAM,EAAC,MAAK,UAAS,IAAGC,KAAE,OAAIA,KAAE,GAAE,IAAGA,KAAE,MAAGA,KAAE,GAAE,IAAGA,MAAG,GAAE,IAAGA,KAAE,MAAGA,KAAE,GAAE,QAAO,CAAC,EAAC,OAAMF,IAAE,QAAO,EAAC,GAAE,EAAC,OAAMF,IAAE,QAAO,EAAC,CAAC,EAAC;AAAC;AAAC,SAAS,EAAEE,IAAE;AAAC,QAAMF,KAAEE,GAAE,OAAMC,KAAED,GAAE,QAAOE,KAAEF,GAAE;AAAM,SAAO,MAAIE,MAAG,MAAIJ,MAAG,MAAIG,MAAGC,OAAIJ,MAAG,QAAMI,MAAG,QAAMD,MAAGC,KAAED;AAAC;AAAC,SAAS,EAAED,IAAEF,IAAEG,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,MAAG,CAACF,GAAE,QAAOE;AAAE,UAAOF,GAAE,MAAK;AAAA,IAAC,KAAI,QAAO;AAAC,YAAMC,KAAE,GAAEE,KAAE,GAAEE,KAAEP,IAAEQ,KAAER;AAAE,cAAOE,GAAE,YAAUA,GAAE,SAAS,aAAW,GAAE;AAAA,QAAC,KAAI;AAAS,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAS,IAAG,GAAE,IAAG,GAAE,GAAE,MAAGJ,GAAC,GAAE,MAAK,EAAEE,IAAE,CAAC,GAAE,QAAO,EAAEA,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAS,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAEK,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACL,IAAEE,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACE,IAAEF,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACE,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAE,MAAK,EAAEN,IAAE,CAAC,GAAE,QAAO,EAAEA,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAW,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAEK,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAEF,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACE,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAE,MAAK,EAAEN,IAAE,CAAC,GAAE,QAAO,EAAEA,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAQ,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGG,IAAEF,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGE,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACL,IAAE,MAAGK,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAE,MAAGC,EAAC,EAAC,CAAC,EAAC,GAAE,QAAO,EAAEN,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAI,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAEE,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACE,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAEF,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACF,IAAEK,EAAC,EAAC,CAAC,EAAC,GAAE,QAAO,EAAEN,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAI;AAAO,UAAAE,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAE,MAAGK,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAEF,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACE,IAAE,MAAGC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,MAAGD,IAAEC,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAAC,EAAC,CAAC,EAAC,GAAE,MAAK,EAAEN,IAAE,CAAC,GAAE,QAAO,EAAEA,EAAC,EAAC,CAAC;AAAA,MAAC;AAAC;AAAA,IAAK;AAAA,IAAC,KAAI;AAAS,cAAOA,GAAE,YAAUA,GAAE,SAAS,aAAW,GAAE;AAAA,QAAC,KAAI,QAAO;AAAC,gBAAMG,KAAE,EAAE,EAAEH,IAAE,CAAC,GAAE,EAAEA,IAAE,IAAG,GAAEC,KAAE,IAAEH,EAAC,GAAEO,KAAE,EAAEP,IAAEG,EAAC;AAAE,UAAAC,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC,GAAED,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,iBAAgB;AAAC,gBAAMF,KAAE,EAAED,IAAE,CAAC,GAAEG,KAAE,EAAEF,IAAE,EAAED,IAAE,IAAG,GAAEF,EAAC,GAAEO,KAAE,EAAEP,EAAC;AAAE,UAAAI,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC,GAAED,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKJ,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,QAAO;AAAC,gBAAME,KAAEM,GAAEX,IAAEG,EAAC;AAAE,UAAAC,GAAE,KAAK,EAAC,OAAMC,GAAE,CAAC,GAAE,MAAK,EAAEH,IAAE,CAAC,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMC,GAAE,CAAC,GAAE,MAAK,EAAEH,IAAE,IAAG,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMC,GAAE,CAAC,GAAE,MAAK,EAAEH,IAAE,IAAG,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,YAAW;AAAC,gBAAMG,KAAE,EAAE,EAAEH,IAAE,CAAC,GAAE,EAAEA,IAAE,IAAG,GAAEC,KAAE,IAAEH,EAAC,GAAEO,KAAE,EAAEP,IAAEG,EAAC;AAAE,UAAAC,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC,GAAED,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC,GAAED,GAAE,KAAK,EAAC,OAAMG,GAAE,CAAC,GAAE,MAAK,EAAEL,IAAE,CAAC,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,WAAU;AAAC,gBAAMC,KAAEA,GAAEH,EAAC;AAAE,UAAAI,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,IAAG,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,CAAC,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,IAAG,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,IAAG,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,UAAS;AAAC,gBAAMC,KAAE,EAAE,EAAED,IAAE,CAAC,GAAE,EAAEA,IAAE,IAAG,CAAC;AAAE,UAAAC,GAAE,KAAG,GAAEA,GAAE,KAAG,GAAEA,GAAE,KAAG,OAAIH,IAAEG,GAAE,KAAG,OAAIH,IAAEI,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAS,IAAG,GAAE,IAAG,GAAE,GAAE,MAAGJ,GAAC,GAAE,MAAKG,GAAC,CAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,eAAc;AAAC,gBAAMA,KAAES,GAAEZ,EAAC;AAAE,UAAAI,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,IAAG,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,CAAC,EAAC,CAAC,GAAEE,GAAE,KAAK,EAAC,OAAMD,GAAE,CAAC,GAAE,MAAK,EAAED,IAAE,IAAG,EAAC,CAAC;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC;AAAA,EAAK;AAAC,SAAOE;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,QAAMF,KAAE,YAAU,QAAOE,MAAA,gBAAAA,GAAG,QAAKA,MAAA,gBAAAA,GAAG,OAAK;AAAK,SAAOF,KAAE,EAAEA,EAAC,IAAE;AAAI;AAAC,SAAS,EAAEE,IAAE;AAAC,SAAM,WAASA,GAAE,OAAK,aAAW;AAAM;AAAC,SAAS,EAAEA,IAAEF,IAAE;AAAC,QAAMG,KAAE,EAAEH,EAAC,GAAEI,MAAEJ,MAAA,gBAAAA,GAAG,WAAQ,EAAEA,GAAE,OAAO,IAAE,MAAKO,MAAEP,MAAA,gBAAAA,GAAG,sBAAmB,OAAGQ,KAAEN,GAAE,cAAaO,KAAE,CAAC;AAAE,MAAIE,KAAE,GAAEC,KAAE;AAAE,QAAMC,KAAEL,GAAE,UAAUA,GAAE,SAAO,CAAC;AAAE,MAAIM;AAAE,SAAOD,MAAG,WAASA,GAAE,SAAOC,KAAED,GAAE,QAAM,EAAEA,GAAE,IAAI,IAAGL,GAAE,QAAS,CAAAH,OAAG;AAJxhM;AAIyhM,QAAG,WAASA,GAAE,QAAM,aAAWA,GAAE,KAAK;AAAO,UAAMG,KAAE,WAASH,GAAE,OAAKA,GAAE,QAAM,EAAEA,GAAE,IAAI,IAAE,GAAE,IAAEF,MAAGK,KAAE,KAAK,KAAK,KAAK,IAAIL,MAAGK,IAAEJ,MAAG,CAAC,CAAC,IAAE;AAAE,QAAGC,MAAGA,GAAE,YAAUA,GAAE,SAAS,MAAK;AAAC,YAAML,KAAE,EAAEE,IAAEG,EAAC,EAAE,KAAM,CAAAH,OAAG;AAAC,cAAMF,KAAEK,GAAE,IAAI,gBAAgB,GAAEF,KAAE,EAAEE,EAAC;AAAE,eAAO,EAAEH,IAAE,GAAEF,IAAEG,IAAEI,EAAC;AAAA,MAAC,CAAE,EAAE,KAAM,CAAAL,OAAG;AAAC,cAAMF,KAAEE,GAAE,OAAMC,KAAED,GAAE;AAAO,eAAOS,KAAE,KAAK,IAAIA,IAAEX,EAAC,GAAEY,KAAE,KAAK,IAAIA,IAAET,EAAC,GAAE,CAAC,EAAC,OAAM,EAAC,MAAK,SAAQ,GAAE,GAAE,GAAE,GAAE,OAAMH,IAAE,QAAOG,IAAE,KAAID,GAAE,IAAG,GAAE,MAAK,MAAK,QAAO,KAAI,CAAC;AAAA,MAAC,CAAE;AAAE,MAAAO,GAAE,KAAKT,EAAC;AAAA,IAAC,OAAK;AAAC,UAAIE,KAAE;AAAE,iBAASG,GAAE,QAAMS,MAAGX,OAAID,KAAE,KAAGM,KAAEM;AAAI,YAAMV,KAAE,YAASJ,MAAA,gBAAAA,GAAG,mBAAc,KAAAA,MAAA,gBAAAA,GAAG,iBAAH,mBAAiB,WAAQ,aAAWK,GAAE,QAAM,EAAEA,EAAC;AAAE,MAAAM,KAAE,KAAK,IAAIA,IAAEP,KAAE,IAAEF,EAAC,GAAEU,KAAE,KAAK,IAAIA,IAAEV,EAAC,GAAEO,GAAE,KAAK,QAAQ,QAAQ,EAAEJ,IAAEH,IAAEE,EAAC,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,EAAEK,EAAC,EAAE,KAAM,CAAAP,OAAG;AAAC,UAAMC,KAAE,CAAC;AAAE,WAAOD,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,QAAMC,GAAE,KAAKD,GAAE,KAAK,IAAEA,GAAE,SAAO,EAAE,UAAU,sCAAsC,EAAE,KAAK,oCAAmCA,GAAE,KAAK;AAAA,IAAC,CAAE,GAAEO,GAAEN,IAAE,CAACQ,IAAEC,EAAC,GAAE,EAAC,MAAKZ,MAAGA,GAAE,MAAK,OAAM,OAAG,SAAQA,MAAGA,GAAE,QAAO,CAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEE,IAAEF,IAAE;AAAC,QAAMG,KAAED,GAAE,cAAaE,KAAE,CAAC,GAAEC,KAAE,EAAEH,EAAC,GAAEM,KAAE,EAAER,EAAC,GAAES,MAAGT,MAAGA,GAAE,UAAQ,EAAEA,GAAE,OAAO,IAAE,SAAO;AAAE,MAAI,GAAEW,KAAE,GAAEC,KAAE;AAAE,SAAOT,GAAE,QAAS,CAACD,IAAEF,OAAI;AAAC,QAAG,CAACE,GAAE;AAAO,QAAG,WAASA,GAAE,QAAM,WAASA,GAAE,KAAK;AAAO,UAAMC,KAAE,CAAC;AAAE,YAAOD,GAAE,MAAK;AAAA,MAAC,KAAI,QAAO;AAAC,cAAME,KAAE,EAAEF,IAAE,CAAC;AAAE,YAAG,EAAEE,EAAC,EAAE;AAAM,cAAMC,KAAED,MAAGA,GAAE,SAAO;AAAE,cAAIJ,OAAI,IAAEK;AAAG,cAAMK,KAAE,KAAK,IAAIF,MAAGH,IAAEI,EAAC,GAAEI,KAAE,MAAIb,KAAEU,KAAEF,KAAEE,MAAGL,KAAE,KAAGK,IAAEI,KAAED,KAAEZ,KAAE,IAAE,IAAEY,KAAEZ;AAAE,QAAAW,KAAE,KAAK,IAAIA,IAAEC,EAAC,GAAEF,KAAE,KAAK,IAAIA,IAAEG,EAAC,GAAEV,GAAE,QAAMS,IAAEV,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,QAAO,MAAK,CAAC,EAAC,SAAQ,KAAI,QAAO,CAAC,GAAE,MAAGS,EAAC,EAAC,GAAE,EAAC,SAAQ,KAAI,QAAO,CAACD,IAAE,MAAGC,EAAC,EAAC,CAAC,EAAC,GAAE,QAAOR,GAAC,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,QAAO;AAAC,cAAMJ,KAAE,KAAK,IAAIQ,MAAG,GAAEC,EAAC,GAAEL,KAAE,EAAEF,IAAE,CAAC,GAAEG,KAAE,EAAEH,IAAE,IAAG,GAAEK,KAAE,EAAEL,IAAE,IAAG,GAAEa,KAAER,KAAE,EAAC,OAAMA,IAAE,OAAM,EAAC,IAAE,CAAC;AAAE,YAAG,WAASL,GAAE,SAAQ;AAAC,gBAAMF,KAAEE,GAAE,OAAMK,KAAEL,GAAE,QAAOM,KAAE,EAAER,MAAGO,KAAEP,KAAEO,KAAE,GAAE,MAAIA,IAAE,MAAIP,EAAC,GAAES,KAAE,EAAC,GAAGM,IAAE,MAAK,QAAO;AAAE,UAAAZ,GAAE,KAAK,EAAC,OAAMK,GAAE,CAAC,GAAE,MAAKH,IAAE,QAAOI,GAAC,CAAC,GAAEN,GAAE,KAAK,EAAC,OAAMK,GAAE,CAAC,GAAE,MAAKH,IAAE,QAAOI,GAAC,CAAC,GAAEN,GAAE,KAAK,EAAC,OAAMK,GAAE,CAAC,GAAE,MAAKJ,IAAE,QAAOK,GAAC,CAAC;AAAA,QAAC,MAAM,CAAAN,GAAE,KAAK,EAAC,OAAM,EAAE,kBAAkB,CAAC,GAAE,MAAKE,IAAE,QAAOU,GAAC,CAAC,GAAEZ,GAAE,KAAK,EAAC,OAAM,EAAE,kBAAkB,CAAC,GAAE,MAAKC,IAAE,QAAOW,GAAC,CAAC;AAAE,QAAAH,KAAE,KAAK,IAAIA,IAAEZ,EAAC,GAAEW,KAAEC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAR,GAAE,KAAKD,EAAC;AAAA,EAAC,CAAE,GAAE,QAAQ,QAAQM,GAAEL,IAAE,CAACO,IAAEC,EAAC,GAAE,EAAC,MAAKZ,MAAGA,GAAE,MAAK,OAAMK,IAAE,SAAQL,MAAGA,GAAE,QAAO,CAAC,CAAC;AAAC;AAAC,eAAe,EAAEE,IAAEF,IAAE;AAAC,QAAMG,KAAE,cAAYD,GAAE,MAAKE,KAAEF,GAAE,cAAaG,KAAE,EAAEL,EAAC,GAAE,IAAEA,MAAGA,GAAE,UAAQ,EAAEA,GAAE,OAAO,IAAE,MAAKW,KAAEN,MAAG,GAAEO,KAAE,CAAC;AAAE,MAAIC,KAAE,GAAEC,KAAE,GAAEE,KAAE;AAAG,WAAQN,KAAE,GAAEA,KAAEN,GAAE,QAAOM,MAAI;AAAC,UAAMR,KAAEE,GAAE,UAAUM,EAAC,GAAEV,KAAE,CAAC;AAAE,QAAGG,MAAG,WAASD,GAAE,KAAK;AAAS,UAAMG,KAAE,EAAE,KAAK,CAAC;AAAE,YAAOH,GAAE,MAAK;AAAA,MAAC,KAAI,QAAO;AAAC,cAAME,KAAE,EAAEF,EAAC,GAAEK,KAAE,KAAK,IAAII,IAAE,KAAG,CAAC;AAAE,QAAAE,KAAE,KAAK,IAAIA,IAAEN,EAAC,GAAEO,KAAE,KAAK,IAAIA,IAAEP,EAAC,GAAES,KAAE;AAAG,YAAIR,KAAE,EAAEN,IAAE,CAAC;AAAE,cAAMQ,KAAE,aAAYR,KAAEA,GAAE,UAAQ,MAAKU,KAAE,EAAEV,EAAC;AAAE,SAACC,MAAG,EAAEO,EAAC,KAAG,YAAUA,GAAE,QAAM,YAAUA,GAAE,SAAOE,OAAIJ,KAAE,MAAM,EAAEE,IAAEE,EAAC,IAAGZ,GAAE,KAAK,EAAC,OAAMK,IAAE,MAAKG,IAAE,QAAOJ,GAAC,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,QAAO;AAAC,cAAMD,KAAE,EAAED,IAAE,CAAC;AAAE,YAAG,EAAEC,EAAC,EAAE;AAAM,cAAMC,KAAE,EAAC,QAAOD,IAAE,OAAME,GAAC;AAAE,QAAAQ,KAAE,KAAK,IAAIA,IAAE,CAAC,GAAEC,KAAE,KAAK,IAAIA,IAAE,CAAC,GAAEd,GAAE,KAAKI,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,WAAU;AAAC,cAAMD,KAAE,EAAC,MAAK,SAAQ,OAAM,GAAE,GAAG,EAAED,IAAE,IAAG,EAAC,GAAEE,KAAE,EAAEF,IAAE,CAAC,GAAEG,KAAE,EAAEH,IAAE,IAAG,GAAEK,KAAE,KAAK,IAAII,IAAE,KAAG,CAAC,GAAEH,KAAEE,GAAEH,EAAC;AAAE,QAAAJ,GAAE,QAAM,GAAEH,GAAE,KAAK,EAAC,OAAMQ,GAAE,CAAC,GAAE,MAAKH,IAAE,QAAOF,GAAC,CAAC,GAAEH,GAAE,KAAK,EAAC,OAAMQ,GAAE,CAAC,GAAE,MAAKH,IAAE,QAAOF,GAAC,CAAC,GAAEH,GAAE,KAAK,EAAC,OAAMQ,GAAE,CAAC,GAAE,MAAKJ,IAAE,QAAOD,GAAC,CAAC;AAAE,cAAMM,KAAE,GAAEC,KAAE,MAAG,IAAE,MAAGH;AAAE,QAAAM,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEK,KAAE,KAAK,IAAIA,IAAEJ,EAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,SAAQ;AAAC,cAAMP,KAAE,EAAE,EAAED,EAAC,CAAC,GAAEE,KAAE,EAAED,EAAC,GAAEE,KAAE,EAAEF,IAAE,CAAC,GAAEI,KAAE,EAAEJ,IAAE,CAAC,GAAEM,KAAE,EAAE;AAAE,QAAAO,KAAE,MAAGhB,GAAE,KAAK,EAAC,OAAMS,GAAE,CAAC,GAAE,MAAKL,GAAC,CAAC,GAAEJ,GAAE,KAAK,EAAC,OAAMS,GAAE,CAAC,GAAE,MAAKJ,GAAC,CAAC,GAAEL,GAAE,KAAK,EAAC,OAAMS,GAAE,CAAC,GAAE,MAAKF,GAAC,CAAC;AAAE,cAAMG,KAAE,KAAK,IAAIC,IAAE,KAAG,CAAC;AAAE,QAAAE,KAAE,KAAK,IAAIA,IAAEH,EAAC,GAAEI,KAAE,KAAK,IAAIA,IAAEJ,EAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,IAAAE,GAAE,KAAKZ,EAAC;AAAA,EAAC;AAAC,SAAOS,GAAEG,IAAE,CAACC,IAAEC,EAAC,GAAE,EAAC,MAAKd,MAAGA,GAAE,MAAK,OAAMgB,IAAE,SAAQhB,MAAGA,GAAE,QAAO,CAAC;AAAC;AAAC,SAAS,GAAGE,IAAEF,IAAE;AAAC,MAAG,MAAIE,GAAE,aAAa,OAAO,QAAO,QAAQ,OAAO,IAAIC,GAAE,sCAAqC,gCAAgC,CAAC;AAAE,UAAOD,GAAE,MAAK;AAAA,IAAC,KAAI;AAAW,aAAO,EAAEA,IAAEF,EAAC;AAAA,IAAE,KAAI;AAAU,aAAO,EAAEE,IAAEF,EAAC;AAAA,IAAE,KAAI;AAAA,IAAa,KAAI;AAAU,aAAO,EAAEE,IAAEF,EAAC;AAAA,EAAC;AAAC,SAAO,QAAQ,OAAO,IAAIG,GAAE,+BAA8B,uBAAuB,CAAC;AAAC;", "names": ["t", "E", "e", "s", "r", "a", "d", "o", "n", "l", "c", "p", "u", "h", "m", "i", "f"]}