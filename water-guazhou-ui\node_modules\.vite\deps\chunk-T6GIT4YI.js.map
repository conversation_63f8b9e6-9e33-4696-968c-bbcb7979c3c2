{"version": 3, "sources": ["../../@arcgis/core/views/interactive/sketch/SketchLabelOptions.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import r from\"../../../core/Accessor.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";let t=class extends r{constructor(o){super(o),this.enabled=!1}};o([e({type:Boolean,nonNullable:!0})],t.prototype,\"enabled\",void 0),t=o([s(\"esri.views.interactive.sketch.SketchLabelOptions\")],t);const c=t;export{c as default};\n"], "mappings": ";;;;;;;;;;AAIkV,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,UAAQ;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,kDAAkD,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": []}