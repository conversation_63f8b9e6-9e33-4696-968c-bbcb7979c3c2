import {
  c as c2
} from "./chunk-XEMCQFPJ.js";
import {
  E,
  Z,
  y as y2
} from "./chunk-LHPAMQPJ.js";
import {
  e as e2
} from "./chunk-WJWRKQWS.js";
import {
  l
} from "./chunk-NRSKI5BU.js";
import {
  i as i2
} from "./chunk-N35UHD63.js";
import {
  n as n2
} from "./chunk-T5TRCNG4.js";
import {
  e as e3
} from "./chunk-EKOSN3EW.js";
import {
  a as a4
} from "./chunk-RZCOX454.js";
import {
  a as a2
} from "./chunk-2WMCP27R.js";
import {
  i
} from "./chunk-RR74IWZB.js";
import {
  p as p2
} from "./chunk-KTB2COPC.js";
import {
  o as o2
} from "./chunk-OQK7L3JR.js";
import {
  p
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import {
  c
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import {
  a as a3
} from "./chunk-Q4VCSCSY.js";
import {
  n
} from "./chunk-MIA6BJ32.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  f
} from "./chunk-VJW7RCN7.js";
import {
  T as T2
} from "./chunk-N7ADFPOO.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  r as r3
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  e,
  r as r2,
  t3 as t
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  j,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/MapImageLayer.js
var k = class extends n(a4(t2(E(y2(p2(c(_(O(p(i(o2(a3(b))))))))))))) {
  constructor(...e4) {
    super(...e4), this.dateFieldsTimeReference = null, this.datesInUnknownTimezone = false, this.dpi = 96, this.gdbVersion = null, this.imageFormat = "png24", this.imageMaxHeight = 2048, this.imageMaxWidth = 2048, this.imageTransparency = true, this.isReference = null, this.labelsVisible = false, this.operationalLayerType = "ArcGISMapServiceLayer", this.preferredTimeReference = null, this.sourceJSON = null, this.sublayers = null, this.type = "map-image", this.url = null;
  }
  normalizeCtorArgs(e4, r4) {
    return "string" == typeof e4 ? { url: e4, ...r4 } : e4;
  }
  load(e4) {
    const r4 = r(e4) ? e4.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Map Service"] }, e4).catch(w).then(() => this._fetchService(r4))), Promise.resolve(this);
  }
  readImageFormat(e4, r4) {
    const t3 = r4.supportedImageFormatTypes;
    return t3 && t3.includes("PNG32") ? "png32" : "png24";
  }
  writeSublayers(e4, r4, t3, i3) {
    var _a;
    if (!this.loaded || !e4) return;
    const o3 = e4.slice().reverse().flatten(({ sublayers: e5 }) => e5 && e5.toArray().reverse()).toArray();
    let s2 = false;
    if (this.capabilities && this.capabilities.operations.supportsExportMap && ((_a = this.capabilities.exportMap) == null ? void 0 : _a.supportsDynamicLayers)) {
      const e5 = t(i3.origin);
      if (e5 === r2.PORTAL_ITEM) {
        const e6 = this.createSublayersForOrigin("service").sublayers;
        s2 = n2(o3, e6, r2.SERVICE);
      } else if (e5 > r2.PORTAL_ITEM) {
        const e6 = this.createSublayersForOrigin("portal-item");
        s2 = n2(o3, e6.sublayers, t(e6.origin));
      }
    }
    const a5 = [], p3 = { writeSublayerStructure: s2, ...i3 };
    let n3 = s2;
    o3.forEach((e5) => {
      const r5 = e5.write({}, p3);
      a5.push(r5), n3 = n3 || "user" === e5.originOf("visible");
    });
    a5.some((e5) => Object.keys(e5).length > 1) && (r4.layers = a5), n3 && (r4.visibleLayers = o3.filter((e5) => e5.visible).map((e5) => e5.id));
  }
  createExportImageParameters(e4, r4, t3, i3) {
    const o3 = i3 && i3.pixelRatio || 1;
    e4 && this.version >= 10 && (e4 = e4.clone().shiftCentralMeridian());
    const s2 = new c2({ layer: this, floors: i3 == null ? void 0 : i3.floors, scale: i2({ extent: e4, width: r4 }) * o3 }), a5 = s2.toJSON();
    s2.destroy();
    const p3 = !i3 || !i3.rotation || this.version < 10.3 ? {} : { rotation: -i3.rotation }, n3 = e4 && e4.spatialReference, m = n3.wkid || JSON.stringify(n3.toJSON());
    a5.dpi *= o3;
    const l2 = {};
    if (i3 == null ? void 0 : i3.timeExtent) {
      const { start: e5, end: r5 } = i3.timeExtent.toJSON();
      l2.time = e5 && r5 && e5 === r5 ? "" + e5 : `${e5 ?? "null"},${r5 ?? "null"}`;
    } else this.timeInfo && !this.timeInfo.hasLiveData && (l2.time = "null,null");
    return { bbox: e4 && e4.xmin + "," + e4.ymin + "," + e4.xmax + "," + e4.ymax, bboxSR: m, imageSR: m, size: r4 + "," + t3, ...a5, ...p3, ...l2 };
  }
  async fetchImage(e4, r4, t3, i3) {
    const { data: o3 } = await this._fetchImage("image", e4, r4, t3, i3);
    return o3;
  }
  async fetchImageBitmap(e4, r4, t3, i3) {
    const { data: o3, url: s2 } = await this._fetchImage("blob", e4, r4, t3, i3);
    return e2(o3, s2);
  }
  async fetchRecomputedExtents(e4 = {}) {
    const i3 = { ...e4, query: { returnUpdates: true, f: "json", ...this.customParameters, token: this.apiKey } }, { data: o3 } = await U(this.url, i3), { extent: s2, fullExtent: a5, timeExtent: p3 } = o3, n3 = s2 || a5;
    return { fullExtent: n3 && w2.fromJSON(n3), timeExtent: p3 && T2.fromJSON({ start: p3[0], end: p3[1] }) };
  }
  loadAll() {
    return l(this, (e4) => {
      e4(this.allSublayers);
    });
  }
  serviceSupportsSpatialReference(e4) {
    return e3(this, e4);
  }
  async _fetchImage(e4, t3, o3, s2, a5) {
    var _a, _b, _c;
    const p3 = { responseType: e4, signal: (a5 == null ? void 0 : a5.signal) ?? null, query: { ...this.parsedUrl.query, ...this.createExportImageParameters(t3, o3, s2, a5), f: "image", ...this.refreshParameters, ...this.customParameters, token: this.apiKey } }, n3 = this.parsedUrl.path + "/export";
    if (null != ((_a = p3.query) == null ? void 0 : _a.dynamicLayers) && !((_c = (_b = this.capabilities) == null ? void 0 : _b.exportMap) == null ? void 0 : _c.supportsDynamicLayers)) throw new s("mapimagelayer:dynamiclayer-not-supported", `service ${this.url} doesn't support dynamic layers, which is required to be able to change the sublayer's order, rendering, labeling or source.`, { query: p3.query });
    try {
      const { data: e5 } = await U(n3, p3);
      return { data: e5, url: n3 };
    } catch (l2) {
      if (j(l2)) throw l2;
      throw new s("mapimagelayer:image-fetch-error", `Unable to load image: ${n3}`, { error: l2 });
    }
  }
  async _fetchService(e4) {
    if (this.sourceJSON) return void this.read(this.sourceJSON, { origin: "service", url: this.parsedUrl });
    const { data: t3, ssl: i3 } = await U(this.parsedUrl.path, { query: { f: "json", ...this.parsedUrl.query, ...this.customParameters, token: this.apiKey }, signal: e4 });
    i3 && (this.url = this.url.replace(/^http:/i, "https:")), this.sourceJSON = t3, this.read(t3, { origin: "service", url: this.parsedUrl });
  }
};
e([y({ type: a2 })], k.prototype, "dateFieldsTimeReference", void 0), e([y({ type: Boolean })], k.prototype, "datesInUnknownTimezone", void 0), e([y()], k.prototype, "dpi", void 0), e([y()], k.prototype, "gdbVersion", void 0), e([y()], k.prototype, "imageFormat", void 0), e([o("imageFormat", ["supportedImageFormatTypes"])], k.prototype, "readImageFormat", null), e([y({ json: { origins: { service: { read: { source: "maxImageHeight" } } } } })], k.prototype, "imageMaxHeight", void 0), e([y({ json: { origins: { service: { read: { source: "maxImageWidth" } } } } })], k.prototype, "imageMaxWidth", void 0), e([y()], k.prototype, "imageTransparency", void 0), e([y({ type: Boolean, json: { read: false, write: { enabled: true, overridePolicy: () => ({ enabled: false }) } } })], k.prototype, "isReference", void 0), e([y({ json: { read: false, write: false } })], k.prototype, "labelsVisible", void 0), e([y({ type: ["ArcGISMapServiceLayer"] })], k.prototype, "operationalLayerType", void 0), e([y({ json: { read: false, write: false } })], k.prototype, "popupEnabled", void 0), e([y({ type: a2 })], k.prototype, "preferredTimeReference", void 0), e([y()], k.prototype, "sourceJSON", void 0), e([y({ json: { write: { ignoreOrigin: true } } })], k.prototype, "sublayers", void 0), e([r3("sublayers", { layers: { type: [Z] }, visibleLayers: { type: [T] } })], k.prototype, "writeSublayers", null), e([y({ type: ["show", "hide", "hide-children"] })], k.prototype, "listMode", void 0), e([y({ json: { read: false }, readOnly: true, value: "map-image" })], k.prototype, "type", void 0), e([y(f)], k.prototype, "url", void 0), k = e([a("esri.layers.MapImageLayer")], k);
var _2 = k;

export {
  _2 as _
};
//# sourceMappingURL=chunk-KOY2CLBW.js.map
