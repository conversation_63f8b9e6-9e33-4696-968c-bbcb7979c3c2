{"version": 3, "sources": ["../../@arcgis/core/views/interactive/support/utils.js", "../../@arcgis/core/views/interactive/GraphicManipulator.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as o}from\"../../../core/maybe.js\";import{pt2px as s}from\"../../../core/screenUtils.js\";function t(t){let e=0,a=0,r=0;return t?(\"cim\"===t.type&&t.data.symbol&&\"symbolLayers\"in t.data.symbol&&t.data.symbol.symbolLayers&&t.data.symbol.symbolLayers.map((s=>{\"CIMVectorMarker\"===s.type&&s.anchorPoint&&(Math.abs(s.anchorPoint.x)>e&&(e=s.anchorPoint.x),Math.abs(s.anchorPoint.y)>a&&(a=s.anchorPoint.y),o(s.size)&&s.size>r&&(r=s.size))})),e=s(e),a=s(a),r=s(r),{offsetX:e,offsetY:a,size:r}):{offsetX:e,offsetY:a,size:r}}export{t as getSymbolInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Accessor.js\";import o from\"../../core/Evented.js\";import{isNone as i,isSome as s}from\"../../core/maybe.js\";import{watch as r,sync as l}from\"../../core/reactiveUtils.js\";import{screenPointObjectToArray as n,pt2px as a,createScreenPointArray as c}from\"../../core/screenUtils.js\";import{property as h}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import{k as m}from\"../../chunks/vec2.js\";import{i as u}from\"../../chunks/vec3.js\";import{c as y}from\"../../chunks/vec3f64.js\";import{canProjectWithoutEngine as b,project as g,projectPointToVector as d}from\"../../geometry/projection.js\";import{getGraphicEffectiveElevationMode as f}from\"../../support/elevationInfoUtils.js\";import{getDefaultSymbol2D as _}from\"../../symbols/support/defaults.js\";import v from\"../../symbols/support/ElevationInfo.js\";import{getSymbolInfo as S}from\"./support/utils.js\";import{intersectsDrapedGeometry as C}from\"../support/drapedUtils.js\";let j=class extends e{set graphic(t){this._circleCollisionCache=null,this._originalSymbol=t.symbol,this._set(\"graphic\",t),this.attachSymbolChanged()}get elevationInfo(){const{layer:t}=this.graphic,e=t&&\"elevationInfo\"in t?t.elevationInfo:null,o=f(this.graphic),i=e?e.offset:0;return new v({mode:o,offset:i})}set focusedSymbol(t){t!==this._get(\"focusedSymbol\")&&(this._set(\"focusedSymbol\",t),this._updateGraphicSymbol(),this._circleCollisionCache=null)}grabbableForEvent(){return!0}set grabbing(t){t!==this._get(\"grabbing\")&&(this._set(\"grabbing\",t),this._updateGraphicSymbol())}set hovering(t){t!==this._get(\"hovering\")&&(this._set(\"hovering\",t),this._updateGraphicSymbol())}set selected(t){t!==this._get(\"selected\")&&(this._set(\"selected\",t),this._updateGraphicSymbol(),this.events.emit(\"select-changed\",{action:t?\"select\":\"deselect\"}))}get _focused(){return this._get(\"hovering\")||this._get(\"grabbing\")}constructor(t){super(t),this.layer=null,this.interactive=!0,this.selectable=!1,this.grabbable=!0,this.dragging=!1,this.cursor=null,this.events=new o.EventEmitter,this._circleCollisionCache=null,this._graphicSymbolChangedHandle=null,this._originalSymbol=null}destroy(){this.detachSymbolChanged(),this._resetGraphicSymbol(),this._set(\"view\",null)}intersectionDistance(t){const e=this.graphic;if(!1===e.visible)return null;const o=e.geometry;if(i(o))return null;const r=this._get(\"focusedSymbol\"),l=s(r)?r:e.symbol;return\"2d\"===this.view.type?this._intersectDistance2D(this.view,t,o,l):this._intersectDistance3D(this.view,t,e)}attach(){this.attachSymbolChanged(),s(this.layer)&&this.layer.add(this.graphic)}detach(){this.detachSymbolChanged(),this._resetGraphicSymbol(),s(this.layer)&&this.layer.remove(this.graphic)}attachSymbolChanged(){this.detachSymbolChanged(),this._graphicSymbolChangedHandle=r((()=>this.graphic?.symbol),(t=>{s(t)&&t!==this.focusedSymbol&&t!==this._originalSymbol&&(this._originalSymbol=t,this._focused&&s(this.focusedSymbol)&&(this.graphic.symbol=this.focusedSymbol))}),l)}detachSymbolChanged(){s(this._graphicSymbolChangedHandle)&&(this._graphicSymbolChangedHandle.remove(),this._graphicSymbolChangedHandle=null)}onElevationChange(){}onViewChange(){}_updateGraphicSymbol(){this.graphic.symbol=this._focused&&s(this.focusedSymbol)?this.focusedSymbol:this._originalSymbol}_resetGraphicSymbol(){this.graphic.symbol=this._originalSymbol}_intersectDistance2D(t,e,o,r){if(r=r||_(o),i(r))return null;const l=1;let c=this._circleCollisionCache;if(\"point\"===o.type&&\"cim\"===r.type&&\"CIMPointSymbol\"===r.data.symbol?.type&&r.data.symbol.symbolLayers){const{offsetX:i,offsetY:s,size:a}=S(r),c=n(e,D),h=a/2,p=t.toScreen(o),u=p.x+i,y=p.y+s;return m(c,[u,y])<h*h?l:null}if(\"point\"!==o.type||\"simple-marker\"!==r.type)return C(e,o,t)?l:null;if(i(c)||!c.originalPoint.equals(o)){const e=o,i=t.spatialReference;if(b(e.spatialReference,i)){const t=g(e,i);c={originalPoint:e.clone(),mapPoint:t,radiusPx:a(r.size)},this._circleCollisionCache=c}}if(s(c)){const o=n(e,D),i=t.toScreen?.(c.mapPoint);if(!i)return null;const s=c.radiusPx,h=i.x+a(r.xoffset),p=i.y-a(r.yoffset);return m(o,[h,p])<s*s?l:null}return null}_intersectDistance3D(t,e,o){const i=t.toMap(e,{include:[o]});return i&&d(i,w,t.renderSpatialReference)?u(w,t.state.camera.eye):null}};t([h({constructOnly:!0,nonNullable:!0})],j.prototype,\"graphic\",null),t([h()],j.prototype,\"elevationInfo\",null),t([h({constructOnly:!0,nonNullable:!0})],j.prototype,\"view\",void 0),t([h({value:null})],j.prototype,\"focusedSymbol\",null),t([h({constructOnly:!0})],j.prototype,\"layer\",void 0),t([h()],j.prototype,\"interactive\",void 0),t([h()],j.prototype,\"selectable\",void 0),t([h()],j.prototype,\"grabbable\",void 0),t([h({value:!1})],j.prototype,\"grabbing\",null),t([h()],j.prototype,\"dragging\",void 0),t([h()],j.prototype,\"hovering\",null),t([h({value:!1})],j.prototype,\"selected\",null),t([h()],j.prototype,\"cursor\",void 0),j=t([p(\"esri.views.interactive.GraphicManipulator\")],j);const w=y(),D=c();export{j as GraphicManipulator};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqG,SAASA,GAAEA,IAAE;AAAC,MAAIC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,SAAOH,MAAG,UAAQA,GAAE,QAAMA,GAAE,KAAK,UAAQ,kBAAiBA,GAAE,KAAK,UAAQA,GAAE,KAAK,OAAO,gBAAcA,GAAE,KAAK,OAAO,aAAa,IAAK,OAAG;AAAC,0BAAoB,EAAE,QAAM,EAAE,gBAAc,KAAK,IAAI,EAAE,YAAY,CAAC,IAAEC,OAAIA,KAAE,EAAE,YAAY,IAAG,KAAK,IAAI,EAAE,YAAY,CAAC,IAAEC,OAAIA,KAAE,EAAE,YAAY,IAAG,EAAE,EAAE,IAAI,KAAG,EAAE,OAAKC,OAAIA,KAAE,EAAE;AAAA,EAAM,CAAE,GAAEF,KAAE,EAAEA,EAAC,GAAEC,KAAE,EAAEA,EAAC,GAAEC,KAAE,EAAEA,EAAC,GAAE,EAAC,SAAQF,IAAE,SAAQC,IAAE,MAAKC,GAAC,KAAG,EAAC,SAAQF,IAAE,SAAQC,IAAE,MAAKC,GAAC;AAAC;;;ACAmnB,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,IAAI,QAAQC,IAAE;AAAC,SAAK,wBAAsB,MAAK,KAAK,kBAAgBA,GAAE,QAAO,KAAK,KAAK,WAAUA,EAAC,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,SAAQC,KAAED,MAAG,mBAAkBA,KAAEA,GAAE,gBAAc,MAAKE,KAAEC,GAAE,KAAK,OAAO,GAAEC,KAAEH,KAAEA,GAAE,SAAO;AAAE,WAAO,IAAII,GAAE,EAAC,MAAKH,IAAE,QAAOE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAcJ,IAAE;AAAC,IAAAA,OAAI,KAAK,KAAK,eAAe,MAAI,KAAK,KAAK,iBAAgBA,EAAC,GAAE,KAAK,qBAAqB,GAAE,KAAK,wBAAsB;AAAA,EAAK;AAAA,EAAC,oBAAmB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,IAAAA,OAAI,KAAK,KAAK,UAAU,MAAI,KAAK,KAAK,YAAWA,EAAC,GAAE,KAAK,qBAAqB;AAAA,EAAE;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,IAAAA,OAAI,KAAK,KAAK,UAAU,MAAI,KAAK,KAAK,YAAWA,EAAC,GAAE,KAAK,qBAAqB;AAAA,EAAE;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,IAAAA,OAAI,KAAK,KAAK,UAAU,MAAI,KAAK,KAAK,YAAWA,EAAC,GAAE,KAAK,qBAAqB,GAAE,KAAK,OAAO,KAAK,kBAAiB,EAAC,QAAOA,KAAE,WAAS,WAAU,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,KAAK,UAAU,KAAG,KAAK,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,cAAY,MAAG,KAAK,aAAW,OAAG,KAAK,YAAU,MAAG,KAAK,WAAS,OAAG,KAAK,SAAO,MAAK,KAAK,SAAO,IAAI,EAAE,gBAAa,KAAK,wBAAsB,MAAK,KAAK,8BAA4B,MAAK,KAAK,kBAAgB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,oBAAoB,GAAE,KAAK,oBAAoB,GAAE,KAAK,KAAK,QAAO,IAAI;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAQ,QAAG,UAAKA,GAAE,QAAQ,QAAO;AAAK,UAAMC,KAAED,GAAE;AAAS,QAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,UAAMI,KAAE,KAAK,KAAK,eAAe,GAAEH,KAAE,EAAEG,EAAC,IAAEA,KAAEL,GAAE;AAAO,WAAM,SAAO,KAAK,KAAK,OAAK,KAAK,qBAAqB,KAAK,MAAKD,IAAEE,IAAEC,EAAC,IAAE,KAAK,qBAAqB,KAAK,MAAKH,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,oBAAoB,GAAE,EAAE,KAAK,KAAK,KAAG,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,oBAAoB,GAAE,KAAK,oBAAoB,GAAE,EAAE,KAAK,KAAK,KAAG,KAAK,MAAM,OAAO,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,SAAK,oBAAoB,GAAE,KAAK,8BAA4B,EAAG,MAAE;AAJ94F;AAIg5F,wBAAK,YAAL,mBAAc;AAAA,OAAS,CAAAD,OAAG;AAAC,QAAEA,EAAC,KAAGA,OAAI,KAAK,iBAAeA,OAAI,KAAK,oBAAkB,KAAK,kBAAgBA,IAAE,KAAK,YAAU,EAAE,KAAK,aAAa,MAAI,KAAK,QAAQ,SAAO,KAAK;AAAA,IAAe,GAAG,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,MAAE,KAAK,2BAA2B,MAAI,KAAK,4BAA4B,OAAO,GAAE,KAAK,8BAA4B;AAAA,EAAK;AAAA,EAAC,oBAAmB;AAAA,EAAC;AAAA,EAAC,eAAc;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,QAAQ,SAAO,KAAK,YAAU,EAAE,KAAK,aAAa,IAAE,KAAK,gBAAc,KAAK;AAAA,EAAe;AAAA,EAAC,sBAAqB;AAAC,SAAK,QAAQ,SAAO,KAAK;AAAA,EAAe;AAAA,EAAC,qBAAqBA,IAAEC,IAAEC,IAAEI,IAAE;AAJt9G;AAIu9G,QAAGA,KAAEA,MAAG,EAAEJ,EAAC,GAAE,EAAEI,EAAC,EAAE,QAAO;AAAK,UAAMH,KAAE;AAAE,QAAI,IAAE,KAAK;AAAsB,QAAG,YAAUD,GAAE,QAAM,UAAQI,GAAE,QAAM,uBAAmB,KAAAA,GAAE,KAAK,WAAP,mBAAe,SAAMA,GAAE,KAAK,OAAO,cAAa;AAAC,YAAK,EAAC,SAAQF,IAAE,SAAQ,GAAE,MAAKG,GAAC,IAAEP,GAAEM,EAAC,GAAEE,KAAE,EAAEP,IAAE,CAAC,GAAE,IAAEM,KAAE,GAAE,IAAEP,GAAE,SAASE,EAAC,GAAEO,KAAE,EAAE,IAAEL,IAAEM,KAAE,EAAE,IAAE;AAAE,aAAO,EAAEF,IAAE,CAACC,IAAEC,EAAC,CAAC,IAAE,IAAE,IAAEP,KAAE;AAAA,IAAI;AAAC,QAAG,YAAUD,GAAE,QAAM,oBAAkBI,GAAE,KAAK,QAAO,EAAEL,IAAEC,IAAEF,EAAC,IAAEG,KAAE;AAAK,QAAG,EAAE,CAAC,KAAG,CAAC,EAAE,cAAc,OAAOD,EAAC,GAAE;AAAC,YAAMD,KAAEC,IAAEE,KAAEJ,GAAE;AAAiB,UAAG,GAAEC,GAAE,kBAAiBG,EAAC,GAAE;AAAC,cAAMJ,KAAE,GAAEC,IAAEG,EAAC;AAAE,YAAE,EAAC,eAAcH,GAAE,MAAM,GAAE,UAASD,IAAE,UAAS,EAAEM,GAAE,IAAI,EAAC,GAAE,KAAK,wBAAsB;AAAA,MAAC;AAAA,IAAC;AAAC,QAAG,EAAE,CAAC,GAAE;AAAC,YAAMJ,KAAE,EAAED,IAAE,CAAC,GAAEG,MAAE,KAAAJ,GAAE,aAAF,wBAAAA,IAAa,EAAE;AAAU,UAAG,CAACI,GAAE,QAAO;AAAK,YAAM,IAAE,EAAE,UAAS,IAAEA,GAAE,IAAE,EAAEE,GAAE,OAAO,GAAE,IAAEF,GAAE,IAAE,EAAEE,GAAE,OAAO;AAAE,aAAO,EAAEJ,IAAE,CAAC,GAAE,CAAC,CAAC,IAAE,IAAE,IAAEC,KAAE;AAAA,IAAI;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,qBAAqBH,IAAEC,IAAEC,IAAE;AAAC,UAAME,KAAEJ,GAAE,MAAMC,IAAE,EAAC,SAAQ,CAACC,EAAC,EAAC,CAAC;AAAE,WAAOE,MAAG,GAAEA,IAAE,GAAEJ,GAAE,sBAAsB,IAAE,EAAE,GAAEA,GAAE,MAAM,OAAO,GAAG,IAAE;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,MAAG,aAAY,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,KAAI,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,2CAA2C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAEW,GAAE;AAAV,IAAY,IAAE,EAAE;", "names": ["t", "e", "a", "r", "t", "e", "o", "l", "i", "x", "r", "a", "c", "u", "y", "n"]}