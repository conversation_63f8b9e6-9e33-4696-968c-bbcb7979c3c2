{"version": 3, "sources": ["../../@arcgis/core/rest/support/ProjectParameters.js", "../../@arcgis/core/rest/geometryService/project.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";import{getJsonType as s}from\"../../geometry/support/jsonUtils.js\";let i=class extends t{constructor(r){super(r),this.geometries=[],this.outSpatialReference=null,this.transformation=null,this.transformForward=null}toJSON(){const r=this.geometries.map((r=>r.toJSON())),t=this.geometries[0],o={};return o.outSR=this.outSpatialReference.wkid||JSON.stringify(this.outSpatialReference.toJSON()),o.inSR=t.spatialReference.wkid||JSON.stringify(t.spatialReference.toJSON()),o.geometries=JSON.stringify({geometryType:s(t),geometries:r}),this.transformation&&(o.transformation=this.transformation.wkid||JSON.stringify(this.transformation)),null!=this.transformForward&&(o.transformForward=this.transformForward),o}};r([o()],i.prototype,\"geometries\",void 0),r([o({json:{read:{source:\"outSR\"}}})],i.prototype,\"outSpatialReference\",void 0),r([o()],i.prototype,\"transformation\",void 0),r([o()],i.prototype,\"transformForward\",void 0),i=r([e(\"esri.rest.support.ProjectParameters\")],i);const a=i;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"../../request.js\";import{ensureType as o}from\"../../core/accessorSupport/ensureType.js\";import{getJsonType as t}from\"../../geometry/support/jsonUtils.js\";import{parseUrl as e,asValidOptions as s}from\"../utils.js\";import{decodeGeometries as p}from\"./utils.js\";import m from\"../support/ProjectParameters.js\";const i=o(m);async function n(o,m,n){m=i(m);const u=e(o),c={...u.query,f:\"json\",...m.toJSON()},j=m.outSpatialReference,a=t(m.geometries[0]),f=s(c,n);return r(u.path+\"/project\",f).then((({data:{geometries:r}})=>p(r,a,j)))}export{n as project};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIoZ,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,sBAAoB,MAAK,KAAK,iBAAe,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,UAAM,IAAE,KAAK,WAAW,IAAK,CAAAC,OAAGA,GAAE,OAAO,CAAE,GAAE,IAAE,KAAK,WAAW,CAAC,GAAEC,KAAE,CAAC;AAAE,WAAOA,GAAE,QAAM,KAAK,oBAAoB,QAAM,KAAK,UAAU,KAAK,oBAAoB,OAAO,CAAC,GAAEA,GAAE,OAAK,EAAE,iBAAiB,QAAM,KAAK,UAAU,EAAE,iBAAiB,OAAO,CAAC,GAAEA,GAAE,aAAW,KAAK,UAAU,EAAC,cAAa,EAAE,CAAC,GAAE,YAAW,EAAC,CAAC,GAAE,KAAK,mBAAiBA,GAAE,iBAAe,KAAK,eAAe,QAAM,KAAK,UAAU,KAAK,cAAc,IAAG,QAAM,KAAK,qBAAmBA,GAAE,mBAAiB,KAAK,mBAAkBA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,QAAO,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEA,EAAC;AAAE,IAAMG,KAAEH;;;ACAl+B,IAAMI,KAAE,EAAEC,EAAC;AAAE,eAAe,EAAEC,IAAE,GAAEC,IAAE;AAAC,MAAEH,GAAE,CAAC;AAAE,QAAM,IAAE,EAAEE,EAAC,GAAEE,KAAE,EAAC,GAAG,EAAE,OAAM,GAAE,QAAO,GAAG,EAAE,OAAO,EAAC,GAAE,IAAE,EAAE,qBAAoBH,KAAE,EAAE,EAAE,WAAW,CAAC,CAAC,GAAEI,KAAE,EAAED,IAAED,EAAC;AAAE,SAAO,EAAE,EAAE,OAAK,YAAWE,EAAC,EAAE,KAAM,CAAC,EAAC,MAAK,EAAC,YAAW,EAAC,EAAC,MAAI,EAAE,GAAEJ,IAAE,CAAC,CAAE;AAAC;", "names": ["i", "r", "o", "a", "i", "a", "o", "n", "c", "f"]}