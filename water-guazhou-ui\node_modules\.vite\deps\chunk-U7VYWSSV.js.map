{"version": 3, "sources": ["../../@arcgis/core/chunks/boundedPlane.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../core/has.js\";import s from\"../core/Logger.js\";import{acosClamped as t}from\"../core/mathUtils.js\";import{ObjectStack as i}from\"../core/ObjectStack.js\";import{a as n,t as o,d as r}from\"./mat4.js\";import{c as e}from\"./mat4f64.js\";import{c as a,g as c,a as u,s as g,i as b,l as f,r as p,e as l,b as m,k as d,m as h,n as j,p as I}from\"./vec3.js\";import{f as y,a as P,c as v}from\"./vec3f64.js\";import{Axis as N}from\"../geometry/support/Axis.js\";import{create as w,projectPointClamp as M,distance2 as S}from\"../geometry/support/lineSegment.js\";import{create as T,wrap as A,copy as x,fromVectorsAndPoint as V,fromValues as _,intersectRay as O,intersectLineSegment as k,intersectLineSegmentClamp as E,isPointInside as U,projectPoint as Y,normal as q,setOffsetFromPoint as B}from\"../geometry/support/plane.js\";import{distance2 as F}from\"../geometry/support/ray.js\";import{projectPointSignedLength as R}from\"../geometry/support/vector.js\";import{sv3d as C}from\"../geometry/support/vectorStacks.js\";const L=s.getLogger(\"esri.views.3d.support.geometryUtils.boundedPlane\");class z{constructor(){this.plane=T(),this.origin=v(),this.basis1=v(),this.basis2=v()}}const G=z;function W(s=Ns){return{plane:T(s.plane),origin:P(s.origin),basis1:P(s.basis1),basis2:P(s.basis2)}}function X(s,t,i){const n=Ts.get();return n.origin=s,n.basis1=t,n.basis2=i,n.plane=A(0,0,0,0),J(n),n}function Z(s,t=W()){return H(s.origin,s.basis1,s.basis2,t)}function D(s,t){a(t.origin,s.origin),a(t.basis1,s.basis1),a(t.basis2,s.basis2),x(t.plane,s.plane)}function H(s,t,i,n=W()){return a(n.origin,s),a(n.basis1,t),a(n.basis2,i),J(n),Ps(n,\"fromValues()\"),n}function J(s){V(s.basis2,s.basis1,s.origin,s.plane)}function K(s,t,i){s!==i&&Z(s,i);const n=c(C.get(),ds(s),t);return u(i.origin,i.origin,n),i.plane[3]-=t,i}function Q(s,t,i){return $(t,i),K(i,bs(s,s.origin),i),i}function $(s,t=W()){const i=(s[2]-s[0])/2,n=(s[3]-s[1])/2;return g(t.origin,s[0]+i,s[1]+n,0),g(t.basis1,i,0,0),g(t.basis2,0,n,0),_(0,0,1,0,t.plane),t}function ss(s,t,i){return!!O(s.plane,t,i)&&js(s,i)}function ts(s,t,i){if(ss(s,t,i))return i;const n=is(s,t,C.get());return u(i,t.origin,c(C.get(),t.direction,b(t.origin,n)/f(t.direction))),i}function is(s,i,n){const o=ws.get();vs(s,i,o,ws.get());let r=Number.POSITIVE_INFINITY;for(const e of As){const c=ys(s,e,Ms.get()),u=C.get();if(k(o,c,u)){const s=p(C.get(),i.origin,u),o=Math.abs(t(l(i.direction,s)));o<r&&(r=o,a(n,u))}}return r===Number.POSITIVE_INFINITY?ns(s,i,n):n}function ns(s,t,i){if(ss(s,t,i))return i;const n=ws.get(),o=ws.get();vs(s,t,n,o);let r=Number.POSITIVE_INFINITY;for(const e of As){const c=ys(s,e,Ms.get()),u=C.get();if(E(n,c,u)){const s=F(t,u);if(!U(o,u))continue;s<r&&(r=s,a(i,u))}}return es(s,t.origin)<r&&os(s,t.origin,i),i}function os(s,t,i){const n=Y(s.plane,t,C.get()),o=M(Is(s,s.basis1),n,-1,1,C.get()),r=M(Is(s,s.basis2),n,-1,1,C.get());return m(i,u(C.get(),o,r),s.origin),i}function rs(s,t,i){const{origin:n,basis1:o,basis2:r}=s,e=m(C.get(),t,n),a=R(o,e),c=R(r,e),u=R(ds(s),e);return g(i,a,c,u)}function es(s,t){const i=rs(s,t,C.get()),{basis1:n,basis2:o}=s,r=f(n),e=f(o),a=Math.max(Math.abs(i[0])-r,0),c=Math.max(Math.abs(i[1])-e,0),u=i[2];return a*a+c*c+u*u}function as(s,t){return Math.sqrt(es(s,t))}function cs(s,t){let i=Number.NEGATIVE_INFINITY;for(const n of As){const o=ys(s,n,Ms.get()),r=S(o,t);r>i&&(i=r)}return Math.sqrt(i)}function us(s,t){return U(s.plane,t)&&js(s,t)}function gs(s,t,i,n){return hs(s,i,n)}function bs(s,t){const i=-s.plane[3];return R(ds(s),t)-i}function fs(s,t,i,n){const o=bs(s,t),r=c(Ss,ds(s),i-o);return u(n,t,r),n}function ps(s,t){return d(s.basis1,t.basis1)&&d(s.basis2,t.basis2)&&d(s.origin,t.origin)}function ls(s,t,i){return s!==i&&Z(s,i),n(xs,t),o(xs,xs),h(i.basis1,s.basis1,xs),h(i.basis2,s.basis2,xs),h(q(i.plane),q(s.plane),xs),h(i.origin,s.origin,t),B(i.plane,i.plane,i.origin),i}function ms(s,t,i,n){return s!==n&&Z(s,n),r(Vs,t,i),h(n.basis1,s.basis1,Vs),h(n.basis2,s.basis2,Vs),J(n),n}function ds(s){return q(s.plane)}function hs(s,t,i){switch(t){case N.X:a(i,s.basis1),j(i,i);break;case N.Y:a(i,s.basis2),j(i,i);break;case N.Z:a(i,ds(s))}return i}function js(s,t){const i=m(C.get(),t,s.origin),n=I(s.basis1),o=I(s.basis2),r=l(s.basis1,i),e=l(s.basis2,i);return-r-n<0&&r-n<0&&-e-o<0&&e-o<0}function Is(s,t){const i=Ms.get();return a(i.origin,s.origin),a(i.vector,t),i}function ys(s,t,i){const{basis1:n,basis2:o,origin:r}=s,e=c(C.get(),n,t.origin[0]),a=c(C.get(),o,t.origin[1]);u(i.origin,e,a),u(i.origin,i.origin,r);const g=c(C.get(),n,t.direction[0]),b=c(C.get(),o,t.direction[1]);return c(i.vector,u(g,g,b),2),i}function Ps(s,t){Math.abs(l(s.basis1,s.basis2)/(f(s.basis1)*f(s.basis2)))>1e-6&&L.warn(t,\"Provided basis vectors are not perpendicular\"),Math.abs(l(s.basis1,ds(s)))>1e-6&&L.warn(t,\"Basis vectors and plane normal are not perpendicular\"),Math.abs(-l(ds(s),s.origin)-s.plane[3])>1e-6&&L.warn(t,\"Plane offset is not consistent with plane origin\")}function vs(s,t,i,n){const o=ds(s);V(o,t.direction,t.origin,i),V(q(i),o,t.origin,n)}const Ns={plane:T(),origin:y(0,0,0),basis1:y(1,0,0),basis2:y(0,1,0)},ws=new i(T),Ms=new i(w),Ss=v(),Ts=new i((()=>W())),As=[{origin:[-1,-1],direction:[1,0]},{origin:[1,-1],direction:[0,1]},{origin:[1,1],direction:[-1,0]},{origin:[-1,1],direction:[0,-1]}],xs=e(),Vs=e(),_s=Object.freeze(Object.defineProperty({__proto__:null,BoundedPlaneClass:G,UP:Ns,altitudeAt:bs,axisAt:gs,closestPoint:ns,closestPointOnSilhouette:is,copy:Z,copyWithoutVerify:D,create:W,distance:as,distance2:es,distanceToSilhouette:cs,elevate:K,equals:ps,extrusionContainsPoint:us,fromAABoundingRect:$,fromValues:H,intersectRay:ss,intersectRayClosestSilhouette:ts,normal:ds,projectPoint:os,projectPointLocal:rs,rotate:ms,setAltitudeAt:fs,setExtent:Q,transform:ls,updateUnboundedPlane:J,wrap:X},Symbol.toStringTag,{value:\"Module\"}));export{fs as A,G as B,Ns as U,W as a,_s as b,Z as c,ps as d,us as e,H as f,$ as g,as as h,ss as i,D as j,K as k,ts as l,is as m,ds as n,ns as o,os as p,rs as q,ms as r,Q as s,ls as t,J as u,es as v,X as w,cs as x,gs as y,bs as z};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIq+B,IAAMA,KAAE,EAAE,UAAU,kDAAkD;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAMC,GAAE,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,SAAO,EAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAED;AAAE,SAAS,EAAEE,KAAE,IAAG;AAAC,SAAM,EAAC,OAAMD,GAAEC,GAAE,KAAK,GAAE,QAAO,EAAEA,GAAE,MAAM,GAAE,QAAO,EAAEA,GAAE,MAAM,GAAE,QAAO,EAAEA,GAAE,MAAM,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE,GAAE;AAAC,QAAMC,KAAE,GAAG,IAAI;AAAE,SAAOA,GAAE,SAAOF,IAAEE,GAAE,SAAOD,IAAEC,GAAE,SAAO,GAAEA,GAAE,QAAM,EAAE,GAAE,GAAE,GAAE,CAAC,GAAEC,GAAED,EAAC,GAAEA;AAAC;AAAC,SAAS,EAAEF,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAOG,GAAEJ,GAAE,QAAOA,GAAE,QAAOA,GAAE,QAAOC,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,EAAAI,GAAEJ,GAAE,QAAOD,GAAE,MAAM,GAAEK,GAAEJ,GAAE,QAAOD,GAAE,MAAM,GAAEK,GAAEJ,GAAE,QAAOD,GAAE,MAAM,GAAE,EAAEC,GAAE,OAAMD,GAAE,KAAK;AAAC;AAAC,SAASI,GAAEJ,IAAEC,IAAE,GAAEC,KAAE,EAAE,GAAE;AAAC,SAAOG,GAAEH,GAAE,QAAOF,EAAC,GAAEK,GAAEH,GAAE,QAAOD,EAAC,GAAEI,GAAEH,GAAE,QAAO,CAAC,GAAEC,GAAED,EAAC,GAAE,GAAGA,IAAE,cAAc,GAAEA;AAAC;AAAC,SAASC,GAAEH,IAAE;AAAC,EAAAM,GAAEN,GAAE,QAAOA,GAAE,QAAOA,GAAE,QAAOA,GAAE,KAAK;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE,GAAE;AAAC,EAAAD,OAAI,KAAG,EAAEA,IAAE,CAAC;AAAE,QAAME,KAAE,EAAE,EAAE,IAAI,GAAE,GAAGF,EAAC,GAAEC,EAAC;AAAE,SAAO,EAAE,EAAE,QAAO,EAAE,QAAOC,EAAC,GAAE,EAAE,MAAM,CAAC,KAAGD,IAAE;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAEA,IAAE,CAAC,GAAE,EAAE,GAAE,GAAGD,IAAEA,GAAE,MAAM,GAAE,CAAC,GAAE;AAAC;AAAC,SAAS,EAAEA,IAAEC,KAAE,EAAE,GAAE;AAAC,QAAM,KAAGD,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG,GAAEE,MAAGF,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAG;AAAE,SAAO,EAAEC,GAAE,QAAOD,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAEE,IAAE,CAAC,GAAE,EAAED,GAAE,QAAO,GAAE,GAAE,CAAC,GAAE,EAAEA,GAAE,QAAO,GAAEC,IAAE,CAAC,GAAEK,GAAE,GAAE,GAAE,GAAE,GAAEN,GAAE,KAAK,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAE;AAAC,SAAM,CAAC,CAACO,GAAER,GAAE,OAAMC,IAAE,CAAC,KAAG,GAAGD,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE,GAAE;AAAC,MAAG,GAAGD,IAAEC,IAAE,CAAC,EAAE,QAAO;AAAE,QAAMC,KAAE,GAAGF,IAAEC,IAAE,EAAE,IAAI,CAAC;AAAE,SAAO,EAAE,GAAEA,GAAE,QAAO,EAAE,EAAE,IAAI,GAAEA,GAAE,WAAU,EAAEA,GAAE,QAAOC,EAAC,IAAEF,GAAEC,GAAE,SAAS,CAAC,CAAC,GAAE;AAAC;AAAC,SAAS,GAAGD,IAAE,GAAEE,IAAE;AAAC,QAAMO,KAAE,GAAG,IAAI;AAAE,KAAGT,IAAE,GAAES,IAAE,GAAG,IAAI,CAAC;AAAE,MAAIJ,KAAE,OAAO;AAAkB,aAAUK,MAAK,IAAG;AAAC,UAAMC,KAAE,GAAGX,IAAEU,IAAE,GAAG,IAAI,CAAC,GAAEE,KAAE,EAAE,IAAI;AAAE,QAAG,EAAEH,IAAEE,IAAEC,EAAC,GAAE;AAAC,YAAMZ,KAAE,EAAE,EAAE,IAAI,GAAE,EAAE,QAAOY,EAAC,GAAEH,KAAE,KAAK,IAAI,EAAE,EAAE,EAAE,WAAUT,EAAC,CAAC,CAAC;AAAE,MAAAS,KAAEJ,OAAIA,KAAEI,IAAEJ,GAAEH,IAAEU,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAOP,OAAI,OAAO,oBAAkB,GAAGL,IAAE,GAAEE,EAAC,IAAEA;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE,GAAE;AAAC,MAAG,GAAGD,IAAEC,IAAE,CAAC,EAAE,QAAO;AAAE,QAAMC,KAAE,GAAG,IAAI,GAAEO,KAAE,GAAG,IAAI;AAAE,KAAGT,IAAEC,IAAEC,IAAEO,EAAC;AAAE,MAAIJ,KAAE,OAAO;AAAkB,aAAUK,MAAK,IAAG;AAAC,UAAMC,KAAE,GAAGX,IAAEU,IAAE,GAAG,IAAI,CAAC,GAAEE,KAAE,EAAE,IAAI;AAAE,QAAG,EAAEV,IAAES,IAAEC,EAAC,GAAE;AAAC,YAAMZ,KAAE,EAAEC,IAAEW,EAAC;AAAE,UAAG,CAAC,EAAEH,IAAEG,EAAC,EAAE;AAAS,MAAAZ,KAAEK,OAAIA,KAAEL,IAAEK,GAAE,GAAEO,EAAC;AAAA,IAAE;AAAA,EAAC;AAAC,SAAO,GAAGZ,IAAEC,GAAE,MAAM,IAAEI,MAAG,GAAGL,IAAEC,GAAE,QAAO,CAAC,GAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAE;AAAC,QAAMC,KAAE,EAAEF,GAAE,OAAMC,IAAE,EAAE,IAAI,CAAC,GAAEQ,KAAEI,GAAE,GAAGb,IAAEA,GAAE,MAAM,GAAEE,IAAE,IAAG,GAAE,EAAE,IAAI,CAAC,GAAEG,KAAEQ,GAAE,GAAGb,IAAEA,GAAE,MAAM,GAAEE,IAAE,IAAG,GAAE,EAAE,IAAI,CAAC;AAAE,SAAO,EAAE,GAAE,EAAE,EAAE,IAAI,GAAEO,IAAEJ,EAAC,GAAEL,GAAE,MAAM,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE,GAAE;AAAC,QAAK,EAAC,QAAOC,IAAE,QAAOO,IAAE,QAAOJ,GAAC,IAAEL,IAAEU,KAAE,EAAE,EAAE,IAAI,GAAET,IAAEC,EAAC,GAAE,IAAE,EAAEO,IAAEC,EAAC,GAAEC,KAAE,EAAEN,IAAEK,EAAC,GAAEE,KAAE,EAAE,GAAGZ,EAAC,GAAEU,EAAC;AAAE,SAAO,EAAE,GAAE,GAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGZ,IAAEC,IAAE;AAAC,QAAM,IAAE,GAAGD,IAAEC,IAAE,EAAE,IAAI,CAAC,GAAE,EAAC,QAAOC,IAAE,QAAOO,GAAC,IAAET,IAAEK,KAAEL,GAAEE,EAAC,GAAEQ,KAAEV,GAAES,EAAC,GAAE,IAAE,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,IAAEJ,IAAE,CAAC,GAAEM,KAAE,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,IAAED,IAAE,CAAC,GAAEE,KAAE,EAAE,CAAC;AAAE,SAAO,IAAE,IAAED,KAAEA,KAAEC,KAAEA;AAAC;AAAC,SAAS,GAAGZ,IAAEC,IAAE;AAAC,SAAO,KAAK,KAAK,GAAGD,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAI,IAAE,OAAO;AAAkB,aAAUC,MAAK,IAAG;AAAC,UAAMO,KAAE,GAAGT,IAAEE,IAAE,GAAG,IAAI,CAAC,GAAEG,KAAE,EAAEI,IAAER,EAAC;AAAE,IAAAI,KAAE,MAAI,IAAEA;AAAA,EAAE;AAAC,SAAO,KAAK,KAAK,CAAC;AAAC;AAAC,SAAS,GAAGL,IAAEC,IAAE;AAAC,SAAO,EAAED,GAAE,OAAMC,EAAC,KAAG,GAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAO,GAAGF,IAAE,GAAEE,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,QAAM,IAAE,CAACD,GAAE,MAAM,CAAC;AAAE,SAAO,EAAE,GAAGA,EAAC,GAAEC,EAAC,IAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAMO,KAAE,GAAGT,IAAEC,EAAC,GAAEI,KAAE,EAAE,IAAG,GAAGL,EAAC,GAAE,IAAES,EAAC;AAAE,SAAO,EAAEP,IAAED,IAAEI,EAAC,GAAEH;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,SAAO,EAAED,GAAE,QAAOC,GAAE,MAAM,KAAG,EAAED,GAAE,QAAOC,GAAE,MAAM,KAAG,EAAED,GAAE,QAAOC,GAAE,MAAM;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAE;AAAC,SAAOD,OAAI,KAAG,EAAEA,IAAE,CAAC,GAAE,EAAE,IAAGC,EAAC,GAAEQ,GAAE,IAAG,EAAE,GAAE,EAAE,EAAE,QAAOT,GAAE,QAAO,EAAE,GAAE,EAAE,EAAE,QAAOA,GAAE,QAAO,EAAE,GAAE,EAAE,EAAE,EAAE,KAAK,GAAE,EAAEA,GAAE,KAAK,GAAE,EAAE,GAAE,EAAE,EAAE,QAAOA,GAAE,QAAOC,EAAC,GAAE,EAAE,EAAE,OAAM,EAAE,OAAM,EAAE,MAAM,GAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAOF,OAAIE,MAAG,EAAEF,IAAEE,EAAC,GAAE,EAAE,IAAGD,IAAE,CAAC,GAAE,EAAEC,GAAE,QAAOF,GAAE,QAAO,EAAE,GAAE,EAAEE,GAAE,QAAOF,GAAE,QAAO,EAAE,GAAEG,GAAED,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,SAAO,EAAEA,GAAE,KAAK;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE,GAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAKC,GAAE;AAAE,MAAAG,GAAE,GAAEL,GAAE,MAAM,GAAE,EAAE,GAAE,CAAC;AAAE;AAAA,IAAM,KAAKE,GAAE;AAAE,MAAAG,GAAE,GAAEL,GAAE,MAAM,GAAE,EAAE,GAAE,CAAC;AAAE;AAAA,IAAM,KAAKE,GAAE;AAAE,MAAAG,GAAE,GAAE,GAAGL,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,EAAE,IAAI,GAAEA,IAAED,GAAE,MAAM,GAAEE,KAAE,EAAEF,GAAE,MAAM,GAAES,KAAE,EAAET,GAAE,MAAM,GAAEK,KAAE,EAAEL,GAAE,QAAO,CAAC,GAAEU,KAAE,EAAEV,GAAE,QAAO,CAAC;AAAE,SAAM,CAACK,KAAEH,KAAE,KAAGG,KAAEH,KAAE,KAAG,CAACQ,KAAED,KAAE,KAAGC,KAAED,KAAE;AAAC;AAAC,SAAS,GAAGT,IAAEC,IAAE;AAAC,QAAM,IAAE,GAAG,IAAI;AAAE,SAAOI,GAAE,EAAE,QAAOL,GAAE,MAAM,GAAEK,GAAE,EAAE,QAAOJ,EAAC,GAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAE;AAAC,QAAK,EAAC,QAAOC,IAAE,QAAOO,IAAE,QAAOJ,GAAC,IAAEL,IAAEU,KAAE,EAAE,EAAE,IAAI,GAAER,IAAED,GAAE,OAAO,CAAC,CAAC,GAAE,IAAE,EAAE,EAAE,IAAI,GAAEQ,IAAER,GAAE,OAAO,CAAC,CAAC;AAAE,IAAE,EAAE,QAAOS,IAAE,CAAC,GAAE,EAAE,EAAE,QAAO,EAAE,QAAOL,EAAC;AAAE,QAAMS,KAAE,EAAE,EAAE,IAAI,GAAEZ,IAAED,GAAE,UAAU,CAAC,CAAC,GAAEc,KAAE,EAAE,EAAE,IAAI,GAAEN,IAAER,GAAE,UAAU,CAAC,CAAC;AAAE,SAAO,EAAE,EAAE,QAAO,EAAEa,IAAEA,IAAEC,EAAC,GAAE,CAAC,GAAE;AAAC;AAAC,SAAS,GAAGf,IAAEC,IAAE;AAAC,OAAK,IAAI,EAAED,GAAE,QAAOA,GAAE,MAAM,KAAGA,GAAEA,GAAE,MAAM,IAAEA,GAAEA,GAAE,MAAM,EAAE,IAAE,QAAMH,GAAE,KAAKI,IAAE,8CAA8C,GAAE,KAAK,IAAI,EAAED,GAAE,QAAO,GAAGA,EAAC,CAAC,CAAC,IAAE,QAAMH,GAAE,KAAKI,IAAE,sDAAsD,GAAE,KAAK,IAAI,CAAC,EAAE,GAAGD,EAAC,GAAEA,GAAE,MAAM,IAAEA,GAAE,MAAM,CAAC,CAAC,IAAE,QAAMH,GAAE,KAAKI,IAAE,kDAAkD;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE,GAAEC,IAAE;AAAC,QAAMO,KAAE,GAAGT,EAAC;AAAE,EAAAM,GAAEG,IAAER,GAAE,WAAUA,GAAE,QAAO,CAAC,GAAEK,GAAE,EAAE,CAAC,GAAEG,IAAER,GAAE,QAAOC,EAAC;AAAC;AAAC,IAAM,KAAG,EAAC,OAAMH,GAAE,GAAE,QAAO,EAAE,GAAE,GAAE,CAAC,GAAE,QAAO,EAAE,GAAE,GAAE,CAAC,GAAE,QAAO,EAAE,GAAE,GAAE,CAAC,EAAC;AAAnE,IAAqE,KAAG,IAAIC,GAAED,EAAC;AAA/E,IAAiF,KAAG,IAAIC,GAAEgB,EAAC;AAA3F,IAA6F,KAAG,EAAE;AAAlG,IAAoG,KAAG,IAAIhB,GAAG,MAAI,EAAE,CAAE;AAAtH,IAAwH,KAAG,CAAC,EAAC,QAAO,CAAC,IAAG,EAAE,GAAE,WAAU,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,CAAC,GAAE,EAAE,GAAE,WAAU,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,CAAC,GAAE,CAAC,GAAE,WAAU,CAAC,IAAG,CAAC,EAAC,GAAE,EAAC,QAAO,CAAC,IAAG,CAAC,GAAE,WAAU,CAAC,GAAE,EAAE,EAAC,CAAC;AAA7P,IAA+P,KAAGU,GAAE;AAApQ,IAAsQ,KAAGA,GAAE;AAA3Q,IAA6Q,KAAG,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,mBAAkB,GAAE,IAAG,IAAG,YAAW,IAAG,QAAO,IAAG,cAAa,IAAG,0BAAyB,IAAG,MAAK,GAAE,mBAAkB,GAAE,QAAO,GAAE,UAAS,IAAG,WAAU,IAAG,sBAAqB,IAAG,SAAQ,GAAE,QAAO,IAAG,wBAAuB,IAAG,oBAAmB,GAAE,YAAWN,IAAE,cAAa,IAAG,+BAA8B,IAAG,QAAO,IAAG,cAAa,IAAG,mBAAkB,IAAG,QAAO,IAAG,eAAc,IAAG,WAAU,GAAE,WAAU,IAAG,sBAAqBD,IAAE,MAAK,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["L", "z", "p", "s", "t", "n", "J", "H", "r", "O", "F", "x", "o", "e", "c", "u", "A", "g", "b", "v"]}