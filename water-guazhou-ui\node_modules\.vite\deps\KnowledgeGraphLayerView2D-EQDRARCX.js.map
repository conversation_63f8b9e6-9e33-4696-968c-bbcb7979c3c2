{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/KnowledgeGraphLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import r from\"../../../core/Collection.js\";import{referenceSetter as t,castForReferenceSetter as s}from\"../../../core/collectionUtils.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import{LayerView2DMixin as a}from\"./LayerView2D.js\";import l from\"../../layers/LayerView.js\";let n=class extends(a(l)){constructor(e){super(e),this.layerViews=new r}set layerViews(e){this._set(\"layerViews\",t(e,this._get(\"layerViews\")))}get updatingProgress(){return 0===this.layerViews.length?1:this.layerViews.reduce(((e,r)=>e+r.updatingProgress),0)/this.layerViews.length}attach(){this._updateStageChildren(),this.addAttachHandles(this.layerViews.on(\"after-changes\",(()=>this._updateStageChildren())))}detach(){this.container.removeAllChildren()}update(e){}moveStart(){}viewChange(){}moveEnd(){}_updateStageChildren(){this.container.removeAllChildren(),this.layerViews.forEach(((e,r)=>this.container.addChildAt(e.container,r)))}};e([o({cast:s})],n.prototype,\"layerViews\",null),e([o({readOnly:!0})],n.prototype,\"updatingProgress\",null),n=e([i(\"esri.views.2d.layers.KnowledgeGraphLayerView2D\")],n);const c=n;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIghB,IAAIA,KAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,WAAWA,IAAE;AAAC,SAAK,KAAK,cAAa,EAAEA,IAAE,KAAK,KAAK,YAAY,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,MAAI,KAAK,WAAW,SAAO,IAAE,KAAK,WAAW,OAAQ,CAACA,IAAE,MAAIA,KAAE,EAAE,kBAAkB,CAAC,IAAE,KAAK,WAAW;AAAA,EAAM;AAAA,EAAC,SAAQ;AAAC,SAAK,qBAAqB,GAAE,KAAK,iBAAiB,KAAK,WAAW,GAAG,iBAAiB,MAAI,KAAK,qBAAqB,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,UAAU,kBAAkB;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,UAAU,kBAAkB,GAAE,KAAK,WAAW,QAAS,CAACA,IAAE,MAAI,KAAK,UAAU,WAAWA,GAAE,WAAU,CAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,gDAAgD,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["n", "e"]}