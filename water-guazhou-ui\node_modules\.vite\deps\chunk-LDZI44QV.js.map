{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/geojson/geojson.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/Error.js\";import{OptimizedFeature as t}from\"../../OptimizedFeature.js\";import n from\"../../OptimizedGeometry.js\";import{normalizeFieldName as o,isNumericField as r}from\"../../../support/fieldUtils.js\";const i={LineString:\"esriGeometryPolyline\",MultiLineString:\"esriGeometryPolyline\",MultiPoint:\"esriGeometryMultipoint\",Point:\"esriGeometryPoint\",Polygon:\"esriGeometryPolygon\",MultiPolygon:\"esriGeometryPolygon\"};function s(e){return i[e]}function*c(e){switch(e.type){case\"Feature\":yield e;break;case\"FeatureCollection\":for(const t of e.features)t&&(yield t)}}function*u(e){if(e)switch(e.type){case\"Point\":yield e.coordinates;break;case\"LineString\":case\"MultiPoint\":yield*e.coordinates;break;case\"MultiLineString\":case\"Polygon\":for(const t of e.coordinates)yield*t;break;case\"MultiPolygon\":for(const t of e.coordinates)for(const e of t)yield*e}}function*l(e,o={}){const{geometryType:r,objectIdField:i}=o;for(const c of e){const{geometry:e,properties:u,id:l}=c;if(e&&s(e.type)!==r)continue;const f=u||{};let a;i&&(a=f[i],null==l||a||(f[i]=a=l));const y=new t(e?g(new n,e,o):null,f,null,a??void 0);yield y}}function f(e){for(const t of e)if(t.length>2)return!0;return!1}function a(e){return!p(e)}function y(e){return p(e)}function p(e){let t=0;for(let n=0;n<e.length;n++){const o=e[n],r=e[(n+1)%e.length];t+=o[0]*r[1]-r[0]*o[1]}return t<=0}function d(e){const t=e[0],n=e[e.length-1];return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]||e.push(t),e}function g(e,t,n){switch(t.type){case\"LineString\":return m(e,t,n);case\"MultiLineString\":return h(e,t,n);case\"MultiPoint\":return w(e,t,n);case\"MultiPolygon\":return P(e,t,n);case\"Point\":return b(e,t,n);case\"Polygon\":return j(e,t,n)}}function m(e,t,n){return G(e,t.coordinates,n),e}function h(e,t,n){for(const o of t.coordinates)G(e,o,n);return e}function w(e,t,n){return G(e,t.coordinates,n),e}function P(e,t,n){for(const o of t.coordinates){S(e,o[0],n);for(let t=1;t<o.length;t++)F(e,o[t],n)}return e}function b(e,t,n){return k(e,t.coordinates,n),e}function j(e,t,n){const o=t.coordinates;S(e,o[0],n);for(let r=1;r<o.length;r++)F(e,o[r],n);return e}function S(e,t,n){const o=d(t);a(o)?M(e,o,n):G(e,o,n)}function F(e,t,n){const o=d(t);y(o)?M(e,o,n):G(e,o,n)}function G(e,t,n){for(const o of t)k(e,o,n);e.lengths.push(t.length)}function M(e,t,n){for(let o=t.length-1;o>=0;o--)k(e,t[o],n);e.lengths.push(t.length)}function k(e,t,n){const[o,r,i]=t;e.coords.push(o,r),n.hasZ&&e.coords.push(i||0)}function O(e){switch(typeof e){case\"string\":return\"esriFieldTypeString\";case\"number\":return\"esriFieldTypeDouble\";default:return\"unknown\"}}function T(t){if(!t)throw new e(\"geojson-layer:empty\",\"GeoJSON data is empty\");if(\"Feature\"!==t.type&&\"FeatureCollection\"!==t.type)throw new e(\"geojson-layer:unsupported-geojson-object\",\"missing or not supported GeoJSON object type\",{data:t});const{crs:n}=t;if(!n)return;const o=\"string\"==typeof n?n:\"name\"===n.type?n.properties.name:\"EPSG\"===n.type?n.properties.code:null,r=new RegExp(\".*(CRS84H?|4326)$\",\"i\");if(!o||!r.test(o))throw new e(\"geojson-layer:unsupported-crs\",\"unsupported GeoJSON 'crs' member\",{crs:n})}function L(e,t={}){const n=[],i=new Set,l=new Set;let a,y=!1,p=null,d=!1,{geometryType:g=null}=t,m=!1;for(const r of c(e)){const{geometry:e,properties:t,id:c}=r;if(!e||(g||(g=s(e.type)),s(e.type)===g)){if(!y){y=f(u(e))}if(d||(d=null!=c,d&&(a=typeof c,t&&(p=Object.keys(t).filter((e=>t[e]===c))))),t&&p&&d&&null!=c&&(p.length>1?p=p.filter((e=>t[e]===c)):1===p.length&&(p=t[p[0]]===c?p:[])),!m&&t){let e=!0;for(const r in t){if(i.has(r))continue;const s=t[r];if(null==s){e=!1,l.add(r);continue}const c=O(s);if(\"unknown\"===c){l.add(r);continue}l.delete(r),i.add(r);const u=o(r);u&&n.push({name:u,alias:r,type:c})}m=e}}}const h=o(1===p?.length&&p[0]||null)??void 0;if(h)for(const o of n)if(o.name===h&&r(o)){o.type=\"esriFieldTypeOID\";break}return{fields:n,geometryType:g,hasZ:y,objectIdFieldName:h,objectIdFieldType:a,unknownFields:Array.from(l)}}function I(e,t){return Array.from(l(c(e),t))}export{I as createOptimizedFeatures,s as getGeometryType,L as inferLayerProperties,T as validateGeoJSON};\n"], "mappings": ";;;;;;;;;;;;;AAIwO,IAAM,IAAE,EAAC,YAAW,wBAAuB,iBAAgB,wBAAuB,YAAW,0BAAyB,OAAM,qBAAoB,SAAQ,uBAAsB,cAAa,sBAAqB;AAAE,SAASA,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC;AAAC;AAAC,UAAS,EAAE,GAAE;AAAC,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAU,YAAM;AAAE;AAAA,IAAM,KAAI;AAAoB,iBAAUC,MAAK,EAAE,SAAS,CAAAA,OAAI,MAAMA;AAAA,EAAE;AAAC;AAAC,UAAS,EAAE,GAAE;AAAC,MAAG,EAAE,SAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,YAAM,EAAE;AAAY;AAAA,IAAM,KAAI;AAAA,IAAa,KAAI;AAAa,aAAM,EAAE;AAAY;AAAA,IAAM,KAAI;AAAA,IAAkB,KAAI;AAAU,iBAAUA,MAAK,EAAE,YAAY,QAAMA;AAAE;AAAA,IAAM,KAAI;AAAe,iBAAUA,MAAK,EAAE,YAAY,YAAUC,MAAKD,GAAE,QAAMC;AAAA,EAAC;AAAC;AAAC,UAAS,EAAE,GAAE,IAAE,CAAC,GAAE;AAAC,QAAK,EAAC,cAAa,GAAE,eAAcC,GAAC,IAAE;AAAE,aAAUC,MAAK,GAAE;AAAC,UAAK,EAAC,UAASF,IAAE,YAAWG,IAAE,IAAGC,GAAC,IAAEF;AAAE,QAAGF,MAAGF,GAAEE,GAAE,IAAI,MAAI,EAAE;AAAS,UAAMK,KAAEF,MAAG,CAAC;AAAE,QAAIG;AAAE,IAAAL,OAAIK,KAAED,GAAEJ,EAAC,GAAE,QAAMG,MAAGE,OAAID,GAAEJ,EAAC,IAAEK,KAAEF;AAAI,UAAMG,KAAE,IAAIT,GAAEE,KAAE,EAAE,IAAI,KAAEA,IAAE,CAAC,IAAE,MAAKK,IAAE,MAAKC,MAAG,MAAM;AAAE,UAAMC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,aAAUR,MAAK,EAAE,KAAGA,GAAE,SAAO,EAAE,QAAM;AAAG,SAAM;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,CAACS,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAOA,GAAE,CAAC;AAAC;AAAC,SAASA,GAAE,GAAE;AAAC,MAAIT,KAAE;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,UAAM,IAAE,EAAE,CAAC,GAAE,IAAE,GAAG,IAAE,KAAG,EAAE,MAAM;AAAE,IAAAA,MAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,CAAC;AAAA,EAAC;AAAC,SAAOA,MAAG;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAMA,KAAE,EAAE,CAAC,GAAE,IAAE,EAAE,EAAE,SAAO,CAAC;AAAE,SAAOA,GAAE,CAAC,MAAI,EAAE,CAAC,KAAGA,GAAE,CAAC,MAAI,EAAE,CAAC,KAAGA,GAAE,CAAC,MAAI,EAAE,CAAC,KAAG,EAAE,KAAKA,EAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAa,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,IAAE,KAAI;AAAkB,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,IAAE,KAAI;AAAa,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,IAAE,KAAI;AAAe,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,IAAE,KAAI;AAAQ,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,IAAE,KAAI;AAAU,aAAO,EAAE,GAAEA,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAE,GAAEA,GAAE,aAAY,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,aAAU,KAAKA,GAAE,YAAY,GAAE,GAAE,GAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAE,GAAEA,GAAE,aAAY,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,aAAU,KAAKA,GAAE,aAAY;AAAC,MAAE,GAAE,EAAE,CAAC,GAAE,CAAC;AAAE,aAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,GAAE,GAAE,EAAEA,EAAC,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,SAAO,EAAE,GAAEA,GAAE,aAAY,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAM,IAAEA,GAAE;AAAY,IAAE,GAAE,EAAE,CAAC,GAAE,CAAC;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,GAAE,EAAE,CAAC,GAAE,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,EAAC;AAAE,IAAE,CAAC,IAAE,EAAE,GAAE,GAAE,CAAC,IAAE,EAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAM,IAAE,EAAEA,EAAC;AAAE,IAAE,CAAC,IAAE,EAAE,GAAE,GAAE,CAAC,IAAE,EAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,aAAU,KAAKA,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,IAAE,QAAQ,KAAKA,GAAE,MAAM;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,WAAQ,IAAEA,GAAE,SAAO,GAAE,KAAG,GAAE,IAAI,GAAE,GAAEA,GAAE,CAAC,GAAE,CAAC;AAAE,IAAE,QAAQ,KAAKA,GAAE,MAAM;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,QAAK,CAAC,GAAE,GAAEE,EAAC,IAAEF;AAAE,IAAE,OAAO,KAAK,GAAE,CAAC,GAAE,EAAE,QAAM,EAAE,OAAO,KAAKE,MAAG,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,OAAO,GAAE;AAAA,IAAC,KAAI;AAAS,aAAM;AAAA,IAAsB,KAAI;AAAS,aAAM;AAAA,IAAsB;AAAQ,aAAM;AAAA,EAAS;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,MAAG,CAACA,GAAE,OAAM,IAAI,EAAE,uBAAsB,uBAAuB;AAAE,MAAG,cAAYA,GAAE,QAAM,wBAAsBA,GAAE,KAAK,OAAM,IAAI,EAAE,4CAA2C,gDAA+C,EAAC,MAAKA,GAAC,CAAC;AAAE,QAAK,EAAC,KAAI,EAAC,IAAEA;AAAE,MAAG,CAAC,EAAE;AAAO,QAAM,IAAE,YAAU,OAAO,IAAE,IAAE,WAAS,EAAE,OAAK,EAAE,WAAW,OAAK,WAAS,EAAE,OAAK,EAAE,WAAW,OAAK,MAAK,IAAE,IAAI,OAAO,qBAAoB,GAAG;AAAE,MAAG,CAAC,KAAG,CAAC,EAAE,KAAK,CAAC,EAAE,OAAM,IAAI,EAAE,iCAAgC,oCAAmC,EAAC,KAAI,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,KAAE,CAAC,GAAE;AAAC,QAAM,IAAE,CAAC,GAAEE,KAAE,oBAAI,OAAIG,KAAE,oBAAI;AAAI,MAAIE,IAAEC,KAAE,OAAGC,KAAE,MAAKC,KAAE,OAAG,EAAC,cAAaC,KAAE,KAAI,IAAEX,IAAEY,KAAE;AAAG,aAAU,KAAK,EAAE,CAAC,GAAE;AAAC,UAAK,EAAC,UAASX,IAAE,YAAWD,IAAE,IAAGG,GAAC,IAAE;AAAE,QAAG,CAACF,OAAIU,OAAIA,KAAEZ,GAAEE,GAAE,IAAI,IAAGF,GAAEE,GAAE,IAAI,MAAIU,KAAG;AAAC,UAAG,CAACH,IAAE;AAAC,QAAAA,KAAE,EAAE,EAAEP,EAAC,CAAC;AAAA,MAAC;AAAC,UAAGS,OAAIA,KAAE,QAAMP,IAAEO,OAAIH,KAAE,OAAOJ,IAAEH,OAAIS,KAAE,OAAO,KAAKT,EAAC,EAAE,OAAQ,CAAAC,OAAGD,GAAEC,EAAC,MAAIE,EAAE,MAAKH,MAAGS,MAAGC,MAAG,QAAMP,OAAIM,GAAE,SAAO,IAAEA,KAAEA,GAAE,OAAQ,CAAAR,OAAGD,GAAEC,EAAC,MAAIE,EAAE,IAAE,MAAIM,GAAE,WAASA,KAAET,GAAES,GAAE,CAAC,CAAC,MAAIN,KAAEM,KAAE,CAAC,KAAI,CAACG,MAAGZ,IAAE;AAAC,YAAIC,KAAE;AAAG,mBAAUY,MAAKb,IAAE;AAAC,cAAGE,GAAE,IAAIW,EAAC,EAAE;AAAS,gBAAMd,KAAEC,GAAEa,EAAC;AAAE,cAAG,QAAMd,IAAE;AAAC,YAAAE,KAAE,OAAGI,GAAE,IAAIQ,EAAC;AAAE;AAAA,UAAQ;AAAC,gBAAMV,KAAE,EAAEJ,EAAC;AAAE,cAAG,cAAYI,IAAE;AAAC,YAAAE,GAAE,IAAIQ,EAAC;AAAE;AAAA,UAAQ;AAAC,UAAAR,GAAE,OAAOQ,EAAC,GAAEX,GAAE,IAAIW,EAAC;AAAE,gBAAMT,KAAE,EAAES,EAAC;AAAE,UAAAT,MAAG,EAAE,KAAK,EAAC,MAAKA,IAAE,OAAMS,IAAE,MAAKV,GAAC,CAAC;AAAA,QAAC;AAAC,QAAAS,KAAEX;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC,QAAMa,KAAE,EAAE,OAAIL,MAAA,gBAAAA,GAAG,WAAQA,GAAE,CAAC,KAAG,IAAI,KAAG;AAAO,MAAGK;AAAE,eAAU,KAAK,EAAE,KAAG,EAAE,SAAOA,MAAG,GAAE,CAAC,GAAE;AAAC,QAAE,OAAK;AAAmB;AAAA,IAAK;AAAA;AAAC,SAAM,EAAC,QAAO,GAAE,cAAaH,IAAE,MAAKH,IAAE,mBAAkBM,IAAE,mBAAkBP,IAAE,eAAc,MAAM,KAAKF,EAAC,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEL,IAAE;AAAC,SAAO,MAAM,KAAK,EAAE,EAAE,CAAC,GAAEA,EAAC,CAAC;AAAC;", "names": ["s", "t", "e", "i", "c", "u", "l", "f", "a", "y", "p", "d", "g", "m", "r", "h"]}