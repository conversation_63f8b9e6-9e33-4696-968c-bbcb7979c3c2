{"version": 3, "sources": ["../../@arcgis/core/chunks/sv_SE.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as r}from\"./_commonjs-dynamic-modules.js\";function a(e,r){for(var a=0;a<r.length;a++){const t=r[a];if(\"string\"!=typeof t&&!Array.isArray(t))for(const r in t)if(\"default\"!==r&&!(r in e)){const a=Object.getOwnPropertyDescriptor(t,r);a&&Object.defineProperty(e,r,a.get?a:{enumerable:!0,get:()=>t[r]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var t,o,n={},i={get exports(){return n},set exports(e){n=e}};t=i,void 0!==(o=function(e,r){Object.defineProperty(r,\"__esModule\",{value:!0}),r.default={_decimalSeparator:\",\",_thousandSeparator:\" \",_percentPrefix:null,_percentSuffix:\"%\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"yyyy-MM-dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_second:\"ss\",_duration_minute:\"mm\",_duration_hour:\"hh\",_duration_day:\"dd\",_duration_week:\"ww\",_duration_month:\"MM\",_duration_year:\"yyyy\",_era_ad:\"e.Kr.\",_era_bc:\"f.Kr.\",A:\"fm\",P:\"em\",AM:\"fm\",PM:\"em\",\"A.M.\":\"f.m.\",\"P.M.\":\"e.m.\",January:\"januari\",February:\"februari\",March:\"mars\",April:\"april\",May:\"maj\",June:\"juni\",July:\"juli\",August:\"augusti\",September:\"september\",October:\"oktober\",November:\"november\",December:\"december\",Jan:\"jan.\",Feb:\"feb.\",Mar:\"mars\",Apr:\"apr.\",\"May(short)\":\"maj\",Jun:\"juni\",Jul:\"juli\",Aug:\"aug.\",Sep:\"sep.\",Oct:\"okt.\",Nov:\"nov.\",Dec:\"dec.\",Sunday:\"söndag\",Monday:\"måndag\",Tuesday:\"tisdag\",Wednesday:\"onsdag\",Thursday:\"torsdag\",Friday:\"fredag\",Saturday:\"lördag\",Sun:\"sön\",Mon:\"mån\",Tue:\"tis\",Wed:\"ons\",Thu:\"tor\",Fri:\"fre\",Sat:\"lör\",_dateOrd:function(e){return\"\"},\"Zoom Out\":\"Zooma ut\",Play:\"Spela\",Stop:\"Stoppa\",Legend:\"Teckenförklaring\",\"Click, tap or press ENTER to toggle\":\"Klicka eller tryck ENTER för att ändra\",Loading:\"Läser in\",Home:\"Hem\",Chart:\"Diagram\",\"Serial chart\":\"Seriediagram\",\"X/Y chart\":\"XY-diagram\",\"Pie chart\":\"Tårtdiagram\",\"Gauge chart\":\"Instrumentdiagram\",\"Radar chart\":\"Radardiagram\",\"Sankey diagram\":\"Sankeydiagram\",\"Chord diagram\":\"Strängdiagram\",\"Flow diagram\":\"Flödesschema\",\"TreeMap chart\":\"Träddiagram \",Series:\"Serier\",\"Candlestick Series\":\"Candlestick-serier\",\"Column Series\":\"Kolumnserier\",\"Line Series\":\"Linjeserier\",\"Pie Slice Series\":\"Tårtserier\",\"X/Y Series\":\"X/Y-serier\",Map:\"Karta\",\"Press ENTER to zoom in\":\"Tryck RETUR för att zooma in\",\"Press ENTER to zoom out\":\"Tryck RETUR för att zooma ut\",\"Use arrow keys to zoom in and out\":\"Använd pil-knapparna för att zooma in och ut\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Använd plus- och minus-knapparna för att zooma in och ut\",Export:\"Exportera\",Image:\"Bild\",Data:\"Data\",Print:\"Skriv ut\",\"Click, tap or press ENTER to open\":\"Klicka eller tryck ENTER för att öppna\",\"Click, tap or press ENTER to print.\":\"Klicka eller tryck ENTER för att skriva ut.\",\"Click, tap or press ENTER to export as %1.\":\"Klicka eller tryck ENTER för att exportera till %1.\",'To save the image, right-click this link and choose \"Save picture as...\"':'För att spara bilden, höger-klicka länken och välj \"Spara bild som...\"','To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':'För att spara bilden, höger-klicka miniatyrbilden till vänster och välj \"Spara bild som...\"',\"(Press ESC to close this message)\":\"(Tryck ESC för att stänga)\",\"Image Export Complete\":\"Bildexport klar\",\"Export operation took longer than expected. Something might have gone wrong.\":\"\",\"Saved from\":\"Sparad från\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"\",\"Use left and right arrows to move selection\":\"Använd vänster och höger pilknappar för att flytta urvalet\",\"Use left and right arrows to move left selection\":\"Använd vänster och höger pilknappar för att flytta vänsterurval\",\"Use left and right arrows to move right selection\":\"Använd vänster och höger pilknappar för att flytta högerurval\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"\",\"Use up and down arrows to move selection\":\"Använd upp och ner pilknappar för att flytta urvalet\",\"Use up and down arrows to move lower selection\":\"Använd upp och ner pilknappar för att flytta nedre urvalet\",\"Use up and down arrows to move upper selection\":\"Använd upp och ner pilknappar för att flytta övre urvalet\",\"From %1 to %2\":\"Från %1 till %2\",\"From %1\":\"Från %1\",\"To %1\":\"Till %1\",\"No parser available for file: %1\":\"\",\"Error parsing file: %1\":\"\",\"Unable to load file: %1\":\"\",\"Invalid date\":\"Ogiltigt datum\"}}(r,n))&&(t.exports=o);const s=a({__proto__:null,default:e(n)},[n]);export{s};\n"], "mappings": ";;;;;;;;;AAI6F,SAAS,EAAE,GAAEA,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUF,MAAKE,GAAE,KAAG,cAAYF,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMC,KAAE,OAAO,yBAAyBC,IAAEF,EAAC;AAAE,QAAAC,MAAG,OAAO,eAAe,GAAED,IAAEC,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEF,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAMG;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAE,IAAE,GAAE,YAAUA,KAAE,SAAS,GAAEH,IAAE;AAAC,SAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,cAAa,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,kBAAiB,MAAK,kBAAiB,MAAK,gBAAe,MAAK,eAAc,MAAK,gBAAe,MAAK,iBAAgB,MAAK,gBAAe,QAAO,SAAQ,SAAQ,SAAQ,SAAQ,GAAE,MAAK,GAAE,MAAK,IAAG,MAAK,IAAG,MAAK,QAAO,QAAO,QAAO,QAAO,SAAQ,WAAU,UAAS,YAAW,OAAM,QAAO,OAAM,SAAQ,KAAI,OAAM,MAAK,QAAO,MAAK,QAAO,QAAO,WAAU,WAAU,aAAY,SAAQ,WAAU,UAAS,YAAW,UAAS,YAAW,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,cAAa,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,QAAO,UAAS,QAAO,UAAS,SAAQ,UAAS,WAAU,UAAS,UAAS,WAAU,QAAO,UAAS,UAAS,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,UAAS,SAASI,IAAE;AAAC,WAAM;AAAA,EAAE,GAAE,YAAW,YAAW,MAAK,SAAQ,MAAK,UAAS,QAAO,oBAAmB,uCAAsC,0CAAyC,SAAQ,YAAW,MAAK,OAAM,OAAM,WAAU,gBAAe,gBAAe,aAAY,cAAa,aAAY,eAAc,eAAc,qBAAoB,eAAc,gBAAe,kBAAiB,iBAAgB,iBAAgB,iBAAgB,gBAAe,gBAAe,iBAAgB,gBAAe,QAAO,UAAS,sBAAqB,sBAAqB,iBAAgB,gBAAe,eAAc,eAAc,oBAAmB,cAAa,cAAa,cAAa,KAAI,SAAQ,0BAAyB,gCAA+B,2BAA0B,gCAA+B,qCAAoC,gDAA+C,+DAA8D,4DAA2D,QAAO,aAAY,OAAM,QAAO,MAAK,QAAO,OAAM,YAAW,qCAAoC,0CAAyC,uCAAsC,+CAA8C,8CAA6C,uDAAsD,4EAA2E,0EAAyE,wFAAuF,+FAA8F,qCAAoC,8BAA6B,yBAAwB,mBAAkB,gFAA+E,IAAG,cAAa,eAAc,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,IAAG,+CAA8C,8DAA6D,oDAAmD,mEAAkE,qDAAoD,iEAAgE,yEAAwE,IAAG,4CAA2C,wDAAuD,kDAAiD,8DAA6D,kDAAiD,6DAA4D,iBAAgB,mBAAkB,WAAU,WAAU,SAAQ,WAAU,oCAAmC,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,gBAAe,iBAAgB;AAAC,EAAE,GAAE,CAAC,OAAK,EAAE,UAAQD;AAAG,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["r", "a", "t", "o", "e"]}