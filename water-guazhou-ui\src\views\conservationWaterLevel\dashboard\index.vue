<template>
  <div class="conservation-dashboard">
    <!-- 标题栏 -->
    <div class="dashboard-header">
      <h1>地下水涵养水位智能分析大屏</h1>
      <div class="header-info">
        <span>更新时间：{{ currentTime }}</span>
        <el-button type="primary" icon="Refresh" @click="refreshData">刷新数据</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><DataLine /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalStations }}</div>
          <div class="stat-label">监测测点</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalAnalysis }}</div>
          <div class="stat-label">分析次数</div>
        </div>
      </div>
      
      <div class="stat-card risk-low">
        <div class="stat-icon">
          <el-icon><SuccessFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ riskStats.low || 0 }}</div>
          <div class="stat-label">低风险区域</div>
        </div>
      </div>
      
      <div class="stat-card risk-medium">
        <div class="stat-icon">
          <el-icon><WarningFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ riskStats.medium || 0 }}</div>
          <div class="stat-label">中风险区域</div>
        </div>
      </div>
      
      <div class="stat-card risk-high">
        <div class="stat-icon">
          <el-icon><CircleCloseFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ riskStats.high || 0 }}</div>
          <div class="stat-label">高风险区域</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 第一行图表 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>水位变化趋势</h3>
            <el-select v-model="selectedStation" @change="updateWaterLevelChart" style="width: 200px">
              <el-option
                v-for="station in stationList"
                :key="station.id"
                :label="station.name"
                :value="station.id"
              />
            </el-select>
          </div>
          <div ref="waterLevelChartRef" class="chart-container"></div>
        </div>
        
        <div class="chart-card">
          <div class="chart-header">
            <h3>风险等级分布</h3>
          </div>
          <div ref="riskChartRef" class="chart-container"></div>
        </div>
      </div>

      <!-- 第二行图表 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>涵养潜力分析</h3>
          </div>
          <div ref="potentialChartRef" class="chart-container"></div>
        </div>
        
        <div class="chart-card">
          <div class="chart-header">
            <h3>环境因素影响</h3>
          </div>
          <div ref="environmentChartRef" class="chart-container"></div>
        </div>
      </div>

      <!-- 第三行图表 -->
      <div class="chart-row">
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>测点分布地图</h3>
          </div>
          <div ref="mapChartRef" class="chart-container map-container"></div>
        </div>
      </div>
    </div>

    <!-- 实时数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <h3>实时监测数据</h3>
      </div>
      <el-table
        :data="realtimeData"
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#1f2937', color: '#fff' }"
      >
        <el-table-column prop="stationName" label="测点名称" width="120" />
        <el-table-column prop="rawWaterLevel" label="原水液位(m)" width="120" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.rawWaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="groundwaterLevel" label="地下水位(m)" width="120" align="center">
          <template #default="scope">
            <span class="level-value">{{ formatNumber(scope.row.groundwaterLevel) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="levelChange" label="液位变化(m)" width="120" align="center">
          <template #default="scope">
            <span :class="getLevelChangeClass(scope.row.levelChange)">
              {{ formatNumber(scope.row.levelChange) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="conservationPotential" label="涵养潜力" width="150" align="center">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.conservationPotential"
              :color="getPotentialColor(scope.row.conservationPotential)"
              :show-text="false"
              style="width: 80px"
            />
            <span style="margin-left: 8px; color: #fff">{{ formatNumber(scope.row.conservationPotential) }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
              {{ getRiskLevelText(scope.row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recordTime" label="更新时间" width="160" align="center">
          <template #default="scope">
            <span style="color: #fff">{{ formatDateTime(scope.row.recordTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'online' ? 'success' : 'danger'">
              {{ scope.row.status === 'online' ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { DataLine, DataAnalysis, SuccessFilled, WarningFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getWaterLevelChangeData,
  getRiskLevelStatistics,
  getConservationPotentialTrend,
  getLatestWaterLevel
} from '@/api/conservationWaterLevel'
import { formatDateTime, formatNumber } from '@/utils/format'

// 响应式数据
const currentTime = ref('')
const totalStations = ref(0)
const totalAnalysis = ref(0)
const riskStats = ref<any>({})
const selectedStation = ref('')
const stationList = ref<any[]>([])
const realtimeData = ref<any[]>([])

// 图表引用
const waterLevelChartRef = ref<HTMLDivElement>()
const riskChartRef = ref<HTMLDivElement>()
const potentialChartRef = ref<HTMLDivElement>()
const environmentChartRef = ref<HTMLDivElement>()
const mapChartRef = ref<HTMLDivElement>()

// 图表实例
let waterLevelChart: echarts.ECharts | null = null
let riskChart: echarts.ECharts | null = null
let potentialChart: echarts.ECharts | null = null
let environmentChart: echarts.ECharts | null = null
let mapChart: echarts.ECharts | null = null

// 定时器
let timer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  initDashboard()
  startTimer()
})

onUnmounted(() => {
  stopTimer()
  disposeCharts()
})

// 初始化大屏
const initDashboard = async () => {
  updateCurrentTime()
  await loadStationList()
  await loadStatistics()
  await loadRealtimeData()
  
  nextTick(() => {
    initCharts()
  })
}

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = formatDateTime(new Date())
}

// 开始定时器
const startTimer = () => {
  timer = setInterval(() => {
    updateCurrentTime()
    loadRealtimeData()
  }, 30000) // 30秒更新一次
}

// 停止定时器
const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 销毁图表
const disposeCharts = () => {
  if (waterLevelChart) {
    waterLevelChart.dispose()
    waterLevelChart = null
  }
  if (riskChart) {
    riskChart.dispose()
    riskChart = null
  }
  if (potentialChart) {
    potentialChart.dispose()
    potentialChart = null
  }
  if (environmentChart) {
    environmentChart.dispose()
    environmentChart = null
  }
  if (mapChart) {
    mapChart.dispose()
    mapChart = null
  }
}

// 加载测点列表
const loadStationList = async () => {
  try {
    // 临时模拟数据
    stationList.value = [
      { id: '1', name: '测点1', location: '位置1', lat: 39.9042, lng: 116.4074 },
      { id: '2', name: '测点2', location: '位置2', lat: 39.9142, lng: 116.4174 },
      { id: '3', name: '测点3', location: '位置3', lat: 39.8942, lng: 116.3974 }
    ]
    totalStations.value = stationList.value.length
    if (stationList.value.length > 0) {
      selectedStation.value = stationList.value[0].id
    }
  } catch (error) {
    console.error('加载测点列表失败:', error)
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await getRiskLevelStatistics()
    if (response.code === 200) {
      const stats = response.data.reduce((acc: any, item: any) => {
        if (item.riskLevel === 1) acc.low = item.count
        if (item.riskLevel === 2) acc.medium = item.count
        if (item.riskLevel === 3) acc.high = item.count
        return acc
      }, {})
      riskStats.value = stats
      totalAnalysis.value = response.data.reduce((sum: number, item: any) => sum + item.count, 0)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载实时数据
const loadRealtimeData = async () => {
  try {
    // 模拟实时数据
    realtimeData.value = stationList.value.map(station => ({
      stationName: station.name,
      rawWaterLevel: (Math.random() * 10 + 5).toFixed(3),
      groundwaterLevel: (Math.random() * 8 + 3).toFixed(3),
      levelChange: (Math.random() * 2 - 1).toFixed(3),
      conservationPotential: Math.floor(Math.random() * 100),
      riskLevel: Math.floor(Math.random() * 3) + 1,
      recordTime: new Date(),
      status: Math.random() > 0.1 ? 'online' : 'offline'
    }))
  } catch (error) {
    console.error('加载实时数据失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  initWaterLevelChart()
  initRiskChart()
  initPotentialChart()
  initEnvironmentChart()
  initMapChart()
}

// 初始化水位变化趋势图
const initWaterLevelChart = () => {
  if (!waterLevelChartRef.value) return

  waterLevelChart = echarts.init(waterLevelChartRef.value, 'dark')
  updateWaterLevelChart()
}

// 更新水位变化趋势图
const updateWaterLevelChart = async () => {
  if (!waterLevelChart || !selectedStation.value) return

  try {
    // 模拟数据
    const data = Array.from({ length: 24 }, (_, i) => {
      const time = new Date()
      time.setHours(i)
      return {
        time: time.getTime(),
        rawWaterLevel: Math.random() * 2 + 8,
        groundwaterLevel: Math.random() * 2 + 6,
        levelChange: Math.random() * 0.5 - 0.25
      }
    })

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['原水液位', '地下水位', '液位变化'],
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        axisLine: { lineStyle: { color: '#333' } },
        axisLabel: { color: '#999' }
      },
      yAxis: [
        {
          type: 'value',
          name: '水位(m)',
          axisLine: { lineStyle: { color: '#333' } },
          axisLabel: { color: '#999' },
          nameTextStyle: { color: '#fff' }
        },
        {
          type: 'value',
          name: '变化量(m)',
          position: 'right',
          axisLine: { lineStyle: { color: '#333' } },
          axisLabel: { color: '#999' },
          nameTextStyle: { color: '#fff' }
        }
      ],
      series: [
        {
          name: '原水液位',
          type: 'line',
          data: data.map(item => [item.time, item.rawWaterLevel]),
          smooth: true,
          lineStyle: { color: '#409EFF', width: 2 },
          areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
        },
        {
          name: '地下水位',
          type: 'line',
          data: data.map(item => [item.time, item.groundwaterLevel]),
          smooth: true,
          lineStyle: { color: '#67C23A', width: 2 },
          areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
        },
        {
          name: '液位变化',
          type: 'line',
          yAxisIndex: 1,
          data: data.map(item => [item.time, item.levelChange]),
          smooth: true,
          lineStyle: { color: '#E6A23C', width: 2 }
        }
      ]
    }

    waterLevelChart.setOption(option)
  } catch (error) {
    console.error('更新水位趋势图失败:', error)
  }
}

// 初始化风险等级分布图
const initRiskChart = () => {
  if (!riskChartRef.value) return

  riskChart = echarts.init(riskChartRef.value, 'dark')

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: { color: '#fff' }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: { color: '#fff' }
    },
    series: [
      {
        name: '风险等级',
        type: 'pie',
        radius: '50%',
        data: [
          { value: riskStats.value.low || 0, name: '低风险', itemStyle: { color: '#67C23A' } },
          { value: riskStats.value.medium || 0, name: '中风险', itemStyle: { color: '#E6A23C' } },
          { value: riskStats.value.high || 0, name: '高风险', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  riskChart.setOption(option)
}

// 初始化涵养潜力分析图
const initPotentialChart = () => {
  if (!potentialChartRef.value) return

  potentialChart = echarts.init(potentialChartRef.value, 'dark')

  // 模拟数据
  const data = stationList.value.map(station => ({
    name: station.name,
    potential: Math.floor(Math.random() * 100)
  }))

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: { color: '#fff' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLine: { lineStyle: { color: '#333' } },
      axisLabel: { color: '#999' }
    },
    yAxis: {
      type: 'value',
      name: '涵养潜力(%)',
      axisLine: { lineStyle: { color: '#333' } },
      axisLabel: { color: '#999' },
      nameTextStyle: { color: '#fff' }
    },
    series: [
      {
        name: '涵养潜力',
        type: 'bar',
        data: data.map(item => ({
          value: item.potential,
          itemStyle: {
            color: item.potential >= 80 ? '#67C23A' :
                   item.potential >= 60 ? '#E6A23C' : '#F56C6C'
          }
        })),
        barWidth: '60%'
      }
    ]
  }

  potentialChart.setOption(option)
}

// 初始化环境因素影响图
const initEnvironmentChart = () => {
  if (!environmentChartRef.value) return

  environmentChart = echarts.init(environmentChartRef.value, 'dark')

  // 模拟数据
  const categories = ['降雨量', '蒸发量', '开采量', '径流量']
  const data = [
    { name: '测点1', values: [45, 25, 30, 20] },
    { name: '测点2', values: [38, 22, 35, 18] },
    { name: '测点3', values: [52, 28, 25, 22] }
  ]

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: data.map(item => item.name),
      textStyle: { color: '#fff' }
    },
    radar: {
      indicator: categories.map(name => ({ name, max: 100 })),
      axisName: { color: '#fff' },
      splitLine: { lineStyle: { color: '#333' } },
      splitArea: { show: false },
      axisLine: { lineStyle: { color: '#333' } }
    },
    series: [
      {
        name: '环境因素',
        type: 'radar',
        data: data.map((item, index) => ({
          value: item.values,
          name: item.name,
          areaStyle: { opacity: 0.3 },
          lineStyle: {
            color: ['#409EFF', '#67C23A', '#E6A23C'][index],
            width: 2
          }
        }))
      }
    ]
  }

  environmentChart.setOption(option)
}

// 初始化地图
const initMapChart = () => {
  if (!mapChartRef.value) return

  mapChart = echarts.init(mapChartRef.value, 'dark')

  // 模拟地图数据
  const mapData = stationList.value.map(station => ({
    name: station.name,
    value: [station.lng, station.lat, Math.floor(Math.random() * 100)],
    riskLevel: Math.floor(Math.random() * 3) + 1
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: { color: '#fff' },
      formatter: (params: any) => {
        return `${params.name}<br/>涵养潜力: ${params.value[2]}%`
      }
    },
    geo: {
      map: 'china',
      roam: true,
      zoom: 1.2,
      center: [116.4074, 39.9042],
      itemStyle: {
        areaColor: '#1e293b',
        borderColor: '#334155'
      },
      emphasis: {
        itemStyle: {
          areaColor: '#374151'
        }
      }
    },
    series: [
      {
        name: '测点分布',
        type: 'scatter',
        coordinateSystem: 'geo',
        data: mapData,
        symbolSize: (val: number[]) => Math.max(val[2] / 5, 10),
        itemStyle: {
          color: (params: any) => {
            const riskLevel = mapData[params.dataIndex].riskLevel
            return riskLevel === 1 ? '#67C23A' :
                   riskLevel === 2 ? '#E6A23C' : '#F56C6C'
          }
        },
        emphasis: {
          scale: true,
          scaleSize: 20
        }
      }
    ]
  }

  mapChart.setOption(option)
}

// 刷新数据
const refreshData = async () => {
  try {
    await loadStatistics()
    await loadRealtimeData()

    // 更新图表
    if (riskChart) {
      const option = riskChart.getOption()
      option.series[0].data = [
        { value: riskStats.value.low || 0, name: '低风险', itemStyle: { color: '#67C23A' } },
        { value: riskStats.value.medium || 0, name: '中风险', itemStyle: { color: '#E6A23C' } },
        { value: riskStats.value.high || 0, name: '高风险', itemStyle: { color: '#F56C6C' } }
      ]
      riskChart.setOption(option)
    }

    updateWaterLevelChart()

    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  }
}

// 工具函数
const getLevelChangeClass = (levelChange: number | undefined) => {
  if (!levelChange) return ''
  if (levelChange > 0) return 'level-increase'
  if (levelChange < 0) return 'level-decrease'
  return ''
}

const getPotentialColor = (potential: number | undefined) => {
  if (!potential) return '#e6e6e6'
  if (potential >= 80) return '#67c23a'
  if (potential >= 60) return '#e6a23c'
  if (potential >= 40) return '#f56c6c'
  return '#909399'
}

const getRiskLevelType = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return 'success'
    case 2: return 'warning'
    case 3: return 'danger'
    default: return 'info'
  }
}

const getRiskLevelText = (riskLevel: number | undefined) => {
  switch (riskLevel) {
    case 1: return '低风险'
    case 2: return '中风险'
    case 3: return '高风险'
    default: return '未知'
  }
}
</script>

<style scoped>
.conservation-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #fff;
  padding: 20px;
  overflow-x: auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.dashboard-header h1 {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(45deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
  color: #94a3b8;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-card.risk-low {
  border-color: rgba(103, 194, 58, 0.3);
  background: rgba(103, 194, 58, 0.1);
}

.stat-card.risk-medium {
  border-color: rgba(230, 162, 60, 0.3);
  background: rgba(230, 162, 60, 0.1);
}

.stat-card.risk-high {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.1);
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-row:last-child {
  grid-template-columns: 1fr;
}

.chart-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.chart-card.full-width {
  grid-column: 1 / -1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.map-container {
  height: 400px;
}

.table-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.table-header {
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.level-value {
  font-weight: 600;
  color: #fff;
}

.level-increase {
  color: #67c23a;
  font-weight: 600;
}

.level-decrease {
  color: #f56c6c;
  font-weight: 600;
}

:deep(.el-table) {
  background: transparent;
  color: #fff;
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
}

:deep(.el-table tr) {
  background: transparent;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(255, 255, 255, 0.02);
}

:deep(.el-table__body tr:hover > td) {
  background: rgba(255, 255, 255, 0.05);
}

:deep(.el-progress-bar__outer) {
  background: rgba(255, 255, 255, 0.1);
}

@media (max-width: 1200px) {
  .stats-section {
    grid-template-columns: repeat(3, 1fr);
  }

  .chart-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}
</style>
