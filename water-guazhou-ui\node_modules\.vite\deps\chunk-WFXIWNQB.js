import {
  r
} from "./chunk-6HCWK637.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T
} from "./chunk-HP475EI3.js";

// node_modules/@arcgis/core/renderers/visualVariables/support/ColorStop.js
var i;
var c = i = class extends l {
  constructor(r2) {
    super(r2), this.color = null, this.label = null, this.value = null;
  }
  writeValue(r2, o, e2) {
    o[e2] = r2 ?? 0;
  }
  clone() {
    return new i({ color: this.color && this.color.clone(), label: this.label, value: this.value });
  }
};
e([y({ type: l2, json: { type: [T], write: true } })], c.prototype, "color", void 0), e([y({ type: String, json: { write: true } })], c.prototype, "label", void 0), e([y({ type: Number, json: { write: { writerEnsuresNonNull: true } } })], c.prototype, "value", void 0), e([r("value")], c.prototype, "writeValue", null), c = i = e([a("esri.renderers.visualVariables.support.ColorStop")], c);
var a2 = c;

export {
  a2 as a
};
//# sourceMappingURL=chunk-WFXIWNQB.js.map
