{"version": 3, "sources": ["../../@arcgis/core/layers/support/LOD.js", "../../@arcgis/core/layers/support/TileInfo.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as t}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";var i;let l=i=class extends o{constructor(e){super(e),this.cols=null,this.level=0,this.levelValue=null,this.origin=null,this.resolution=0,this.rows=null,this.scale=0}clone(){return new i({cols:this.cols,level:this.level,levelValue:this.levelValue,resolution:this.resolution,rows:this.rows,scale:this.scale})}};e([r({json:{write:!0,origins:{\"web-document\":{read:!1,write:!1},\"portal-item\":{read:!1,write:!1}}}})],l.prototype,\"cols\",void 0),e([r({type:t,json:{write:!0}})],l.prototype,\"level\",void 0),e([r({type:String,json:{write:!0}})],l.prototype,\"levelValue\",void 0),e([r({json:{write:!0,origins:{\"web-document\":{read:!1,write:!1},\"portal-item\":{read:!1,write:!1}}}})],l.prototype,\"origin\",void 0),e([r({type:Number,json:{write:!0}})],l.prototype,\"resolution\",void 0),e([r({json:{write:!0,origins:{\"web-document\":{read:!1,write:!1},\"portal-item\":{read:!1,write:!1}}}})],l.prototype,\"rows\",void 0),e([r({type:Number,json:{write:!0}})],l.prototype,\"scale\",void 0),l=i=e([s(\"esri.layers.support.LOD\")],l);const p=l;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONMap as t}from\"../../core/jsonMap.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{isNone as r}from\"../../core/maybe.js\";import{getMetersPerUnitForSR as s}from\"../../core/unitUtils.js\";import{property as i}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as l}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as n}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as a}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as p}from\"../../core/accessorSupport/decorators/writer.js\";import c from\"../../geometry/Point.js\";import u from\"../../geometry/SpatialReference.js\";import{create as f}from\"../../geometry/support/aaBoundingRect.js\";import{isValid as h,getInfo as m,isGeographic as d,equals as y}from\"../../geometry/support/spatialReferenceUtils.js\";import{project as g}from\"../../geometry/support/webMercatorUtils.js\";import v from\"./LOD.js\";import{TileKey as w}from\"./TileKey.js\";var x;const O=new t({PNG:\"png\",PNG8:\"png8\",PNG24:\"png24\",PNG32:\"png32\",JPEG:\"jpg\",JPG:\"jpg\",DIB:\"dib\",TIFF:\"tiff\",EMF:\"emf\",PS:\"ps\",PDF:\"pdf\",GIF:\"gif\",SVG:\"svg\",SVGZ:\"svgz\",Mixed:\"mixed\",MIXED:\"mixed\",LERC:\"lerc\",LERC2D:\"lerc2d\",RAW:\"raw\",pbf:\"pbf\"});let S=x=class extends o{static create(e={}){const{resolutionFactor:t=1,scales:o,size:r=256,spatialReference:i=u.WebMercator,numLODs:l=24}=e;if(!h(i)){const e=[];if(o)for(let t=0;t<o.length;t++){const r=o[t];e.push(new v({level:t,scale:r,resolution:r}))}else{let t=5e-4;for(let o=l-1;o>=0;o--)e.unshift(new v({level:o,scale:t,resolution:t})),t*=2}return new x({dpi:96,lods:e,origin:new c(0,0,i),size:[r,r],spatialReference:i})}const n=m(i),a=e.origin?new c({x:e.origin.x,y:e.origin.y,spatialReference:i}):new c(n?{x:n.origin[0],y:n.origin[1],spatialReference:i}:{x:0,y:0,spatialReference:i}),p=96,f=1/(s(i)*39.37*p),y=[];if(o)for(let s=0;s<o.length;s++){const e=o[s],t=e*f;y.push(new v({level:s,scale:e,resolution:t}))}else{let e=d(i)?512/r*591657527.5917094:256/r*591657527.591555;const o=Math.ceil(l/t);y.push(new v({level:0,scale:e,resolution:e*f}));for(let r=1;r<o;r++){const o=e/2**t,s=o*f;y.push(new v({level:r,scale:o,resolution:s})),e=o}}return new x({dpi:p,lods:y,origin:a,size:[r,r],spatialReference:i})}constructor(e){super(e),this.dpi=96,this.format=null,this.origin=null,this.minScale=0,this.maxScale=0,this.size=null,this.spatialReference=null}get isWrappable(){const{spatialReference:e,origin:t}=this;if(e&&t){const o=m(e);return e.isWrappable&&!!o&&Math.abs(o.origin[0]-t.x)<=o.dx}return!1}readOrigin(e,t){return c.fromJSON({spatialReference:t.spatialReference,...e})}set lods(e){let t=0,o=0;const r=[],s=this._levelToLOD={};e&&(t=-1/0,o=1/0,e.forEach((e=>{r.push(e.scale),t=e.scale>t?e.scale:t,o=e.scale<o?e.scale:o,s[e.level]=e}))),this._set(\"scales\",r),this._set(\"minScale\",t),this._set(\"maxScale\",o),this._set(\"lods\",e),this._initializeUpsampleLevels()}readSize(e,t){return[t.cols,t.rows]}writeSize(e,t){t.cols=e[0],t.rows=e[1]}zoomToScale(e){const t=this.scales;if(e<=0)return t[0];if(e>=t.length-1)return t[t.length-1];const o=Math.floor(e),r=o+1;return t[o]/(t[o]/t[r])**(e-o)}scaleToZoom(e){const t=this.scales,o=t.length-1;let r=0;for(;r<o;r++){const o=t[r],s=t[r+1];if(o<=e)return r;if(s===e)return r+1;if(o>e&&s<e)return r+Math.log(o/e)/Math.log(o/s)}return r}snapScale(e,t=.95){const o=this.scaleToZoom(e);return o%Math.floor(o)>=t?this.zoomToScale(Math.ceil(o)):this.zoomToScale(Math.floor(o))}tileAt(e,t,o,s){const i=this.lodAt(e);if(!i)return null;let l,n;if(\"number\"==typeof t)l=t,n=o;else if(y(t.spatialReference,this.spatialReference))l=t.x,n=t.y,s=o;else{const e=g(t,this.spatialReference);if(r(e))return null;l=e.x,n=e.y,s=o}const a=i.resolution*this.size[0],p=i.resolution*this.size[1];return s||(s=new w(null,0,0,0,f())),s.level=e,s.row=Math.floor((this.origin.y-n)/p+.001),s.col=Math.floor((l-this.origin.x)/a+.001),this.updateTileInfo(s),s}updateTileInfo(e,t=x.ExtrapolateOptions.NONE){let o=this.lodAt(e.level);if(!o&&t===x.ExtrapolateOptions.POWER_OF_TWO){const t=this.lods[this.lods.length-1];t.level<e.level&&(o=t)}if(!o)return;const r=e.level-o.level,s=o.resolution*this.size[0]/2**r,i=o.resolution*this.size[1]/2**r;e.id=`${e.level}/${e.row}/${e.col}`,e.extent||(e.extent=f()),e.extent[0]=this.origin.x+e.col*s,e.extent[1]=this.origin.y-(e.row+1)*i,e.extent[2]=e.extent[0]+s,e.extent[3]=e.extent[1]+i}upsampleTile(e){const t=this._upsampleLevels[e.level];return!(!t||-1===t.parentLevel)&&(e.level=t.parentLevel,e.row=Math.floor(e.row/t.factor+.001),e.col=Math.floor(e.col/t.factor+.001),this.updateTileInfo(e),!0)}getTileBounds(e,t){const o=this.lodAt(t.level);if(null==o)return null;const{resolution:r}=o,s=r*this.size[0],i=r*this.size[1];return e[0]=this.origin.x+t.col*s,e[1]=this.origin.y-(t.row+1)*i,e[2]=e[0]+s,e[3]=e[1]+i,e}lodAt(e){return this._levelToLOD?.[e]??null}clone(){return x.fromJSON(this.write({}))}getOrCreateCompatible(e,t){if(256===this.size[0]&&256===this.size[1])return 256===e?this:null;const o=[],r=this.lods.length;for(let s=0;s<r;s++){const e=this.lods[s],r=e.resolution*t;o.push(new v({level:e.level,scale:e.scale,resolution:r}))}return new x({size:[e,e],dpi:this.dpi,format:this.format,compressionQuality:this.compressionQuality,origin:this.origin,spatialReference:this.spatialReference,lods:o})}_initializeUpsampleLevels(){const e=this.lods;this._upsampleLevels=[];let t=null;for(let o=0;o<e.length;o++){const r=e[o];this._upsampleLevels[r.level]={parentLevel:t?t.level:-1,factor:t?t.resolution/r.resolution:0},t=r}}};e([i({type:Number,json:{write:!0}})],S.prototype,\"compressionQuality\",void 0),e([i({type:Number,json:{write:!0}})],S.prototype,\"dpi\",void 0),e([i({type:String,json:{read:O.read,write:O.write,origins:{\"web-scene\":{read:!1,write:!1}}}})],S.prototype,\"format\",void 0),e([i({readOnly:!0})],S.prototype,\"isWrappable\",null),e([i({type:c,json:{write:!0}})],S.prototype,\"origin\",void 0),e([n(\"origin\")],S.prototype,\"readOrigin\",null),e([i({type:[v],value:null,json:{write:!0}})],S.prototype,\"lods\",null),e([i({readOnly:!0})],S.prototype,\"minScale\",void 0),e([i({readOnly:!0})],S.prototype,\"maxScale\",void 0),e([i({readOnly:!0})],S.prototype,\"scales\",void 0),e([i({cast:e=>Array.isArray(e)?e:\"number\"==typeof e?[e,e]:[256,256]})],S.prototype,\"size\",void 0),e([n(\"size\",[\"rows\",\"cols\"])],S.prototype,\"readSize\",null),e([p(\"size\",{cols:{type:l},rows:{type:l}})],S.prototype,\"writeSize\",null),e([i({type:u,json:{write:!0}})],S.prototype,\"spatialReference\",void 0),S=x=e([a(\"esri.layers.support.TileInfo\")],S),function(e){var t;(t=e.ExtrapolateOptions||(e.ExtrapolateOptions={}))[t.NONE=0]=\"NONE\",t[t.POWER_OF_TWO=1]=\"POWER_OF_TWO\"}(S||(S={}));const j=S;export{j as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIoW,IAAI;AAAE,IAAIA,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,MAAK,KAAK,QAAM,GAAE,KAAK,aAAW,MAAK,KAAK,SAAO,MAAK,KAAK,aAAW,GAAE,KAAK,OAAK,MAAK,KAAK,QAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,YAAW,KAAK,YAAW,YAAW,KAAK,YAAW,MAAK,KAAK,MAAK,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,gBAAe,EAAC,MAAK,OAAG,OAAM,MAAE,GAAE,eAAc,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;;;ACAjT,IAAI;AAAE,IAAM,IAAE,IAAI,EAAE,EAAC,KAAI,OAAM,MAAK,QAAO,OAAM,SAAQ,OAAM,SAAQ,MAAK,OAAM,KAAI,OAAM,KAAI,OAAM,MAAK,QAAO,KAAI,OAAM,IAAG,MAAK,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,MAAK,QAAO,OAAM,SAAQ,OAAM,SAAQ,MAAK,QAAO,QAAO,UAAS,KAAI,OAAM,KAAI,MAAK,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,OAAO,OAAOE,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,kBAAiBC,KAAE,GAAE,QAAOC,IAAE,MAAKC,KAAE,KAAI,kBAAiBC,KAAE,EAAE,aAAY,SAAQC,KAAE,GAAE,IAAEL;AAAE,QAAG,CAAC,EAAEI,EAAC,GAAE;AAAC,YAAMJ,KAAE,CAAC;AAAE,UAAGE,GAAE,UAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,cAAME,KAAED,GAAED,EAAC;AAAE,QAAAD,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMC,IAAE,OAAME,IAAE,YAAWA,GAAC,CAAC,CAAC;AAAA,MAAC;AAAA,WAAK;AAAC,YAAIF,KAAE;AAAK,iBAAQC,KAAEG,KAAE,GAAEH,MAAG,GAAEA,KAAI,CAAAF,GAAE,QAAQ,IAAI,EAAE,EAAC,OAAME,IAAE,OAAMD,IAAE,YAAWA,GAAC,CAAC,CAAC,GAAEA,MAAG;AAAA,MAAC;AAAC,aAAO,IAAI,EAAE,EAAC,KAAI,IAAG,MAAKD,IAAE,QAAO,IAAI,EAAE,GAAE,GAAEI,EAAC,GAAE,MAAK,CAACD,IAAEA,EAAC,GAAE,kBAAiBC,GAAC,CAAC;AAAA,IAAC;AAAC,UAAM,IAAE,EAAEA,EAAC,GAAEE,KAAEN,GAAE,SAAO,IAAI,EAAE,EAAC,GAAEA,GAAE,OAAO,GAAE,GAAEA,GAAE,OAAO,GAAE,kBAAiBI,GAAC,CAAC,IAAE,IAAI,EAAE,IAAE,EAAC,GAAE,EAAE,OAAO,CAAC,GAAE,GAAE,EAAE,OAAO,CAAC,GAAE,kBAAiBA,GAAC,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,kBAAiBA,GAAC,CAAC,GAAEG,KAAE,IAAGC,KAAE,KAAG,EAAEJ,EAAC,IAAE,QAAMG,KAAGE,KAAE,CAAC;AAAE,QAAGP,GAAE,UAAQQ,KAAE,GAAEA,KAAER,GAAE,QAAOQ,MAAI;AAAC,YAAMV,KAAEE,GAAEQ,EAAC,GAAET,KAAED,KAAEQ;AAAE,MAAAC,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMC,IAAE,OAAMV,IAAE,YAAWC,GAAC,CAAC,CAAC;AAAA,IAAC;AAAA,SAAK;AAAC,UAAID,KAAEE,GAAEE,EAAC,IAAE,MAAID,KAAE,sBAAkB,MAAIA,KAAE;AAAiB,YAAMD,KAAE,KAAK,KAAKG,KAAEJ,EAAC;AAAE,MAAAQ,GAAE,KAAK,IAAI,EAAE,EAAC,OAAM,GAAE,OAAMT,IAAE,YAAWA,KAAEQ,GAAC,CAAC,CAAC;AAAE,eAAQL,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,cAAMD,KAAEF,KAAE,KAAGC,IAAES,KAAER,KAAEM;AAAE,QAAAC,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMN,IAAE,OAAMD,IAAE,YAAWQ,GAAC,CAAC,CAAC,GAAEV,KAAEE;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,IAAI,EAAE,EAAC,KAAIK,IAAE,MAAKE,IAAE,QAAOH,IAAE,MAAK,CAACH,IAAEA,EAAC,GAAE,kBAAiBC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,MAAI,IAAG,KAAK,SAAO,MAAK,KAAK,SAAO,MAAK,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,OAAK,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,UAAK,EAAC,kBAAiBA,IAAE,QAAOC,GAAC,IAAE;AAAK,QAAGD,MAAGC,IAAE;AAAC,YAAMC,KAAE,EAAEF,EAAC;AAAE,aAAOA,GAAE,eAAa,CAAC,CAACE,MAAG,KAAK,IAAIA,GAAE,OAAO,CAAC,IAAED,GAAE,CAAC,KAAGC,GAAE;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,WAAWF,IAAEC,IAAE;AAAC,WAAO,EAAE,SAAS,EAAC,kBAAiBA,GAAE,kBAAiB,GAAGD,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,QAAIC,KAAE,GAAEC,KAAE;AAAE,UAAMC,KAAE,CAAC,GAAEO,KAAE,KAAK,cAAY,CAAC;AAAE,IAAAV,OAAIC,KAAE,KAAG,GAAEC,KAAE,IAAE,GAAEF,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAG,GAAE,KAAKH,GAAE,KAAK,GAAEC,KAAED,GAAE,QAAMC,KAAED,GAAE,QAAMC,IAAEC,KAAEF,GAAE,QAAME,KAAEF,GAAE,QAAME,IAAEQ,GAAEV,GAAE,KAAK,IAAEA;AAAA,IAAC,CAAE,IAAG,KAAK,KAAK,UAASG,EAAC,GAAE,KAAK,KAAK,YAAWF,EAAC,GAAE,KAAK,KAAK,YAAWC,EAAC,GAAE,KAAK,KAAK,QAAOF,EAAC,GAAE,KAAK,0BAA0B;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,WAAM,CAACA,GAAE,MAAKA,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,UAAUD,IAAEC,IAAE;AAAC,IAAAA,GAAE,OAAKD,GAAE,CAAC,GAAEC,GAAE,OAAKD,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAO,QAAGD,MAAG,EAAE,QAAOC,GAAE,CAAC;AAAE,QAAGD,MAAGC,GAAE,SAAO,EAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC;AAAE,UAAMC,KAAE,KAAK,MAAMF,EAAC,GAAEG,KAAED,KAAE;AAAE,WAAOD,GAAEC,EAAC,KAAGD,GAAEC,EAAC,IAAED,GAAEE,EAAC,OAAKH,KAAEE;AAAA,EAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAOC,KAAED,GAAE,SAAO;AAAE,QAAIE,KAAE;AAAE,WAAKA,KAAED,IAAEC,MAAI;AAAC,YAAMD,KAAED,GAAEE,EAAC,GAAEO,KAAET,GAAEE,KAAE,CAAC;AAAE,UAAGD,MAAGF,GAAE,QAAOG;AAAE,UAAGO,OAAIV,GAAE,QAAOG,KAAE;AAAE,UAAGD,KAAEF,MAAGU,KAAEV,GAAE,QAAOG,KAAE,KAAK,IAAID,KAAEF,EAAC,IAAE,KAAK,IAAIE,KAAEQ,EAAC;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAA,EAAC,UAAUH,IAAEC,KAAE,MAAI;AAAC,UAAMC,KAAE,KAAK,YAAYF,EAAC;AAAE,WAAOE,KAAE,KAAK,MAAMA,EAAC,KAAGD,KAAE,KAAK,YAAY,KAAK,KAAKC,EAAC,CAAC,IAAE,KAAK,YAAY,KAAK,MAAMA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAEC,IAAEC,IAAEQ,IAAE;AAAC,UAAMN,KAAE,KAAK,MAAMJ,EAAC;AAAE,QAAG,CAACI,GAAE,QAAO;AAAK,QAAIC,IAAE;AAAE,QAAG,YAAU,OAAOJ,GAAE,CAAAI,KAAEJ,IAAE,IAAEC;AAAA,aAAU,EAAED,GAAE,kBAAiB,KAAK,gBAAgB,EAAE,CAAAI,KAAEJ,GAAE,GAAE,IAAEA,GAAE,GAAES,KAAER;AAAA,SAAM;AAAC,YAAMF,KAAE,EAAEC,IAAE,KAAK,gBAAgB;AAAE,UAAG,EAAED,EAAC,EAAE,QAAO;AAAK,MAAAK,KAAEL,GAAE,GAAE,IAAEA,GAAE,GAAEU,KAAER;AAAA,IAAC;AAAC,UAAMI,KAAEF,GAAE,aAAW,KAAK,KAAK,CAAC,GAAEG,KAAEH,GAAE,aAAW,KAAK,KAAK,CAAC;AAAE,WAAOM,OAAIA,KAAE,IAAIT,GAAE,MAAK,GAAE,GAAE,GAAE,EAAE,CAAC,IAAGS,GAAE,QAAMV,IAAEU,GAAE,MAAI,KAAK,OAAO,KAAK,OAAO,IAAE,KAAGH,KAAE,IAAI,GAAEG,GAAE,MAAI,KAAK,OAAOL,KAAE,KAAK,OAAO,KAAGC,KAAE,IAAI,GAAE,KAAK,eAAeI,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,eAAeV,IAAEC,KAAE,EAAE,mBAAmB,MAAK;AAAC,QAAIC,KAAE,KAAK,MAAMF,GAAE,KAAK;AAAE,QAAG,CAACE,MAAGD,OAAI,EAAE,mBAAmB,cAAa;AAAC,YAAMA,KAAE,KAAK,KAAK,KAAK,KAAK,SAAO,CAAC;AAAE,MAAAA,GAAE,QAAMD,GAAE,UAAQE,KAAED;AAAA,IAAE;AAAC,QAAG,CAACC,GAAE;AAAO,UAAMC,KAAEH,GAAE,QAAME,GAAE,OAAMQ,KAAER,GAAE,aAAW,KAAK,KAAK,CAAC,IAAE,KAAGC,IAAEC,KAAEF,GAAE,aAAW,KAAK,KAAK,CAAC,IAAE,KAAGC;AAAE,IAAAH,GAAE,KAAG,GAAGA,GAAE,KAAK,IAAIA,GAAE,GAAG,IAAIA,GAAE,GAAG,IAAGA,GAAE,WAASA,GAAE,SAAO,EAAE,IAAGA,GAAE,OAAO,CAAC,IAAE,KAAK,OAAO,IAAEA,GAAE,MAAIU,IAAEV,GAAE,OAAO,CAAC,IAAE,KAAK,OAAO,KAAGA,GAAE,MAAI,KAAGI,IAAEJ,GAAE,OAAO,CAAC,IAAEA,GAAE,OAAO,CAAC,IAAEU,IAAEV,GAAE,OAAO,CAAC,IAAEA,GAAE,OAAO,CAAC,IAAEI;AAAA,EAAC;AAAA,EAAC,aAAaJ,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAgBD,GAAE,KAAK;AAAE,WAAM,EAAE,CAACC,MAAG,OAAKA,GAAE,iBAAeD,GAAE,QAAMC,GAAE,aAAYD,GAAE,MAAI,KAAK,MAAMA,GAAE,MAAIC,GAAE,SAAO,IAAI,GAAED,GAAE,MAAI,KAAK,MAAMA,GAAE,MAAIC,GAAE,SAAO,IAAI,GAAE,KAAK,eAAeD,EAAC,GAAE;AAAA,EAAG;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,MAAMD,GAAE,KAAK;AAAE,QAAG,QAAMC,GAAE,QAAO;AAAK,UAAK,EAAC,YAAWC,GAAC,IAAED,IAAEQ,KAAEP,KAAE,KAAK,KAAK,CAAC,GAAEC,KAAED,KAAE,KAAK,KAAK,CAAC;AAAE,WAAOH,GAAE,CAAC,IAAE,KAAK,OAAO,IAAEC,GAAE,MAAIS,IAAEV,GAAE,CAAC,IAAE,KAAK,OAAO,KAAGC,GAAE,MAAI,KAAGG,IAAEJ,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEU,IAAEV,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEI,IAAEJ;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAJnyJ;AAIoyJ,aAAO,UAAK,gBAAL,mBAAmBA,QAAI;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,EAAE,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAEC,IAAE;AAAC,QAAG,QAAM,KAAK,KAAK,CAAC,KAAG,QAAM,KAAK,KAAK,CAAC,EAAE,QAAO,QAAMD,KAAE,OAAK;AAAK,UAAME,KAAE,CAAC,GAAEC,KAAE,KAAK,KAAK;AAAO,aAAQO,KAAE,GAAEA,KAAEP,IAAEO,MAAI;AAAC,YAAMV,KAAE,KAAK,KAAKU,EAAC,GAAEP,KAAEH,GAAE,aAAWC;AAAE,MAAAC,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMF,GAAE,OAAM,OAAMA,GAAE,OAAM,YAAWG,GAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,CAACH,IAAEA,EAAC,GAAE,KAAI,KAAK,KAAI,QAAO,KAAK,QAAO,oBAAmB,KAAK,oBAAmB,QAAO,KAAK,QAAO,kBAAiB,KAAK,kBAAiB,MAAKE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,UAAMF,KAAE,KAAK;AAAK,SAAK,kBAAgB,CAAC;AAAE,QAAIC,KAAE;AAAK,aAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMC,KAAEH,GAAEE,EAAC;AAAE,WAAK,gBAAgBC,GAAE,KAAK,IAAE,EAAC,aAAYF,KAAEA,GAAE,QAAM,IAAG,QAAOA,KAAEA,GAAE,aAAWE,GAAE,aAAW,EAAC,GAAEF,KAAEE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAE,MAAK,OAAM,EAAE,OAAM,SAAQ,EAAC,aAAY,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAE,EAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,MAAK,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAAH,OAAG,MAAM,QAAQA,EAAC,IAAEA,KAAE,YAAU,OAAOA,KAAE,CAACA,IAAEA,EAAC,IAAE,CAAC,KAAI,GAAG,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,QAAO,CAAC,QAAO,MAAM,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,QAAO,EAAC,MAAK,EAAC,MAAK,EAAC,GAAE,MAAK,EAAC,MAAK,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC,GAAE,SAASA,IAAE;AAAC,MAAIC;AAAE,GAACA,KAAED,GAAE,uBAAqBA,GAAE,qBAAmB,CAAC,IAAIC,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,eAAa,CAAC,IAAE;AAAc,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE;", "names": ["l", "e", "e", "t", "o", "r", "i", "l", "a", "p", "f", "y", "s"]}