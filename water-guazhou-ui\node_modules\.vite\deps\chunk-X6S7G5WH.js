import {
  m
} from "./chunk-B4YFVQZH.js";
import {
  c as c2,
  h,
  l
} from "./chunk-CDZ24ELJ.js";
import {
  M
} from "./chunk-VHLK35TF.js";
import {
  c,
  i,
  x
} from "./chunk-G5KX4JSG.js";
import {
  r
} from "./chunk-MQAXMQFG.js";
import {
  n
} from "./chunk-36FLFRUE.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/3d/interactive/editingTools/isSupportedGraphicUtils.js
function E(E2) {
  switch (E2) {
    case P.SUPPORTED:
      break;
    case P.GRAPHICS_LAYER_MISSING:
      return "not owned by a graphics layer";
    case P.GEOMETRY_MISSING:
      return "no geometry";
    case P.GEOMETRY_TYPE_UNSUPPORTED:
      return "the geometry type is not supported";
    case P.ELEVATION_MODE_UNSUPPORTED:
      return "the elevation mode is not supported";
    case P.SYMBOL_TYPE_UNSUPPORTED:
      return "the symbol type is not supported";
  }
  return "";
}
var P;
!function(E2) {
  E2[E2.SUPPORTED = 0] = "SUPPORTED", E2[E2.GRAPHICS_LAYER_MISSING = 1] = "GRAPHICS_LAYER_MISSING", E2[E2.GEOMETRY_MISSING = 2] = "GEOMETRY_MISSING", E2[E2.GEOMETRY_TYPE_UNSUPPORTED = 3] = "GEOMETRY_TYPE_UNSUPPORTED", E2[E2.ELEVATION_MODE_UNSUPPORTED = 4] = "ELEVATION_MODE_UNSUPPORTED", E2[E2.SYMBOL_TYPE_UNSUPPORTED = 5] = "SYMBOL_TYPE_UNSUPPORTED";
}(P || (P = {}));

// node_modules/@arcgis/core/views/3d/interactive/editingTools/moveGraphic/isSupportedGraphic.js
function i2(i3) {
  var _a;
  if ("graphics" !== ((_a = i3.layer) == null ? void 0 : _a.type)) return P.GRAPHICS_LAYER_MISSING;
  if (t(i3.geometry)) return P.GEOMETRY_MISSING;
  switch (i3.geometry.type) {
    case "polygon":
    case "point":
    case "polyline":
    case "mesh":
      break;
    default:
      return P.GEOMETRY_TYPE_UNSUPPORTED;
  }
  return "on-the-ground" !== l(i3) && c2(i3) ? P.ELEVATION_MODE_UNSUPPORTED : P.SUPPORTED;
}

// node_modules/@arcgis/core/views/3d/interactive/editingTools/reshapeGraphic/isSupportedGraphic.js
function l2(e) {
  return u(e).result;
}
function m2(e) {
  return u(e).geometry;
}
function u(l3) {
  var _a;
  if ("graphics" !== ((_a = l3.layer) == null ? void 0 : _a.type)) return { result: P.GRAPHICS_LAYER_MISSING, geometry: null };
  if (t(l3.geometry)) return { result: P.GEOMETRY_MISSING, geometry: null };
  return "on-the-ground" !== l(l3) && c2(l3) ? { result: P.ELEVATION_MODE_UNSUPPORTED, geometry: null } : "point" !== l3.geometry.type && "polyline" !== l3.geometry.type && ("polygon" !== l3.geometry.type || l3.geometry instanceof m) ? { result: P.GEOMETRY_TYPE_UNSUPPORTED, geometry: null } : { result: P.SUPPORTED, geometry: l3.geometry };
}

// node_modules/@arcgis/core/views/interactive/support/viewUtils.js
function f(e, r2, o2, t2, n2 = n()) {
  const a = r(j, e);
  return a[2] = h(t2, a, r2, o2) || 0, t2.renderCoordsHelper.toRenderCoords(a, r2, n2), n2;
}
function u2(r2, o2, t2, n2) {
  return "2d" === n2.type ? (R.x = r2[0], R.y = r2[1], R.spatialReference = o2, n2.toScreen(R)) : (f(r2, o2, t2, n2, j), n2.state.camera.projectToScreen(j, v2), c(v2[0], v2[1]));
}
var R = M(0, 0, 0, null);
var j = n();
var h2 = x();
var C = x();
var v2 = i();

export {
  E,
  P,
  i2 as i,
  l2 as l,
  m2 as m,
  f,
  u2 as u
};
//# sourceMappingURL=chunk-X6S7G5WH.js.map
