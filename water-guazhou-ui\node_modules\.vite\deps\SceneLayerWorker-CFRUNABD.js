import "./chunk-YELYN22P.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import {
  a
} from "./chunk-FZ7BG3VX.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-JEDE7445.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/libs/i3s/enums.js
var n;
var e;
!function(n4) {
  n4[n4.None = 0] = "None", n4[n4.Int16 = 1] = "Int16", n4[n4.Int32 = 2] = "Int32";
}(n || (n = {})), function(n4) {
  n4[n4.Replace = 0] = "Replace", n4[n4.Outside = 1] = "Outside", n4[n4.Inside = 2] = "Inside", n4[n4.Finished = 3] = "Finished";
}(e || (e = {}));

// node_modules/@arcgis/core/libs/i3s/I3SModule.js
function e2() {
  return n2 || (n2 = new Promise((t) => import("./i3s-AYKQAEJA.js").then((t2) => t2.i).then(({ default: e3 }) => {
    const n4 = e3({ locateFile: i, onRuntimeInitialized: () => t(n4) });
    delete n4.then;
  })).catch((t) => {
    throw t;
  })), n2;
}
function i(e3) {
  return a(`esri/libs/i3s/${e3}`);
}
var n2;

// node_modules/@arcgis/core/views/3d/layers/i3s/I3SNode.js
var n3;
var o;
var s;
var a2;
var c;
!function(e3) {
  e3[e3.Unmodified = 0] = "Unmodified", e3[e3.Culled = 1] = "Culled", e3[e3.NotChecked = 2] = "NotChecked";
}(n3 || (n3 = {})), function(e3) {
  e3[e3.Unmodified = 0] = "Unmodified", e3[e3.PotentiallyModified = 1] = "PotentiallyModified", e3[e3.Culled = 2] = "Culled", e3[e3.Unknown = 3] = "Unknown", e3[e3.NotChecked = 4] = "NotChecked";
}(o || (o = {}));
!function(e3) {
  e3[e3.Unknown = 0] = "Unknown", e3[e3.Uncached = 1] = "Uncached", e3[e3.Cached = 2] = "Cached";
}(s || (s = {})), function(e3) {
  e3[e3.None = 0] = "None", e3[e3.MaxScreenThreshold = 1] = "MaxScreenThreshold", e3[e3.ScreenSpaceRelative = 2] = "ScreenSpaceRelative", e3[e3.RemovedFeatureDiameter = 3] = "RemovedFeatureDiameter", e3[e3.DistanceRangeFromDefaultCamera = 4] = "DistanceRangeFromDefaultCamera";
}(a2 || (a2 = {})), function(e3) {
  e3[e3.Hole = 0] = "Hole", e3[e3.Leaf = 1] = "Leaf";
}(c || (c = {}));

// node_modules/@arcgis/core/views/3d/layers/SceneLayerWorker.js
async function o2(e3) {
  await p();
  const t = [e3.geometryBuffer];
  return { result: b(e3, t), transferList: t };
}
async function s2(e3) {
  var _a;
  await p();
  const t = [e3.geometryBuffer], { geometryBuffer: r2 } = e3, n4 = r2.byteLength, o3 = u._malloc(n4), s3 = new Uint8Array(u.HEAPU8.buffer, o3, n4);
  s3.set(new Uint8Array(r2));
  const i3 = u.dracoDecompressPointCloudData(o3, s3.byteLength);
  if (u._free(o3), i3.error.length > 0) throw new Error(`i3s.wasm: ${i3.error}`);
  const f2 = ((_a = i3.featureIds) == null ? void 0 : _a.length) > 0 ? i3.featureIds.slice() : null, a4 = i3.positions.slice();
  return f2 && t.push(f2.buffer), t.push(a4.buffer), { result: { positions: a4, featureIds: f2 }, transferList: t };
}
async function i2(e3) {
  await p(), d(e3);
  const t = { buffer: e3.buffer };
  return { result: t, transferList: [t.buffer] };
}
async function f(e3) {
  await p(), y(e3);
}
async function a3(e3) {
  await p(), u.setLegacySchema(e3.context, e3.jsonSchema);
}
function l(e3) {
  E2(e3);
}
var c2;
var u;
function y(e3) {
  const t = e3.modifications, r2 = u._malloc(8 * t.length), n4 = new Float64Array(u.HEAPU8.buffer, r2, t.length);
  for (let o3 = 0; o3 < t.length; ++o3) n4[o3] = t[o3];
  u.setModifications(e3.context, r2, t.length, e3.isGeodetic), u._free(r2);
}
function b(r2, n4) {
  if (!u) return null;
  const { context: o3, localOrigin: s3, globalTrafo: i3, mbs: f2, obb: a4, elevationOffset: l2, geometryBuffer: c3, geometryDescriptor: y2, indexToVertexProjector: b2, vertexToRenderProjector: m2 } = r2, d2 = u._malloc(c3.byteLength), E3 = 33, p2 = u._malloc(E3 * Float64Array.BYTES_PER_ELEMENT), g2 = new Uint8Array(u.HEAPU8.buffer, d2, c3.byteLength);
  g2.set(new Uint8Array(c3));
  const w = new Float64Array(u.HEAPU8.buffer, p2, E3);
  h(w, s3);
  let A = w.byteOffset + 3 * w.BYTES_PER_ELEMENT, _ = new Float64Array(w.buffer, A);
  h(_, i3), A += 16 * w.BYTES_PER_ELEMENT, _ = new Float64Array(w.buffer, A), h(_, f2), A += 4 * w.BYTES_PER_ELEMENT, r(a4) && (_ = new Float64Array(w.buffer, A), h(_, a4.center), A += 3 * w.BYTES_PER_ELEMENT, _ = new Float64Array(w.buffer, A), h(_, a4.halfSize), A += 3 * w.BYTES_PER_ELEMENT, _ = new Float64Array(w.buffer, A), h(_, a4.quaternion));
  const L = y2, I = { isDraco: false, isLegacy: false, color: r2.layouts.some((e3) => e3.some((e4) => "color" === e4.name)), normal: r2.needNormals && r2.layouts.some((e3) => e3.some((e4) => "normalCompressed" === e4.name)), uv0: r2.layouts.some((e3) => e3.some((e4) => "uv0" === e4.name)), uvRegion: r2.layouts.some((e3) => e3.some((e4) => "uvRegion" === e4.name)), featureIndex: L.featureIndex }, T = u.process(o3, !!r2.obb, d2, g2.byteLength, L, I, p2, l2, b2, m2, r2.normalReferenceFrame);
  if (u._free(p2), u._free(d2), T.error.length > 0) throw new Error(`i3s.wasm: ${T.error}`);
  if (T.discarded) return null;
  const P = T.componentOffsets.length > 0 ? T.componentOffsets.slice() : null, U = T.featureIds.length > 0 ? T.featureIds.slice() : null, B = T.interleavedVertedData.slice().buffer, F = T.indicesType === n.Int16 ? new Uint16Array(T.indices.buffer, T.indices.byteOffset, T.indices.byteLength / 2).slice() : new Uint32Array(T.indices.buffer, T.indices.byteOffset, T.indices.byteLength / 4).slice(), M = T.positions.slice(), S = T.positionIndicesType === n.Int16 ? new Uint16Array(T.positionIndices.buffer, T.positionIndices.byteOffset, T.positionIndices.byteLength / 2).slice() : new Uint32Array(T.positionIndices.buffer, T.positionIndices.byteOffset, T.positionIndices.byteLength / 4).slice(), x = { layout: r2.layouts[0], interleavedVertexData: B, indices: F, hasColors: T.hasColors, hasModifications: T.hasModifications, positionData: { data: M, indices: S } };
  return U && n4.push(U.buffer), P && n4.push(P.buffer), n4.push(B), n4.push(F.buffer), n4.push(M.buffer), n4.push(S.buffer), { componentOffsets: P, featureIds: U, transformedGeometry: x, obb: T.obb };
}
function m(e3) {
  return 0 === e3 ? o.Unmodified : 1 === e3 ? o.PotentiallyModified : 2 === e3 ? o.Culled : o.Unknown;
}
function d(e3) {
  const { context: t, buffer: r2 } = e3, n4 = u._malloc(r2.byteLength), o3 = r2.byteLength / Float64Array.BYTES_PER_ELEMENT, s3 = new Float64Array(u.HEAPU8.buffer, n4, o3), i3 = new Float64Array(r2);
  s3.set(i3), u.filterOBBs(t, n4, o3), i3.set(s3), u._free(n4);
}
function E2(e3) {
  u && u.destroy(e3);
}
function h(e3, t) {
  for (let r2 = 0; r2 < t.length; ++r2) e3[r2] = t[r2];
}
function p() {
  return u ? Promise.resolve() : (c2 || (c2 = e2().then((e3) => {
    u = e3, c2 = null;
  })), c2);
}
var g = { transform: b, destroy: E2 };
export {
  l as destroyContext,
  s2 as dracoDecompressPointCloudData,
  i2 as filterObbsForModifications,
  d as filterObbsForModificationsSync,
  p as initialize,
  m as interpretObbModificationResults,
  o2 as process,
  a3 as setLegacySchema,
  f as setModifications,
  y as setModificationsSync,
  g as test
};
//# sourceMappingURL=SceneLayerWorker-CFRUNABD.js.map
