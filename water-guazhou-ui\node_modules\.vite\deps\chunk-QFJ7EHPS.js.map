{"version": 3, "sources": ["../../@arcgis/core/symbols/cim/CIMSymbolRasterizer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Color.js\";import t from\"../../request.js\";import{unwrapOrThrow as a}from\"../../core/maybe.js\";import{throwIfAborted as i}from\"../../core/promiseUtils.js\";import{pt2px as r}from\"../../core/screenUtils.js\";import{analyzeCIMSymbol as s,analyzeCIMResource as o}from\"./cimAnalyzer.js\";import n from\"./CIMResourceManager.js\";import{Transformation as c,CanvasDrawHelper as h}from\"./CIMSymbolDrawHelper.js\";import{OverrideHelper as l,CIMSymbolHelper as m}from\"./CIMSymbolHelper.js\";import g from\"./Rasterizer.js\";import u from\"./TextRasterizer.js\";import{mapCIMSymbolToGeometryType as f,createLabelOverrideFunction as d,evaluateValueOrFunction as p,colorToArray as y}from\"./utils.js\";import{scaleCIMMarker as w}from\"../support/cimSymbolUtils.js\";import{Symbol3DAnchorPosition2D as C}from\"../support/Symbol3DAnchorPosition2D.js\";var M;!function(e){e.Legend=\"legend\",e.Preview=\"preview\"}(M||(M={}));const _=e=>e&&e.scaleFactor?e.scaleFactor:1,v=96/72;class z{constructor(e,t){this._spatialReference=e,this._avoidSDF=t,this._resourceCache=new Map,this._imageDataCanvas=null,this._pictureMarkerCache=new Map,this._textRasterizer=new u,this._cimResourceManager=new n,this._rasterizer=new g(this._cimResourceManager)}get resourceManager(){return this._cimResourceManager}async rasterizeCIMSymbolAsync(e,t,a,i,r,s,o,n){if(!e)return null;const{data:g}=e;if(!g||\"CIMSymbolReference\"!==g.type||!g.symbol)return null;const{symbol:u}=g;s||(s=f(u));const d=await l.resolveSymbolOverrides(g,t,this._spatialReference,r,s,o,n);this._imageDataCanvas||(this._imageDataCanvas=document.createElement(\"canvas\"));const p=this._imageDataCanvas,y=this._cimResourceManager,w=[];m.fetchResources(d,y,w),w.length>0&&await Promise.all(w);const{width:C,height:M}=a,_=I(s,C,M,i),z=m.getEnvelope(d,_,y);if(!z)return null;const x=(window.devicePixelRatio||1)*v;let R=1,b=0,P=0;switch(u.type){case\"CIMPointSymbol\":case\"CIMTextSymbol\":{let e=1;z.width>C&&(e=C/z.width);let t=1;z.height>M&&(t=M/z.height),\"preview\"===i&&(z.width<C&&(e=C/z.width),z.height<M&&(t=M/z.height)),R=Math.min(e,t),b=z.x+z.width/2,P=z.y+z.height/2}break;case\"CIMLineSymbol\":{let e=1;z.height>M&&(e=M/z.height),R=e,P=z.y+z.height/2;const t=z.x*R+C/2,a=(z.x+z.width)*R+C/2;if(t<0){const{paths:e}=_;e[0][0][0]-=t}if(a>C){const{paths:e}=_;e[0][2][0]-=a-C}}break;case\"CIMPolygonSymbol\":{b=z.x+z.width/2,P=z.y+z.height/2;const e=z.x*R+C/2,t=(z.x+z.width)*R+C/2,a=z.y*R+M/2,i=(z.y+z.height)*R+M/2,{rings:r}=_;e<0&&(r[0][0][0]-=e,r[0][3][0]-=e,r[0][4][0]-=e),a<0&&(r[0][0][1]+=a,r[0][1][1]+=a,r[0][4][1]+=a),t>C&&(r[0][1][0]-=t-C,r[0][2][0]-=t-C),i>M&&(r[0][2][1]+=i-M,r[0][3][1]+=i-M)}}p.width=C*x,p.height=M*x;const D=1;p.width+=2*D,p.height+=2*D;const S=p.getContext(\"2d\"),k=c.createIdentity();k.translate(-b,-P),k.scale(R*x,-R*x),k.translate(C*x/2+D,M*x/2+D),S.clearRect(0,0,p.width,p.height);return new h(S,y,k,!0).drawSymbol(d,_),S.getImageData(0,0,p.width,p.height)}async analyzeCIMSymbol(e,t,a,r,o){const n=[],c=t?{geometryType:r,spatialReference:this._spatialReference,fields:t}:null;let h;await s(e.data,c,this._cimResourceManager,n,this._avoidSDF),i(o);for(const i of n)\"CIMPictureMarker\"!==i.cim.type&&\"CIMPictureFill\"!==i.cim.type&&\"CIMPictureStroke\"!==i.cim.type||(h||(h=[]),h.push(this._fetchPictureMarkerResource(i,o))),a&&\"text\"===i.type&&\"string\"==typeof i.text&&i.text.includes(\"[\")&&(i.text=d(a,i.text,i.cim.textCase));return h&&await Promise.all(h),n}rasterizeCIMSymbol3D(e,t,a,i,r,s){const o=[];for(const n of e){i&&\"function\"==typeof i.scaleFactor&&(i.scaleFactor=i.scaleFactor(t,r,s));const e=this._getRasterizedResource(n,t,a,i,r,s);if(!e)continue;let c=0,h=e.anchorX||0,l=e.anchorY||0,m=!1,g=0,u=0;if(\"esriGeometryPoint\"===a){const e=_(i);if(g=p(n.offsetX,t,r,s)*e||0,u=p(n.offsetY,t,r,s)*e||0,\"marker\"===n.type)c=p(n.rotation,t,r,s)||0,m=!!n.rotateClockwise&&n.rotateClockwise;else if(\"text\"===n.type){if(c=p(n.angle,t,r,s)||0,void 0!==n.horizontalAlignment)switch(n.horizontalAlignment){case\"left\":h=-.5;break;case\"right\":h=.5;break;default:h=0}if(void 0!==n.verticalAlignment)switch(n.verticalAlignment){case\"top\":l=.5;break;case\"bottom\":l=-.5;break;case\"baseline\":l=-.25;break;default:l=0}}}null!=e&&o.push({angle:c,rotateClockWise:m,anchorX:h,anchorY:l,offsetX:g,offsetY:u,rasterizedResource:e})}return this.getSymbolImage(o)}getSymbolImage(e){const t=document.createElement(\"canvas\"),i=a(t.getContext(\"2d\"));let s=0,o=0,n=0,c=0;const h=[];for(let a=0;a<e.length;a++){const t=e[a],l=t.rasterizedResource;if(!l)continue;const m=l.size,g=t.offsetX,u=t.offsetY,f=t.anchorX,d=t.anchorY,p=t.rotateClockWise||!1;let y=t.angle,w=r(g)-m[0]*(.5+f),C=r(u)-m[1]*(.5+d),M=w+m[0],_=C+m[1];if(y){p&&(y=-y);const e=Math.sin(y*Math.PI/180),t=Math.cos(y*Math.PI/180),a=w*t-C*e,i=w*e+C*t,r=w*t-_*e,s=w*e+_*t,o=M*t-_*e,n=M*e+_*t,c=M*t-C*e,h=M*e+C*t;w=Math.min(a,r,o,c),C=Math.min(i,s,n,h),M=Math.max(a,r,o,c),_=Math.max(i,s,n,h)}s=w<s?w:s,o=C<o?C:o,n=M>n?M:n,c=_>c?_:c;const v=i.createImageData(l.size[0],l.size[1]);v.data.set(new Uint8ClampedArray(l.image.buffer));const z={offsetX:g,offsetY:u,rotateClockwise:p,angle:y,rasterizedImage:v,anchorX:f,anchorY:d};h.push(z)}t.width=n-s,t.height=c-o;const l=-s,m=c;for(let a=0;a<h.length;a++){const e=h[a],t=this._imageDataToCanvas(e.rasterizedImage),s=e.rasterizedImage.width,o=e.rasterizedImage.height,n=l-s*(.5+e.anchorX),c=m-o*(.5-e.anchorY);if(e.angle){const a=(360-e.angle)*Math.PI/180;i.save(),i.translate(r(e.offsetX),-r(e.offsetY)),i.translate(l,m),i.rotate(a),i.translate(-l,-m),i.drawImage(t,n,c),i.restore()}else i.drawImage(t,n+r(e.offsetX),c-r(e.offsetY))}const g=new C({x:l/t.width-.5,y:m/t.height-.5});return{imageData:0!==t.width&&0!==t.height?i.getImageData(0,0,t.width,t.height):i.createImageData(1,1),anchorPosition:g}}async _fetchPictureMarkerResource(e,a){const i=e.materialHash;if(!this._pictureMarkerCache.get(i)){const r=(await t(e.cim.url,{responseType:\"image\",signal:a&&a.signal})).data;this._pictureMarkerCache.set(i,r)}}_imageDataToCanvas(e){this._imageDataCanvas||(this._imageDataCanvas=document.createElement(\"canvas\"));const t=this._imageDataCanvas,i=a(t.getContext(\"2d\"));return t.width=e.width,t.height=e.height,i.putImageData(e,0,0),t}_imageTo32Array(t,i,r,s){this._imageDataCanvas||(this._imageDataCanvas=document.createElement(\"canvas\"));const o=this._imageDataCanvas,n=a(o.getContext(\"2d\"));if(o.width=i,o.height=r,n.drawImage(t,0,0,i,r),s){n.save();const a=new e(s);n.fillStyle=a.toHex(),n.globalCompositeOperation=\"multiply\",n.fillRect(0,0,i,r),n.globalCompositeOperation=\"destination-atop\",n.drawImage(t,0,0,i,r),n.restore()}return new Uint32Array(n.getImageData(0,0,i,r).data.buffer)}_getRasterizedResource(e,t,i,r,s,o){let n,c,h;const l=null,m=null;if(\"text\"===e.type)return this._rasterizeTextResource(e,t,r,s,o);({analyzedCIM:n,hash:c}=x(e,t,s,o));const g=_(r);if(\"CIMPictureMarker\"===e.cim.type){const i=p(e.size,t,s,o)*g,{image:r,width:n,height:c}=a(this._getPictureResource(e,i,p(e.color,t,s,o)));return h={image:r,size:[n,c],sdf:!1,simplePattern:!1,anchorX:e.anchorPoint?e.anchorPoint.x:0,anchorY:e.anchorPoint?e.anchorPoint.y:0},h}w(n,g,{preserveOutlineWidth:!1});const u=n;c+=i,r&&(c+=JSON.stringify(r));const f=this._resourceCache;return f.has(c)?f.get(c):(h=this._rasterizer.rasterizeJSONResource({cim:u,type:e.type,url:e.url,mosaicHash:c,size:l,path:m},window.devicePixelRatio||1,this._avoidSDF),f.set(c,h),h)}_rasterizeTextResource(e,t,a,i,r){const s=_(a),o=p(e.text,t,i,r);if(!o||0===o.length)return null;const n=p(e.fontName,t,i,r),c=p(e.style,t,i,r),h=p(e.weight,t,i,r),l=p(e.decoration,t,i,r),m=p(e.size,t,i,r)*s,g=p(e.horizontalAlignment,t,i,r),u=p(e.verticalAlignment,t,i,r),f=y(p(e.color,t,i,r)),d=y(p(e.outlineColor,t,i,r)),w={color:f,size:m,horizontalAlignment:g,verticalAlignment:u,font:{family:n,style:c,weight:h,decoration:l},halo:{size:p(e.outlineSize,t,i,r)||0,color:d,style:c},pixelRatio:1,premultiplyColors:!this._avoidSDF};return this._textRasterizer.rasterizeText(o,w)}_getPictureResource(e,t,a){const i=this._pictureMarkerCache.get(e.materialHash);if(!i)return null;const s=i.height/i.width,o=t?s>1?r(t):r(t)/s:i.width,n=t?s>1?r(t)*s:r(t):i.height;return{image:this._imageTo32Array(i,o,n,a),width:o,height:n}}}function I(e,t,a,i){const r=1,s=-t/2+r,o=t/2-r,n=a/2-r,c=-a/2+r;switch(e){case\"esriGeometryPoint\":return{x:0,y:0};case\"esriGeometryPolyline\":return{paths:[[[s,0],[0,0],[o,0]]]};default:return\"legend\"===i?{rings:[[[s,n],[o,0],[o,c],[s,c],[s,n]]]}:{rings:[[[s,n],[o,n],[o,c],[s,c],[s,n]]]}}}function x(e,t,a,i){let r,s;if(\"function\"==typeof e.materialHash){r=(0,e.materialHash)(t,a,i),s=o(e.cim,e.materialOverrides)}else r=e.materialHash,s=e.cim;return{analyzedCIM:s,hash:r}}export{z as CIMSymbolRasterizer,M as GeometryStyle};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIw0B,IAAI;AAAE,CAAC,SAAS,GAAE;AAAC,IAAE,SAAO,UAAS,EAAE,UAAQ;AAAS,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE,OAAG,KAAG,EAAE,cAAY,EAAE,cAAY;AAA1C,IAA4C,IAAE,KAAG;AAAG,IAAM,IAAN,MAAO;AAAA,EAAC,YAAY,GAAEA,IAAE;AAAC,SAAK,oBAAkB,GAAE,KAAK,YAAUA,IAAE,KAAK,iBAAe,oBAAI,OAAI,KAAK,mBAAiB,MAAK,KAAK,sBAAoB,oBAAI,OAAI,KAAK,kBAAgB,IAAI,KAAE,KAAK,sBAAoB,IAAI,KAAE,KAAK,cAAY,IAAIC,GAAE,KAAK,mBAAmB;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,MAAM,wBAAwB,GAAED,IAAE,GAAE,GAAEE,IAAEC,IAAEC,IAAE,GAAE;AAAC,QAAG,CAAC,EAAE,QAAO;AAAK,UAAK,EAAC,MAAK,EAAC,IAAE;AAAE,QAAG,CAAC,KAAG,yBAAuB,EAAE,QAAM,CAAC,EAAE,OAAO,QAAO;AAAK,UAAK,EAAC,QAAOC,GAAC,IAAE;AAAE,IAAAF,OAAIA,KAAE,EAAEE,EAAC;AAAG,UAAM,IAAE,MAAM,GAAE,uBAAuB,GAAEL,IAAE,KAAK,mBAAkBE,IAAEC,IAAEC,IAAE,CAAC;AAAE,SAAK,qBAAmB,KAAK,mBAAiB,SAAS,cAAc,QAAQ;AAAG,UAAME,KAAE,KAAK,kBAAiB,IAAE,KAAK,qBAAoB,IAAE,CAAC;AAAE,OAAE,eAAe,GAAE,GAAE,CAAC,GAAE,EAAE,SAAO,KAAG,MAAM,QAAQ,IAAI,CAAC;AAAE,UAAK,EAAC,OAAM,GAAE,QAAOC,GAAC,IAAE,GAAEC,KAAE,EAAEL,IAAE,GAAEI,IAAE,CAAC,GAAEE,KAAE,GAAE,YAAY,GAAED,IAAE,CAAC;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,UAAMC,MAAG,OAAO,oBAAkB,KAAG;AAAE,QAAI,IAAE,GAAE,IAAE,GAAE,IAAE;AAAE,YAAOL,GAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAiB,KAAI;AAAgB;AAAC,cAAIM,KAAE;AAAE,UAAAF,GAAE,QAAM,MAAIE,KAAE,IAAEF,GAAE;AAAO,cAAIT,KAAE;AAAE,UAAAS,GAAE,SAAOF,OAAIP,KAAEO,KAAEE,GAAE,SAAQ,cAAY,MAAIA,GAAE,QAAM,MAAIE,KAAE,IAAEF,GAAE,QAAOA,GAAE,SAAOF,OAAIP,KAAEO,KAAEE,GAAE,UAAS,IAAE,KAAK,IAAIE,IAAEX,EAAC,GAAE,IAAES,GAAE,IAAEA,GAAE,QAAM,GAAE,IAAEA,GAAE,IAAEA,GAAE,SAAO;AAAA,QAAC;AAAC;AAAA,MAAM,KAAI;AAAgB;AAAC,cAAIE,KAAE;AAAE,UAAAF,GAAE,SAAOF,OAAII,KAAEJ,KAAEE,GAAE,SAAQ,IAAEE,IAAE,IAAEF,GAAE,IAAEA,GAAE,SAAO;AAAE,gBAAMT,KAAES,GAAE,IAAE,IAAE,IAAE,GAAEG,MAAGH,GAAE,IAAEA,GAAE,SAAO,IAAE,IAAE;AAAE,cAAGT,KAAE,GAAE;AAAC,kBAAK,EAAC,OAAMW,GAAC,IAAEH;AAAE,YAAAG,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGX;AAAA,UAAC;AAAC,cAAGY,KAAE,GAAE;AAAC,kBAAK,EAAC,OAAMD,GAAC,IAAEH;AAAE,YAAAG,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGC,KAAE;AAAA,UAAC;AAAA,QAAC;AAAC;AAAA,MAAM,KAAI,oBAAmB;AAAC,YAAEH,GAAE,IAAEA,GAAE,QAAM,GAAE,IAAEA,GAAE,IAAEA,GAAE,SAAO;AAAE,cAAME,KAAEF,GAAE,IAAE,IAAE,IAAE,GAAET,MAAGS,GAAE,IAAEA,GAAE,SAAO,IAAE,IAAE,GAAEG,KAAEH,GAAE,IAAE,IAAEF,KAAE,GAAEM,MAAGJ,GAAE,IAAEA,GAAE,UAAQ,IAAEF,KAAE,GAAE,EAAC,OAAML,GAAC,IAAEM;AAAE,QAAAG,KAAE,MAAIT,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGS,IAAET,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGS,IAAET,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGS,KAAGC,KAAE,MAAIV,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGU,IAAEV,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGU,IAAEV,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGU,KAAGZ,KAAE,MAAIE,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGF,KAAE,GAAEE,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGF,KAAE,IAAGa,KAAEN,OAAIL,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGW,KAAEN,IAAEL,GAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAGW,KAAEN;AAAA,MAAE;AAAA,IAAC;AAAC,IAAAD,GAAE,QAAM,IAAEI,IAAEJ,GAAE,SAAOC,KAAEG;AAAE,UAAM,IAAE;AAAE,IAAAJ,GAAE,SAAO,IAAE,GAAEA,GAAE,UAAQ,IAAE;AAAE,UAAM,IAAEA,GAAE,WAAW,IAAI,GAAE,IAAEQ,GAAE,eAAe;AAAE,MAAE,UAAU,CAAC,GAAE,CAAC,CAAC,GAAE,EAAE,MAAM,IAAEJ,IAAE,CAAC,IAAEA,EAAC,GAAE,EAAE,UAAU,IAAEA,KAAE,IAAE,GAAEH,KAAEG,KAAE,IAAE,CAAC,GAAE,EAAE,UAAU,GAAE,GAAEJ,GAAE,OAAMA,GAAE,MAAM;AAAE,WAAO,IAAI,EAAE,GAAE,GAAE,GAAE,IAAE,EAAE,WAAW,GAAEE,EAAC,GAAE,EAAE,aAAa,GAAE,GAAEF,GAAE,OAAMA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,GAAEN,IAAE,GAAEE,IAAEE,IAAE;AAAC,UAAM,IAAE,CAAC,GAAEH,KAAED,KAAE,EAAC,cAAaE,IAAE,kBAAiB,KAAK,mBAAkB,QAAOF,GAAC,IAAE;AAAK,QAAI;AAAE,UAAM,EAAE,EAAE,MAAKC,IAAE,KAAK,qBAAoB,GAAE,KAAK,SAAS,GAAE,EAAEG,EAAC;AAAE,eAAU,KAAK,EAAE,wBAAqB,EAAE,IAAI,QAAM,qBAAmB,EAAE,IAAI,QAAM,uBAAqB,EAAE,IAAI,SAAO,MAAI,IAAE,CAAC,IAAG,EAAE,KAAK,KAAK,4BAA4B,GAAEA,EAAC,CAAC,IAAG,KAAG,WAAS,EAAE,QAAM,YAAU,OAAO,EAAE,QAAM,EAAE,KAAK,SAAS,GAAG,MAAI,EAAE,OAAKH,GAAE,GAAE,EAAE,MAAK,EAAE,IAAI,QAAQ;AAAG,WAAO,KAAG,MAAM,QAAQ,IAAI,CAAC,GAAE;AAAA,EAAC;AAAA,EAAC,qBAAqB,GAAED,IAAE,GAAE,GAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAU,KAAK,GAAE;AAAC,WAAG,cAAY,OAAO,EAAE,gBAAc,EAAE,cAAY,EAAE,YAAYJ,IAAEE,IAAEC,EAAC;AAAG,YAAMQ,KAAE,KAAK,uBAAuB,GAAEX,IAAE,GAAE,GAAEE,IAAEC,EAAC;AAAE,UAAG,CAACQ,GAAE;AAAS,UAAIV,KAAE,GAAE,IAAEU,GAAE,WAAS,GAAEI,KAAEJ,GAAE,WAAS,GAAEK,KAAE,OAAG,IAAE,GAAEX,KAAE;AAAE,UAAG,wBAAsB,GAAE;AAAC,cAAMM,KAAE,EAAE,CAAC;AAAE,YAAG,IAAE,EAAE,EAAE,SAAQX,IAAEE,IAAEC,EAAC,IAAEQ,MAAG,GAAEN,KAAE,EAAE,EAAE,SAAQL,IAAEE,IAAEC,EAAC,IAAEQ,MAAG,GAAE,aAAW,EAAE,KAAK,CAAAV,KAAE,EAAE,EAAE,UAASD,IAAEE,IAAEC,EAAC,KAAG,GAAEa,KAAE,CAAC,CAAC,EAAE,mBAAiB,EAAE;AAAA,iBAAwB,WAAS,EAAE,MAAK;AAAC,cAAGf,KAAE,EAAE,EAAE,OAAMD,IAAEE,IAAEC,EAAC,KAAG,GAAE,WAAS,EAAE,oBAAoB,SAAO,EAAE,qBAAoB;AAAA,YAAC,KAAI;AAAO,kBAAE;AAAI;AAAA,YAAM,KAAI;AAAQ,kBAAE;AAAG;AAAA,YAAM;AAAQ,kBAAE;AAAA,UAAC;AAAC,cAAG,WAAS,EAAE,kBAAkB,SAAO,EAAE,mBAAkB;AAAA,YAAC,KAAI;AAAM,cAAAY,KAAE;AAAG;AAAA,YAAM,KAAI;AAAS,cAAAA,KAAE;AAAI;AAAA,YAAM,KAAI;AAAW,cAAAA,KAAE;AAAK;AAAA,YAAM;AAAQ,cAAAA,KAAE;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,cAAMJ,MAAGP,GAAE,KAAK,EAAC,OAAMH,IAAE,iBAAgBe,IAAE,SAAQ,GAAE,SAAQD,IAAE,SAAQ,GAAE,SAAQV,IAAE,oBAAmBM,GAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,eAAeP,EAAC;AAAA,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,UAAMJ,KAAE,SAAS,cAAc,QAAQ,GAAE,IAAE,EAAEA,GAAE,WAAW,IAAI,CAAC;AAAE,QAAIG,KAAE,GAAEC,KAAE,GAAE,IAAE,GAAEH,KAAE;AAAE,UAAM,IAAE,CAAC;AAAE,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,YAAMD,KAAE,EAAE,CAAC,GAAEe,KAAEf,GAAE;AAAmB,UAAG,CAACe,GAAE;AAAS,YAAMC,KAAED,GAAE,MAAKE,KAAEjB,GAAE,SAAQK,KAAEL,GAAE,SAAQkB,KAAElB,GAAE,SAAQ,IAAEA,GAAE,SAAQM,KAAEN,GAAE,mBAAiB;AAAG,UAAI,IAAEA,GAAE,OAAM,IAAE,EAAEiB,EAAC,IAAED,GAAE,CAAC,KAAG,MAAGE,KAAG,IAAE,EAAEb,EAAC,IAAEW,GAAE,CAAC,KAAG,MAAG,IAAGT,KAAE,IAAES,GAAE,CAAC,GAAER,KAAE,IAAEQ,GAAE,CAAC;AAAE,UAAG,GAAE;AAAC,QAAAV,OAAI,IAAE,CAAC;AAAG,cAAMK,KAAE,KAAK,IAAI,IAAE,KAAK,KAAG,GAAG,GAAEX,KAAE,KAAK,IAAI,IAAE,KAAK,KAAG,GAAG,GAAEY,KAAE,IAAEZ,KAAE,IAAEW,IAAEE,KAAE,IAAEF,KAAE,IAAEX,IAAEE,KAAE,IAAEF,KAAEQ,KAAEG,IAAER,KAAE,IAAEQ,KAAEH,KAAER,IAAEI,KAAEG,KAAEP,KAAEQ,KAAEG,IAAEQ,KAAEZ,KAAEI,KAAEH,KAAER,IAAEC,KAAEM,KAAEP,KAAE,IAAEW,IAAES,KAAEb,KAAEI,KAAE,IAAEX;AAAE,YAAE,KAAK,IAAIY,IAAEV,IAAEE,IAAEH,EAAC,GAAE,IAAE,KAAK,IAAIY,IAAEV,IAAEgB,IAAEC,EAAC,GAAEb,KAAE,KAAK,IAAIK,IAAEV,IAAEE,IAAEH,EAAC,GAAEO,KAAE,KAAK,IAAIK,IAAEV,IAAEgB,IAAEC,EAAC;AAAA,MAAC;AAAC,MAAAjB,KAAE,IAAEA,KAAE,IAAEA,IAAEC,KAAE,IAAEA,KAAE,IAAEA,IAAE,IAAEG,KAAE,IAAEA,KAAE,GAAEN,KAAEO,KAAEP,KAAEO,KAAEP;AAAE,YAAMoB,KAAE,EAAE,gBAAgBN,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,CAAC;AAAE,MAAAM,GAAE,KAAK,IAAI,IAAI,kBAAkBN,GAAE,MAAM,MAAM,CAAC;AAAE,YAAMN,KAAE,EAAC,SAAQQ,IAAE,SAAQZ,IAAE,iBAAgBC,IAAE,OAAM,GAAE,iBAAgBe,IAAE,SAAQH,IAAE,SAAQ,EAAC;AAAE,QAAE,KAAKT,EAAC;AAAA,IAAC;AAAC,IAAAT,GAAE,QAAM,IAAEG,IAAEH,GAAE,SAAOC,KAAEG;AAAE,UAAMW,KAAE,CAACZ,IAAEa,KAAEf;AAAE,aAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,KAAI;AAAC,YAAMU,KAAE,EAAE,CAAC,GAAEX,KAAE,KAAK,mBAAmBW,GAAE,eAAe,GAAER,KAAEQ,GAAE,gBAAgB,OAAMP,KAAEO,GAAE,gBAAgB,QAAOQ,KAAEJ,KAAEZ,MAAG,MAAGQ,GAAE,UAASV,KAAEe,KAAEZ,MAAG,MAAGO,GAAE;AAAS,UAAGA,GAAE,OAAM;AAAC,cAAMC,MAAG,MAAID,GAAE,SAAO,KAAK,KAAG;AAAI,UAAE,KAAK,GAAE,EAAE,UAAU,EAAEA,GAAE,OAAO,GAAE,CAAC,EAAEA,GAAE,OAAO,CAAC,GAAE,EAAE,UAAUI,IAAEC,EAAC,GAAE,EAAE,OAAOJ,EAAC,GAAE,EAAE,UAAU,CAACG,IAAE,CAACC,EAAC,GAAE,EAAE,UAAUhB,IAAEmB,IAAElB,EAAC,GAAE,EAAE,QAAQ;AAAA,MAAC,MAAM,GAAE,UAAUD,IAAEmB,KAAE,EAAER,GAAE,OAAO,GAAEV,KAAE,EAAEU,GAAE,OAAO,CAAC;AAAA,IAAC;AAAC,UAAM,IAAE,IAAI,EAAE,EAAC,GAAEI,KAAEf,GAAE,QAAM,KAAG,GAAEgB,KAAEhB,GAAE,SAAO,IAAE,CAAC;AAAE,WAAM,EAAC,WAAU,MAAIA,GAAE,SAAO,MAAIA,GAAE,SAAO,EAAE,aAAa,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM,IAAE,EAAE,gBAAgB,GAAE,CAAC,GAAE,gBAAe,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,4BAA4B,GAAE,GAAE;AAAC,UAAM,IAAE,EAAE;AAAa,QAAG,CAAC,KAAK,oBAAoB,IAAI,CAAC,GAAE;AAAC,YAAME,MAAG,MAAM,EAAE,EAAE,IAAI,KAAI,EAAC,cAAa,SAAQ,QAAO,KAAG,EAAE,OAAM,CAAC,GAAG;AAAK,WAAK,oBAAoB,IAAI,GAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAmB,GAAE;AAAC,SAAK,qBAAmB,KAAK,mBAAiB,SAAS,cAAc,QAAQ;AAAG,UAAMF,KAAE,KAAK,kBAAiB,IAAE,EAAEA,GAAE,WAAW,IAAI,CAAC;AAAE,WAAOA,GAAE,QAAM,EAAE,OAAMA,GAAE,SAAO,EAAE,QAAO,EAAE,aAAa,GAAE,GAAE,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE,GAAEE,IAAEC,IAAE;AAAC,SAAK,qBAAmB,KAAK,mBAAiB,SAAS,cAAc,QAAQ;AAAG,UAAMC,KAAE,KAAK,kBAAiB,IAAE,EAAEA,GAAE,WAAW,IAAI,CAAC;AAAE,QAAGA,GAAE,QAAM,GAAEA,GAAE,SAAOF,IAAE,EAAE,UAAUF,IAAE,GAAE,GAAE,GAAEE,EAAC,GAAEC,IAAE;AAAC,QAAE,KAAK;AAAE,YAAM,IAAE,IAAI,EAAEA,EAAC;AAAE,QAAE,YAAU,EAAE,MAAM,GAAE,EAAE,2BAAyB,YAAW,EAAE,SAAS,GAAE,GAAE,GAAED,EAAC,GAAE,EAAE,2BAAyB,oBAAmB,EAAE,UAAUF,IAAE,GAAE,GAAE,GAAEE,EAAC,GAAE,EAAE,QAAQ;AAAA,IAAC;AAAC,WAAO,IAAI,YAAY,EAAE,aAAa,GAAE,GAAE,GAAEA,EAAC,EAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAEF,IAAE,GAAEE,IAAEC,IAAEC,IAAE;AAAC,QAAI,GAAEH,IAAE;AAAE,UAAMc,KAAE,MAAKC,KAAE;AAAK,QAAG,WAAS,EAAE,KAAK,QAAO,KAAK,uBAAuB,GAAEhB,IAAEE,IAAEC,IAAEC,EAAC;AAAE,KAAC,EAAC,aAAY,GAAE,MAAKH,GAAC,IAAE,EAAE,GAAED,IAAEG,IAAEC,EAAC;AAAG,UAAM,IAAE,EAAEF,EAAC;AAAE,QAAG,uBAAqB,EAAE,IAAI,MAAK;AAAC,YAAMW,KAAE,EAAE,EAAE,MAAKb,IAAEG,IAAEC,EAAC,IAAE,GAAE,EAAC,OAAMF,IAAE,OAAMiB,IAAE,QAAOlB,GAAC,IAAE,EAAE,KAAK,oBAAoB,GAAEY,IAAE,EAAE,EAAE,OAAMb,IAAEG,IAAEC,EAAC,CAAC,CAAC;AAAE,aAAO,IAAE,EAAC,OAAMF,IAAE,MAAK,CAACiB,IAAElB,EAAC,GAAE,KAAI,OAAG,eAAc,OAAG,SAAQ,EAAE,cAAY,EAAE,YAAY,IAAE,GAAE,SAAQ,EAAE,cAAY,EAAE,YAAY,IAAE,EAAC,GAAE;AAAA,IAAC;AAAC,MAAE,GAAE,GAAE,EAAC,sBAAqB,MAAE,CAAC;AAAE,UAAMI,KAAE;AAAE,IAAAJ,MAAG,GAAEC,OAAID,MAAG,KAAK,UAAUC,EAAC;AAAG,UAAMgB,KAAE,KAAK;AAAe,WAAOA,GAAE,IAAIjB,EAAC,IAAEiB,GAAE,IAAIjB,EAAC,KAAG,IAAE,KAAK,YAAY,sBAAsB,EAAC,KAAII,IAAE,MAAK,EAAE,MAAK,KAAI,EAAE,KAAI,YAAWJ,IAAE,MAAKc,IAAE,MAAKC,GAAC,GAAE,OAAO,oBAAkB,GAAE,KAAK,SAAS,GAAEE,GAAE,IAAIjB,IAAE,CAAC,GAAE;AAAA,EAAE;AAAA,EAAC,uBAAuB,GAAED,IAAE,GAAE,GAAEE,IAAE;AAAC,UAAMC,KAAE,EAAE,CAAC,GAAEC,KAAE,EAAE,EAAE,MAAKJ,IAAE,GAAEE,EAAC;AAAE,QAAG,CAACE,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAM,IAAE,EAAE,EAAE,UAASJ,IAAE,GAAEE,EAAC,GAAED,KAAE,EAAE,EAAE,OAAMD,IAAE,GAAEE,EAAC,GAAE,IAAE,EAAE,EAAE,QAAOF,IAAE,GAAEE,EAAC,GAAEa,KAAE,EAAE,EAAE,YAAWf,IAAE,GAAEE,EAAC,GAAEc,KAAE,EAAE,EAAE,MAAKhB,IAAE,GAAEE,EAAC,IAAEC,IAAE,IAAE,EAAE,EAAE,qBAAoBH,IAAE,GAAEE,EAAC,GAAEG,KAAE,EAAE,EAAE,mBAAkBL,IAAE,GAAEE,EAAC,GAAEgB,KAAE,EAAE,EAAE,EAAE,OAAMlB,IAAE,GAAEE,EAAC,CAAC,GAAE,IAAE,EAAE,EAAE,EAAE,cAAaF,IAAE,GAAEE,EAAC,CAAC,GAAE,IAAE,EAAC,OAAMgB,IAAE,MAAKF,IAAE,qBAAoB,GAAE,mBAAkBX,IAAE,MAAK,EAAC,QAAO,GAAE,OAAMJ,IAAE,QAAO,GAAE,YAAWc,GAAC,GAAE,MAAK,EAAC,MAAK,EAAE,EAAE,aAAYf,IAAE,GAAEE,EAAC,KAAG,GAAE,OAAM,GAAE,OAAMD,GAAC,GAAE,YAAW,GAAE,mBAAkB,CAAC,KAAK,UAAS;AAAE,WAAO,KAAK,gBAAgB,cAAcG,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoB,GAAEJ,IAAE,GAAE;AAAC,UAAM,IAAE,KAAK,oBAAoB,IAAI,EAAE,YAAY;AAAE,QAAG,CAAC,EAAE,QAAO;AAAK,UAAMG,KAAE,EAAE,SAAO,EAAE,OAAMC,KAAEJ,KAAEG,KAAE,IAAE,EAAEH,EAAC,IAAE,EAAEA,EAAC,IAAEG,KAAE,EAAE,OAAM,IAAEH,KAAEG,KAAE,IAAE,EAAEH,EAAC,IAAEG,KAAE,EAAEH,EAAC,IAAE,EAAE;AAAO,WAAM,EAAC,OAAM,KAAK,gBAAgB,GAAEI,IAAE,GAAE,CAAC,GAAE,OAAMA,IAAE,QAAO,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEJ,IAAE,GAAE,GAAE;AAAC,QAAME,KAAE,GAAEC,KAAE,CAACH,KAAE,IAAEE,IAAEE,KAAEJ,KAAE,IAAEE,IAAE,IAAE,IAAE,IAAEA,IAAED,KAAE,CAAC,IAAE,IAAEC;AAAE,UAAO,GAAE;AAAA,IAAC,KAAI;AAAoB,aAAM,EAAC,GAAE,GAAE,GAAE,EAAC;AAAA,IAAE,KAAI;AAAuB,aAAM,EAAC,OAAM,CAAC,CAAC,CAACC,IAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAACC,IAAE,CAAC,CAAC,CAAC,EAAC;AAAA,IAAE;AAAQ,aAAM,aAAW,IAAE,EAAC,OAAM,CAAC,CAAC,CAACD,IAAE,CAAC,GAAE,CAACC,IAAE,CAAC,GAAE,CAACA,IAAEH,EAAC,GAAE,CAACE,IAAEF,EAAC,GAAE,CAACE,IAAE,CAAC,CAAC,CAAC,EAAC,IAAE,EAAC,OAAM,CAAC,CAAC,CAACA,IAAE,CAAC,GAAE,CAACC,IAAE,CAAC,GAAE,CAACA,IAAEH,EAAC,GAAE,CAACE,IAAEF,EAAC,GAAE,CAACE,IAAE,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEH,IAAE,GAAE,GAAE;AAAC,MAAIE,IAAEC;AAAE,MAAG,cAAY,OAAO,EAAE,cAAa;AAAC,IAAAD,MAAG,GAAE,EAAE,cAAcF,IAAE,GAAE,CAAC,GAAEG,KAAE,GAAE,EAAE,KAAI,EAAE,iBAAiB;AAAA,EAAC,MAAM,CAAAD,KAAE,EAAE,cAAaC,KAAE,EAAE;AAAI,SAAM,EAAC,aAAYA,IAAE,MAAKD,GAAC;AAAC;", "names": ["t", "c", "r", "s", "o", "u", "p", "M", "_", "z", "x", "e", "a", "i", "O", "l", "m", "g", "f", "n", "h", "v"]}