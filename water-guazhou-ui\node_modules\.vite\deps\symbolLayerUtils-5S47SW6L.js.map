{"version": 3, "sources": ["../../@arcgis/core/symbols/support/symbolLayerUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../request.js\";import r from\"../../core/Error.js\";import o from\"../../core/ItemCache.js\";import{isSome as t,unwrapOrThrow as n}from\"../../core/maybe.js\";import{size as i,create as s}from\"../../geometry/support/aaBoundingBox.js\";import{objectSymbolLayerSizeWithResourceSize as u,objectSymbolLayerPrimitiveBoundingBox as c}from\"./symbolLayerUtils3D.js\";let a=m();function m(){return new o(50)}function y(){a=m()}function f(e,o){if(\"icon\"===e.type)return p(e,o);if(\"object\"===e.type)return d(e,o);throw new r(\"symbol3d:unsupported-symbol-layer\",\"computeLayerSize only works with symbol layers of type Icon and Object\")}async function l(e,o){if(\"icon\"===e.type)return h(e,o);if(\"object\"===e.type)return w(e,o);throw new r(\"symbol3d:unsupported-symbol-layer\",\"computeLayerSize only works with symbol layers of type Icon and Object\")}async function p(e,o){if(e.resource?.href)return b(e.resource.href).then((e=>[e.width,e.height]));if(e.resource?.primitive)return t(o)?[o,o]:[256,256];throw new r(\"symbol3d:invalid-symbol-layer\",\"symbol layers of type Icon must have either an href or a primitive resource\")}function h(e,r){return p(e,r).then((r=>{if(null==e.size)return r;const o=r[0]/r[1];return o>1?[e.size,e.size/o]:[e.size*o,e.size]}))}function b(r){return e(r,{responseType:\"image\"}).then((e=>e.data))}function d(e,r){return j(e,r).then((e=>i(e)))}async function w(e,r){const o=await d(e,r);return u(o,e)}async function j(e,o){if(!e.isPrimitive){const r=n(e.resource?.href),o=a.get(r);if(void 0!==o)return o;const t=await import(\"../../views/3d/layers/graphics/objectResourceUtils.js\"),i=await t.fetch(r,{disableTextures:!0});return a.put(r,i.referenceBoundingBox),i.referenceBoundingBox}let i=null;if(e.resource&&e.resource.primitive&&(i=s(c(e.resource.primitive)),t(o)))for(let r=0;r<i.length;r++)i[r]*=o;return i?Promise.resolve(i):Promise.reject(new r(\"symbol:invalid-resource\",\"The symbol does not have a valid resource\"))}export{y as clearBoundingBoxCache,p as computeIconLayerResourceSize,f as computeLayerResourceSize,l as computeLayerSize,d as computeObjectLayerResourceSize};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgX,IAAIA,KAAE,EAAE;AAAE,SAAS,IAAG;AAAC,SAAO,IAAI,EAAE,EAAE;AAAC;AAAC,SAAS,IAAG;AAAC,EAAAA,KAAE,EAAE;AAAC;AAAC,SAAS,EAAEC,IAAE,GAAE;AAAC,MAAG,WAASA,GAAE,KAAK,QAAO,EAAEA,IAAE,CAAC;AAAE,MAAG,aAAWA,GAAE,KAAK,QAAO,EAAEA,IAAE,CAAC;AAAE,QAAM,IAAI,EAAE,qCAAoC,wEAAwE;AAAC;AAAC,eAAe,EAAEA,IAAE,GAAE;AAAC,MAAG,WAASA,GAAE,KAAK,QAAO,EAAEA,IAAE,CAAC;AAAE,MAAG,aAAWA,GAAE,KAAK,QAAO,EAAEA,IAAE,CAAC;AAAE,QAAM,IAAI,EAAE,qCAAoC,wEAAwE;AAAC;AAAC,eAAe,EAAEA,IAAE,GAAE;AAJl2B;AAIm2B,OAAG,KAAAA,GAAE,aAAF,mBAAY,KAAK,QAAO,EAAEA,GAAE,SAAS,IAAI,EAAE,KAAM,CAAAA,OAAG,CAACA,GAAE,OAAMA,GAAE,MAAM,CAAE;AAAE,OAAG,KAAAA,GAAE,aAAF,mBAAY,UAAU,QAAO,EAAE,CAAC,IAAE,CAAC,GAAE,CAAC,IAAE,CAAC,KAAI,GAAG;AAAE,QAAM,IAAI,EAAE,iCAAgC,6EAA6E;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,EAAED,IAAEC,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAG,QAAMD,GAAE,KAAK,QAAOC;AAAE,UAAM,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAE,WAAO,IAAE,IAAE,CAACD,GAAE,MAAKA,GAAE,OAAK,CAAC,IAAE,CAACA,GAAE,OAAK,GAAEA,GAAE,IAAI;AAAA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,SAAO,EAAEA,IAAE,EAAC,cAAa,QAAO,CAAC,EAAE,KAAM,CAAAD,OAAGA,GAAE,IAAK;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAO,EAAED,IAAEC,EAAC,EAAE,KAAM,CAAAD,OAAG,EAAEA,EAAC,CAAE;AAAC;AAAC,eAAe,EAAEA,IAAEC,IAAE;AAAC,QAAM,IAAE,MAAM,EAAED,IAAEC,EAAC;AAAE,SAAO,EAAE,GAAED,EAAC;AAAC;AAAC,eAAe,EAAEA,IAAE,GAAE;AAJn6C;AAIo6C,MAAG,CAACA,GAAE,aAAY;AAAC,UAAMC,KAAE,GAAE,KAAAD,GAAE,aAAF,mBAAY,IAAI,GAAEE,KAAEH,GAAE,IAAIE,EAAC;AAAE,QAAG,WAASC,GAAE,QAAOA;AAAE,UAAMC,KAAE,MAAM,OAAO,mCAAuD,GAAEC,KAAE,MAAMD,GAAE,MAAMF,IAAE,EAAC,iBAAgB,KAAE,CAAC;AAAE,WAAOF,GAAE,IAAIE,IAAEG,GAAE,oBAAoB,GAAEA,GAAE;AAAA,EAAoB;AAAC,MAAI,IAAE;AAAK,MAAGJ,GAAE,YAAUA,GAAE,SAAS,cAAY,IAAE,EAAEK,GAAEL,GAAE,SAAS,SAAS,CAAC,GAAE,EAAE,CAAC,GAAG,UAAQC,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,GAAEA,EAAC,KAAG;AAAE,SAAO,IAAE,QAAQ,QAAQ,CAAC,IAAE,QAAQ,OAAO,IAAI,EAAE,2BAA0B,2CAA2C,CAAC;AAAC;", "names": ["a", "e", "r", "o", "t", "i", "c"]}