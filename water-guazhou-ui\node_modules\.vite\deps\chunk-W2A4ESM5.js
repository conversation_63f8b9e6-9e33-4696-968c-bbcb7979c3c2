import {
  T
} from "./chunk-ZOZF7ATM.js";
import {
  A,
  C,
  E,
  M,
  N as N2,
  S as S3,
  V,
  l as l2
} from "./chunk-5VENLL23.js";
import {
  t as t2
} from "./chunk-Q7KDWWJV.js";
import {
  F,
  J,
  N,
  S as S2,
  f as f2,
  j,
  q,
  y
} from "./chunk-6KZTVN32.js";
import {
  f,
  n2 as n,
  p
} from "./chunk-IZLLLMFE.js";
import {
  a
} from "./chunk-BS3GJQ77.js";
import {
  L,
  S,
  m
} from "./chunk-RFYOGM4H.js";
import {
  i
} from "./chunk-57XIOVP5.js";
import {
  m as m2
} from "./chunk-6ILWLF72.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  u
} from "./chunk-GZGAQUSK.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/widgets/Legend/styles/support/relationshipUtils.js
var b = "esri-relationship-ramp";
var u2 = `${b}--diamond`;
var c = `${b}--square`;
var f3 = "http://www.w3.org/2000/svg";
var p2 = { diamondContainer: `${u2}__container`, diamondLeftCol: `${u2}__left-column`, diamondRightCol: `${u2}__right-column`, diamondMidCol: `${u2}__middle-column`, diamondMidColLabel: `${u2}__middle-column--label`, diamondMidColRamp: `${u2}__middle-column--ramp`, squareTable: `${c}__table`, squareTableRow: `${c}__table-row`, squareTableCell: `${c}__table-cell`, squareTableLabel: `${c}__table-label`, squareTableLabelLeftBottom: `${c}__table-label--left-bottom`, squareTableLabelRightBottom: `${c}__table-label--right-bottom`, squareTableLabelLeftTop: `${c}__table-label--left-top`, squareTableLabelRightTop: `${c}__table-label--right-top` };
function h(e4, l5, a4, r7) {
  const { focus: t4, labels: s6 } = e4, o4 = !!t4, i6 = T2(e4, l5, a4, r7), b4 = { justifyContent: "center" }, u7 = f();
  return o4 ? n("div", { class: p2.diamondContainer, styles: b4 }, n("div", { class: p2.diamondLeftCol }, u7 ? s6.right : s6.left), n("div", { class: p2.diamondMidCol }, n("div", { class: p2.diamondMidColLabel }, s6.top), i6, n("div", { class: p2.diamondMidColLabel }, s6.bottom)), n("div", { class: p2.diamondRightCol }, u7 ? s6.left : s6.right)) : n("div", { class: p2.squareTable }, n("div", { class: p2.squareTableRow }, n("div", { class: p(p2.squareTableCell, p2.squareTableLabel, p2.squareTableLabelRightBottom) }, u7 ? s6.top : s6.left), n("div", { class: p2.squareTableCell }), n("div", { class: p(p2.squareTableCell, p2.squareTableLabel, p2.squareTableLabelLeftBottom) }, u7 ? s6.left : s6.top)), n("div", { class: p2.squareTableRow }, n("div", { class: p2.squareTableCell }), i6, n("div", { class: p2.squareTableCell })), n("div", { class: p2.squareTableRow }, n("div", { class: p(p2.squareTableCell, p2.squareTableLabel, p2.squareTableLabelRightTop) }, u7 ? s6.right : s6.bottom), n("div", { class: p2.squareTableCell }), n("div", { class: p(p2.squareTableCell, p2.squareTableLabel, p2.squareTableLabelLeftTop) }, u7 ? s6.bottom : s6.right)));
}
function k(e4, l5, a4) {
  const r7 = `${a4}_arrowStart`, t4 = `${a4}_arrowEnd`, s6 = "left" === e4, o4 = { markerStart: null, markerEnd: null };
  switch (l5) {
    case "HL":
      s6 ? o4.markerStart = `url(#${t4})` : o4.markerEnd = `url(#${r7})`;
      break;
    case "LL":
      o4.markerStart = `url(#${t4})`;
      break;
    case "LH":
      s6 ? o4.markerEnd = `url(#${r7})` : o4.markerStart = `url(#${t4})`;
      break;
    default:
      o4.markerEnd = `url(#${r7})`;
  }
  return o4;
}
function T2(n6, d, b4, u7, c3 = 60) {
  const { focus: h4, numClasses: T3, colors: _, rotation: q2 } = n6, $ = !!h4, g3 = Math.sqrt(c3 ** 2 + c3 ** 2) + ($ ? 0 : 5), L2 = [], C3 = [], v2 = [], w2 = (c3 || 75) / T3;
  for (let s6 = 0; s6 < T3; s6++) {
    const o4 = s6 * w2;
    for (let i6 = 0; i6 < T3; i6++) {
      const n7 = i6 * w2, d2 = A(_[s6][i6]), m4 = S3(null), b5 = { type: "rect", x: n7, y: o4, width: w2, height: w2 }, u8 = N2(d2);
      u8 && L2.push(u8);
      const c4 = M(b5, d2.fill, m4, null);
      c4 && C3.push(c4), v2.push(E(b5));
    }
  }
  const y2 = 10, x2 = 15, R2 = 15, E2 = 10;
  let M2 = null;
  $ || (M2 = "margin: -15px -15px -18px -15px");
  const S5 = k("left", h4, d), j3 = k("right", h4, d), B = V(v2), H2 = C(B, g3, g3, 0, false, q2, false), U = C(B, g3, g3, 0, false, $ ? -45 : null, false), W = { filter: q(u7), opacity: null == b4 ? "" : `${b4}` };
  return n("div", { styles: W, class: $ ? p2.diamondMidColRamp : p2.squareTableCell }, n("svg", { xmlns: f3, width: g3, height: g3, style: M2 }, n("defs", null, n("marker", { id: `${d}_arrowStart`, markerWidth: "10", markerHeight: "10", refX: "5", refY: "5", markerUnits: "strokeWidth", orient: "auto" }, n("polyline", { points: "0,0 5,5 0,10", fill: "none", stroke: "#555555", "stroke-width": "1" })), n("marker", { id: `${d}_arrowEnd`, markerWidth: "10", markerHeight: "10", refX: "0", refY: "5", markerUnits: "strokeWidth", orient: "auto" }, n("polyline", { points: "5,0 0,5 5,10", fill: "none", stroke: "#555555", "stroke-width": "1" })), L2), n("g", { transform: H2 }, C3), n("g", { transform: U }, n("line", { fill: "none", stroke: "#555555", "stroke-width": "1", "marker-start": S5.markerStart, "marker-end": S5.markerEnd, x1: -y2, y1: c3 - x2, x2: -y2, y2: x2 }), n("line", { fill: "none", stroke: "#555555", "stroke-width": "1", "marker-start": j3.markerStart, "marker-end": j3.markerEnd, x1: R2, y1: c3 + E2, x2: c3 - R2, y2: c3 + E2 }))));
}

// node_modules/@arcgis/core/renderers/support/numberUtils.js
var n2 = /^-?(\d+)(\.(\d+))?$/i;
function e2(t4, n6) {
  return t4 - n6;
}
function r3(t4, n6) {
  let e4, r7;
  return e4 = Number(t4.toFixed(n6)), e4 < t4 ? r7 = e4 + 1 / 10 ** n6 : (r7 = e4, e4 -= 1 / 10 ** n6), e4 = Number(e4.toFixed(n6)), r7 = Number(r7.toFixed(n6)), [e4, r7];
}
function o(t4, n6, e4, r7, o4) {
  const i6 = l3(t4, n6, e4, r7), u7 = null == i6.previous || i6.previous <= o4, s6 = null == i6.next || i6.next <= o4;
  return u7 && s6 || i6.previous + i6.next <= 2 * o4;
}
function i2(t4) {
  const e4 = String(t4), r7 = e4.match(n2);
  if (r7 && r7[1]) return { integer: r7[1].split("").length, fractional: r7[3] ? r7[3].split("").length : 0 };
  if (e4.toLowerCase().includes("e")) {
    const t5 = e4.split("e"), n6 = t5[0], r8 = t5[1];
    if (n6 && r8) {
      const t6 = Number(n6);
      let e5 = Number(r8);
      const o4 = e5 > 0;
      o4 || (e5 = Math.abs(e5));
      const l5 = i2(t6);
      return o4 ? (l5.integer += e5, e5 > l5.fractional ? l5.fractional = 0 : l5.fractional -= e5) : (l5.fractional += e5, e5 > l5.integer ? l5.integer = 1 : l5.integer -= e5), l5;
    }
  }
  return { integer: 0, fractional: 0 };
}
function l3(t4, n6, e4, r7) {
  const o4 = { previous: null, next: null };
  if (null != e4) {
    const r8 = t4 - e4, i6 = n6 - e4 - r8;
    o4.previous = Math.floor(Math.abs(100 * i6 / r8));
  }
  if (null != r7) {
    const e5 = r7 - t4, i6 = r7 - n6 - e5;
    o4.next = Math.floor(Math.abs(100 * i6 / e5));
  }
  return o4;
}
function u3(t4, n6 = {}) {
  const l5 = t4.slice(0), { tolerance: u7 = 2, strictBounds: s6 = false, indexes: c3 = l5.map((t5, n7) => n7) } = n6;
  c3.sort(e2);
  for (let e4 = 0; e4 < c3.length; e4++) {
    const t5 = c3[e4], n7 = l5[t5], a4 = 0 === t5 ? null : l5[t5 - 1], f4 = t5 === l5.length - 1 ? null : l5[t5 + 1], m4 = i2(n7).fractional;
    if (m4) {
      let i6, c4 = 0, g3 = false;
      for (; c4 <= m4 && !g3; ) {
        const t6 = r3(n7, c4);
        i6 = s6 && 0 === e4 ? t6[1] : t6[0], g3 = o(n7, i6, a4, f4, u7), c4++;
      }
      g3 && (l5[t5] = i6);
    }
  }
  return l5;
}
var s2 = { maximumFractionDigits: 20 };
function c2(n6) {
  return m(n6, s2);
}

// node_modules/@arcgis/core/widgets/Legend/support/utils.js
var s3 = "<";
var i3 = ">";
var r4 = S("short-date");
function o2(l5, a4, o4, n6) {
  let e4 = "";
  0 === a4 ? e4 = `${s3} ` : a4 === o4 && (e4 = `${i3} `);
  let u7 = null;
  return u7 = n6 ? L(l5, r4) : c2(l5), e4 + u7;
}
var n3 = ["data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwcdIkqhiWn5fHwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu6JrzFUAAAAASUVORK5CYII=", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwaeIkqhiWl5/HwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu6sKxboAAAAASUVORK5CYII=", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwadJEqhiWl5fPwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu75+IUcAAAAASUVORK5CYII="];
async function e3(A2) {
  if (!("visualVariables" in A2) || !A2.visualVariables) return null;
  const l5 = A2.visualVariables.find((A3) => "color" === A3.type);
  if (!l5) return null;
  let t4 = null, a4 = null;
  if (l5.stops) {
    if (1 === l5.stops.length) return l5.stops[0].color;
    t4 = l5.stops[0].value, a4 = l5.stops[l5.stops.length - 1].value;
  }
  const s6 = null != t4 && null != a4 ? t4 + (a4 - t4) / 2 : 0, { getColor: i6 } = await import("./visualVariableUtils-572M5EGS.js");
  return i6(l5, s6) ?? null;
}
async function u4(A2, l5) {
  const t4 = A2.trailCap, s6 = A2.trailWidth || 1, i6 = l5 || await e3(A2) || A2.color;
  return new m2({ cap: t4, color: i6, width: s6 });
}

// node_modules/@arcgis/core/widgets/Legend/support/colorRampUtils.js
var o3 = new l([64, 64, 64]);
function n4(l5, o4) {
  const n6 = [], r7 = l5.length - 1;
  return 5 === l5.length ? n6.push(0, 2, 4) : n6.push(0, r7), l5.map((l6, t4) => n6.includes(t4) ? o2(l6, t4, r7, o4) : null);
}
async function r5(l5, e4, o4) {
  let r7 = false, a4 = [], u7 = [];
  if (l5.stops) {
    const e5 = l5.stops;
    a4 = e5.map((l6) => l6.value), r7 = e5.some((l6) => !!l6.label), r7 && (u7 = e5.map((l6) => l6.label));
  }
  const s6 = a4[0], i6 = a4[a4.length - 1];
  if (null == s6 && null == i6) return null;
  const c3 = r7 ? null : n4(a4, o4 ?? false);
  return (await Promise.all(a4.map(async (o5, n6) => ({ value: o5, color: "opacity" === l5.type ? await t3(o5, l5, e4) : (await import("./visualVariableUtils-572M5EGS.js")).getColor(l5, o5), label: r7 ? u7[n6] : (c3 == null ? void 0 : c3[n6]) ?? "" })))).reverse();
}
async function t3(e4, n6, r7 = o3) {
  const t4 = new l(r7), a4 = (await import("./visualVariableUtils-572M5EGS.js")).getOpacity(n6, e4);
  return null != a4 && (t4.a = a4), t4;
}
function a2(l5) {
  let e4 = false, o4 = [], r7 = [];
  o4 = l5.map((l6) => l6.value), e4 = l5.some((l6) => !!l6.label), e4 && (r7 = l5.map((l6) => l6.label ?? ""));
  const t4 = o4[0], a4 = o4[o4.length - 1];
  if (null == t4 && null == a4) return null;
  const s6 = e4 ? null : n4(o4, false);
  return o4.map((o5, n6) => ({ value: o5, color: u5(o5, l5), label: e4 ? r7[n6] : (s6 == null ? void 0 : s6[n6]) ?? "" })).reverse();
}
function u5(e4, o4) {
  const { startIndex: n6, endIndex: r7, weight: t4 } = s4(e4, o4);
  if (n6 === r7) return o4[n6].color;
  const a4 = l.blendColors(o4[n6].color, o4[r7].color, t4);
  return new l(a4);
}
function s4(l5, e4) {
  let o4 = 0, n6 = e4.length - 1;
  return e4.some((e5, r7) => l5 < e5.value ? (n6 = r7, true) : (o4 = r7, false)), { startIndex: o4, endIndex: n6, weight: (l5 - e4[o4].value) / (e4[n6].value - e4[o4].value) };
}
function i4(e4, o4) {
  let n6 = [];
  if (e4 && "multipart" === e4.type) e4.colorRamps.reverse().forEach((r7, t4) => {
    0 === t4 ? n6.push({ value: o4.max, color: new l(r7.toColor), label: "high" }) : n6.push({ value: null, color: new l(r7.toColor), label: "" }), t4 === e4.colorRamps.length - 1 ? n6.push({ value: o4.min, color: new l(r7.fromColor), label: "low" }) : n6.push({ value: null, color: new l(r7.fromColor), label: "" });
  });
  else {
    let r7, t4;
    e4 && "algorithmic" === e4.type ? (r7 = e4.fromColor, t4 = e4.toColor) : (r7 = [0, 0, 0, 1], t4 = [255, 255, 255, 1]), n6 = [{ value: o4.max, color: new l(t4), label: "high" }, { value: o4.min, color: new l(r7), label: "low" }];
  }
  return n6;
}

// node_modules/@arcgis/core/widgets/Legend/support/heatmapRampUtils.js
function r6(r7) {
  if (!r7.colorStops) return [];
  const e4 = [...r7.colorStops].filter((o4) => {
    var _a;
    return ((_a = o4.color) == null ? void 0 : _a.a) > 0;
  });
  let t4 = e4.length - 1;
  if (e4 && e4[0]) {
    const r8 = e4[t4];
    r8 && 1 !== r8.ratio && (e4.push(new a({ ratio: 1, color: r8.color })), t4++);
  }
  return e4.map((o4, e5) => {
    var _a, _b;
    let l5 = "";
    return 0 === e5 ? l5 = ((_a = r7.legendOptions) == null ? void 0 : _a.minLabel) || "low" : e5 === t4 && (l5 = ((_b = r7.legendOptions) == null ? void 0 : _b.maxLabel) || "high"), { color: o4.color, label: l5, ratio: o4.ratio };
  }).reverse();
}

// node_modules/@arcgis/core/widgets/Legend/support/relationshipRampUtils.js
var s5 = { HH: 315, HL: 45, LL: 135, LH: 225 };
var l4 = { 2: [["HL", "HH"], ["LL", "LH"]], 3: [["HL", "HM", "HH"], ["ML", "MM", "MH"], ["LL", "LM", "LH"]], 4: [["HL", "HM1", "HM2", "HH"], ["M2L", "M2M1", "M2M2", "M2H"], ["M1L", "M1M1", "M1M2", "M1H"], ["LL", "LM1", "LM2", "LH"]] };
function n5(t4) {
  if (!t4) return;
  const { type: s6 } = t4;
  if (s6.includes("3d")) return T(t4.symbolLayers.getItemAt(0));
  if ("simple-line" === s6) {
    const o4 = y(t4);
    return o4 && o4.color;
  }
  if ("simple-marker" === t4.type && ("x" === t4.style || "cross" === t4.style)) {
    const o4 = y(t4);
    return o4 && o4.color;
  }
  return f2(t4);
}
function H(t4, e4) {
  const o4 = e4.HH.label, r7 = e4.LL.label, s6 = e4.HL.label, l5 = e4.LH.label;
  switch (t4) {
    case "HH":
    default:
      return { top: o4, bottom: r7, left: s6, right: l5 };
    case "HL":
      return { top: s6, bottom: l5, left: r7, right: o4 };
    case "LL":
      return { top: r7, bottom: o4, left: l5, right: s6 };
    case "LH":
      return { top: l5, bottom: s6, left: o4, right: r7 };
  }
}
function i5(t4) {
  const { focus: e4, infos: o4, numClasses: r7 } = t4, s6 = l4[r7], L2 = {};
  o4.forEach((t5) => {
    L2[t5.value] = { label: t5.label, fill: n5(t5.symbol) };
  });
  const i6 = [];
  for (let l5 = 0; l5 < r7; l5++) {
    const t5 = [];
    for (let e5 = 0; e5 < r7; e5++) {
      const o5 = L2[s6[l5][e5]];
      t5.push(o5.fill);
    }
    i6.push(t5);
  }
  return { type: "relationship-ramp", numClasses: r7, focus: e4, colors: i6, labels: H(e4, L2), rotation: u6(e4) };
}
function u6(t4) {
  let e4 = s5[t4];
  return t4 && null == e4 && (e4 = s5.HH), e4 || 0;
}

// node_modules/@arcgis/core/symbols/support/symbolUtils.js
var g = null;
var b2 = [255, 255, 255];
function w(e4, t4) {
  return Math.floor(Math.random() * (t4 - e4 + 1) + e4);
}
function S4(e4, t4, l5) {
  const { backgroundColor: r7, outline: i6, dotSize: o4 } = e4, n6 = l5 && l5.swatchSize || t2.size, a4 = 0.8, c3 = Math.round(n6 * n6 / o4 ** 2 * a4), u7 = window.devicePixelRatio, f4 = document.createElement("canvas"), p4 = n6 * u7;
  f4.width = p4, f4.height = p4, f4.style.width = f4.width / u7 + "px", f4.style.height = f4.height / u7 + "px";
  const h4 = f4.getContext("2d");
  if (r7 && (h4.fillStyle = r7.toCss(true), h4.fillRect(0, 0, p4, p4), h4.fill()), h4.fillStyle = t4.toCss(true), g && g.length / 2 === c3) for (let s6 = 0; s6 < 2 * c3; s6 += 2) {
    const e5 = g[s6], t5 = g[s6 + 1];
    h4.fillRect(e5, t5, o4 * u7, o4 * u7), h4.fill();
  }
  else {
    g = [];
    for (let e5 = 0; e5 < 2 * c3; e5 += 2) {
      const e6 = w(0, p4), t5 = w(0, p4);
      g.push(e6, t5), h4.fillRect(e6, t5, o4 * u7, o4 * u7), h4.fill();
    }
  }
  i6 && (i6.color && (h4.strokeStyle = i6.color.toCss(true)), h4.lineWidth = i6.width, h4.strokeRect(0, 0, p4, p4));
  const y2 = new Image(n6, n6);
  return y2.src = f4.toDataURL(), y2;
}
function v(e4, t4 = {}) {
  const l5 = t4.radius || 40, r7 = 2 * Math.PI * l5, s6 = e4.length, n6 = r7 / s6, a4 = [], c3 = y(t4.outline);
  null != (c3 == null ? void 0 : c3.width) && (c3.width *= 2), (c3 || t4.backgroundColor) && a4.push({ shape: { type: "circle", cx: l5, cy: l5, r: l5 }, fill: t4.backgroundColor, stroke: c3, offset: [0, 0] });
  const u7 = t4.values, f4 = u7 && u7.length === s6 && 100 === u7.reduce((e5, t5) => e5 + t5, 0), p4 = [0];
  for (let i6 = 0; i6 < s6; i6++) {
    let t5 = null;
    f4 && (t5 = u7[i6] * r7 / 100, p4.push(t5 + p4[i6])), a4.push({ shape: { type: "circle", cx: l5, cy: l5, r: l5 / 2 }, fill: [0, 0, 0, 0], stroke: { width: l5, dashArray: `${(t5 ?? n6) / 2} ${r7}`, dashoffset: "-" + (f4 ? p4[i6] / 2 : i6 * (n6 / 2)), color: e4[i6] }, offset: [0, 0] });
  }
  let h4 = null;
  const y2 = 2 * l5 + ((c3 == null ? void 0 : c3.width) || 0), d = t4.holePercentage;
  if (d) {
    c3 && a4.push({ shape: { type: "circle", cx: l5, cy: l5, r: l5 * d }, fill: null, stroke: c3, offset: [0, 0] });
    const e5 = y2 / 2;
    h4 = [[{ shape: { type: "circle", cx: e5, cy: e5, r: e5 }, fill: b2, stroke: c3 ? { ...c3, color: b2 } : null, offset: [0, 0] }, { shape: { type: "circle", cx: e5, cy: e5, r: l5 * d }, fill: [0, 0, 0], stroke: null, offset: [0, 0] }]];
  }
  return l2([a4], [y2, y2], { effectView: t4.effectList, ignoreStrokeWidth: true, masking: h4, rotation: -90 });
}
function x(e4, t4 = {}) {
  const l5 = 24, r7 = 75, i6 = "horizontal" === t4.align, s6 = i6 ? r7 : l5, o4 = i6 ? l5 : r7, { width: n6 = s6, height: a4 = o4, gradient: c3 = true } = t4, u7 = window.devicePixelRatio, f4 = n6 * u7, p4 = a4 * u7, h4 = document.createElement("canvas");
  h4.width = f4, h4.height = p4, h4.style.width = `${n6}px`, h4.style.height = `${a4}px`;
  const y2 = h4.getContext("2d"), d = i6 ? f4 : 0, m4 = i6 ? 0 : p4;
  if (c3) {
    const t5 = y2.createLinearGradient(0, 0, d, m4), l6 = e4.length, r8 = 1 === l6 ? 0 : 1 / (l6 - 1);
    e4.forEach((e5, l7) => t5.addColorStop(l7 * r8, e5.toString())), y2.fillStyle = t5, y2.fillRect(0, 0, f4, p4);
  } else {
    const t5 = i6 ? f4 / e4.length : f4, l6 = i6 ? p4 : p4 / e4.length;
    let r8 = 0, s7 = 0;
    for (const o5 of e4) y2.fillStyle = o5.toString(), y2.fillRect(r8, s7, t5, l6), r8 = i6 ? r8 + t5 : 0, s7 = i6 ? 0 : s7 + l6;
  }
  const g3 = document.createElement("div");
  return g3.style.width = `${n6}px`, g3.style.height = `${a4}px`, k3(g3, t4 == null ? void 0 : t4.effectList), g3.appendChild(h4), g3;
}
function k3(e4, t4) {
  if (!t4) return;
  e4.style.filter = q(t4);
  const l5 = t4.effects;
  if (l5) {
    for (const r7 of l5) if ("drop-shadow" === (r7 == null ? void 0 : r7.type)) {
      r7.offsetX < 0 ? e4.style.marginLeft = `${Math.abs(r7.offsetX)}px` : e4.style.marginRight = `${r7.offsetX}px`;
      break;
    }
  }
}
async function C2(e4, t4) {
  switch (e4.type) {
    case "web-style": {
      const { previewWebStyleSymbol: l5 } = await import("./previewWebStyleSymbol-C4WNNFNZ.js");
      return l5(e4, C2, t4);
    }
    case "label-3d":
    case "line-3d":
    case "mesh-3d":
    case "point-3d":
    case "polygon-3d": {
      const { previewSymbol3D: l5 } = await import("./previewSymbol3D-OXL3SQIC.js");
      return l5(e4, t4);
    }
    case "simple-marker":
    case "simple-line":
    case "simple-fill":
    case "picture-marker":
    case "picture-fill":
    case "text": {
      const { previewSymbol2D: l5 } = await import("./previewSymbol2D-B6BMNVPM.js");
      return l5(e4, t4);
    }
    case "cim": {
      const { previewCIMSymbol: l5 } = await import("./previewCIMSymbol-6JSLL3EW.js");
      return l5(e4, t4);
    }
    default:
      return;
  }
}
function j2(e4) {
  return e4 && "opacity" in e4 ? e4.opacity * j2(e4.parent) : 1;
}
async function R(i6, s6) {
  if (!i6) return;
  const o4 = i6.sourceLayer, n6 = (r(s6) && s6.useSourceLayer ? o4 : i6.layer) ?? o4, a4 = j2(n6);
  if (r(i6.symbol) && (!r(s6) || true !== s6.ignoreGraphicSymbol)) {
    const t4 = "web-style" === i6.symbol.type ? await F(i6.symbol, { ...s6, cache: r(s6) ? s6.webStyleCache : null }) : i6.symbol.clone();
    return S2(t4, null, a4), t4;
  }
  const h4 = (r(s6) ? s6.renderer : null) ?? (n6 && "renderer" in n6 ? n6.renderer : null);
  let y2 = h4 && "getSymbolAsync" in h4 ? await h4.getSymbolAsync(i6, s6) : null;
  if (!y2) return;
  if (y2 = "web-style" === y2.type ? await y2.fetchSymbol({ ...s6, cache: r(s6) ? s6.webStyleCache : null }) : y2.clone(), !(h4 && "visualVariables" in h4 && h4.visualVariables && h4.visualVariables.length)) return S2(y2, null, a4), y2;
  if ("arcadeRequiredForVisualVariables" in h4 && h4.arcadeRequiredForVisualVariables && (t(s6) || t(s6.arcade))) {
    const e4 = { ...e(s6) };
    e4.arcade = await i(), s6 = e4;
  }
  const d = await import("./visualVariableUtils-572M5EGS.js"), m4 = [], g3 = [], b4 = [], w2 = [];
  for (const e4 of h4.visualVariables) switch (e4.type) {
    case "color":
      m4.push(e4);
      break;
    case "opacity":
      g3.push(e4);
      break;
    case "rotation":
      w2.push(e4);
      break;
    case "size":
      e4.target || b4.push(e4);
  }
  const S5 = !!m4.length && m4[m4.length - 1], v2 = S5 ? d.getColor(S5, i6, s6) : null, V2 = !!g3.length && g3[g3.length - 1];
  let x2 = V2 ? d.getOpacity(V2, i6, s6) : null;
  if (null != a4 && (x2 = null != x2 ? x2 * a4 : a4), S2(y2, v2, x2), b4.length) {
    const e4 = d.getAllSizes(b4, i6, s6);
    await J(y2, e4);
  }
  for (const e4 of w2) N(y2, d.getRotationAngle(e4, i6, s6), e4.axis);
  return y2;
}

// node_modules/@arcgis/core/renderers/support/utils.js
var m3 = s.getLogger("esri.renderers.support.utils");
var g2 = S("short-date");
async function h3(e4, t4, i6) {
  r2(e4, t4, () => []).push(...i6);
}
async function b3(t4) {
  var _a, _b;
  const l5 = /* @__PURE__ */ new Map();
  if (!t4) return l5;
  if ("visualVariables" in t4 && t4.visualVariables) {
    const e4 = t4.visualVariables.filter((e5) => "color" === e5.type);
    for (const t5 of e4) {
      const e5 = (await r5(t5) ?? []).map((e6) => e6.color);
      await h3(l5, t5.field || t5.valueExpression, e5);
    }
  }
  if ("heatmap" === t4.type) {
    const e4 = r6(t4).map((e5) => e5.color);
    await h3(l5, t4.field || t4.valueExpression, e4);
  } else if ("pie-chart" === t4.type) {
    for (const e4 of t4.attributes) await h3(l5, e4.field || e4.valueExpression, [e4.color]);
    await h3(l5, "default", [(_a = t4 == null ? void 0 : t4.othersCategory) == null ? void 0 : _a.color, j(t4.backgroundFillSymbol, null)]);
  } else if ("dot-density" === t4.type) {
    for (const e4 of t4.attributes) await h3(l5, e4.field || e4.valueExpression, [e4.color]);
    await h3(l5, "default", [t4.backgroundColor]);
  } else if ("unique-value" === t4.type) if ("predominance" === ((_b = t4.authoringInfo) == null ? void 0 : _b.type)) for (const e4 of t4.uniqueValueInfos ?? []) await h3(l5, e4.value.toString(), [j(e4.symbol, null)]);
  else {
    const e4 = (t4.uniqueValueInfos ?? []).map((e5) => j(e5.symbol, null)), { field: i6, field2: o4, field3: n6, valueExpression: a4 } = t4;
    (i6 || a4) && await h3(l5, i6 || a4, e4), o4 && await h3(l5, o4, e4), n6 && await h3(l5, n6, e4);
  }
  else if ("class-breaks" === t4.type) {
    const e4 = t4.classBreakInfos.map((e5) => j(e5.symbol, null)), { field: i6, valueExpression: o4 } = t4;
    await h3(l5, i6 ?? o4, e4);
  } else "simple" === t4.type && await h3(l5, "default", [j(t4.symbol, null)]);
  return "defaultSymbol" in t4 && t4.defaultSymbol && await h3(l5, "default", [j(t4.defaultSymbol, null)]), l5.forEach((t5, i6) => {
    const o4 = u(t5.filter(Boolean), (e4, t6) => JSON.stringify(e4) === JSON.stringify(t6));
    l5.set(i6, o4);
  }), l5;
}

export {
  i2 as i,
  l3 as l,
  u3 as u,
  s3 as s,
  i3 as i2,
  o2 as o,
  n3 as n,
  e3 as e,
  u4 as u2,
  r5 as r,
  a2 as a,
  u5 as u3,
  i4 as i3,
  r6 as r2,
  b3 as b,
  h,
  i5 as i4,
  u6 as u4,
  S4 as S,
  v,
  x,
  C2 as C,
  R
};
//# sourceMappingURL=chunk-W2A4ESM5.js.map
