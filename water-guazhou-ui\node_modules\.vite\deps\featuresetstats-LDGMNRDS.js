import {
  l
} from "./chunk-XBLAL33X.js";
import {
  B,
  G,
  J,
  V,
  Z,
  v
} from "./chunk-REVHHZEO.js";
import "./chunk-6OFWBRK2.js";
import "./chunk-WC4DQSYX.js";
import {
  f
} from "./chunk-2CLVPBYJ.js";
import {
  e,
  t
} from "./chunk-YFVPK4WM.js";
import "./chunk-3HW44BD3.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/arcade/functions/featuresetstats.js
async function f2(n, t2, r, i, f3, d2) {
  if (1 === i.length) {
    if (J(i[0])) return l(n, i[0], Z(i[1], -1));
    if (V(i[0])) return l(n, i[0].toArray(), Z(i[1], -1));
  } else if (2 === i.length) {
    if (J(i[0])) return l(n, i[0], Z(i[1], -1));
    if (V(i[0])) return l(n, i[0].toArray(), Z(i[1], -1));
    if (G(i[0])) {
      const r2 = await i[0].load(), e2 = await l2(f.create(i[1], r2.getFieldsIndex()), d2, f3);
      return i[0].calculateStatistic(n, e2, Z(i[2], 1e3), t2.abortSignal);
    }
  } else if (3 === i.length && G(i[0])) {
    const r2 = await i[0].load(), e2 = await l2(f.create(i[1], r2.getFieldsIndex()), d2, f3);
    return i[0].calculateStatistic(n, e2, Z(i[2], 1e3), t2.abortSignal);
  }
  return l(n, i, -1);
}
async function l2(n, t2, r) {
  const e2 = n.getVariables();
  if (e2.length > 0) {
    const a = [];
    for (let n2 = 0; n2 < e2.length; n2++) {
      const i2 = { name: e2[n2] };
      a.push(await t2.evaluateIdentifier(r, i2));
    }
    const i = {};
    for (let n2 = 0; n2 < e2.length; n2++) i[e2[n2]] = a[n2];
    return n.parameters = i, n;
  }
  return n;
}
function d(c) {
  "async" === c.mode && (c.functions.stdev = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("stdev", t3, r, e2, n, c));
  }, c.functions.variance = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("variance", t3, r, e2, n, c));
  }, c.functions.average = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("mean", t3, r, e2, n, c));
  }, c.functions.mean = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("mean", t3, r, e2, n, c));
  }, c.functions.sum = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("sum", t3, r, e2, n, c));
  }, c.functions.min = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("min", t3, r, e2, n, c));
  }, c.functions.max = function(n, t2) {
    return c.standardFunctionAsync(n, t2, (t3, r, e2) => f2("max", t3, r, e2, n, c));
  }, c.functions.count = function(u, o) {
    return c.standardFunctionAsync(u, o, (c2, f3, l3) => {
      if (B(l3, 1, 1, u, o), G(l3[0])) return l3[0].count(c2.abortSignal);
      if (J(l3[0]) || v(l3[0])) return l3[0].length;
      if (V(l3[0])) return l3[0].length();
      throw new t(u, e.InvalidParameter, o);
    });
  });
}
export {
  d as registerFunctions
};
//# sourceMappingURL=featuresetstats-LDGMNRDS.js.map
