{"version": 3, "sources": ["../../@arcgis/core/layers/support/rasterFunctions/BaseFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/AspectFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/BaseRasterFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/AspectFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/customBandIndexUtils.js", "../../@arcgis/core/layers/support/rasterFunctions/bandIndexUtils.js", "../../@arcgis/core/layers/support/rasterFunctions/BandArithmeticFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/BandArithmeticFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/ColormapFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/colormaps.js", "../../@arcgis/core/layers/support/rasterFunctions/ColormapFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/CompositeBandFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/CompositeBandFunction.js", "../../@arcgis/core/layers/support/rasterFunctionConstants.js", "../../@arcgis/core/layers/support/rasterFunctions/convolutionUtils.js", "../../@arcgis/core/layers/support/rasterFunctions/ConvolutionFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/ConvolutionFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/ExtractBandFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/ExtractBandFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/LocalFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/localUtils.js", "../../@arcgis/core/layers/support/rasterFunctions/LocalFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/MaskFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/MaskFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/NDVIFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/NDVIFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/RemapFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/RemapFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/SlopeFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/SlopeFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/StatisticsHistogramFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/StatisticsHistogramFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/StretchFunctionArguments.js", "../../@arcgis/core/layers/support/rasterFunctions/StretchFunction.js", "../../@arcgis/core/layers/support/rasterFunctions/rasterFunctionHelper.js", "../../@arcgis/core/layers/support/rasterTransforms/BaseRasterTransform.js", "../../@arcgis/core/layers/support/rasterTransforms/GCSShiftTransform.js", "../../@arcgis/core/layers/support/rasterTransforms/IdentityTransform.js", "../../@arcgis/core/layers/support/rasterTransforms/PolynomialTransform.js", "../../@arcgis/core/layers/support/rasterTransforms/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{JSONSupport as s}from\"../../../core/JSONSupport.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as t}from\"../../../core/accessorSupport/decorators/subclass.js\";let e=class extends s{constructor(){super(...arguments),this.raster=void 0}};r([o({json:{write:!0}})],e.prototype,\"raster\",void 0),e=r([t(\"esri.layers.support.rasterFunctions.AspectFunctionArguments\")],e);const p=e;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./BaseFunctionArguments.js\";var e;let t=e=class extends o{clone(){return new e({raster:this.raster})}};t=e=r([s(\"esri.layers.support.rasterFunctions.AspectFunctionArguments\")],t);const c=t;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{JSONSupport as s}from\"../../../core/JSONSupport.js\";import{isSome as e,isNone as r}from\"../../../core/maybe.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import{cast as n}from\"../../../core/accessorSupport/decorators/cast.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"../PixelBlock.js\";import u from\"../RasterInfo.js\";let p=class extends s{constructor(){super(...arguments),this.functionArguments=null,this.readingBufferSize=0,this.id=-1,this.isNoopProcess=!1,this.rawInputBandIds=[],this.isInputBandIdsSwizzled=!1,this.swizzledBandSelection=[],this.isBranch=!1,this._bindingResult=null}get supportsGPU(){return this._bindingResult.supportsGPU}bind(t,s=!1,r=-1){this.id=r+1;const i=this._getRasterValues();let n=!0;for(let o=0;o<i.length;o++){const r=i[o];if(e(r)&&this._isRasterFunctionValue(r)){const e=r.bind(t,s,this.id+o);if(!e.success)return this._bindingResult=e,e;n=n&&e.supportsGPU}}return!this.rasterInfo||s?(this.sourceRasterInfos=this._getSourceRasterInfos(t),this._bindingResult=this._bindSourceRasters(),this._bindingResult.supportsGPU=n&&this._bindingResult.supportsGPU,this.processInputBandIds(),this._bindingResult):(this._bindingResult={success:!0,supportsGPU:!0},this.processInputBandIds(),this._bindingResult)}process(t){const s=this._getRasterValues(),e=0===s.length?t.pixelBlocks??t.primaryPixelBlocks:s.map((s=>this._readRasterValue(s,t)));return this._processPixels({...t,pixelBlocks:e})}processInputBandIds(){const t=this._getRasterValues().filter(this._isRasterFunctionValue);let s;if(t.length>1){const s=t.map((t=>t.processInputBandIds()[0]));this.rawInputBandIds=s,this.isInputBandIdsSwizzled=this.rawInputBandIds.some(((t,s)=>t!==s));const e=t.filter((t=>\"ExtractBand\"===t.functionName));return e.length&&e.forEach(((t,s)=>{t.isInputBandIdsSwizzled=!0,t.swizzledBandSelection=[s,s,s]})),this.rawInputBandIds}const e=t[0];if(e){if(s=e.processInputBandIds(),e.isInputBandIdsSwizzled)return this.rawInputBandIds=s,s}else{s=[];const{bandCount:t}=this.sourceRasterInfos[0];for(let e=0;e<t;e++)s.push(e)}const r=this._getInputBandIds(s);return this.isInputBandIdsSwizzled=r.some(((t,s)=>t!==s)),this.rawInputBandIds=r,this.rawInputBandIds}getPrimaryRasters(){const t=[],s=[];return this._getPrimaryRasters(this,t,s),{rasters:t,rasterIds:s}}getWebGLProcessorDefinition(){const t=this._getWebGLParameters(),{raster:s,rasters:e}=this.functionArguments;return e&&Array.isArray(e)&&e.length?(t.rasters=e.map((t=>this._isRasterFunctionValue(t)?t.getWebGLProcessorDefinition():\"number\"==typeof t?{name:\"Constant\",parameters:{value:t},pixelType:\"f32\",id:-1,isNoopProcess:!1}:{name:\"Identity\",parameters:{value:t},pixelType:\"f32\",id:-1,isNoopProcess:!1})),t.rasters.some((t=>null!=t))||(t.rasters=null)):this._isRasterFunctionValue(s)&&(t.raster=s.getWebGLProcessorDefinition()),{name:this.functionName,parameters:t,pixelType:this.outputPixelType,id:this.id,isNoopProcess:this.isNoopProcess}}getFlatWebGLFunctionChain(){const t=this.getWebGLProcessorDefinition();if(!t)return null;const s=[t],{parameters:e}=t;let r=e.rasters||e.raster&&[e.raster];for(;r?.length;){s.unshift(...r);const t=[];for(let s=0;s<r.length;s++){const{parameters:e}=r[s],i=e.rasters||e.raster&&[e.raster];i?.length&&t.push(...i)}r=t}for(let n=s.length-1;n>=0;n--)s[n].isNoopProcess&&s.splice(n,1);let i=!1;for(let n=0;n<s.length;n++){const t=s[n];t.id=s.length-n-1;const{rasters:e}=t.parameters;i=i||null!=e&&e.length>1}return{hasBranches:i,functions:s}}_getOutputPixelType(t){return\"unknown\"===this.outputPixelType?t:this.outputPixelType??t}_getWebGLParameters(){return{}}_getInputBandIds(t){return t}_isOutputRoundingNeeded(){const{outputPixelType:t}=this;return(t?.startsWith(\"u\")||t?.startsWith(\"s\"))??!1}_getRasterValues(){const{rasterArgumentNames:t}=this;return\"rasters\"===t[0]?this.functionArguments.rasters??[]:t.map((t=>this.functionArguments[t]))}_getSourceRasterInfos(t){const s=this._getRasterValues(),{rasterInfos:e,rasterIds:r}=t;if(0===s.length)return e;const i=s.map((t=>t&&\"object\"==typeof t&&\"bind\"in t&&t.rasterInfo?t.rasterInfo:\"string\"==typeof t&&r.includes(t)?e[r.indexOf(t)]:\"number\"!=typeof t?e[0]:void 0)),n=i.find((t=>t))??e[0];return i.forEach(((t,s)=>{void 0===t&&(i[s]=n)})),i}_getPrimaryRasterId(t){return t?.url}_getPrimaryRasters(t,s=[],e=[]){for(let r=0;r<t.sourceRasters.length;r++){const i=t.sourceRasters[r];if(\"number\"!=typeof i)if(\"bind\"in i)this._getPrimaryRasters(i,s,e);else{const t=i,r=this._getPrimaryRasterId(t);if(null==r)continue;e.includes(r)||(this.mainPrimaryRasterId===r?(s.unshift(t),e.unshift(r)):(s.push(t),e.push(r)))}}}_isRasterFunctionValue(t){return null!=t&&\"object\"==typeof t&&\"getWebGLProcessorDefinition\"in t}_readRasterValue(t,s){const{primaryPixelBlocks:e}=s;if(r(t)||\"$$\"===t){const t=e[0];return r(t)?null:t.clone()}if(\"string\"==typeof t){const r=s.primaryRasterIds.indexOf(t);return-1===r?null:e[r]}if(\"number\"==typeof t){const s=e[0];if(r(s))return null;const{width:i,height:n,pixelType:o,mask:u}=s,p=u?new Uint8Array(u):null,l=new Float32Array(i*n);l.fill(t);const d=this.sourceRasterInfos[0].bandCount,c=new Array(d).fill(l);return new a({width:i,height:n,pixelType:o,pixels:c,mask:p})}return t.process(s)}};t([i({json:{write:!0}})],p.prototype,\"functionName\",void 0),t([i({json:{write:!0}})],p.prototype,\"functionArguments\",void 0),t([i()],p.prototype,\"rasterArgumentNames\",void 0),t([i({json:{write:!0}}),n((t=>t?.toLowerCase()))],p.prototype,\"outputPixelType\",void 0),t([i({json:{write:!0}})],p.prototype,\"mainPrimaryRasterId\",void 0),t([i()],p.prototype,\"sourceRasters\",void 0),t([i({type:[u],json:{write:!0}})],p.prototype,\"sourceRasterInfos\",void 0),t([i({json:{write:!0}})],p.prototype,\"rasterInfo\",void 0),t([i({json:{write:!0}})],p.prototype,\"readingBufferSize\",void 0),t([i({json:{write:!0}})],p.prototype,\"id\",void 0),t([i()],p.prototype,\"isNoopProcess\",void 0),t([i()],p.prototype,\"supportsGPU\",null),t([i()],p.prototype,\"rawInputBandIds\",void 0),t([i()],p.prototype,\"isInputBandIdsSwizzled\",void 0),t([i()],p.prototype,\"swizzledBandSelection\",void 0),t([i()],p.prototype,\"isBranch\",void 0),t([i()],p.prototype,\"_bindingResult\",void 0),p=t([o(\"esri.layers.support.rasterFunctions.BaseRasterFunction\")],p);const l=p;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./AspectFunctionArguments.js\";import i from\"./BaseRasterFunction.js\";import{aspect as n}from\"./surfaceUtils.js\";let p=class extends i{constructor(){super(...arguments),this.functionName=\"Aspect\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.isGCS=!1}_bindSourceRasters(){const t=this.sourceRasterInfos[0];this.isGCS=t.spatialReference?.isGeographic??!1,this.outputPixelType=this._getOutputPixelType(\"f32\");const s=t.clone();return s.pixelType=this.outputPixelType,s.statistics=[{min:0,max:360,avg:180,stddev:30}],s.histograms=null,s.colormap=null,s.attributeTable=null,s.bandCount=1,this.rasterInfo=s,{success:!0,supportsGPU:!0}}_processPixels(t){const e=t.pixelBlocks?.[0];if(s(e))return null;const{extent:r}=t,o=r?{x:r.width/e.width,y:r.height/e.height}:{x:1,y:1};return n(e,{resolution:o})}};t([e({json:{write:!0,name:\"rasterFunction\"}})],p.prototype,\"functionName\",void 0),t([e({type:o,json:{write:!0,name:\"rasterFunctionArguments\"}})],p.prototype,\"functionArguments\",void 0),t([e()],p.prototype,\"rasterArgumentNames\",void 0),t([e({json:{write:!0}})],p.prototype,\"isGCS\",void 0),p=t([r(\"esri.layers.support.rasterFunctions.AspectFunction\")],p);const c=p;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../core/has.js\";const e=new Set([\"+\",\"-\",\"*\",\"/\",\"(\",\")\"]);function t(t,n){(t=t.replace(/ /g,\"\")).startsWith(\"-\")&&(t=\"0\"+t),t.startsWith(\"+\")&&(t=t.slice(1,t.length));const r=t.split(\"\"),l=[],o=[];let s=\"\";for(let a=0;a<r.length;a++){const t=r[a];if(e.has(t))\"\"!==s&&o.push(parseFloat(s)),l.push(t),s=\"\";else{if(\"b\"===t.toLowerCase()){a++,s=t.concat(r[a]),o.push(n[parseInt(s[1],10)-1]),s=\"\";continue}s=s.concat(t),a===r.length-1&&o.push(parseFloat(s))}}return{ops:l,nums:o}}function n(e,t,n,r){if(\"number\"==typeof n&&\"number\"==typeof r)return n+r;let l;if(\"number\"==typeof n){l=r.length;const e=n;(n=new Float32Array(l)).fill(e)}else if(l=n.length,r.constructor===Number){const e=r;(r=new Float32Array(l)).fill(e)}const o=new Float32Array(l);switch(t){case\"+\":for(let t=0;t<l;t++)(null==e||e[t])&&(o[t]=n[t]+r[t]);break;case\"-\":for(let t=0;t<l;t++)(null==e||e[t])&&(o[t]=n[t]-r[t]);break;case\"*\":for(let t=0;t<l;t++)(null==e||e[t])&&(o[t]=n[t]*r[t]);break;case\"/\":for(let t=0;t<l;t++)(null==e||e[t])&&r[t]&&(o[t]=n[t]/r[t]);break;case\"(\":case\")\":throw new Error(\"encountered error with custom band index equation\")}return o}function r(e,t){e.splice(t,1);let n=0,r=0;do{n=0,r=0;for(let t=0;t<e.length;t++)if(\"(\"===e[t])n=t;else if(\")\"===e[t]){r=t;break}r===n+1&&e.splice(n,2)}while(r===n+1);return e}function l(e){if(1===e.length)return{opIndex:0,numIndex:0};let t=0,n=0;for(let s=0;s<e.length;s++)if(\"(\"===e[s])t=s;else if(\")\"===e[s]){n=s;break}const r=0===n?e:e.slice(t+1,n);let l=-1;for(let s=0;s<r.length;s++)if(\"*\"===r[s]||\"/\"===r[s]){l=s;break}if(l>-1)n>0&&(l+=t+1);else{for(let e=0;e<r.length;e++)if(\"+\"===r[e]||\"-\"===r[e]){l=e;break}n>0&&(l+=t+1)}let o=0;for(let s=0;s<l;s++)\"(\"===e[s]&&o++;return{opIndex:l,numIndex:l-o}}function o(e,o,s){let a,{ops:f,nums:i}=t(s,o);if(0===f.length){const e=1===i.length?i[0]:o[0];if(e instanceof Float32Array)return[e];const t=new Float32Array(o[0].length);return\"number\"==typeof e?t.fill(e):t.set(e),[t]}for(;f.length>0;){const{numIndex:t,opIndex:o}=l(f);if(a=n(e,f[o],i[t],i[t+1]),1===f.length)break;f=r(f,o),i.splice(t,2,a)}return[a]}export{o as calculateCustomBandIndex};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{JSONMap as n}from\"../../../core/jsonMap.js\";import{isSome as t}from\"../../../core/maybe.js\";import e from\"../PixelBlock.js\";import{calculateCustomBandIndex as r}from\"./customBandIndexUtils.js\";import{isValidPixelBlock as o}from\"./pixelUtils.js\";const a=new n({0:\"custom\",1:\"ndvi\",2:\"savi\",3:\"tsavi\",4:\"msavi\",5:\"gemi\",6:\"pvi\",7:\"gvitm\",8:\"sultan\",9:\"vari\",10:\"gndvi\",11:\"sr\",12:\"ndvi-re\",13:\"sr-re\",14:\"mtvi2\",15:\"rtvi-core\",16:\"ci-re\",17:\"ci-g\",18:\"ndwi\",19:\"evi\",20:\"iron-oxide\",21:\"ferrous-minerals\",22:\"clay-minerals\",23:\"wndwi\",24:\"bai\",25:\"nbr\",26:\"ndbi\",27:\"ndmi\",28:\"ndsi\",29:\"mndwi\"},{useNumericKeys:!0});function s(n,a){if(!o(n))return n;const{equation:s,method:i}=a,l=a.bandIndexes.map((n=>n-1)),{pixels:j,mask:q}=n;let M;switch(i){case\"gndvi\":case\"nbr\":case\"ndbi\":case\"ndvi\":case\"ndvi-re\":case\"ndsi\":case\"ndmi\":case\"mndwi\":M=c(q,j[l[0]],j[l[1]]);break;case\"ndwi\":M=c(q,j[l[1]],j[l[0]]);break;case\"sr\":case\"sr-re\":case\"iron-oxide\":case\"ferrous-minerals\":case\"clay-minerals\":M=u(q,j[l[0]],j[l[1]]);break;case\"ci-g\":case\"ci-re\":M=f(q,j[l[0]],j[l[1]]);break;case\"savi\":M=w(q,j[l[0]],j[l[1]],l[2]+1);break;case\"tsavi\":M=m(q,j[l[0]],j[l[1]],l[2]+1,l[3]+1,l[4]+1);break;case\"msavi\":M=h(q,j[l[0]],j[l[1]]);break;case\"gemi\":M=d(q,j[l[0]],j[l[1]]);break;case\"pvi\":M=g(q,j[l[0]],j[l[1]],l[2]+1,l[3]+1);break;case\"gvitm\":M=y(q,[j[l[0]],j[l[1]],j[l[2]],j[l[3]],j[l[4]],j[l[5]]]);break;case\"sultan\":M=b(q,[j[l[0]],j[l[1]],j[l[2]],j[l[3]],j[l[4]],j[l[5]]]);break;case\"vari\":M=v(q,[j[l[0]],j[l[1]],j[l[2]]]);break;case\"mtvi2\":M=k(q,[j[l[0]],j[l[1]],j[l[2]]]);break;case\"rtvi-core\":M=A(q,[j[l[0]],j[l[1]],j[l[2]]]);break;case\"evi\":M=p(q,[j[l[0]],j[l[1]],j[l[2]]]);break;case\"wndwi\":M=F(q,[j[l[0]],j[l[1]],j[l[2]]],l[3]?l[3]+1:.5);break;case\"bai\":M=x(q,j[l[0]],j[l[1]]);break;case\"custom\":M=r(q,j,s);break;default:return n}const U=t(q)?new Uint8Array(q.length):null;t(q)&&t(U)&&U.set(q);const B=new e({width:n.width,height:n.height,pixelType:\"f32\",pixels:M,mask:U});return B.updateStatistics(),B}function i(n,t,r,o){const{mask:a,pixels:s,width:i,height:l}=n,c=s[r],u=s[t],f=u.length,w=o?new Uint8Array(f):new Float32Array(f),m=o?100:1,h=o?100.5:0;for(let e=0;e<f;e++)if(null==a||a[e]){const n=c[e],t=u[e],r=n+t;r&&(w[e]=(n-t)/r*m+h)}const d=new e({width:i,height:l,mask:a,pixelType:o?\"u8\":\"f32\",pixels:[w]});return d.updateStatistics(),d}function l(n){const t=new Float32Array(9);return t[3*n[0]]=1,t[3*n[1]+1]=1,t[3*n[2]+2]=1,t}function c(n,t,e){const r=e.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a]){const n=t[a],r=e[a],s=n+r;s&&(o[a]=(n-r)/s)}return[o]}function u(n,t,e){const r=e.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a]){const n=t[a],r=e[a];r&&(o[a]=n/r)}return[o]}function f(n,t,e){const r=t.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a]){const n=t[a],r=e[a];r&&(o[a]=n/r-1)}return[o]}function w(n,t,e,r){const o=e.length,a=new Float32Array(o);for(let s=0;s<o;s++)if(null==n||n[s]){const n=e[s],o=t[s],i=o+n+r;i&&(a[s]=(o-n)/i*(1+r))}return[a]}function m(n,t,e,r,o,a){const s=e.length,i=new Float32Array(s),l=-o*r+a*(1+r*r);for(let c=0;c<s;c++)if(null==n||n[c]){const n=e[c],a=t[c],s=o*a+n+l;s&&(i[c]=r*(a-r*n-o)/s)}return[i]}function h(n,t,e){const r=e.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a]){const n=e[a],r=t[a];o[a]=.5*(2*(r+1)-Math.sqrt((2*r+1)**2-8*(r-n)))}return[o]}function d(n,t,e){const r=e.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a]){const n=e[a],r=t[a];if(1!==n){const t=(2*(r*r-n*n)+1.5*r+.5*n)/(r+n+.5);o[a]=t*(1-.25*t)-(n-.125)/(1-n)}}return[o]}function g(n,t,e,r,o){const a=e.length,s=new Float32Array(a),i=Math.sqrt(1+r*r);for(let l=0;l<a;l++)if(null==n||n[l]){const n=e[l],a=t[l];s[l]=(a-r*n-o)/i}return[s]}function y(n,t){const[e,r,o,a,s,i]=t,l=e.length,c=new Float32Array(l);for(let u=0;u<l;u++)(null==n||n[u])&&(c[u]=-.2848*e[u]-.2435*r[u]-.5436*o[u]+.7243*a[u]+.084*s[u]-1.18*i[u]);return[c]}function b(n,t){const[e,,r,o,a,s]=t,i=e.length,l=new Float32Array(i),c=new Float32Array(i),u=new Float32Array(i);for(let f=0;f<i;f++)(null==n||n[f])&&(l[f]=s[f]?a[f]/s[f]*100:0,c[f]=e[f]?a[f]/e[f]*100:0,u[f]=o[f]?r[f]/o[f]*(a[f]/o[f])*100:0);return[l,c,u]}function v(n,t){const[e,r,o]=t,a=e.length,s=new Float32Array(a);for(let i=0;i<a;i++)if(null==n||n[i])for(i=0;i<a;i++){const n=e[i],t=r[i],a=t+n-o[i];a&&(s[i]=(t-n)/a)}return[s]}function k(n,t){const[e,r,o]=t,a=e.length,s=new Float32Array(a);for(let i=0;i<a;i++)if(null==n||n[i])for(i=0;i<a;i++){const n=e[i],t=r[i],a=o[i],l=Math.sqrt((2*n+1)**2-6*n-5*Math.sqrt(t)-.5);s[i]=1.5*(1.2*(n-a)-2.5*(t-a))*l}return[s]}function A(n,t){const[e,r,o]=t,a=e.length,s=new Float32Array(a);for(let i=0;i<a;i++)if(null==n||n[i])for(i=0;i<a;i++){const n=e[i],t=r[i],a=o[i];s[i]=100*(n-t)-10*(n-a)}return[s]}function p(n,t){const[e,r,o]=t,a=e.length,s=new Float32Array(a);for(let i=0;i<a;i++)if(null==n||n[i])for(i=0;i<a;i++){const n=e[i],t=r[i],a=n+6*t-7.5*o[i]+1;a&&(s[i]=2.5*(n-t)/a)}return[s]}function F(n,t,e=.5){const[r,o,a]=t,s=o.length,i=new Float32Array(s);for(let l=0;l<s;l++)if(null==n||n[l])for(l=0;l<s;l++){const n=r[l],t=o[l],s=a[l],c=n+e*t+(1-e)*s;c&&(i[l]=(n-e*t-(1-e)*s)/c)}return[i]}function x(n,t,e){const r=e.length,o=new Float32Array(r);for(let a=0;a<r;a++)if(null==n||n[a])for(a=0;a<r;a++){const n=(.1-t[a])**2+(.06-e[a])**2;n&&(o[a]=1/n)}return[o]}export{a as bandIndexMethodMap,s as calculateBandIndex,i as calculateNDVI,l as getBandMatrix3};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{clone as o}from\"../../../core/lang.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{enumeration as t}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";import{bandIndexMethodMap as n}from\"./bandIndexUtils.js\";import c from\"./BaseFunctionArguments.js\";var p;let a=p=class extends c{constructor(){super(...arguments),this.method=\"custom\"}clone(){return new p({method:this.method,bandIndexes:this.bandIndexes,raster:o(this.raster)})}};r([s({json:{type:String,write:!0}})],a.prototype,\"bandIndexes\",void 0),r([t(n)],a.prototype,\"method\",void 0),a=p=r([e(\"esri.layers.support.rasterFunctions.BandArithmeticFunctionArguments\")],a);const i=a;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import n from\"./BandArithmeticFunctionArguments.js\";import{calculateBandIndex as a,getBandMatrix3 as i}from\"./bandIndexUtils.js\";import o from\"./BaseRasterFunction.js\";const c=new Set([\"vari\",\"mtvi2\",\"rtvi-core\",\"evi\"]);let u=class extends o{constructor(){super(...arguments),this.functionName=\"BandArithmetic\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"]}_bindSourceRasters(){this.outputPixelType=this._getOutputPixelType(\"f32\");const e=this.sourceRasterInfos[0],s=e.clone();s.pixelType=this.outputPixelType,s.statistics=null,s.histograms=null,s.bandCount=\"sultan\"===this.functionArguments.method?e.bandCount:1,this.rasterInfo=s;return{success:!0,supportsGPU:![\"custom\",\"gvitm\",\"sultan\"].includes(this.functionArguments.method)}}_processPixels(e){const t=e.pixelBlocks?.[0];if(s(t))return t;const{method:r,bandIndexes:n}=this.functionArguments,i=n.split(\" \").map((e=>parseFloat(e)));return a(t,{method:r,bandIndexes:i,equation:n})}_getWebGLParameters(){const e=this.functionArguments.bandIndexes.split(\" \").map((e=>parseFloat(e)-1));2===e.length&&e.push(0);const s=this.isInputBandIdsSwizzled?[0,1,2]:e;let t,r;const n=new Float32Array(3),{method:a}=this.functionArguments;switch(a){case\"gndvi\":case\"nbr\":case\"ndbi\":case\"ndvi\":case\"ndvi-re\":case\"ndsi\":case\"ndmi\":case\"mndwi\":t=i([s[0],s[1],0]),r=\"ndxi\";break;case\"ndwi\":t=i([s[1],s[0],0]),r=\"ndxi\";break;case\"sr\":case\"sr-re\":case\"iron-oxide\":case\"ferrous-minerals\":case\"clay-minerals\":t=i([s[0],s[1],0]),r=\"sr\";break;case\"ci-g\":case\"ci-re\":t=i([s[0],s[1],0]),r=\"ci\";break;case\"savi\":t=i([s[0],s[1],0]),r=\"savi\",n[0]=e[2]+1;break;case\"tsavi\":t=i([s[0],s[1],0]),r=\"tsavi\",n[0]=e[2]+1,n[1]=e[3]+1,n[2]=e[4]+1;break;case\"msavi\":t=i([s[0],s[1],0]),r=\"msavi\";break;case\"gemi\":t=i([s[0],s[1],0]),r=\"gemi\";break;case\"pvi\":t=i([s[0],s[1],0]),r=\"tsavi\",n[0]=e[2]+1,n[1]=e[3]+1;break;case\"vari\":t=i([s[0],s[1],s[2]]),r=\"vari\";break;case\"mtvi2\":t=i([s[0],s[1],s[2]]),r=\"mtvi2\";break;case\"rtvi-core\":t=i([s[0],s[1],s[2]]),r=\"rtvicore\";break;case\"evi\":t=i([s[0],s[1],s[2]]),r=\"evi\";break;case\"wndwi\":t=i([s[0],s[1],0]),r=\"wndwi\",n[0]=e[3]?e[3]+1:.5;break;case\"bai\":t=i([s[1],s[0],0]),r=\"bai\";break;default:t=i([0,1,2]),r=\"custom\"}return{bandIndexMat3:t,indexType:r,adjustments:n}}_getInputBandIds(e){if(\"custom\"===this.functionArguments.method)return e;const s=this.functionArguments.bandIndexes.split(\" \").map((e=>parseFloat(e)-1)),t=e.length,r=s.map((e=>e>=t?t-1:e)),n=c.has(this.functionArguments.method)?3:2,a=r.slice(0,n).map((s=>e[s]));return 2===a.length&&a.push(0),a}};e([t({json:{write:!0,name:\"rasterFunction\"}})],u.prototype,\"functionName\",void 0),e([t({type:n,json:{write:!0,name:\"rasterFunctionArguments\"}})],u.prototype,\"functionArguments\",void 0),e([t()],u.prototype,\"rasterArgumentNames\",void 0),u=e([r(\"esri.layers.support.rasterFunctions.BandArithmeticFunction\")],u);const m=u;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import{clone as r}from\"../../../core/lang.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import{cast as t}from\"../../../core/accessorSupport/decorators/cast.js\";import{reader as p}from\"../../../core/accessorSupport/decorators/reader.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"./BaseFunctionArguments.js\";import{colorRampDict as m,rasterColormapNames as l}from\"../../../renderers/support/colorRampUtils.js\";import{fromJSON as c,types as n}from\"../../../rest/support/colorRamps.js\";var i;let u=i=class extends a{castColormapName(o){if(!o)return null;const r=o.toLowerCase();return l.includes(r)?r:null}readColorRamp(o){return c(o)}readColorRampName(o,r){if(!o)return null;const e=m.jsonValues.find((r=>r.toLowerCase()===o.toLowerCase()));return e?m.fromJSON(e):null}clone(){return new i({colormap:r(this.colormap),colormapName:this.colormapName,colorRamp:this.colorRamp?.clone(),colorRampName:this.colorRampName})}};o([e({type:[[Number]],json:{write:!0}})],u.prototype,\"colormap\",void 0),o([e({type:String,json:{write:!0}})],u.prototype,\"colormapName\",void 0),o([t(\"colormapName\")],u.prototype,\"castColormapName\",null),o([e({types:n,json:{write:!0}})],u.prototype,\"colorRamp\",void 0),o([p(\"colorRamp\")],u.prototype,\"readColorRamp\",null),o([e({type:m.apiValues,json:{type:m.jsonValues,write:m.write}})],u.prototype,\"colorRampName\",void 0),o([p(\"colorRampName\")],u.prototype,\"readColorRampName\",null),u=i=o([s(\"esri.layers.support.rasterFunctions.ColormapFunctionArguments\")],u);const d=u;export{d as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{convertColorRampToColors as r}from\"../../../renderers/support/colorRampUtils.js\";const t=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[112,75,3],[113,76,3],[114,77,3],[115,77,3],[116,78,3],[117,79,3],[118,79,3],[119,80,3],[121,81,4],[122,82,4],[123,82,4],[124,83,4],[125,84,4],[126,84,4],[127,85,4],[128,86,4],[129,86,4],[130,87,4],[131,88,4],[132,89,4],[133,89,4],[134,90,4],[135,91,4],[136,91,4],[137,92,4],[138,93,4],[139,94,4],[140,94,4],[142,95,5],[143,96,5],[144,96,5],[145,97,5],[146,98,5],[147,99,5],[148,99,5],[149,100,5],[150,101,5],[151,101,5],[152,102,5],[153,103,5],[154,104,5],[155,104,5],[156,105,5],[157,106,5],[158,106,5],[159,107,5],[160,108,5],[161,108,5],[162,109,5],[164,110,6],[165,111,6],[166,111,6],[167,112,6],[168,113,6],[169,113,6],[170,114,6],[171,115,6],[172,116,6],[173,116,6],[174,117,6],[245,0,0],[245,5,0],[245,10,0],[246,15,0],[246,20,0],[246,25,0],[246,30,0],[247,35,0],[247,40,0],[247,45,0],[247,50,0],[247,55,0],[248,60,0],[248,65,0],[248,70,0],[248,75,0],[249,81,0],[249,86,0],[249,91,0],[249,96,0],[250,101,0],[250,106,0],[250,111,0],[250,116,0],[250,121,0],[251,126,0],[251,131,0],[251,136,0],[251,141,0],[252,146,0],[252,151,0],[252,156,0],[252,156,0],[251,159,0],[250,162,0],[249,165,0],[248,168,0],[247,171,0],[246,174,0],[245,177,0],[245,179,0],[244,182,0],[243,185,0],[242,188,0],[241,191,0],[240,194,0],[239,197,0],[238,200,0],[237,203,0],[236,206,0],[235,209,0],[234,212,0],[233,215,0],[232,218,0],[231,221,0],[230,224,0],[230,226,0],[229,229,0],[228,232,0],[227,235,0],[226,238,0],[225,241,0],[224,244,0],[223,247,0],[165,247,0],[163,244,0],[161,240,0],[158,237,0],[156,233,1],[154,230,1],[152,227,1],[149,223,1],[147,220,1],[145,216,1],[143,213,1],[140,210,2],[138,206,2],[136,203,2],[134,200,2],[132,196,2],[129,193,2],[127,189,2],[125,186,3],[123,183,3],[120,179,3],[118,176,3],[116,172,3],[114,169,3],[111,166,3],[109,162,4],[107,159,4],[105,155,4],[103,152,4],[100,149,4],[98,145,4],[96,142,4],[94,138,5],[91,135,5],[89,132,5],[87,128,5],[85,125,5],[82,121,5],[80,118,5],[78,115,6],[76,111,6],[73,108,6],[71,105,6],[69,101,6],[67,98,6],[65,94,6],[62,91,7],[60,88,7],[58,84,7],[56,81,7],[53,77,7],[51,74,7],[49,71,7],[47,67,8],[44,64,8],[42,60,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8],[40,57,8]],o=[[36,0,255],[36,0,255],[36,0,255],[36,0,255],[245,20,0],[245,24,0],[245,29,0],[245,31,0],[247,33,0],[247,33,0],[247,37,0],[247,41,0],[247,41,0],[247,41,0],[247,45,0],[247,45,0],[247,47,0],[247,49,0],[247,49,0],[247,54,0],[247,54,0],[247,56,0],[247,58,0],[247,58,0],[250,62,0],[250,62,0],[250,62,0],[250,67,0],[250,67,0],[250,67,0],[250,69,0],[250,71,0],[250,71,0],[250,75,0],[250,75,0],[250,78,0],[250,79,0],[250,79,0],[250,79,0],[250,81,0],[250,83,0],[250,83,0],[250,87,0],[250,87,0],[250,90,0],[250,92,0],[252,93,0],[252,93,0],[252,97,0],[252,97,0],[252,97,0],[252,97,0],[252,101,0],[252,101,0],[252,101,0],[252,101,0],[252,105,0],[252,105,0],[252,107,0],[252,109,0],[252,109,0],[252,113,13],[255,118,20],[255,119,23],[255,121,25],[255,126,33],[255,132,38],[255,133,40],[255,135,43],[255,141,48],[255,144,54],[255,150,59],[255,152,61],[255,153,64],[255,159,69],[255,163,77],[255,165,79],[255,168,82],[255,174,87],[255,176,92],[255,181,97],[255,183,99],[255,186,102],[255,191,107],[255,197,115],[255,201,120],[255,203,123],[255,205,125],[255,209,130],[255,214,138],[255,216,141],[255,218,143],[255,224,150],[255,228,156],[255,234,163],[255,236,165],[255,238,168],[255,243,173],[255,248,181],[255,252,186],[253,252,186],[250,252,187],[244,250,180],[238,247,176],[234,246,173],[231,245,169],[223,240,163],[217,237,157],[211,235,150],[205,233,146],[200,230,142],[195,227,136],[189,224,132],[184,222,126],[180,220,123],[174,217,119],[169,214,114],[163,212,108],[160,210,105],[154,207,101],[148,204,96],[143,201,93],[138,199,88],[134,197,84],[130,194,81],[126,191,77],[117,189,70],[115,186,68],[112,184,64],[106,181,60],[100,179,55],[94,176,49],[92,174,47],[90,173,45],[81,168,37],[75,166,33],[71,163,28],[66,160,24],[62,158,21],[56,156,14],[51,153,0],[51,153,0],[51,153,0],[50,150,0],[50,150,0],[50,150,0],[50,150,0],[49,148,0],[49,148,0],[49,148,0],[48,145,0],[48,145,0],[48,145,0],[48,145,0],[48,143,0],[48,143,0],[48,143,0],[48,143,0],[47,140,0],[47,140,0],[47,140,0],[47,140,0],[46,138,0],[46,138,0],[46,138,0],[46,138,0],[45,135,0],[45,135,0],[45,135,0],[45,135,0],[44,133,0],[44,133,0],[44,133,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[43,130,0],[42,128,0],[42,128,0],[42,128,0],[42,125,0],[42,125,0],[42,125,0],[42,125,0],[41,122,0],[41,122,0],[41,122,0],[41,122,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[40,120,0],[39,117,0],[39,117,0],[39,117,0],[39,117,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0],[38,115,0]];function e(t,o){const e=[],n=[];for(let r=0;r<t.length-1;r++)e.push({type:\"algorithmic\",algorithm:\"esriHSVAlgorithm\",fromColor:t[r].slice(1),toColor:t[r+1].slice(1)}),n.push(t[r+1][0]-t[r][0]);const s=t[t.length-1][0];return r({type:\"multipart\",colorRamps:e},{numColors:s,weights:o=o??n})}function n(){return e([[0,0,191,191],[51,0,255,0],[102,255,255,0],[153,255,127,0],[204,191,127,63],[256,20,20,20]])}function s(){const r=e([[0,255,255,255],[70,0,0,255],[80,205,193,173],[100,150,150,150],[110,120,100,51],[130,120,200,100],[140,28,144,3],[160,6,55,0],[180,10,30,25],[201,6,27,7]]);for(let t=r.length;t<256;t++)r.push([6,27,7]);return r}function l(){return r({type:\"algorithmic\",algorithm:\"esriHSVAlgorithm\",fromColor:[0,0,0],toColor:[255,255,255]})}function i(){const r=[];for(let t=0;t<256;t++){const t=[];for(let r=0;r<3;r++)t.push(Math.round(255*Math.random()));r.push(t)}return r}function a(){return e([[0,38,54,41],[69,79,90,82],[131,156,156,156],[256,253,241,253]],[.268,.238,.495])}function c(r){let e;switch(r){case\"elevation\":e=n();break;case\"gray\":e=l();break;case\"hillshade\":e=a();break;case\"ndvi\":e=t;break;case\"ndvi2\":e=s();break;case\"ndvi3\":e=o;break;case\"random\":e=i()}return e?(e=e.map(((r,t)=>[t,...r])),e):null}export{c as getColormapByName};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import t from\"./BaseRasterFunction.js\";import e from\"./ColormapFunctionArguments.js\";import{getColormapByName as p}from\"./colormaps.js\";import{convertColorRampToColormap as n,getColorRampJSON as i}from\"../../../renderers/support/colorRampUtils.js\";let c=class extends t{constructor(){super(...arguments),this.functionName=\"Colormap\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.isNoopProcess=!0}_bindSourceRasters(){const o=this.sourceRasterInfos[0];if(o.bandCount>1)return{success:!1,supportsGPU:!1,error:\"colormap-function: source data must be single band\"};let{colormap:r,colormapName:s,colorRamp:t,colorRampName:e}=this.functionArguments;if(!r?.length)if(t)r=n(t,{interpolateAlpha:!0});else if(e){const o=i(e);o&&(r=n(o))}else s&&(r=p(s));if(!r?.length)return{success:!1,supportsGPU:!1,error:\"colormap-function: missing colormap argument\"};this.outputPixelType=this._getOutputPixelType(\"u8\");const c=o.clone();return c.pixelType=this.outputPixelType,c.colormap=r,c.bandCount=1,this.rasterInfo=c,{success:!0,supportsGPU:!0}}_processPixels(o){return o.pixelBlocks?.[0]}};o([r({json:{write:!0,name:\"rasterFunction\"}})],c.prototype,\"functionName\",void 0),o([r({type:e,json:{write:!0,name:\"rasterFunctionArguments\"}})],c.prototype,\"functionArguments\",void 0),o([r()],c.prototype,\"rasterArgumentNames\",void 0),o([r()],c.prototype,\"isNoopProcess\",void 0),o([r({json:{write:!0}})],c.prototype,\"indexedColormap\",void 0),c=o([s(\"esri.layers.support.rasterFunctions.ColormapFunction\")],c);const u=c;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{clone as s}from\"../../../core/lang.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as e}from\"../../../core/accessorSupport/decorators/writer.js\";import p from\"./BaseFunctionArguments.js\";var a;let c=a=class extends p{constructor(){super(...arguments),this.rasters=[]}writeRasters(r,s){s.rasters=r.map((r=>\"number\"==typeof r||\"string\"==typeof r?r:r.toJSON()))}clone(){return new a({rasters:s(this.rasters)})}};r([t({json:{write:!0}})],c.prototype,\"rasters\",void 0),r([e(\"rasters\")],c.prototype,\"writeRasters\",null),c=a=r([o(\"esri.layers.support.rasterFunctions.CompositeBandFunctionArguments\")],c);const n=c;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isSome as s,isNone as r}from\"../../../core/maybe.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import n from\"./BaseRasterFunction.js\";import i from\"./CompositeBandFunctionArguments.js\";import{compositeBands as a}from\"./pixelUtils.js\";let u=class extends n{constructor(){super(...arguments),this.functionName=\"CompositeBand\",this.functionArguments=null,this.rasterArgumentNames=[\"rasters\"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,r=t[0];this.outputPixelType=this._getOutputPixelType(r.pixelType);const e=r.clone();if(e.attributeTable=null,e.colormap=null,e.pixelType=this.outputPixelType,e.bandCount=t.map((({bandCount:t})=>t)).reduce(((t,s)=>t+s)),t.every((({statistics:t})=>s(t)&&t.length))){const r=[];t.forEach((({statistics:t})=>s(t)&&r.push(...t))),e.statistics=r}if(t.every((({histograms:t})=>s(t)&&t.length))){const r=[];t.forEach((({histograms:t})=>s(t)&&r.push(...t))),e.histograms=r}e.bandCount>1&&(e.colormap=null,e.attributeTable=null),this.rasterInfo=e;return{success:!0,supportsGPU:e.bandCount<=3}}_processPixels(t){const{pixelBlocks:s}=t;if(!s)return null;const e=s?.[0];return r(e)?null:a(s)}_getWebGLParameters(){return{bandCount:this.rasterInfo.bandCount}}};t([e({json:{write:!0,name:\"rasterFunction\"}})],u.prototype,\"functionName\",void 0),t([e({type:i,json:{write:!0,name:\"rasterFunctionArguments\"}})],u.prototype,\"functionArguments\",void 0),t([e()],u.prototype,\"rasterArgumentNames\",void 0),u=t([o(\"esri.layers.support.rasterFunctions.CompositeBandFunction\")],u);const p=u;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst e={degree:1,percentRise:2,adjusted:3},r={userDefined:0,NDVI:1,SAVI:2,TSAVI:3,MSAVI:4,GEMI:5,PVI:6,GVITM:7,Sultan:8,VARI:9,GNDVI:10,SR:11,NDVIRe:12,SRRe:13,MTVI2:14,RTVICore:15,CIRe:16,CIG:17,NDWI:18,EVI:19,ironOxide:20,ferrousMinerals:21,clayMinerals:22,WNDWI:23,BAI:24,NBR:25,NDBI:26,NDMI:27,NDSI:28,MNDWI:29},i={random:\"Random\",NDVI:\"NDVI\",NDVI2:\"NDVI2\",NDVI3:\"NDVI3\",elevation:\"Elevation\",gray:\"Gray\",hillshade:\"Hillshade\"},t={aspect:\"Aspect\",blackToWhite:\"Black to White\",blueBright:\"Blue Bright\",blueLightToDark:\"Blue Light to Dark\",blueGreenBright:\"Blue-Green Bright\",blueGreenLightToDark:\"Blue-Green Light to Dark\",brownLightToDark:\"Brown Light to Dark\",brownToBlueGreenDivergingRight:\"Brown to Blue Green Diverging, Bright\",brownToBlueGreenDiverging:\"Brown to Blue Green Diverging, Dark\",coefficientBias:\"Coefficient Bias\",coldToHotDiverging:\"Cold to Hot Diverging\",conditionNumber:\"Condition Number\",cyanToPurple:\"Cyan to Purple\",cyanLightToBlueDark:\"Cyan-Light to Blue-Dark\",distance:\"Distance\",elevation1:\"Elevation #1\",elevation2:\"Elevation #2\",errors:\"Errors\",grayLightToDark:\"Gray Light to Dark\",greenBright:\"Green Bright\",greenLightToDark:\"Green Light to Dark\",greenToBlue:\"Green to Blue\",orangeBright:\"Orange Bright\",orangeLightToDark:\"Orange Light to Dark\",partialSpectrum:\"Partial Spectrum\",partialSpectrum1Diverging:\"Partial Spectrum 1 Diverging\",partialSpectrum2Diverging:\"Partial Spectrum 2 Diverging\",pinkToYellowGreenDivergingBright:\"Pink to YellowGreen Diverging, Bright\",pinkToYellowGreenDivergingDark:\"Pink to YellowGreen Diverging, Dark\",precipitation:\"Precipitation\",prediction:\"Prediction\",purpleBright:\"Purple Bright\",purpleToGreenDivergingBright:\"Purple to Green Diverging, Bright\",purpleToGreenDivergingDark:\"Purple to Green Diverging, Dark\",purpleBlueBright:\"Purple-Blue Bright\",purpleBlueLightToDark:\"Purple-Blue Light to Dark\",purpleRedBright:\"Purple-Red Bright\",purpleRedLightToDark:\"Purple-Red Light to Dark\",redBright:\"Red Bright\",redLightToDark:\"Red Light to Dark\",redToBlueDivergingBright:\"Red to Blue Diverging, Bright\",redToBlueDivergingDark:\"Red to Blue Diverging, Dark\",redToGreen:\"Red to Green\",redToGreenDivergingBright:\"Red to Green Diverging, Bright\",redToGreenDivergingDark:\"Red to Green Diverging, Dark\",slope:\"Slope\",spectrumFullBright:\"Spectrum-Full Bright\",spectrumFullDark:\"Spectrum-Full Dark\",spectrumFullLight:\"Spectrum-Full Light\",surface:\"Surface\",temperature:\"Temperature\",whiteToBlack:\"White to Black\",yellowToDarkRed:\"Yellow to Dark Red\",yellowToGreenToDarkBlue:\"Yellow to Green to Dark Blue\",yellowToRed:\"Yellow to Red\",yellowGreenBright:\"Yellow-Green Bright\",yellowGreenLightToDark:\"Yellow-Green Light to Dark\"},o={bestMatch:0,fail:1},n={matchAny:0,matchAll:1},a={userDefined:-1,lineDetectionHorizontal:0,lineDetectionVertical:1,lineDetectionLeftDiagonal:2,lineDetectionRightDiagonal:3,gradientNorth:4,gradientWest:5,gradientEast:6,gradientSouth:7,gradientNorthEast:8,gradientNorthWest:9,smoothArithmeticMean:10,smoothing3x3:11,smoothing5x5:12,sharpening3x3:13,sharpening5x5:14,laplacian3x3:15,laplacian5x5:16,sobelHorizontal:17,sobelVertical:18,sharpen:19,sharpen2:20,pointSpread:21,none:255},l={none:0,standardDeviation:3,histogramEqualization:4,minMax:5,percentClip:6,sigmoid:9},g={plus:1,minus:2,times:3,sqrt:4,power:5,abs:10,divide:23,exp:25,exp10:26,exp2:27,int:30,float:32,ln:35,log10:36,log2:37,mod:44,negate:45,roundDown:48,roundUp:49,square:53,floatDivide:64,floorDivide:65},D={bitwiseAnd:11,bitwiseLeftShift:12,bitwiseNot:13,bitwiseOr:14,bitwiseRightShift:15,bitwiseXOr:16,booleanAnd:17,booleanNot:18,booleanOr:19,booleanXOr:20,equalTo:24,greaterThan:28,greaterThanEqual:29,lessThan:33,lessThanEqual:34,isNull:31,notEqual:46},h={acos:6,asin:7,atan:8,atanh:9,cos:21,cosh:22,sin:51,sinh:52,tan:56,tanh:57,acosh:59,asinh:60,atan2:61},u={majority:38,max:39,mean:40,med:41,min:42,minority:43,range:47,stddev:54,sum:55,variety:58,majorityIgnoreNoData:66,maxIgnoreNoData:67,meanIgnoreNoData:68,medIgnoreNoData:69,minIgnoreNoData:70,minorityIgnoreNoData:71,rangeIgnoreNoData:72,stddevIgnoreNoData:73,sumIgnoreNoData:74,varietyIgnoreNoData:75},s={setNull:50,conditional:78},p={...g,...D,...h,...u,...s};export{r as bandIndexType,u as cellStatisticalOperation,t as colorRampName,i as colormapName,a as convolutionKernel,g as localArithmeticOperation,s as localConditionalOperation,D as localLogicalOperation,p as localOperators,h as localTrigonometricOperation,o as missingBandAction,n as noDataInterpretation,e as slopeType,l as stretchType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../PixelBlock.js\";import{convolutionKernel as e}from\"../rasterFunctionConstants.js\";const n=new Map;function o(t){const e=Math.sqrt(t.length),n=t.slice(0,e),o=[1];for(let l=1;l<e;l++){let n=null;for(let o=0;o<e;o++){const r=t[o+l*e],s=t[o];if(null==n)if(0===s){if(r)return{separable:!1,row:null,col:null}}else n=r/s;else if(r/s!==n)return{separable:!1,row:null,col:null}}if(null==n)return{separable:!1,row:null,col:null};o.push(n)}return{separable:!0,row:n,col:o}}function l(t,e,n,o,l,r,s){const i=new Float32Array(e*n),a=r.length,c=s?0:o,h=s?o:0,f=s?1:e;for(let p=c;p<n-c;p++){const n=p*e;for(let s=h;s<e-h;s++){if(l&&!l[n+s])continue;let e=0;for(let l=0;l<a;l++)e+=t[n+s+(l-o)*f]*r[l];i[n+s]=e}}return i}function r(t,e,n,o,l,r,s){const i=new Float32Array(e*n),a=Math.floor(o/2),c=Math.floor(l/2);for(let h=a;h<n-a;h++){const n=h*e;for(let h=c;h<e-c;h++){if(r&&!r[n+h])continue;let f=0;for(let r=0;r<o;r++)for(let o=0;o<l;o++)f+=t[n+h+(r-a)*e+o-c]*s[r*l+o];i[n+h]=f}}return i}function s(e,n,o=!0){const{pixels:l,width:s,height:a,pixelType:c,mask:h}=e,f=l.length,p=[],{kernel:u,rows:g,cols:x}=n;for(let t=0;t<f;t++){const e=r(l[t],s,a,g,x,h,u);o&&i(e,s,a,g,x),p.push(e)}return new t({width:s,height:a,pixelType:c,pixels:p,mask:h})}function i(t,e,n,o,l){const r=Math.floor(o/2);for(let i=0;i<r;i++)for(let o=0;o<e;o++)t[i*e+o]=t[(l-1-i)*e+o],t[(n-1-i)*e+o]=t[(n-l+i)*e+o];const s=Math.floor(l/2);for(let i=0;i<n;i++){const n=i*e;for(let o=0;o<s;o++)t[n+o]=t[n+l-1-o],t[n+e-o-1]=t[n+e+o-l]}}function a(e,n,o,r=!0){const{pixels:s,width:a,height:c,pixelType:h,mask:f}=e,p=s.length,u=[],g=n.length,x=o.length,w=Math.floor(g/2),m=Math.floor(x/2);for(let t=0;t<p;t++){let e=l(s[t],a,c,w,f,n,!0);e=l(e,a,c,m,f,o,!1),r&&i(e,a,c,g,x),u.push(e)}return new t({width:a,height:c,pixelType:h,pixels:u,mask:f})}function c(t,e){const n=o(e.kernel),l=!1!==e.mirrorEdges,r=n.separable?a(t,n.row,n.col,l):s(t,e,l),{outputPixelType:i}=e;return i&&r.clamp(i),r}n.set(e.none,[0,0,0,0,1,0,0,0,0]),n.set(e.lineDetectionHorizontal,[-1,-1,-1,2,2,2,-1,-1,-1]),n.set(e.lineDetectionVertical,[-1,2,-1,-1,2,-1,-1,2,-1]),n.set(e.lineDetectionLeftDiagonal,[2,-1,-1,-1,2,-1,-1,-1,2]),n.set(e.lineDetectionRightDiagonal,[-1,-1,2,-1,2,-1,2,-1,-1]),n.set(e.gradientNorth,[-1,-2,-1,0,0,0,1,2,1]),n.set(e.gradientWest,[-1,0,1,-2,0,2,-1,0,1]),n.set(e.gradientEast,[1,0,-1,2,0,-2,1,0,-1]),n.set(e.gradientSouth,[1,2,1,0,0,0,-1,-2,-1]),n.set(e.gradientNorthEast,[0,-1,-2,1,0,-1,2,1,0]),n.set(e.gradientNorthWest,[-2,-1,0,-1,0,1,0,1,2]),n.set(e.smoothArithmeticMean,[.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111,.111111111111]),n.set(e.smoothing3x3,[.0625,.125,.0625,.125,.25,.125,.0625,.125,.0625]),n.set(e.smoothing5x5,[1,1,1,1,1,1,4,4,4,1,1,4,12,4,1,1,4,4,4,1,1,1,1,1,1]),n.set(e.sharpening3x3,[-1,-1,-1,-1,9,-1,-1,-1,-1]),n.set(e.sharpening5x5,[-1,-3,-4,-3,-1,-3,0,6,0,-3,-4,6,21,6,-4,-3,0,6,0,-3,-1,-3,-4,-3,-1]),n.set(e.laplacian3x3,[0,-1,0,-1,4,-1,0,-1,0]),n.set(e.laplacian5x5,[0,0,-1,0,0,0,-1,-2,-1,0,-1,-2,17,-2,-1,0,-1,-2,-1,0,0,0,-1,0,0]),n.set(e.sobelHorizontal,[-1,-2,-1,0,0,0,1,2,1]),n.set(e.sobelVertical,[-1,0,1,-2,0,2,-1,0,1]),n.set(e.sharpen,[0,-.25,0,-.25,2,-.25,0,-.25,0]),n.set(e.sharpen2,[-.25,-.25,-.25,-.25,3,-.25,-.25,-.25,-.25]),n.set(e.pointSpread,[-.627,.352,-.627,.352,2.923,.352,-.627,.352,-.627]);export{c as convolute,a as convoluteSeparable,n as convolutionKernels,o as separateKernels};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../chunks/tslib.es6.js\";import{clone as t}from\"../../../core/lang.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import{convolutionKernel as e}from\"../rasterFunctionConstants.js\";import n from\"./BaseFunctionArguments.js\";import{convolutionKernels as i}from\"./convolutionUtils.js\";var p;let c=p=class extends n{constructor(){super(...arguments),this.rows=3,this.cols=3,this.kernel=[0,0,0,0,1,0,0,0,0]}set convolutionType(o){this._set(\"convolutionType\",o);const t=i.get(o);if(!t||o===e.userDefined||o===e.none)return;const s=Math.sqrt(t.length);this._set(\"kernel\",t),this._set(\"cols\",s),this._set(\"rows\",s)}clone(){return new p({cols:this.cols,rows:this.rows,kernel:[...this.kernel],convolutionType:this.convolutionType,raster:t(this.raster)})}};o([s({json:{type:Number,write:!0}})],c.prototype,\"rows\",void 0),o([s({json:{type:Number,write:!0}})],c.prototype,\"cols\",void 0),o([s({json:{name:\"type\",type:Number,write:!0}})],c.prototype,\"convolutionType\",null),o([s({json:{type:[Number],write:!0}})],c.prototype,\"kernel\",void 0),c=p=o([r(\"esri.layers.support.rasterFunctions.ConvolutionFunctionArguments\")],c);const u=c;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isNone as e}from\"../../../core/maybe.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import{convolutionKernel as r}from\"../rasterFunctionConstants.js\";import{getPixelValueRange as n}from\"../rasterFormats/pixelRangeUtils.js\";import i from\"./BaseRasterFunction.js\";import u from\"./ConvolutionFunctionArguments.js\";import{convolute as c}from\"./convolutionUtils.js\";const p=25;let l=class extends i{constructor(){super(...arguments),this.functionName=\"Convolution\",this.rasterArgumentNames=[\"raster\"]}_bindSourceRasters(){const{convolutionType:t,rows:e,cols:o,kernel:s}=this.functionArguments;if(!Object.values(r).includes(t))return{success:!1,supportsGPU:!1,error:`convolution-function: the specified kernel type is not supported ${t}`};if(t!==r.none&&e*o!==s.length)return{success:!1,supportsGPU:!1,error:\"convolution-function: the specified rows and cols do not match the length of the kernel\"};const n=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType(n.pixelType);const i=n.clone();i.pixelType=this.outputPixelType;const u=[r.none,r.sharpen,r.sharpen2,r.sharpening3x3,r.sharpening5x5];\"u8\"===this.outputPixelType||u.includes(t)||(i.statistics=null,i.histograms=null),i.colormap=null,i.attributeTable=null,this.rasterInfo=i;return{success:!0,supportsGPU:s.length<=p}}_processPixels(t){const o=t.pixelBlocks?.[0];if(e(o)||this.functionArguments.convolutionType===r.none)return o;let{kernel:s,rows:n,cols:i}=this.functionArguments;const u=s.reduce(((t,e)=>t+e));return 0!==u&&1!==u&&(s=s.map((t=>t/u))),c(o,{kernel:s,rows:n,cols:i,outputPixelType:this.outputPixelType})}_getWebGLParameters(){let{kernel:t}=this.functionArguments;const e=t.reduce(((t,e)=>t+e));0!==e&&1!==e&&(t=t.map((t=>t/e)));const o=new Float32Array(p);return o.set(t),{kernelRows:this.functionArguments.rows,kernelCols:this.functionArguments.cols,kernel:o,clampRange:n(this.outputPixelType)}}};t([o({json:{write:!0,name:\"rasterFunction\"}})],l.prototype,\"functionName\",void 0),t([o({type:u,json:{write:!0,name:\"rasterFunctionArguments\"}})],l.prototype,\"functionArguments\",void 0),t([o()],l.prototype,\"rasterArgumentNames\",void 0),l=t([s(\"esri.layers.support.rasterFunctions.ConvolutionFunction\")],l);const a=l;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../../../chunks/tslib.es6.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as t}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./BaseFunctionArguments.js\";import{MissingBandAction as e}from\"./pixelUtils.js\";var i;let n=i=class extends o{constructor(){super(...arguments),this.bandIds=[],this.missingBandAction=e.bestMatch}clone(){return new i({bandIds:[...this.bandIds],missingBandAction:this.missingBandAction})}};s([r({json:{write:!0}})],n.prototype,\"bandIds\",void 0),s([r({json:{write:!0}})],n.prototype,\"missingBandAction\",void 0),n=i=s([t(\"esri.layers.support.rasterFunctions.ExtractBandFunctionArguments\")],n);const c=n;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isSome as s,isNone as e}from\"../../../core/maybe.js\";import{property as n}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import{getBandMatrix3 as o}from\"./bandIndexUtils.js\";import i from\"./BaseRasterFunction.js\";import a from\"./ExtractBandFunctionArguments.js\";import{MissingBandAction as u}from\"./pixelUtils.js\";let c=class extends i{constructor(){super(...arguments),this.functionName=\"ExtractBand\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,e=t[0],{bandCount:n}=e,{bandIds:r,missingBandAction:o}=this.functionArguments;if(o===u.fail&&r.some((t=>t<0||t>=n)))return{success:!1,supportsGPU:!1,error:\"extract-band-function: invalid bandIds\"};this.outputPixelType=this._getOutputPixelType(\"f32\");const i=e.clone();i.pixelType=this.outputPixelType,i.bandCount=r.length;const{statistics:a,histograms:c}=i;s(a)&&a.length&&(i.statistics=r.map((t=>a[t]||a[a.length-1]))),s(c)&&c.length&&(i.histograms=r.map((t=>c[t]||c[c.length-1]))),this.rasterInfo=i;return{success:!0,supportsGPU:i.bandCount<=3}}_processPixels(t){const s=t.pixelBlocks?.[0];if(e(s))return null;const n=s.pixels.length,r=this.functionArguments.bandIds.map((t=>t>=n?n-1:t));return s.extractBands(r)}_getWebGLParameters(){let t;if(this.isInputBandIdsSwizzled)t=this.swizzledBandSelection.length?this.swizzledBandSelection:[0,1,2];else{t=[...this.functionArguments.bandIds],0===t.length?t=[0,1,2]:t.length<3&&(t[1]=t[1]??t[0],t[2]=t[2]??t[1]);for(let s=0;s<3;s++)t[s]=Math.min(t[s],2)}return{bandIndexMat3:o(t)}}_getInputBandIds(t){const s=t.length;return this.functionArguments.bandIds.map((t=>t>=s?s-1:t)).map((s=>t[s]))}};t([n({json:{write:!0,name:\"rasterFunction\"}})],c.prototype,\"functionName\",void 0),t([n({type:a,json:{write:!0,name:\"rasterFunctionArguments\"}})],c.prototype,\"functionArguments\",void 0),t([n()],c.prototype,\"rasterArgumentNames\",void 0),c=t([r(\"esri.layers.support.rasterFunctions.ExtractBandFunction\")],c);const p=c;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{clone as s}from\"../../../core/lang.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{subclass as t}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as e}from\"../../../core/accessorSupport/decorators/writer.js\";import p from\"./BaseFunctionArguments.js\";var a;let i=a=class extends p{constructor(){super(...arguments),this.rasters=[],this.processAsMultiband=!0}writeRasters(r,s){s.rasters=r.map((r=>\"number\"==typeof r||\"string\"==typeof r?r:r.toJSON()))}clone(){return new a({operation:this.operation,processAsMultiband:this.processAsMultiband,rasters:s(this.rasters)})}};r([o({json:{write:!0}})],i.prototype,\"operation\",void 0),r([o({json:{write:!0}})],i.prototype,\"rasters\",void 0),r([e(\"rasters\")],i.prototype,\"writeRasters\",null),r([o({json:{write:!0}})],i.prototype,\"processAsMultiband\",void 0),i=a=r([t(\"esri.layers.support.rasterFunctions.LocalFunctionArguments\")],i);const c=i;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../core/maybe.js\";import e from\"../PixelBlock.js\";import{localTrigonometricOperation as n,localLogicalOperation as r,localConditionalOperation as o}from\"../rasterFunctionConstants.js\";import{getPixelValueRange as s}from\"../rasterFormats/pixelRangeUtils.js\";const a=new Map;function l(t){return a.get(t)}a.set(n.acos,[0,Math.PI]),a.set(n.asin,[-Math.PI/2,Math.PI/2]),a.set(n.atan,[-Math.PI/2,Math.PI/2]),a.set(n.cos,[-1,1]),a.set(n.sin,[-1,1]),a.set(r.booleanAnd,[0,1]),a.set(r.booleanNot,[0,1]),a.set(r.booleanOr,[0,1]),a.set(r.booleanXOr,[0,1]),a.set(r.equalTo,[0,1]),a.set(r.notEqual,[0,1]),a.set(r.greaterThan,[0,1]),a.set(r.greaterThanEqual,[0,1]),a.set(r.lessThan,[0,1]),a.set(r.lessThanEqual,[0,1]),a.set(r.isNull,[0,1]);const c=[0,2,2,2,1,2,1,1,1,1,1,2,2,1,2,2,2,2,1,2,2,1,1,2,2,1,1,1,2,2,1,1,1,2,2,1,1,1,999,999,999,999,999,999,2,1,2,999,1,1,2,1,1,1,999,999,1,1,999,1,1,2,999,999,2,2,999,999,999,999,999,999,999,999,999,999,3,999,3];function f(e,n=!1){const r=e.map((t=>t.mask)),o=r.filter((e=>t(e))),s=e[0].pixels[0].length;if(0===o.length)return new Uint8Array(s).fill(255);const a=o[0],l=new Uint8Array(a);if(1===o.length)return l;if(!n){for(let t=1;t<o.length;t++){const e=o[t];for(let t=0;t<l.length;t++)l[t]&&(l[t]=e[t]?255:0)}return l}if(o.length!==r.length)return new Uint8Array(s).fill(255);for(let t=1;t<o.length;t++){const e=o[t];for(let t=0;t<l.length;t++)0===l[t]&&(l[t]=e[t]?255:0)}return l}function u(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]+s[e]);return l}function h(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(\"f32\",s);return a.set(o),a}function i(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]-s[e]);return l}function g(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]*s[e]);return l}function p(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(r,s);for(let e=0;e<s;e++)n&&!n[e]||(a[e]=Math.sign(o[e])*Math.floor(Math.abs(o[e])));return a}function m(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]/s[e]);return l}function y(t,e,n){return m(t,e,\"f32\")}function d(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=Math.floor(o[e]/s[e]));return l}function M(t,r,o,s){const a=t[0],l=a.length,c=e.createEmptyBand(o,l);if(s===n.atanh){for(let t=0;t<l;t++)if(r[t]){const e=a[t];Math.abs(e)>=1?r[t]=0:c[t]=Math.atanh(e)}return c}const f=s===n.asin?Math.asin:Math.acos;for(let e=0;e<l;e++)if(r[e]){const t=a[e];Math.abs(t)>1?r[e]=0:c[e]=f(t)}return c}function E(t,n,r,o){const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o(s[e]));return l}function B(t,n,r,o){const[s,a]=t,l=s.length,c=e.createEmptyBand(r,l);for(let e=0;e<l;e++)n&&!n[e]||(c[e]=o(s[e],a[e]));return c}function w(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]&s[e]);return l}function b(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]<<s[e]);return l}function x(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(r,s);for(let e=0;e<s;e++)n&&!n[e]||(a[e]=~o[e]);return a}function A(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]|s[e]);return l}function k(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]>>s[e]);return l}function N(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]^s[e]);return l}function P(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]&&s[e]?1:0);return l}function T(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(r,s);for(let e=0;e<s;e++)n&&!n[e]||(a[e]=o[e]?0:1);return a}function q(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]||s[e]?1:0);return l}function U(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=(o[e]?1:0)^(s[e]?1:0));return l}function I(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]===s[e]?1:0);return l}function j(t,n,r,o){const[s]=t,a=s.length,l=e.createEmptyBand(r,a),c=o===Math.E;for(let e=0;e<a;e++)n&&!n[e]||(l[e]=c?Math.exp(s[e]):o**s[e]);return l}function F(t,e,n){return j(t,e,n,10)}function z(t,e,n){return j(t,e,n,2)}function O(t,e,n){return j(t,e,n,Math.E)}function W(t,n,r,o){const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(s[e]<=0?n[e]=0:l[e]=o(s[e]));return l}function C(t,e,n){return W(t,e,n,Math.log10)}function R(t,e,n){return W(t,e,n,Math.log2)}function S(t,e,n){return W(t,e,n,Math.log)}function X(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]>s[e]?1:0);return l}function v(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]>=s[e]?1:0);return l}function D(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]<s[e]?1:0);return l}function G(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]<=s[e]?1:0);return l}function H(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(r,s);if(!n)return a;for(let e=0;e<s;e++)a[e]=n[e]?0:1;return a}function J(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]%s[e]);return l}function K(t,n,r){const[o]=t,s=o.length,a=e.createEmptyBand(r,s);for(let e=0;e<s;e++)n&&!n[e]||(a[e]=-o[e]);return a}function L(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n&&!n[e]||(l[e]=o[e]===s[e]?0:1);return l}function Q(t,n,r){const[o,s]=t,a=o.length,l=e.createEmptyBand(r,a),c=new Uint8Array(a);for(let e=0;e<a;e++)null!=n&&!n[e]||0!==o[e]||(l[e]=s[e],c[e]=255);return{band:l,mask:c}}function V(t,n,r){const[o,s,a]=t,l=o.length,c=e.createEmptyBand(r,l);for(let e=0;e<l;e++)n&&!n[e]||(c[e]=o[e]?s[e]:a[e]);return c}function Y(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e]){let n=s[e];for(let r=1;r<o;r++){const o=t[r][e];n<o&&(n=o)}l[e]=n}return l}function Z(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e]){let n=s[e];for(let r=1;r<o;r++){const o=t[r][e];n>o&&(n=o)}l[e]=n}return l}function $(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e]){let n=s[e],r=n;for(let s=1;s<o;s++){const o=t[s][e];r<o?r=o:n>o&&(n=o)}l[e]=r-n}return l}function _(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e]){let n=0;for(let r=0;r<o;r++)n+=t[r][e];l[e]=n/o}return l}function tt(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e])for(let n=0;n<o;n++){const r=t[n];l[e]+=r[e]}return l}function et(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)if(!n||n[e]){const n=new Float32Array(o);let r=0;for(let a=0;a<o;a++){const o=t[a];r+=o[e],n[a]=o[e]}r/=o;let s=0;for(let t=0;t<o;t++)s+=(n[t]-r)**2;l[e]=Math.sqrt(s/o)}return l}function nt(t,n,r){const o=t.length;if(o<2)return t[0];const s=Math.floor(o/2),[a]=t,l=a.length,c=e.createEmptyBand(r,l),f=new Float32Array(o),u=o%2==1;for(let e=0;e<l;e++)if(!n||n[e]){for(let n=0;n<o;n++)f[n]=t[n][e];f.sort(),c[e]=u?f[s]:(f[s]+f[s-1])/2}return c}function rt(t,n,r){const[o,s]=t;if(null==s)return o;const a=o.length,l=e.createEmptyBand(r,a);for(let e=0;e<a;e++)n[e]&&(o[e]===s[e]?l[e]=o[e]:n[e]=0);return l}function ot(t,n,r){const o=t.length;if(o<=2)return rt(t,n,r);const s=t[0].length,a=e.createEmptyBand(r,s),l=new Map;for(let e=0;e<s;e++)if(!n||n[e]){let n;l.clear();for(let a=0;a<o;a++)n=t[a][e],l.set(n,l.has(n)?l.get(n)+1:1);let r=0,s=0;for(const t of l.keys())r=l.get(t),r>s&&(s=r,n=t);a[e]=n}return a}function st(t,n,r){const o=t.length;if(o<=2)return rt(t,n,r);const s=t[0].length,a=e.createEmptyBand(r,s),l=new Map;for(let e=0;e<s;e++)if(!n||n[e]){let n;l.clear();for(let a=0;a<o;a++)n=t[a][e],l.set(n,l.has(n)?l.get(n)+1:1);let r=0,s=t.length;for(const t of l.keys())r=l.get(t),r<s&&(s=r,n=t);a[e]=n}return a}function at(t,n,r){const o=t.length;if(o<2)return t[0];const[s]=t,a=s.length,l=e.createEmptyBand(r,a),c=new Set;for(let e=0;e<a;e++)if(!n||n[e]){let n;c.clear();for(let r=0;r<o;r++)n=t[r][e],c.add(n);l[e]=c.size}return l}const lt=new Map,ct=new Map,ft=new Map,ut=new Map;function ht(){lt.size||(lt.set(4,Math.sqrt),lt.set(6,Math.acos),lt.set(7,Math.asin),lt.set(8,Math.atan),lt.set(9,Math.atanh),lt.set(10,Math.abs),lt.set(21,Math.cos),lt.set(22,Math.cosh),lt.set(48,Math.floor),lt.set(49,Math.ceil),lt.set(51,Math.sin),lt.set(52,Math.sinh),lt.set(56,Math.tan),lt.set(57,Math.tanh),lt.set(59,Math.acosh),lt.set(60,Math.asinh),lt.set(65,Math.floor),ct.set(5,Math.pow),ct.set(61,Math.atan2),ft.set(1,u),ft.set(2,i),ft.set(3,g),ft.set(11,w),ft.set(12,b),ft.set(12,b),ft.set(13,x),ft.set(14,A),ft.set(15,k),ft.set(16,N),ft.set(17,P),ft.set(18,T),ft.set(19,q),ft.set(20,U),ft.set(23,m),ft.set(24,I),ft.set(25,O),ft.set(26,F),ft.set(27,z),ft.set(28,X),ft.set(29,v),ft.set(30,p),ft.set(31,H),ft.set(32,h),ft.set(33,D),ft.set(34,G),ft.set(35,S),ft.set(36,C),ft.set(37,R),ft.set(44,J),ft.set(45,K),ft.set(46,L),ft.set(64,y),ft.set(65,d),ft.set(76,V),ft.set(78,V),ut.set(38,ot),ut.set(39,Y),ut.set(40,_),ut.set(41,nt),ut.set(42,Z),ut.set(43,st),ut.set(47,$),ut.set(54,et),ut.set(55,tt),ut.set(58,at),ut.set(66,ot),ut.set(67,Y),ut.set(68,_),ut.set(69,nt),ut.set(70,Z),ut.set(71,st),ut.set(72,$),ut.set(73,et),ut.set(74,tt),ut.set(75,at))}function it(t,e,n,r){let[o,a]=s(n);const l=n.startsWith(\"u\")||n.startsWith(\"s\");l&&(o-=1e-5,a+=1e-5);for(let s=0;s<e.length;s++)if(e[s]){const n=t[s];isNaN(n)||n<o||n>a?e[s]=0:r[s]=l?Math.round(n):n}}function gt(t,s,l={}){ht();let c=f(t,s>=66&&s<=75);const{outputPixelType:u=\"f32\"}=l,h=!ut.has(s)||l.processAsMultiband,i=h?t[0].pixels.length:1,g=[];for(let f=0;f<i;f++){const l=ut.has(s)&&!h?t.flatMap((t=>t.pixels)):t.map((t=>t.pixels[f]));let i,p=!0;if(s===o.setNull){const t=Q(l,c,u);i=t.band,c=t.mask,p=!1}else if(ft.has(s)){i=ft.get(s)(l,c,\"f64\")}else if(lt.has(s))i=s===n.asin||s===n.acos||s===n.atanh?M(l,c,\"f64\",s):E(l,c,\"f64\",lt.get(s));else if(ct.has(s))i=B(l,c,\"f64\",ct.get(s));else if(ut.has(s)){i=ut.get(s)(l,c,\"f64\")}else i=l[0],p=!1;if(p&&s!==r.isNull&&!a.has(s)){const t=e.createEmptyBand(u,i.length);c||(c=new Uint8Array(i.length).fill(255)),it(i,c,u,t),i=t}g.push(i)}const p=t[0];return new e({width:p.width,height:p.height,pixelType:u,mask:s===r.isNull?null:c,pixels:g})}export{l as getOutputDomain,gt as local,c as operandsCount};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";import{localOperators as r}from\"../rasterFunctionConstants.js\";import{getPixelValueRange as n}from\"../rasterFormats/pixelRangeUtils.js\";import i from\"./BaseRasterFunction.js\";import u from\"./LocalFunctionArguments.js\";import{operandsCount as a,getOutputDomain as c,local as p}from\"./localUtils.js\";let m=class extends i{constructor(){super(...arguments),this.functionName=\"Local\",this.functionArguments=null,this.rasterArgumentNames=[\"rasters\"]}_bindSourceRasters(){const{sourceRasterInfos:t}=this,s=t[0],{bandCount:o}=s,{processAsMultiband:e}=this.functionArguments;if(t.some((t=>t.bandCount!==o)))return{success:!1,supportsGPU:!1,error:\"local-function: input rasters do not have same band count\"};const{operation:r,rasters:n}=this.functionArguments,i=a[r];if(!(999===i||n.length===i||n.length<=1&&1===i))return{success:!1,supportsGPU:!1,error:`local-function: the length of functionArguments.rasters does not match operation's requirement: ${i}`};this.outputPixelType=this._getOutputPixelType(\"f32\");const u=s.clone();u.pixelType=this.outputPixelType,u.statistics=null,u.histograms=null,u.colormap=null,u.attributeTable=null,u.bandCount=999!==i||e?o:1;const p=c(r);if(p){u.statistics=[];for(let t=0;t<u.bandCount;t++)u.statistics[t]={min:p[0],max:p[1],avg:(p[0]+p[1])/2,stddev:(p[0]+p[1])/10}}this.rasterInfo=u;return{success:!0,supportsGPU:1===u.bandCount&&i<=3&&(r<11||r>16)}}_processPixels(t){const{pixelBlocks:o}=t;return s(o)||o.some((t=>s(t)))?null:p(o,this.functionArguments.operation,{processAsMultiband:this.functionArguments.processAsMultiband,outputPixelType:this.outputPixelType??void 0})}_getWebGLParameters(){const{operation:t}=this.functionArguments,s=a[t],o=Object.keys(r).find((s=>r[s]===t))?.toLowerCase()??\"undefined\",e=this.outputPixelType??\"f32\";let[i,u]=n(e);const c=e.startsWith(\"u\")||e.startsWith(\"s\");return c&&(i-=1e-4,u+=1e-4),{imageCount:s,operationName:o,domainRange:[i,u],isOutputRounded:c}}};t([o({json:{write:!0,name:\"rasterFunction\"}})],m.prototype,\"functionName\",void 0),t([o({type:u,json:{write:!0,name:\"rasterFunctionArguments\"}})],m.prototype,\"functionArguments\",void 0),t([o()],m.prototype,\"rasterArgumentNames\",void 0),m=t([e(\"esri.layers.support.rasterFunctions.LocalFunction\")],m);const l=m;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./BaseFunctionArguments.js\";import{NoDataInterpretation as s}from\"./pixelUtils.js\";var n;let a=n=class extends o{constructor(){super(...arguments),this.includedRanges=null,this.noDataValues=null,this.noDataInterpretation=s.matchAny}get normalizedNoDataValues(){const{noDataValues:t}=this;if(!t?.length)return null;let e=!1;const r=t.map((t=>{if(\"number\"==typeof t)return e=!0,[t];if(\"string\"==typeof t){const r=t.trim().split(\" \").filter((t=>\"\"!==t.trim())).map((t=>Number(t)));return e=e||r.length>0,0===r.length?null:r}return null}));return e?r:null}clone(){return new n({includedRanges:this.includedRanges?.slice()??[],noDataValues:this.noDataValues?.slice()??[],noDataInterpretation:this.noDataInterpretation})}};t([e({json:{write:!0}})],a.prototype,\"includedRanges\",void 0),t([e({json:{write:!0}})],a.prototype,\"noDataValues\",void 0),t([e()],a.prototype,\"normalizedNoDataValues\",null),t([e({json:{write:!0}})],a.prototype,\"noDataInterpretation\",void 0),a=n=t([r(\"esri.layers.support.rasterFunctions.MaskFunctionArguments\")],a);const i=a;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{NUMBER_MAX_FLOAT32 as e}from\"../../../core/mathUtils.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import n from\"./BaseRasterFunction.js\";import u from\"./MaskFunctionArguments.js\";import{createMaskLUT as a,MAX_MAP_SIZE_GPU as i,NoDataInterpretation as l,mask as c}from\"./pixelUtils.js\";let p=class extends n{constructor(){super(...arguments),this.functionName=\"Mask\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"]}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,this.rasterInfo=t;const{includedRanges:s,normalizedNoDataValues:o}=this.functionArguments;if(!s?.length&&!o?.length)return{success:!1,supportsGPU:!1,error:\"missing includedRanges or noDataValues argument\"};let r=[];for(let u=0;u<t.bandCount;u++){const t=a(e,s?.slice(2*u,2*u+2),o?.[u]);if(null==t){r=null;break}r.push(t)}this.lookups=r;const n=null!=o&&o.every((t=>t?.length===o[0]?.length));return{success:!0,supportsGPU:(!s||s.length<=2*i)&&(!o||n&&o[0].length<=i)}}_processPixels(t){const e=t.pixelBlocks?.[0];if(s(e))return null;const{outputPixelType:o,lookups:r}=this,{includedRanges:n,noDataInterpretation:u,normalizedNoDataValues:a}=this.functionArguments,i=u===l.matchAll;return c(e,{includedRanges:n,noDataValues:a,outputPixelType:o,matchAll:i,lookups:r})}_getWebGLParameters(){const{includedRanges:t,normalizedNoDataValues:s}=this.functionArguments,o=new Float32Array(i);o.fill(e),s?.[0]?.length&&o.set(s[0]);const r=new Float32Array(i);for(let n=0;n<r.length;n+=2)r[n]=t?.[n]??-e,r[n+1]=t?.[n+1]??e;return t&&t.length&&r.set(t),{bandCount:this.sourceRasterInfos[0].bandCount,noDataValues:o,includedRanges:r}}};t([o({json:{write:!0,name:\"rasterFunction\"}})],p.prototype,\"functionName\",void 0),t([o({type:u,json:{write:!0,name:\"rasterFunctionArguments\"}})],p.prototype,\"functionArguments\",void 0),t([o()],p.prototype,\"rasterArgumentNames\",void 0),t([o({json:{write:!0}})],p.prototype,\"lookups\",void 0),p=t([r(\"esri.layers.support.rasterFunctions.MaskFunction\")],p);const m=p;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import e from\"./BaseFunctionArguments.js\";var o;let i=o=class extends e{constructor(){super(...arguments),this.visibleBandID=0,this.infraredBandID=1,this.scientificOutput=!1}clone(){const{visibleBandID:r,infraredBandID:t,scientificOutput:s}=this;return new o({visibleBandID:r,infraredBandID:t,scientificOutput:s})}};r([t({json:{write:!0}})],i.prototype,\"visibleBandID\",void 0),r([t({json:{write:!0}})],i.prototype,\"infraredBandID\",void 0),r([t({json:{write:!0}})],i.prototype,\"scientificOutput\",void 0),i=o=r([s(\"esri.layers.support.rasterFunctions.NDVIFunctionArguments\")],i);const n=i;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";import{calculateNDVI as n,getBandMatrix3 as o}from\"./bandIndexUtils.js\";import i from\"./BaseRasterFunction.js\";import u from\"./NDVIFunctionArguments.js\";let c=class extends i{constructor(){super(...arguments),this.functionName=\"NDVI\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"]}_bindSourceRasters(){const{scientificOutput:t}=this.functionArguments;this.outputPixelType=this._getOutputPixelType(t?\"f32\":\"u8\");const s=this.sourceRasterInfos[0].clone();s.pixelType=this.outputPixelType,s.colormap=null,s.histograms=null,s.bandCount=1;const[r,e,n,o]=t?[-1,1,0,.1]:[0,200,100,10];return s.statistics=[{min:r,max:e,avg:n,stddev:o}],this.rasterInfo=s,{success:!0,supportsGPU:!0}}_processPixels(t){const r=t.pixelBlocks?.[0];if(s(r))return null;const{visibleBandID:e,infraredBandID:o,scientificOutput:i}=this.functionArguments;return n(r,e,o,!i)}_getWebGLParameters(){const{visibleBandID:t,infraredBandID:s,scientificOutput:r}=this.functionArguments,e=this.isInputBandIdsSwizzled?[0,1,2]:[s,t,0];return{bandIndexMat3:o(e),scaled:!r}}_getInputBandIds(t){const{visibleBandID:s,infraredBandID:r}=this.functionArguments;return[r,s,0].map((s=>t[s]))}};t([r({json:{write:!0,name:\"rasterFunction\"}})],c.prototype,\"functionName\",void 0),t([r({type:u,json:{write:!0,name:\"rasterFunctionArguments\"}})],c.prototype,\"functionArguments\",void 0),t([r()],c.prototype,\"rasterArgumentNames\",void 0),c=t([e(\"esri.layers.support.rasterFunctions.NDVIFunction\")],c);const a=c;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import e from\"./BaseFunctionArguments.js\";var n;let a=n=class extends e{constructor(){super(...arguments),this.inputRanges=null,this.outputValues=null,this.noDataRanges=null,this.allowUnmatched=!1,this.isLastInputRangeInclusive=!1}clone(){return new n({inputRanges:[...this.inputRanges],outputValues:[...this.outputValues],noDataRanges:[...this.noDataRanges],allowUnmatched:this.allowUnmatched,isLastInputRangeInclusive:this.isLastInputRangeInclusive})}};t([s({json:{write:!0}})],a.prototype,\"inputRanges\",void 0),t([s({json:{write:!0}})],a.prototype,\"outputValues\",void 0),t([s({json:{write:!0}})],a.prototype,\"noDataRanges\",void 0),t([s({json:{write:!0}})],a.prototype,\"allowUnmatched\",void 0),t([s({json:{write:!0}})],a.prototype,\"isLastInputRangeInclusive\",void 0),a=n=t([o(\"esri.layers.support.rasterFunctions.RemapFunctionArguments\")],a);const r=a;export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{NUMBER_MAX_FLOAT32 as e}from\"../../../core/mathUtils.js\";import{isSome as s,isNone as n}from\"../../../core/maybe.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import{getPixelValueRange as r}from\"../rasterFormats/pixelRangeUtils.js\";import u from\"./BaseRasterFunction.js\";import{createRemapLUT as i,MAX_MAP_SIZE_GPU as l,lookupPixels as p,lookupBandValues as c,remap as m}from\"./pixelUtils.js\";import g from\"./RemapFunctionArguments.js\";let h=class extends u{constructor(){super(...arguments),this.functionName=\"Remap\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.lookup=null}_bindSourceRasters(){const t=this.sourceRasterInfos[0].clone(),{pixelType:e}=t;this.outputPixelType=this._getOutputPixelType(e),t.pixelType=this.outputPixelType,t.colormap=null,t.histograms=null,t.bandCount=1,t.attributeTable=null;const{statistics:n}=t,{allowUnmatched:o,outputValues:a,inputRanges:r,noDataRanges:u,isLastInputRangeInclusive:p}=this.functionArguments;if(s(n)&&n.length&&a?.length)if(o){const e=Math.min.apply(null,[...a,n[0].min]),s=Math.max.apply(null,[...a,n[0].max]);t.statistics=[{...n[0],min:e,max:s}]}else{let e=a[0],s=e;for(let t=0;t<a.length;t++)e=e>a[t]?a[t]:e,s=s>a[t]?s:a[t];t.statistics=[{...n[0],min:e,max:s}]}this.rasterInfo=t,this.lookup=o?null:i({srcPixelType:e,inputRanges:r,outputValues:a,noDataRanges:u,allowUnmatched:o,isLastInputRangeInclusive:p,outputPixelType:this.outputPixelType});return{success:!0,supportsGPU:(!a||a.length<=l)&&(!u||u.length<=l)}}_processPixels(t){const e=t.pixelBlocks?.[0];if(n(e))return null;const{lookup:o,outputPixelType:a}=this;if(o){const t=p(e,{lut:[o.lut],offset:o.offset,outputPixelType:a});return s(t)&&o.mask&&(t.mask=c(e.pixels[0],e.mask,o.mask,o.offset,\"u8\")),t}const{inputRanges:r,outputValues:u,noDataRanges:i,allowUnmatched:l,isLastInputRangeInclusive:g}=this.functionArguments;return m(e,{inputRanges:r,outputValues:u,noDataRanges:i,outputPixelType:a,allowUnmatched:l,isLastInputRangeInclusive:g})}_getWebGLParameters(){const{allowUnmatched:t,inputRanges:s,outputValues:n,noDataRanges:o,isLastInputRangeInclusive:a}=this.functionArguments,u=new Float32Array(3*l),i=1e-5,p=n.length;if(s?.length){let t=0,o=0;for(let r=0;r<u.length;r+=3)u[r]=s[t++]??e-1,u[r+1]=s[t++]??e,u[r+2]=n[o++]??0,o<=p&&(r>0&&(u[r]-=i),(o<p||!a)&&(u[r+1]-=i))}const c=new Float32Array(2*l);c.fill(e),o?.length&&c.set(o);return{allowUnmatched:t,rangeMaps:u,noDataRanges:c,clampRange:r(this.outputPixelType)}}};t([o({json:{write:!0,name:\"rasterFunction\"}})],h.prototype,\"functionName\",void 0),t([o({type:g,json:{write:!0,name:\"rasterFunctionArguments\"}})],h.prototype,\"functionArguments\",void 0),t([o()],h.prototype,\"rasterArgumentNames\",void 0),t([o({json:{write:!0}})],h.prototype,\"lookup\",void 0),h=t([a(\"esri.layers.support.rasterFunctions.RemapFunction\")],h);const f=h;export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{JSONMap as r}from\"../../../core/jsonMap.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{enumeration as t}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import p from\"./BaseFunctionArguments.js\";var i;const c=new r({1:\"degree\",2:\"percent-rise\",3:\"adjusted\"},{useNumericKeys:!0});let a=i=class extends p{constructor(){super(...arguments),this.slopeType=\"degree\",this.zFactor=1,this.pixelSizePower=.664,this.pixelSizeFactor=.024,this.removeEdgeEffect=!1}clone(){return new i({slopeType:this.slopeType,zFactor:this.zFactor,pixelSizePower:this.pixelSizePower,pixelSizeFactor:this.pixelSizeFactor,removeEdgeEffect:this.removeEdgeEffect,raster:this.raster})}};e([t(c)],a.prototype,\"slopeType\",void 0),e([o({type:Number,json:{write:!0}})],a.prototype,\"zFactor\",void 0),e([o({type:Number,json:{name:\"psPower\",write:!0}})],a.prototype,\"pixelSizePower\",void 0),e([o({type:Number,json:{name:\"psZFactor\",write:!0}})],a.prototype,\"pixelSizeFactor\",void 0),e([o({type:Boolean,json:{write:!0}})],a.prototype,\"removeEdgeEffect\",void 0),a=i=e([s(\"esri.layers.support.rasterFunctions.SlopeFunctionArguments\")],a);const n=a;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import{isNone as t}from\"../../../core/maybe.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./BaseRasterFunction.js\";import i from\"./SlopeFunctionArguments.js\";import{slope as n}from\"./surfaceUtils.js\";const p=1/111e3;let c=class extends o{constructor(){super(...arguments),this.functionName=\"Slope\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.isGCS=!1}_bindSourceRasters(){this.outputPixelType=this._getOutputPixelType(\"f32\");const e=this.sourceRasterInfos[0].clone();return e.pixelType=this.outputPixelType,e.statistics=\"percent-rise\"!==this.functionArguments.slopeType?[{min:0,max:90,avg:1,stddev:1}]:null,e.histograms=null,e.colormap=null,e.attributeTable=null,e.bandCount=1,this.rasterInfo=e,this.isGCS=e.spatialReference?.isGeographic??!1,{success:!0,supportsGPU:!0}}_processPixels(e){const s=e.pixelBlocks?.[0];if(t(s))return null;const{zFactor:r,slopeType:o,pixelSizePower:i,pixelSizeFactor:p}=this.functionArguments,{isGCS:c}=this,{extent:u}=e,a=u?{x:u.width/s.width,y:u.height/s.height}:{x:1,y:1};return n(s,{zFactor:r,slopeType:o,pixelSizePower:i,pixelSizeFactor:p,isGCS:c,resolution:a})}_getWebGLParameters(){const{zFactor:e,slopeType:t,pixelSizeFactor:s,pixelSizePower:r}=this.functionArguments;return{zFactor:this.isGCS&&e>=1?e*p:e,slopeType:t,pixelSizeFactor:s??0,pixelSizePower:r??0}}};e([s({json:{write:!0,name:\"rasterFunction\"}})],c.prototype,\"functionName\",void 0),e([s({type:i,json:{write:!0,name:\"rasterFunctionArguments\"}})],c.prototype,\"functionArguments\",void 0),e([s()],c.prototype,\"rasterArgumentNames\",void 0),e([s({json:{write:!0}})],c.prototype,\"isGCS\",void 0),c=e([r(\"esri.layers.support.rasterFunctions.SlopeFunction\")],c);const u=c;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{clone as s}from\"../../../core/lang.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{reader as o}from\"../../../core/accessorSupport/decorators/reader.js\";import{subclass as e}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as i}from\"../../../core/accessorSupport/decorators/writer.js\";import a from\"./BaseFunctionArguments.js\";var c;let n=c=class extends a{constructor(){super(...arguments),this.statistics=null,this.histograms=null}readStatistics(t,s){if(!t?.length)return null;const r=[];return t.forEach((t=>{const s={min:t.min,max:t.max,avg:t.avg??t.mean,stddev:t.stddev??t.standardDeviation};r.push(s)})),r}writeStatistics(t,s,r){if(!t?.length)return;const o=[];t.forEach((t=>{const s={...t,mean:t.avg,standardDeviation:t.stddev};delete s.avg,delete s.stddev,o.push(s)})),s[r]=o}clone(){return new c({statistics:s(this.statistics),histograms:s(this.histograms)})}};t([r({json:{write:!0}})],n.prototype,\"statistics\",void 0),t([o(\"statistics\")],n.prototype,\"readStatistics\",null),t([i(\"statistics\")],n.prototype,\"writeStatistics\",null),t([r({json:{write:!0}})],n.prototype,\"histograms\",void 0),n=c=t([e(\"esri.layers.support.rasterFunctions.StatisticsHistogramFunctionArguments\")],n);const p=n;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../../../chunks/tslib.es6.js\";import{property as t}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as r}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"./BaseRasterFunction.js\";import e from\"./StatisticsHistogramFunctionArguments.js\";let i=class extends o{constructor(){super(...arguments),this.functionName=\"StatisticsHistogram\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.isNoopProcess=!0}_bindSourceRasters(){const s=this.sourceRasterInfos[0];this.outputPixelType=this._getOutputPixelType(\"u8\");const t=s.clone(),{statistics:r,histograms:o}=this.functionArguments;return o&&(t.histograms=o),r&&(t.statistics=r),this.rasterInfo=t,{success:!0,supportsGPU:!0}}_processPixels(s){return s.pixelBlocks?.[0]}};s([t({json:{write:!0,name:\"rasterFunction\"}})],i.prototype,\"functionName\",void 0),s([t({type:e,json:{write:!0,name:\"rasterFunctionArguments\"}})],i.prototype,\"functionArguments\",void 0),s([t()],i.prototype,\"rasterArgumentNames\",void 0),s([t({json:{write:!0}})],i.prototype,\"indexedColormap\",void 0),s([t()],i.prototype,\"isNoopProcess\",void 0),i=s([r(\"esri.layers.support.rasterFunctions.StatisticsHistogramFunction\")],i);const n=i;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{JSONMap as e}from\"../../../core/jsonMap.js\";import{clone as o}from\"../../../core/lang.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{enumeration as s}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as a}from\"../../../core/accessorSupport/decorators/writer.js\";import m from\"./BaseFunctionArguments.js\";var n;const p=new e({0:\"none\",3:\"standard-deviation\",4:\"histogram-equalization\",5:\"min-max\",6:\"percent-clip\",9:\"sigmoid\"},{useNumericKeys:!0});let c=n=class extends m{constructor(){super(...arguments),this.computeGamma=!1,this.dynamicRangeAdjustment=!1,this.gamma=[],this.histograms=null,this.statistics=null,this.stretchType=\"none\",this.useGamma=!1}writeStatistics(t,e,o){t?.length&&(Array.isArray(t[0])||(t=t.map((t=>[t.min,t.max,t.avg,t.stddev]))),e[o]=t)}clone(){return new n({stretchType:this.stretchType,outputMin:this.outputMin,outputMax:this.outputMax,useGamma:this.useGamma,computeGamma:this.computeGamma,statistics:o(this.statistics),gamma:o(this.gamma),sigmoidStrengthLevel:this.sigmoidStrengthLevel,numberOfStandardDeviations:this.numberOfStandardDeviations,minPercent:this.minPercent,maxPercent:this.maxPercent,histograms:o(this.histograms),dynamicRangeAdjustment:this.dynamicRangeAdjustment,raster:this.raster})}};t([r({type:Boolean,json:{write:!0}})],c.prototype,\"computeGamma\",void 0),t([r({type:Boolean,json:{name:\"dra\",write:!0}})],c.prototype,\"dynamicRangeAdjustment\",void 0),t([r({type:[Number],json:{write:!0}})],c.prototype,\"gamma\",void 0),t([r()],c.prototype,\"histograms\",void 0),t([r({type:Number,json:{write:!0}})],c.prototype,\"maxPercent\",void 0),t([r({type:Number,json:{write:!0}})],c.prototype,\"minPercent\",void 0),t([r({type:Number,json:{write:!0}})],c.prototype,\"numberOfStandardDeviations\",void 0),t([r({type:Number,json:{name:\"max\",write:!0}})],c.prototype,\"outputMax\",void 0),t([r({type:Number,json:{name:\"min\",write:!0}})],c.prototype,\"outputMin\",void 0),t([r({type:Number,json:{write:!0}})],c.prototype,\"sigmoidStrengthLevel\",void 0),t([r({json:{type:[[Number]],write:!0}})],c.prototype,\"statistics\",void 0),t([a(\"statistics\")],c.prototype,\"writeStatistics\",null),t([s(p)],c.prototype,\"stretchType\",void 0),t([r({type:Boolean,json:{write:!0}})],c.prototype,\"useGamma\",void 0),c=n=t([i(\"esri.layers.support.rasterFunctions.StretchFunctionArguments\")],c);const u=c;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import{isNone as s}from\"../../../core/maybe.js\";import{property as e}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import r from\"./BaseRasterFunction.js\";import{lookupPixels as u}from\"./pixelUtils.js\";import n from\"./StretchFunctionArguments.js\";import{getStretchCutoff as i,createStretchLUT as a,stretch as p,computeGammaCorrection as c}from\"./stretchUtils.js\";let m=class extends r{constructor(){super(...arguments),this.functionName=\"Stretch\",this.functionArguments=null,this.rasterArgumentNames=[\"raster\"],this.lookup=null,this.cutOffs=null}_bindSourceRasters(){this.lookup=null,this.cutOffs=null;const t=this.sourceRasterInfos[0],{pixelType:s}=t,{functionArguments:e}=this,{dynamicRangeAdjustment:o,gamma:r,useGamma:u}=e;if(!o&&[\"u8\",\"u16\",\"s8\",\"s16\"].includes(s)){const o=i(e.toJSON(),{rasterInfo:t}),n=this._isOutputRoundingNeeded()?\"round\":\"float\";this.lookup=a({pixelType:s,...o,gamma:u?r:null,rounding:n}),this.cutOffs=o}else o||(this.cutOffs=i(e.toJSON(),{rasterInfo:t}));this.outputPixelType=this._getOutputPixelType(s);const n=t.clone();n.pixelType=this.outputPixelType,n.statistics=null,n.histograms=null,n.colormap=null,n.attributeTable=null,\"u8\"===this.outputPixelType&&(n.keyProperties.DataType=\"processed\"),this.rasterInfo=n;return{success:!0,supportsGPU:!o}}_processPixels(t){const e=t.pixelBlocks?.[0];if(s(e))return e;const{lookup:o}=this;if(o)return u(e,{...o,outputPixelType:this.rasterInfo.pixelType});const{functionArguments:r}=this,n=this.cutOffs||i(r.toJSON(),{rasterInfo:this.sourceRasterInfos[0],pixelBlock:e}),a=r.useGamma?r.gamma:null;return p(e,{...n,gamma:a,outputPixelType:this.outputPixelType})}_getWebGLParameters(){const{outputMin:t=0,outputMax:s=255,gamma:e,useGamma:o}=this.functionArguments,r=this.rasterInfo.bandCount>=2?3:1,u=o&&e&&e.length?c(r,e):[1,1,1],{minCutOff:n,maxCutOff:i}=this.cutOffs??{minCutOff:[0,0,0],maxCutOff:[255,255,255]};1===n.length&&(n[1]=n[2]=n[0],i[1]=i[2]=i[0]);const a=new Float32Array(r);let p;for(p=0;p<r;p++)a[p]=(s-t)/(i[p]-n[p]);const m=this._isOutputRoundingNeeded();return{bandCount:r,outMin:t,outMax:s,minCutOff:n,maxCutOff:i,factor:a,useGamma:o,gamma:o&&e?e:[1,1,1],gammaCorrection:o&&u?u:[1,1,1],stretchType:this.functionArguments.stretchType,isOutputRounded:m,type:\"stretch\"}}};t([e({json:{write:!0,name:\"rasterFunction\"}})],m.prototype,\"functionName\",void 0),t([e({type:n,json:{write:!0,name:\"rasterFunctionArguments\"}})],m.prototype,\"functionArguments\",void 0),t([e()],m.prototype,\"rasterArgumentNames\",void 0),t([e({json:{write:!0}})],m.prototype,\"lookup\",void 0),t([e({json:{write:!0}})],m.prototype,\"cutOffs\",void 0),m=t([o(\"esri.layers.support.rasterFunctions.StretchFunction\")],m);const l=m;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{toRGB as t}from\"../../../core/colorUtils.js\";import r from\"../../../core/Error.js\";import{clone as e}from\"../../../core/lang.js\";import n from\"./AspectFunction.js\";import o from\"./BandArithmeticFunction.js\";import s from\"./ColormapFunction.js\";import a from\"./CompositeBandFunction.js\";import i from\"./ConvolutionFunction.js\";import u from\"./ExtractBandFunction.js\";import c from\"./LocalFunction.js\";import m from\"./MaskFunction.js\";import l from\"./NDVIFunction.js\";import p from\"./RemapFunction.js\";import f from\"./SlopeFunction.js\";import d from\"./StatisticsHistogramFunction.js\";import g from\"./StretchFunction.js\";const A=new Map;function F(t,r,e=[\"raster\"],n=\"\"){A.set(t,{desc:n,ctor:r,rasterArgumentNames:e})}function y(){const t=new Set;return A.forEach(((r,e)=>t.add(e))),t}function h(t,r){const{rasterFunctionArguments:e}=t;if(!e)return;(e.rasters||[e.raster]).forEach((t=>{t&&\"number\"!=typeof t&&(\"string\"==typeof t?t.startsWith(\"http\")&&(r.includes(t)||r.push(t)):\"rasterFunctionArguments\"in t&&h(t,r))}))}function C(t,n){if(n=n??{},\"function\"in(t=e(t))&&\"arguments\"in t&&t.arguments&&(t=D(t,n)),\"rasterFunction\"in t)return v(t=S(t),n);throw new r(\"raster-function-helper\",\"unsupported raster function json.\")}function N(t,r){return\"rasters\"===r[0]&&Array.isArray(t.rasters)?t.rasters:r.map((r=>t[r]))}function b(t){return!!(t&&\"object\"==typeof t&&t.rasterFunction&&t.rasterFunctionArguments)}function S(t){const{rasterFunction:r,rasterFunctionArguments:e}=t,n={};for(const o in e){let t=e[o];const r=o.toLowerCase();if(\"rasters\"===r&&Array.isArray(t))n.rasters=t.map((t=>b(t)?S(t):t));else switch(b(t)&&(t=S(t)),r){case\"dra\":n.dra=t;break;case\"pspower\":n.psPower=t;break;case\"pszfactor\":n.psZFactor=t;break;case\"bandids\":n.bandIds=t;break;default:n[o[0].toLowerCase()+o.slice(1)]=t}}return\"Local\"!==r||n.rasters?.length||(n.rasters=[\"$$\"]),{...t,rasterFunctionArguments:n}}function v(t,e){const{rasterFunction:n,rasterFunctionArguments:o}=t,s=t.outputPixelType?.toLowerCase();if(null==n||!A.has(n))throw new r(\"raster-function-helper\",`unsupported raster function: ${n}`);const a=A.get(n),i=(\"function\"==typeof a.ctor?a.ctor:a.ctor.default).fromJSON({...t,outputPixelType:s}),{rasterArgumentNames:u}=i,c=[],m=N(o,u),l=\"rasters\"===u[0],p=[];for(let r=0;r<m.length;r++){const t=m[r];let n;null==t||\"string\"==typeof t&&t.startsWith(\"$\")?c.push(e?.raster):\"string\"==typeof t?e[t]&&c.push(e[t]):\"number\"!=typeof t&&\"rasterFunction\"in t&&(n=v(t,e),l||(i.functionArguments[u[r]]=n),c.push(n)),l&&p.push(n??t)}if(l&&(i.functionArguments.rasters=p),e){i.sourceRasters=c;const t=e.raster?.url;t&&(i.mainPrimaryRasterId=t)}return i}function j(t,r){if(t&&r)for(const e in t){const n=t[e];n&&\"object\"==typeof n&&(n.function&&n.arguments?j(n.arguments,r):\"RasterFunctionVariable\"===n.type&&null!=r[n.name]&&(n.value=r[n.name]))}}function R(t){if(!t||\"object\"!=typeof t)return t;if(Array.isArray(t)&&0===t.length)return 0===t.length?null:[\"number\",\"string\"].includes(typeof t[0])?t:t.map((t=>R(t)));if(\"value\"in t&&[\"number\",\"string\",\"boolean\"].includes(typeof t.value))return t.value;if(!(\"type\"in t))return t;switch(t.type){case\"Scalar\":return t.value;case\"AlgorithmicColorRamp\":return w(t);case\"MultiPartColorRamp\":return{type:\"multipart\",colorRamps:t.ArrayOfColorRamp.map(w)};case\"ArgumentArray\":return t.elements?.length?\"RasterStatistics\"===t.elements[0].type?t.elements:\"RasterFunctionVariable\"===t.elements[0].type?t.elements.map((t=>null!=t.value?R(t.value):t.name.toLowerCase().includes(\"raster\")?\"$$\":null)):t:t.elements;default:return t}}function w(r){const e=r.algorithm??\"esriHSVAlgorithm\";let{FromColor:n,ToColor:o}=r;if(!Array.isArray(n)){const{r,g:e,b:o}=t({h:n.Hue,s:n.Saturation,v:n.Value});n=[r,e,o,n.AlphaValue]}if(!Array.isArray(o)){const{r,g:e,b:n}=t({h:o.Hue,s:o.Saturation,v:o.Value});o=[r,e,n,o.AlphaValue]}return{type:\"algorithmic\",algorithm:e,fromColor:n,toColor:o}}function D(t,r){r&&j(t,r);const e={};return V(t,e),e}function V(t,r){if(!t||!r)return;const{function:e,arguments:n}=t;if(!e||!n)return;r.rasterFunction=e.type.replace(\"Function\",\"\"),r.outputPixelType=e.pixelType;const o={};r.rasterFunctionArguments=o;for(const s in n){const t=n[s];\"object\"==typeof t&&(\"function\"in t&&t.function&&t.arguments?(r.rasterFunctionArguments[s]={},V(t,r.rasterFunctionArguments[s])):\"value\"in t&&(o[s]=R(t.value)))}switch(o.DEM&&!o.Raster&&(o.Raster=o.DEM,delete o.DEM),r.rasterFunction){case\"Stretch\":k(o);break;case\"Colormap\":T(o);break;case\"Convolution\":x(o);break;case\"Mask\":B(o)}}function k(t){t.Statistics?.length&&\"object\"==typeof t.Statistics&&(t.Statistics=t.Statistics.map((t=>[t.min,t.max,t.mean,t.standardDeviation]))),null!=t.NumberOfStandardDeviation&&(t.NumberOfStandardDeviations=t.NumberOfStandardDeviation,delete t.NumberOfStandardDeviation)}function T(t){\"randomcolorramp\"===t.ColorRamp?.type?.toLowerCase()&&(delete t.ColorRamp,t.ColormapName=\"Random\"),0===t.ColorSchemeType&&delete t.ColorRamp}function x(t){null!=t.ConvolutionType&&(t.Type=t.ConvolutionType,delete t.ConvolutionType)}function B(t){t.NoDataValues?.length&&\"string\"==typeof t.NoDataValues[0]&&(t.NoDataValues=t.NoDataValues.filter((t=>\"\"!==t)).map((t=>Number(t))))}A.set(\"Aspect\",{desc:\"Aspect Function\",ctor:n,rasterArgumentNames:[\"raster\"]}),A.set(\"BandArithmetic\",{desc:\"Band Arithmetic Function\",ctor:o,rasterArgumentNames:[\"raster\"]}),A.set(\"Colormap\",{desc:\"Colormap Function\",ctor:s,rasterArgumentNames:[\"raster\"]}),A.set(\"CompositeBand\",{desc:\"CompositeBand Function\",ctor:a,rasterArgumentNames:[\"rasters\"]}),A.set(\"Convolution\",{desc:\"Convolution Function\",ctor:i,rasterArgumentNames:[\"raster\"]}),A.set(\"ExtractBand\",{desc:\"ExtractBand Function\",ctor:u,rasterArgumentNames:[\"raster\"]}),A.set(\"Local\",{desc:\"Local Function\",ctor:c,rasterArgumentNames:[\"rasters\"]}),A.set(\"Mask\",{desc:\"Mask Function\",ctor:m,rasterArgumentNames:[\"raster\"]}),A.set(\"NDVI\",{desc:\"NDVI Function\",ctor:l,rasterArgumentNames:[\"raster\"]}),A.set(\"Remap\",{desc:\"Remap Function\",ctor:p,rasterArgumentNames:[\"raster\"]}),A.set(\"Slope\",{desc:\"Slope Function\",ctor:f,rasterArgumentNames:[\"raster\"]}),A.set(\"StatisticsHistogram\",{desc:\"Statistics Histogram Function\",ctor:d,rasterArgumentNames:[\"raster\"]}),A.set(\"Stretch\",{desc:\"Stretch Function\",ctor:g,rasterArgumentNames:[\"raster\"]});export{C as create,h as getPrimaryRasterUrls,N as getRasterValues,y as getSupportedFunctions,F as register};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../../core/JSONSupport.js\";import{property as s}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";let t=class extends e{get affectsPixelSize(){return!1}forwardTransform(r){return r}inverseTransform(r){return r}};r([s()],t.prototype,\"affectsPixelSize\",null),r([s({json:{write:!0}})],t.prototype,\"spatialReference\",void 0),t=r([o(\"esri.layers.support.rasterTransforms.BaseRasterTransform\")],t);const a=t;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{enumeration as s}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{subclass as t}from\"../../../core/accessorSupport/decorators/subclass.js\";import e from\"./BaseRasterTransform.js\";let a=class extends e{constructor(){super(...arguments),this.type=\"gcs-shift\",this.tolerance=1e-8}forwardTransform(r){return\"point\"===(r=r.clone()).type?(r.x>180+this.tolerance&&(r.x-=360),r):(r.xmin>=180-this.tolerance?(r.xmax-=360,r.xmin-=360):r.xmax>180+this.tolerance&&(r.xmin=-180,r.xmax=180),r)}inverseTransform(r){return\"point\"===(r=r.clone()).type?(r.x<-this.tolerance&&(r.x+=360),r):(r.xmin<-this.tolerance&&(r.xmin+=360,r.xmax+=360),r)}};r([s({GCSShiftXform:\"gcs-shift\"})],a.prototype,\"type\",void 0),r([o()],a.prototype,\"tolerance\",void 0),a=r([t(\"esri.layers.support.rasterTransforms.GCSShiftTransform\")],a);const c=a;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import\"../../../core/Logger.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import\"../../../core/Error.js\";import\"../../../core/has.js\";import{enumeration as o}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import t from\"./BaseRasterTransform.js\";let e=class extends t{constructor(){super(...arguments),this.type=\"identity\"}};r([o({IdentityXform:\"identity\"})],e.prototype,\"type\",void 0),e=r([s(\"esri.layers.support.rasterTransforms.IdentityTransform\")],e);const p=e;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{enumeration as o}from\"../../../core/accessorSupport/decorators/enumeration.js\";import{reader as t}from\"../../../core/accessorSupport/decorators/reader.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import{writer as n}from\"../../../core/accessorSupport/decorators/writer.js\";import i from\"./BaseRasterTransform.js\";import f from\"../../../geometry/Point.js\";import p from\"../../../geometry/Extent.js\";function l(e,r,o){const{x:t,y:s}=r;if(o<2){return{x:e[0]+t*e[2]+s*e[4],y:e[1]+t*e[3]+s*e[5]}}if(2===o){const r=t*t,o=s*s,n=t*s;return{x:e[0]+t*e[2]+s*e[4]+r*e[6]+n*e[8]+o*e[10],y:e[1]+t*e[3]+s*e[5]+r*e[7]+n*e[9]+o*e[11]}}const n=t*t,i=s*s,f=t*s,p=n*t,l=n*s,a=t*i,c=s*i;return{x:e[0]+t*e[2]+s*e[4]+n*e[6]+f*e[8]+i*e[10]+p*e[12]+l*e[14]+a*e[16]+c*e[18],y:e[1]+t*e[3]+s*e[5]+n*e[7]+f*e[9]+i*e[11]+p*e[13]+l*e[15]+a*e[17]+c*e[19]}}function a(e,r,o){const{xmin:t,ymin:s,xmax:n,ymax:i,spatialReference:f}=r;let a=[];if(o<2)a.push({x:t,y:i}),a.push({x:n,y:i}),a.push({x:t,y:s}),a.push({x:n,y:s});else{let e=10;for(let r=0;r<e;r++)a.push({x:t,y:s+(i-s)*r/(e-1)}),a.push({x:n,y:s+(i-s)*r/(e-1)});e=8;for(let r=1;r<=e;r++)a.push({x:t+(n-t)*r/e,y:s}),a.push({x:t+(n-t)*r/e,y:i})}a=a.map((r=>l(e,r,o)));const c=a.map((e=>e.x)),u=a.map((e=>e.y));return new p({xmin:Math.min.apply(null,c),xmax:Math.max.apply(null,c),ymin:Math.min.apply(null,u),ymax:Math.max.apply(null,u),spatialReference:f})}function c(e){const[r,o,t,s,n,i]=e,f=t*i-n*s,p=n*s-t*i;return[(n*o-r*i)/f,(t*o-r*s)/p,i/f,s/p,-n/f,-t/p]}let u=class extends i{constructor(){super(...arguments),this.polynomialOrder=1,this.type=\"polynomial\"}readForwardCoefficients(e,r){const{coeffX:o,coeffY:t}=r;if(!o?.length||!t?.length||o.length!==t.length)return null;const s=[];for(let n=0;n<o.length;n++)s.push(o[n]),s.push(t[n]);return s}writeForwardCoefficients(e,r,o){const t=[],s=[];for(let n=0;n<e?.length;n++)n%2==0?t.push(e[n]):s.push(e[n]);r.coeffX=t,r.coeffY=s}get inverseCoefficients(){let e=this._get(\"inverseCoefficients\");const r=this._get(\"forwardCoefficients\");return!e&&r&&this.polynomialOrder<2&&(e=c(r)),e}set inverseCoefficients(e){this._set(\"inverseCoefficients\",e)}readInverseCoefficients(e,r){const{inverseCoeffX:o,inverseCoeffY:t}=r;if(!o?.length||!t?.length||o.length!==t.length)return null;const s=[];for(let n=0;n<o.length;n++)s.push(o[n]),s.push(t[n]);return s}writeInverseCoefficients(e,r,o){const t=[],s=[];for(let n=0;n<e?.length;n++)n%2==0?t.push(e[n]):s.push(e[n]);r.inverseCoeffX=t,r.inverseCoeffY=s}get affectsPixelSize(){return this.polynomialOrder>0}forwardTransform(e){if(\"point\"===e.type){const r=l(this.forwardCoefficients,e,this.polynomialOrder);return new f({x:r.x,y:r.y,spatialReference:e.spatialReference})}return a(this.forwardCoefficients,e,this.polynomialOrder)}inverseTransform(e){if(\"point\"===e.type){const r=l(this.inverseCoefficients,e,this.polynomialOrder);return new f({x:r.x,y:r.y,spatialReference:e.spatialReference})}return a(this.inverseCoefficients,e,this.polynomialOrder)}};e([r({json:{write:!0}})],u.prototype,\"polynomialOrder\",void 0),e([r()],u.prototype,\"forwardCoefficients\",void 0),e([t(\"forwardCoefficients\",[\"coeffX\",\"coeffY\"])],u.prototype,\"readForwardCoefficients\",null),e([n(\"forwardCoefficients\")],u.prototype,\"writeForwardCoefficients\",null),e([r({json:{write:!0}})],u.prototype,\"inverseCoefficients\",null),e([t(\"inverseCoefficients\",[\"inverseCoeffX\",\"inverseCoeffY\"])],u.prototype,\"readInverseCoefficients\",null),e([n(\"inverseCoefficients\")],u.prototype,\"writeInverseCoefficients\",null),e([r()],u.prototype,\"affectsPixelSize\",null),e([o({PolynomialXform:\"polynomial\"})],u.prototype,\"type\",void 0),u=e([s(\"esri.layers.support.rasterTransforms.PolynomialTransform\")],u);const m=u;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport r from\"./GCSShiftTransform.js\";import t from\"./IdentityTransform.js\";import n from\"./PolynomialTransform.js\";const o={GCSShiftXform:r,IdentityXform:t,PolynomialXform:n},e=Object.keys(o);function f(r){const t=r?.type;return!r||e.includes(t)}function i(r){const t=r?.type;if(!t)return null;const n=o[r?.type];if(n){const t=new n;return t.read(r),t}return null}export{f as isTransformSupported,i as readTransform};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIoW,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO;AAAA,EAAM;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,6DAA6D,CAAC,GAAEA,EAAC;AAAE,IAAMC,KAAED;;;ACA1N,IAAIE;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAC,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAC;AAAEC,KAAED,KAAE,EAAE,CAAC,EAAE,6DAA6D,CAAC,GAAEC,EAAC;AAAE,IAAM,IAAEA;;;ACAV,IAAIE,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,oBAAkB,MAAK,KAAK,oBAAkB,GAAE,KAAK,KAAG,IAAG,KAAK,gBAAc,OAAG,KAAK,kBAAgB,CAAC,GAAE,KAAK,yBAAuB,OAAG,KAAK,wBAAsB,CAAC,GAAE,KAAK,WAAS,OAAG,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,eAAe;AAAA,EAAW;AAAA,EAAC,KAAKC,IAAEC,MAAE,OAAGC,KAAE,IAAG;AAAC,SAAK,KAAGA,KAAE;AAAE,UAAMC,MAAE,KAAK,iBAAiB;AAAE,QAAIC,MAAE;AAAG,aAAQC,MAAE,GAAEA,MAAEF,IAAE,QAAOE,OAAI;AAAC,YAAMH,KAAEC,IAAEE,GAAC;AAAE,UAAG,EAAEH,EAAC,KAAG,KAAK,uBAAuBA,EAAC,GAAE;AAAC,cAAMI,KAAEJ,GAAE,KAAKF,IAAEC,KAAE,KAAK,KAAGI,GAAC;AAAE,YAAG,CAACC,GAAE,QAAQ,QAAO,KAAK,iBAAeA,IAAEA;AAAE,QAAAF,MAAEA,OAAGE,GAAE;AAAA,MAAW;AAAA,IAAC;AAAC,WAAM,CAAC,KAAK,cAAYL,OAAG,KAAK,oBAAkB,KAAK,sBAAsBD,EAAC,GAAE,KAAK,iBAAe,KAAK,mBAAmB,GAAE,KAAK,eAAe,cAAYI,OAAG,KAAK,eAAe,aAAY,KAAK,oBAAoB,GAAE,KAAK,mBAAiB,KAAK,iBAAe,EAAC,SAAQ,MAAG,aAAY,KAAE,GAAE,KAAK,oBAAoB,GAAE,KAAK;AAAA,EAAe;AAAA,EAAC,QAAQJ,IAAE;AAAC,UAAMC,MAAE,KAAK,iBAAiB,GAAEK,KAAE,MAAIL,IAAE,SAAOD,GAAE,eAAaA,GAAE,qBAAmBC,IAAE,IAAK,CAAAA,QAAG,KAAK,iBAAiBA,KAAED,EAAC,CAAE;AAAE,WAAO,KAAK,eAAe,EAAC,GAAGA,IAAE,aAAYM,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMN,KAAE,KAAK,iBAAiB,EAAE,OAAO,KAAK,sBAAsB;AAAE,QAAIC;AAAE,QAAGD,GAAE,SAAO,GAAE;AAAC,YAAMC,MAAED,GAAE,IAAK,CAAAA,OAAGA,GAAE,oBAAoB,EAAE,CAAC,CAAE;AAAE,WAAK,kBAAgBC,KAAE,KAAK,yBAAuB,KAAK,gBAAgB,KAAM,CAACD,IAAEC,QAAID,OAAIC,GAAE;AAAE,YAAMK,KAAEN,GAAE,OAAQ,CAAAA,OAAG,kBAAgBA,GAAE,YAAa;AAAE,aAAOM,GAAE,UAAQA,GAAE,QAAS,CAACN,IAAEC,QAAI;AAAC,QAAAD,GAAE,yBAAuB,MAAGA,GAAE,wBAAsB,CAACC,KAAEA,KAAEA,GAAC;AAAA,MAAC,CAAE,GAAE,KAAK;AAAA,IAAe;AAAC,UAAMK,KAAEN,GAAE,CAAC;AAAE,QAAGM,IAAE;AAAC,UAAGL,MAAEK,GAAE,oBAAoB,GAAEA,GAAE,uBAAuB,QAAO,KAAK,kBAAgBL,KAAEA;AAAA,IAAC,OAAK;AAAC,MAAAA,MAAE,CAAC;AAAE,YAAK,EAAC,WAAUD,GAAC,IAAE,KAAK,kBAAkB,CAAC;AAAE,eAAQM,KAAE,GAAEA,KAAEN,IAAEM,KAAI,CAAAL,IAAE,KAAKK,EAAC;AAAA,IAAC;AAAC,UAAMJ,KAAE,KAAK,iBAAiBD,GAAC;AAAE,WAAO,KAAK,yBAAuBC,GAAE,KAAM,CAACF,IAAEC,QAAID,OAAIC,GAAE,GAAE,KAAK,kBAAgBC,IAAE,KAAK;AAAA,EAAe;AAAA,EAAC,oBAAmB;AAAC,UAAMF,KAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,WAAO,KAAK,mBAAmB,MAAKD,IAAEC,GAAC,GAAE,EAAC,SAAQD,IAAE,WAAUC,IAAC;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,UAAMD,KAAE,KAAK,oBAAoB,GAAE,EAAC,QAAOC,KAAE,SAAQK,GAAC,IAAE,KAAK;AAAkB,WAAOA,MAAG,MAAM,QAAQA,EAAC,KAAGA,GAAE,UAAQN,GAAE,UAAQM,GAAE,IAAK,CAAAN,OAAG,KAAK,uBAAuBA,EAAC,IAAEA,GAAE,4BAA4B,IAAE,YAAU,OAAOA,KAAE,EAAC,MAAK,YAAW,YAAW,EAAC,OAAMA,GAAC,GAAE,WAAU,OAAM,IAAG,IAAG,eAAc,MAAE,IAAE,EAAC,MAAK,YAAW,YAAW,EAAC,OAAMA,GAAC,GAAE,WAAU,OAAM,IAAG,IAAG,eAAc,MAAE,CAAE,GAAEA,GAAE,QAAQ,KAAM,CAAAA,OAAG,QAAMA,EAAE,MAAIA,GAAE,UAAQ,SAAO,KAAK,uBAAuBC,GAAC,MAAID,GAAE,SAAOC,IAAE,4BAA4B,IAAG,EAAC,MAAK,KAAK,cAAa,YAAWD,IAAE,WAAU,KAAK,iBAAgB,IAAG,KAAK,IAAG,eAAc,KAAK,cAAa;AAAA,EAAC;AAAA,EAAC,4BAA2B;AAAC,UAAMA,KAAE,KAAK,4BAA4B;AAAE,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,MAAE,CAACD,EAAC,GAAE,EAAC,YAAWM,GAAC,IAAEN;AAAE,QAAIE,KAAEI,GAAE,WAASA,GAAE,UAAQ,CAACA,GAAE,MAAM;AAAE,WAAKJ,MAAA,gBAAAA,GAAG,UAAQ;AAAC,MAAAD,IAAE,QAAQ,GAAGC,EAAC;AAAE,YAAMF,KAAE,CAAC;AAAE,eAAQC,MAAE,GAAEA,MAAEC,GAAE,QAAOD,OAAI;AAAC,cAAK,EAAC,YAAWK,GAAC,IAAEJ,GAAED,GAAC,GAAEE,MAAEG,GAAE,WAASA,GAAE,UAAQ,CAACA,GAAE,MAAM;AAAE,SAAAH,OAAA,gBAAAA,IAAG,WAAQH,GAAE,KAAK,GAAGG,GAAC;AAAA,MAAC;AAAC,MAAAD,KAAEF;AAAA,IAAC;AAAC,aAAQI,MAAEH,IAAE,SAAO,GAAEG,OAAG,GAAEA,MAAI,CAAAH,IAAEG,GAAC,EAAE,iBAAeH,IAAE,OAAOG,KAAE,CAAC;AAAE,QAAID,MAAE;AAAG,aAAQC,MAAE,GAAEA,MAAEH,IAAE,QAAOG,OAAI;AAAC,YAAMJ,KAAEC,IAAEG,GAAC;AAAE,MAAAJ,GAAE,KAAGC,IAAE,SAAOG,MAAE;AAAE,YAAK,EAAC,SAAQE,GAAC,IAAEN,GAAE;AAAW,MAAAG,MAAEA,OAAG,QAAMG,MAAGA,GAAE,SAAO;AAAA,IAAC;AAAC,WAAM,EAAC,aAAYH,KAAE,WAAUF,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,WAAM,cAAY,KAAK,kBAAgBA,KAAE,KAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,UAAK,EAAC,iBAAgBA,GAAC,IAAE;AAAK,aAAOA,MAAA,gBAAAA,GAAG,WAAW,UAAMA,MAAA,gBAAAA,GAAG,WAAW,UAAO;AAAA,EAAE;AAAA,EAAC,mBAAkB;AAAC,UAAK,EAAC,qBAAoBA,GAAC,IAAE;AAAK,WAAM,cAAYA,GAAE,CAAC,IAAE,KAAK,kBAAkB,WAAS,CAAC,IAAEA,GAAE,IAAK,CAAAA,OAAG,KAAK,kBAAkBA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,UAAMC,MAAE,KAAK,iBAAiB,GAAE,EAAC,aAAYK,IAAE,WAAUJ,GAAC,IAAEF;AAAE,QAAG,MAAIC,IAAE,OAAO,QAAOK;AAAE,UAAMH,MAAEF,IAAE,IAAK,CAAAD,OAAGA,MAAG,YAAU,OAAOA,MAAG,UAASA,MAAGA,GAAE,aAAWA,GAAE,aAAW,YAAU,OAAOA,MAAGE,GAAE,SAASF,EAAC,IAAEM,GAAEJ,GAAE,QAAQF,EAAC,CAAC,IAAE,YAAU,OAAOA,KAAEM,GAAE,CAAC,IAAE,MAAO,GAAEF,MAAED,IAAE,KAAM,CAAAH,OAAGA,EAAE,KAAGM,GAAE,CAAC;AAAE,WAAOH,IAAE,QAAS,CAACH,IAAEC,QAAI;AAAC,iBAASD,OAAIG,IAAEF,GAAC,IAAEG;AAAA,IAAE,CAAE,GAAED;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAE;AAAC,WAAOA,MAAA,gBAAAA,GAAG;AAAA,EAAG;AAAA,EAAC,mBAAmBA,IAAEC,MAAE,CAAC,GAAEK,KAAE,CAAC,GAAE;AAAC,aAAQJ,KAAE,GAAEA,KAAEF,GAAE,cAAc,QAAOE,MAAI;AAAC,YAAMC,MAAEH,GAAE,cAAcE,EAAC;AAAE,UAAG,YAAU,OAAOC,IAAE,KAAG,UAASA,IAAE,MAAK,mBAAmBA,KAAEF,KAAEK,EAAC;AAAA,WAAM;AAAC,cAAMN,KAAEG,KAAED,KAAE,KAAK,oBAAoBF,EAAC;AAAE,YAAG,QAAME,GAAE;AAAS,QAAAI,GAAE,SAASJ,EAAC,MAAI,KAAK,wBAAsBA,MAAGD,IAAE,QAAQD,EAAC,GAAEM,GAAE,QAAQJ,EAAC,MAAID,IAAE,KAAKD,EAAC,GAAEM,GAAE,KAAKJ,EAAC;AAAA,MAAG;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAE;AAAC,WAAO,QAAMA,MAAG,YAAU,OAAOA,MAAG,iCAAgCA;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEC,KAAE;AAAC,UAAK,EAAC,oBAAmBK,GAAC,IAAEL;AAAE,QAAG,EAAED,EAAC,KAAG,SAAOA,IAAE;AAAC,YAAMA,KAAEM,GAAE,CAAC;AAAE,aAAO,EAAEN,EAAC,IAAE,OAAKA,GAAE,MAAM;AAAA,IAAC;AAAC,QAAG,YAAU,OAAOA,IAAE;AAAC,YAAME,KAAED,IAAE,iBAAiB,QAAQD,EAAC;AAAE,aAAM,OAAKE,KAAE,OAAKI,GAAEJ,EAAC;AAAA,IAAC;AAAC,QAAG,YAAU,OAAOF,IAAE;AAAC,YAAMC,MAAEK,GAAE,CAAC;AAAE,UAAG,EAAEL,GAAC,EAAE,QAAO;AAAK,YAAK,EAAC,OAAME,KAAE,QAAOC,KAAE,WAAUC,KAAE,MAAKE,IAAC,IAAEN,KAAEF,MAAEQ,MAAE,IAAI,WAAWA,GAAC,IAAE,MAAKC,MAAE,IAAI,aAAaL,MAAEC,GAAC;AAAE,MAAAI,IAAE,KAAKR,EAAC;AAAE,YAAMS,KAAE,KAAK,kBAAkB,CAAC,EAAE,WAAUC,MAAE,IAAI,MAAMD,EAAC,EAAE,KAAKD,GAAC;AAAE,aAAO,IAAIG,GAAE,EAAC,OAAMR,KAAE,QAAOC,KAAE,WAAUC,KAAE,QAAOK,KAAE,MAAKX,IAAC,CAAC;AAAA,IAAC;AAAC,WAAOC,GAAE,QAAQC,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,GAAEE,GAAG,CAAAD,OAAGA,MAAA,gBAAAA,GAAG,aAAc,CAAC,GAAED,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,wDAAwD,CAAC,GAAEA,EAAC;AAAE,IAAMS,KAAET;;;ACAtwL,IAAIa,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,UAAS,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,QAAM;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAJ5oB;AAI6oB,UAAMC,KAAE,KAAK,kBAAkB,CAAC;AAAE,SAAK,UAAM,KAAAA,GAAE,qBAAF,mBAAoB,iBAAc,OAAG,KAAK,kBAAgB,KAAK,oBAAoB,KAAK;AAAE,UAAMC,MAAED,GAAE,MAAM;AAAE,WAAOC,IAAE,YAAU,KAAK,iBAAgBA,IAAE,aAAW,CAAC,EAAC,KAAI,GAAE,KAAI,KAAI,KAAI,KAAI,QAAO,GAAE,CAAC,GAAEA,IAAE,aAAW,MAAKA,IAAE,WAAS,MAAKA,IAAE,iBAAe,MAAKA,IAAE,YAAU,GAAE,KAAK,aAAWA,KAAE,EAAC,SAAQ,MAAG,aAAY,KAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAJpgC;AAIqgC,UAAME,MAAE,KAAAF,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEE,EAAC,EAAE,QAAO;AAAK,UAAK,EAAC,QAAOC,GAAC,IAAEH,IAAEI,MAAED,KAAE,EAAC,GAAEA,GAAE,QAAMD,GAAE,OAAM,GAAEC,GAAE,SAAOD,GAAE,OAAM,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,WAAOE,GAAEF,IAAE,EAAC,YAAWE,IAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEN,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,oDAAoD,CAAC,GAAEA,EAAC;AAAE,IAAMO,KAAEP;;;ACAr+C,IAAMQ,KAAE,oBAAI,IAAI,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,CAAC;AAAE,SAASC,GAAEA,IAAEC,KAAE;AAAC,GAACD,KAAEA,GAAE,QAAQ,MAAK,EAAE,GAAG,WAAW,GAAG,MAAIA,KAAE,MAAIA,KAAGA,GAAE,WAAW,GAAG,MAAIA,KAAEA,GAAE,MAAM,GAAEA,GAAE,MAAM;AAAG,QAAME,KAAEF,GAAE,MAAM,EAAE,GAAEG,MAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,MAAIC,MAAE;AAAG,WAAQC,MAAE,GAAEA,MAAEJ,GAAE,QAAOI,OAAI;AAAC,UAAMN,KAAEE,GAAEI,GAAC;AAAE,QAAGP,GAAE,IAAIC,EAAC,EAAE,QAAKK,OAAGD,IAAE,KAAK,WAAWC,GAAC,CAAC,GAAEF,IAAE,KAAKH,EAAC,GAAEK,MAAE;AAAA,SAAO;AAAC,UAAG,QAAML,GAAE,YAAY,GAAE;AAAC,QAAAM,OAAID,MAAEL,GAAE,OAAOE,GAAEI,GAAC,CAAC,GAAEF,IAAE,KAAKH,IAAE,SAASI,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,CAAC,GAAEA,MAAE;AAAG;AAAA,MAAQ;AAAC,MAAAA,MAAEA,IAAE,OAAOL,EAAC,GAAEM,QAAIJ,GAAE,SAAO,KAAGE,IAAE,KAAK,WAAWC,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,KAAIF,KAAE,MAAKC,IAAC;AAAC;AAAC,SAASH,GAAEF,IAAEC,IAAEC,KAAEC,IAAE;AAAC,MAAG,YAAU,OAAOD,OAAG,YAAU,OAAOC,GAAE,QAAOD,MAAEC;AAAE,MAAIC;AAAE,MAAG,YAAU,OAAOF,KAAE;AAAC,IAAAE,MAAED,GAAE;AAAO,UAAMH,KAAEE;AAAE,KAACA,MAAE,IAAI,aAAaE,GAAC,GAAG,KAAKJ,EAAC;AAAA,EAAC,WAASI,MAAEF,IAAE,QAAOC,GAAE,gBAAc,QAAO;AAAC,UAAMH,KAAEG;AAAE,KAACA,KAAE,IAAI,aAAaC,GAAC,GAAG,KAAKJ,EAAC;AAAA,EAAC;AAAC,QAAMK,MAAE,IAAI,aAAaD,GAAC;AAAE,UAAOH,IAAE;AAAA,IAAC,KAAI;AAAI,eAAQA,KAAE,GAAEA,KAAEG,KAAEH,KAAI,EAAC,QAAMD,MAAGA,GAAEC,EAAC,OAAKI,IAAEJ,EAAC,IAAEC,IAAED,EAAC,IAAEE,GAAEF,EAAC;AAAG;AAAA,IAAM,KAAI;AAAI,eAAQA,KAAE,GAAEA,KAAEG,KAAEH,KAAI,EAAC,QAAMD,MAAGA,GAAEC,EAAC,OAAKI,IAAEJ,EAAC,IAAEC,IAAED,EAAC,IAAEE,GAAEF,EAAC;AAAG;AAAA,IAAM,KAAI;AAAI,eAAQA,KAAE,GAAEA,KAAEG,KAAEH,KAAI,EAAC,QAAMD,MAAGA,GAAEC,EAAC,OAAKI,IAAEJ,EAAC,IAAEC,IAAED,EAAC,IAAEE,GAAEF,EAAC;AAAG;AAAA,IAAM,KAAI;AAAI,eAAQA,KAAE,GAAEA,KAAEG,KAAEH,KAAI,EAAC,QAAMD,MAAGA,GAAEC,EAAC,MAAIE,GAAEF,EAAC,MAAII,IAAEJ,EAAC,IAAEC,IAAED,EAAC,IAAEE,GAAEF,EAAC;AAAG;AAAA,IAAM,KAAI;AAAA,IAAI,KAAI;AAAI,YAAM,IAAI,MAAM,mDAAmD;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAASF,GAAEH,IAAEC,IAAE;AAAC,EAAAD,GAAE,OAAOC,IAAE,CAAC;AAAE,MAAIC,MAAE,GAAEC,KAAE;AAAE,KAAE;AAAC,IAAAD,MAAE,GAAEC,KAAE;AAAE,aAAQF,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAG,QAAMD,GAAEC,EAAC,EAAE,CAAAC,MAAED;AAAA,aAAU,QAAMD,GAAEC,EAAC,GAAE;AAAC,MAAAE,KAAEF;AAAE;AAAA,IAAK;AAAC,IAAAE,OAAID,MAAE,KAAGF,GAAE,OAAOE,KAAE,CAAC;AAAA,EAAC,SAAOC,OAAID,MAAE;AAAG,SAAOF;AAAC;AAAC,SAASI,GAAEJ,IAAE;AAAC,MAAG,MAAIA,GAAE,OAAO,QAAM,EAAC,SAAQ,GAAE,UAAS,EAAC;AAAE,MAAIC,KAAE,GAAEC,MAAE;AAAE,WAAQI,MAAE,GAAEA,MAAEN,GAAE,QAAOM,MAAI,KAAG,QAAMN,GAAEM,GAAC,EAAE,CAAAL,KAAEK;AAAA,WAAU,QAAMN,GAAEM,GAAC,GAAE;AAAC,IAAAJ,MAAEI;AAAE;AAAA,EAAK;AAAC,QAAMH,KAAE,MAAID,MAAEF,KAAEA,GAAE,MAAMC,KAAE,GAAEC,GAAC;AAAE,MAAIE,MAAE;AAAG,WAAQE,MAAE,GAAEA,MAAEH,GAAE,QAAOG,MAAI,KAAG,QAAMH,GAAEG,GAAC,KAAG,QAAMH,GAAEG,GAAC,GAAE;AAAC,IAAAF,MAAEE;AAAE;AAAA,EAAK;AAAC,MAAGF,MAAE,GAAG,CAAAF,MAAE,MAAIE,OAAGH,KAAE;AAAA,OAAO;AAAC,aAAQD,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,KAAG,QAAMG,GAAEH,EAAC,KAAG,QAAMG,GAAEH,EAAC,GAAE;AAAC,MAAAI,MAAEJ;AAAE;AAAA,IAAK;AAAC,IAAAE,MAAE,MAAIE,OAAGH,KAAE;AAAA,EAAE;AAAC,MAAII,MAAE;AAAE,WAAQC,MAAE,GAAEA,MAAEF,KAAEE,MAAI,SAAMN,GAAEM,GAAC,KAAGD;AAAI,SAAM,EAAC,SAAQD,KAAE,UAASA,MAAEC,IAAC;AAAC;AAAC,SAASA,GAAEL,IAAEK,KAAEC,KAAE;AAAC,MAAIC,KAAE,EAAC,KAAIC,IAAE,MAAKC,IAAC,IAAER,GAAEK,KAAED,GAAC;AAAE,MAAG,MAAIG,GAAE,QAAO;AAAC,UAAMR,KAAE,MAAIS,IAAE,SAAOA,IAAE,CAAC,IAAEJ,IAAE,CAAC;AAAE,QAAGL,cAAa,aAAa,QAAM,CAACA,EAAC;AAAE,UAAMC,KAAE,IAAI,aAAaI,IAAE,CAAC,EAAE,MAAM;AAAE,WAAM,YAAU,OAAOL,KAAEC,GAAE,KAAKD,EAAC,IAAEC,GAAE,IAAID,EAAC,GAAE,CAACC,EAAC;AAAA,EAAC;AAAC,SAAKO,GAAE,SAAO,KAAG;AAAC,UAAK,EAAC,UAASP,IAAE,SAAQI,IAAC,IAAED,GAAEI,EAAC;AAAE,QAAGD,MAAEL,GAAEF,IAAEQ,GAAEH,GAAC,GAAEI,IAAER,EAAC,GAAEQ,IAAER,KAAE,CAAC,CAAC,GAAE,MAAIO,GAAE,OAAO;AAAM,IAAAA,KAAEL,GAAEK,IAAEH,GAAC,GAAEI,IAAE,OAAOR,IAAE,GAAEM,GAAC;AAAA,EAAC;AAAC,SAAM,CAACA,GAAC;AAAC;;;ACAlzD,IAAMG,KAAE,IAAIC,GAAE,EAAC,GAAE,UAAS,GAAE,QAAO,GAAE,QAAO,GAAE,SAAQ,GAAE,SAAQ,GAAE,QAAO,GAAE,OAAM,GAAE,SAAQ,GAAE,UAAS,GAAE,QAAO,IAAG,SAAQ,IAAG,MAAK,IAAG,WAAU,IAAG,SAAQ,IAAG,SAAQ,IAAG,aAAY,IAAG,SAAQ,IAAG,QAAO,IAAG,QAAO,IAAG,OAAM,IAAG,cAAa,IAAG,oBAAmB,IAAG,iBAAgB,IAAG,SAAQ,IAAG,OAAM,IAAG,OAAM,IAAG,QAAO,IAAG,QAAO,IAAG,QAAO,IAAG,QAAO,GAAE,EAAC,gBAAe,KAAE,CAAC;AAAE,SAASA,GAAEC,KAAEF,KAAE;AAAC,MAAG,CAACA,GAAEE,GAAC,EAAE,QAAOA;AAAE,QAAK,EAAC,UAASD,KAAE,QAAOE,IAAC,IAAEH,KAAEI,MAAEJ,IAAE,YAAY,IAAK,CAAAE,QAAGA,MAAE,CAAE,GAAE,EAAC,QAAOG,IAAE,MAAKC,GAAC,IAAEJ;AAAE,MAAIK;AAAE,UAAOJ,KAAE;AAAA,IAAC,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAM,KAAI;AAAA,IAAO,KAAI;AAAA,IAAO,KAAI;AAAA,IAAU,KAAI;AAAA,IAAO,KAAI;AAAA,IAAO,KAAI;AAAQ,MAAAI,KAAEC,GAAEF,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAG,KAAEC,GAAEF,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAA,IAAK,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAa,KAAI;AAAA,IAAmB,KAAI;AAAgB,MAAAG,KAAEE,GAAEH,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAA,IAAO,KAAI;AAAQ,MAAAG,KAAEG,GAAEJ,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAG,KAAEI,GAAEL,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEA,IAAE,CAAC,IAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAEK,GAAEN,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAE,EAAED,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAG,KAAE,EAAED,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAM,MAAAG,KAAEM,GAAEP,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,CAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAEO,GAAER,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAG,KAAE,EAAED,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAG,KAAE,EAAED,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAEQ,GAAET,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAY,MAAAG,KAAES,GAAEV,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAM,MAAAG,KAAEU,GAAEX,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAG,KAAE,EAAED,IAAE,CAACD,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC,GAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAE,IAAE,GAAE;AAAE;AAAA,IAAM,KAAI;AAAM,MAAAG,KAAEW,GAAEZ,IAAED,GAAED,IAAE,CAAC,CAAC,GAAEC,GAAED,IAAE,CAAC,CAAC,CAAC;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAG,KAAEY,GAAEb,IAAED,IAAEJ,GAAC;AAAE;AAAA,IAAM;AAAQ,aAAOC;AAAA,EAAC;AAAC,QAAMkB,KAAE,EAAEd,EAAC,IAAE,IAAI,WAAWA,GAAE,MAAM,IAAE;AAAK,IAAEA,EAAC,KAAG,EAAEc,EAAC,KAAGA,GAAE,IAAId,EAAC;AAAE,QAAMe,KAAE,IAAIT,GAAE,EAAC,OAAMV,IAAE,OAAM,QAAOA,IAAE,QAAO,WAAU,OAAM,QAAOK,IAAE,MAAKa,GAAC,CAAC;AAAE,SAAOC,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAS,EAAEnB,KAAEoB,IAAEC,IAAEJ,KAAE;AAAC,QAAK,EAAC,MAAKnB,KAAE,QAAOC,KAAE,OAAME,KAAE,QAAOC,IAAC,IAAEF,KAAEM,MAAEP,IAAEsB,EAAC,GAAEd,MAAER,IAAEqB,EAAC,GAAEZ,KAAED,IAAE,QAAOE,KAAEQ,MAAE,IAAI,WAAWT,EAAC,IAAE,IAAI,aAAaA,EAAC,GAAEE,MAAEO,MAAE,MAAI,GAAEK,KAAEL,MAAE,QAAM;AAAE,WAAQM,KAAE,GAAEA,KAAEf,IAAEe,KAAI,KAAG,QAAMzB,OAAGA,IAAEyB,EAAC,GAAE;AAAC,UAAMvB,MAAEM,IAAEiB,EAAC,GAAEH,KAAEb,IAAEgB,EAAC,GAAEF,KAAErB,MAAEoB;AAAE,IAAAC,OAAIZ,GAAEc,EAAC,KAAGvB,MAAEoB,MAAGC,KAAEX,MAAEY;AAAA,EAAE;AAAC,QAAME,KAAE,IAAId,GAAE,EAAC,OAAMT,KAAE,QAAOC,KAAE,MAAKJ,KAAE,WAAUmB,MAAE,OAAK,OAAM,QAAO,CAACR,EAAC,EAAC,CAAC;AAAE,SAAOe,GAAE,iBAAiB,GAAEA;AAAC;AAAC,SAAStB,GAAEF,KAAE;AAAC,QAAMoB,KAAE,IAAI,aAAa,CAAC;AAAE,SAAOA,GAAE,IAAEpB,IAAE,CAAC,CAAC,IAAE,GAAEoB,GAAE,IAAEpB,IAAE,CAAC,IAAE,CAAC,IAAE,GAAEoB,GAAE,IAAEpB,IAAE,CAAC,IAAE,CAAC,IAAE,GAAEoB;AAAC;AAAC,SAASd,GAAEN,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,QAAON,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,GAAE;AAAC,UAAME,MAAEoB,GAAEtB,GAAC,GAAEuB,KAAEE,GAAEzB,GAAC,GAAEC,MAAEC,MAAEqB;AAAE,IAAAtB,QAAIkB,IAAEnB,GAAC,KAAGE,MAAEqB,MAAGtB;AAAA,EAAE;AAAC,SAAM,CAACkB,GAAC;AAAC;AAAC,SAASV,GAAEP,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,QAAON,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,GAAE;AAAC,UAAME,MAAEoB,GAAEtB,GAAC,GAAEuB,KAAEE,GAAEzB,GAAC;AAAE,IAAAuB,OAAIJ,IAAEnB,GAAC,IAAEE,MAAEqB;AAAA,EAAE;AAAC,SAAM,CAACJ,GAAC;AAAC;AAAC,SAAST,GAAER,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAED,GAAE,QAAOH,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,GAAE;AAAC,UAAME,MAAEoB,GAAEtB,GAAC,GAAEuB,KAAEE,GAAEzB,GAAC;AAAE,IAAAuB,OAAIJ,IAAEnB,GAAC,IAAEE,MAAEqB,KAAE;AAAA,EAAE;AAAC,SAAM,CAACJ,GAAC;AAAC;AAAC,SAASR,GAAET,KAAEoB,IAAEG,IAAEF,IAAE;AAAC,QAAMJ,MAAEM,GAAE,QAAOzB,MAAE,IAAI,aAAamB,GAAC;AAAE,WAAQlB,MAAE,GAAEA,MAAEkB,KAAElB,MAAI,KAAG,QAAMC,OAAGA,IAAED,GAAC,GAAE;AAAC,UAAMC,MAAEuB,GAAExB,GAAC,GAAEkB,MAAEG,GAAErB,GAAC,GAAEE,MAAEgB,MAAEjB,MAAEqB;AAAE,IAAApB,QAAIH,IAAEC,GAAC,KAAGkB,MAAEjB,OAAGC,OAAG,IAAEoB;AAAA,EAAG;AAAC,SAAM,CAACvB,GAAC;AAAC;AAAC,SAASY,GAAEV,KAAEoB,IAAEG,IAAEF,IAAEJ,KAAEnB,KAAE;AAAC,QAAMC,MAAEwB,GAAE,QAAOtB,MAAE,IAAI,aAAaF,GAAC,GAAEG,MAAE,CAACe,MAAEI,KAAEvB,OAAG,IAAEuB,KAAEA;AAAG,WAAQf,MAAE,GAAEA,MAAEP,KAAEO,MAAI,KAAG,QAAMN,OAAGA,IAAEM,GAAC,GAAE;AAAC,UAAMN,MAAEuB,GAAEjB,GAAC,GAAER,MAAEsB,GAAEd,GAAC,GAAEP,MAAEkB,MAAEnB,MAAEE,MAAEE;AAAE,IAAAH,QAAIE,IAAEK,GAAC,IAAEe,MAAGvB,MAAEuB,KAAErB,MAAEiB,OAAGlB;AAAA,EAAE;AAAC,SAAM,CAACE,GAAC;AAAC;AAAC,SAAS,EAAED,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,QAAON,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,GAAE;AAAC,UAAME,MAAEuB,GAAEzB,GAAC,GAAEuB,KAAED,GAAEtB,GAAC;AAAE,IAAAmB,IAAEnB,GAAC,IAAE,OAAI,KAAGuB,KAAE,KAAG,KAAK,MAAM,IAAEA,KAAE,MAAI,IAAE,KAAGA,KAAErB,IAAE;AAAA,EAAE;AAAC,SAAM,CAACiB,GAAC;AAAC;AAAC,SAAS,EAAEjB,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,QAAON,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,GAAE;AAAC,UAAME,MAAEuB,GAAEzB,GAAC,GAAEuB,KAAED,GAAEtB,GAAC;AAAE,QAAG,MAAIE,KAAE;AAAC,YAAMoB,MAAG,KAAGC,KAAEA,KAAErB,MAAEA,OAAG,MAAIqB,KAAE,MAAGrB,QAAIqB,KAAErB,MAAE;AAAI,MAAAiB,IAAEnB,GAAC,IAAEsB,MAAG,IAAE,OAAIA,OAAIpB,MAAE,UAAO,IAAEA;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,CAACiB,GAAC;AAAC;AAAC,SAASN,GAAEX,KAAEoB,IAAEG,IAAEF,IAAEJ,KAAE;AAAC,QAAMnB,MAAEyB,GAAE,QAAOxB,MAAE,IAAI,aAAaD,GAAC,GAAEG,MAAE,KAAK,KAAK,IAAEoB,KAAEA,EAAC;AAAE,WAAQnB,MAAE,GAAEA,MAAEJ,KAAEI,MAAI,KAAG,QAAMF,OAAGA,IAAEE,GAAC,GAAE;AAAC,UAAMF,MAAEuB,GAAErB,GAAC,GAAEJ,MAAEsB,GAAElB,GAAC;AAAE,IAAAH,IAAEG,GAAC,KAAGJ,MAAEuB,KAAErB,MAAEiB,OAAGhB;AAAA,EAAC;AAAC,SAAM,CAACF,GAAC;AAAC;AAAC,SAASa,GAAEZ,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAEF,IAAEJ,KAAEnB,KAAEC,KAAEE,GAAC,IAAEmB,IAAElB,MAAEqB,GAAE,QAAOjB,MAAE,IAAI,aAAaJ,GAAC;AAAE,WAAQK,MAAE,GAAEA,MAAEL,KAAEK,MAAI,EAAC,QAAMP,OAAGA,IAAEO,GAAC,OAAKD,IAAEC,GAAC,IAAE,UAAOgB,GAAEhB,GAAC,IAAE,SAAMc,GAAEd,GAAC,IAAE,SAAMU,IAAEV,GAAC,IAAE,SAAMT,IAAES,GAAC,IAAE,QAAKR,IAAEQ,GAAC,IAAE,OAAKN,IAAEM,GAAC;AAAG,SAAM,CAACD,GAAC;AAAC;AAAC,SAAS,EAAEN,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAE,EAACF,IAAEJ,KAAEnB,KAAEC,GAAC,IAAEqB,IAAEnB,MAAEsB,GAAE,QAAOrB,MAAE,IAAI,aAAaD,GAAC,GAAEK,MAAE,IAAI,aAAaL,GAAC,GAAEM,MAAE,IAAI,aAAaN,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,EAAC,QAAMR,OAAGA,IAAEQ,EAAC,OAAKN,IAAEM,EAAC,IAAET,IAAES,EAAC,IAAEV,IAAEU,EAAC,IAAET,IAAES,EAAC,IAAE,MAAI,GAAEF,IAAEE,EAAC,IAAEe,GAAEf,EAAC,IAAEV,IAAEU,EAAC,IAAEe,GAAEf,EAAC,IAAE,MAAI,GAAED,IAAEC,EAAC,IAAES,IAAET,EAAC,IAAEa,GAAEb,EAAC,IAAES,IAAET,EAAC,KAAGV,IAAEU,EAAC,IAAES,IAAET,EAAC,KAAG,MAAI;AAAG,SAAM,CAACN,KAAEI,KAAEC,GAAC;AAAC;AAAC,SAAS,EAAEP,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAEF,IAAEJ,GAAC,IAAEG,IAAEtB,MAAEyB,GAAE,QAAOxB,MAAE,IAAI,aAAaD,GAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEH,KAAEG,MAAI,KAAG,QAAMD,OAAGA,IAAEC,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMD,MAAEuB,GAAEtB,GAAC,GAAEmB,KAAEC,GAAEpB,GAAC,GAAEH,MAAEsB,KAAEpB,MAAEiB,IAAEhB,GAAC;AAAE,IAAAH,QAAIC,IAAEE,GAAC,KAAGmB,KAAEpB,OAAGF;AAAA,EAAE;AAAC,SAAM,CAACC,GAAC;AAAC;AAAC,SAASc,GAAEb,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAEF,IAAEJ,GAAC,IAAEG,IAAEtB,MAAEyB,GAAE,QAAOxB,MAAE,IAAI,aAAaD,GAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEH,KAAEG,MAAI,KAAG,QAAMD,OAAGA,IAAEC,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMD,MAAEuB,GAAEtB,GAAC,GAAEmB,KAAEC,GAAEpB,GAAC,GAAEH,MAAEmB,IAAEhB,GAAC,GAAEC,MAAE,KAAK,MAAM,IAAEF,MAAE,MAAI,IAAE,IAAEA,MAAE,IAAE,KAAK,KAAKoB,EAAC,IAAE,GAAE;AAAE,IAAArB,IAAEE,GAAC,IAAE,OAAK,OAAKD,MAAEF,OAAG,OAAKsB,KAAEtB,QAAII;AAAA,EAAC;AAAC,SAAM,CAACH,GAAC;AAAC;AAAC,SAASe,GAAEd,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAEF,IAAEJ,GAAC,IAAEG,IAAEtB,MAAEyB,GAAE,QAAOxB,MAAE,IAAI,aAAaD,GAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEH,KAAEG,MAAI,KAAG,QAAMD,OAAGA,IAAEC,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMD,MAAEuB,GAAEtB,GAAC,GAAEmB,KAAEC,GAAEpB,GAAC,GAAEH,MAAEmB,IAAEhB,GAAC;AAAE,IAAAF,IAAEE,GAAC,IAAE,OAAKD,MAAEoB,MAAG,MAAIpB,MAAEF;AAAA,EAAE;AAAC,SAAM,CAACC,GAAC;AAAC;AAAC,SAASgB,GAAEf,KAAEoB,IAAE;AAAC,QAAK,CAACG,IAAEF,IAAEJ,GAAC,IAAEG,IAAEtB,MAAEyB,GAAE,QAAOxB,MAAE,IAAI,aAAaD,GAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEH,KAAEG,MAAI,KAAG,QAAMD,OAAGA,IAAEC,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMD,MAAEuB,GAAEtB,GAAC,GAAEmB,KAAEC,GAAEpB,GAAC,GAAEH,MAAEE,MAAE,IAAEoB,KAAE,MAAIH,IAAEhB,GAAC,IAAE;AAAE,IAAAH,QAAIC,IAAEE,GAAC,IAAE,OAAKD,MAAEoB,MAAGtB;AAAA,EAAE;AAAC,SAAM,CAACC,GAAC;AAAC;AAAC,SAAS,EAAEC,KAAEoB,IAAEG,KAAE,KAAG;AAAC,QAAK,CAACF,IAAEJ,KAAEnB,GAAC,IAAEsB,IAAErB,MAAEkB,IAAE,QAAOhB,MAAE,IAAI,aAAaF,GAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEH,KAAEG,MAAI,KAAG,QAAMF,OAAGA,IAAEE,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEH,KAAEG,OAAI;AAAC,UAAMF,MAAEqB,GAAEnB,GAAC,GAAEkB,KAAEH,IAAEf,GAAC,GAAEH,MAAED,IAAEI,GAAC,GAAEI,MAAEN,MAAEuB,KAAEH,MAAG,IAAEG,MAAGxB;AAAE,IAAAO,QAAIL,IAAEC,GAAC,KAAGF,MAAEuB,KAAEH,MAAG,IAAEG,MAAGxB,OAAGO;AAAA,EAAE;AAAC,SAAM,CAACL,GAAC;AAAC;AAAC,SAASe,GAAEhB,KAAEoB,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,QAAON,MAAE,IAAI,aAAaI,EAAC;AAAE,WAAQvB,MAAE,GAAEA,MAAEuB,IAAEvB,MAAI,KAAG,QAAME,OAAGA,IAAEF,GAAC,EAAE,MAAIA,MAAE,GAAEA,MAAEuB,IAAEvB,OAAI;AAAC,UAAME,OAAG,MAAGoB,GAAEtB,GAAC,MAAI,KAAG,OAAIyB,GAAEzB,GAAC,MAAI;AAAE,IAAAE,QAAIiB,IAAEnB,GAAC,IAAE,IAAEE;AAAA,EAAE;AAAC,SAAM,CAACiB,GAAC;AAAC;;;ACArvJ,IAAIQ;AAAE,IAAIC,KAAED,KAAE,cAAcA,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO;AAAA,EAAQ;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIA,GAAE,EAAC,QAAO,KAAK,QAAO,aAAY,KAAK,aAAY,QAAO,EAAE,KAAK,MAAM,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,QAAO,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACC,GAAED,EAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,qEAAqE,CAAC,GAAEC,EAAC;AAAE,IAAME,KAAEF;;;ACAzW,IAAMG,KAAE,oBAAI,IAAI,CAAC,QAAO,SAAQ,aAAY,KAAK,CAAC;AAAE,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,kBAAiB,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,kBAAgB,KAAK,oBAAoB,KAAK;AAAE,UAAMC,KAAE,KAAK,kBAAkB,CAAC,GAAEC,MAAED,GAAE,MAAM;AAAE,IAAAC,IAAE,YAAU,KAAK,iBAAgBA,IAAE,aAAW,MAAKA,IAAE,aAAW,MAAKA,IAAE,YAAU,aAAW,KAAK,kBAAkB,SAAOD,GAAE,YAAU,GAAE,KAAK,aAAWC;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAY,CAAC,CAAC,UAAS,SAAQ,QAAQ,EAAE,SAAS,KAAK,kBAAkB,MAAM,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAJvlC;AAIwlC,UAAME,MAAE,KAAAF,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEE,EAAC,EAAE,QAAOA;AAAE,UAAK,EAAC,QAAOC,IAAE,aAAYC,IAAC,IAAE,KAAK,mBAAkBC,MAAED,IAAE,MAAM,GAAG,EAAE,IAAK,CAAAJ,OAAG,WAAWA,EAAC,CAAE;AAAE,WAAOC,GAAEC,IAAE,EAAC,QAAOC,IAAE,aAAYE,KAAE,UAASD,IAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMJ,KAAE,KAAK,kBAAkB,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,WAAWA,EAAC,IAAE,CAAE;AAAE,UAAIA,GAAE,UAAQA,GAAE,KAAK,CAAC;AAAE,UAAMC,MAAE,KAAK,yBAAuB,CAAC,GAAE,GAAE,CAAC,IAAED;AAAE,QAAIE,IAAEC;AAAE,UAAMC,MAAE,IAAI,aAAa,CAAC,GAAE,EAAC,QAAOE,IAAC,IAAE,KAAK;AAAkB,YAAOA,KAAE;AAAA,MAAC,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAM,KAAI;AAAA,MAAO,KAAI;AAAA,MAAO,KAAI;AAAA,MAAU,KAAI;AAAA,MAAO,KAAI;AAAA,MAAO,KAAI;AAAQ,QAAAJ,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAO;AAAA,MAAM,KAAI;AAAO,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAO;AAAA,MAAM,KAAI;AAAA,MAAK,KAAI;AAAA,MAAQ,KAAI;AAAA,MAAa,KAAI;AAAA,MAAmB,KAAI;AAAgB,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAK;AAAA,MAAM,KAAI;AAAA,MAAO,KAAI;AAAQ,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAK;AAAA,MAAM,KAAI;AAAO,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE,QAAOC,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE;AAAE;AAAA,MAAM,KAAI;AAAQ,QAAAE,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE,SAAQC,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE,GAAEI,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE,GAAEI,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE;AAAE;AAAA,MAAM,KAAI;AAAQ,QAAAE,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAQ;AAAA,MAAM,KAAI;AAAO,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAO;AAAA,MAAM,KAAI;AAAM,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE,SAAQC,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE,GAAEI,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAE;AAAE;AAAA,MAAM,KAAI;AAAO,QAAAE,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC,GAAEE,KAAE;AAAO;AAAA,MAAM,KAAI;AAAQ,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC,GAAEE,KAAE;AAAQ;AAAA,MAAM,KAAI;AAAY,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC,GAAEE,KAAE;AAAW;AAAA,MAAM,KAAI;AAAM,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAEA,IAAE,CAAC,CAAC,CAAC,GAAEE,KAAE;AAAM;AAAA,MAAM,KAAI;AAAQ,QAAAD,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE,SAAQC,IAAE,CAAC,IAAEJ,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,IAAE;AAAG;AAAA,MAAM,KAAI;AAAM,QAAAE,KAAEH,GAAE,CAACE,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,CAAC,CAAC,GAAEE,KAAE;AAAM;AAAA,MAAM;AAAQ,QAAAD,KAAEH,GAAE,CAAC,GAAE,GAAE,CAAC,CAAC,GAAEI,KAAE;AAAA,IAAQ;AAAC,WAAM,EAAC,eAAcD,IAAE,WAAUC,IAAE,aAAYC,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAE;AAAC,QAAG,aAAW,KAAK,kBAAkB,OAAO,QAAOA;AAAE,UAAMC,MAAE,KAAK,kBAAkB,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAD,OAAG,WAAWA,EAAC,IAAE,CAAE,GAAEE,KAAEF,GAAE,QAAOG,KAAEF,IAAE,IAAK,CAAAD,OAAGA,MAAGE,KAAEA,KAAE,IAAEF,EAAE,GAAEI,MAAEP,GAAE,IAAI,KAAK,kBAAkB,MAAM,IAAE,IAAE,GAAES,MAAEH,GAAE,MAAM,GAAEC,GAAC,EAAE,IAAK,CAAAH,QAAGD,GAAEC,GAAC,CAAE;AAAE,WAAO,MAAIK,IAAE,UAAQA,IAAE,KAAK,CAAC,GAAEA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKO,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAEA,EAAC;AAAE,IAAMS,KAAET;;;ACA7gF,IAAIU;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,iBAAiBC,KAAE;AAAC,QAAG,CAACA,IAAE,QAAO;AAAK,UAAMC,KAAED,IAAE,YAAY;AAAE,WAAO,EAAE,SAASC,EAAC,IAAEA,KAAE;AAAA,EAAI;AAAA,EAAC,cAAcD,KAAE;AAAC,WAAOD,GAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,KAAEC,IAAE;AAAC,QAAG,CAACD,IAAE,QAAO;AAAK,UAAME,KAAEC,GAAE,WAAW,KAAM,CAAAF,OAAGA,GAAE,YAAY,MAAID,IAAE,YAAY,CAAE;AAAE,WAAOE,KAAEC,GAAE,SAASD,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAJh5B;AAIi5B,WAAO,IAAIL,GAAE,EAAC,UAAS,EAAE,KAAK,QAAQ,GAAE,cAAa,KAAK,cAAa,YAAU,UAAK,cAAL,mBAAgB,SAAQ,eAAc,KAAK,cAAa,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,MAAM,CAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACM,GAAE,cAAc,CAAC,GAAEN,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,WAAW,CAAC,GAAEA,GAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,GAAE,WAAU,MAAK,EAAC,MAAKA,GAAE,YAAW,OAAMA,GAAE,MAAK,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,eAAe,CAAC,GAAEA,GAAE,WAAU,qBAAoB,IAAI,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,+DAA+D,CAAC,GAAEC,EAAC;AAAE,IAAMO,KAAEP;;;ACAhgD,IAAMQ,KAAE,CAAC,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,GAAE,CAAC,GAAE,CAAC,KAAI,GAAE,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,CAAC,CAAC;AAArxF,IAAuxFC,KAAE,CAAC,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,IAAG,GAAE,GAAG,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,EAAE,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,GAAE,CAAC,IAAG,KAAI,CAAC,CAAC;AAAE,SAASC,GAAEF,IAAEC,KAAE;AAAC,QAAMC,KAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEJ,GAAE,SAAO,GAAEI,KAAI,CAAAF,GAAE,KAAK,EAAC,MAAK,eAAc,WAAU,oBAAmB,WAAUF,GAAEI,EAAC,EAAE,MAAM,CAAC,GAAE,SAAQJ,GAAEI,KAAE,CAAC,EAAE,MAAM,CAAC,EAAC,CAAC,GAAED,IAAE,KAAKH,GAAEI,KAAE,CAAC,EAAE,CAAC,IAAEJ,GAAEI,EAAC,EAAE,CAAC,CAAC;AAAE,QAAMC,MAAEL,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC;AAAE,SAAO,EAAE,EAAC,MAAK,aAAY,YAAWE,GAAC,GAAE,EAAC,WAAUG,KAAE,SAAQJ,MAAEA,OAAGE,IAAC,CAAC;AAAC;AAAC,SAASA,KAAG;AAAC,SAAOD,GAAE,CAAC,CAAC,GAAE,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,GAAE,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,KAAI,CAAC,GAAE,CAAC,KAAI,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,IAAG,IAAG,EAAE,CAAC,CAAC;AAAC;AAAC,SAASG,KAAG;AAAC,QAAMD,KAAEF,GAAE,CAAC,CAAC,GAAE,KAAI,KAAI,GAAG,GAAE,CAAC,IAAG,GAAE,GAAE,GAAG,GAAE,CAAC,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,IAAG,KAAI,CAAC,GAAE,CAAC,KAAI,GAAE,IAAG,CAAC,GAAE,CAAC,KAAI,IAAG,IAAG,EAAE,GAAE,CAAC,KAAI,GAAE,IAAG,CAAC,CAAC,CAAC;AAAE,WAAQF,KAAEI,GAAE,QAAOJ,KAAE,KAAIA,KAAI,CAAAI,GAAE,KAAK,CAAC,GAAE,IAAG,CAAC,CAAC;AAAE,SAAOA;AAAC;AAAC,SAASE,KAAG;AAAC,SAAO,EAAE,EAAC,MAAK,eAAc,WAAU,oBAAmB,WAAU,CAAC,GAAE,GAAE,CAAC,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,EAAC,CAAC;AAAC;AAAC,SAASC,KAAG;AAAC,QAAMH,KAAE,CAAC;AAAE,WAAQJ,KAAE,GAAEA,KAAE,KAAIA,MAAI;AAAC,UAAMA,KAAE,CAAC;AAAE,aAAQI,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAJ,GAAE,KAAK,KAAK,MAAM,MAAI,KAAK,OAAO,CAAC,CAAC;AAAE,IAAAI,GAAE,KAAKJ,EAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAASI,KAAG;AAAC,SAAON,GAAE,CAAC,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,CAAC,GAAE,CAAC,OAAK,OAAK,KAAI,CAAC;AAAC;AAAC,SAASO,GAAEL,IAAE;AAAC,MAAIF;AAAE,UAAOE,IAAE;AAAA,IAAC,KAAI;AAAY,MAAAF,KAAEC,GAAE;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAD,KAAEI,GAAE;AAAE;AAAA,IAAM,KAAI;AAAY,MAAAJ,KAAEM,GAAE;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAN,KAAEF;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAE,KAAEG,GAAE;AAAE;AAAA,IAAM,KAAI;AAAQ,MAAAH,KAAED;AAAE;AAAA,IAAM,KAAI;AAAS,MAAAC,KAAEK,GAAE;AAAA,EAAC;AAAC,SAAOL,MAAGA,KAAEA,GAAE,IAAK,CAACE,IAAEJ,OAAI,CAACA,IAAE,GAAGI,EAAC,CAAE,GAAEF,MAAG;AAAI;;;ACAv+M,IAAIQ,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,YAAW,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAAC,UAAMC,MAAE,KAAK,kBAAkB,CAAC;AAAE,QAAGA,IAAE,YAAU,EAAE,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,qDAAoD;AAAE,QAAG,EAAC,UAASC,IAAE,cAAaC,KAAE,WAAUC,IAAE,eAAcC,GAAC,IAAE,KAAK;AAAkB,QAAG,EAACH,MAAA,gBAAAA,GAAG,QAAO,KAAGE,GAAE,CAAAF,KAAE,EAAEE,IAAE,EAAC,kBAAiB,KAAE,CAAC;AAAA,aAAUC,IAAE;AAAC,YAAMJ,MAAEK,GAAED,EAAC;AAAE,MAAAJ,QAAIC,KAAE,EAAED,GAAC;AAAA,IAAE,MAAM,CAAAE,QAAID,KAAEH,GAAEI,GAAC;AAAG,QAAG,EAACD,MAAA,gBAAAA,GAAG,QAAO,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,+CAA8C;AAAE,SAAK,kBAAgB,KAAK,oBAAoB,IAAI;AAAE,UAAMH,MAAEE,IAAE,MAAM;AAAE,WAAOF,IAAE,YAAU,KAAK,iBAAgBA,IAAE,WAASG,IAAEH,IAAE,YAAU,GAAE,KAAK,aAAWA,KAAE,EAAC,SAAQ,MAAG,aAAY,KAAE;AAAA,EAAC;AAAA,EAAC,eAAeE,KAAE;AAJr1C;AAIs1C,YAAO,KAAAA,IAAE,gBAAF,mBAAgB;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKQ,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,sDAAsD,CAAC,GAAEA,EAAC;AAAE,IAAMS,KAAET;;;ACA12C,IAAIU;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaC,IAAEC,KAAE;AAAC,IAAAA,IAAE,UAAQD,GAAE,IAAK,CAAAA,OAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,KAAEA,KAAEA,GAAE,OAAO,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,GAAE,EAAC,SAAQ,EAAE,KAAK,OAAO,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACE,GAAE,SAAS,CAAC,GAAEF,GAAE,WAAU,gBAAe,IAAI,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,oEAAoE,CAAC,GAAEC,EAAC;AAAE,IAAMI,KAAEJ;;;ACA3V,IAAIK,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,iBAAgB,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,SAAS;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,mBAAkBC,GAAC,IAAE,MAAKC,KAAED,GAAE,CAAC;AAAE,SAAK,kBAAgB,KAAK,oBAAoBC,GAAE,SAAS;AAAE,UAAMC,KAAED,GAAE,MAAM;AAAE,QAAGC,GAAE,iBAAe,MAAKA,GAAE,WAAS,MAAKA,GAAE,YAAU,KAAK,iBAAgBA,GAAE,YAAUF,GAAE,IAAK,CAAC,EAAC,WAAUA,GAAC,MAAIA,EAAE,EAAE,OAAQ,CAACA,IAAEG,QAAIH,KAAEG,GAAE,GAAEH,GAAE,MAAO,CAAC,EAAC,YAAWA,GAAC,MAAI,EAAEA,EAAC,KAAGA,GAAE,MAAO,GAAE;AAAC,YAAMC,KAAE,CAAC;AAAE,MAAAD,GAAE,QAAS,CAAC,EAAC,YAAWA,GAAC,MAAI,EAAEA,EAAC,KAAGC,GAAE,KAAK,GAAGD,EAAC,CAAE,GAAEE,GAAE,aAAWD;AAAA,IAAC;AAAC,QAAGD,GAAE,MAAO,CAAC,EAAC,YAAWA,GAAC,MAAI,EAAEA,EAAC,KAAGA,GAAE,MAAO,GAAE;AAAC,YAAMC,KAAE,CAAC;AAAE,MAAAD,GAAE,QAAS,CAAC,EAAC,YAAWA,GAAC,MAAI,EAAEA,EAAC,KAAGC,GAAE,KAAK,GAAGD,EAAC,CAAE,GAAEE,GAAE,aAAWD;AAAA,IAAC;AAAC,IAAAC,GAAE,YAAU,MAAIA,GAAE,WAAS,MAAKA,GAAE,iBAAe,OAAM,KAAK,aAAWA;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAYA,GAAE,aAAW,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAE;AAAC,UAAK,EAAC,aAAYG,IAAC,IAAEH;AAAE,QAAG,CAACG,IAAE,QAAO;AAAK,UAAMD,KAAEC,OAAA,gBAAAA,IAAI;AAAG,WAAO,EAAED,EAAC,IAAE,OAAK,EAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,WAAM,EAAC,WAAU,KAAK,WAAW,UAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEN,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAEA,EAAC;AAAE,IAAMO,MAAEP;;;ACAtuD,IAA0qFQ,KAAE,EAAC,aAAY,IAAG,yBAAwB,GAAE,uBAAsB,GAAE,2BAA0B,GAAE,4BAA2B,GAAE,eAAc,GAAE,cAAa,GAAE,cAAa,GAAE,eAAc,GAAE,mBAAkB,GAAE,mBAAkB,GAAE,sBAAqB,IAAG,cAAa,IAAG,cAAa,IAAG,eAAc,IAAG,eAAc,IAAG,cAAa,IAAG,cAAa,IAAG,iBAAgB,IAAG,eAAc,IAAG,SAAQ,IAAG,UAAS,IAAG,aAAY,IAAG,MAAK,IAAG;AAAzlG,IAAmrGC,KAAE,EAAC,MAAK,GAAE,OAAM,GAAE,OAAM,GAAE,MAAK,GAAE,OAAM,GAAE,KAAI,IAAG,QAAO,IAAG,KAAI,IAAG,OAAM,IAAG,MAAK,IAAG,KAAI,IAAG,OAAM,IAAG,IAAG,IAAG,OAAM,IAAG,MAAK,IAAG,KAAI,IAAG,QAAO,IAAG,WAAU,IAAG,SAAQ,IAAG,QAAO,IAAG,aAAY,IAAG,aAAY,GAAE;AAA53G,IAA83GC,KAAE,EAAC,YAAW,IAAG,kBAAiB,IAAG,YAAW,IAAG,WAAU,IAAG,mBAAkB,IAAG,YAAW,IAAG,YAAW,IAAG,YAAW,IAAG,WAAU,IAAG,YAAW,IAAG,SAAQ,IAAG,aAAY,IAAG,kBAAiB,IAAG,UAAS,IAAG,eAAc,IAAG,QAAO,IAAG,UAAS,GAAE;AAAxnH,IAA0nHC,KAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,OAAM,GAAE,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,OAAM,IAAG,OAAM,IAAG,OAAM,GAAE;AAAjuH,IAAmuHC,KAAE,EAAC,UAAS,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,KAAI,IAAG,UAAS,IAAG,OAAM,IAAG,QAAO,IAAG,KAAI,IAAG,SAAQ,IAAG,sBAAqB,IAAG,iBAAgB,IAAG,kBAAiB,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,sBAAqB,IAAG,mBAAkB,IAAG,oBAAmB,IAAG,iBAAgB,IAAG,qBAAoB,GAAE;AAAjhI,IAAmhIC,KAAE,EAAC,SAAQ,IAAG,aAAY,GAAE;AAA/iI,IAAijIC,MAAE,EAAC,GAAGL,IAAE,GAAGC,IAAE,GAAGC,IAAE,GAAGC,IAAE,GAAGC,GAAC;;;ACA1+H,IAAME,KAAE,oBAAI;AAAI,SAASC,GAAEC,IAAE;AAAC,QAAMC,KAAE,KAAK,KAAKD,GAAE,MAAM,GAAEF,MAAEE,GAAE,MAAM,GAAEC,EAAC,GAAEF,MAAE,CAAC,CAAC;AAAE,WAAQG,MAAE,GAAEA,MAAED,IAAEC,OAAI;AAAC,QAAIJ,MAAE;AAAK,aAAQC,MAAE,GAAEA,MAAEE,IAAEF,OAAI;AAAC,YAAMI,KAAEH,GAAED,MAAEG,MAAED,EAAC,GAAEG,MAAEJ,GAAED,GAAC;AAAE,UAAG,QAAMD,IAAE,KAAG,MAAIM,KAAE;AAAC,YAAGD,GAAE,QAAM,EAAC,WAAU,OAAG,KAAI,MAAK,KAAI,KAAI;AAAA,MAAC,MAAM,CAAAL,MAAEK,KAAEC;AAAA,eAAUD,KAAEC,QAAIN,IAAE,QAAM,EAAC,WAAU,OAAG,KAAI,MAAK,KAAI,KAAI;AAAA,IAAC;AAAC,QAAG,QAAMA,IAAE,QAAM,EAAC,WAAU,OAAG,KAAI,MAAK,KAAI,KAAI;AAAE,IAAAC,IAAE,KAAKD,GAAC;AAAA,EAAC;AAAC,SAAM,EAAC,WAAU,MAAG,KAAIA,KAAE,KAAIC,IAAC;AAAC;AAAC,SAASG,GAAEF,IAAEC,IAAEH,KAAEC,KAAEG,KAAEC,IAAEC,KAAE;AAAC,QAAMC,MAAE,IAAI,aAAaJ,KAAEH,GAAC,GAAEQ,MAAEH,GAAE,QAAOI,MAAEH,MAAE,IAAEL,KAAES,KAAEJ,MAAEL,MAAE,GAAEU,KAAEL,MAAE,IAAEH;AAAE,WAAQS,MAAEH,KAAEG,MAAEZ,MAAES,KAAEG,OAAI;AAAC,UAAMZ,MAAEY,MAAET;AAAE,aAAQG,MAAEI,IAAEJ,MAAEH,KAAEO,IAAEJ,OAAI;AAAC,UAAGF,OAAG,CAACA,IAAEJ,MAAEM,GAAC,EAAE;AAAS,UAAIH,KAAE;AAAE,eAAQC,MAAE,GAAEA,MAAEI,KAAEJ,MAAI,CAAAD,MAAGD,GAAEF,MAAEM,OAAGF,MAAEH,OAAGU,EAAC,IAAEN,GAAED,GAAC;AAAE,MAAAG,IAAEP,MAAEM,GAAC,IAAEH;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAASF,GAAEH,IAAEC,IAAEH,KAAEC,KAAEG,KAAEC,IAAEC,KAAE;AAAC,QAAMC,MAAE,IAAI,aAAaJ,KAAEH,GAAC,GAAEQ,MAAE,KAAK,MAAMP,MAAE,CAAC,GAAEQ,MAAE,KAAK,MAAML,MAAE,CAAC;AAAE,WAAQM,KAAEF,KAAEE,KAAEV,MAAEQ,KAAEE,MAAI;AAAC,UAAMV,MAAEU,KAAEP;AAAE,aAAQO,KAAED,KAAEC,KAAEP,KAAEM,KAAEC,MAAI;AAAC,UAAGL,MAAG,CAACA,GAAEL,MAAEU,EAAC,EAAE;AAAS,UAAIC,KAAE;AAAE,eAAQN,KAAE,GAAEA,KAAEJ,KAAEI,KAAI,UAAQJ,MAAE,GAAEA,MAAEG,KAAEH,MAAI,CAAAU,MAAGT,GAAEF,MAAEU,MAAGL,KAAEG,OAAGL,KAAEF,MAAEQ,GAAC,IAAEH,IAAED,KAAED,MAAEH,GAAC;AAAE,MAAAM,IAAEP,MAAEU,EAAC,IAAEC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOJ;AAAC;AAAC,SAASD,GAAEH,IAAEH,KAAEC,MAAE,MAAG;AAAC,QAAK,EAAC,QAAOG,KAAE,OAAME,KAAE,QAAOE,KAAE,WAAUC,KAAE,MAAKC,GAAC,IAAEP,IAAEQ,KAAEP,IAAE,QAAOQ,MAAE,CAAC,GAAE,EAAC,QAAOC,KAAE,MAAKC,IAAE,MAAKC,GAAC,IAAEf;AAAE,WAAQE,KAAE,GAAEA,KAAES,IAAET,MAAI;AAAC,UAAMC,KAAEE,GAAED,IAAEF,EAAC,GAAEI,KAAEE,KAAEM,IAAEC,IAAEL,IAAEG,GAAC;AAAE,IAAAZ,OAAGM,GAAEJ,IAAEG,KAAEE,KAAEM,IAAEC,EAAC,GAAEH,IAAE,KAAKT,EAAC;AAAA,EAAC;AAAC,SAAO,IAAIa,GAAE,EAAC,OAAMV,KAAE,QAAOE,KAAE,WAAUC,KAAE,QAAOG,KAAE,MAAKF,GAAC,CAAC;AAAC;AAAC,SAASH,GAAEL,IAAEC,IAAEH,KAAEC,KAAEG,KAAE;AAAC,QAAMC,KAAE,KAAK,MAAMJ,MAAE,CAAC;AAAE,WAAQM,MAAE,GAAEA,MAAEF,IAAEE,MAAI,UAAQN,MAAE,GAAEA,MAAEE,IAAEF,MAAI,CAAAC,GAAEK,MAAEJ,KAAEF,GAAC,IAAEC,IAAGE,MAAE,IAAEG,OAAGJ,KAAEF,GAAC,GAAEC,IAAGF,MAAE,IAAEO,OAAGJ,KAAEF,GAAC,IAAEC,IAAGF,MAAEI,MAAEG,OAAGJ,KAAEF,GAAC;AAAE,QAAMK,MAAE,KAAK,MAAMF,MAAE,CAAC;AAAE,WAAQG,MAAE,GAAEA,MAAEP,KAAEO,OAAI;AAAC,UAAMP,MAAEO,MAAEJ;AAAE,aAAQF,MAAE,GAAEA,MAAEK,KAAEL,MAAI,CAAAC,GAAEF,MAAEC,GAAC,IAAEC,GAAEF,MAAEI,MAAE,IAAEH,GAAC,GAAEC,GAAEF,MAAEG,KAAEF,MAAE,CAAC,IAAEC,GAAEF,MAAEG,KAAEF,MAAEG,GAAC;AAAA,EAAC;AAAC;AAAC,SAASI,IAAEL,IAAEH,KAAEC,KAAEI,KAAE,MAAG;AAAC,QAAK,EAAC,QAAOC,KAAE,OAAME,KAAE,QAAOC,KAAE,WAAUC,IAAE,MAAKC,GAAC,IAAER,IAAES,MAAEN,IAAE,QAAOO,MAAE,CAAC,GAAEC,KAAEd,IAAE,QAAOe,KAAEd,IAAE,QAAOgB,KAAE,KAAK,MAAMH,KAAE,CAAC,GAAEE,MAAE,KAAK,MAAMD,KAAE,CAAC;AAAE,WAAQb,KAAE,GAAEA,KAAEU,KAAEV,MAAI;AAAC,QAAIC,KAAEC,GAAEE,IAAEJ,EAAC,GAAEM,KAAEC,KAAEQ,IAAEN,IAAEX,KAAE,IAAE;AAAE,IAAAG,KAAEC,GAAED,IAAEK,KAAEC,KAAEO,KAAEL,IAAEV,KAAE,KAAE,GAAEI,MAAGE,GAAEJ,IAAEK,KAAEC,KAAEK,IAAEC,EAAC,GAAEF,IAAE,KAAKV,EAAC;AAAA,EAAC;AAAC,SAAO,IAAIa,GAAE,EAAC,OAAMR,KAAE,QAAOC,KAAE,WAAUC,IAAE,QAAOG,KAAE,MAAKF,GAAC,CAAC;AAAC;AAAC,SAASF,GAAEP,IAAEC,IAAE;AAAC,QAAMH,MAAEC,GAAEE,GAAE,MAAM,GAAEC,MAAE,UAAKD,GAAE,aAAYE,KAAEL,IAAE,YAAUQ,IAAEN,IAAEF,IAAE,KAAIA,IAAE,KAAII,GAAC,IAAEE,GAAEJ,IAAEC,IAAEC,GAAC,GAAE,EAAC,iBAAgBG,IAAC,IAAEJ;AAAE,SAAOI,OAAGF,GAAE,MAAME,GAAC,GAAEF;AAAC;AAACL,GAAE,IAAIQ,GAAE,MAAK,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,yBAAwB,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,uBAAsB,CAAC,IAAG,GAAE,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,2BAA0B,CAAC,GAAE,IAAG,IAAG,IAAG,GAAE,IAAG,IAAG,IAAG,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,4BAA2B,CAAC,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,eAAc,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,eAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,mBAAkB,CAAC,GAAE,IAAG,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,mBAAkB,CAAC,IAAG,IAAG,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,sBAAqB,CAAC,gBAAc,gBAAc,gBAAc,gBAAc,gBAAc,gBAAc,gBAAc,gBAAc,cAAa,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,QAAM,OAAK,QAAM,OAAK,MAAI,OAAK,QAAM,OAAK,MAAK,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,eAAc,CAAC,IAAG,IAAG,IAAG,IAAG,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,eAAc,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,IAAG,GAAE,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,GAAE,IAAG,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,cAAa,CAAC,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,IAAG,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,iBAAgB,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,eAAc,CAAC,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,SAAQ,CAAC,GAAE,OAAK,GAAE,OAAK,GAAE,OAAK,GAAE,OAAK,CAAC,CAAC,GAAER,GAAE,IAAIQ,GAAE,UAAS,CAAC,OAAK,OAAK,OAAK,OAAK,GAAE,OAAK,OAAK,OAAK,KAAI,CAAC,GAAER,GAAE,IAAIQ,GAAE,aAAY,CAAC,QAAM,OAAK,QAAM,OAAK,OAAM,OAAK,QAAM,OAAK,MAAK,CAAC;;;ACA30F,IAAIU;AAAE,IAAIC,KAAED,MAAE,cAAcA,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,SAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAgBE,KAAE;AAAC,SAAK,KAAK,mBAAkBA,GAAC;AAAE,UAAMC,KAAEC,GAAE,IAAIF,GAAC;AAAE,QAAG,CAACC,MAAGD,QAAIG,GAAE,eAAaH,QAAIG,GAAE,KAAK;AAAO,UAAMC,MAAE,KAAK,KAAKH,GAAE,MAAM;AAAE,SAAK,KAAK,UAASA,EAAC,GAAE,KAAK,KAAK,QAAOG,GAAC,GAAE,KAAK,KAAK,QAAOA,GAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIN,IAAE,EAAC,MAAK,KAAK,MAAK,MAAK,KAAK,MAAK,QAAO,CAAC,GAAG,KAAK,MAAM,GAAE,iBAAgB,KAAK,iBAAgB,QAAO,EAAE,KAAK,MAAM,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,QAAO,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,QAAO,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,QAAO,MAAK,QAAO,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,CAAC,MAAM,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAED,MAAE,EAAE,CAAC,EAAE,kEAAkE,CAAC,GAAEC,EAAC;AAAE,IAAMM,KAAEN;;;ACA9qB,IAAMO,MAAE;AAAG,IAAIC,KAAE,cAAcA,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,eAAc,KAAK,sBAAoB,CAAC,QAAQ;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,iBAAgBC,IAAE,MAAKC,IAAE,MAAKC,KAAE,QAAOC,IAAC,IAAE,KAAK;AAAkB,QAAG,CAAC,OAAO,OAAOC,EAAC,EAAE,SAASJ,EAAC,EAAE,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,oEAAoEA,EAAC,GAAE;AAAE,QAAGA,OAAII,GAAE,QAAMH,KAAEC,QAAIC,IAAE,OAAO,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,0FAAyF;AAAE,UAAME,MAAE,KAAK,kBAAkB,CAAC;AAAE,SAAK,kBAAgB,KAAK,oBAAoBA,IAAE,SAAS;AAAE,UAAMC,MAAED,IAAE,MAAM;AAAE,IAAAC,IAAE,YAAU,KAAK;AAAgB,UAAMC,MAAE,CAACH,GAAE,MAAKA,GAAE,SAAQA,GAAE,UAASA,GAAE,eAAcA,GAAE,aAAa;AAAE,aAAO,KAAK,mBAAiBG,IAAE,SAASP,EAAC,MAAIM,IAAE,aAAW,MAAKA,IAAE,aAAW,OAAMA,IAAE,WAAS,MAAKA,IAAE,iBAAe,MAAK,KAAK,aAAWA;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAYH,IAAE,UAAQL,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeE,IAAE;AAJ9hD;AAI+hD,UAAME,OAAE,KAAAF,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEE,GAAC,KAAG,KAAK,kBAAkB,oBAAkBE,GAAE,KAAK,QAAOF;AAAE,QAAG,EAAC,QAAOC,KAAE,MAAKE,KAAE,MAAKC,IAAC,IAAE,KAAK;AAAkB,UAAMC,MAAEJ,IAAE,OAAQ,CAACH,IAAEC,OAAID,KAAEC,EAAE;AAAE,WAAO,MAAIM,OAAG,MAAIA,QAAIJ,MAAEA,IAAE,IAAK,CAAAH,OAAGA,KAAEO,GAAE,IAAGC,GAAEN,KAAE,EAAC,QAAOC,KAAE,MAAKE,KAAE,MAAKC,KAAE,iBAAgB,KAAK,gBAAe,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,QAAG,EAAC,QAAON,GAAC,IAAE,KAAK;AAAkB,UAAMC,KAAED,GAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,EAAE;AAAE,UAAIA,MAAG,MAAIA,OAAID,KAAEA,GAAE,IAAK,CAAAA,OAAGA,KAAEC,EAAE;AAAG,UAAMC,MAAE,IAAI,aAAaJ,GAAC;AAAE,WAAOI,IAAE,IAAIF,EAAC,GAAE,EAAC,YAAW,KAAK,kBAAkB,MAAK,YAAW,KAAK,kBAAkB,MAAK,QAAOE,KAAE,YAAWC,GAAE,KAAK,eAAe,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKQ,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAER,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,yDAAyD,CAAC,GAAEA,EAAC;AAAE,IAAMK,MAAEL;;;ACAlhE,IAAIU;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,oBAAkBC,GAAE;AAAA,EAAS;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,GAAE,EAAC,SAAQ,CAAC,GAAG,KAAK,OAAO,GAAE,mBAAkB,KAAK,kBAAiB,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,kEAAkE,CAAC,GAAEC,EAAC;AAAE,IAAMG,MAAEH;;;ACAlQ,IAAII,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,eAAc,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,mBAAkBC,GAAC,IAAE,MAAKC,KAAED,GAAE,CAAC,GAAE,EAAC,WAAUE,IAAC,IAAED,IAAE,EAAC,SAAQE,IAAE,mBAAkBC,IAAC,IAAE,KAAK;AAAkB,QAAGA,QAAIC,GAAE,QAAMF,GAAE,KAAM,CAAAH,OAAGA,KAAE,KAAGA,MAAGE,GAAE,EAAE,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,yCAAwC;AAAE,SAAK,kBAAgB,KAAK,oBAAoB,KAAK;AAAE,UAAMI,MAAEL,GAAE,MAAM;AAAE,IAAAK,IAAE,YAAU,KAAK,iBAAgBA,IAAE,YAAUH,GAAE;AAAO,UAAK,EAAC,YAAWI,KAAE,YAAWT,IAAC,IAAEQ;AAAE,MAAEC,GAAC,KAAGA,IAAE,WAASD,IAAE,aAAWH,GAAE,IAAK,CAAAH,OAAGO,IAAEP,EAAC,KAAGO,IAAEA,IAAE,SAAO,CAAC,CAAE,IAAG,EAAET,GAAC,KAAGA,IAAE,WAASQ,IAAE,aAAWH,GAAE,IAAK,CAAAH,OAAGF,IAAEE,EAAC,KAAGF,IAAEA,IAAE,SAAO,CAAC,CAAE,IAAG,KAAK,aAAWQ;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAYA,IAAE,aAAW,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeN,IAAE;AAJvyC;AAIwyC,UAAMK,OAAE,KAAAL,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEK,GAAC,EAAE,QAAO;AAAK,UAAMH,MAAEG,IAAE,OAAO,QAAOF,KAAE,KAAK,kBAAkB,QAAQ,IAAK,CAAAH,OAAGA,MAAGE,MAAEA,MAAE,IAAEF,EAAE;AAAE,WAAOK,IAAE,aAAaF,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,QAAIH;AAAE,QAAG,KAAK,uBAAuB,CAAAA,KAAE,KAAK,sBAAsB,SAAO,KAAK,wBAAsB,CAAC,GAAE,GAAE,CAAC;AAAA,SAAM;AAAC,MAAAA,KAAE,CAAC,GAAG,KAAK,kBAAkB,OAAO,GAAE,MAAIA,GAAE,SAAOA,KAAE,CAAC,GAAE,GAAE,CAAC,IAAEA,GAAE,SAAO,MAAIA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,KAAGA,GAAE,CAAC;AAAG,eAAQK,MAAE,GAAEA,MAAE,GAAEA,MAAI,CAAAL,GAAEK,GAAC,IAAE,KAAK,IAAIL,GAAEK,GAAC,GAAE,CAAC;AAAA,IAAC;AAAC,WAAM,EAAC,eAAcN,GAAEC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,UAAMK,MAAEL,GAAE;AAAO,WAAO,KAAK,kBAAkB,QAAQ,IAAK,CAAAA,OAAGA,MAAGK,MAAEA,MAAE,IAAEL,EAAE,EAAE,IAAK,CAAAK,QAAGL,GAAEK,GAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEP,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,KAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,yDAAyD,CAAC,GAAEA,GAAC;AAAE,IAAMU,MAAEV;;;ACAtvD,IAAIW;AAAE,IAAIC,KAAED,MAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,qBAAmB;AAAA,EAAE;AAAA,EAAC,aAAaC,IAAEC,KAAE;AAAC,IAAAA,IAAE,UAAQD,GAAE,IAAK,CAAAA,OAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,KAAEA,KAAEA,GAAE,OAAO,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIH,IAAE,EAAC,WAAU,KAAK,WAAU,oBAAmB,KAAK,oBAAmB,SAAQ,EAAE,KAAK,OAAO,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACE,GAAE,SAAS,CAAC,GAAEF,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAEA,KAAED,MAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAEC,EAAC;AAAE,IAAMI,MAAEJ;;;ACA9vB,IAAMK,MAAE,oBAAI;AAAI,SAASC,GAAEC,IAAE;AAAC,SAAOF,IAAE,IAAIE,EAAC;AAAC;AAACF,IAAE,IAAIG,GAAE,MAAK,CAAC,GAAE,KAAK,EAAE,CAAC,GAAEH,IAAE,IAAIG,GAAE,MAAK,CAAC,CAAC,KAAK,KAAG,GAAE,KAAK,KAAG,CAAC,CAAC,GAAEH,IAAE,IAAIG,GAAE,MAAK,CAAC,CAAC,KAAK,KAAG,GAAE,KAAK,KAAG,CAAC,CAAC,GAAEH,IAAE,IAAIG,GAAE,KAAI,CAAC,IAAG,CAAC,CAAC,GAAEH,IAAE,IAAIG,GAAE,KAAI,CAAC,IAAG,CAAC,CAAC,GAAEH,IAAE,IAAII,GAAE,YAAW,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,YAAW,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,WAAU,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,YAAW,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,SAAQ,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,UAAS,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,aAAY,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,kBAAiB,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,UAAS,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,eAAc,CAAC,GAAE,CAAC,CAAC,GAAEJ,IAAE,IAAII,GAAE,QAAO,CAAC,GAAE,CAAC,CAAC;AAAE,IAAMC,MAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAE,GAAE,GAAE,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAI,KAAI,GAAE,GAAE,KAAI,GAAE,GAAE,GAAE,KAAI,KAAI,GAAE,GAAE,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,KAAI,GAAE,KAAI,CAAC;AAAE,SAASC,GAAEC,IAAEC,MAAE,OAAG;AAAC,QAAMC,KAAEF,GAAE,IAAK,CAAAL,OAAGA,GAAE,IAAK,GAAEQ,MAAED,GAAE,OAAQ,CAAAF,OAAG,EAAEA,EAAC,CAAE,GAAEI,MAAEJ,GAAE,CAAC,EAAE,OAAO,CAAC,EAAE;AAAO,MAAG,MAAIG,IAAE,OAAO,QAAO,IAAI,WAAWC,GAAC,EAAE,KAAK,GAAG;AAAE,QAAMX,MAAEU,IAAE,CAAC,GAAET,MAAE,IAAI,WAAWD,GAAC;AAAE,MAAG,MAAIU,IAAE,OAAO,QAAOT;AAAE,MAAG,CAACO,KAAE;AAAC,aAAQN,KAAE,GAAEA,KAAEQ,IAAE,QAAOR,MAAI;AAAC,YAAMK,KAAEG,IAAER,EAAC;AAAE,eAAQA,KAAE,GAAEA,KAAED,IAAE,QAAOC,KAAI,CAAAD,IAAEC,EAAC,MAAID,IAAEC,EAAC,IAAEK,GAAEL,EAAC,IAAE,MAAI;AAAA,IAAE;AAAC,WAAOD;AAAA,EAAC;AAAC,MAAGS,IAAE,WAASD,GAAE,OAAO,QAAO,IAAI,WAAWE,GAAC,EAAE,KAAK,GAAG;AAAE,WAAQT,KAAE,GAAEA,KAAEQ,IAAE,QAAOR,MAAI;AAAC,UAAMK,KAAEG,IAAER,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,IAAE,QAAOC,KAAI,OAAID,IAAEC,EAAC,MAAID,IAAEC,EAAC,IAAEK,GAAEL,EAAC,IAAE,MAAI;AAAA,EAAE;AAAC,SAAOD;AAAC;AAAC,SAASW,IAAEV,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASE,GAAED,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgB,OAAMF,GAAC;AAAE,SAAOX,IAAE,IAAIU,GAAC,GAAEV;AAAC;AAAC,SAASc,GAAEZ,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASc,GAAEb,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASe,IAAEd,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC;AAAE,WAAQJ,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIP,IAAEO,EAAC,IAAE,KAAK,KAAKG,IAAEH,EAAC,CAAC,IAAE,KAAK,MAAM,KAAK,IAAIG,IAAEH,EAAC,CAAC,CAAC;AAAG,SAAOP;AAAC;AAAC,SAASa,GAAEX,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASgB,GAAEf,IAAEK,IAAEC,KAAE;AAAC,SAAOK,GAAEX,IAAEK,IAAE,KAAK;AAAC;AAAC,SAASW,GAAEhB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAE,KAAK,MAAMG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC,CAAC;AAAG,SAAON;AAAC;AAAC,SAASkB,GAAEjB,IAAEO,IAAEC,KAAEC,KAAE;AAAC,QAAMX,MAAEE,GAAE,CAAC,GAAED,MAAED,IAAE,QAAOK,MAAEQ,GAAE,gBAAgBH,KAAET,GAAC;AAAE,MAAGU,QAAIR,GAAE,OAAM;AAAC,aAAQD,KAAE,GAAEA,KAAED,KAAEC,KAAI,KAAGO,GAAEP,EAAC,GAAE;AAAC,YAAMK,KAAEP,IAAEE,EAAC;AAAE,WAAK,IAAIK,EAAC,KAAG,IAAEE,GAAEP,EAAC,IAAE,IAAEG,IAAEH,EAAC,IAAE,KAAK,MAAMK,EAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAC,QAAMC,KAAEK,QAAIR,GAAE,OAAK,KAAK,OAAK,KAAK;AAAK,WAAQI,KAAE,GAAEA,KAAEN,KAAEM,KAAI,KAAGE,GAAEF,EAAC,GAAE;AAAC,UAAML,KAAEF,IAAEO,EAAC;AAAE,SAAK,IAAIL,EAAC,IAAE,IAAEO,GAAEF,EAAC,IAAE,IAAEF,IAAEE,EAAC,IAAED,GAAEJ,EAAC;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAAS,EAAEH,IAAEM,KAAEC,IAAEC,KAAE;AAAC,QAAK,CAACC,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEC,IAAEJ,EAAC,CAAC;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAEC,KAAE;AAAC,QAAK,CAACC,KAAEX,GAAC,IAAEE,IAAED,MAAEU,IAAE,QAAON,MAAEQ,GAAE,gBAAgBJ,IAAER,GAAC;AAAE,WAAQM,KAAE,GAAEA,KAAEN,KAAEM,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIF,IAAEE,EAAC,IAAEG,IAAEC,IAAEJ,EAAC,GAAEP,IAAEO,EAAC,CAAC;AAAG,SAAOF;AAAC;AAAC,SAASe,GAAElB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASoB,GAAEnB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASqB,GAAEpB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC;AAAE,WAAQJ,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIP,IAAEO,EAAC,IAAE,CAACG,IAAEH,EAAC;AAAG,SAAOP;AAAC;AAAC,SAASuB,GAAErB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASuB,GAAEtB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC;AAAE,WAAQJ,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIP,IAAEO,EAAC,IAAEG,IAAEH,EAAC,IAAE,IAAE;AAAG,SAAOP;AAAC;AAAC,SAAS,EAAEE,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,KAAGG,IAAEH,EAAC,IAAE,IAAE,MAAII,IAAEJ,EAAC,IAAE,IAAE;AAAI,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,MAAII,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAEC,KAAE;AAAC,QAAK,CAACC,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC,GAAEK,MAAEK,QAAI,KAAK;AAAE,WAAQH,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEF,MAAE,KAAK,IAAIM,IAAEJ,EAAC,CAAC,IAAEG,OAAGC,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAASwB,GAAEvB,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,EAAE;AAAC;AAAC,SAAS,EAAEN,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,CAAC;AAAC;AAAC,SAAS,EAAEN,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,KAAK,CAAC;AAAC;AAAC,SAAS,EAAEN,IAAEM,KAAEC,IAAEC,KAAE;AAAC,QAAK,CAACC,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAII,IAAEJ,EAAC,KAAG,IAAEC,IAAED,EAAC,IAAE,IAAEN,IAAEM,EAAC,IAAEG,IAAEC,IAAEJ,EAAC,CAAC;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,KAAK,KAAK;AAAC;AAAC,SAAS,EAAEN,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,KAAK,IAAI;AAAC;AAAC,SAAS,EAAEN,IAAEK,IAAEC,KAAE;AAAC,SAAO,EAAEN,IAAEK,IAAEC,KAAE,KAAK,GAAG;AAAC;AAAC,SAAS,EAAEN,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAASyB,GAAExB,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAASG,GAAEF,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,KAAGI,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC;AAAE,MAAG,CAACH,IAAE,QAAOR;AAAE,WAAQO,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAP,IAAEO,EAAC,IAAEC,IAAED,EAAC,IAAE,IAAE;AAAE,SAAOP;AAAC;AAAC,SAAS,EAAEE,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,GAAC,IAAER,IAAES,MAAED,IAAE,QAAOV,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC;AAAE,WAAQJ,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIP,IAAEO,EAAC,IAAE,CAACG,IAAEH,EAAC;AAAG,SAAOP;AAAC;AAAC,SAAS,EAAEE,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,MAAII,IAAEJ,EAAC,IAAE,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET,IAAEF,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC,GAAEK,MAAE,IAAI,WAAWL,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,SAAMC,OAAG,CAACA,IAAED,EAAC,KAAG,MAAIG,IAAEH,EAAC,MAAIN,IAAEM,EAAC,IAAEI,IAAEJ,EAAC,GAAEF,IAAEE,EAAC,IAAE;AAAK,SAAM,EAAC,MAAKN,KAAE,MAAKI,IAAC;AAAC;AAAC,SAAS,EAAEH,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,KAAEX,GAAC,IAAEE,IAAED,MAAES,IAAE,QAAOL,MAAEQ,GAAE,gBAAgBJ,IAAER,GAAC;AAAE,WAAQM,KAAE,GAAEA,KAAEN,KAAEM,KAAI,CAAAC,OAAG,CAACA,IAAED,EAAC,MAAIF,IAAEE,EAAC,IAAEG,IAAEH,EAAC,IAAEI,IAAEJ,EAAC,IAAEP,IAAEO,EAAC;AAAG,SAAOF;AAAC;AAAC,SAAS,EAAEH,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC,MAAEG,IAAEJ,EAAC;AAAE,aAAQE,KAAE,GAAEA,KAAEC,KAAED,MAAI;AAAC,YAAMC,MAAER,GAAEO,EAAC,EAAEF,EAAC;AAAE,MAAAC,MAAEE,QAAIF,MAAEE;AAAA,IAAE;AAAC,IAAAT,IAAEM,EAAC,IAAEC;AAAA,EAAC;AAAC,SAAOP;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC,MAAEG,IAAEJ,EAAC;AAAE,aAAQE,KAAE,GAAEA,KAAEC,KAAED,MAAI;AAAC,YAAMC,MAAER,GAAEO,EAAC,EAAEF,EAAC;AAAE,MAAAC,MAAEE,QAAIF,MAAEE;AAAA,IAAE;AAAC,IAAAT,IAAEM,EAAC,IAAEC;AAAA,EAAC;AAAC,SAAOP;AAAC;AAAC,SAAS,EAAEC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC,MAAEG,IAAEJ,EAAC,GAAEE,KAAED;AAAE,aAAQG,MAAE,GAAEA,MAAED,KAAEC,OAAI;AAAC,YAAMD,MAAER,GAAES,GAAC,EAAEJ,EAAC;AAAE,MAAAE,KAAEC,MAAED,KAAEC,MAAEF,MAAEE,QAAIF,MAAEE;AAAA,IAAE;AAAC,IAAAT,IAAEM,EAAC,IAAEE,KAAED;AAAA,EAAC;AAAC,SAAOP;AAAC;AAAC,SAAS0B,GAAEzB,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC,MAAE;AAAE,aAAQC,KAAE,GAAEA,KAAEC,KAAED,KAAI,CAAAD,OAAGN,GAAEO,EAAC,EAAEF,EAAC;AAAE,IAAAN,IAAEM,EAAC,IAAEC,MAAEE;AAAA,EAAC;AAAC,SAAOT;AAAC;AAAC,SAAS,GAAGC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,EAAE,UAAQC,MAAE,GAAEA,MAAEE,KAAEF,OAAI;AAAC,UAAMC,KAAEP,GAAEM,GAAC;AAAE,IAAAP,IAAEM,EAAC,KAAGE,GAAEF,EAAC;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAAS,GAAGC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,UAAMC,MAAE,IAAI,aAAaE,GAAC;AAAE,QAAID,KAAE;AAAE,aAAQT,MAAE,GAAEA,MAAEU,KAAEV,OAAI;AAAC,YAAMU,MAAER,GAAEF,GAAC;AAAE,MAAAS,MAAGC,IAAEH,EAAC,GAAEC,IAAER,GAAC,IAAEU,IAAEH,EAAC;AAAA,IAAC;AAAC,IAAAE,MAAGC;AAAE,QAAIC,MAAE;AAAE,aAAQT,KAAE,GAAEA,KAAEQ,KAAER,KAAI,CAAAS,QAAIH,IAAEN,EAAC,IAAEO,OAAI;AAAE,IAAAR,IAAEM,EAAC,IAAE,KAAK,KAAKI,MAAED,GAAC;AAAA,EAAC;AAAC,SAAOT;AAAC;AAAC,SAAS,GAAGC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAMS,MAAE,KAAK,MAAMD,MAAE,CAAC,GAAE,CAACV,GAAC,IAAEE,IAAED,MAAED,IAAE,QAAOK,MAAEQ,GAAE,gBAAgBJ,IAAER,GAAC,GAAEK,KAAE,IAAI,aAAaI,GAAC,GAAEE,MAAEF,MAAE,KAAG;AAAE,WAAQH,KAAE,GAAEA,KAAEN,KAAEM,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,aAAQC,MAAE,GAAEA,MAAEE,KAAEF,MAAI,CAAAF,GAAEE,GAAC,IAAEN,GAAEM,GAAC,EAAED,EAAC;AAAE,IAAAD,GAAE,KAAK,GAAED,IAAEE,EAAC,IAAEK,MAAEN,GAAEK,GAAC,KAAGL,GAAEK,GAAC,IAAEL,GAAEK,MAAE,CAAC,KAAG;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAAS,GAAGH,IAAEM,KAAEC,IAAE;AAAC,QAAK,CAACC,KAAEC,GAAC,IAAET;AAAE,MAAG,QAAMS,IAAE,QAAOD;AAAE,QAAMV,MAAEU,IAAE,QAAOT,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC;AAAE,WAAQO,KAAE,GAAEA,KAAEP,KAAEO,KAAI,CAAAC,IAAED,EAAC,MAAIG,IAAEH,EAAC,MAAII,IAAEJ,EAAC,IAAEN,IAAEM,EAAC,IAAEG,IAAEH,EAAC,IAAEC,IAAED,EAAC,IAAE;AAAG,SAAON;AAAC;AAAC,SAAS,GAAGC,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,OAAG,EAAE,QAAO,GAAGR,IAAEM,KAAEC,EAAC;AAAE,QAAME,MAAET,GAAE,CAAC,EAAE,QAAOF,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC,GAAEV,MAAE,oBAAI;AAAI,WAAQM,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC;AAAE,IAAAP,IAAE,MAAM;AAAE,aAAQD,MAAE,GAAEA,MAAEU,KAAEV,MAAI,CAAAQ,MAAEN,GAAEF,GAAC,EAAEO,EAAC,GAAEN,IAAE,IAAIO,KAAEP,IAAE,IAAIO,GAAC,IAAEP,IAAE,IAAIO,GAAC,IAAE,IAAE,CAAC;AAAE,QAAIC,KAAE,GAAEE,MAAE;AAAE,eAAUT,MAAKD,IAAE,KAAK,EAAE,CAAAQ,KAAER,IAAE,IAAIC,EAAC,GAAEO,KAAEE,QAAIA,MAAEF,IAAED,MAAEN;AAAG,IAAAF,IAAEO,EAAC,IAAEC;AAAA,EAAC;AAAC,SAAOR;AAAC;AAAC,SAAS,GAAGE,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,OAAG,EAAE,QAAO,GAAGR,IAAEM,KAAEC,EAAC;AAAE,QAAME,MAAET,GAAE,CAAC,EAAE,QAAOF,MAAEa,GAAE,gBAAgBJ,IAAEE,GAAC,GAAEV,MAAE,oBAAI;AAAI,WAAQM,KAAE,GAAEA,KAAEI,KAAEJ,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC;AAAE,IAAAP,IAAE,MAAM;AAAE,aAAQD,MAAE,GAAEA,MAAEU,KAAEV,MAAI,CAAAQ,MAAEN,GAAEF,GAAC,EAAEO,EAAC,GAAEN,IAAE,IAAIO,KAAEP,IAAE,IAAIO,GAAC,IAAEP,IAAE,IAAIO,GAAC,IAAE,IAAE,CAAC;AAAE,QAAIC,KAAE,GAAEE,MAAET,GAAE;AAAO,eAAUA,MAAKD,IAAE,KAAK,EAAE,CAAAQ,KAAER,IAAE,IAAIC,EAAC,GAAEO,KAAEE,QAAIA,MAAEF,IAAED,MAAEN;AAAG,IAAAF,IAAEO,EAAC,IAAEC;AAAA,EAAC;AAAC,SAAOR;AAAC;AAAC,SAAS,GAAGE,IAAEM,KAAEC,IAAE;AAAC,QAAMC,MAAER,GAAE;AAAO,MAAGQ,MAAE,EAAE,QAAOR,GAAE,CAAC;AAAE,QAAK,CAACS,GAAC,IAAET,IAAEF,MAAEW,IAAE,QAAOV,MAAEY,GAAE,gBAAgBJ,IAAET,GAAC,GAAEK,MAAE,oBAAI;AAAI,WAAQE,KAAE,GAAEA,KAAEP,KAAEO,KAAI,KAAG,CAACC,OAAGA,IAAED,EAAC,GAAE;AAAC,QAAIC;AAAE,IAAAH,IAAE,MAAM;AAAE,aAAQI,KAAE,GAAEA,KAAEC,KAAED,KAAI,CAAAD,MAAEN,GAAEO,EAAC,EAAEF,EAAC,GAAEF,IAAE,IAAIG,GAAC;AAAE,IAAAP,IAAEM,EAAC,IAAEF,IAAE;AAAA,EAAI;AAAC,SAAOJ;AAAC;AAAC,IAAM,KAAG,oBAAI;AAAb,IAAiB,KAAG,oBAAI;AAAxB,IAA4B,KAAG,oBAAI;AAAnC,IAAuC,KAAG,oBAAI;AAAI,SAAS,KAAI;AAAC,KAAG,SAAO,GAAG,IAAI,GAAE,KAAK,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,KAAK,GAAE,GAAG,IAAI,IAAG,KAAK,GAAG,GAAE,GAAG,IAAI,IAAG,KAAK,GAAG,GAAE,GAAG,IAAI,IAAG,KAAK,IAAI,GAAE,GAAG,IAAI,IAAG,KAAK,KAAK,GAAE,GAAG,IAAI,IAAG,KAAK,IAAI,GAAE,GAAG,IAAI,IAAG,KAAK,GAAG,GAAE,GAAG,IAAI,IAAG,KAAK,IAAI,GAAE,GAAG,IAAI,IAAG,KAAK,GAAG,GAAE,GAAG,IAAI,IAAG,KAAK,IAAI,GAAE,GAAG,IAAI,IAAG,KAAK,KAAK,GAAE,GAAG,IAAI,IAAG,KAAK,KAAK,GAAE,GAAG,IAAI,IAAG,KAAK,KAAK,GAAE,GAAG,IAAI,GAAE,KAAK,GAAG,GAAE,GAAG,IAAI,IAAG,KAAK,KAAK,GAAE,GAAG,IAAI,GAAEW,GAAC,GAAE,GAAG,IAAI,GAAEE,EAAC,GAAE,GAAG,IAAI,GAAEC,EAAC,GAAE,GAAG,IAAI,IAAGK,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAGA,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGX,EAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGY,EAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAGV,GAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGb,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGa,EAAC,GAAE,GAAG,IAAI,IAAGC,EAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGS,EAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAGA,EAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,CAAC,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,EAAE,GAAE,GAAG,IAAI,IAAG,EAAE;AAAE;AAAC,SAAS,GAAGzB,IAAEK,IAAEC,KAAEC,IAAE;AAAC,MAAG,CAACC,KAAEV,GAAC,IAAEW,GAAEH,GAAC;AAAE,QAAMP,MAAEO,IAAE,WAAW,GAAG,KAAGA,IAAE,WAAW,GAAG;AAAE,EAAAP,QAAIS,OAAG,MAAKV,OAAG;AAAM,WAAQW,MAAE,GAAEA,MAAEJ,GAAE,QAAOI,MAAI,KAAGJ,GAAEI,GAAC,GAAE;AAAC,UAAMH,MAAEN,GAAES,GAAC;AAAE,UAAMH,GAAC,KAAGA,MAAEE,OAAGF,MAAER,MAAEO,GAAEI,GAAC,IAAE,IAAEF,GAAEE,GAAC,IAAEV,MAAE,KAAK,MAAMO,GAAC,IAAEA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGN,IAAES,KAAEV,MAAE,CAAC,GAAE;AAAC,KAAG;AAAE,MAAII,MAAEC,GAAEJ,IAAES,OAAG,MAAIA,OAAG,EAAE;AAAE,QAAK,EAAC,iBAAgBC,MAAE,MAAK,IAAEX,KAAEE,KAAE,CAAC,GAAG,IAAIQ,GAAC,KAAGV,IAAE,oBAAmBa,MAAEX,KAAED,GAAE,CAAC,EAAE,OAAO,SAAO,GAAEa,KAAE,CAAC;AAAE,WAAQT,KAAE,GAAEA,KAAEQ,KAAER,MAAI;AAAC,UAAML,MAAE,GAAG,IAAIU,GAAC,KAAG,CAACR,KAAED,GAAE,QAAS,CAAAA,OAAGA,GAAE,MAAO,IAAEA,GAAE,IAAK,CAAAA,OAAGA,GAAE,OAAOI,EAAC,CAAE;AAAE,QAAIQ,KAAEE,MAAE;AAAG,QAAGL,QAAIA,GAAE,SAAQ;AAAC,YAAMT,KAAE,EAAED,KAAEI,KAAEO,GAAC;AAAE,MAAAE,MAAEZ,GAAE,MAAKG,MAAEH,GAAE,MAAKc,MAAE;AAAA,IAAE,WAAS,GAAG,IAAIL,GAAC,GAAE;AAAC,MAAAG,MAAE,GAAG,IAAIH,GAAC,EAAEV,KAAEI,KAAE,KAAK;AAAA,IAAC,WAAS,GAAG,IAAIM,GAAC,EAAE,CAAAG,MAAEH,QAAIR,GAAE,QAAMQ,QAAIR,GAAE,QAAMQ,QAAIR,GAAE,QAAMgB,GAAElB,KAAEI,KAAE,OAAMM,GAAC,IAAE,EAAEV,KAAEI,KAAE,OAAM,GAAG,IAAIM,GAAC,CAAC;AAAA,aAAU,GAAG,IAAIA,GAAC,EAAE,CAAAG,MAAE,EAAEb,KAAEI,KAAE,OAAM,GAAG,IAAIM,GAAC,CAAC;AAAA,aAAU,GAAG,IAAIA,GAAC,GAAE;AAAC,MAAAG,MAAE,GAAG,IAAIH,GAAC,EAAEV,KAAEI,KAAE,KAAK;AAAA,IAAC,MAAM,CAAAS,MAAEb,IAAE,CAAC,GAAEe,MAAE;AAAG,QAAGA,OAAGL,QAAIP,GAAE,UAAQ,CAACJ,IAAE,IAAIW,GAAC,GAAE;AAAC,YAAMT,KAAEW,GAAE,gBAAgBD,KAAEE,IAAE,MAAM;AAAE,MAAAT,QAAIA,MAAE,IAAI,WAAWS,IAAE,MAAM,EAAE,KAAK,GAAG,IAAG,GAAGA,KAAET,KAAEO,KAAEV,EAAC,GAAEY,MAAEZ;AAAA,IAAC;AAAC,IAAAa,GAAE,KAAKD,GAAC;AAAA,EAAC;AAAC,QAAME,MAAEd,GAAE,CAAC;AAAE,SAAO,IAAIW,GAAE,EAAC,OAAMG,IAAE,OAAM,QAAOA,IAAE,QAAO,WAAUJ,KAAE,MAAKD,QAAIP,GAAE,SAAO,OAAKC,KAAE,QAAOU,GAAC,CAAC;AAAC;;;ACAngU,IAAIa,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,SAAQ,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,SAAS;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,mBAAkBC,GAAC,IAAE,MAAKC,MAAED,GAAE,CAAC,GAAE,EAAC,WAAUE,IAAC,IAAED,KAAE,EAAC,oBAAmBE,GAAC,IAAE,KAAK;AAAkB,QAAGH,GAAE,KAAM,CAAAA,OAAGA,GAAE,cAAYE,GAAE,EAAE,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,4DAA2D;AAAE,UAAK,EAAC,WAAUE,IAAE,SAAQC,IAAC,IAAE,KAAK,mBAAkBC,MAAEC,IAAEH,EAAC;AAAE,QAAG,EAAE,QAAME,OAAGD,IAAE,WAASC,OAAGD,IAAE,UAAQ,KAAG,MAAIC,KAAG,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,mGAAmGA,GAAC,GAAE;AAAE,SAAK,kBAAgB,KAAK,oBAAoB,KAAK;AAAE,UAAME,MAAEP,IAAE,MAAM;AAAE,IAAAO,IAAE,YAAU,KAAK,iBAAgBA,IAAE,aAAW,MAAKA,IAAE,aAAW,MAAKA,IAAE,WAAS,MAAKA,IAAE,iBAAe,MAAKA,IAAE,YAAU,QAAMF,OAAGH,KAAED,MAAE;AAAE,UAAMO,MAAEV,GAAEK,EAAC;AAAE,QAAGK,KAAE;AAAC,MAAAD,IAAE,aAAW,CAAC;AAAE,eAAQR,KAAE,GAAEA,KAAEQ,IAAE,WAAUR,KAAI,CAAAQ,IAAE,WAAWR,EAAC,IAAE,EAAC,KAAIS,IAAE,CAAC,GAAE,KAAIA,IAAE,CAAC,GAAE,MAAKA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,GAAE,SAAQA,IAAE,CAAC,IAAEA,IAAE,CAAC,KAAG,GAAE;AAAA,IAAC;AAAC,SAAK,aAAWD;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAY,MAAIA,IAAE,aAAWF,OAAG,MAAIF,KAAE,MAAIA,KAAE,IAAG;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAE;AAAC,UAAK,EAAC,aAAYE,IAAC,IAAEF;AAAE,WAAO,EAAEE,GAAC,KAAGA,IAAE,KAAM,CAAAF,OAAG,EAAEA,EAAC,CAAE,IAAE,OAAK,GAAEE,KAAE,KAAK,kBAAkB,WAAU,EAAC,oBAAmB,KAAK,kBAAkB,oBAAmB,iBAAgB,KAAK,mBAAiB,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAJj7D;AAIk7D,UAAK,EAAC,WAAUF,GAAC,IAAE,KAAK,mBAAkBC,MAAEM,IAAEP,EAAC,GAAEE,QAAE,YAAO,KAAKO,GAAC,EAAE,KAAM,CAAAR,QAAGQ,IAAER,GAAC,MAAID,EAAE,MAAjC,mBAAoC,kBAAe,aAAYG,KAAE,KAAK,mBAAiB;AAAM,QAAG,CAACG,KAAEE,GAAC,IAAEP,GAAEE,EAAC;AAAE,UAAMI,MAAEJ,GAAE,WAAW,GAAG,KAAGA,GAAE,WAAW,GAAG;AAAE,WAAOI,QAAID,OAAG,MAAKE,OAAG,OAAM,EAAC,YAAWP,KAAE,eAAcC,KAAE,aAAY,CAACI,KAAEE,GAAC,GAAE,iBAAgBD,IAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKS,KAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEA,EAAC;AAAE,IAAMC,KAAED;;;ACAvoE,IAAIY;AAAE,IAAIC,MAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,iBAAe,MAAK,KAAK,eAAa,MAAK,KAAK,uBAAqBC,GAAE;AAAA,EAAQ;AAAA,EAAC,IAAI,yBAAwB;AAAC,UAAK,EAAC,cAAaC,GAAC,IAAE;AAAK,QAAG,EAACA,MAAA,gBAAAA,GAAG,QAAO,QAAO;AAAK,QAAIC,KAAE;AAAG,UAAMC,KAAEF,GAAE,IAAK,CAAAA,OAAG;AAAC,UAAG,YAAU,OAAOA,GAAE,QAAOC,KAAE,MAAG,CAACD,EAAC;AAAE,UAAG,YAAU,OAAOA,IAAE;AAAC,cAAME,KAAEF,GAAE,KAAK,EAAE,MAAM,GAAG,EAAE,OAAQ,CAAAA,OAAG,OAAKA,GAAE,KAAK,CAAE,EAAE,IAAK,CAAAA,OAAG,OAAOA,EAAC,CAAE;AAAE,eAAOC,KAAEA,MAAGC,GAAE,SAAO,GAAE,MAAIA,GAAE,SAAO,OAAKA;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAE;AAAE,WAAOD,KAAEC,KAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAJt2B;AAIu2B,WAAO,IAAIN,GAAE,EAAC,kBAAe,UAAK,mBAAL,mBAAqB,YAAS,CAAC,GAAE,gBAAa,UAAK,iBAAL,mBAAmB,YAAS,CAAC,GAAE,sBAAqB,KAAK,qBAAoB,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,0BAAyB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,wBAAuB,MAAM,GAAEA,MAAED,KAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAEC,GAAC;AAAE,IAAMM,KAAEN;;;ACAnvB,IAAIO,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,QAAO,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMC,KAAE,KAAK,kBAAkB,CAAC,EAAE,MAAM,GAAE,EAAC,WAAUC,GAAC,IAAED;AAAE,SAAK,kBAAgB,KAAK,oBAAoBC,EAAC,GAAED,GAAE,YAAU,KAAK,iBAAgB,KAAK,aAAWA;AAAE,UAAK,EAAC,gBAAeE,KAAE,wBAAuBC,IAAC,IAAE,KAAK;AAAkB,QAAG,EAACD,OAAA,gBAAAA,IAAG,WAAQ,EAACC,OAAA,gBAAAA,IAAG,QAAO,QAAM,EAAC,SAAQ,OAAG,aAAY,OAAG,OAAM,kDAAiD;AAAE,QAAIC,KAAE,CAAC;AAAE,aAAQC,MAAE,GAAEA,MAAEL,GAAE,WAAUK,OAAI;AAAC,YAAML,KAAE,EAAEC,IAAEC,OAAA,gBAAAA,IAAG,MAAM,IAAEG,KAAE,IAAEA,MAAE,IAAGF,OAAA,gBAAAA,IAAIE,IAAE;AAAE,UAAG,QAAML,IAAE;AAAC,QAAAI,KAAE;AAAK;AAAA,MAAK;AAAC,MAAAA,GAAE,KAAKJ,EAAC;AAAA,IAAC;AAAC,SAAK,UAAQI;AAAE,UAAME,MAAE,QAAMH,OAAGA,IAAE,MAAO,CAAAH,OAAC;AAJjvC;AAImvC,cAAAA,MAAA,gBAAAA,GAAG,cAAS,KAAAG,IAAE,CAAC,MAAH,mBAAM;AAAA,KAAO;AAAE,WAAM,EAAC,SAAQ,MAAG,cAAa,CAACD,OAAGA,IAAE,UAAQ,IAAEE,QAAK,CAACD,OAAGG,OAAGH,IAAE,CAAC,EAAE,UAAQC,IAAE;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAE;AAJ32C;AAI42C,UAAMC,MAAE,KAAAD,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,UAAK,EAAC,iBAAgBE,KAAE,SAAQC,GAAC,IAAE,MAAK,EAAC,gBAAeE,KAAE,sBAAqBD,KAAE,wBAAuBE,IAAC,IAAE,KAAK,mBAAkBC,MAAEH,QAAIF,GAAE;AAAS,WAAOM,GAAER,IAAE,EAAC,gBAAeK,KAAE,cAAaC,KAAE,iBAAgBJ,KAAE,UAASK,KAAE,SAAQJ,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAJxpD;AAIypD,UAAK,EAAC,gBAAeJ,IAAE,wBAAuBE,IAAC,IAAE,KAAK,mBAAkBC,MAAE,IAAI,aAAaC,EAAC;AAAE,IAAAD,IAAE,KAAK,CAAC,KAAE,KAAAD,OAAA,gBAAAA,IAAI,OAAJ,mBAAQ,WAAQC,IAAE,IAAID,IAAE,CAAC,CAAC;AAAE,UAAME,KAAE,IAAI,aAAaA,EAAC;AAAE,aAAQE,MAAE,GAAEA,MAAEF,GAAE,QAAOE,OAAG,EAAE,CAAAF,GAAEE,GAAC,KAAEN,MAAA,gBAAAA,GAAIM,SAAI,CAAC,GAAEF,GAAEE,MAAE,CAAC,KAAEN,MAAA,gBAAAA,GAAIM,MAAE,OAAI;AAAE,WAAON,MAAGA,GAAE,UAAQI,GAAE,IAAIJ,EAAC,GAAE,EAAC,WAAU,KAAK,kBAAkB,CAAC,EAAE,WAAU,cAAaG,KAAE,gBAAeC,GAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEN,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKU,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEV,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,WAAU,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,kDAAkD,CAAC,GAAEA,GAAC;AAAE,IAAMY,KAAEZ;;;ACA7/D,IAAIa;AAAE,IAAIC,MAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,GAAE,KAAK,iBAAe,GAAE,KAAK,mBAAiB;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,UAAK,EAAC,eAAcC,IAAE,gBAAeC,IAAE,kBAAiBC,IAAC,IAAE;AAAK,WAAO,IAAIL,GAAE,EAAC,eAAcG,IAAE,gBAAeC,IAAE,kBAAiBC,IAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEJ,IAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,oBAAmB,MAAM,GAAEA,MAAED,KAAE,EAAE,CAAC,EAAE,2DAA2D,CAAC,GAAEC,GAAC;AAAE,IAAMK,KAAEL;;;ACAhY,IAAIM,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,QAAO,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAK,EAAC,kBAAiBC,GAAC,IAAE,KAAK;AAAkB,SAAK,kBAAgB,KAAK,oBAAoBA,KAAE,QAAM,IAAI;AAAE,UAAMC,MAAE,KAAK,kBAAkB,CAAC,EAAE,MAAM;AAAE,IAAAA,IAAE,YAAU,KAAK,iBAAgBA,IAAE,WAAS,MAAKA,IAAE,aAAW,MAAKA,IAAE,YAAU;AAAE,UAAK,CAACC,IAAEC,IAAEC,KAAEC,GAAC,IAAEL,KAAE,CAAC,IAAG,GAAE,GAAE,GAAE,IAAE,CAAC,GAAE,KAAI,KAAI,EAAE;AAAE,WAAOC,IAAE,aAAW,CAAC,EAAC,KAAIC,IAAE,KAAIC,IAAE,KAAIC,KAAE,QAAOC,IAAC,CAAC,GAAE,KAAK,aAAWJ,KAAE,EAAC,SAAQ,MAAG,aAAY,KAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAJ9hC;AAI+hC,UAAME,MAAE,KAAAF,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEE,EAAC,EAAE,QAAO;AAAK,UAAK,EAAC,eAAcC,IAAE,gBAAeE,KAAE,kBAAiBC,IAAC,IAAE,KAAK;AAAkB,WAAO,EAAEJ,IAAEC,IAAEE,KAAE,CAACC,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAK,EAAC,eAAcN,IAAE,gBAAeC,KAAE,kBAAiBC,GAAC,IAAE,KAAK,mBAAkBC,KAAE,KAAK,yBAAuB,CAAC,GAAE,GAAE,CAAC,IAAE,CAACF,KAAED,IAAE,CAAC;AAAE,WAAM,EAAC,eAAcD,GAAEI,EAAC,GAAE,QAAO,CAACD,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE;AAAC,UAAK,EAAC,eAAcC,KAAE,gBAAeC,GAAC,IAAE,KAAK;AAAkB,WAAM,CAACA,IAAED,KAAE,CAAC,EAAE,IAAK,CAAAA,QAAGD,GAAEC,GAAC,CAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEH,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEN,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,kDAAkD,CAAC,GAAEA,GAAC;AAAE,IAAMS,MAAET;;;ACA/7C,IAAIU;AAAE,IAAIC,MAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,MAAK,KAAK,eAAa,MAAK,KAAK,eAAa,MAAK,KAAK,iBAAe,OAAG,KAAK,4BAA0B;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAC,aAAY,CAAC,GAAG,KAAK,WAAW,GAAE,cAAa,CAAC,GAAG,KAAK,YAAY,GAAE,cAAa,CAAC,GAAG,KAAK,YAAY,GAAE,gBAAe,KAAK,gBAAe,2BAA0B,KAAK,0BAAyB,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,IAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,6BAA4B,MAAM,GAAEA,MAAED,KAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAEC,GAAC;AAAE,IAAME,KAAEF;;;ACAnc,IAAIG,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,SAAQ,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,qBAAoB;AAAC,UAAMC,KAAE,KAAK,kBAAkB,CAAC,EAAE,MAAM,GAAE,EAAC,WAAUC,GAAC,IAAED;AAAE,SAAK,kBAAgB,KAAK,oBAAoBC,EAAC,GAAED,GAAE,YAAU,KAAK,iBAAgBA,GAAE,WAAS,MAAKA,GAAE,aAAW,MAAKA,GAAE,YAAU,GAAEA,GAAE,iBAAe;AAAK,UAAK,EAAC,YAAWE,IAAC,IAAEF,IAAE,EAAC,gBAAeG,KAAE,cAAaC,KAAE,aAAYC,IAAE,cAAaC,KAAE,2BAA0BC,IAAC,IAAE,KAAK;AAAkB,QAAG,EAAEL,GAAC,KAAGA,IAAE,WAAQE,OAAA,gBAAAA,IAAG,QAAO,KAAGD,KAAE;AAAC,YAAMF,KAAE,KAAK,IAAI,MAAM,MAAK,CAAC,GAAGG,KAAEF,IAAE,CAAC,EAAE,GAAG,CAAC,GAAEM,MAAE,KAAK,IAAI,MAAM,MAAK,CAAC,GAAGJ,KAAEF,IAAE,CAAC,EAAE,GAAG,CAAC;AAAE,MAAAF,GAAE,aAAW,CAAC,EAAC,GAAGE,IAAE,CAAC,GAAE,KAAID,IAAE,KAAIO,IAAC,CAAC;AAAA,IAAC,OAAK;AAAC,UAAIP,KAAEG,IAAE,CAAC,GAAEI,MAAEP;AAAE,eAAQD,KAAE,GAAEA,KAAEI,IAAE,QAAOJ,KAAI,CAAAC,KAAEA,KAAEG,IAAEJ,EAAC,IAAEI,IAAEJ,EAAC,IAAEC,IAAEO,MAAEA,MAAEJ,IAAEJ,EAAC,IAAEQ,MAAEJ,IAAEJ,EAAC;AAAE,MAAAA,GAAE,aAAW,CAAC,EAAC,GAAGE,IAAE,CAAC,GAAE,KAAID,IAAE,KAAIO,IAAC,CAAC;AAAA,IAAC;AAAC,SAAK,aAAWR,IAAE,KAAK,SAAOG,MAAE,OAAK,EAAE,EAAC,cAAaF,IAAE,aAAYI,IAAE,cAAaD,KAAE,cAAaE,KAAE,gBAAeH,KAAE,2BAA0BI,KAAE,iBAAgB,KAAK,gBAAe,CAAC;AAAE,WAAM,EAAC,SAAQ,MAAG,cAAa,CAACH,OAAGA,IAAE,UAAQC,QAAK,CAACC,OAAGA,IAAE,UAAQD,IAAE;AAAA,EAAC;AAAA,EAAC,eAAeL,IAAE;AAJxuD;AAIyuD,UAAMC,MAAE,KAAAD,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,UAAK,EAAC,QAAOE,KAAE,iBAAgBC,IAAC,IAAE;AAAK,QAAGD,KAAE;AAAC,YAAMH,KAAEO,GAAEN,IAAE,EAAC,KAAI,CAACE,IAAE,GAAG,GAAE,QAAOA,IAAE,QAAO,iBAAgBC,IAAC,CAAC;AAAE,aAAO,EAAEJ,EAAC,KAAGG,IAAE,SAAOH,GAAE,OAAK,EAAEC,GAAE,OAAO,CAAC,GAAEA,GAAE,MAAKE,IAAE,MAAKA,IAAE,QAAO,IAAI,IAAGH;AAAA,IAAC;AAAC,UAAK,EAAC,aAAYK,IAAE,cAAaC,KAAE,cAAaG,KAAE,gBAAeV,KAAE,2BAA0BW,GAAC,IAAE,KAAK;AAAkB,WAAO,EAAET,IAAE,EAAC,aAAYI,IAAE,cAAaC,KAAE,cAAaG,KAAE,iBAAgBL,KAAE,gBAAeL,KAAE,2BAA0BW,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAK,EAAC,gBAAeV,IAAE,aAAYQ,KAAE,cAAaN,KAAE,cAAaC,KAAE,2BAA0BC,IAAC,IAAE,KAAK,mBAAkBE,MAAE,IAAI,aAAa,IAAED,EAAC,GAAEI,MAAE,MAAKF,MAAEL,IAAE;AAAO,QAAGM,OAAA,gBAAAA,IAAG,QAAO;AAAC,UAAIR,KAAE,GAAEG,MAAE;AAAE,eAAQE,KAAE,GAAEA,KAAEC,IAAE,QAAOD,MAAG,EAAE,CAAAC,IAAED,EAAC,IAAEG,IAAER,IAAG,KAAG,IAAE,GAAEM,IAAED,KAAE,CAAC,IAAEG,IAAER,IAAG,KAAG,GAAEM,IAAED,KAAE,CAAC,IAAEH,IAAEC,KAAG,KAAG,GAAEA,OAAGI,QAAIF,KAAE,MAAIC,IAAED,EAAC,KAAGI,OAAIN,MAAEI,OAAG,CAACH,SAAKE,IAAED,KAAE,CAAC,KAAGI;AAAA,IAAG;AAAC,UAAME,MAAE,IAAI,aAAa,IAAEN,EAAC;AAAE,IAAAM,IAAE,KAAK,CAAC,IAAER,OAAA,gBAAAA,IAAG,WAAQQ,IAAE,IAAIR,GAAC;AAAE,WAAM,EAAC,gBAAeH,IAAE,WAAUM,KAAE,cAAaK,KAAE,YAAWH,GAAE,KAAK,eAAe,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEV,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKO,IAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEA,EAAC;AAAE,IAAMc,KAAEd;;;ACA7iF,IAAIe;AAAE,IAAMC,MAAE,IAAIC,GAAE,EAAC,GAAE,UAAS,GAAE,gBAAe,GAAE,WAAU,GAAE,EAAC,gBAAe,KAAE,CAAC;AAAE,IAAIC,MAAEH,MAAE,cAAcI,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU,UAAS,KAAK,UAAQ,GAAE,KAAK,iBAAe,OAAK,KAAK,kBAAgB,OAAK,KAAK,mBAAiB;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIJ,IAAE,EAAC,WAAU,KAAK,WAAU,SAAQ,KAAK,SAAQ,gBAAe,KAAK,gBAAe,iBAAgB,KAAK,iBAAgB,kBAAiB,KAAK,kBAAiB,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAACK,GAAEJ,GAAC,CAAC,GAAEE,IAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,WAAU,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,aAAY,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,oBAAmB,MAAM,GAAEA,MAAEH,MAAE,EAAE,CAAC,EAAE,4DAA4D,CAAC,GAAEG,GAAC;AAAE,IAAMG,MAAEH;;;ACAn5B,IAAMI,MAAE,IAAE;AAAM,IAAIC,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,SAAQ,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,QAAM;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAJzpB;AAI0pB,SAAK,kBAAgB,KAAK,oBAAoB,KAAK;AAAE,UAAMC,KAAE,KAAK,kBAAkB,CAAC,EAAE,MAAM;AAAE,WAAOA,GAAE,YAAU,KAAK,iBAAgBA,GAAE,aAAW,mBAAiB,KAAK,kBAAkB,YAAU,CAAC,EAAC,KAAI,GAAE,KAAI,IAAG,KAAI,GAAE,QAAO,EAAC,CAAC,IAAE,MAAKA,GAAE,aAAW,MAAKA,GAAE,WAAS,MAAKA,GAAE,iBAAe,MAAKA,GAAE,YAAU,GAAE,KAAK,aAAWA,IAAE,KAAK,UAAM,KAAAA,GAAE,qBAAF,mBAAoB,iBAAc,OAAG,EAAC,SAAQ,MAAG,aAAY,KAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAJ1jC;AAI2jC,UAAMC,OAAE,KAAAD,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEC,GAAC,EAAE,QAAO;AAAK,UAAK,EAAC,SAAQC,IAAE,WAAUC,KAAE,gBAAeC,KAAE,iBAAgBP,IAAC,IAAE,KAAK,mBAAkB,EAAC,OAAMC,IAAC,IAAE,MAAK,EAAC,QAAOO,IAAC,IAAEL,IAAEM,MAAED,MAAE,EAAC,GAAEA,IAAE,QAAMJ,IAAE,OAAM,GAAEI,IAAE,SAAOJ,IAAE,OAAM,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,WAAOK,GAAEL,KAAE,EAAC,SAAQC,IAAE,WAAUC,KAAE,gBAAeC,KAAE,iBAAgBP,KAAE,OAAMC,KAAE,YAAWQ,IAAC,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAK,EAAC,SAAQN,IAAE,WAAUO,IAAE,iBAAgBN,KAAE,gBAAeC,GAAC,IAAE,KAAK;AAAkB,WAAM,EAAC,SAAQ,KAAK,SAAOF,MAAG,IAAEA,KAAEH,MAAEG,IAAE,WAAUO,IAAE,iBAAgBN,OAAG,GAAE,gBAAeC,MAAG,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEJ,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKU,KAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEV,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,SAAQ,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEA,GAAC;AAAE,IAAMO,MAAEP;;;ACA76C,IAAIW;AAAE,IAAIC,MAAED,MAAE,cAAcE,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,aAAW,MAAK,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,eAAeC,IAAEC,KAAE;AAAC,QAAG,EAACD,MAAA,gBAAAA,GAAG,QAAO,QAAO;AAAK,UAAME,KAAE,CAAC;AAAE,WAAOF,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAMC,MAAE,EAAC,KAAID,GAAE,KAAI,KAAIA,GAAE,KAAI,KAAIA,GAAE,OAAKA,GAAE,MAAK,QAAOA,GAAE,UAAQA,GAAE,kBAAiB;AAAE,MAAAE,GAAE,KAAKD,GAAC;AAAA,IAAC,CAAE,GAAEC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,KAAEC,IAAE;AAAC,QAAG,EAACF,MAAA,gBAAAA,GAAG,QAAO;AAAO,UAAMG,MAAE,CAAC;AAAE,IAAAH,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAMC,MAAE,EAAC,GAAGD,IAAE,MAAKA,GAAE,KAAI,mBAAkBA,GAAE,OAAM;AAAE,aAAOC,IAAE,KAAI,OAAOA,IAAE,QAAOE,IAAE,KAAKF,GAAC;AAAA,IAAC,CAAE,GAAEA,IAAEC,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIN,IAAE,EAAC,YAAW,EAAE,KAAK,UAAU,GAAE,YAAW,EAAE,KAAK,UAAU,EAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,YAAY,CAAC,GAAEA,IAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAACI,GAAE,YAAY,CAAC,GAAEJ,IAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,cAAa,MAAM,GAAEA,MAAED,MAAE,EAAE,CAAC,EAAE,0EAA0E,CAAC,GAAEC,GAAC;AAAE,IAAMC,MAAED;;;ACA/8B,IAAIM,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,uBAAsB,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,gBAAc;AAAA,EAAE;AAAA,EAAC,qBAAoB;AAAC,UAAMC,MAAE,KAAK,kBAAkB,CAAC;AAAE,SAAK,kBAAgB,KAAK,oBAAoB,IAAI;AAAE,UAAMC,KAAED,IAAE,MAAM,GAAE,EAAC,YAAWE,IAAE,YAAWC,IAAC,IAAE,KAAK;AAAkB,WAAOA,QAAIF,GAAE,aAAWE,MAAGD,OAAID,GAAE,aAAWC,KAAG,KAAK,aAAWD,IAAE,EAAC,SAAQ,MAAG,aAAY,KAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,KAAE;AAJ71B;AAI81B,YAAO,KAAAA,IAAE,gBAAF,mBAAgB;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEF,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,KAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEN,IAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,iBAAgB,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,iEAAiE,CAAC,GAAEA,GAAC;AAAE,IAAMO,MAAEP;;;ACApvB,IAAIQ;AAAE,IAAMC,MAAE,IAAIC,GAAE,EAAC,GAAE,QAAO,GAAE,sBAAqB,GAAE,0BAAyB,GAAE,WAAU,GAAE,gBAAe,GAAE,UAAS,GAAE,EAAC,gBAAe,KAAE,CAAC;AAAE,IAAIC,MAAEH,MAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,OAAG,KAAK,yBAAuB,OAAG,KAAK,QAAM,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,aAAW,MAAK,KAAK,cAAY,QAAO,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,gBAAgBG,IAAEC,IAAEC,KAAE;AAAC,KAAAF,MAAA,gBAAAA,GAAG,YAAS,MAAM,QAAQA,GAAE,CAAC,CAAC,MAAIA,KAAEA,GAAE,IAAK,CAAAA,OAAG,CAACA,GAAE,KAAIA,GAAE,KAAIA,GAAE,KAAIA,GAAE,MAAM,CAAE,IAAGC,GAAEC,GAAC,IAAEF;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIJ,IAAE,EAAC,aAAY,KAAK,aAAY,WAAU,KAAK,WAAU,WAAU,KAAK,WAAU,UAAS,KAAK,UAAS,cAAa,KAAK,cAAa,YAAW,EAAE,KAAK,UAAU,GAAE,OAAM,EAAE,KAAK,KAAK,GAAE,sBAAqB,KAAK,sBAAqB,4BAA2B,KAAK,4BAA2B,YAAW,KAAK,YAAW,YAAW,KAAK,YAAW,YAAW,EAAE,KAAK,UAAU,GAAE,wBAAuB,KAAK,wBAAuB,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEG,IAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,OAAM,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,8BAA6B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,OAAM,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,OAAM,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,CAAC,CAAC,MAAM,CAAC,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACI,GAAE,YAAY,CAAC,GAAEJ,IAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAACG,GAAEL,GAAC,CAAC,GAAEE,IAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,YAAW,MAAM,GAAEA,MAAEH,MAAE,EAAE,CAAC,EAAE,8DAA8D,CAAC,GAAEG,GAAC;AAAE,IAAMK,MAAEL;;;ACA96D,IAAIM,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,WAAU,KAAK,oBAAkB,MAAK,KAAK,sBAAoB,CAAC,QAAQ,GAAE,KAAK,SAAO,MAAK,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,qBAAoB;AAAC,SAAK,SAAO,MAAK,KAAK,UAAQ;AAAK,UAAMC,KAAE,KAAK,kBAAkB,CAAC,GAAE,EAAC,WAAUC,IAAC,IAAED,IAAE,EAAC,mBAAkBE,GAAC,IAAE,MAAK,EAAC,wBAAuBC,KAAE,OAAMC,IAAE,UAASC,IAAC,IAAEH;AAAE,QAAG,CAACC,OAAG,CAAC,MAAK,OAAM,MAAK,KAAK,EAAE,SAASF,GAAC,GAAE;AAAC,YAAME,MAAEG,GAAEJ,GAAE,OAAO,GAAE,EAAC,YAAWF,GAAC,CAAC,GAAEO,MAAE,KAAK,wBAAwB,IAAE,UAAQ;AAAQ,WAAK,SAAOF,GAAE,EAAC,WAAUJ,KAAE,GAAGE,KAAE,OAAME,MAAED,KAAE,MAAK,UAASG,IAAC,CAAC,GAAE,KAAK,UAAQJ;AAAA,IAAC,MAAM,CAAAA,QAAI,KAAK,UAAQG,GAAEJ,GAAE,OAAO,GAAE,EAAC,YAAWF,GAAC,CAAC;AAAG,SAAK,kBAAgB,KAAK,oBAAoBC,GAAC;AAAE,UAAMM,MAAEP,GAAE,MAAM;AAAE,IAAAO,IAAE,YAAU,KAAK,iBAAgBA,IAAE,aAAW,MAAKA,IAAE,aAAW,MAAKA,IAAE,WAAS,MAAKA,IAAE,iBAAe,MAAK,SAAO,KAAK,oBAAkBA,IAAE,cAAc,WAAS,cAAa,KAAK,aAAWA;AAAE,WAAM,EAAC,SAAQ,MAAG,aAAY,CAACJ,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAE;AAJp/C;AAIq/C,UAAME,MAAE,KAAAF,GAAE,gBAAF,mBAAgB;AAAG,QAAG,EAAEE,EAAC,EAAE,QAAOA;AAAE,UAAK,EAAC,QAAOC,IAAC,IAAE;AAAK,QAAGA,IAAE,QAAOK,GAAEN,IAAE,EAAC,GAAGC,KAAE,iBAAgB,KAAK,WAAW,UAAS,CAAC;AAAE,UAAK,EAAC,mBAAkBC,GAAC,IAAE,MAAKG,MAAE,KAAK,WAASD,GAAEF,GAAE,OAAO,GAAE,EAAC,YAAW,KAAK,kBAAkB,CAAC,GAAE,YAAWF,GAAC,CAAC,GAAEO,MAAEL,GAAE,WAASA,GAAE,QAAM;AAAK,WAAOM,GAAER,IAAE,EAAC,GAAGK,KAAE,OAAME,KAAE,iBAAgB,KAAK,gBAAe,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAK,EAAC,WAAUT,KAAE,GAAE,WAAUC,MAAE,KAAI,OAAMC,IAAE,UAASC,IAAC,IAAE,KAAK,mBAAkBC,KAAE,KAAK,WAAW,aAAW,IAAE,IAAE,GAAEC,MAAEF,OAAGD,MAAGA,GAAE,SAAOS,GAAEP,IAAEF,EAAC,IAAE,CAAC,GAAE,GAAE,CAAC,GAAE,EAAC,WAAUK,KAAE,WAAUK,IAAC,IAAE,KAAK,WAAS,EAAC,WAAU,CAAC,GAAE,GAAE,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,EAAC;AAAE,UAAIL,IAAE,WAASA,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC,GAAEK,IAAE,CAAC,IAAEA,IAAE,CAAC,IAAEA,IAAE,CAAC;AAAG,UAAMH,MAAE,IAAI,aAAaL,EAAC;AAAE,QAAII;AAAE,SAAIA,MAAE,GAAEA,MAAEJ,IAAEI,MAAI,CAAAC,IAAED,GAAC,KAAGP,MAAED,OAAIY,IAAEJ,GAAC,IAAED,IAAEC,GAAC;AAAG,UAAMV,MAAE,KAAK,wBAAwB;AAAE,WAAM,EAAC,WAAUM,IAAE,QAAOJ,IAAE,QAAOC,KAAE,WAAUM,KAAE,WAAUK,KAAE,QAAOH,KAAE,UAASN,KAAE,OAAMA,OAAGD,KAAEA,KAAE,CAAC,GAAE,GAAE,CAAC,GAAE,iBAAgBC,OAAGE,MAAEA,MAAE,CAAC,GAAE,GAAE,CAAC,GAAE,aAAY,KAAK,kBAAkB,aAAY,iBAAgBP,KAAE,MAAK,UAAS;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,MAAG,MAAK,iBAAgB,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKO,KAAE,MAAK,EAAC,OAAM,MAAG,MAAK,0BAAyB,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,qDAAqD,CAAC,GAAEA,EAAC;AAAE,IAAMC,MAAED;;;ACAvuE,IAAMe,KAAE,oBAAI;AAAwJ,SAASC,GAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,yBAAwBC,GAAC,IAAEF;AAAE,MAAG,CAACE,GAAE;AAAO,GAACA,GAAE,WAAS,CAACA,GAAE,MAAM,GAAG,QAAS,CAAAF,OAAG;AAAC,IAAAA,MAAG,YAAU,OAAOA,OAAI,YAAU,OAAOA,KAAEA,GAAE,WAAW,MAAM,MAAIC,GAAE,SAASD,EAAC,KAAGC,GAAE,KAAKD,EAAC,KAAG,6BAA4BA,MAAGD,GAAEC,IAAEC,EAAC;AAAA,EAAE,CAAE;AAAC;AAAC,SAASE,GAAEH,IAAEI,KAAE;AAAC,MAAGA,MAAEA,OAAG,CAAC,GAAE,eAAaJ,KAAE,EAAEA,EAAC,MAAI,eAAcA,MAAGA,GAAE,cAAYA,KAAEK,GAAEL,IAAEI,GAAC,IAAG,oBAAmBJ,GAAE,QAAOM,GAAEN,KAAEO,GAAEP,EAAC,GAAEI,GAAC;AAAE,QAAM,IAAI,EAAE,0BAAyB,mCAAmC;AAAC;AAAC,SAASI,GAAER,IAAEC,IAAE;AAAC,SAAM,cAAYA,GAAE,CAAC,KAAG,MAAM,QAAQD,GAAE,OAAO,IAAEA,GAAE,UAAQC,GAAE,IAAK,CAAAA,OAAGD,GAAEC,EAAC,CAAE;AAAC;AAAC,SAASQ,GAAET,IAAE;AAAC,SAAM,CAAC,EAAEA,MAAG,YAAU,OAAOA,MAAGA,GAAE,kBAAgBA,GAAE;AAAwB;AAAC,SAASO,GAAEP,IAAE;AAJh5C;AAIi5C,QAAK,EAAC,gBAAeC,IAAE,yBAAwBC,GAAC,IAAEF,IAAEI,MAAE,CAAC;AAAE,aAAUM,OAAKR,IAAE;AAAC,QAAIF,KAAEE,GAAEQ,GAAC;AAAE,UAAMT,KAAES,IAAE,YAAY;AAAE,QAAG,cAAYT,MAAG,MAAM,QAAQD,EAAC,EAAE,CAAAI,IAAE,UAAQJ,GAAE,IAAK,CAAAA,OAAGS,GAAET,EAAC,IAAEO,GAAEP,EAAC,IAAEA,EAAE;AAAA,QAAO,SAAOS,GAAET,EAAC,MAAIA,KAAEO,GAAEP,EAAC,IAAGC,IAAE;AAAA,MAAC,KAAI;AAAM,QAAAG,IAAE,MAAIJ;AAAE;AAAA,MAAM,KAAI;AAAU,QAAAI,IAAE,UAAQJ;AAAE;AAAA,MAAM,KAAI;AAAY,QAAAI,IAAE,YAAUJ;AAAE;AAAA,MAAM,KAAI;AAAU,QAAAI,IAAE,UAAQJ;AAAE;AAAA,MAAM;AAAQ,QAAAI,IAAEM,IAAE,CAAC,EAAE,YAAY,IAAEA,IAAE,MAAM,CAAC,CAAC,IAAEV;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,YAAUC,QAAG,KAAAG,IAAE,YAAF,mBAAW,YAASA,IAAE,UAAQ,CAAC,IAAI,IAAG,EAAC,GAAGJ,IAAE,yBAAwBI,IAAC;AAAC;AAAC,SAASE,GAAEN,IAAEE,IAAE;AAJn3D;AAIo3D,QAAK,EAAC,gBAAeE,KAAE,yBAAwBM,IAAC,IAAEV,IAAEW,OAAE,KAAAX,GAAE,oBAAF,mBAAmB;AAAc,MAAG,QAAMI,OAAG,CAACQ,GAAE,IAAIR,GAAC,EAAE,OAAM,IAAI,EAAE,0BAAyB,gCAAgCA,GAAC,EAAE;AAAE,QAAMS,MAAED,GAAE,IAAIR,GAAC,GAAEU,OAAG,cAAY,OAAOD,IAAE,OAAKA,IAAE,OAAKA,IAAE,KAAK,SAAS,SAAS,EAAC,GAAGb,IAAE,iBAAgBW,IAAC,CAAC,GAAE,EAAC,qBAAoBI,IAAC,IAAED,KAAEE,MAAE,CAAC,GAAEC,MAAET,GAAEE,KAAEK,GAAC,GAAEG,MAAE,cAAYH,IAAE,CAAC,GAAEI,MAAE,CAAC;AAAE,WAAQlB,KAAE,GAAEA,KAAEgB,IAAE,QAAOhB,MAAI;AAAC,UAAMD,KAAEiB,IAAEhB,EAAC;AAAE,QAAIG;AAAE,YAAMJ,MAAG,YAAU,OAAOA,MAAGA,GAAE,WAAW,GAAG,IAAEgB,IAAE,KAAKd,MAAA,gBAAAA,GAAG,MAAM,IAAE,YAAU,OAAOF,KAAEE,GAAEF,EAAC,KAAGgB,IAAE,KAAKd,GAAEF,EAAC,CAAC,IAAE,YAAU,OAAOA,MAAG,oBAAmBA,OAAII,MAAEE,GAAEN,IAAEE,EAAC,GAAEgB,QAAIJ,IAAE,kBAAkBC,IAAEd,EAAC,CAAC,IAAEG,MAAGY,IAAE,KAAKZ,GAAC,IAAGc,OAAGC,IAAE,KAAKf,OAAGJ,EAAC;AAAA,EAAC;AAAC,MAAGkB,QAAIJ,IAAE,kBAAkB,UAAQK,MAAGjB,IAAE;AAAC,IAAAY,IAAE,gBAAcE;AAAE,UAAMhB,MAAE,KAAAE,GAAE,WAAF,mBAAU;AAAI,IAAAF,OAAIc,IAAE,sBAAoBd;AAAA,EAAE;AAAC,SAAOc;AAAC;AAAC,SAASM,GAAEpB,IAAEC,IAAE;AAAC,MAAGD,MAAGC,GAAE,YAAUC,MAAKF,IAAE;AAAC,UAAMI,MAAEJ,GAAEE,EAAC;AAAE,IAAAE,OAAG,YAAU,OAAOA,QAAIA,IAAE,YAAUA,IAAE,YAAUgB,GAAEhB,IAAE,WAAUH,EAAC,IAAE,6BAA2BG,IAAE,QAAM,QAAMH,GAAEG,IAAE,IAAI,MAAIA,IAAE,QAAMH,GAAEG,IAAE,IAAI;AAAA,EAAG;AAAC;AAAC,SAASiB,GAAErB,IAAE;AAJ/xF;AAIgyF,MAAG,CAACA,MAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,MAAG,MAAM,QAAQA,EAAC,KAAG,MAAIA,GAAE,OAAO,QAAO,MAAIA,GAAE,SAAO,OAAK,CAAC,UAAS,QAAQ,EAAE,SAAS,OAAOA,GAAE,CAAC,CAAC,IAAEA,KAAEA,GAAE,IAAK,CAAAA,OAAGqB,GAAErB,EAAC,CAAE;AAAE,MAAG,WAAUA,MAAG,CAAC,UAAS,UAAS,SAAS,EAAE,SAAS,OAAOA,GAAE,KAAK,EAAE,QAAOA,GAAE;AAAM,MAAG,EAAE,UAASA,IAAG,QAAOA;AAAE,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAS,aAAOA,GAAE;AAAA,IAAM,KAAI;AAAuB,aAAOsB,GAAEtB,EAAC;AAAA,IAAE,KAAI;AAAqB,aAAM,EAAC,MAAK,aAAY,YAAWA,GAAE,iBAAiB,IAAIsB,EAAC,EAAC;AAAA,IAAE,KAAI;AAAgB,eAAO,KAAAtB,GAAE,aAAF,mBAAY,UAAO,uBAAqBA,GAAE,SAAS,CAAC,EAAE,OAAKA,GAAE,WAAS,6BAA2BA,GAAE,SAAS,CAAC,EAAE,OAAKA,GAAE,SAAS,IAAK,CAAAA,OAAG,QAAMA,GAAE,QAAMqB,GAAErB,GAAE,KAAK,IAAEA,GAAE,KAAK,YAAY,EAAE,SAAS,QAAQ,IAAE,OAAK,IAAK,IAAEA,KAAEA,GAAE;AAAA,IAAS;AAAQ,aAAOA;AAAA,EAAC;AAAC;AAAC,SAASsB,GAAErB,IAAE;AAAC,QAAMC,KAAED,GAAE,aAAW;AAAmB,MAAG,EAAC,WAAUG,KAAE,SAAQM,IAAC,IAAET;AAAE,MAAG,CAAC,MAAM,QAAQG,GAAC,GAAE;AAAC,UAAK,EAAC,GAAAH,IAAE,GAAEC,IAAE,GAAEQ,IAAC,IAAES,GAAE,EAAC,GAAEf,IAAE,KAAI,GAAEA,IAAE,YAAW,GAAEA,IAAE,MAAK,CAAC;AAAE,IAAAA,MAAE,CAACH,IAAEC,IAAEQ,KAAEN,IAAE,UAAU;AAAA,EAAC;AAAC,MAAG,CAAC,MAAM,QAAQM,GAAC,GAAE;AAAC,UAAK,EAAC,GAAAT,IAAE,GAAEC,IAAE,GAAEE,IAAC,IAAEe,GAAE,EAAC,GAAET,IAAE,KAAI,GAAEA,IAAE,YAAW,GAAEA,IAAE,MAAK,CAAC;AAAE,IAAAA,MAAE,CAACT,IAAEC,IAAEE,KAAEM,IAAE,UAAU;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,eAAc,WAAUR,IAAE,WAAUE,KAAE,SAAQM,IAAC;AAAC;AAAC,SAASL,GAAEL,IAAEC,IAAE;AAAC,EAAAA,MAAGmB,GAAEpB,IAAEC,EAAC;AAAE,QAAMC,KAAE,CAAC;AAAE,SAAOqB,GAAEvB,IAAEE,EAAC,GAAEA;AAAC;AAAC,SAASqB,GAAEvB,IAAEC,IAAE;AAAC,MAAG,CAACD,MAAG,CAACC,GAAE;AAAO,QAAK,EAAC,UAASC,IAAE,WAAUE,IAAC,IAAEJ;AAAE,MAAG,CAACE,MAAG,CAACE,IAAE;AAAO,EAAAH,GAAE,iBAAeC,GAAE,KAAK,QAAQ,YAAW,EAAE,GAAED,GAAE,kBAAgBC,GAAE;AAAU,QAAMQ,MAAE,CAAC;AAAE,EAAAT,GAAE,0BAAwBS;AAAE,aAAUC,OAAKP,KAAE;AAAC,UAAMJ,KAAEI,IAAEO,GAAC;AAAE,gBAAU,OAAOX,OAAI,cAAaA,MAAGA,GAAE,YAAUA,GAAE,aAAWC,GAAE,wBAAwBU,GAAC,IAAE,CAAC,GAAEY,GAAEvB,IAAEC,GAAE,wBAAwBU,GAAC,CAAC,KAAG,WAAUX,OAAIU,IAAEC,GAAC,IAAEU,GAAErB,GAAE,KAAK;AAAA,EAAG;AAAC,UAAOU,IAAE,OAAK,CAACA,IAAE,WAASA,IAAE,SAAOA,IAAE,KAAI,OAAOA,IAAE,MAAKT,GAAE,gBAAe;AAAA,IAAC,KAAI;AAAU,MAAAuB,GAAEd,GAAC;AAAE;AAAA,IAAM,KAAI;AAAW,MAAAe,GAAEf,GAAC;AAAE;AAAA,IAAM,KAAI;AAAc,MAAAgB,GAAEhB,GAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAiB,GAAEjB,GAAC;AAAA,EAAC;AAAC;AAAC,SAASc,GAAExB,IAAE;AAJ56I;AAI66I,SAAAA,GAAE,eAAF,mBAAc,WAAQ,YAAU,OAAOA,GAAE,eAAaA,GAAE,aAAWA,GAAE,WAAW,IAAK,CAAAA,OAAG,CAACA,GAAE,KAAIA,GAAE,KAAIA,GAAE,MAAKA,GAAE,iBAAiB,CAAE,IAAG,QAAMA,GAAE,8BAA4BA,GAAE,6BAA2BA,GAAE,2BAA0B,OAAOA,GAAE;AAA0B;AAAC,SAASyB,GAAEzB,IAAE;AAJ/rJ;AAIgsJ,0BAAoB,WAAAA,GAAE,cAAF,mBAAa,SAAb,mBAAmB,mBAAgB,OAAOA,GAAE,WAAUA,GAAE,eAAa,WAAU,MAAIA,GAAE,mBAAiB,OAAOA,GAAE;AAAS;AAAC,SAAS0B,GAAE1B,IAAE;AAAC,UAAMA,GAAE,oBAAkBA,GAAE,OAAKA,GAAE,iBAAgB,OAAOA,GAAE;AAAgB;AAAC,SAAS2B,GAAE3B,IAAE;AAJr7J;AAIs7J,SAAAA,GAAE,iBAAF,mBAAgB,WAAQ,YAAU,OAAOA,GAAE,aAAa,CAAC,MAAIA,GAAE,eAAaA,GAAE,aAAa,OAAQ,CAAAA,OAAG,OAAKA,EAAE,EAAE,IAAK,CAAAA,OAAG,OAAOA,EAAC,CAAE;AAAE;AAACY,GAAE,IAAI,UAAS,EAAC,MAAK,mBAAkB,MAAKI,IAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEJ,GAAE,IAAI,kBAAiB,EAAC,MAAK,4BAA2B,MAAKK,IAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEL,GAAE,IAAI,YAAW,EAAC,MAAK,qBAAoB,MAAKG,IAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEH,GAAE,IAAI,iBAAgB,EAAC,MAAK,0BAAyB,MAAKO,KAAE,qBAAoB,CAAC,SAAS,EAAC,CAAC,GAAEP,GAAE,IAAI,eAAc,EAAC,MAAK,wBAAuB,MAAKC,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAED,GAAE,IAAI,eAAc,EAAC,MAAK,wBAAuB,MAAKO,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEP,GAAE,IAAI,SAAQ,EAAC,MAAK,kBAAiB,MAAKM,IAAE,qBAAoB,CAAC,SAAS,EAAC,CAAC,GAAEN,GAAE,IAAI,QAAO,EAAC,MAAK,iBAAgB,MAAKK,IAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEL,GAAE,IAAI,QAAO,EAAC,MAAK,iBAAgB,MAAKC,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAED,GAAE,IAAI,SAAQ,EAAC,MAAK,kBAAiB,MAAKgB,IAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEhB,GAAE,IAAI,SAAQ,EAAC,MAAK,kBAAiB,MAAKG,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEH,GAAE,IAAI,uBAAsB,EAAC,MAAK,iCAAgC,MAAKR,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC,GAAEQ,GAAE,IAAI,WAAU,EAAC,MAAK,oBAAmB,MAAKM,KAAE,qBAAoB,CAAC,QAAQ,EAAC,CAAC;;;ACA/xL,IAAIW,KAAE,cAAc,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBC,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0DAA0D,CAAC,GAAEA,EAAC;AAAE,IAAME,MAAEF;;;ACA3O,IAAIG,MAAE,cAAcA,IAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,aAAY,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,iBAAiBC,IAAE;AAAC,WAAM,aAAWA,KAAEA,GAAE,MAAM,GAAG,QAAMA,GAAE,IAAE,MAAI,KAAK,cAAYA,GAAE,KAAG,MAAKA,OAAIA,GAAE,QAAM,MAAI,KAAK,aAAWA,GAAE,QAAM,KAAIA,GAAE,QAAM,OAAKA,GAAE,OAAK,MAAI,KAAK,cAAYA,GAAE,OAAK,MAAKA,GAAE,OAAK,MAAKA;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAM,aAAWA,KAAEA,GAAE,MAAM,GAAG,QAAMA,GAAE,IAAE,CAAC,KAAK,cAAYA,GAAE,KAAG,MAAKA,OAAIA,GAAE,OAAK,CAAC,KAAK,cAAYA,GAAE,QAAM,KAAIA,GAAE,QAAM,MAAKA;AAAA,EAAE;AAAC;AAAE,EAAE,CAACC,GAAE,EAAC,eAAc,YAAW,CAAC,CAAC,GAAEF,IAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,aAAY,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,wDAAwD,CAAC,GAAEA,GAAC;AAAE,IAAMG,MAAEH;;;ACAvmB,IAAII,KAAE,cAAcC,IAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAU;AAAC;AAAE,EAAE,CAACC,GAAE,EAAC,eAAc,WAAU,CAAC,CAAC,GAAEF,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,wDAAwD,CAAC,GAAEA,EAAC;AAAE,IAAMG,MAAEH;;;ACAqC,SAASI,IAAEC,IAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,GAAEC,IAAE,GAAEC,IAAC,IAAEH;AAAE,MAAGC,MAAE,GAAE;AAAC,WAAM,EAAC,GAAEF,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,EAAC;AAAA,EAAC;AAAC,MAAG,MAAIE,KAAE;AAAC,UAAMD,KAAEE,KAAEA,IAAED,MAAEE,MAAEA,KAAEC,MAAEF,KAAEC;AAAE,WAAM,EAAC,GAAEJ,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,IAAEC,KAAED,GAAE,CAAC,IAAEK,MAAEL,GAAE,CAAC,IAAEE,MAAEF,GAAE,EAAE,GAAE,GAAEA,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,IAAEC,KAAED,GAAE,CAAC,IAAEK,MAAEL,GAAE,CAAC,IAAEE,MAAEF,GAAE,EAAE,EAAC;AAAA,EAAC;AAAC,QAAMK,MAAEF,KAAEA,IAAEG,MAAEF,MAAEA,KAAEG,KAAEJ,KAAEC,KAAEI,MAAEH,MAAEF,IAAEJ,MAAEM,MAAED,KAAEK,MAAEN,KAAEG,KAAEI,MAAEN,MAAEE;AAAE,SAAM,EAAC,GAAEN,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,IAAEK,MAAEL,GAAE,CAAC,IAAEO,KAAEP,GAAE,CAAC,IAAEM,MAAEN,GAAE,EAAE,IAAEQ,MAAER,GAAE,EAAE,IAAED,MAAEC,GAAE,EAAE,IAAES,MAAET,GAAE,EAAE,IAAEU,MAAEV,GAAE,EAAE,GAAE,GAAEA,GAAE,CAAC,IAAEG,KAAEH,GAAE,CAAC,IAAEI,MAAEJ,GAAE,CAAC,IAAEK,MAAEL,GAAE,CAAC,IAAEO,KAAEP,GAAE,CAAC,IAAEM,MAAEN,GAAE,EAAE,IAAEQ,MAAER,GAAE,EAAE,IAAED,MAAEC,GAAE,EAAE,IAAES,MAAET,GAAE,EAAE,IAAEU,MAAEV,GAAE,EAAE,EAAC;AAAC;AAAC,SAASS,IAAET,IAAEC,IAAEC,KAAE;AAAC,QAAK,EAAC,MAAKC,IAAE,MAAKC,KAAE,MAAKC,KAAE,MAAKC,KAAE,kBAAiBC,GAAC,IAAEN;AAAE,MAAIQ,MAAE,CAAC;AAAE,MAAGP,MAAE,EAAE,CAAAO,IAAE,KAAK,EAAC,GAAEN,IAAE,GAAEG,IAAC,CAAC,GAAEG,IAAE,KAAK,EAAC,GAAEJ,KAAE,GAAEC,IAAC,CAAC,GAAEG,IAAE,KAAK,EAAC,GAAEN,IAAE,GAAEC,IAAC,CAAC,GAAEK,IAAE,KAAK,EAAC,GAAEJ,KAAE,GAAED,IAAC,CAAC;AAAA,OAAM;AAAC,QAAIJ,KAAE;AAAG,aAAQC,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAQ,IAAE,KAAK,EAAC,GAAEN,IAAE,GAAEC,OAAGE,MAAEF,OAAGH,MAAGD,KAAE,GAAE,CAAC,GAAES,IAAE,KAAK,EAAC,GAAEJ,KAAE,GAAED,OAAGE,MAAEF,OAAGH,MAAGD,KAAE,GAAE,CAAC;AAAE,IAAAA,KAAE;AAAE,aAAQC,KAAE,GAAEA,MAAGD,IAAEC,KAAI,CAAAQ,IAAE,KAAK,EAAC,GAAEN,MAAGE,MAAEF,MAAGF,KAAED,IAAE,GAAEI,IAAC,CAAC,GAAEK,IAAE,KAAK,EAAC,GAAEN,MAAGE,MAAEF,MAAGF,KAAED,IAAE,GAAEM,IAAC,CAAC;AAAA,EAAC;AAAC,EAAAG,MAAEA,IAAE,IAAK,CAAAR,OAAGF,IAAEC,IAAEC,IAAEC,GAAC,CAAE;AAAE,QAAMQ,MAAED,IAAE,IAAK,CAAAT,OAAGA,GAAE,CAAE,GAAEW,MAAEF,IAAE,IAAK,CAAAT,OAAGA,GAAE,CAAE;AAAE,SAAO,IAAIY,GAAE,EAAC,MAAK,KAAK,IAAI,MAAM,MAAKF,GAAC,GAAE,MAAK,KAAK,IAAI,MAAM,MAAKA,GAAC,GAAE,MAAK,KAAK,IAAI,MAAM,MAAKC,GAAC,GAAE,MAAK,KAAK,IAAI,MAAM,MAAKA,GAAC,GAAE,kBAAiBJ,GAAC,CAAC;AAAC;AAAC,SAASG,IAAEV,IAAE;AAAC,QAAK,CAACC,IAAEC,KAAEC,IAAEC,KAAEC,KAAEC,GAAC,IAAEN,IAAEO,KAAEJ,KAAEG,MAAED,MAAED,KAAEI,MAAEH,MAAED,MAAED,KAAEG;AAAE,SAAM,EAAED,MAAEH,MAAED,KAAEK,OAAGC,KAAGJ,KAAED,MAAED,KAAEG,OAAGI,KAAEF,MAAEC,IAAEH,MAAEI,KAAE,CAACH,MAAEE,IAAE,CAACJ,KAAEK,GAAC;AAAC;AAAC,IAAIG,MAAE,cAAcF,IAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,GAAE,KAAK,OAAK;AAAA,EAAY;AAAA,EAAC,wBAAwBT,IAAEC,IAAE;AAAC,UAAK,EAAC,QAAOC,KAAE,QAAOC,GAAC,IAAEF;AAAE,QAAG,EAACC,OAAA,gBAAAA,IAAG,WAAQ,EAACC,MAAA,gBAAAA,GAAG,WAAQD,IAAE,WAASC,GAAE,OAAO,QAAO;AAAK,UAAMC,MAAE,CAAC;AAAE,aAAQC,MAAE,GAAEA,MAAEH,IAAE,QAAOG,MAAI,CAAAD,IAAE,KAAKF,IAAEG,GAAC,CAAC,GAAED,IAAE,KAAKD,GAAEE,GAAC,CAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,yBAAyBJ,IAAEC,IAAEC,KAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,aAAQC,MAAE,GAAEA,OAAEL,MAAA,gBAAAA,GAAG,SAAOK,MAAI,CAAAA,MAAE,KAAG,IAAEF,GAAE,KAAKH,GAAEK,GAAC,CAAC,IAAED,IAAE,KAAKJ,GAAEK,GAAC,CAAC;AAAE,IAAAJ,GAAE,SAAOE,IAAEF,GAAE,SAAOG;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,QAAIJ,KAAE,KAAK,KAAK,qBAAqB;AAAE,UAAMC,KAAE,KAAK,KAAK,qBAAqB;AAAE,WAAM,CAACD,MAAGC,MAAG,KAAK,kBAAgB,MAAID,KAAEU,IAAET,EAAC,IAAGD;AAAA,EAAC;AAAA,EAAC,IAAI,oBAAoBA,IAAE;AAAC,SAAK,KAAK,uBAAsBA,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,UAAK,EAAC,eAAcC,KAAE,eAAcC,GAAC,IAAEF;AAAE,QAAG,EAACC,OAAA,gBAAAA,IAAG,WAAQ,EAACC,MAAA,gBAAAA,GAAG,WAAQD,IAAE,WAASC,GAAE,OAAO,QAAO;AAAK,UAAMC,MAAE,CAAC;AAAE,aAAQC,MAAE,GAAEA,MAAEH,IAAE,QAAOG,MAAI,CAAAD,IAAE,KAAKF,IAAEG,GAAC,CAAC,GAAED,IAAE,KAAKD,GAAEE,GAAC,CAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,yBAAyBJ,IAAEC,IAAEC,KAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,aAAQC,MAAE,GAAEA,OAAEL,MAAA,gBAAAA,GAAG,SAAOK,MAAI,CAAAA,MAAE,KAAG,IAAEF,GAAE,KAAKH,GAAEK,GAAC,CAAC,IAAED,IAAE,KAAKJ,GAAEK,GAAC,CAAC;AAAE,IAAAJ,GAAE,gBAAcE,IAAEF,GAAE,gBAAcG;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,kBAAgB;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAE;AAAC,QAAG,YAAUA,GAAE,MAAK;AAAC,YAAMC,KAAEF,IAAE,KAAK,qBAAoBC,IAAE,KAAK,eAAe;AAAE,aAAO,IAAI,EAAE,EAAC,GAAEC,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBD,GAAE,iBAAgB,CAAC;AAAA,IAAC;AAAC,WAAOS,IAAE,KAAK,qBAAoBT,IAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAG,YAAUA,GAAE,MAAK;AAAC,YAAMC,KAAEF,IAAE,KAAK,qBAAoBC,IAAE,KAAK,eAAe;AAAE,aAAO,IAAI,EAAE,EAAC,GAAEC,GAAE,GAAE,GAAEA,GAAE,GAAE,kBAAiBD,GAAE,iBAAgB,CAAC;AAAA,IAAC;AAAC,WAAOS,IAAE,KAAK,qBAAoBT,IAAE,KAAK,eAAe;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEW,IAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,uBAAsB,CAAC,UAAS,QAAQ,CAAC,CAAC,GAAEA,IAAE,WAAU,2BAA0B,IAAI,GAAE,EAAE,CAACV,GAAE,qBAAqB,CAAC,GAAEU,IAAE,WAAU,4BAA2B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,IAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,uBAAsB,CAAC,iBAAgB,eAAe,CAAC,CAAC,GAAEA,IAAE,WAAU,2BAA0B,IAAI,GAAE,EAAE,CAACV,GAAE,qBAAqB,CAAC,GAAEU,IAAE,WAAU,4BAA2B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,IAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAACT,GAAE,EAAC,iBAAgB,aAAY,CAAC,CAAC,GAAES,IAAE,WAAU,QAAO,MAAM,GAAEA,MAAE,EAAE,CAAC,EAAE,0DAA0D,CAAC,GAAEA,GAAC;AAAE,IAAME,MAAEF;;;ACAhxH,IAAMG,KAAE,EAAC,eAAcC,KAAE,eAAcC,KAAE,iBAAgBC,IAAC;AAA1D,IAA4DC,KAAE,OAAO,KAAKJ,EAAC;AAAE,SAASK,GAAEC,IAAE;AAAC,QAAMC,KAAED,MAAA,gBAAAA,GAAG;AAAK,SAAM,CAACA,MAAGF,GAAE,SAASG,EAAC;AAAC;AAAC,SAASC,IAAEF,IAAE;AAAC,QAAMC,KAAED,MAAA,gBAAAA,GAAG;AAAK,MAAG,CAACC,GAAE,QAAO;AAAK,QAAME,MAAET,GAAEM,MAAA,gBAAAA,GAAG,IAAI;AAAE,MAAGG,KAAE;AAAC,UAAMF,KAAE,IAAIE;AAAE,WAAOF,GAAE,KAAKD,EAAC,GAAEC;AAAA,EAAC;AAAC,SAAO;AAAI;", "names": ["e", "p", "e", "t", "p", "p", "t", "s", "r", "i", "n", "o", "e", "u", "l", "d", "c", "m", "p", "l", "t", "s", "e", "r", "o", "c", "e", "t", "n", "r", "l", "o", "s", "a", "f", "i", "a", "s", "n", "i", "l", "j", "q", "M", "c", "u", "f", "w", "m", "g", "y", "k", "A", "p", "x", "o", "U", "B", "t", "r", "h", "e", "d", "p", "a", "o", "i", "c", "u", "l", "e", "s", "t", "r", "n", "i", "a", "m", "i", "u", "p", "o", "r", "e", "a", "s", "d", "t", "o", "e", "n", "r", "s", "l", "i", "a", "c", "c", "l", "o", "r", "s", "t", "e", "m", "d", "u", "a", "c", "p", "r", "s", "n", "u", "l", "t", "r", "e", "s", "n", "p", "a", "g", "D", "h", "u", "s", "p", "n", "o", "t", "e", "l", "r", "s", "i", "a", "c", "h", "f", "p", "u", "g", "x", "m", "w", "p", "c", "o", "t", "n", "a", "s", "u", "p", "l", "t", "e", "o", "s", "a", "n", "i", "u", "c", "i", "n", "p", "s", "c", "c", "l", "t", "e", "n", "r", "o", "s", "i", "a", "p", "a", "i", "p", "r", "s", "c", "a", "l", "t", "h", "D", "c", "f", "e", "n", "r", "o", "s", "u", "m", "i", "g", "p", "y", "d", "M", "w", "b", "x", "A", "k", "F", "v", "_", "m", "l", "t", "s", "o", "e", "r", "n", "i", "c", "u", "p", "n", "a", "p", "o", "t", "e", "r", "i", "p", "l", "t", "e", "s", "o", "r", "u", "n", "a", "i", "k", "m", "o", "i", "p", "r", "t", "s", "n", "c", "l", "t", "s", "r", "e", "n", "o", "i", "a", "n", "a", "p", "r", "h", "l", "t", "e", "n", "o", "a", "r", "u", "p", "s", "i", "g", "c", "f", "i", "c", "s", "a", "p", "o", "n", "p", "c", "l", "e", "s", "r", "o", "i", "u", "a", "t", "n", "c", "n", "p", "t", "s", "r", "o", "i", "l", "s", "t", "r", "o", "p", "n", "n", "p", "s", "c", "t", "e", "o", "r", "u", "m", "l", "t", "s", "e", "o", "r", "u", "x", "n", "p", "a", "y", "M", "i", "A", "h", "t", "r", "e", "C", "n", "D", "v", "S", "N", "b", "o", "s", "A", "a", "i", "u", "c", "m", "l", "p", "j", "R", "w", "V", "k", "T", "x", "B", "f", "t", "r", "a", "a", "r", "o", "c", "e", "a", "o", "p", "l", "e", "r", "o", "t", "s", "n", "i", "f", "p", "a", "c", "u", "w", "m", "o", "c", "p", "m", "e", "f", "r", "t", "i", "n"]}