import {
  n as n6
} from "./chunk-FAMLZKHJ.js";
import {
  A as A3,
  B,
  V,
  Y,
  _ as _2,
  p2 as p,
  q
} from "./chunk-6IU6DQRF.js";
import {
  a as a3,
  f2 as f
} from "./chunk-YELYN22P.js";
import {
  l
} from "./chunk-JJ3NE6DY.js";
import {
  o as o3,
  r as r7,
  s
} from "./chunk-PHEIXDVR.js";
import {
  b,
  j as j4,
  n as n5
} from "./chunk-5S4W3ME5.js";
import {
  A as A2,
  L,
  a as a2,
  j as j3,
  k,
  m as m3,
  o as o2,
  v as v2,
  w as w2
} from "./chunk-SROTSYJS.js";
import {
  e as e4,
  n as n4,
  r as r6
} from "./chunk-FOE4ICAJ.js";
import {
  e as e3,
  n as n3,
  r as r5
} from "./chunk-NOZFLZZL.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  m
} from "./chunk-V5GIYRXW.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import {
  m as m2
} from "./chunk-EIGTETCG.js";
import {
  A,
  G,
  H,
  P,
  _,
  g,
  j,
  o,
  r as r4,
  u,
  x,
  z
} from "./chunk-MQAXMQFG.js";
import {
  e as e2,
  n as n2,
  r as r3
} from "./chunk-36FLFRUE.js";
import {
  E,
  a,
  j as j2
} from "./chunk-RQXGVG3K.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import {
  F,
  i
} from "./chunk-GZGAQUSK.js";
import {
  e,
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/interactive/coordinateHelper.js
var R2;
!function(e8) {
  e8[e8.Z = 0] = "Z", e8[e8.M = 1] = "M";
}(R2 || (R2 = {}));
var x2 = class {
  constructor(e8) {
    this.spatialReference = e8;
  }
  createVector() {
    return this._tag(n4());
  }
  pointToVector(e8) {
    return this._tag(r6(e8.x, e8.y));
  }
  arrayToVector(e8) {
    return this._tag(r6(e8[0], e8[1]));
  }
  vectorToArray(e8) {
    return [e8[0], e8[1]];
  }
  pointToArray(e8) {
    return [e8.x, e8.y];
  }
  vectorToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = void 0, t5.m = void 0, t5.spatialReference = this.spatialReference, t5;
  }
  arrayToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = void 0, t5.m = void 0, t5.spatialReference = this.spatialReference, t5;
  }
  vectorToDehydratedPoint(e8, t5 = { x: void 0, y: void 0, z: void 0, m: void 0, hasZ: void 0, hasM: void 0, spatialReference: void 0, type: "point" }) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = void 0, t5.m = void 0, t5.hasZ = false, t5.hasM = false, t5.spatialReference = this.spatialReference, t5;
  }
  lerp(t5, r11, a4, n7) {
    return A2(n7, t5, r11, a4);
  }
  addDelta(e8, t5, r11) {
    e8[0] += t5, e8[1] += r11;
  }
  distance(e8, r11) {
    return m3(e8, r11);
  }
  getZ(e8, t5) {
    return t5;
  }
  hasZ() {
    return false;
  }
  getM(e8, t5) {
    return t5;
  }
  hasM() {
    return false;
  }
  clone(e8) {
    return this._tag(e4(e8));
  }
  copy(e8, t5) {
    return a2(t5, e8);
  }
  fromXYZ(e8) {
    return this._tag(r6(e8[0], e8[1]));
  }
  toXYZ(e8, t5 = n2()) {
    return o(t5, e8[0], e8[1], 0);
  }
  pointToXYZ(e8, t5 = n2()) {
    return o(t5, e8.x, e8.y, 0);
  }
  equals(e8, t5) {
    return k(e8, t5);
  }
  _tag(e8) {
    return e8;
  }
};
var M = class {
  constructor(e8, t5) {
    this._valueType = e8, this.spatialReference = t5;
  }
  createVector() {
    return this._tag(n2());
  }
  pointToVector(e8) {
    return this._tag(r3(e8.x, e8.y, this._valueType === R2.Z ? e8.z : e8.m));
  }
  arrayToVector(e8) {
    return this._tag(r3(e8[0], e8[1], e8[2] || 0));
  }
  vectorToArray(e8) {
    return [e8[0], e8[1], e8[2]];
  }
  pointToArray(e8) {
    return this._valueType === R2.Z ? [e8.x, e8.y, e8.z] : [e8.x, e8.y, e8.m];
  }
  vectorToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = this._valueType === R2.Z ? e8[2] : void 0, t5.m = this._valueType === R2.M ? e8[2] : void 0, t5.spatialReference = this.spatialReference, t5;
  }
  arrayToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = this._valueType === R2.Z ? e8[2] : void 0, t5.m = this._valueType === R2.M ? e8[2] : void 0, t5.spatialReference = this.spatialReference, t5;
  }
  vectorToDehydratedPoint(e8, t5 = { x: void 0, y: void 0, z: void 0, m: void 0, hasZ: void 0, hasM: void 0, spatialReference: void 0, type: "point" }) {
    const r11 = this._valueType === R2.Z, a4 = this._valueType === R2.M;
    return t5.x = e8[0], t5.y = e8[1], t5.z = r11 ? e8[2] : void 0, t5.m = a4 ? e8[2] : void 0, t5.hasZ = r11, t5.hasM = a4, t5.spatialReference = this.spatialReference, t5;
  }
  lerp(e8, t5, r11, a4) {
    return A(a4, e8, t5, r11);
  }
  addDelta(e8, t5, r11, a4) {
    e8[0] += t5, e8[1] += r11, this._valueType === R2.Z && (e8[2] += a4);
  }
  distance(e8, r11) {
    return this._valueType === R2.Z ? x(e8, r11) : m3(e8, r11);
  }
  getZ(e8, t5) {
    return this._valueType === R2.Z ? e8[2] : t5;
  }
  hasZ() {
    return this._valueType === R2.Z;
  }
  getM(e8, t5) {
    return this._valueType === R2.M ? e8[2] : t5;
  }
  hasM() {
    return this._valueType === R2.M;
  }
  clone(e8) {
    return this._tag(e2(e8));
  }
  copy(e8, t5) {
    return r4(t5, e8);
  }
  fromXYZ(e8, t5 = 0, r11 = 0) {
    return this._tag(r3(e8[0], e8[1], this._valueType === R2.Z ? e8.length > 2 ? e8[2] : t5 : r11));
  }
  toXYZ(e8, t5 = n2()) {
    return o(t5, e8[0], e8[1], this._valueType === R2.Z ? e8[2] : 0);
  }
  pointToXYZ(e8, t5 = n2()) {
    return o(t5, e8.x, e8.y, this._valueType === R2.Z ? e8.z ?? 0 : 0);
  }
  equals(e8, t5) {
    return G(e8, t5);
  }
  _tag(e8) {
    return e8;
  }
};
var z2 = class {
  constructor(e8) {
    this.spatialReference = e8;
  }
  createVector() {
    return this._tag(n3());
  }
  pointToVector(e8) {
    return this._tag(r5(e8.x, e8.y, e8.z, e8.m));
  }
  arrayToVector(e8) {
    return this._tag(r5(e8[0], e8[1], e8[2] || 0, e8[3] || 0));
  }
  vectorToArray(e8) {
    return [e8[0], e8[1], e8[2], e8[3]];
  }
  pointToArray(e8) {
    return [e8.x, e8.y, e8.z, e8.m];
  }
  vectorToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = e8[2], t5.m = e8[3], t5.spatialReference = this.spatialReference, t5;
  }
  arrayToPoint(e8, t5 = new w()) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = e8[2], t5.m = e8[3], t5.spatialReference = this.spatialReference, t5;
  }
  vectorToDehydratedPoint(e8, t5 = { x: void 0, y: void 0, z: void 0, m: void 0, hasZ: void 0, hasM: void 0, spatialReference: void 0, type: "point" }) {
    return t5.x = e8[0], t5.y = e8[1], t5.z = e8[2], t5.m = e8[3], t5.hasZ = true, t5.hasM = true, t5.spatialReference = this.spatialReference, t5;
  }
  lerp(e8, t5, r11, a4) {
    return j2(a4, e8, t5, r11);
  }
  addDelta(e8, t5, r11, a4) {
    e8[0] += t5, e8[1] += r11, e8[2] += a4;
  }
  distance(e8, t5) {
    return x(e8, t5);
  }
  getZ(e8) {
    return e8[2];
  }
  hasZ() {
    return true;
  }
  getM(e8) {
    return e8[3];
  }
  hasM() {
    return true;
  }
  clone(e8) {
    return this._tag(e3(e8));
  }
  copy(e8, t5) {
    return a(t5, e8);
  }
  fromXYZ(e8, t5 = 0, r11 = 0) {
    return this._tag(r5(e8[0], e8[1], e8.length > 2 ? e8[2] : t5, r11));
  }
  toXYZ(e8, t5 = n2()) {
    return o(t5, e8[0], e8[1], e8[2]);
  }
  pointToXYZ(e8, t5 = n2()) {
    return o(t5, e8.x, e8.y, e8.z ?? 0);
  }
  equals(e8, t5) {
    return E(e8, t5);
  }
  _tag(e8) {
    return e8;
  }
};
function w3(e8, t5, r11) {
  return e8 && t5 ? new z2(r11) : t5 ? new M(R2.M, r11) : e8 ? new M(R2.Z, r11) : new x2(r11);
}

// node_modules/@arcgis/core/views/interactive/editGeometry/unnormalizationHelper.js
function e5(o4, r11) {
  if (!r11.supported) return;
  let e8 = 1 / 0, p4 = -1 / 0;
  const n7 = r11.upperBoundX - r11.lowerBoundX;
  o4.forEach((o5) => {
    let u3 = o5.pos[0];
    for (; u3 < r11.lowerBoundX; ) u3 += n7;
    for (; u3 > r11.upperBoundX; ) u3 -= n7;
    e8 = Math.min(e8, u3), p4 = Math.max(p4, u3), o5.pos[0] = u3;
  });
  const u2 = p4 - e8;
  n7 - u2 < u2 && o4.forEach((o5) => {
    o5.pos[0] < 0 && (o5.pos[0] += n7);
  });
}
function p2(e8, p4) {
  const n7 = R(e8);
  return p4 === l.Global && n7 ? { supported: true, lowerBoundX: n7.valid[0], upperBoundX: n7.valid[1] } : { supported: false, lowerBoundX: null, upperBoundX: null };
}

// node_modules/@arcgis/core/views/interactive/editGeometry/EditGeometry.js
var c = class {
  constructor(e8) {
    this.component = e8, this.leftEdge = null, this.rightEdge = null, this.type = "vertex", this.index = null;
  }
  get pos() {
    return this._pos;
  }
  set pos(e8) {
    this._pos = e8, this.component.unnormalizeVertexPositions();
  }
};
var l2 = class {
  constructor(e8, t5, s3) {
    this.component = e8, this.leftVertex = t5, this.rightVertex = s3, this.type = "edge", t5.rightEdge = this, s3.leftEdge = this;
  }
};
var p3 = class {
  constructor(e8, t5) {
    this._spatialReference = e8, this._viewingMode = t5, this.vertices = [], this.edges = [], this.index = null;
  }
  unnormalizeVertexPositions() {
    this.vertices.length <= 1 || e5(this.vertices, p2(this._spatialReference, this._viewingMode));
  }
  updateVertexIndex(e8, t5) {
    if (0 === this.vertices.length) return;
    const s3 = this.vertices[0];
    let i4 = null, r11 = e8, n7 = t5;
    do {
      i4 = r11, i4.index = n7++, r11 = i4.rightEdge ? i4.rightEdge.rightVertex : null;
    } while (null != r11 && r11 !== s3);
    i4.leftEdge && i4 !== this.vertices[this.vertices.length - 1] && this.swapVertices(this.vertices.indexOf(i4), this.vertices.length - 1);
  }
  getFirstVertex() {
    return 0 === this.vertices.length ? null : this.vertices[0];
  }
  getLastVertex() {
    return 0 === this.vertices.length ? null : this.vertices[this.vertices.length - 1];
  }
  isClosed() {
    return this.vertices.length > 2 && null !== this.vertices[0].leftEdge;
  }
  swapVertices(e8, t5) {
    const s3 = this.vertices[e8];
    this.vertices[e8] = this.vertices[t5], this.vertices[t5] = s3;
  }
  iterateVertices(e8) {
    if (0 === this.vertices.length) return;
    const t5 = this.vertices[0];
    let i4 = t5;
    do {
      e8(i4, i4.index), i4 = r(i4.rightEdge) ? i4.rightEdge.rightVertex : null;
    } while (i4 !== t5 && null != i4);
  }
};
var g2 = class _g extends n {
  constructor(e8, t5) {
    super(), this.type = e8, this.coordinateHelper = t5, this._geometry = null, this._dirty = true, this.components = [];
  }
  get geometry() {
    if (this._dirty) {
      switch (this.type) {
        case "point":
          this._geometry = this._toPoint();
          break;
        case "polyline":
          this._geometry = this._toPolyline();
          break;
        case "polygon":
          this._geometry = this._toPolygon();
      }
      this._dirty = false;
    }
    return this._geometry;
  }
  get spatialReference() {
    return this.coordinateHelper.spatialReference;
  }
  notifyChanges(e8) {
    this._dirty = true, this.emit("change", e8);
  }
  _toPoint() {
    return 0 === this.components.length || 0 === this.components[0].vertices.length ? null : this.coordinateHelper.vectorToPoint(this.components[0].vertices[0].pos);
  }
  _toPolyline() {
    const e8 = [], t5 = this.coordinateHelper.vectorToArray;
    for (const s3 of this.components) {
      if (s3.vertices.length < 1) continue;
      const i4 = [];
      let r11 = s3.vertices.find((e9) => null == e9.leftEdge);
      const n7 = r11;
      do {
        i4.push(t5(r11.pos)), r11 = r11.rightEdge ? r11.rightEdge.rightVertex : null;
      } while (r11 && r11 !== n7);
      e8.push(i4);
    }
    return new m({ paths: e8, spatialReference: this.spatialReference, hasZ: this.coordinateHelper.hasZ(), hasM: this.coordinateHelper.hasM() });
  }
  _toPolygon() {
    const e8 = [], t5 = this.coordinateHelper.vectorToArray;
    for (const i4 of this.components) {
      if (i4.vertices.length < 1) continue;
      const r11 = [], n7 = i4.vertices[0];
      let o4 = n7;
      const h = o4;
      do {
        r11.push(t5(o4.pos)), o4 = r(o4.rightEdge) ? o4.rightEdge.rightVertex : null;
      } while (o4 && o4 !== h);
      i4.isClosed() && r11.push(t5(n7.pos)), e8.push(r11);
    }
    return new v({ rings: e8, spatialReference: this.spatialReference, hasZ: this.coordinateHelper.hasZ(), hasM: this.coordinateHelper.hasM() });
  }
  static fromGeometry(t5, s3) {
    const i4 = t5.spatialReference, r11 = w3(t5.hasZ, t5.hasM, i4), o4 = new _g(t5.type, r11);
    switch (t5.type) {
      case "polygon": {
        const n7 = t5.rings;
        for (let t6 = 0; t6 < n7.length; ++t6) {
          const h = n7[t6], g3 = new p3(i4, s3);
          g3.index = t6;
          const a4 = h.length > 2 && i(h[0], h[h.length - 1]), d2 = a4 ? h.length - 1 : h.length;
          for (let e8 = 0; e8 < d2; ++e8) {
            const t7 = r11.arrayToVector(h[e8]), s4 = new c(g3);
            g3.vertices.push(s4), s4.pos = t7, s4.index = e8;
          }
          const u2 = g3.vertices.length - 1;
          for (let e8 = 0; e8 < u2; ++e8) {
            const t7 = g3.vertices[e8], s4 = g3.vertices[e8 + 1], i5 = new l2(g3, t7, s4);
            g3.edges.push(i5);
          }
          if (a4) {
            const e8 = new l2(g3, g3.vertices[g3.vertices.length - 1], g3.vertices[0]);
            g3.edges.push(e8);
          }
          o4.components.push(g3);
        }
        break;
      }
      case "polyline": {
        const e8 = t5.paths;
        for (let t6 = 0; t6 < e8.length; ++t6) {
          const n7 = e8[t6], h = new p3(i4, s3);
          h.index = t6;
          const g3 = n7.length;
          for (let e9 = 0; e9 < g3; ++e9) {
            const t7 = r11.arrayToVector(n7[e9]), s4 = new c(h);
            h.vertices.push(s4), s4.pos = t7, s4.index = e9;
          }
          const a4 = h.vertices.length - 1;
          for (let e9 = 0; e9 < a4; ++e9) {
            const t7 = h.vertices[e9], s4 = h.vertices[e9 + 1], i5 = new l2(h, t7, s4);
            h.edges.push(i5);
          }
          o4.components.push(h);
        }
        break;
      }
      case "point": {
        const e8 = new p3(i4, s3);
        e8.index = 0;
        const n7 = new c(e8);
        n7.index = 0, n7.pos = r11.pointToVector(t5), e8.vertices.push(n7), o4.components.push(e8);
        break;
      }
    }
    return o4;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/interfaces.js
var E2;
!function(E3) {
  E3[E3.NEW_STEP = 0] = "NEW_STEP", E3[E3.ACCUMULATE_STEPS = 1] = "ACCUMULATE_STEPS";
}(E2 || (E2 = {}));

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/AppendVertex.js
var d = class {
  constructor(e8, t5, i4) {
    this._editGeometry = e8, this._component = t5, this._pos = i4, this._addedVertex = null, this._originalEdge = null, this._left = null, this._right = null;
  }
  apply() {
    let d2 = "redo";
    t(this._addedVertex) && (d2 = "apply", this._addedVertex = new c(this._component));
    const h = this._component.getLastVertex();
    if (t(h)) this._component.vertices.push(this._addedVertex), this._addedVertex.pos = this._pos, this._addedVertex.index = 0;
    else {
      let i4 = null;
      h.rightEdge && (this._originalEdge = h.rightEdge, i4 = this._originalEdge.rightVertex, this._component.edges.splice(this._component.edges.indexOf(this._originalEdge), 1)), this._component.vertices.push(this._addedVertex), this._addedVertex.pos = this._pos, t(this._left) && (this._left = new l2(this._component, h, this._addedVertex)), this._component.edges.push(this._left), h.rightEdge = this._left, r(this._originalEdge) && r(i4) && (t(this._right) && (this._right = new l2(this._component, this._addedVertex, i4)), this._component.edges.push(this._right), i4.leftEdge = this._right), this._component.updateVertexIndex(this._addedVertex, h.index + 1);
    }
    this._editGeometry.notifyChanges({ operation: d2, addedVertices: [this._addedVertex] });
  }
  undo() {
    t(this._addedVertex) || (this._component.vertices.splice(this._component.vertices.indexOf(this._addedVertex), 1), r(this._left) && (this._component.edges.splice(this._component.edges.indexOf(this._left), 1), this._left.leftVertex.rightEdge = null), r(this._right) && (this._component.edges.splice(this._component.edges.indexOf(this._right), 1), this._right.rightVertex.leftEdge = null), r(this._originalEdge) && (this._component.edges.push(this._originalEdge), this._originalEdge.leftVertex.rightEdge = this._originalEdge, this._originalEdge.rightVertex.leftEdge = this._originalEdge), r(this._left) ? this._component.updateVertexIndex(this._left.leftVertex, this._left.leftVertex.index) : this._component.updateVertexIndex(this._addedVertex, 0), this._editGeometry.notifyChanges({ operation: "undo", removedVertices: [this._addedVertex] }));
  }
  accumulate() {
    return false;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/UpdateVertices.js
var t2 = class _t {
  constructor(t5, e8, i4) {
    this._editGeometry = t5, this._vertices = e8, this.operation = i4, this._undone = false;
  }
  apply() {
    this._vertices.forEach((t5) => this.operation.apply(t5)), this._editGeometry.components.forEach((t5) => t5.unnormalizeVertexPositions()), this._editGeometry.notifyChanges({ operation: this._undone ? "redo" : "apply", updatedVertices: this._vertices });
  }
  undo() {
    this._vertices.forEach((t5) => this.operation.undo(t5)), this._editGeometry.notifyChanges({ operation: "undo", updatedVertices: this._vertices }), this._undone = true;
  }
  canAccumulate(t5) {
    if (this._undone || t5._vertices.length !== this._vertices.length) return false;
    for (let e8 = 0; e8 < t5._vertices.length; ++e8) if (t5._vertices[e8] !== this._vertices[e8]) return false;
    return this.operation.canAccumulate(t5.operation);
  }
  accumulate(e8) {
    return !!(e8 instanceof _t && this.canAccumulate(e8)) && (this._vertices.forEach((t5) => this.operation.accumulate(t5, e8.operation)), this.operation.accumulateParams(e8.operation), this._editGeometry.components.forEach((t5) => t5.unnormalizeVertexPositions()), this._editGeometry.notifyChanges({ operation: "apply", updatedVertices: this._vertices }), true);
  }
};
var e6;
!function(t5) {
  t5[t5.CUMULATIVE = 0] = "CUMULATIVE", t5[t5.REPLACE = 1] = "REPLACE";
}(e6 || (e6 = {}));

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/RemoveVertices.js
var r8 = class {
  constructor(e8, t5, r11 = 0) {
    this._editGeometry = e8, this._vertices = t5, this._minNumberOfVertices = r11, this.removedVertices = null;
  }
  apply() {
    let t5 = "redo";
    if (null == this.removedVertices) {
      const r11 = this.removedVertices = [];
      this._vertices.forEach((t6) => {
        const i4 = this._removeVertex(t6);
        r(i4) && r11.push(i4);
      }), t5 = "apply";
    } else this.removedVertices.forEach((e8) => {
      this._removeVertex(e8.removedVertex);
    });
    this._editGeometry.notifyChanges({ operation: t5, removedVertices: this._vertices });
  }
  undo() {
    var _a;
    (_a = this.removedVertices) == null ? void 0 : _a.forEach((e8) => {
      this._undoRemoveVertex(e8);
    }), this._editGeometry.notifyChanges({ operation: "undo", addedVertices: this._vertices });
  }
  accumulate() {
    return false;
  }
  _removeVertex(e8) {
    const r11 = e8.component;
    if (r11.vertices.length <= this._minNumberOfVertices) return null;
    const i4 = { removedVertex: e8, createdEdge: null }, s3 = e8.leftEdge, d2 = e8.rightEdge;
    return r11.vertices.splice(r11.vertices.indexOf(e8), 1), s3 && (r11.edges.splice(r11.edges.indexOf(s3), 1), s3.leftVertex.rightEdge = null), d2 && (r11.edges.splice(r11.edges.indexOf(d2), 1), d2.rightVertex.leftEdge = null), 0 === e8.index && d2 && this._vertices.length > 0 && r11.swapVertices(r11.vertices.indexOf(d2.rightVertex), 0), s3 && d2 && (i4.createdEdge = new l2(r11, s3.leftVertex, d2.rightVertex), r11.edges.push(i4.createdEdge)), d2 && r11.updateVertexIndex(d2.rightVertex, d2.rightVertex.index - 1), i4;
  }
  _undoRemoveVertex(e8) {
    const t5 = e8.removedVertex, r11 = e8.removedVertex.component, i4 = t5.leftEdge, s3 = t5.rightEdge;
    e8.createdEdge && r11.edges.splice(r11.edges.indexOf(e8.createdEdge), 1), r11.vertices.push(t5), i4 && (r11.edges.push(i4), i4.leftVertex.rightEdge = i4), s3 && (r11.edges.push(s3), s3.rightVertex.leftEdge = s3), r11.updateVertexIndex(t5, t5.index);
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/SplitEdge.js
var r9 = class {
  constructor(e8, t5, i4) {
    this._editGeometry = e8, this._edge = t5, this._t = i4, this.createdVertex = null, this._left = null, this._right = null;
  }
  apply() {
    let r11 = "redo";
    const s3 = this._edge, d2 = s3.component, h = s3.leftVertex, g3 = s3.rightVertex;
    d2.edges.splice(d2.edges.indexOf(s3), 1), t(this.createdVertex) && (r11 = "apply", this.createdVertex = new c(s3.component)), d2.vertices.push(this.createdVertex), this.createdVertex.pos = this._editGeometry.coordinateHelper.lerp(s3.leftVertex.pos, s3.rightVertex.pos, this._t, this._editGeometry.coordinateHelper.createVector()), t(this._left) && (this._left = new l2(d2, h, this.createdVertex)), this._left.leftVertex.leftEdge ? d2.edges.push(this._left) : d2.edges.unshift(this._left), h.rightEdge = this._left, t(this._right) && (this._right = new l2(d2, this.createdVertex, g3)), d2.edges.push(this._right), g3.leftEdge = this._right, d2.updateVertexIndex(this.createdVertex, h.index + 1), this._editGeometry.notifyChanges({ operation: r11, addedVertices: [this.createdVertex] });
  }
  undo() {
    if (t(this.createdVertex) || t(this._left) || t(this._right)) return null;
    const t5 = this._edge, i4 = t5.component, r11 = this.createdVertex.leftEdge, s3 = this.createdVertex.rightEdge, d2 = r11 == null ? void 0 : r11.leftVertex, h = s3 == null ? void 0 : s3.rightVertex;
    i4.vertices.splice(i4.vertices.indexOf(this.createdVertex), 1), i4.edges.splice(i4.edges.indexOf(this._left), 1), i4.edges.splice(i4.edges.indexOf(this._right), 1), this._edge.leftVertex.leftEdge ? i4.edges.push(this._edge) : i4.edges.unshift(this._edge), d2 && (d2.rightEdge = t5), h && (h.leftEdge = t5), d2 && i4.updateVertexIndex(d2, d2.index), this._editGeometry.notifyChanges({ operation: "undo", removedVertices: [this.createdVertex] });
  }
  accumulate() {
    return false;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/SetVertexPosition.js
var i2 = class _i {
  constructor(t5, e8, i4) {
    this._editGeometry = t5, this._vertex = e8, this._pos = i4;
  }
  apply() {
    const e8 = t(this._originalPosition);
    e8 && (this._originalPosition = this._vertex.pos), this._apply(e8 ? "apply" : "redo");
  }
  undo() {
    this._vertex.pos = e(this._originalPosition), this._editGeometry.notifyChanges({ operation: "undo", updatedVertices: [this._vertex] });
  }
  accumulate(t5) {
    return t5 instanceof _i && t5._vertex === this._vertex && (this._pos = t5._pos, this._apply("apply"), true);
  }
  _apply(t5) {
    this._vertex.pos = this._pos, this._editGeometry.components.forEach((t6) => t6.unnormalizeVertexPositions()), this._editGeometry.notifyChanges({ operation: t5, updatedVertices: [this._vertex] });
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/CloseComponent.js
var i3 = class {
  constructor(e8, t5) {
    this._editGeometry = e8, this._component = t5, this._createdEdge = null;
  }
  apply() {
    let e8 = "redo";
    if (t(this._createdEdge)) {
      e8 = "apply";
      const i4 = this._component.getFirstVertex(), o4 = this._component.getLastVertex();
      if (this._component.isClosed() || this._component.vertices.length < 3 || t(i4) || t(o4)) return;
      this._createdEdge = new l2(this._component, o4, i4);
    }
    this._createdEdge.leftVertex.rightEdge = this._createdEdge, this._createdEdge.rightVertex.leftEdge = this._createdEdge, this._component.edges.push(this._createdEdge), this._editGeometry.notifyChanges({ operation: e8 });
  }
  undo() {
    t(this._createdEdge) || (F(this._component.edges, this._createdEdge), this._createdEdge.leftVertex.rightEdge = null, this._createdEdge.rightVertex.leftEdge = null, this._editGeometry.notifyChanges({ operation: "undo" }));
  }
  accumulate() {
    return false;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/MoveVertex.js
var t3 = class _t {
  constructor(t5, s3, d2, h) {
    this._helper = t5, this.dx = s3, this.dy = d2, this.dz = h;
  }
  _move(t5, s3, d2, h) {
    this._helper.addDelta(t5.pos, s3, d2, h);
  }
  apply(t5) {
    this._move(t5, this.dx, this.dy, this.dz);
  }
  undo(t5) {
    this._move(t5, -this.dx, -this.dy, -this.dz);
  }
  canAccumulate(s3) {
    return s3 instanceof _t;
  }
  accumulate(t5, s3) {
    this._move(t5, s3.dx, s3.dy, s3.dz);
  }
  accumulateParams(t5) {
    this.dx += t5.dx, this.dy += t5.dy, this.dz += t5.dz;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/OffsetEdgeVertex.js
var P2 = class _P {
  get plane() {
    return this._plane;
  }
  get requiresSplitEdgeLeft() {
    return !this._left.isOriginalDirection;
  }
  get requiresSplitEdgeRight() {
    return !this._right.isOriginalDirection;
  }
  get edgeDirection() {
    return this._edgeDirection;
  }
  constructor(t5, i4, e8, s3 = 0, n7 = j5.IMMEDIATE) {
    this._helper = t5, this._planeType = i4, this._edge = e8, this.distance = s3, this._plane = p(), this._offsetPlane = p(), this._minDistance = -1 / 0, this._maxDistance = 1 / 0, this._selectedArrow = 1, n7 === j5.IMMEDIATE && this._initialize();
  }
  _initialize() {
    this._initializeNeighbors(), this._initializePlane(), this._initializeDistanceConstraints();
  }
  _initializeNeighbors() {
    var _a, _b, _c, _d;
    const t5 = this._toXYZ(this._edge.leftVertex.pos), i4 = this._toXYZ((_b = (_a = this._edge.leftVertex.leftEdge) == null ? void 0 : _a.leftVertex) == null ? void 0 : _b.pos), e8 = this._toXYZ(this._edge.rightVertex.pos), s3 = this._toXYZ((_d = (_c = this._edge.rightVertex.rightEdge) == null ? void 0 : _c.rightVertex) == null ? void 0 : _d.pos);
    this._edgeDirection = H(n2(), t5, e8), this._left = this._computeNeighbor(t5, i4, this._edgeDirection), this._right = this._computeNeighbor(e8, s3, this._edgeDirection);
  }
  _toXYZ(t5) {
    return r(t5) ? this._helper.toXYZ(t5) : null;
  }
  _pointToXYZ(t5) {
    return this._toXYZ(this._helper.pointToVector(t5));
  }
  _computeNeighbor(t5, i4, s3) {
    if (t(i4)) return { start: t5, end: i4, direction: r3(-s3[1], s3[0], 0), isOriginalDirection: true };
    const n7 = H(n2(), t5, i4), r11 = !this._passesBisectingAngleThreshold(n7, s3);
    return { start: t5, end: i4, direction: r11 ? this._bisectVectorsPerpendicular(s3, n7) : n7, isOriginalDirection: !r11 };
  }
  _passesBisectingAngleThreshold(t5, i4) {
    const e8 = Math.abs(a3(i4, t5));
    return e8 >= T && e8 <= Math.PI - T;
  }
  _bisectVectorsPerpendicular(t5, i4) {
    const e8 = P(t5, i4) < 0 ? t5 : j(n2(), t5), s3 = Math.abs(P(e8, i4));
    if (!(s3 < Z || s3 > 1 - Z)) return this._bisectDirection(e8, i4);
    const n7 = _(n2(), e8, [0, 0, 1]);
    return z(n7, n7);
  }
  _bisectDirection(t5, i4) {
    const e8 = u(n2(), t5, i4);
    return z(e8, e8);
  }
  _initializePlane() {
    const t5 = this._computeNormalDirection(this._left), i4 = this._computeNormalDirection(this._right);
    P(t5, i4) < 0 && j(i4, i4), _2(this._left.start, this._bisectDirection(t5, i4), this._plane);
  }
  _computeNormalDirection(t5) {
    const i4 = _(n2(), t5.direction, this._edgeDirection);
    z(i4, i4);
    const e8 = _(n2(), this._edgeDirection, i4);
    return this._planeType === A4.XY && (e8[2] = 0), z(e8, e8);
  }
  _initializeDistanceConstraints() {
    r(this._left.end) && !this.requiresSplitEdgeLeft && this._updateDistanceConstraint(V(this._plane, this._left.end)), r(this._right.end) && !this.requiresSplitEdgeRight && this._updateDistanceConstraint(V(this._plane, this._right.end)), this._updateIntersectDistanceConstraint(this._plane);
  }
  _updateDistanceConstraint(t5) {
    t5 <= 0 && (this._minDistance = Math.max(this._minDistance, t5)), t5 >= 0 && (this._maxDistance = Math.min(this._maxDistance, t5));
  }
  _updateIntersectDistanceConstraint(t5) {
    const i4 = Y(t5), e8 = this._edgeDirection, o4 = u(n2(), this._left.start, this._left.direction), a4 = u(n2(), this._right.start, this._right.direction), _3 = this._pointInBasis2D(n4(), i4, e8, this._left.start), c3 = this._pointInBasis2D(n4(), i4, e8, o4), l3 = this._pointInBasis2D(n4(), i4, e8, this._right.start), g3 = this._pointInBasis2D(n4(), i4, e8, a4), [f2] = j4({ start: c3, end: _3, type: b.LINE }, { start: g3, end: l3, type: b.LINE });
    if (!f2) return;
    const u2 = o2(n4(), _3, c3);
    v2(u2, u2);
    const D = o2(n4(), f2, c3), E3 = j3(u2, D), b2 = u(n2(), o4, g(n2(), this._left.direction, -E3)), I = V(t5, b2);
    this._updateDistanceConstraint(I);
  }
  _pointInBasis2D(t5, i4, e8, s3) {
    return t5[0] = f(i4, s3), t5[1] = f(e8, s3), t5;
  }
  _offset(t5, e8) {
    Number.isFinite(this._minDistance) && (e8 = Math.max(this._minDistance, e8)), Number.isFinite(this._maxDistance) && (e8 = Math.min(this._maxDistance, e8)), A3(this._offsetPlane, this._plane), this._offsetPlane[3] -= e8;
    const s3 = (t6, e9, s4) => r(e9) && q(this._offsetPlane, t6, u(n2(), t6, e9), s4), n7 = n2();
    (t5 === this._edge.leftVertex ? s3(this._left.start, this._left.direction, n7) : s3(this._right.start, this._right.direction, n7)) && this._helper.copy(this._helper.fromXYZ(n7, void 0, this._helper.getM(t5.pos)), t5.pos);
  }
  selectArrowFromStartPoint(t5) {
    this._selectedArrow = B(this.plane, this._pointToXYZ(t5)) ? 1 : -1;
  }
  get selectedArrow() {
    return this._selectedArrow;
  }
  signedDistanceToPoint(t5) {
    return V(this.plane, this._pointToXYZ(t5));
  }
  apply(t5) {
    this._offset(t5, this.distance);
  }
  undo(t5) {
    this._offset(t5, 0);
  }
  canAccumulate(t5) {
    return t5 instanceof _P && this._edge.leftVertex.index === t5._edge.leftVertex.index && this._edge.rightVertex.index === t5._edge.rightVertex.index && this._edge.component === t5._edge.component && this._maybeEqualsVec3(this._left.direction, t5._left.direction) && this._maybeEqualsVec3(this._right.direction, t5._right.direction) && G(Y(this._plane), Y(t5._plane));
  }
  accumulate(t5, i4) {
    const e8 = this._plane[3] - i4._plane[3] + i4.distance;
    this._offset(t5, e8);
  }
  accumulateParams(t5) {
    const i4 = t5.distance - t5._plane[3];
    this.distance = i4 + this._plane[3];
  }
  clone() {
    const t5 = new _P(this._helper, this._planeType, this._edge, this.distance, j5.DEFERRED);
    return A3(t5._plane, this._plane), A3(t5._offsetPlane, this._offsetPlane), t5._maxDistance = this._maxDistance, t5._minDistance = this._minDistance, t5._left = this._cloneNeighbor(this._left), t5._right = this._cloneNeighbor(this._right), t5._edgeDirection = r4(n2(), this._edgeDirection), t5;
  }
  _maybeEqualsVec3(t5, s3) {
    return t(t5) && t(s3) || r(t5) && r(s3) && G(t5, s3);
  }
  _cloneNeighbor({ start: t5, end: e8, direction: s3, isOriginalDirection: n7 }) {
    return { start: r4(n2(), t5), end: r(e8) ? r4(n2(), e8) : null, direction: r4(n2(), s3), isOriginalDirection: n7 };
  }
};
var T = m2(15);
var Z = 1e-3;
var A4;
var j5;
!function(t5) {
  t5[t5.XYZ = 0] = "XYZ", t5[t5.XY = 1] = "XY";
}(A4 || (A4 = {})), function(t5) {
  t5[t5.IMMEDIATE = 0] = "IMMEDIATE", t5[t5.DEFERRED = 1] = "DEFERRED";
}(j5 || (j5 = {}));

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/RotateVertex.js
var s2 = class _s {
  constructor(t5, a4, s3 = e6.CUMULATIVE) {
    this.origin = t5, this.angle = a4, this._accumulationType = s3;
  }
  _rotate(t5, i4) {
    L(t5.pos, t5.pos, this.origin, i4);
  }
  apply(t5) {
    this._rotate(t5, this.angle);
  }
  undo(t5) {
    this._rotate(t5, -this.angle);
  }
  canAccumulate(a4) {
    return a4 instanceof _s && i(this.origin, a4.origin);
  }
  accumulate(t5, a4) {
    const s3 = a4._accumulationType === e6.REPLACE;
    this._rotate(t5, s3 ? a4.angle - this.angle : a4.angle);
  }
  accumulateParams(t5) {
    const a4 = t5._accumulationType === e6.REPLACE;
    this.angle = a4 ? t5.angle : this.angle + t5.angle;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/ScaleVertex.js
var c2 = class _c {
  constructor(t5, a4, c3, o4, r11 = e6.CUMULATIVE) {
    this.origin = t5, this.axis1 = a4, this.factor1 = c3, this.factor2 = o4, this._accumulationType = r11, this.axis2 = r6(a4[1], -a4[0]);
  }
  _scale(t5, s3, i4) {
    w2(t5.pos, t5.pos, this.origin, this.axis1, s3), w2(t5.pos, t5.pos, this.origin, this.axis2, i4);
  }
  apply(t5) {
    this._scale(t5, this.factor1, this.factor2);
  }
  undo(t5) {
    this._scale(t5, 1 / this.factor1, 1 / this.factor2);
  }
  canAccumulate(s3) {
    return s3 instanceof _c && i(this.origin, s3.origin) && i(this.axis1, s3.axis1);
  }
  accumulate(t5, s3) {
    s3._accumulationType === e6.REPLACE ? this._scale(t5, s3.factor1 / this.factor1, s3.factor2 / this.factor2) : this._scale(t5, s3.factor1, s3.factor2);
  }
  accumulateParams(t5) {
    const s3 = t5._accumulationType === e6.REPLACE;
    this.factor1 = s3 ? t5.factor1 : this.factor1 * t5.factor1, this.factor2 = s3 ? t5.factor2 : this.factor2 * t5.factor2;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/operations/UndoGroup.js
var t4 = class {
  constructor() {
    this._operations = [], this._closed = false;
  }
  close() {
    this._closed = true;
  }
  apply() {
    for (const t5 of this._operations) t5.apply();
  }
  undo() {
    for (let t5 = this._operations.length - 1; t5 >= 0; t5--) this._operations[t5].undo();
  }
  accumulate(t5) {
    if (this._closed) return false;
    const o4 = this._operations.length ? this._operations[this._operations.length - 1] : null;
    return o4 && o4.accumulate(t5) || (this._operations.push(t5), t5.apply()), true;
  }
};

// node_modules/@arcgis/core/views/interactive/editGeometry/EditGeometryOperations.js
var V2 = class _V extends n {
  constructor(e8) {
    super(), this.data = e8, this._undoStack = [], this._redoStack = [], this._listener = this.data.on("change", (e9) => {
      e9.addedVertices && this.emit("vertex-add", { type: "vertex-add", vertices: e9.addedVertices, operation: e9.operation }), e9.removedVertices && this.emit("vertex-remove", { type: "vertex-remove", vertices: e9.removedVertices, operation: e9.operation }), e9.updatedVertices && this.emit("vertex-update", { type: "vertex-update", vertices: e9.updatedVertices, operation: e9.operation });
    });
  }
  destroy() {
    this._listener.remove();
  }
  splitEdge(e8, t5) {
    return this._apply(new r9(this.data, e8, t5));
  }
  updateVertices(e8, t5, r11 = E2.ACCUMULATE_STEPS) {
    return this._apply(new t2(this.data, e8, t5), r11);
  }
  moveVertices(e8, t5, r11, s3, i4 = E2.ACCUMULATE_STEPS) {
    return this.updateVertices(e8, new t3(this.data.coordinateHelper, t5, r11, s3), i4);
  }
  scaleVertices(e8, t5, r11, s3, i4, a4 = E2.ACCUMULATE_STEPS, p4 = e6.CUMULATIVE) {
    return this.updateVertices(e8, new c2(t5, r11, s3, i4, p4), a4);
  }
  rotateVertices(e8, t5, r11, s3 = E2.ACCUMULATE_STEPS, i4 = e6.CUMULATIVE) {
    return this.updateVertices(e8, new s2(t5, r11, i4), s3);
  }
  removeVertices(e8) {
    return this._apply(new r8(this.data, e8, this._minNumVerticesPerType));
  }
  appendVertex(e8) {
    return 0 === this.data.components.length ? null : this._apply(new d(this.data, this.data.components[0], e8));
  }
  setVertexPosition(e8, t5) {
    return this._apply(new i2(this.data, e8, t5));
  }
  offsetEdge(e8, t5, r11, s3 = E2.ACCUMULATE_STEPS) {
    return this.updateVertices([t5.leftVertex, t5.rightVertex], new P2(this.data.coordinateHelper, e8, t5, r11), s3);
  }
  closeComponent(e8) {
    return this.data.components.includes(e8) ? this._apply(new i3(this.data, e8)) : null;
  }
  canRemoveVertex() {
    return this.data.components[0].vertices.length > this._minNumVerticesPerType;
  }
  createUndoGroup() {
    const e8 = new t4();
    return this._apply(e8), { remove: () => e8.close() };
  }
  undo() {
    if (this._undoStack.length > 0) {
      const e8 = this._undoStack.pop();
      return e8.undo(), this._redoStack.push(e8), e8;
    }
    return null;
  }
  redo() {
    if (this._redoStack.length > 0) {
      const e8 = this._redoStack.pop();
      return e8.apply(), this._undoStack.push(e8), e8;
    }
    return null;
  }
  get canUndo() {
    return this._undoStack.length > 0;
  }
  get canRedo() {
    return this._redoStack.length > 0;
  }
  get lastOperation() {
    return this._undoStack.length > 0 ? this._undoStack[this._undoStack.length - 1] : null;
  }
  get _minNumVerticesPerType() {
    switch (this.data.type) {
      case "point":
        return 1;
      case "polyline":
        return 2;
      case "polygon":
        return 3;
      default:
        return 0;
    }
  }
  _apply(e8, r11 = E2.ACCUMULATE_STEPS) {
    return r11 !== E2.NEW_STEP && !t(this.lastOperation) && this.lastOperation.accumulate(e8) || (e8.apply(), this._undoStack.push(e8), this._redoStack = []), e8;
  }
  static fromGeometry(e8, t5) {
    return new _V(g2.fromGeometry(e8, t5));
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/SnappingContext.js
var e7 = class {
  constructor(e8) {
    this.vertexHandle = null, this.excludeFeature = null, this.visualizer = null, this.selfSnappingZ = null, this.editGeometryOperations = e8.editGeometryOperations, this.elevationInfo = e8.elevationInfo, this.pointer = e8.pointer, this.vertexHandle = e8.vertexHandle, this.excludeFeature = e8.excludeFeature, this.visualizer = e8.visualizer, this.selfSnappingZ = e8.selfSnappingZ;
  }
  get coordinateHelper() {
    return this.editGeometryOperations.data.coordinateHelper;
  }
  get spatialReference() {
    return this.coordinateHelper.spatialReference;
  }
};

// node_modules/@arcgis/core/views/interactive/snapping/SnappingVisualizer.js
var r10 = class {
  draw(r11, a4) {
    const p4 = this._getUniqueHints(r11), h = this.sortUniqueHints(p4), u2 = [];
    for (const i4 of h) i4 instanceof o3 && u2.push(this.visualizeIntersectionPoint(i4, a4)), i4 instanceof n5 && u2.push(this.visualizeLine(i4, a4)), i4 instanceof r7 && u2.push(this.visualizeParallelSign(i4, a4)), i4 instanceof s && u2.push(this.visualizeRightAngleQuad(i4, a4)), i4 instanceof n6 && u2.push(this.visualizePoint(i4, a4));
    return r2(u2);
  }
  sortUniqueHints(i4) {
    return i4;
  }
  _getUniqueHints(i4) {
    const n7 = [];
    for (const t5 of i4) {
      let i5 = true;
      for (const s3 of n7) if (t5.equals(s3)) {
        i5 = false;
        break;
      }
      i5 && n7.push(t5);
    }
    return n7;
  }
};

export {
  r10 as r,
  w3 as w,
  p3 as p,
  g2 as g,
  E2 as E,
  t2 as t,
  e6 as e,
  t3 as t2,
  P2 as P,
  A4 as A,
  s2 as s,
  c2 as c,
  V2 as V,
  e7 as e2
};
//# sourceMappingURL=chunk-PTI7U6FU.js.map
