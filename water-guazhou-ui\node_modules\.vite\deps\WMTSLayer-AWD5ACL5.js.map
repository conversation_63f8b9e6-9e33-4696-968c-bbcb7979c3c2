{"version": 3, "sources": ["../../@arcgis/core/layers/support/TileInfoTilemapCache.js", "../../@arcgis/core/layers/support/TileMatrixSet.js", "../../@arcgis/core/layers/support/WMTSStyle.js", "../../@arcgis/core/layers/support/WMTSSublayer.js", "../../@arcgis/core/layers/support/wmtsUtils.js", "../../@arcgis/core/layers/WMTSLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{waitTick as i}from\"../../core/promiseUtils.js\";class l{constructor(i,l=0,a=i.lods.length-1){this.tileInfo=i,this.minLOD=l,this.maxLOD=a}getAvailability(i,l,a){const t=this.tileInfo?.lodAt(i);return!t||i<this.minLOD||i>this.maxLOD?\"unavailable\":t.cols&&t.rows?a>=t.cols[0]&&a<=t.cols[1]&&l>=t.rows[0]&&l<=t.rows[1]?\"available\":\"unavailable\":\"available\"}async fetchAvailability(l,a,t,e){return await i(e),this.getAvailability(l,a,t)}async fetchAvailabilityUpsample(l,a,t,e,s){await i(s),e.level=l,e.row=a,e.col=t;const o=this.tileInfo;for(o.updateTileInfo(e);;){const i=this.getAvailability(e.level,e.row,e.col);if(\"unavailable\"!==i)return i;if(!o.upsampleTile(e))return\"unavailable\"}}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"../../geometry/Extent.js\";import i from\"./TileInfo.js\";var l;let n=l=class extends o{constructor(t){super(t),this.fullExtent=null,this.id=null,this.tileInfo=null}clone(){const t=new l;return this.hasOwnProperty(\"fullExtent\")&&(t.fullExtent=this.fullExtent&&this.fullExtent.clone()),this.hasOwnProperty(\"id\")&&(t.id=this.id),this.hasOwnProperty(\"tileInfo\")&&(t.tileInfo=this.tileInfo&&this.tileInfo.clone()),t}};t([e({type:s,json:{read:{source:\"fullExtent\"}}})],n.prototype,\"fullExtent\",void 0),t([e({type:String,json:{read:{source:\"id\"}}})],n.prototype,\"id\",void 0),t([e({type:i,json:{read:{source:\"tileInfo\"}}})],n.prototype,\"tileInfo\",void 0),n=l=t([r(\"esri.layer.support.TileMatrixSet\")],n);const p=n;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";var o;let i=o=class extends e{constructor(r){super(r),this.id=null,this.title=null,this.description=null,this.legendUrl=null}clone(){const r=new o;return this.hasOwnProperty(\"description\")&&(r.description=this.description),this.hasOwnProperty(\"id\")&&(r.id=this.id),this.hasOwnProperty(\"isDefault\")&&(r.isDefault=this.isDefault),this.hasOwnProperty(\"keywords\")&&(r.keywords=this.keywords&&this.keywords.slice()),this.hasOwnProperty(\"legendUrl\")&&(r.legendUrl=this.legendUrl),this.hasOwnProperty(\"title\")&&(r.title=this.title),r}};r([s({json:{read:{source:\"id\"}}})],i.prototype,\"id\",void 0),r([s({json:{read:{source:\"title\"}}})],i.prototype,\"title\",void 0),r([s({json:{read:{source:\"abstract\"}}})],i.prototype,\"description\",void 0),r([s({json:{read:{source:\"legendUrl\"}}})],i.prototype,\"legendUrl\",void 0),r([s({json:{read:{source:\"isDefault\"}}})],i.prototype,\"isDefault\",void 0),r([s({json:{read:{source:\"keywords\"}}})],i.prototype,\"keywords\",void 0),i=o=r([t(\"esri.layer.support.WMTSStyle\")],i);const p=i;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Collection.js\";import{JSONSupport as s}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as i}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as l}from\"../../core/accessorSupport/decorators/subclass.js\";import o from\"../../geometry/Extent.js\";import a from\"./TileMatrixSet.js\";import n from\"./WMTSStyle.js\";var p;let m=p=class extends s{constructor(t){super(t),this.fullExtent=null,this.fullExtents=null,this.imageFormats=null,this.id=null,this.layer=null,this.styles=null,this.tileMatrixSetId=null,this.tileMatrixSets=null}get description(){return this._get(\"description\")}set description(t){this._set(\"description\",t)}readFullExtent(t,e){return(t=e.fullExtent)?o.fromJSON(t):null}readFullExtents(t,e){return e.fullExtents?.length?e.fullExtents.map((t=>o.fromJSON(t))):e.tileMatrixSets?.map((t=>o.fromJSON(t.fullExtent))).filter((t=>t))??[]}get imageFormat(){let t=this._get(\"imageFormat\");return t||(t=this.imageFormats&&this.imageFormats.length?this.imageFormats[0]:\"\"),t}set imageFormat(t){const e=this.imageFormats;t&&(t.includes(\"image/\")||e&&!e.includes(t))&&(t.includes(\"image/\")||(t=\"image/\"+t),e&&!e.includes(t))?console.error(\"The layer doesn't support the format of \"+t):this._set(\"imageFormat\",t)}get styleId(){let t=this._get(\"styleId\");return t||(t=this.styles?.length?this.styles.getItemAt(0).id:\"\"),t}set styleId(t){this._set(\"styleId\",t)}get title(){return this._get(\"title\")}set title(t){this._set(\"title\",t)}get tileMatrixSet(){return this.tileMatrixSets?this.tileMatrixSets.find((t=>t.id===this.tileMatrixSetId)):null}clone(){const t=new p;return this.hasOwnProperty(\"description\")&&(t.description=this.description),this.hasOwnProperty(\"imageFormats\")&&(t.imageFormats=this.imageFormats&&this.imageFormats.slice()),this.hasOwnProperty(\"imageFormat\")&&(t.imageFormat=this.imageFormat),this.hasOwnProperty(\"fullExtent\")&&(t.fullExtent=this.fullExtent&&this.fullExtent.clone()),this.hasOwnProperty(\"id\")&&(t.id=this.id),this.hasOwnProperty(\"layer\")&&(t.layer=this.layer),this.hasOwnProperty(\"styleId\")&&(t.styleId=this.styleId),this.hasOwnProperty(\"styles\")&&(t.styles=this.styles&&this.styles.clone()),this.hasOwnProperty(\"tileMatrixSetId\")&&(t.tileMatrixSetId=this.tileMatrixSetId),this.hasOwnProperty(\"tileMatrixSets\")&&(t.tileMatrixSets=this.tileMatrixSets?.clone()),this.hasOwnProperty(\"title\")&&(t.title=this.title),t}};t([r()],m.prototype,\"description\",null),t([r()],m.prototype,\"fullExtent\",void 0),t([i(\"fullExtent\",[\"fullExtent\"])],m.prototype,\"readFullExtent\",null),t([r({readOnly:!0})],m.prototype,\"fullExtents\",void 0),t([i(\"fullExtents\",[\"fullExtents\",\"tileMatrixSets\"])],m.prototype,\"readFullExtents\",null),t([r()],m.prototype,\"imageFormat\",null),t([r({json:{read:{source:\"formats\"}}})],m.prototype,\"imageFormats\",void 0),t([r()],m.prototype,\"id\",void 0),t([r()],m.prototype,\"layer\",void 0),t([r()],m.prototype,\"styleId\",null),t([r({type:e.ofType(n),json:{read:{source:\"styles\"}}})],m.prototype,\"styles\",void 0),t([r({value:null,json:{write:{ignoreOrigin:!0}}})],m.prototype,\"title\",null),t([r()],m.prototype,\"tileMatrixSetId\",void 0),t([r({readOnly:!0})],m.prototype,\"tileMatrixSet\",null),t([r({type:e.ofType(a),json:{read:{source:\"tileMatrixSets\"}}})],m.prototype,\"tileMatrixSets\",void 0),m=p=t([l(\"esri.layers.support.WMTSSublayer\")],m);const u=m;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../core/Error.js\";import{isSome as t}from\"../../core/maybe.js\";import{getReferenceEllipsoidFromWKID as n}from\"../../geometry/ellipsoidUtils.js\";import i from\"../../geometry/Extent.js\";import r from\"../../geometry/Point.js\";import o from\"../../geometry/support/WKIDUnitConversion.js\";import{isAxesOrderReversedForWkid as l}from\"../ogc/crsUtils.js\";import{visitXML as s}from\"../ogc/xmlUtils.js\";import a from\"./LOD.js\";import c from\"./TileInfo.js\";const u=90.71428571428571;function p(t){const n=t.replace(/ows:/gi,\"\");if(!g(\"Contents\",(new DOMParser).parseFromString(n,\"text/xml\").documentElement))throw new e(\"wmtslayer:wmts-capabilities-xml-is-not-valid\",\"the wmts get capabilities response is not compliant\",{text:t})}function f(t,n){t=t.replace(/ows:/gi,\"\");const i=(new DOMParser).parseFromString(t,\"text/xml\").documentElement,r=new Map,o=new Map,l=g(\"Contents\",i);if(!l)throw new e(\"wmtslayer:wmts-capabilities-xml-is-not-valid\");const s=g(\"OperationsMetadata\",i)?.querySelector(\"[name='GetTile']\"),a=s?.getElementsByTagName(\"Get\"),c=a&&Array.prototype.slice.call(a),u=n.url?.indexOf(\"https\"),p=void 0!==u&&u>-1;let f,d,m=n.serviceMode,h=n?.url;if(c&&c.length&&c.some((e=>{const t=g(\"Constraint\",e);return!t||C(\"AllowedValues\",\"Value\",m,t)?(h=e.attributes[0].nodeValue,!0):(!t||C(\"AllowedValues\",\"Value\",\"RESTful\",t)||C(\"AllowedValues\",\"Value\",\"REST\",t)?d=e.attributes[0].nodeValue:t&&!C(\"AllowedValues\",\"Value\",\"KVP\",t)||(f=e.attributes[0].nodeValue),!1)})),!h)if(d)h=d,m=\"RESTful\";else if(f)h=f,m=\"KVP\";else{const e=g(\"ServiceMetadataURL\",i);h=e?.getAttribute(\"xlink:href\")}const T=h.indexOf(\"1.0.0/\");-1===T&&\"RESTful\"===m?h+=\"/\":T>-1&&(h=h.substring(0,T)),\"KVP\"===m&&(h+=T>-1?\"\":\"?\"),p&&(h=h.replace(/^http:/i,\"https:\"));const y=w(\"ServiceIdentification>ServiceTypeVersion\",i),R=w(\"ServiceIdentification>AccessConstraints\",i),S=R&&/^none$/i.test(R)?null:R,V=x(\"Layer\",l),b=x(\"TileMatrixSet\",l),E=V.map((e=>{const t=w(\"Identifier\",e);return r.set(t,e),M(t,e,b,p,y)}));return{copyright:S,dimensionMap:o,layerMap:r,layers:E,serviceMode:m,tileUrl:h}}function d(e){return e.layers.forEach((e=>{e.tileMatrixSets?.forEach((e=>{const t=e.tileInfo;t&&96!==t.dpi&&(t.lods?.forEach((n=>{n.scale=96*n.scale/t.dpi,n.resolution=j(t.spatialReference?.wkid,n.scale*u/96,e.id)})),t.dpi=96)}))})),e}function m(e){return e.nodeType===Node.ELEMENT_NODE}function g(e,t){for(let n=0;n<t.childNodes.length;n++){const i=t.childNodes[n];if(m(i)&&i.nodeName===e)return i}return null}function x(e,t){const n=[];for(let i=0;i<t.childNodes.length;i++){const r=t.childNodes[i];m(r)&&r.nodeName===e&&n.push(r)}return n}function h(e,n){const i=[];for(let t=0;t<n.childNodes.length;t++){const r=n.childNodes[t];m(r)&&r.nodeName===e&&i.push(r)}return i.map((e=>e.textContent)).filter(t)}function w(e,t){return e.split(\">\").forEach((e=>{t&&(t=g(e,t))})),t&&t.textContent}function C(e,t,n,i){let r;return Array.prototype.slice.call(i.childNodes).some((i=>{if(i.nodeName.includes(e)){const e=g(t,i),o=e&&e.textContent;if(o===n||n.split(\":\")&&n.split(\":\")[1]===o)return r=i,!0}return!1})),r}function M(e,t,n,i,r){const o=w(\"Abstract\",t),l=h(\"Format\",t);return{id:e,fullExtent:V(t),fullExtents:b(t),description:o,formats:l,styles:E(t,i),title:w(\"Title\",t),tileMatrixSets:L(r,t,n)}}function T(e,t){const n=[],i=e.layerMap?.get(t);if(!i)return null;const r=x(\"ResourceURL\",i),o=x(\"Dimension\",i);let l,s,a,c;return o.length&&(l=w(\"Identifier\",o[0]),s=h(\"Default\",o[0])||h(\"Value\",o[0])),o.length>1&&(a=w(\"Identifier\",o[1]),c=h(\"Default\",o[1])||h(\"Value\",o[1])),e.dimensionMap.set(t,{dimensions:s,dimensions2:c}),r.forEach((e=>{let t=e.getAttribute(\"template\");if(\"tile\"===e.getAttribute(\"resourceType\")){if(l&&s.length)if(t.includes(\"{\"+l+\"}\"))t=t.replace(\"{\"+l+\"}\",\"{dimensionValue}\");else{const e=t.toLowerCase().indexOf(\"{\"+l.toLowerCase()+\"}\");e>-1&&(t=t.substring(0,e)+\"{dimensionValue}\"+t.substring(e+l.length+2))}if(a&&c.length)if(t.includes(\"{\"+a+\"}\"))t=t.replace(\"{\"+a+\"}\",\"{dimensionValue2}\");else{const e=t.toLowerCase().indexOf(\"{\"+a.toLowerCase()+\"}\");e>-1&&(t=t.substring(0,e)+\"{dimensionValue2}\"+t.substring(e+a.length+2))}n.push({template:t,format:e.getAttribute(\"format\"),resourceType:\"tile\"})}})),n}function y(e,t,n,i,r,o,l,s){const a=R(e,t,i);if(!(a?.length>0))return\"\";const{dimensionMap:c}=e,u=c.get(t).dimensions?.[0],p=c.get(t).dimensions2?.[0];return a[l%a.length].template.replace(/\\{Style\\}/gi,r??\"\").replace(/\\{TileMatrixSet\\}/gi,n??\"\").replace(/\\{TileMatrix\\}/gi,o).replace(/\\{TileRow\\}/gi,\"\"+l).replace(/\\{TileCol\\}/gi,\"\"+s).replace(/\\{dimensionValue\\}/gi,u).replace(/\\{dimensionValue2\\}/gi,p)}function R(e,t,n){const i=T(e,t),r=i?.filter((e=>e.format===n));return(r?.length?r:i)??[]}function S(e,t,n,i){const{dimensionMap:r}=e,o=T(e,t);let l=\"\";if(o&&o.length>0){const e=r.get(t).dimensions&&r.get(t).dimensions[0],s=r.get(t).dimensions2&&r.get(t).dimensions2[0];l=o[0].template,l.indexOf(\".xxx\")===l.length-4&&(l=l.slice(0,l.length-4)),l=l.replace(/\\{Style\\}/gi,i),l=l.replace(/\\{TileMatrixSet\\}/gi,n),l=l.replace(/\\{TileMatrix\\}/gi,\"{level}\"),l=l.replace(/\\{TileRow\\}/gi,\"{row}\"),l=l.replace(/\\{TileCol\\}/gi,\"{col}\"),l=l.replace(/\\{dimensionValue\\}/gi,e),l=l.replace(/\\{dimensionValue2\\}/gi,s)}return l}function V(e){const t=g(\"WGS84BoundingBox\",e),n=t?w(\"LowerCorner\",t).split(\" \"):[\"-180\",\"-90\"],i=t?w(\"UpperCorner\",t).split(\" \"):[\"180\",\"90\"];return{xmin:parseFloat(n[0]),ymin:parseFloat(n[1]),xmax:parseFloat(i[0]),ymax:parseFloat(i[1]),spatialReference:{wkid:4326}}}function b(e){const t=[];return s(e,{BoundingBox:e=>{if(!e.getAttribute(\"crs\"))return;const n=e.getAttribute(\"crs\").toLowerCase(),i=N(n),r=n.includes(\"epsg\")&&l(i.wkid);let o,a,c,u;s(e,{LowerCorner:e=>{[o,a]=e.textContent.split(\" \").map((e=>Number.parseFloat(e))),r&&([o,a]=[a,o])},UpperCorner:e=>{[c,u]=e.textContent.split(\" \").map((e=>Number.parseFloat(e))),r&&([c,u]=[u,c])}}),t.push({xmin:o,ymin:a,xmax:c,ymax:u,spatialReference:i})}}),t}function E(e,t){return x(\"Style\",e).map((e=>{const n=g(\"LegendURL\",e),i=g(\"Keywords\",e),r=i?h(\"Keyword\",i):[];let o=n&&n.getAttribute(\"xlink:href\");t&&(o=o&&o.replace(/^http:/i,\"https:\"));return{abstract:w(\"Abstract\",e),id:w(\"Identifier\",e),isDefault:\"true\"===e.getAttribute(\"isDefault\"),keywords:r,legendUrl:o,title:w(\"Title\",e)}}))}function L(e,t,n){return x(\"TileMatrixSetLink\",t).map((t=>I(e,t,n)))}function I(e,t,n){const i=g(\"TileMatrixSet\",t).textContent,r=h(\"TileMatrix\",t),o=n.find((e=>{const t=g(\"Identifier\",e),n=t&&t.textContent;return!!(n===i||i.split(\":\")&&i.split(\":\")[1]===n)})),l=g(\"TileMatrixSetLimits\",t),s=l&&x(\"TileMatrixLimits\",l),a=new Map;if(s?.length)for(const c of s){const e=g(\"TileMatrix\",c).textContent,t=+g(\"MinTileRow\",c).textContent,n=+g(\"MaxTileRow\",c).textContent,i=+g(\"MinTileCol\",c).textContent,r=+g(\"MaxTileCol\",c).textContent;a.set(e,{minCol:i,maxCol:r,minRow:t,maxRow:n})}const u=w(\"SupportedCRS\",o).toLowerCase(),p=A(o,u),f=p.spatialReference,d=g(\"TileMatrix\",o),m=[parseInt(w(\"TileWidth\",d),10),parseInt(w(\"TileHeight\",d),10)],M=[];if(r.length)r.forEach(((e,t)=>{const n=C(\"TileMatrix\",\"Identifier\",e,o);M.push(k(n,u,t,i,a))}));else{x(\"TileMatrix\",o).forEach(((e,t)=>{M.push(k(e,u,t,i,a))}))}const T=F(e,o,p,m,M[0]).toJSON(),y=new c({dpi:96,spatialReference:f,size:m,origin:p,lods:M}).toJSON();return{id:i,fullExtent:T,tileInfo:y}}function N(e){e=e.toLowerCase();let n=parseInt(e.split(\":\").pop(),10);900913!==n&&3857!==n||(n=102100);const i=U(e);return t(i)&&(n=i),{wkid:n}}function A(e,t){return v(g(\"TileMatrix\",e),t)}function v(e,t){const n=N(t),[i,o]=w(\"TopLeftCorner\",e).split(\" \").map((e=>parseFloat(e))),s=t.includes(\"epsg\")&&l(n.wkid);return new r(s?{x:o,y:i,spatialReference:n}:{x:i,y:o,spatialReference:n})}function F(e,t,n,r,o){const l=g(\"BoundingBox\",t);let s,a,c,u,p,f;if(l&&(s=w(\"LowerCorner\",l).split(\" \"),a=w(\"UpperCorner\",l).split(\" \")),s&&s.length>1&&a&&a.length>1)c=parseFloat(s[0]),p=parseFloat(s[1]),u=parseFloat(a[0]),f=parseFloat(a[1]);else{const e=g(\"TileMatrix\",t),i=parseInt(w(\"MatrixWidth\",e),10),l=parseInt(w(\"MatrixHeight\",e),10);c=n.x,f=n.y,u=c+i*r[0]*o.resolution,p=f-l*r[1]*o.resolution}return O(e,n.spatialReference,n)?new i(p,c,f,u,n.spatialReference):new i(c,p,u,f,n.spatialReference)}function O(e,t,n){return\"1.0.0\"===e&&l(t.wkid)&&!(n.spatialReference.isGeographic&&n.x<-90&&n.y>=-90)}var D;function U(e){return e.includes(\"crs84\")||e.includes(\"crs:84\")?D.CRS84:e.includes(\"crs83\")||e.includes(\"crs:83\")?D.CRS83:e.includes(\"crs27\")||e.includes(\"crs:27\")?D.CRS27:null}function k(e,t,n,i,r){const o=N(t),l=w(\"Identifier\",e);let s=parseFloat(w(\"ScaleDenominator\",e));const c=j(o.wkid,s,i);s*=96/u;const p=+w(\"MatrixWidth\",e),f=+w(\"MatrixHeight\",e),{maxCol:d=p-1,maxRow:m=f-1,minCol:g=0,minRow:x=0}=r.get(l)??{},{x:h,y:C}=v(e,t);return new a({cols:[g,d],level:n,levelValue:l,origin:[h,C],scale:s,resolution:c,rows:[x,m]})}function j(e,t,i){let r;return r=o.hasOwnProperty(\"\"+e)?o.values[o[e]]:\"default028mm\"===i?6370997*Math.PI/180:n(e).metersPerDegree,7*t/25e3/r}!function(e){e[e.CRS84=4326]=\"CRS84\",e[e.CRS83=4269]=\"CRS83\",e[e.CRS27=4267]=\"CRS27\"}(D||(D={}));export{y as getTileUrlFromResourceUrls,S as getTileUrlTemplateFromResourceUrls,R as getTileUrlTemplates,f as parseCapabilities,d as parseResourceInfo,p as validateCapabilities};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../request.js\";import r from\"../core/Collection.js\";import i from\"../core/Error.js\";import{clone as s}from\"../core/lang.js\";import{isSome as a}from\"../core/maybe.js\";import{MultiOriginJSONMixin as o}from\"../core/MultiOriginJSONSupport.js\";import{getDeepValue as l}from\"../core/object.js\";import{throwIfAbortError as n}from\"../core/promiseUtils.js\";import{watch as m,on as p,sync as c}from\"../core/reactiveUtils.js\";import{urlToObject as u,objectToQuery as y}from\"../core/urlUtils.js\";import{property as d}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{reader as h}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as f}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as v}from\"../core/accessorSupport/decorators/writer.js\";import g from\"../geometry/Extent.js\";import L from\"./Layer.js\";import w from\"./WebTileLayer.js\";import{BlendLayer as I}from\"./mixins/BlendLayer.js\";import{OperationalLayer as S}from\"./mixins/OperationalLayer.js\";import{PortalLayer as M}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as T}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as P}from\"./mixins/ScaleRangeLayer.js\";import{createBitmap as b}from\"./support/imageBitmapUtils.js\";import j from\"./support/TileInfo.js\";import x from\"./support/TileInfoTilemapCache.js\";import{WMTSLayerInfo as E}from\"./support/WMTSLayerInfo.js\";import _ from\"./support/WMTSSublayer.js\";import{getTileUrlFromResourceUrls as C,getTileUrlTemplateFromResourceUrls as U,validateCapabilities as R,parseCapabilities as O,parseResourceInfo as W}from\"./support/wmtsUtils.js\";const A={\"image/png\":\".png\",\"image/png8\":\".png\",\"image/png24\":\".png\",\"image/png32\":\".png\",\"image/jpg\":\".jpg\",\"image/jpeg\":\".jpeg\",\"image/gif\":\".gif\",\"image/bmp\":\".bmp\",\"image/tiff\":\".tif\",\"image/jpgpng\":\"\",\"image/jpegpng\":\"\",\"image/unknown\":\"\"},F=new Set([\"version\",\"service\",\"request\",\"layer\",\"style\",\"format\",\"tilematrixset\",\"tilematrix\",\"tilerow\",\"tilecol\"]);let V=class extends(I(T(P(S(M(o(L))))))){constructor(...e){super(...e),this.copyright=\"\",this.customParameters=null,this.customLayerParameters=null,this.fullExtent=null,this.operationalLayerType=\"WebTiledLayer\",this.resourceInfo=null,this.serviceMode=\"RESTful\",this.sublayers=null,this.type=\"wmts\",this.version=\"1.0.0\",this.addHandles([m((()=>this.activeLayer),((e,t)=>{t&&(t.layer=null),e&&(e.layer=this)}),c),p((()=>this.sublayers),\"after-add\",(({item:e})=>{e.layer=this}),c),p((()=>this.sublayers),\"after-remove\",(({item:e})=>{e.layer=null}),c),m((()=>this.sublayers),((e,t)=>{if(t)for(const r of t)r.layer=null;if(e)for(const r of e)r.layer=this}),c)])}normalizeCtorArgs(e,t){return\"string\"==typeof e?{url:e,...t}:e}load(e){if(\"KVP\"===this.serviceMode||\"RESTful\"===this.serviceMode)return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"WMTS\"]},e).catch(n).then((()=>this._fetchService(e))).catch((e=>{throw n(e),new i(\"wmtslayer:unsupported-service-data\",\"Invalid response from the WMTS service.\",{error:e})}))),Promise.resolve(this);console.error(\"WMTS mode could only be 'KVP' or 'RESTful'\")}get activeLayer(){return this._get(\"activeLayer\")}set activeLayer(e){this._set(\"activeLayer\",e)}readActiveLayerFromService(e,t,r){this.activeLayer||(this.activeLayer=new _);let i=t.layers.find((e=>e.id===this.activeLayer.id));return i||(i=t.layers[0]),this.activeLayer.read(i,r),this.activeLayer}readActiveLayerFromItemOrWebDoc(e,t){const{templateUrl:r,wmtsInfo:i}=t,s=r?this._getLowerCasedUrlParams(r):null,a=i?.layerIdentifier;let o=null;const l=i?.tileMatrixSet;l&&(Array.isArray(l)?l.length&&(o=l[0]):o=l);const n=s?.format,m=s?.style;return new _({id:a,imageFormat:n,styleId:m,tileMatrixSetId:o})}writeActiveLayer(e,t,r,i){const s=this.activeLayer;t.templateUrl=this.getUrlTemplate(s.id,s.tileMatrixSetId,s.imageFormat,s.styleId);const a=l(\"tileMatrixSet.tileInfo\",s);t.tileInfo=a?a.toJSON(i):null,t.wmtsInfo={...t.wmtsInfo,layerIdentifier:s.id,tileMatrixSet:s.tileMatrixSetId}}readCustomParameters(e,t){const r=t.wmtsInfo;return r?this._mergeParams(r.customParameters,r.url):null}get fullExtents(){return this.activeLayer.fullExtents}readServiceMode(e,t){return t.templateUrl.includes(\"?\")?\"KVP\":\"RESTful\"}readSublayersFromService(e,t,r){return $(t.layers,r)}get supportedSpatialReferences(){return this.activeLayer.tileMatrixSets?.map((e=>e.tileInfo?.spatialReference)).toArray().filter(a)??[]}get tilemapCache(){const e=this.activeLayer?.tileMatrixSet?.tileInfo;return e?new x(e):void 0}get title(){return this.activeLayer?.title??\"Layer\"}set title(e){this._overrideIfSome(\"title\",e)}get url(){return this._get(\"url\")}set url(e){e&&\"/\"===e.substr(-1)?this._set(\"url\",e.slice(0,-1)):this._set(\"url\",e)}createWebTileLayer(e){const t=this.getUrlTemplate(this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId),r=this._getTileMatrixSetById(e.tileMatrixSetId)?.tileInfo,i=e.fullExtent,s=new E({layerIdentifier:e.id,tileMatrixSet:e.tileMatrixSetId,url:this.url});return this.customLayerParameters&&(s.customLayerParameters=this.customLayerParameters),this.customParameters&&(s.customParameters=this.customParameters),new w({fullExtent:i,urlTemplate:t,tileInfo:r,wmtsInfo:s})}async fetchTile(e,r,i){const s=this.getTileUrl(e,r,i),{data:a}=await t(s,{responseType:\"image\"});return a}async fetchImageBitmapTile(e,r,i){const s=this.getTileUrl(e,r,i),{data:a}=await t(s,{responseType:\"blob\"});return b(a,s)}findSublayerById(e){return this.sublayers?.find((t=>t.id===e))}getTileUrl(e,t,r){const i=this._getTileMatrixSetById(this.activeLayer.tileMatrixSetId)?.tileInfo?.lods[e],s=i?i.levelValue?i.levelValue:`${i.level}`:`${e}`;let a=this.resourceInfo?\"\":C({dimensionMap:this.dimensionMap,layerMap:this.layerMap},this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId,s,t,r);if(!a){a=this.getUrlTemplate(this.activeLayer.id,this.activeLayer.tileMatrixSetId,this.activeLayer.imageFormat,this.activeLayer.styleId).replace(/\\{level\\}/gi,s).replace(/\\{row\\}/gi,`${t}`).replace(/\\{col\\}/gi,`${r}`)}return a=this._appendCustomLayerParameters(a),a}getUrlTemplate(e,t,r,i){if(!this.resourceInfo){const r=U({dimensionMap:this.dimensionMap,layerMap:this.layerMap},e,t,i);if(r)return r}if(\"KVP\"===this.serviceMode)return this.url+\"?SERVICE=WMTS&VERSION=\"+this.version+\"&REQUEST=GetTile&LAYER=\"+e+\"&STYLE=\"+i+\"&FORMAT=\"+r+\"&TILEMATRIXSET=\"+t+\"&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}\";if(\"RESTful\"===this.serviceMode){let s=\"\";return A[r.toLowerCase()]&&(s=A[r.toLowerCase()]),this.url+e+\"/\"+i+\"/\"+t+\"/{level}/{row}/{col}\"+s}return\"\"}async _fetchService(e){let t;if(this.resourceInfo)\"KVP\"===this.resourceInfo.serviceMode&&(this.url+=this.url.includes(\"?\")?\"\":\"?\"),t={ssl:!1,data:this.resourceInfo};else try{t=await this._getCapabilities(this.serviceMode,e),R(t.data)}catch{const s=\"KVP\"===this.serviceMode?\"RESTful\":\"KVP\";try{t=await this._getCapabilities(s,e),R(t.data),this.serviceMode=s}catch(r){throw new i(\"wmtslayer:unsupported-service-data\",\"Services does not support RESTful or KVP service modes.\",{error:r})}}this.resourceInfo?t.data=W(t.data):t.data=O(t.data,{serviceMode:this.serviceMode,url:this.url}),t.data&&this.read(t.data,{origin:\"service\"})}async _getCapabilities(e,r){const i=this._getCapabilitiesUrl(e);return await t(i,{...r,responseType:\"text\"})}_getTileMatrixSetById(e){const t=this.findSublayerById(this.activeLayer.id)?.tileMatrixSets?.find((t=>t.id===e));return t}_appendCustomParameters(e){return this._appendParameters(e,this.customParameters)}_appendCustomLayerParameters(e){return this._appendParameters(e,{...s(this.customParameters),...this.customLayerParameters})}_appendParameters(e,t){const r=u(e),i={...r.query,...t},s=y(i);return\"\"===s?r.path:`${r.path}?${s}`}_getCapabilitiesUrl(e){this.url=this.url.split(\"?\")[0];const t=\"KVP\"===e?`${this.url}?request=GetCapabilities&service=WMTS&version=${this.version}`:`${this.url}/${this.version}/WMTSCapabilities.xml`;return this._appendCustomParameters(t)}_getLowerCasedUrlParams(e){if(!e)return null;const t=u(e).query;if(!t)return null;const r={};return Object.keys(t).forEach((e=>{r[e.toLowerCase()]=t[e]})),r}_mergeParams(e,t){const r=this._getLowerCasedUrlParams(t);if(r){const t=Object.keys(r);t.length&&(e=e?s(e):{},t.forEach((t=>{e.hasOwnProperty(t)||F.has(t)||(e[t]=r[t])})))}return e}};function $(e,t){return e.map((e=>{const r=new _;return r.read(e,t),r}))}e([d()],V.prototype,\"dimensionMap\",void 0),e([d()],V.prototype,\"layerMap\",void 0),e([d({type:_,json:{origins:{\"web-document\":{write:{ignoreOrigin:!0}}}}})],V.prototype,\"activeLayer\",null),e([h(\"service\",\"activeLayer\",[\"layers\"])],V.prototype,\"readActiveLayerFromService\",null),e([h([\"web-document\",\"portal-item\"],\"activeLayer\",[\"wmtsInfo\"])],V.prototype,\"readActiveLayerFromItemOrWebDoc\",null),e([v([\"web-document\",\"portal-item\"],\"activeLayer\",{templateUrl:{type:String},tileInfo:{type:j},\"wmtsInfo.layerIdentifier\":{type:String},\"wmtsInfo.tileMatrixSet\":{type:String}})],V.prototype,\"writeActiveLayer\",null),e([d({type:String,value:\"\",json:{write:!0}})],V.prototype,\"copyright\",void 0),e([d({type:[\"show\",\"hide\"]})],V.prototype,\"listMode\",void 0),e([d({json:{read:!0,write:!0}})],V.prototype,\"blendMode\",void 0),e([d({json:{origins:{\"web-document\":{read:{source:[\"wmtsInfo.customParameters\",\"wmtsInfo.url\"]},write:{target:\"wmtsInfo.customParameters\"}},\"portal-item\":{read:{source:[\"wmtsInfo.customParameters\",\"wmtsInfo.url\"]},write:{target:\"wmtsInfo.customParameters\"}}}}})],V.prototype,\"customParameters\",void 0),e([h([\"portal-item\",\"web-document\"],\"customParameters\")],V.prototype,\"readCustomParameters\",null),e([d({json:{origins:{\"web-document\":{read:{source:\"wmtsInfo.customLayerParameters\"},write:{target:\"wmtsInfo.customLayerParameters\"}},\"portal-item\":{read:{source:\"wmtsInfo.customLayerParameters\"},write:{target:\"wmtsInfo.customLayerParameters\"}}}}})],V.prototype,\"customLayerParameters\",void 0),e([d({type:g,json:{write:{ignoreOrigin:!0},origins:{\"web-document\":{read:{source:\"fullExtent\"}},\"portal-item\":{read:{source:\"fullExtent\"}}}}})],V.prototype,\"fullExtent\",void 0),e([d({readOnly:!0})],V.prototype,\"fullExtents\",null),e([d({type:[\"WebTiledLayer\"]})],V.prototype,\"operationalLayerType\",void 0),e([d()],V.prototype,\"resourceInfo\",void 0),e([d()],V.prototype,\"serviceMode\",void 0),e([h([\"portal-item\",\"web-document\"],\"serviceMode\",[\"templateUrl\"])],V.prototype,\"readServiceMode\",null),e([d({type:r.ofType(_)})],V.prototype,\"sublayers\",void 0),e([h(\"service\",\"sublayers\",[\"layers\"])],V.prototype,\"readSublayersFromService\",null),e([d({readOnly:!0})],V.prototype,\"supportedSpatialReferences\",null),e([d({readOnly:!0})],V.prototype,\"tilemapCache\",null),e([d({json:{read:{source:\"title\"}}})],V.prototype,\"title\",null),e([d({json:{read:!1},readOnly:!0,value:\"wmts\"})],V.prototype,\"type\",void 0),e([d({json:{origins:{service:{read:{source:\"tileUrl\"}},\"web-document\":{read:{source:\"wmtsInfo.url\"},write:{target:\"wmtsInfo.url\"}},\"portal-item\":{read:{source:\"wmtsInfo.url\"},write:{target:\"wmtsInfo.url\"}}}}})],V.prototype,\"url\",null),e([d()],V.prototype,\"version\",void 0),V=e([f(\"esri.layers.WMTSLayer\")],V);const K=V;export{K as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsD,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAED,KAAE,GAAEE,KAAED,GAAE,KAAK,SAAO,GAAE;AAAC,SAAK,WAASA,IAAE,KAAK,SAAOD,IAAE,KAAK,SAAOE;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAED,IAAEE,IAAE;AAJrK;AAIsK,UAAMC,MAAE,UAAK,aAAL,mBAAe,MAAMF;AAAG,WAAM,CAACE,MAAGF,KAAE,KAAK,UAAQA,KAAE,KAAK,SAAO,gBAAcE,GAAE,QAAMA,GAAE,OAAKD,MAAGC,GAAE,KAAK,CAAC,KAAGD,MAAGC,GAAE,KAAK,CAAC,KAAGH,MAAGG,GAAE,KAAK,CAAC,KAAGH,MAAGG,GAAE,KAAK,CAAC,IAAE,cAAY,gBAAc;AAAA,EAAW;AAAA,EAAC,MAAM,kBAAkBH,IAAEE,IAAEC,IAAEC,IAAE;AAAC,WAAO,MAAM,EAAEA,EAAC,GAAE,KAAK,gBAAgBJ,IAAEE,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,0BAA0BH,IAAEE,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAM,EAAEA,EAAC,GAAED,GAAE,QAAMJ,IAAEI,GAAE,MAAIF,IAAEE,GAAE,MAAID;AAAE,UAAMG,KAAE,KAAK;AAAS,SAAIA,GAAE,eAAeF,EAAC,OAAI;AAAC,YAAMH,KAAE,KAAK,gBAAgBG,GAAE,OAAMA,GAAE,KAAIA,GAAE,GAAG;AAAE,UAAG,kBAAgBH,GAAE,QAAOA;AAAE,UAAG,CAACK,GAAE,aAAaF,EAAC,EAAE,QAAM;AAAA,IAAa;AAAA,EAAC;AAAC;;;ACA3R,IAAIG;AAAE,IAAIC,KAAED,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,KAAG,MAAK,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,IAAIF;AAAE,WAAO,KAAK,eAAe,YAAY,MAAIE,GAAE,aAAW,KAAK,cAAY,KAAK,WAAW,MAAM,IAAG,KAAK,eAAe,IAAI,MAAIA,GAAE,KAAG,KAAK,KAAI,KAAK,eAAe,UAAU,MAAIA,GAAE,WAAS,KAAK,YAAU,KAAK,SAAS,MAAM,IAAGA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,MAAK,EAAC,QAAO,aAAY,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,KAAI,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKG,IAAE,MAAK,EAAC,MAAK,EAAC,QAAO,WAAU,EAAC,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAEC,EAAC;AAAE,IAAMI,KAAEJ;;;ACA5sB,IAAIK;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,KAAG,MAAK,KAAK,QAAM,MAAK,KAAK,cAAY,MAAK,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,IAAID;AAAE,WAAO,KAAK,eAAe,aAAa,MAAIC,GAAE,cAAY,KAAK,cAAa,KAAK,eAAe,IAAI,MAAIA,GAAE,KAAG,KAAK,KAAI,KAAK,eAAe,WAAW,MAAIA,GAAE,YAAU,KAAK,YAAW,KAAK,eAAe,UAAU,MAAIA,GAAE,WAAS,KAAK,YAAU,KAAK,SAAS,MAAM,IAAG,KAAK,eAAe,WAAW,MAAIA,GAAE,YAAU,KAAK,YAAW,KAAK,eAAe,OAAO,MAAIA,GAAE,QAAM,KAAK,QAAOA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,KAAI,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,QAAO,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,WAAU,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,YAAW,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,YAAW,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,WAAU,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,IAAED,KAAE,EAAE,CAAC,EAAE,8BAA8B,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;;;ACAlxB,IAAIC;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,eAAa,MAAK,KAAK,KAAG,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,kBAAgB,MAAK,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,SAAK,KAAK,eAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,YAAOD,KAAEC,GAAE,cAAYC,GAAE,SAASF,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAJt7B;AAIu7B,aAAO,KAAAA,GAAE,gBAAF,mBAAe,UAAOA,GAAE,YAAY,IAAK,CAAAD,OAAGE,GAAE,SAASF,EAAC,CAAE,MAAE,KAAAC,GAAE,mBAAF,mBAAkB,IAAK,CAAAD,OAAGE,GAAE,SAASF,GAAE,UAAU,GAAI,OAAQ,CAAAA,OAAGA,QAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,QAAIA,KAAE,KAAK,KAAK,aAAa;AAAE,WAAOA,OAAIA,KAAE,KAAK,gBAAc,KAAK,aAAa,SAAO,KAAK,aAAa,CAAC,IAAE,KAAIA;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAa,IAAAD,OAAIA,GAAE,SAAS,QAAQ,KAAGC,MAAG,CAACA,GAAE,SAASD,EAAC,OAAKA,GAAE,SAAS,QAAQ,MAAIA,KAAE,WAASA,KAAGC,MAAG,CAACA,GAAE,SAASD,EAAC,KAAG,QAAQ,MAAM,6CAA2CA,EAAC,IAAE,KAAK,KAAK,eAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAJ/7C;AAIg8C,QAAIA,KAAE,KAAK,KAAK,SAAS;AAAE,WAAOA,OAAIA,OAAE,UAAK,WAAL,mBAAa,UAAO,KAAK,OAAO,UAAU,CAAC,EAAE,KAAG,KAAIA;AAAA,EAAC;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,SAAK,KAAK,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,KAAK,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,KAAK,iBAAe,KAAK,eAAe,KAAM,CAAAA,OAAGA,GAAE,OAAK,KAAK,eAAgB,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAJlwD;AAImwD,UAAMA,KAAE,IAAID;AAAE,WAAO,KAAK,eAAe,aAAa,MAAIC,GAAE,cAAY,KAAK,cAAa,KAAK,eAAe,cAAc,MAAIA,GAAE,eAAa,KAAK,gBAAc,KAAK,aAAa,MAAM,IAAG,KAAK,eAAe,aAAa,MAAIA,GAAE,cAAY,KAAK,cAAa,KAAK,eAAe,YAAY,MAAIA,GAAE,aAAW,KAAK,cAAY,KAAK,WAAW,MAAM,IAAG,KAAK,eAAe,IAAI,MAAIA,GAAE,KAAG,KAAK,KAAI,KAAK,eAAe,OAAO,MAAIA,GAAE,QAAM,KAAK,QAAO,KAAK,eAAe,SAAS,MAAIA,GAAE,UAAQ,KAAK,UAAS,KAAK,eAAe,QAAQ,MAAIA,GAAE,SAAO,KAAK,UAAQ,KAAK,OAAO,MAAM,IAAG,KAAK,eAAe,iBAAiB,MAAIA,GAAE,kBAAgB,KAAK,kBAAiB,KAAK,eAAe,gBAAgB,MAAIA,GAAE,kBAAe,UAAK,mBAAL,mBAAqB,UAAS,KAAK,eAAe,OAAO,MAAIA,GAAE,QAAM,KAAK,QAAOA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,cAAa,CAAC,YAAY,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,eAAc,CAAC,eAAc,gBAAgB,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,UAAS,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOD,EAAC,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,SAAQ,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,MAAK,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAOA,EAAC,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,iBAAgB,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,IAAEA,KAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACA1/F,IAAMI,KAAE;AAAkB,SAASC,GAAEC,IAAE;AAAC,QAAMC,KAAED,GAAE,QAAQ,UAAS,EAAE;AAAE,MAAG,CAAC,EAAE,YAAY,IAAI,YAAW,gBAAgBC,IAAE,UAAU,EAAE,eAAe,EAAE,OAAM,IAAI,EAAE,gDAA+C,uDAAsD,EAAC,MAAKD,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAJhvB;AAIivB,EAAAD,KAAEA,GAAE,QAAQ,UAAS,EAAE;AAAE,QAAME,KAAG,IAAI,YAAW,gBAAgBF,IAAE,UAAU,EAAE,iBAAgBG,KAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAIC,KAAE,EAAE,YAAWH,EAAC;AAAE,MAAG,CAACG,GAAE,OAAM,IAAI,EAAE,8CAA8C;AAAE,QAAMC,MAAE,OAAE,sBAAqBJ,EAAC,MAAxB,mBAA2B,cAAc,qBAAoBK,KAAED,MAAA,gBAAAA,GAAG,qBAAqB,QAAOE,KAAED,MAAG,MAAM,UAAU,MAAM,KAAKA,EAAC,GAAET,MAAE,KAAAG,GAAE,QAAF,mBAAO,QAAQ,UAASF,MAAE,WAASD,MAAGA,KAAE;AAAG,MAAIW,IAAEC,IAAEC,KAAEV,GAAE,aAAYW,KAAEX,MAAA,gBAAAA,GAAG;AAAI,MAAGO,MAAGA,GAAE,UAAQA,GAAE,KAAM,CAAAK,OAAG;AAAC,UAAMb,KAAE,EAAE,cAAaa,EAAC;AAAE,WAAM,CAACb,MAAG,EAAE,iBAAgB,SAAQW,IAAEX,EAAC,KAAGY,KAAEC,GAAE,WAAW,CAAC,EAAE,WAAU,SAAK,CAACb,MAAG,EAAE,iBAAgB,SAAQ,WAAUA,EAAC,KAAG,EAAE,iBAAgB,SAAQ,QAAOA,EAAC,IAAEU,KAAEG,GAAE,WAAW,CAAC,EAAE,YAAUb,MAAG,CAAC,EAAE,iBAAgB,SAAQ,OAAMA,EAAC,MAAIS,KAAEI,GAAE,WAAW,CAAC,EAAE,YAAW;AAAA,EAAG,CAAE,GAAE,CAACD,GAAE,KAAGF,GAAE,CAAAE,KAAEF,IAAEC,KAAE;AAAA,WAAkBF,GAAE,CAAAG,KAAEH,IAAEE,KAAE;AAAA,OAAU;AAAC,UAAME,KAAE,EAAE,sBAAqBX,EAAC;AAAE,IAAAU,KAAEC,MAAA,gBAAAA,GAAG,aAAa;AAAA,EAAa;AAAC,QAAMC,KAAEF,GAAE,QAAQ,QAAQ;AAAE,SAAKE,MAAG,cAAYH,KAAEC,MAAG,MAAIE,KAAE,OAAKF,KAAEA,GAAE,UAAU,GAAEE,EAAC,IAAG,UAAQH,OAAIC,MAAGE,KAAE,KAAG,KAAG,MAAKf,QAAIa,KAAEA,GAAE,QAAQ,WAAU,QAAQ;AAAG,QAAMG,KAAEC,GAAE,4CAA2Cd,EAAC,GAAEe,KAAED,GAAE,2CAA0Cd,EAAC,GAAEgB,KAAED,MAAG,UAAU,KAAKA,EAAC,IAAE,OAAKA,IAAEE,KAAE,EAAE,SAAQd,EAAC,GAAEe,KAAE,EAAE,iBAAgBf,EAAC,GAAEgB,KAAEF,GAAE,IAAK,CAAAN,OAAG;AAAC,UAAMb,KAAEgB,GAAE,cAAaH,EAAC;AAAE,WAAOV,GAAE,IAAIH,IAAEa,EAAC,GAAE,EAAEb,IAAEa,IAAEO,IAAErB,KAAEgB,EAAC;AAAA,EAAC,CAAE;AAAE,SAAM,EAAC,WAAUG,IAAE,cAAad,IAAE,UAASD,IAAE,QAAOkB,IAAE,aAAYV,IAAE,SAAQC,GAAC;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,SAAOA,GAAE,OAAO,QAAS,CAAAA,OAAG;AAJlkE;AAImkE,UAAAA,GAAE,mBAAF,mBAAkB,QAAS,CAAAA,OAAG;AAJjmE,UAAAS;AAIkmE,YAAMtB,KAAEa,GAAE;AAAS,MAAAb,MAAG,OAAKA,GAAE,SAAMsB,MAAAtB,GAAE,SAAF,gBAAAsB,IAAQ,QAAS,CAAArB,OAAG;AAJzpE,YAAAqB;AAI0pE,QAAArB,GAAE,QAAM,KAAGA,GAAE,QAAMD,GAAE,KAAIC,GAAE,aAAWsB,IAAED,MAAAtB,GAAE,qBAAF,gBAAAsB,IAAoB,MAAKrB,GAAE,QAAMH,KAAE,IAAGe,GAAE,EAAE;AAAA,MAAC,IAAIb,GAAE,MAAI;AAAA,IAAG;AAAA,EAAG,CAAE,GAAEa;AAAC;AAAC,SAASF,GAAEE,IAAE;AAAC,SAAOA,GAAE,aAAW,KAAK;AAAY;AAAC,SAAS,EAAEA,IAAEb,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,WAAW,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAE,WAAWC,EAAC;AAAE,QAAGU,GAAET,EAAC,KAAGA,GAAE,aAAWW,GAAE,QAAOX;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAEW,IAAEb,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEF,GAAE,WAAW,QAAOE,MAAI;AAAC,UAAMC,KAAEH,GAAE,WAAWE,EAAC;AAAE,IAAAS,GAAER,EAAC,KAAGA,GAAE,aAAWU,MAAGZ,GAAE,KAAKE,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAAS,EAAEY,IAAEZ,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,WAAQF,KAAE,GAAEA,KAAEC,GAAE,WAAW,QAAOD,MAAI;AAAC,UAAMG,KAAEF,GAAE,WAAWD,EAAC;AAAE,IAAAW,GAAER,EAAC,KAAGA,GAAE,aAAWU,MAAGX,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOD,GAAE,IAAK,CAAAW,OAAGA,GAAE,WAAY,EAAE,OAAO,CAAC;AAAC;AAAC,SAASG,GAAEH,IAAEb,IAAE;AAAC,SAAOa,GAAE,MAAM,GAAG,EAAE,QAAS,CAAAA,OAAG;AAAC,IAAAb,OAAIA,KAAE,EAAEa,IAAEb,EAAC;AAAA,EAAE,CAAE,GAAEA,MAAGA,GAAE;AAAW;AAAC,SAAS,EAAEa,IAAEb,IAAEC,IAAEC,IAAE;AAAC,MAAIC;AAAE,SAAO,MAAM,UAAU,MAAM,KAAKD,GAAE,UAAU,EAAE,KAAM,CAAAA,OAAG;AAAC,QAAGA,GAAE,SAAS,SAASW,EAAC,GAAE;AAAC,YAAMA,KAAE,EAAEb,IAAEE,EAAC,GAAEE,KAAES,MAAGA,GAAE;AAAY,UAAGT,OAAIH,MAAGA,GAAE,MAAM,GAAG,KAAGA,GAAE,MAAM,GAAG,EAAE,CAAC,MAAIG,GAAE,QAAOD,KAAED,IAAE;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE,CAAE,GAAEC;AAAC;AAAC,SAAS,EAAEU,IAAEb,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEY,GAAE,YAAWhB,EAAC,GAAEK,KAAE,EAAE,UAASL,EAAC;AAAE,SAAM,EAAC,IAAGa,IAAE,YAAW,EAAEb,EAAC,GAAE,aAAYoB,GAAEpB,EAAC,GAAE,aAAYI,IAAE,SAAQC,IAAE,QAAO,EAAEL,IAAEE,EAAC,GAAE,OAAMc,GAAE,SAAQhB,EAAC,GAAE,gBAAewB,GAAErB,IAAEH,IAAEC,EAAC,EAAC;AAAC;AAAC,SAAS,EAAEY,IAAEb,IAAE;AAJntG;AAIotG,QAAMC,KAAE,CAAC,GAAEC,MAAE,KAAAW,GAAE,aAAF,mBAAY,IAAIb;AAAG,MAAG,CAACE,GAAE,QAAO;AAAK,QAAMC,KAAE,EAAE,eAAcD,EAAC,GAAEE,KAAE,EAAE,aAAYF,EAAC;AAAE,MAAIG,IAAEC,IAAEC,IAAEC;AAAE,SAAOJ,GAAE,WAASC,KAAEW,GAAE,cAAaZ,GAAE,CAAC,CAAC,GAAEE,KAAE,EAAE,WAAUF,GAAE,CAAC,CAAC,KAAG,EAAE,SAAQA,GAAE,CAAC,CAAC,IAAGA,GAAE,SAAO,MAAIG,KAAES,GAAE,cAAaZ,GAAE,CAAC,CAAC,GAAEI,KAAE,EAAE,WAAUJ,GAAE,CAAC,CAAC,KAAG,EAAE,SAAQA,GAAE,CAAC,CAAC,IAAGS,GAAE,aAAa,IAAIb,IAAE,EAAC,YAAWM,IAAE,aAAYE,GAAC,CAAC,GAAEL,GAAE,QAAS,CAAAU,OAAG;AAAC,QAAIb,KAAEa,GAAE,aAAa,UAAU;AAAE,QAAG,WAASA,GAAE,aAAa,cAAc,GAAE;AAAC,UAAGR,MAAGC,GAAE,OAAO,KAAGN,GAAE,SAAS,MAAIK,KAAE,GAAG,EAAE,CAAAL,KAAEA,GAAE,QAAQ,MAAIK,KAAE,KAAI,kBAAkB;AAAA,WAAM;AAAC,cAAMQ,KAAEb,GAAE,YAAY,EAAE,QAAQ,MAAIK,GAAE,YAAY,IAAE,GAAG;AAAE,QAAAQ,KAAE,OAAKb,KAAEA,GAAE,UAAU,GAAEa,EAAC,IAAE,qBAAmBb,GAAE,UAAUa,KAAER,GAAE,SAAO,CAAC;AAAA,MAAE;AAAC,UAAGE,MAAGC,GAAE,OAAO,KAAGR,GAAE,SAAS,MAAIO,KAAE,GAAG,EAAE,CAAAP,KAAEA,GAAE,QAAQ,MAAIO,KAAE,KAAI,mBAAmB;AAAA,WAAM;AAAC,cAAMM,KAAEb,GAAE,YAAY,EAAE,QAAQ,MAAIO,GAAE,YAAY,IAAE,GAAG;AAAE,QAAAM,KAAE,OAAKb,KAAEA,GAAE,UAAU,GAAEa,EAAC,IAAE,sBAAoBb,GAAE,UAAUa,KAAEN,GAAE,SAAO,CAAC;AAAA,MAAE;AAAC,MAAAN,GAAE,KAAK,EAAC,UAASD,IAAE,QAAOa,GAAE,aAAa,QAAQ,GAAE,cAAa,OAAM,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAEZ;AAAC;AAAC,SAASc,GAAEF,IAAEb,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAJpoI;AAIqoI,QAAMC,KAAE,EAAEM,IAAEb,IAAEE,EAAC;AAAE,MAAG,GAAEK,MAAA,gBAAAA,GAAG,UAAO,GAAG,QAAM;AAAG,QAAK,EAAC,cAAaC,GAAC,IAAEK,IAAEf,MAAE,KAAAU,GAAE,IAAIR,EAAC,EAAE,eAAT,mBAAsB,IAAGD,OAAE,KAAAS,GAAE,IAAIR,EAAC,EAAE,gBAAT,mBAAuB;AAAG,SAAOO,GAAEF,KAAEE,GAAE,MAAM,EAAE,SAAS,QAAQ,eAAcJ,MAAG,EAAE,EAAE,QAAQ,uBAAsBF,MAAG,EAAE,EAAE,QAAQ,oBAAmBG,EAAC,EAAE,QAAQ,iBAAgB,KAAGC,EAAC,EAAE,QAAQ,iBAAgB,KAAGC,EAAC,EAAE,QAAQ,wBAAuBR,EAAC,EAAE,QAAQ,yBAAwBC,GAAC;AAAC;AAAC,SAAS,EAAEc,IAAEb,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEW,IAAEb,EAAC,GAAEG,KAAED,MAAA,gBAAAA,GAAG,OAAQ,CAAAW,OAAGA,GAAE,WAASZ;AAAI,WAAOE,MAAA,gBAAAA,GAAG,UAAOA,KAAED,OAAI,CAAC;AAAC;AAAC,SAAS,EAAEW,IAAEb,IAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,cAAaC,GAAC,IAAEU,IAAET,KAAE,EAAES,IAAEb,EAAC;AAAE,MAAIK,KAAE;AAAG,MAAGD,MAAGA,GAAE,SAAO,GAAE;AAAC,UAAMS,KAAEV,GAAE,IAAIH,EAAC,EAAE,cAAYG,GAAE,IAAIH,EAAC,EAAE,WAAW,CAAC,GAAEM,KAAEH,GAAE,IAAIH,EAAC,EAAE,eAAaG,GAAE,IAAIH,EAAC,EAAE,YAAY,CAAC;AAAE,IAAAK,KAAED,GAAE,CAAC,EAAE,UAASC,GAAE,QAAQ,MAAM,MAAIA,GAAE,SAAO,MAAIA,KAAEA,GAAE,MAAM,GAAEA,GAAE,SAAO,CAAC,IAAGA,KAAEA,GAAE,QAAQ,eAAcH,EAAC,GAAEG,KAAEA,GAAE,QAAQ,uBAAsBJ,EAAC,GAAEI,KAAEA,GAAE,QAAQ,oBAAmB,SAAS,GAAEA,KAAEA,GAAE,QAAQ,iBAAgB,OAAO,GAAEA,KAAEA,GAAE,QAAQ,iBAAgB,OAAO,GAAEA,KAAEA,GAAE,QAAQ,wBAAuBQ,EAAC,GAAER,KAAEA,GAAE,QAAQ,yBAAwBC,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,EAAEQ,IAAE;AAAC,QAAMb,KAAE,EAAE,oBAAmBa,EAAC,GAAEZ,KAAED,KAAEgB,GAAE,eAAchB,EAAC,EAAE,MAAM,GAAG,IAAE,CAAC,QAAO,KAAK,GAAEE,KAAEF,KAAEgB,GAAE,eAAchB,EAAC,EAAE,MAAM,GAAG,IAAE,CAAC,OAAM,IAAI;AAAE,SAAM,EAAC,MAAK,WAAWC,GAAE,CAAC,CAAC,GAAE,MAAK,WAAWA,GAAE,CAAC,CAAC,GAAE,MAAK,WAAWC,GAAE,CAAC,CAAC,GAAE,MAAK,WAAWA,GAAE,CAAC,CAAC,GAAE,kBAAiB,EAAC,MAAK,KAAI,EAAC;AAAC;AAAC,SAASkB,GAAEP,IAAE;AAAC,QAAMb,KAAE,CAAC;AAAE,SAAOI,GAAES,IAAE,EAAC,aAAY,CAAAA,OAAG;AAAC,QAAG,CAACA,GAAE,aAAa,KAAK,EAAE;AAAO,UAAMZ,KAAEY,GAAE,aAAa,KAAK,EAAE,YAAY,GAAEX,KAAE,EAAED,EAAC,GAAEE,KAAEF,GAAE,SAAS,MAAM,KAAGG,GAAEF,GAAE,IAAI;AAAE,QAAIE,IAAEG,IAAEC,IAAEV;AAAE,IAAAM,GAAES,IAAE,EAAC,aAAY,CAAAA,OAAG;AAAC,OAACT,IAAEG,EAAC,IAAEM,GAAE,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,OAAO,WAAWA,EAAC,CAAE,GAAEV,OAAI,CAACC,IAAEG,EAAC,IAAE,CAACA,IAAEH,EAAC;AAAA,IAAE,GAAE,aAAY,CAAAS,OAAG;AAAC,OAACL,IAAEV,EAAC,IAAEe,GAAE,YAAY,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,OAAO,WAAWA,EAAC,CAAE,GAAEV,OAAI,CAACK,IAAEV,EAAC,IAAE,CAACA,IAAEU,EAAC;AAAA,IAAE,EAAC,CAAC,GAAER,GAAE,KAAK,EAAC,MAAKI,IAAE,MAAKG,IAAE,MAAKC,IAAE,MAAKV,IAAE,kBAAiBI,GAAC,CAAC;AAAA,EAAC,EAAC,CAAC,GAAEF;AAAC;AAAC,SAAS,EAAEa,IAAEb,IAAE;AAAC,SAAO,EAAE,SAAQa,EAAC,EAAE,IAAK,CAAAA,OAAG;AAAC,UAAMZ,KAAE,EAAE,aAAYY,EAAC,GAAEX,KAAE,EAAE,YAAWW,EAAC,GAAEV,KAAED,KAAE,EAAE,WAAUA,EAAC,IAAE,CAAC;AAAE,QAAIE,KAAEH,MAAGA,GAAE,aAAa,YAAY;AAAE,IAAAD,OAAII,KAAEA,MAAGA,GAAE,QAAQ,WAAU,QAAQ;AAAG,WAAM,EAAC,UAASY,GAAE,YAAWH,EAAC,GAAE,IAAGG,GAAE,cAAaH,EAAC,GAAE,WAAU,WAASA,GAAE,aAAa,WAAW,GAAE,UAASV,IAAE,WAAUC,IAAE,OAAMY,GAAE,SAAQH,EAAC,EAAC;AAAA,EAAC,CAAE;AAAC;AAAC,SAASW,GAAEX,IAAEb,IAAEC,IAAE;AAAC,SAAO,EAAE,qBAAoBD,EAAC,EAAE,IAAK,CAAAA,OAAG,EAAEa,IAAEb,IAAEC,EAAC,CAAE;AAAC;AAAC,SAAS,EAAEY,IAAEb,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,iBAAgBF,EAAC,EAAE,aAAYG,KAAE,EAAE,cAAaH,EAAC,GAAEI,KAAEH,GAAE,KAAM,CAAAY,OAAG;AAAC,UAAMb,KAAE,EAAE,cAAaa,EAAC,GAAEZ,KAAED,MAAGA,GAAE;AAAY,WAAM,CAAC,EAAEC,OAAIC,MAAGA,GAAE,MAAM,GAAG,KAAGA,GAAE,MAAM,GAAG,EAAE,CAAC,MAAID;AAAA,EAAE,CAAE,GAAEI,KAAE,EAAE,uBAAsBL,EAAC,GAAEM,KAAED,MAAG,EAAE,oBAAmBA,EAAC,GAAEE,KAAE,oBAAI;AAAI,MAAGD,MAAA,gBAAAA,GAAG,OAAO,YAAUE,MAAKF,IAAE;AAAC,UAAMO,KAAE,EAAE,cAAaL,EAAC,EAAE,aAAYR,KAAE,CAAC,EAAE,cAAaQ,EAAC,EAAE,aAAYP,KAAE,CAAC,EAAE,cAAaO,EAAC,EAAE,aAAYN,KAAE,CAAC,EAAE,cAAaM,EAAC,EAAE,aAAYL,KAAE,CAAC,EAAE,cAAaK,EAAC,EAAE;AAAY,IAAAD,GAAE,IAAIM,IAAE,EAAC,QAAOX,IAAE,QAAOC,IAAE,QAAOH,IAAE,QAAOC,GAAC,CAAC;AAAA,EAAC;AAAC,QAAMH,KAAEkB,GAAE,gBAAeZ,EAAC,EAAE,YAAY,GAAEL,MAAE0B,GAAErB,IAAEN,EAAC,GAAEW,KAAEV,IAAE,kBAAiBW,KAAE,EAAE,cAAaN,EAAC,GAAEO,KAAE,CAAC,SAASK,GAAE,aAAYN,EAAC,GAAE,EAAE,GAAE,SAASM,GAAE,cAAaN,EAAC,GAAE,EAAE,CAAC,GAAEgB,KAAE,CAAC;AAAE,MAAGvB,GAAE,OAAO,CAAAA,GAAE,QAAS,CAACU,IAAEb,OAAI;AAAC,UAAMC,KAAE,EAAE,cAAa,cAAaY,IAAET,EAAC;AAAE,IAAAsB,GAAE,KAAK,EAAEzB,IAAEH,IAAEE,IAAEE,IAAEK,EAAC,CAAC;AAAA,EAAC,CAAE;AAAA,OAAM;AAAC,MAAE,cAAaH,EAAC,EAAE,QAAS,CAACS,IAAEb,OAAI;AAAC,MAAA0B,GAAE,KAAK,EAAEb,IAAEf,IAAEE,IAAEE,IAAEK,EAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,QAAMO,KAAE,EAAED,IAAET,IAAEL,KAAEY,IAAEe,GAAE,CAAC,CAAC,EAAE,OAAO,GAAEX,KAAE,IAAIQ,GAAE,EAAC,KAAI,IAAG,kBAAiBd,IAAE,MAAKE,IAAE,QAAOZ,KAAE,MAAK2B,GAAC,CAAC,EAAE,OAAO;AAAE,SAAM,EAAC,IAAGxB,IAAE,YAAWY,IAAE,UAASC,GAAC;AAAC;AAAC,SAAS,EAAEF,IAAE;AAAC,EAAAA,KAAEA,GAAE,YAAY;AAAE,MAAIZ,KAAE,SAASY,GAAE,MAAM,GAAG,EAAE,IAAI,GAAE,EAAE;AAAE,aAASZ,MAAG,SAAOA,OAAIA,KAAE;AAAQ,QAAMC,KAAEyB,GAAEd,EAAC;AAAE,SAAO,EAAEX,EAAC,MAAID,KAAEC,KAAG,EAAC,MAAKD,GAAC;AAAC;AAAC,SAASwB,GAAEZ,IAAEb,IAAE;AAAC,SAAO,EAAE,EAAE,cAAaa,EAAC,GAAEb,EAAC;AAAC;AAAC,SAAS,EAAEa,IAAEb,IAAE;AAAC,QAAMC,KAAE,EAAED,EAAC,GAAE,CAACE,IAAEE,EAAC,IAAEY,GAAE,iBAAgBH,EAAC,EAAE,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAG,WAAWA,EAAC,CAAE,GAAEP,KAAEN,GAAE,SAAS,MAAM,KAAGI,GAAEH,GAAE,IAAI;AAAE,SAAO,IAAIe,GAAEV,KAAE,EAAC,GAAEF,IAAE,GAAEF,IAAE,kBAAiBD,GAAC,IAAE,EAAC,GAAEC,IAAE,GAAEE,IAAE,kBAAiBH,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEY,IAAEb,IAAEC,IAAEE,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAE,eAAcL,EAAC;AAAE,MAAIM,IAAEC,IAAEC,IAAEV,IAAEC,KAAEU;AAAE,MAAGJ,OAAIC,KAAEU,GAAE,eAAcX,EAAC,EAAE,MAAM,GAAG,GAAEE,KAAES,GAAE,eAAcX,EAAC,EAAE,MAAM,GAAG,IAAGC,MAAGA,GAAE,SAAO,KAAGC,MAAGA,GAAE,SAAO,EAAE,CAAAC,KAAE,WAAWF,GAAE,CAAC,CAAC,GAAEP,MAAE,WAAWO,GAAE,CAAC,CAAC,GAAER,KAAE,WAAWS,GAAE,CAAC,CAAC,GAAEE,KAAE,WAAWF,GAAE,CAAC,CAAC;AAAA,OAAM;AAAC,UAAMM,KAAE,EAAE,cAAab,EAAC,GAAEE,KAAE,SAASc,GAAE,eAAcH,EAAC,GAAE,EAAE,GAAER,KAAE,SAASW,GAAE,gBAAeH,EAAC,GAAE,EAAE;AAAE,IAAAL,KAAEP,GAAE,GAAEQ,KAAER,GAAE,GAAEH,KAAEU,KAAEN,KAAEC,GAAE,CAAC,IAAEC,GAAE,YAAWL,MAAEU,KAAEJ,KAAEF,GAAE,CAAC,IAAEC,GAAE;AAAA,EAAU;AAAC,SAAOwB,GAAEf,IAAEZ,GAAE,kBAAiBA,EAAC,IAAE,IAAIe,GAAEjB,KAAES,IAAEC,IAAEX,IAAEG,GAAE,gBAAgB,IAAE,IAAIe,GAAER,IAAET,KAAED,IAAEW,IAAER,GAAE,gBAAgB;AAAC;AAAC,SAAS2B,GAAEf,IAAEb,IAAEC,IAAE;AAAC,SAAM,YAAUY,MAAGT,GAAEJ,GAAE,IAAI,KAAG,EAAEC,GAAE,iBAAiB,gBAAcA,GAAE,IAAE,OAAKA,GAAE,KAAG;AAAI;AAAC,IAAI;AAAE,SAAS0B,GAAEd,IAAE;AAAC,SAAOA,GAAE,SAAS,OAAO,KAAGA,GAAE,SAAS,QAAQ,IAAE,EAAE,QAAMA,GAAE,SAAS,OAAO,KAAGA,GAAE,SAAS,QAAQ,IAAE,EAAE,QAAMA,GAAE,SAAS,OAAO,KAAGA,GAAE,SAAS,QAAQ,IAAE,EAAE,QAAM;AAAI;AAAC,SAAS,EAAEA,IAAEb,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAEJ,EAAC,GAAEK,KAAEW,GAAE,cAAaH,EAAC;AAAE,MAAIP,KAAE,WAAWU,GAAE,oBAAmBH,EAAC,CAAC;AAAE,QAAML,KAAEe,GAAEnB,GAAE,MAAKE,IAAEJ,EAAC;AAAE,EAAAI,MAAG,KAAGR;AAAE,QAAMC,MAAE,CAACiB,GAAE,eAAcH,EAAC,GAAEJ,KAAE,CAACO,GAAE,gBAAeH,EAAC,GAAE,EAAC,QAAOH,KAAEX,MAAE,GAAE,QAAOY,KAAEF,KAAE,GAAE,QAAOoB,KAAE,GAAE,QAAOC,KAAE,EAAC,IAAE3B,GAAE,IAAIE,EAAC,KAAG,CAAC,GAAE,EAAC,GAAEO,IAAE,GAAEmB,GAAC,IAAE,EAAElB,IAAEb,EAAC;AAAE,SAAO,IAAID,GAAE,EAAC,MAAK,CAAC8B,IAAEnB,EAAC,GAAE,OAAMT,IAAE,YAAWI,IAAE,QAAO,CAACO,IAAEmB,EAAC,GAAE,OAAMzB,IAAE,YAAWE,IAAE,MAAK,CAACsB,IAAEnB,EAAC,EAAC,CAAC;AAAC;AAAC,SAASY,GAAEV,IAAEb,IAAEE,IAAE;AAAC,MAAIC;AAAE,SAAOA,KAAEA,GAAE,eAAe,KAAGU,EAAC,IAAEV,GAAE,OAAOA,GAAEU,EAAC,CAAC,IAAE,mBAAiBX,KAAE,UAAQ,KAAK,KAAG,MAAIH,GAAEc,EAAC,EAAE,iBAAgB,IAAEb,KAAE,OAAKG;AAAC;AAAC,CAAC,SAASU,IAAE;AAAC,EAAAA,GAAEA,GAAE,QAAM,IAAI,IAAE,SAAQA,GAAEA,GAAE,QAAM,IAAI,IAAE,SAAQA,GAAEA,GAAE,QAAM,IAAI,IAAE;AAAO,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACAxsO,IAAMmB,KAAE,EAAC,aAAY,QAAO,cAAa,QAAO,eAAc,QAAO,eAAc,QAAO,aAAY,QAAO,cAAa,SAAQ,aAAY,QAAO,aAAY,QAAO,cAAa,QAAO,gBAAe,IAAG,iBAAgB,IAAG,iBAAgB,GAAE;AAAnP,IAAqPC,KAAE,oBAAI,IAAI,CAAC,WAAU,WAAU,WAAU,SAAQ,SAAQ,UAAS,iBAAgB,cAAa,WAAU,SAAS,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAEC,GAAEC,GAAE,EAAE,EAAEC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,YAAU,IAAG,KAAK,mBAAiB,MAAK,KAAK,wBAAsB,MAAK,KAAK,aAAW,MAAK,KAAK,uBAAqB,iBAAgB,KAAK,eAAa,MAAK,KAAK,cAAY,WAAU,KAAK,YAAU,MAAK,KAAK,OAAK,QAAO,KAAK,UAAQ,SAAQ,KAAK,WAAW,CAACC,GAAG,MAAI,KAAK,aAAc,CAACD,IAAEF,OAAI;AAAC,MAAAA,OAAIA,GAAE,QAAM,OAAME,OAAIA,GAAE,QAAM;AAAA,IAAK,GAAG,CAAC,GAAEE,GAAG,MAAI,KAAK,WAAW,aAAa,CAAC,EAAC,MAAKF,GAAC,MAAI;AAAC,MAAAA,GAAE,QAAM;AAAA,IAAI,GAAG,CAAC,GAAEE,GAAG,MAAI,KAAK,WAAW,gBAAgB,CAAC,EAAC,MAAKF,GAAC,MAAI;AAAC,MAAAA,GAAE,QAAM;AAAA,IAAI,GAAG,CAAC,GAAEC,GAAG,MAAI,KAAK,WAAY,CAACD,IAAEF,OAAI;AAAC,UAAGA,GAAE,YAAUK,MAAKL,GAAE,CAAAK,GAAE,QAAM;AAAK,UAAGH,GAAE,YAAUG,MAAKH,GAAE,CAAAG,GAAE,QAAM;AAAA,IAAI,GAAG,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEF,IAAE;AAAC,WAAM,YAAU,OAAOE,KAAE,EAAC,KAAIA,IAAE,GAAGF,GAAC,IAAEE;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAG,UAAQ,KAAK,eAAa,cAAY,KAAK,YAAY,QAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,MAAM,EAAC,GAAEA,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,cAAcA,EAAC,CAAE,EAAE,MAAO,CAAAA,OAAG;AAAC,YAAM,EAAEA,EAAC,GAAE,IAAI,EAAE,sCAAqC,2CAA0C,EAAC,OAAMA,GAAC,CAAC;AAAA,IAAC,CAAE,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAE,YAAQ,MAAM,4CAA4C;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYA,IAAE;AAAC,SAAK,KAAK,eAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEF,IAAEK,IAAE;AAAC,SAAK,gBAAc,KAAK,cAAY,IAAI;AAAG,QAAIC,KAAEN,GAAE,OAAO,KAAM,CAAAE,OAAGA,GAAE,OAAK,KAAK,YAAY,EAAG;AAAE,WAAOI,OAAIA,KAAEN,GAAE,OAAO,CAAC,IAAG,KAAK,YAAY,KAAKM,IAAED,EAAC,GAAE,KAAK;AAAA,EAAW;AAAA,EAAC,gCAAgCH,IAAEF,IAAE;AAAC,UAAK,EAAC,aAAYK,IAAE,UAASC,GAAC,IAAEN,IAAEO,KAAEF,KAAE,KAAK,wBAAwBA,EAAC,IAAE,MAAKD,KAAEE,MAAA,gBAAAA,GAAG;AAAgB,QAAIE,KAAE;AAAK,UAAML,KAAEG,MAAA,gBAAAA,GAAG;AAAc,IAAAH,OAAI,MAAM,QAAQA,EAAC,IAAEA,GAAE,WAASK,KAAEL,GAAE,CAAC,KAAGK,KAAEL;AAAG,UAAMM,KAAEF,MAAA,gBAAAA,GAAG,QAAOG,KAAEH,MAAA,gBAAAA,GAAG;AAAM,WAAO,IAAI,EAAE,EAAC,IAAGH,IAAE,aAAYK,IAAE,SAAQC,IAAE,iBAAgBF,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBN,IAAEF,IAAEK,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAY,IAAAP,GAAE,cAAY,KAAK,eAAeO,GAAE,IAAGA,GAAE,iBAAgBA,GAAE,aAAYA,GAAE,OAAO;AAAE,UAAMH,KAAE,EAAE,0BAAyBG,EAAC;AAAE,IAAAP,GAAE,WAASI,KAAEA,GAAE,OAAOE,EAAC,IAAE,MAAKN,GAAE,WAAS,EAAC,GAAGA,GAAE,UAAS,iBAAgBO,GAAE,IAAG,eAAcA,GAAE,gBAAe;AAAA,EAAC;AAAA,EAAC,qBAAqBL,IAAEF,IAAE;AAAC,UAAMK,KAAEL,GAAE;AAAS,WAAOK,KAAE,KAAK,aAAaA,GAAE,kBAAiBA,GAAE,GAAG,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,YAAY;AAAA,EAAW;AAAA,EAAC,gBAAgBH,IAAEF,IAAE;AAAC,WAAOA,GAAE,YAAY,SAAS,GAAG,IAAE,QAAM;AAAA,EAAS;AAAA,EAAC,yBAAyBE,IAAEF,IAAEK,IAAE;AAAC,WAAO,EAAEL,GAAE,QAAOK,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,6BAA4B;AAJ/wI;AAIgxI,aAAO,UAAK,YAAY,mBAAjB,mBAAiC,IAAK,CAAAH,OAAC;AAJ9zI,UAAAS;AAIg0I,cAAAA,MAAAT,GAAE,aAAF,gBAAAS,IAAY;AAAA,OAAmB,UAAU,OAAO,OAAI,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAJz4I;AAI04I,UAAMT,MAAE,gBAAK,gBAAL,mBAAkB,kBAAlB,mBAAiC;AAAS,WAAOA,KAAE,IAAIC,GAAED,EAAC,IAAE;AAAA,EAAM;AAAA,EAAC,IAAI,QAAO;AAJh+I;AAIi+I,aAAO,UAAK,gBAAL,mBAAkB,UAAO;AAAA,EAAO;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,gBAAgB,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,IAAIA,IAAE;AAAC,IAAAA,MAAG,QAAMA,GAAE,OAAO,EAAE,IAAE,KAAK,KAAK,OAAMA,GAAE,MAAM,GAAE,EAAE,CAAC,IAAE,KAAK,KAAK,OAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAJhsJ;AAIisJ,UAAMF,KAAE,KAAK,eAAe,KAAK,YAAY,IAAG,KAAK,YAAY,iBAAgB,KAAK,YAAY,aAAY,KAAK,YAAY,OAAO,GAAEK,MAAE,UAAK,sBAAsBH,GAAE,eAAe,MAA5C,mBAA+C,UAASI,KAAEJ,GAAE,YAAWK,KAAE,IAAIR,GAAE,EAAC,iBAAgBG,GAAE,IAAG,eAAcA,GAAE,iBAAgB,KAAI,KAAK,IAAG,CAAC;AAAE,WAAO,KAAK,0BAAwBK,GAAE,wBAAsB,KAAK,wBAAuB,KAAK,qBAAmBA,GAAE,mBAAiB,KAAK,mBAAkB,IAAIK,GAAE,EAAC,YAAWN,IAAE,aAAYN,IAAE,UAASK,IAAE,UAASE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUL,IAAEG,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAWL,IAAEG,IAAEC,EAAC,GAAE,EAAC,MAAKF,GAAC,IAAE,MAAMS,GAAEN,IAAE,EAAC,cAAa,QAAO,CAAC;AAAE,WAAOH;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBF,IAAEG,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAWL,IAAEG,IAAEC,EAAC,GAAE,EAAC,MAAKF,GAAC,IAAE,MAAMS,GAAEN,IAAE,EAAC,cAAa,OAAM,CAAC;AAAE,WAAOL,GAAEE,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBL,IAAE;AAJz6K;AAI06K,YAAO,UAAK,cAAL,mBAAgB,KAAM,CAAAF,OAAGA,GAAE,OAAKE;AAAA,EAAG;AAAA,EAAC,WAAWA,IAAEF,IAAEK,IAAE;AAJt+K;AAIu+K,UAAMC,MAAE,gBAAK,sBAAsB,KAAK,YAAY,eAAe,MAA3D,mBAA8D,aAA9D,mBAAwE,KAAKJ,KAAGK,KAAED,KAAEA,GAAE,aAAWA,GAAE,aAAW,GAAGA,GAAE,KAAK,KAAG,GAAGJ,EAAC;AAAG,QAAIE,KAAE,KAAK,eAAa,KAAGU,GAAE,EAAC,cAAa,KAAK,cAAa,UAAS,KAAK,SAAQ,GAAE,KAAK,YAAY,IAAG,KAAK,YAAY,iBAAgB,KAAK,YAAY,aAAY,KAAK,YAAY,SAAQP,IAAEP,IAAEK,EAAC;AAAE,QAAG,CAACD,IAAE;AAAC,MAAAA,KAAE,KAAK,eAAe,KAAK,YAAY,IAAG,KAAK,YAAY,iBAAgB,KAAK,YAAY,aAAY,KAAK,YAAY,OAAO,EAAE,QAAQ,eAAcG,EAAC,EAAE,QAAQ,aAAY,GAAGP,EAAC,EAAE,EAAE,QAAQ,aAAY,GAAGK,EAAC,EAAE;AAAA,IAAC;AAAC,WAAOD,KAAE,KAAK,6BAA6BA,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEF,IAAEK,IAAEC,IAAE;AAAC,QAAG,CAAC,KAAK,cAAa;AAAC,YAAMD,KAAE,EAAE,EAAC,cAAa,KAAK,cAAa,UAAS,KAAK,SAAQ,GAAEH,IAAEF,IAAEM,EAAC;AAAE,UAAGD,GAAE,QAAOA;AAAA,IAAC;AAAC,QAAG,UAAQ,KAAK,YAAY,QAAO,KAAK,MAAI,2BAAyB,KAAK,UAAQ,4BAA0BH,KAAE,YAAUI,KAAE,aAAWD,KAAE,oBAAkBL,KAAE;AAAkD,QAAG,cAAY,KAAK,aAAY;AAAC,UAAIO,KAAE;AAAG,aAAOX,GAAES,GAAE,YAAY,CAAC,MAAIE,KAAEX,GAAES,GAAE,YAAY,CAAC,IAAG,KAAK,MAAIH,KAAE,MAAII,KAAE,MAAIN,KAAE,yBAAuBO;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,MAAM,cAAcL,IAAE;AAAC,QAAIF;AAAE,QAAG,KAAK,aAAa,WAAQ,KAAK,aAAa,gBAAc,KAAK,OAAK,KAAK,IAAI,SAAS,GAAG,IAAE,KAAG,MAAKA,KAAE,EAAC,KAAI,OAAG,MAAK,KAAK,aAAY;AAAA,QAAO,KAAG;AAAC,MAAAA,KAAE,MAAM,KAAK,iBAAiB,KAAK,aAAYE,EAAC,GAAEH,GAAEC,GAAE,IAAI;AAAA,IAAC,QAAM;AAAC,YAAMO,KAAE,UAAQ,KAAK,cAAY,YAAU;AAAM,UAAG;AAAC,QAAAP,KAAE,MAAM,KAAK,iBAAiBO,IAAEL,EAAC,GAAEH,GAAEC,GAAE,IAAI,GAAE,KAAK,cAAYO;AAAA,MAAC,SAAOF,IAAE;AAAC,cAAM,IAAI,EAAE,sCAAqC,2DAA0D,EAAC,OAAMA,GAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAC,SAAK,eAAaL,GAAE,OAAK,EAAEA,GAAE,IAAI,IAAEA,GAAE,OAAK,EAAEA,GAAE,MAAK,EAAC,aAAY,KAAK,aAAY,KAAI,KAAK,IAAG,CAAC,GAAEA,GAAE,QAAM,KAAK,KAAKA,GAAE,MAAK,EAAC,QAAO,UAAS,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBE,IAAEG,IAAE;AAAC,UAAMC,KAAE,KAAK,oBAAoBJ,EAAC;AAAE,WAAO,MAAMW,GAAEP,IAAE,EAAC,GAAGD,IAAE,cAAa,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBH,IAAE;AAJjyO;AAIkyO,UAAMF,MAAE,gBAAK,iBAAiB,KAAK,YAAY,EAAE,MAAzC,mBAA4C,mBAA5C,mBAA4D,KAAM,CAAAA,OAAGA,GAAE,OAAKE;AAAI,WAAOF;AAAA,EAAC;AAAA,EAAC,wBAAwBE,IAAE;AAAC,WAAO,KAAK,kBAAkBA,IAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,6BAA6BA,IAAE;AAAC,WAAO,KAAK,kBAAkBA,IAAE,EAAC,GAAG,EAAE,KAAK,gBAAgB,GAAE,GAAG,KAAK,sBAAqB,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEF,IAAE;AAAC,UAAMK,KAAE,EAAEH,EAAC,GAAEI,KAAE,EAAC,GAAGD,GAAE,OAAM,GAAGL,GAAC,GAAEO,KAAE,EAAED,EAAC;AAAE,WAAM,OAAKC,KAAEF,GAAE,OAAK,GAAGA,GAAE,IAAI,IAAIE,EAAC;AAAA,EAAE;AAAA,EAAC,oBAAoBL,IAAE;AAAC,SAAK,MAAI,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC;AAAE,UAAMF,KAAE,UAAQE,KAAE,GAAG,KAAK,GAAG,iDAAiD,KAAK,OAAO,KAAG,GAAG,KAAK,GAAG,IAAI,KAAK,OAAO;AAAwB,WAAO,KAAK,wBAAwBF,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBE,IAAE;AAAC,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMF,KAAE,EAAEE,EAAC,EAAE;AAAM,QAAG,CAACF,GAAE,QAAO;AAAK,UAAMK,KAAE,CAAC;AAAE,WAAO,OAAO,KAAKL,EAAC,EAAE,QAAS,CAAAE,OAAG;AAAC,MAAAG,GAAEH,GAAE,YAAY,CAAC,IAAEF,GAAEE,EAAC;AAAA,IAAC,CAAE,GAAEG;AAAA,EAAC;AAAA,EAAC,aAAaH,IAAEF,IAAE;AAAC,UAAMK,KAAE,KAAK,wBAAwBL,EAAC;AAAE,QAAGK,IAAE;AAAC,YAAML,KAAE,OAAO,KAAKK,EAAC;AAAE,MAAAL,GAAE,WAASE,KAAEA,KAAE,EAAEA,EAAC,IAAE,CAAC,GAAEF,GAAE,QAAS,CAAAA,OAAG;AAAC,QAAAE,GAAE,eAAeF,EAAC,KAAGH,GAAE,IAAIG,EAAC,MAAIE,GAAEF,EAAC,IAAEK,GAAEL,EAAC;AAAA,MAAE,CAAE;AAAA,IAAE;AAAC,WAAOE;AAAA,EAAC;AAAC;AAAE,SAAS,EAAEA,IAAEF,IAAE;AAAC,SAAOE,GAAE,IAAK,CAAAA,OAAG;AAAC,UAAMG,KAAE,IAAI;AAAE,WAAOA,GAAE,KAAKH,IAAEF,EAAC,GAAEK;AAAA,EAAC,CAAE;AAAC;AAAC,EAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,SAAQ,EAAC,gBAAe,EAAC,OAAM,EAAC,cAAa,KAAE,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,WAAU,eAAc,CAAC,QAAQ,CAAC,CAAC,GAAEA,GAAE,WAAU,8BAA6B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,gBAAe,aAAa,GAAE,eAAc,CAAC,UAAU,CAAC,CAAC,GAAEA,GAAE,WAAU,mCAAkC,IAAI,GAAE,EAAE,CAACO,GAAE,CAAC,gBAAe,aAAa,GAAE,eAAc,EAAC,aAAY,EAAC,MAAK,OAAM,GAAE,UAAS,EAAC,MAAKU,GAAC,GAAE,4BAA2B,EAAC,MAAK,OAAM,GAAE,0BAAyB,EAAC,MAAK,OAAM,EAAC,CAAC,CAAC,GAAEjB,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,OAAM,IAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,gBAAe,EAAC,MAAK,EAAC,QAAO,CAAC,6BAA4B,cAAc,EAAC,GAAE,OAAM,EAAC,QAAO,4BAA2B,EAAC,GAAE,eAAc,EAAC,MAAK,EAAC,QAAO,CAAC,6BAA4B,cAAc,EAAC,GAAE,OAAM,EAAC,QAAO,4BAA2B,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,eAAc,cAAc,GAAE,kBAAkB,CAAC,GAAEA,GAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,gBAAe,EAAC,MAAK,EAAC,QAAO,iCAAgC,GAAE,OAAM,EAAC,QAAO,iCAAgC,EAAC,GAAE,eAAc,EAAC,MAAK,EAAC,QAAO,iCAAgC,GAAE,OAAM,EAAC,QAAO,iCAAgC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKkB,IAAE,MAAK,EAAC,OAAM,EAAC,cAAa,KAAE,GAAE,SAAQ,EAAC,gBAAe,EAAC,MAAK,EAAC,QAAO,aAAY,EAAC,GAAE,eAAc,EAAC,MAAK,EAAC,QAAO,aAAY,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAElB,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,eAAe,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,eAAc,cAAc,GAAE,eAAc,CAAC,aAAa,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAE,OAAO,CAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,aAAY,CAAC,QAAQ,CAAC,CAAC,GAAEA,GAAE,WAAU,4BAA2B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,8BAA6B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,EAAC,QAAO,QAAO,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,MAAG,OAAM,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,UAAS,EAAC,GAAE,gBAAe,EAAC,MAAK,EAAC,QAAO,eAAc,GAAE,OAAM,EAAC,QAAO,eAAc,EAAC,GAAE,eAAc,EAAC,MAAK,EAAC,QAAO,eAAc,GAAE,OAAM,EAAC,QAAO,eAAc,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["l", "i", "a", "t", "e", "s", "o", "l", "n", "t", "w", "j", "p", "o", "r", "p", "p", "t", "e", "w", "u", "p", "t", "n", "i", "r", "o", "l", "s", "a", "c", "f", "d", "m", "h", "e", "T", "y", "w", "R", "S", "V", "b", "E", "_a", "j", "L", "A", "M", "U", "O", "g", "x", "C", "A", "F", "V", "p", "t", "O", "e", "l", "a", "r", "i", "s", "o", "n", "m", "_a", "L", "U", "y", "j", "w"]}