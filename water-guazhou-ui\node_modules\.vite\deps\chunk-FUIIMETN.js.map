{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/data/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{JSONMap as e}from\"../../../core/jsonMap.js\";import{isNone as t,isSome as i}from\"../../../core/maybe.js\";import{getUnitString as r}from\"../../../core/unitUtils.js\";import{canProjectWithoutEngine as n}from\"../../../geometry/projection.js\";import{getGeometryExtent as o}from\"../../../geometry/support/extentUtils.js\";import{fromJSON as s,isExtent as a,isPolygon as l,isPolyline as m}from\"../../../geometry/support/jsonUtils.js\";import{normalizeCentralMeridian as f}from\"../../../geometry/support/normalizeUtils.js\";import{equals as u,isGeographic as c,isWebMercator as p,WGS84 as y}from\"../../../geometry/support/spatialReferenceUtils.js\";import{quantizeOptimizedGeometry as g,convertToPoint as S,generalizeOptimizedGeometry as R,removeZMValues as w,convertToPolyline as j,convertToPolygon as h,convertToMultipoint as d}from\"../featureConversionUtils.js\";import x from\"../OptimizedGeometry.js\";import{checkProjectionSupport as U,project as M}from\"./projectionSupport.js\";const O=new e({esriSRUnit_Meter:\"meters\",esriSRUnit_Kilometer:\"kilometers\",esriSRUnit_Foot:\"feet\",esriSRUnit_StatuteMile:\"miles\",esriSRUnit_NauticalMile:\"nautical-miles\",esriSRUnit_USNauticalMile:\"us-nautical-miles\"}),F=Object.freeze({}),N=new x,_=new x,G=new x,P={esriGeometryPoint:S,esriGeometryPolyline:j,esriGeometryPolygon:h,esriGeometryMultipoint:d};function b(e,i,r,n=e.hasZ,o=e.hasM){if(t(i))return null;const s=e.hasZ&&n,a=e.hasM&&o;if(r){const t=g(G,i,e.hasZ,e.hasM,\"esriGeometryPoint\",r,n,o);return S(t,s,a)}return S(i,s,a)}function v(e,r,n,o,s,a,l=r,m=n){const f=r&&l,u=n&&m,c=i(o)?\"coords\"in o?o:o.geometry:null;if(t(c))return null;if(s){let t=R(_,c,r,n,e,s,l,m);return a&&(t=g(G,t,f,u,e,a)),P[e]?.(t,f,u)??null}if(a){const t=g(G,c,r,n,e,a,l,m);return P[e]?.(t,f,u)??null}return w(N,c,r,n,l,m),P[e]?.(N,f,u)??null}async function z(e,t,i){const{outFields:r,orderByFields:n,groupByFieldsForStatistics:o,outStatistics:s}=e;if(r)for(let a=0;a<r.length;a++)r[a]=r[a].trim();if(n)for(let a=0;a<n.length;a++)n[a]=n[a].trim();if(o)for(let a=0;a<o.length;a++)o[a]=o[a].trim();if(s)for(let a=0;a<s.length;a++)s[a].onStatisticField&&(s[a].onStatisticField=s[a].onStatisticField.trim());return e.geometry&&!e.outSR&&(e.outSR=e.geometry.spatialReference),J(e,t,i)}async function J(e,i,r){if(!e)return null;let{where:n}=e;if(e.where=n=n&&n.trim(),(!n||/^1 *= *1$/.test(n)||i&&i===n)&&(e.where=null),!e.geometry)return e;let a=await Z(e);if(e.distance=0,e.units=null,\"esriSpatialRelEnvelopeIntersects\"===e.spatialRel){const{spatialReference:t}=e.geometry;a=o(a),a.spatialReference=t}if(a){await U(a.spatialReference,r),a=B(a,r);const i=(await f(s(a)))[0];if(t(i))throw F;const n=\"quantizationParameters\"in e&&e.quantizationParameters?.tolerance||\"maxAllowableOffset\"in e&&e.maxAllowableOffset||0,o=n&&A(a,r)?{densificationStep:8*n}:void 0,l=i.toJSON(),m=await M(l,l.spatialReference,r,o);if(!m)throw F;m.spatialReference=r,e.geometry=m}return e}function A(e,t){if(!e)return!1;const i=e.spatialReference;return(a(e)||l(e)||m(e))&&!u(i,t)&&!n(i,t)}function B(e,t){const i=e.spatialReference;return A(e,t)&&a(e)?{spatialReference:i,rings:[[[e.xmin,e.ymin],[e.xmin,e.ymax],[e.xmax,e.ymax],[e.xmax,e.ymin],[e.xmin,e.ymin]]]}:e}async function Z(e){const{distance:t,units:i}=e,n=e.geometry;if(null==t||\"vertexAttributes\"in n)return n;const o=n.spatialReference,s=i?O.fromJSON(i):r(o),a=o&&(c(o)||p(o))?n:await U(o,y).then((()=>M(n,y)));return(await q())(a.spatialReference,a,t,s)}async function q(){return(await import(\"../../../geometry/geometryEngineJSON.js\")).geodesicBuffer}function E(e){return e&&k in e?JSON.parse(JSON.stringify(e,C)):e}const k=\"_geVersion\",C=(e,t)=>e!==k?t:void 0;export{F as QUERY_ENGINE_EMPTY_RESULT,E as cleanFromGeometryEngine,v as getGeometry,z as normalizeQuery,J as normalizeQueryLike,b as transformCentroid};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+8B,IAAM,IAAE,IAAI,EAAE,EAAC,kBAAiB,UAAS,sBAAqB,cAAa,iBAAgB,QAAO,wBAAuB,SAAQ,yBAAwB,kBAAiB,2BAA0B,oBAAmB,CAAC;AAAxN,IAA0N,IAAE,OAAO,OAAO,CAAC,CAAC;AAA5O,IAA8O,IAAE,IAAIA;AAApP,IAAsP,IAAE,IAAIA;AAA5P,IAA8P,IAAE,IAAIA;AAApQ,IAAsQ,IAAE,EAAC,mBAAkB,GAAE,sBAAqB,GAAE,qBAAoB,GAAE,wBAAuB,EAAC;AAAE,SAAS,EAAE,GAAE,GAAEC,IAAE,IAAE,EAAE,MAAKC,KAAE,EAAE,MAAK;AAAC,MAAG,EAAE,CAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,EAAE,QAAM,GAAE,IAAE,EAAE,QAAMD;AAAE,MAAGD,IAAE;AAAC,UAAMD,KAAE,GAAE,GAAE,GAAE,EAAE,MAAK,EAAE,MAAK,qBAAoBC,IAAE,GAAEC,EAAC;AAAE,WAAO,EAAEF,IAAEG,IAAE,CAAC;AAAA,EAAC;AAAC,SAAO,EAAE,GAAEA,IAAE,CAAC;AAAC;AAAC,SAASC,GAAE,GAAEH,IAAE,GAAEC,IAAEC,IAAE,GAAEE,KAAEJ,IAAE,IAAE,GAAE;AAJrgD;AAIsgD,QAAMK,KAAEL,MAAGI,IAAEE,KAAE,KAAG,GAAEC,KAAE,EAAEN,EAAC,IAAE,YAAWA,KAAEA,KAAEA,GAAE,WAAS;AAAK,MAAG,EAAEM,EAAC,EAAE,QAAO;AAAK,MAAGL,IAAE;AAAC,QAAIH,KAAE,GAAE,GAAEQ,IAAEP,IAAE,GAAE,GAAEE,IAAEE,IAAE,CAAC;AAAE,WAAO,MAAIL,KAAE,GAAE,GAAEA,IAAEM,IAAEC,IAAE,GAAE,CAAC,MAAG,OAAE,OAAF,2BAAOP,IAAEM,IAAEC,QAAI;AAAA,EAAI;AAAC,MAAG,GAAE;AAAC,UAAMP,KAAE,GAAE,GAAEQ,IAAEP,IAAE,GAAE,GAAE,GAAEI,IAAE,CAAC;AAAE,aAAO,OAAE,OAAF,2BAAOL,IAAEM,IAAEC,QAAI;AAAA,EAAI;AAAC,SAAO,GAAE,GAAEC,IAAEP,IAAE,GAAEI,IAAE,CAAC,KAAE,OAAE,OAAF,2BAAO,GAAEC,IAAEC,QAAI;AAAI;AAAC,eAAe,EAAE,GAAEP,IAAE,GAAE;AAAC,QAAK,EAAC,WAAUC,IAAE,eAAc,GAAE,4BAA2BC,IAAE,eAAcC,GAAC,IAAE;AAAE,MAAGF,GAAE,UAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAI,CAAAA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,KAAK;AAAE,MAAG,EAAE,UAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,IAAE,EAAE,CAAC,EAAE,KAAK;AAAE,MAAGC,GAAE,UAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAI,CAAAA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,KAAK;AAAE,MAAGC,GAAE,UAAQ,IAAE,GAAE,IAAEA,GAAE,QAAO,IAAI,CAAAA,GAAE,CAAC,EAAE,qBAAmBA,GAAE,CAAC,EAAE,mBAAiBA,GAAE,CAAC,EAAE,iBAAiB,KAAK;AAAG,SAAO,EAAE,YAAU,CAAC,EAAE,UAAQ,EAAE,QAAM,EAAE,SAAS,mBAAkB,EAAE,GAAEH,IAAE,CAAC;AAAC;AAAC,eAAe,EAAE,GAAE,GAAEC,IAAE;AAJttE;AAIutE,MAAG,CAAC,EAAE,QAAO;AAAK,MAAG,EAAC,OAAM,EAAC,IAAE;AAAE,MAAG,EAAE,QAAM,IAAE,KAAG,EAAE,KAAK,IAAG,CAAC,KAAG,YAAY,KAAK,CAAC,KAAG,KAAG,MAAI,OAAK,EAAE,QAAM,OAAM,CAAC,EAAE,SAAS,QAAO;AAAE,MAAI,IAAE,MAAM,EAAE,CAAC;AAAE,MAAG,EAAE,WAAS,GAAE,EAAE,QAAM,MAAK,uCAAqC,EAAE,YAAW;AAAC,UAAK,EAAC,kBAAiBD,GAAC,IAAE,EAAE;AAAS,QAAE,EAAE,CAAC,GAAE,EAAE,mBAAiBA;AAAA,EAAC;AAAC,MAAG,GAAE;AAAC,UAAMM,GAAE,EAAE,kBAAiBL,EAAC,GAAE,IAAE,EAAE,GAAEA,EAAC;AAAE,UAAMQ,MAAG,MAAML,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC;AAAE,QAAG,EAAEK,EAAC,EAAE,OAAM;AAAE,UAAMC,KAAE,4BAA2B,OAAG,OAAE,2BAAF,mBAA0B,cAAW,wBAAuB,KAAG,EAAE,sBAAoB,GAAER,KAAEQ,MAAG,EAAE,GAAET,EAAC,IAAE,EAAC,mBAAkB,IAAES,GAAC,IAAE,QAAOL,KAAEI,GAAE,OAAO,GAAE,IAAE,MAAM,EAAEJ,IAAEA,GAAE,kBAAiBJ,IAAEC,EAAC;AAAE,QAAG,CAAC,EAAE,OAAM;AAAE,MAAE,mBAAiBD,IAAE,EAAE,WAAS;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,EAAE,GAAED,IAAE;AAAC,MAAG,CAAC,EAAE,QAAM;AAAG,QAAM,IAAE,EAAE;AAAiB,UAAO,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,EAAE,CAAC,MAAI,CAAC,EAAE,GAAEA,EAAC,KAAG,CAAC,GAAE,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,QAAM,IAAE,EAAE;AAAiB,SAAO,EAAE,GAAEA,EAAC,KAAG,EAAE,CAAC,IAAE,EAAC,kBAAiB,GAAE,OAAM,CAAC,CAAC,CAAC,EAAE,MAAK,EAAE,IAAI,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,CAAC,CAAC,EAAC,IAAE;AAAC;AAAC,eAAe,EAAE,GAAE;AAAC,QAAK,EAAC,UAASA,IAAE,OAAM,EAAC,IAAE,GAAE,IAAE,EAAE;AAAS,MAAG,QAAMA,MAAG,sBAAqB,EAAE,QAAO;AAAE,QAAME,KAAE,EAAE,kBAAiBC,KAAE,IAAE,EAAE,SAAS,CAAC,IAAE,EAAED,EAAC,GAAE,IAAEA,OAAI,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,IAAE,MAAMI,GAAEJ,IAAE,CAAC,EAAE,KAAM,MAAI,EAAE,GAAE,CAAC,CAAE;AAAE,UAAO,MAAMS,GAAE,GAAG,EAAE,kBAAiB,GAAEX,IAAEG,EAAC;AAAC;AAAC,eAAeQ,KAAG;AAAC,UAAO,MAAM,OAAO,kCAAyC,GAAG;AAAc;AAAC,SAASC,GAAE,GAAE;AAAC,SAAO,KAAGC,MAAK,IAAE,KAAK,MAAM,KAAK,UAAU,GAAEC,EAAC,CAAC,IAAE;AAAC;AAAC,IAAMD,KAAE;AAAR,IAAqBC,KAAE,CAAC,GAAEd,OAAI,MAAIa,KAAEb,KAAE;", "names": ["t", "r", "o", "s", "v", "l", "f", "u", "c", "i", "n", "q", "E", "k", "C"]}