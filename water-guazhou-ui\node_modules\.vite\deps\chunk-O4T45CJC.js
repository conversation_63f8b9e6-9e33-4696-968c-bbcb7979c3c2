import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/layers/mixins/EditBusLayer.js
var d = new n.EventEmitter();
var i = "esri.layers.mixins.EditBusLayer";
var l = Symbol(i);
function o(e2) {
  return null != e2 && "object" == typeof e2 && l in e2;
}
var a2 = (t) => {
  var o2;
  let a3 = class extends t {
    constructor(...e2) {
      super(...e2), this[o2] = true, this.when().then(() => {
        this.own([d.on("edits", (e3) => {
          var _a, _b, _c;
          const t2 = "layer" in e3 ? e3.layer : null, r = "layer" in e3 ? (_a = e3.layer) == null ? void 0 : _a.url : e3.serviceUrl, d2 = "layer" in e3 ? (_b = e3.layer) == null ? void 0 : _b.layerId : e3.layerId, i2 = e3.event;
          if (t2 === this || r !== this.url) return;
          if (null != d2 && null != this.layerId && d2 === this.layerId) return void this.emit("edits", p(i2));
          const l2 = (_c = i2.editedFeatures) == null ? void 0 : _c.find(({ layerId: e4 }) => e4 === this.layerId);
          if (l2) {
            const { adds: e4, updates: t3, deletes: r2 } = l2.editedFeatures, d3 = { edits: null, addedAttachments: [], deletedAttachments: [], updatedAttachments: [], addedFeatures: e4 ? e4.map(({ attributes: e5 }) => ({ objectId: this.objectIdField && e5[this.objectIdField], globalId: this.globalIdField && e5[this.globalIdField] })) : [], deletedFeatures: r2 ? r2.map(({ attributes: e5 }) => ({ objectId: this.objectIdField && e5[this.objectIdField], globalId: this.globalIdField && e5[this.globalIdField] })) : [], updatedFeatures: t3 ? t3.map(({ current: { attributes: e5 } }) => ({ objectId: this.objectIdField && e5[this.objectIdField], globalId: this.globalIdField && e5[this.globalIdField] })) : [], editedFeatures: p(i2.editedFeatures), exceededTransferLimit: false };
            this.emit("edits", d3);
          }
        })]);
      }, () => {
      });
    }
  };
  return o2 = l, a3 = e([a(i)], a3), a3;
};

export {
  d,
  o,
  a2 as a
};
//# sourceMappingURL=chunk-O4T45CJC.js.map
