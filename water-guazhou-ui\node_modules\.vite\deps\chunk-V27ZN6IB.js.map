{"version": 3, "sources": ["../../@arcgis/core/views/3d/glTF/internal/TextureTransformUtils.js", "../../@arcgis/core/views/3d/layers/graphics/ProcessedObjectResource.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/DefaultMaterialTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/DefaultMaterialTechniqueConfiguration.js", "../../@arcgis/core/views/3d/webgl-engine/shaders/RealisticTreeTechnique.js", "../../@arcgis/core/views/3d/webgl-engine/materials/DefaultMaterial.js", "../../@arcgis/core/views/3d/layers/graphics/wosrLoader.js", "../../@arcgis/core/views/3d/layers/graphics/objectResourceUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as s,isSome as t}from\"../../../../core/maybe.js\";import{f as o,c as a}from\"../../../../chunks/mat3f32.js\";import{Z as r,O as f}from\"../../../../chunks/vec2f32.js\";import{m as n}from\"../../../../chunks/mat3.js\";function c(c){if(s(c))return null;const m=t(c.offset)?c.offset:r,e=t(c.rotation)?c.rotation:0,i=t(c.scale)?c.scale:f,h=o(1,0,0,0,1,0,m[0],m[1],1),u=o(Math.cos(e),-Math.sin(e),0,Math.sin(e),Math.cos(e),0,0,0,1),p=o(i[0],0,0,0,i[1],0,0,0,1),j=a();return n(j,u,p),n(j,h,j),j}export{c as getTransformMatrix};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass s{constructor(){this.geometries=new Array,this.materials=new Array,this.textures=new Array}}class t{constructor(t,e,r){this.name=t,this.lodThreshold=e,this.pivotOffset=r,this.stageResources=new s,this.numberOfVertices=0}}export{t as ProcessedObjectResource,s as ProcessedObjectStageResources};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e}from\"../../../../chunks/mat3f64.js\";import{f as t,c as i}from\"../../../../chunks/vec3f64.js\";import{f as s}from\"../../../../chunks/vec4f64.js\";import{ViewingMode as r}from\"../../../ViewingMode.js\";import{ShaderOutput as o}from\"../core/shaderLibrary/ShaderOutput.js\";import{NormalAttributeType as a}from\"../core/shaderLibrary/attributes/NormalAttribute.glsl.js\";import{TextureCoordinateAttributeType as l}from\"../core/shaderLibrary/attributes/TextureCoordinateAttribute.glsl.js\";import{VertexNormalPassParameters as n,VertexNormalDrawParameters as h}from\"../core/shaderLibrary/attributes/VertexNormal.glsl.js\";import{defaultMaskAlphaCutoff as c}from\"../core/shaderLibrary/util/AlphaCutoff.js\";import{ReloadableShaderModule as u}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{ShaderTechnique as p}from\"../core/shaderTechnique/ShaderTechnique.js\";import{CullFaceOptions as m,DepthTestFunction as d,AlphaDiscardMode as f}from\"../lib/basicInterfaces.js\";import{Default3D as b}from\"../lib/DefaultVertexAttributeLocations.js\";import{RenderOccludedFlag as v}from\"../lib/Material.js\";import{blendingDefault as g,oitBlending as S,oitDepthTest as y,getOITPolygonOffset as x}from\"../lib/OrderIndependentTransparency.js\";import{Program as T}from\"../lib/Program.js\";import{stencilWriteMaskOn as j,stencilToolMaskBaseParams as P,stencilBaseAllZerosParams as O}from\"../lib/StencilUtils.js\";import{TransparencyPassType as C}from\"../lib/TransparencyPassType.js\";import{D as L}from\"../../../../chunks/DefaultMaterial.glsl.js\";import{ContextType as M}from\"../../../webgl/context-util.js\";import{CompareFunction as z}from\"../../../webgl/enums.js\";import{makePipelineState as A,cullingParams as w,defaultDepthWriteParams as D,defaultColorWriteParams as F}from\"../../../webgl/renderState.js\";class k extends n{constructor(){super(...arguments),this.isSchematic=!1,this.usePBR=!1,this.mrrFactors=t(0,1,.5),this.hasVertexColors=!1,this.hasSymbolColors=!1,this.doubleSided=!1,this.doubleSidedType=\"normal\",this.cullFace=m.Back,this.emissiveFactor=t(0,0,0),this.instancedDoublePrecision=!1,this.normalType=a.Attribute,this.receiveSSAO=!0,this.receiveShadows=!0,this.castShadows=!0,this.shadowMappingEnabled=!1,this.ambient=t(.2,.2,.2),this.diffuse=t(.8,.8,.8),this.externalColor=s(1,1,1,1),this.colorMixMode=\"multiply\",this.opacity=1,this.layerOpacity=1,this.origin=i(),this.hasSlicePlane=!1,this.hasSliceHighlight=!0,this.offsetTransparentBackfaces=!1,this.vvSizeEnabled=!1,this.vvSizeMinSize=[1,1,1],this.vvSizeMaxSize=[100,100,100],this.vvSizeOffset=[0,0,0],this.vvSizeFactor=[1,1,1],this.vvSizeValue=[1,1,1],this.vvColorEnabled=!1,this.vvColorValues=[0,0,0,0,0,0,0,0],this.vvColorColors=[1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0,1,0,0,0],this.vvSymbolAnchor=[0,0,0],this.vvSymbolRotationMatrix=e(),this.vvOpacityEnabled=!1,this.vvOpacityValues=[],this.vvOpacityOpacities=[],this.transparent=!1,this.writeDepth=!0,this.customDepthTest=d.Less,this.textureAlphaMode=f.Blend,this.textureAlphaCutoff=c,this.textureAlphaPremultiplied=!1,this.hasOccludees=!1,this.renderOccluded=v.Occlude}}class E extends h{constructor(){super(...arguments),this.origin=i(),this.slicePlaneLocalOrigin=this.origin}}class _ extends p{initializeConfiguration(e,t){t.hasWebGL2Context=e.rctx.type===M.WEBGL2,t.spherical=e.viewingMode===r.Global,t.doublePrecisionRequiresObfuscation=e.rctx.driverTest.doublePrecisionRequiresObfuscation.result,t.textureCoordinateType=t.hasColorTexture||t.hasMetallicRoughnessTexture||t.hasEmissionTexture||t.hasOcclusionTexture||t.hasNormalTexture?l.Default:l.None,t.objectAndLayerIdColorInstanced=t.instanced}initializeProgram(e){return this._initializeProgram(e,_.shader)}_initializeProgram(e,t){return new T(e.rctx,t.get().build(this.configuration),b)}_convertDepthTestFunction(e){return e===d.Lequal?z.LEQUAL:z.LESS}_makePipeline(e,t){const i=this.configuration,s=e===C.NONE,r=e===C.FrontFace;return A({blending:i.output!==o.Color&&i.output!==o.Alpha||!i.transparent?null:s?g:S(e),culling:N(i)?w(i.cullFace):null,depthTest:{func:y(e,this._convertDepthTestFunction(i.customDepthTest))},depthWrite:(s||r)&&i.writeDepth?D:null,colorWrite:F,stencilWrite:i.hasOccludees?j:null,stencilTest:i.hasOccludees?t?P:O:null,polygonOffset:s||r?null:x(i.enableOffset)})}initializePipeline(){return this._occludeePipelineState=this._makePipeline(this.configuration.transparencyPassType,!0),this._makePipeline(this.configuration.transparencyPassType,!1)}getPipelineState(e,t){return t?this._occludeePipelineState:super.getPipelineState(e,t)}}function N(e){return e.cullFace!==m.None||!e.hasSlicePlane&&(!e.transparent&&!e.doubleSidedMode)}_.shader=new u(L,(()=>import(\"./DefaultMaterial.glsl.js\")));export{E as DefaultMaterialDrawParameters,k as DefaultMaterialPassParameters,_ as DefaultMaterialTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../../chunks/tslib.es6.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import{NormalAttributeType as e}from\"../core/shaderLibrary/attributes/NormalAttribute.glsl.js\";import{TextureCoordinateAttributeType as r}from\"../core/shaderLibrary/attributes/TextureCoordinateAttribute.glsl.js\";import{NormalsDoubleSidedMode as s}from\"../core/shaderLibrary/shading/Normals.glsl.js\";import{PBRMode as i}from\"../core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js\";import{parameter as a}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{AlphaDiscardMode as p,CullFaceOptions as h,DepthTestFunction as n}from\"../lib/basicInterfaces.js\";import{TransparencyPassType as d}from\"../lib/TransparencyPassType.js\";import{DefaultTechniqueConfiguration as l}from\"../materials/DefaultTechniqueConfiguration.js\";class c extends l{constructor(){super(...arguments),this.output=t.Color,this.alphaDiscardMode=p.Opaque,this.doubleSidedMode=s.None,this.pbrMode=i.Disabled,this.cullFace=h.None,this.transparencyPassType=d.NONE,this.normalType=e.Attribute,this.textureCoordinateType=r.None,this.customDepthTest=n.Less,this.spherical=!1,this.hasVertexColors=!1,this.hasSymbolColors=!1,this.hasVerticalOffset=!1,this.hasSlicePlane=!1,this.hasSliceHighlight=!0,this.hasColorTexture=!1,this.hasMetallicRoughnessTexture=!1,this.hasEmissionTexture=!1,this.hasOcclusionTexture=!1,this.hasNormalTexture=!1,this.hasScreenSizePerspective=!1,this.hasVertexTangents=!1,this.hasOccludees=!1,this.hasMultipassTerrain=!1,this.hasModelTransformation=!1,this.offsetBackfaces=!1,this.vvSize=!1,this.vvColor=!1,this.receiveShadows=!1,this.receiveAmbientOcclusion=!1,this.textureAlphaPremultiplied=!1,this.instanced=!1,this.instancedColor=!1,this.objectAndLayerIdColorInstanced=!1,this.instancedDoublePrecision=!1,this.doublePrecisionRequiresObfuscation=!1,this.writeDepth=!0,this.transparent=!1,this.enableOffset=!0,this.cullAboveGround=!1,this.snowCover=!1,this.hasColorTextureTransform=!1,this.hasEmissionTextureTransform=!1,this.hasNormalTextureTransform=!1,this.hasOcclusionTextureTransform=!1,this.hasMetallicRoughnessTextureTransform=!1}}o([a({count:t.COUNT})],c.prototype,\"output\",void 0),o([a({count:p.COUNT})],c.prototype,\"alphaDiscardMode\",void 0),o([a({count:s.COUNT})],c.prototype,\"doubleSidedMode\",void 0),o([a({count:i.COUNT})],c.prototype,\"pbrMode\",void 0),o([a({count:h.COUNT})],c.prototype,\"cullFace\",void 0),o([a({count:d.COUNT})],c.prototype,\"transparencyPassType\",void 0),o([a({count:e.COUNT})],c.prototype,\"normalType\",void 0),o([a({count:r.COUNT})],c.prototype,\"textureCoordinateType\",void 0),o([a({count:n.COUNT})],c.prototype,\"customDepthTest\",void 0),o([a()],c.prototype,\"spherical\",void 0),o([a()],c.prototype,\"hasVertexColors\",void 0),o([a()],c.prototype,\"hasSymbolColors\",void 0),o([a()],c.prototype,\"hasVerticalOffset\",void 0),o([a()],c.prototype,\"hasSlicePlane\",void 0),o([a()],c.prototype,\"hasSliceHighlight\",void 0),o([a()],c.prototype,\"hasColorTexture\",void 0),o([a()],c.prototype,\"hasMetallicRoughnessTexture\",void 0),o([a()],c.prototype,\"hasEmissionTexture\",void 0),o([a()],c.prototype,\"hasOcclusionTexture\",void 0),o([a()],c.prototype,\"hasNormalTexture\",void 0),o([a()],c.prototype,\"hasScreenSizePerspective\",void 0),o([a()],c.prototype,\"hasVertexTangents\",void 0),o([a()],c.prototype,\"hasOccludees\",void 0),o([a()],c.prototype,\"hasMultipassTerrain\",void 0),o([a()],c.prototype,\"hasModelTransformation\",void 0),o([a()],c.prototype,\"offsetBackfaces\",void 0),o([a()],c.prototype,\"vvSize\",void 0),o([a()],c.prototype,\"vvColor\",void 0),o([a()],c.prototype,\"receiveShadows\",void 0),o([a()],c.prototype,\"receiveAmbientOcclusion\",void 0),o([a()],c.prototype,\"textureAlphaPremultiplied\",void 0),o([a()],c.prototype,\"instanced\",void 0),o([a()],c.prototype,\"instancedColor\",void 0),o([a()],c.prototype,\"objectAndLayerIdColorInstanced\",void 0),o([a()],c.prototype,\"instancedDoublePrecision\",void 0),o([a()],c.prototype,\"doublePrecisionRequiresObfuscation\",void 0),o([a()],c.prototype,\"writeDepth\",void 0),o([a()],c.prototype,\"transparent\",void 0),o([a()],c.prototype,\"enableOffset\",void 0),o([a()],c.prototype,\"cullAboveGround\",void 0),o([a()],c.prototype,\"snowCover\",void 0),o([a()],c.prototype,\"hasColorTextureTransform\",void 0),o([a()],c.prototype,\"hasEmissionTextureTransform\",void 0),o([a()],c.prototype,\"hasNormalTextureTransform\",void 0),o([a()],c.prototype,\"hasOcclusionTextureTransform\",void 0),o([a()],c.prototype,\"hasMetallicRoughnessTextureTransform\",void 0),o([a({constValue:!0})],c.prototype,\"hasVvInstancing\",void 0),o([a({constValue:!1})],c.prototype,\"useCustomDTRExponentForWater\",void 0),o([a({constValue:!1})],c.prototype,\"supportsTextureAtlas\",void 0),o([a({constValue:!0})],c.prototype,\"useFillLights\",void 0);export{c as DefaultMaterialTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{NormalAttributeType as e}from\"../core/shaderLibrary/attributes/NormalAttribute.glsl.js\";import{NormalsDoubleSidedMode as r}from\"../core/shaderLibrary/shading/Normals.glsl.js\";import{ReloadableShaderModule as i}from\"../core/shaderTechnique/ReloadableShaderModule.js\";import{DefaultMaterialTechnique as a}from\"./DefaultMaterialTechnique.js\";import{R as s}from\"../../../../chunks/RealisticTree.glsl.js\";class t extends a{initializeConfiguration(i,a){super.initializeConfiguration(i,a),a.hasMetallicRoughnessTexture=!1,a.hasEmissionTexture=!1,a.hasOcclusionTexture=!1,a.hasNormalTexture=!1,a.hasModelTransformation=!1,a.normalType=e.Attribute,a.doubleSidedMode=r.<PERSON>,a.hasVertexTangents=!1}initializeProgram(e){return this._initializeProgram(e,t.shader)}}t.shader=new i(s,(()=>import(\"./RealisticTree.glsl.js\")));export{t as RealisticTreeTechnique};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../core/has.js\";import{isSome as e}from\"../../../../core/maybe.js\";import{s as t,c as r,n as a,b as s,l as i,g as o,e as n,t as h}from\"../../../../chunks/vec3.js\";import{c,f as l}from\"../../../../chunks/vec3f64.js\";import{ViewingMode as u}from\"../../../ViewingMode.js\";import{newLayout as m}from\"../../support/buffer/InterleavedLayout.js\";import{ShaderOutput as p}from\"../core/shaderLibrary/ShaderOutput.js\";import{NormalsDoubleSidedMode as d}from\"../core/shaderLibrary/shading/Normals.glsl.js\";import{PBRMode as f}from\"../core/shaderLibrary/shading/PhysicallyBasedRenderingParameters.glsl.js\";import{CullFaceOptions as g}from\"../lib/basicInterfaces.js\";import{GLTextureMaterial as T}from\"../lib/GLTextureMaterial.js\";import{Material as _}from\"../lib/Material.js\";import{OITPolygonOffsetLimit as x}from\"../lib/OrderIndependentTransparency.js\";import{RenderSlot as b}from\"../lib/RenderSlot.js\";import{VertexAttribute as S}from\"../lib/VertexAttribute.js\";import{getVerticalOffsetObject3D as v}from\"../lib/verticalOffsetUtils.js\";import{DefaultBufferWriter as w}from\"./DefaultBufferWriter.js\";import{verticalOffsetAtDistance as O,intersectTriangleGeometry as M}from\"./internal/MaterialUtil.js\";import{DefaultMaterialTechnique as A,DefaultMaterialPassParameters as y}from\"../shaders/DefaultMaterialTechnique.js\";import{DefaultMaterialTechniqueConfiguration as C}from\"../shaders/DefaultMaterialTechniqueConfiguration.js\";import{RealisticTreeTechnique as R}from\"../shaders/RealisticTreeTechnique.js\";class E extends _{constructor(e){super(e,j),this.supportsEdges=!0,this._configuration=new C,this._vertexBufferLayout=L(this.parameters)}isVisibleForOutput(e){return e!==p.Shadow&&e!==p.ShadowExcludeHighlight&&e!==p.ShadowHighlight||this.parameters.castShadows}isVisible(){const t=this.parameters;if(!super.isVisible()||0===t.layerOpacity)return!1;const{instanced:r,hasVertexColors:a,hasSymbolColors:s,vvColorEnabled:i}=t,o=e(r)&&r.includes(\"color\"),n=\"replace\"===t.colorMixMode,h=t.opacity>0,c=t.externalColor&&t.externalColor[3]>0;return a&&(o||i||s)?!!n||h:a?n?c:h:o||i||s?!!n||h:n?c:h}getConfiguration(t,r){return this._configuration.output=t,this._configuration.hasNormalTexture=!!this.parameters.normalTextureId,this._configuration.hasColorTexture=!!this.parameters.textureId,this._configuration.hasVertexTangents=this.parameters.hasVertexTangents,this._configuration.instanced=!!this.parameters.instanced,this._configuration.instancedDoublePrecision=this.parameters.instancedDoublePrecision,this._configuration.vvSize=this.parameters.vvSizeEnabled,this._configuration.hasVerticalOffset=e(this.parameters.verticalOffset),this._configuration.hasScreenSizePerspective=e(this.parameters.screenSizePerspective),this._configuration.hasSlicePlane=this.parameters.hasSlicePlane,this._configuration.hasSliceHighlight=this.parameters.hasSliceHighlight,this._configuration.alphaDiscardMode=this.parameters.textureAlphaMode,this._configuration.normalType=this.parameters.normalType,this._configuration.transparent=this.parameters.transparent,this._configuration.writeDepth=this.parameters.writeDepth,e(this.parameters.customDepthTest)&&(this._configuration.customDepthTest=this.parameters.customDepthTest),this._configuration.hasOccludees=this.parameters.hasOccludees,this._configuration.cullFace=this.parameters.hasSlicePlane?g.None:this.parameters.cullFace,this._configuration.hasMultipassTerrain=r.multipassTerrain.enabled,this._configuration.cullAboveGround=r.multipassTerrain.cullAboveGround,this._configuration.hasModelTransformation=e(this.parameters.modelTransformation),t!==p.Color&&t!==p.Alpha||(this._configuration.hasVertexColors=this.parameters.hasVertexColors,this._configuration.hasSymbolColors=this.parameters.hasSymbolColors,this.parameters.treeRendering?this._configuration.doubleSidedMode=d.WindingOrder:this._configuration.doubleSidedMode=this.parameters.doubleSided&&\"normal\"===this.parameters.doubleSidedType?d.View:this.parameters.doubleSided&&\"winding-order\"===this.parameters.doubleSidedType?d.WindingOrder:d.None,this._configuration.instancedColor=e(this.parameters.instanced)&&this.parameters.instanced.includes(\"color\"),this._configuration.receiveShadows=this.parameters.receiveShadows&&this.parameters.shadowMappingEnabled,this._configuration.receiveAmbientOcclusion=!!r.ssaoHelper.active&&this.parameters.receiveSSAO,this._configuration.vvColor=this.parameters.vvColorEnabled,this._configuration.textureAlphaPremultiplied=!!this.parameters.textureAlphaPremultiplied,this._configuration.pbrMode=this.parameters.usePBR?this.parameters.isSchematic?f.Schematic:f.Normal:f.Disabled,this._configuration.hasMetallicRoughnessTexture=!!this.parameters.metallicRoughnessTextureId,this._configuration.hasEmissionTexture=!!this.parameters.emissiveTextureId,this._configuration.hasOcclusionTexture=!!this.parameters.occlusionTextureId,this._configuration.offsetBackfaces=!(!this.parameters.transparent||!this.parameters.offsetTransparentBackfaces),this._configuration.transparencyPassType=r.transparencyPassType,this._configuration.enableOffset=r.camera.relativeElevation<x,this._configuration.snowCover=this.hasSnowCover(r),this._configuration.hasColorTextureTransform=!!this.parameters.colorTextureTransformMatrix,this._configuration.hasNormalTextureTransform=!!this.parameters.normalTextureTransformMatrix,this._configuration.hasEmissionTextureTransform=!!this.parameters.emissiveTextureTransformMatrix,this._configuration.hasOcclusionTextureTransform=!!this.parameters.occlusionTextureTransformMatrix,this._configuration.hasMetallicRoughnessTextureTransform=!!this.parameters.metallicRoughnessTextureTransformMatrix),this._configuration}hasSnowCover(t){return e(t.weather)&&t.weatherVisible&&\"snowy\"===t.weather.type&&\"enabled\"===t.weather.snowCover}intersect(c,l,m,p,d,f){if(e(this.parameters.verticalOffset)){const e=m.camera;t(z,l[12],l[13],l[14]);let c=null;switch(m.viewingMode){case u.Global:c=a(B,z);break;case u.Local:c=r(B,N)}let f=0;const g=s(G,z,e.eye),T=i(g),_=o(g,g,1/T);let x=null;this.parameters.screenSizePerspective&&(x=n(c,_)),f+=O(e,T,this.parameters.verticalOffset,x??0,this.parameters.screenSizePerspective),o(c,c,f),h(H,c,m.transform.inverseRotation),p=s(D,p,H),d=s(V,d,H)}M(c,m,p,d,v(m.verticalOffset),f)}requiresSlot(e,t){if(t===p.Color||t===p.Alpha||t===p.Depth||t===p.Normal||t===p.Shadow||t===p.ShadowHighlight||t===p.ShadowExcludeHighlight||t===p.Highlight||t===p.ObjectAndLayerIdColor){return e===(this.parameters.transparent?this.parameters.writeDepth?b.TRANSPARENT_MATERIAL:b.TRANSPARENT_DEPTH_WRITE_DISABLED_MATERIAL:b.OPAQUE_MATERIAL)||e===b.DRAPED_MATERIAL}return!1}createGLMaterial(e){return new I(e)}createBufferWriter(){return new w(this._vertexBufferLayout)}}class I extends T{constructor(e){super({...e,...e.material.parameters})}_updateShadowState(e){e.shadowMap.enabled!==this._material.parameters.shadowMappingEnabled&&this._material.setParameters({shadowMappingEnabled:e.shadowMap.enabled})}_updateOccludeeState(e){e.hasOccludees!==this._material.parameters.hasOccludees&&this._material.setParameters({hasOccludees:e.hasOccludees})}beginSlot(e){this._output!==p.Color&&this._output!==p.Alpha||(this._updateShadowState(e),this._updateOccludeeState(e));const r=this._material.parameters;this.updateTexture(r.textureId);const a=e.camera.viewInverseTransposeMatrix;return t(r.origin,a[3],a[7],a[11]),this._material.setParameters(this.textureBindParameters),this.ensureTechnique(r.treeRendering?R:A,e)}}class P extends y{constructor(){super(...arguments),this.initTextureTransparent=!1,this.treeRendering=!1,this.hasVertexTangents=!1}}const j=new P;function L(e){const t=m().vec3f(S.POSITION).vec3f(S.NORMAL),r=e.textureId||e.normalTextureId||e.metallicRoughnessTextureId||e.emissiveTextureId||e.occlusionTextureId;return e.hasVertexTangents&&t.vec4f(S.TANGENT),r&&t.vec2f(S.UV0),e.hasVertexColors&&t.vec4u8(S.COLOR),e.hasSymbolColors&&t.vec4u8(S.SYMBOLCOLOR),has(\"enable-feature:objectAndLayerId-rendering\")&&t.vec4u8(S.OBJECTANDLAYERIDCOLOR),t}const D=c(),V=c(),N=l(0,0,1),B=c(),H=c(),z=c(),G=c();export{I as DefaultGLMaterial,E as DefaultMaterial,P as DefaultMaterialParameters};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import{result as t}from\"../../../../core/asyncUtils.js\";import{estimateNestedObjectSize as r}from\"../../../../core/byteSizeEstimations.js\";import n from\"../../../../core/Error.js\";import s from\"../../../../core/Logger.js\";import{isSome as o,unwrap as a,isNone as i}from\"../../../../core/maybe.js\";import{NestedMap as u}from\"../../../../core/NestedMap.js\";import{throwIfAbortError as l}from\"../../../../core/promiseUtils.js\";import{Version as c}from\"../../../../core/Version.js\";import{d as p}from\"../../../../chunks/vec3f64.js\";import{empty as m,expandWithVec3 as f}from\"../../../../geometry/support/aaBoundingBox.js\";import{requestImage as d}from\"../../../../support/requestImageUtils.js\";import{Attribute as g}from\"../../webgl-engine/lib/Attribute.js\";import{CullFaceOptions as y,AlphaDiscardMode as b}from\"../../webgl-engine/lib/basicInterfaces.js\";import{Geometry as w}from\"../../webgl-engine/lib/Geometry.js\";import{Texture as x}from\"../../webgl-engine/lib/Texture.js\";import{DefaultMaterial as h}from\"../../webgl-engine/materials/DefaultMaterial.js\";import{TextureWrapMode as v}from\"../../../webgl/enums.js\";const j=s.getLogger(\"esri.views.3d.layers.graphics.objectResourceUtils\");async function A(e,t){const n=await M(e,t),s=await k(n.textureDefinitions??{},t);let o=0;for(const r in s)if(s.hasOwnProperty(r)){const e=s[r];o+=e?.image?e.image.width*e.image.height*4:0}return{resource:n,textures:s,size:o+r(n)}}async function M(r,n){const s=o(n)&&n.streamDataRequester;if(s)return P(r,s,n);const i=await t(e(r,a(n)));if(!0===i.ok)return i.value.data;l(i.error),U(i.error)}async function P(e,r,n){const s=await t(r.request(e,\"json\",n));if(!0===s.ok)return s.value;l(s.error),U(s.error.details.url)}function U(e){throw new n(\"\",`Request for object resource failed: ${e}`)}function E(e){const t=e.params,r=t.topology;let n=!0;switch(t.vertexAttributes||(j.warn(\"Geometry must specify vertex attributes\"),n=!1),t.topology){case\"PerAttributeArray\":break;case\"Indexed\":case null:case void 0:{const e=t.faces;if(e){if(t.vertexAttributes)for(const r in t.vertexAttributes){const t=e[r];t&&t.values?(null!=t.valueType&&\"UInt32\"!==t.valueType&&(j.warn(`Unsupported indexed geometry indices type '${t.valueType}', only UInt32 is currently supported`),n=!1),null!=t.valuesPerElement&&1!==t.valuesPerElement&&(j.warn(`Unsupported indexed geometry values per element '${t.valuesPerElement}', only 1 is currently supported`),n=!1)):(j.warn(`Indexed geometry does not specify face indices for '${r}' attribute`),n=!1)}}else j.warn(\"Indexed geometries must specify faces\"),n=!1;break}default:j.warn(`Unsupported topology '${r}'`),n=!1}e.params.material||(j.warn(\"Geometry requires material\"),n=!1);const s=e.params.vertexAttributes;for(const o in s){s[o].values||(j.warn(\"Geometries with externally defined attributes are not yet supported\"),n=!1)}return n}function I(e,t){const r=new Array,n=new Array,s=new Array,a=new u,l=e.resource,m=c.parse(l.version||\"1.0\",\"wosr\");R.validate(m);const f=l.model.name,d=l.model.geometries,b=l.materialDefinitions??{},v=e.textures;let j=0;const A=new Map;for(let u=0;u<d.length;u++){const e=d[u];if(!E(e))continue;const l=O(e),c=e.params.vertexAttributes,m=[];for(const t in c){const e=c[t],r=e.values;m.push([t,new g(r,e.valuesPerElement,!0)])}const f=[];if(\"PerAttributeArray\"!==e.params.topology){const t=e.params.faces;for(const e in t)f.push([e,t[e].values])}const M=l.texture,P=v&&v[M];if(P&&!A.has(M)){const{image:e,params:t}=P,r=new x(e,t);n.push(r),A.set(M,r)}const U=A.get(M),I=U?U.id:void 0,T=l.material;let k=a.get(T,M);if(i(k)){const e=b[T.substring(T.lastIndexOf(\"/\")+1)].params;1===e.transparency&&(e.transparency=0);const r=P&&P.alphaChannelUsage,n=e.transparency>0||\"transparency\"===r||\"maskAndTransparency\"===r,s=P?q(P.alphaChannelUsage):void 0,i={ambient:p(e.diffuse),diffuse:p(e.diffuse),opacity:1-(e.transparency||0),transparent:n,textureAlphaMode:s,textureAlphaCutoff:.33,textureId:I,initTextureTransparent:!0,doubleSided:!0,cullFace:y.None,colorMixMode:e.externalColorMixMode||\"tint\",textureAlphaPremultiplied:!!P&&!!P.params.preMultiplyAlpha};o(t)&&t.materialParamsMixin&&Object.assign(i,t.materialParamsMixin),k=new h(i),a.set(T,M,k)}s.push(k);const R=new w(k,m,f);j+=f.position?f.position.length:0,r.push(R)}return{engineResources:[{name:f,stageResources:{textures:n,materials:s,geometries:r},pivotOffset:l.model.pivotOffset,numberOfVertices:j,lodThreshold:null}],referenceBoundingBox:T(r)}}function T(e){const t=m();return e.forEach((e=>{const r=e.boundingInfo;o(r)&&(f(t,r.bbMin),f(t,r.bbMax))})),t}async function k(e,t){const r=[];for(const a in e){const n=e[a],s=n.images[0].data;if(!s){j.warn(\"Externally referenced texture data is not yet supported\");continue}const i=n.encoding+\";base64,\"+s,u=\"/textureDefinitions/\"+a,l=\"rgba\"===n.channels?n.alphaChannelUsage||\"transparency\":\"none\",c={noUnpackFlip:!0,wrap:{s:v.REPEAT,t:v.REPEAT},preMultiplyAlpha:q(l)!==b.Opaque},p=o(t)&&t.disableTextures?Promise.resolve(null):d(i,t);r.push(p.then((e=>({refId:u,image:e,params:c,alphaChannelUsage:l}))))}const n=await Promise.all(r),s={};for(const o of n)s[o.refId]=o;return s}function q(e){switch(e){case\"mask\":return b.Mask;case\"maskAndTransparency\":return b.MaskBlend;case\"none\":return b.Opaque;default:return b.Blend}}function O(e){const t=e.params;return{id:1,material:t.material,texture:t.texture,region:t.texture}}const R=new c(1,2,\"wosr\");export{k as createTextureResources,A as load,I as processLoadResult};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{adjustStaticAGOUrl as e}from\"../../../../core/devEnvironmentUtils.js\";import{get as t,isSome as r,isNone as o}from\"../../../../core/maybe.js\";import{b as s}from\"../../../../chunks/mat3.js\";import{c as i}from\"../../../../chunks/mat3f64.js\";import{a as n}from\"../../../../chunks/mat4.js\";import{c as a}from\"../../../../chunks/mat4f64.js\";import{m as u,b as l,C as c,l as m,n as f,h as d}from\"../../../../chunks/vec3.js\";import{c as p}from\"../../../../chunks/vec3f64.js\";import{empty as g,expandWithVec3 as b}from\"../../../../geometry/support/aaBoundingBox.js\";import{BufferViewVec3f as x,BufferViewVec4f as h,BufferViewVec2f as T,BufferViewVec4u8 as R,BufferViewVec4u16 as w,BufferViewVec3u8 as j,BufferViewVec3u16 as M}from\"../../../../geometry/support/buffer/BufferView.js\";import{t as v,a as y,s as B}from\"../../../../chunks/vec32.js\";import{t as L,s as C}from\"../../../../chunks/vec42.js\";import{createBuffer as I}from\"../../../../geometry/support/buffer/utils.js\";import{DefaultLoadingContext as A}from\"../../glTF/DefaultLoadingContext.js\";import{loadGLTF as O}from\"../../glTF/loader.js\";import{triangleFanToTriangles as S,triangleStripToTriangles as E,trianglesToTriangles as N}from\"../../glTF/internal/indexUtils.js\";import{isEncodedMeshTexture as k}from\"../../glTF/internal/resourceUtils.js\";import{getTransformMatrix as F}from\"../../glTF/internal/TextureTransformUtils.js\";import{ProcessedObjectResource as P}from\"./ProcessedObjectResource.js\";import{load as V,processLoadResult as _}from\"./wosrLoader.js\";import{NormalAttributeType as G}from\"../../webgl-engine/core/shaderLibrary/attributes/NormalAttribute.glsl.js\";import{Attribute as D}from\"../../webgl-engine/lib/Attribute.js\";import{AlphaDiscardMode as U,DepthTestFunction as H,CullFaceOptions as q}from\"../../webgl-engine/lib/basicInterfaces.js\";import{Geometry as W}from\"../../webgl-engine/lib/Geometry.js\";import{Texture as $}from\"../../webgl-engine/lib/Texture.js\";import{VertexAttribute as z}from\"../../webgl-engine/lib/VertexAttribute.js\";import{DefaultMaterial as K}from\"../../webgl-engine/materials/DefaultMaterial.js\";import{COLOR_GAMMA as Q}from\"../../webgl-engine/materials/DefaultMaterial_COLOR_GAMMA.js\";import{PrimitiveType as J}from\"../../../webgl/enums.js\";import{n as X}from\"../../../../chunks/vec22.js\";import{c as Y,f as Z}from\"../../../../chunks/vec43.js\";import{c as ee}from\"../../../../chunks/vec33.js\";async function te(o,s){const i=re(e(o));if(\"wosr\"===i.fileType){const e=await(s.cache?s.cache.loadWOSR(i.url,s):V(i.url,s)),{engineResources:t,referenceBoundingBox:r}=_(e,s);return{lods:t,referenceBoundingBox:r,isEsriSymbolResource:!1,isWosr:!0}}const n=await(s.cache?s.cache.loadGLTF(i.url,s,!!s.usePBR):O(new A(s.streamDataRequester),i.url,s,s.usePBR)),a=t(n.model.meta,\"ESRI_proxyEllipsoid\"),u=n.meta.isEsriSymbolResource&&r(a)&&n.meta.uri.includes(\"/RealisticTrees/\");u&&!n.customMeta.esriTreeRendering&&(n.customMeta.esriTreeRendering=!0,le(n,a));const l=!!s.usePBR,c=n.meta.isEsriSymbolResource?{usePBR:l,isSchematic:!1,treeRendering:u,mrrFactors:[0,1,.2]}:{usePBR:l,isSchematic:!1,treeRendering:!1,mrrFactors:[0,1,.5]},m={...s.materialParamsMixin,treeRendering:u},{engineResources:f,referenceBoundingBox:d}=oe(n,c,m,s.skipHighLods&&null==i.specifiedLodIndex?{skipHighLods:!0}:{skipHighLods:!1,singleLodIndex:i.specifiedLodIndex});return{lods:f,referenceBoundingBox:d,isEsriSymbolResource:n.meta.isEsriSymbolResource,isWosr:!1}}function re(e){const t=e.match(/(.*\\.(gltf|glb))(\\?lod=([0-9]+))?$/);if(t)return{fileType:\"gltf\",url:t[1],specifiedLodIndex:null!=t[4]?Number(t[4]):null};return e.match(/(.*\\.(json|json\\.gz))$/)?{fileType:\"wosr\",url:e,specifiedLodIndex:null}:{fileType:\"unknown\",url:e,specifiedLodIndex:null}}function oe(e,t,o,s){const i=e.model,n=new Array,a=new Map,u=new Map,l=i.lods.length,c=g();return i.lods.forEach(((e,m)=>{const f=!0===s.skipHighLods&&(l>1&&0===m||l>3&&1===m)||!1===s.skipHighLods&&null!=s.singleLodIndex&&m!==s.singleLodIndex;if(f&&0!==m)return;const d=new P(e.name,e.lodThreshold,[0,0,0]);e.parts.forEach((e=>{const s=f?new K({}):se(i,e,d,t,o,a,u),{geometry:n,vertexCount:l}=ie(e,r(s)?s:new K({})),p=n.boundingInfo;r(p)&&0===m&&(b(c,p.bbMin),b(c,p.bbMax)),r(s)&&(d.stageResources.geometries.push(n),d.numberOfVertices+=l)})),f||n.push(d)})),{engineResources:n,referenceBoundingBox:c}}function se(e,t,s,i,n,a,u){const l=t.material+(t.attributes.normal?\"_normal\":\"\")+(t.attributes.color?\"_color\":\"\")+(t.attributes.texCoord0?\"_texCoord0\":\"\")+(t.attributes.tangent?\"_tangent\":\"\"),c=e.materials.get(t.material),m=r(t.attributes.texCoord0),f=r(t.attributes.normal);if(o(c))return null;const d=ae(c.alphaMode);if(!a.has(l)){if(m){const t=(t,o=!1)=>{if(r(t)&&!u.has(t)){const s=e.textures.get(t);if(r(s)){const e=s.data;u.set(t,new $(k(e)?e.data:e,{...s.parameters,preMultiplyAlpha:!k(e)&&o,encoding:k(e)&&r(e.encoding)?e.encoding:void 0}))}}};t(c.textureColor,d!==U.Opaque),t(c.textureNormal),t(c.textureOcclusion),t(c.textureEmissive),t(c.textureMetallicRoughness)}const o=c.color[0]**(1/Q),s=c.color[1]**(1/Q),p=c.color[2]**(1/Q),g=c.emissiveFactor[0]**(1/Q),b=c.emissiveFactor[1]**(1/Q),x=c.emissiveFactor[2]**(1/Q),h=r(c.textureColor)&&m?u.get(c.textureColor):null;a.set(l,new K({...i,transparent:d===U.Blend,customDepthTest:H.Lequal,textureAlphaMode:d,textureAlphaCutoff:c.alphaCutoff,diffuse:[o,s,p],ambient:[o,s,p],opacity:c.opacity,doubleSided:c.doubleSided,doubleSidedType:\"winding-order\",cullFace:c.doubleSided?q.None:q.Back,hasVertexColors:!!t.attributes.color,hasVertexTangents:!!t.attributes.tangent,normalType:f?G.Attribute:G.ScreenDerivative,castShadows:!0,receiveSSAO:!0,textureId:r(h)?h.id:void 0,colorMixMode:c.colorMixMode,normalTextureId:r(c.textureNormal)&&m?u.get(c.textureNormal).id:void 0,textureAlphaPremultiplied:r(h)&&!!h.params.preMultiplyAlpha,occlusionTextureId:r(c.textureOcclusion)&&m?u.get(c.textureOcclusion).id:void 0,emissiveTextureId:r(c.textureEmissive)&&m?u.get(c.textureEmissive).id:void 0,metallicRoughnessTextureId:r(c.textureMetallicRoughness)&&m?u.get(c.textureMetallicRoughness).id:void 0,emissiveFactor:[g,b,x],mrrFactors:[c.metallicFactor,c.roughnessFactor,i.mrrFactors[2]],isSchematic:!1,colorTextureTransformMatrix:F(c.colorTextureTransform),normalTextureTransformMatrix:F(c.normalTextureTransform),occlusionTextureTransformMatrix:F(c.occlusionTextureTransform),emissiveTextureTransformMatrix:F(c.emissiveTextureTransform),metallicRoughnessTextureTransformMatrix:F(c.metallicRoughnessTextureTransform),...n}))}const p=a.get(l);if(s.stageResources.materials.push(p),m){const e=e=>{r(e)&&s.stageResources.textures.push(u.get(e))};e(c.textureColor),e(c.textureNormal),e(c.textureOcclusion),e(c.textureEmissive),e(c.textureMetallicRoughness)}return p}function ie(e,t){const o=e.attributes.position.count,i=ue(e.indices||o,e.primitiveType),n=I(x,o);v(n,e.attributes.position,e.transform);const a=[[z.POSITION,new D(n.typedBuffer,n.elementCount,!0)]],u=[[z.POSITION,i]];if(r(e.attributes.normal)){const t=I(x,o);s(ne,e.transform),y(t,e.attributes.normal,ne),a.push([z.NORMAL,new D(t.typedBuffer,t.elementCount,!0)]),u.push([z.NORMAL,i])}if(r(e.attributes.tangent)){const t=I(h,o);s(ne,e.transform),L(t,e.attributes.tangent,ne),a.push([z.TANGENT,new D(t.typedBuffer,t.elementCount,!0)]),u.push([z.TANGENT,i])}if(r(e.attributes.texCoord0)){const t=I(T,o);X(t,e.attributes.texCoord0),a.push([z.UV0,new D(t.typedBuffer,t.elementCount,!0)]),u.push([z.UV0,i])}if(r(e.attributes.color)){const t=I(R,o);if(4===e.attributes.color.elementCount)e.attributes.color instanceof h?C(t,e.attributes.color,255):e.attributes.color instanceof R?Y(t,e.attributes.color):e.attributes.color instanceof w&&C(t,e.attributes.color,1/256);else{Z(t,255,255,255,255);const r=new j(t.buffer,0,4);e.attributes.color instanceof x?B(r,e.attributes.color,255):e.attributes.color instanceof j?ee(r,e.attributes.color):e.attributes.color instanceof M&&B(r,e.attributes.color,1/256)}a.push([z.COLOR,new D(t.typedBuffer,t.elementCount,!0)]),u.push([z.COLOR,i])}return{geometry:new W(t,a,u),vertexCount:o}}const ne=i();function ae(e){switch(e){case\"BLEND\":return U.Blend;case\"MASK\":return U.Mask;case\"OPAQUE\":case null:case void 0:return U.Opaque}}function ue(e,t){switch(t){case J.TRIANGLES:return N(e);case J.TRIANGLE_STRIP:return E(e);case J.TRIANGLE_FAN:return S(e)}}function le(e,t){for(let r=0;r<e.model.lods.length;++r){const s=e.model.lods[r];for(const i of s.parts){const s=i.attributes.normal;if(o(s))return;const g=i.attributes.position,b=g.count,h=p(),T=p(),w=p(),j=I(R,b),M=I(x,b),v=n(a(),i.transform);for(let o=0;o<b;o++){g.getVec(o,T),s.getVec(o,h),u(T,T,i.transform),l(w,T,t.center),c(w,w,t.radius);const n=w[2],a=m(w),p=Math.min(.45+.55*a*a,1);c(w,w,t.radius),null!==v&&u(w,w,v),f(w,w),r+1!==e.model.lods.length&&e.model.lods.length>1&&d(w,w,h,n>-1?.2:Math.min(-4*n-3.8,1)),M.setVec(o,w),j.set(o,0,255*p),j.set(o,1,255*p),j.set(o,2,255*p),j.set(o,3,255)}i.attributes.normal=M,i.attributes.color=j}}}export{te as fetch,oe as gltfToEngineResources,re as parseUrl};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgO,SAASA,GAAEA,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,EAAED,GAAE,MAAM,IAAEA,GAAE,SAAOA,IAAEE,MAAE,EAAEF,GAAE,QAAQ,IAAEA,GAAE,WAAS,GAAEG,KAAE,EAAEH,GAAE,KAAK,IAAEA,GAAE,QAAMG,IAAEC,KAAEC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAAC,GAAEK,KAAED,GAAE,KAAK,IAAIH,GAAC,GAAE,CAAC,KAAK,IAAIA,GAAC,GAAE,GAAE,KAAK,IAAIA,GAAC,GAAE,KAAK,IAAIA,GAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAEG,GAAEF,GAAE,CAAC,GAAE,GAAE,GAAE,GAAEA,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC,GAAEI,KAAEL,IAAE;AAAE,SAAOC,GAAEI,IAAED,IAAE,CAAC,GAAEH,GAAEI,IAAEH,IAAEG,EAAC,GAAEA;AAAC;;;ACA/e,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,aAAW,IAAI,SAAM,KAAK,YAAU,IAAI,SAAM,KAAK,WAAS,IAAI;AAAA,EAAK;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYA,KAAEC,KAAEC,KAAE;AAAC,SAAK,OAAKF,KAAE,KAAK,eAAaC,KAAE,KAAK,cAAYC,KAAE,KAAK,iBAAe,IAAIH,MAAE,KAAK,mBAAiB;AAAA,EAAC;AAAC;;;ACAqiD,IAAM,IAAN,cAAgBI,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,OAAG,KAAK,SAAO,OAAG,KAAK,aAAWC,GAAE,GAAE,GAAE,GAAE,GAAE,KAAK,kBAAgB,OAAG,KAAK,kBAAgB,OAAG,KAAK,cAAY,OAAG,KAAK,kBAAgB,UAAS,KAAK,WAASC,GAAE,MAAK,KAAK,iBAAeD,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,2BAAyB,OAAG,KAAK,aAAWE,GAAE,WAAU,KAAK,cAAY,MAAG,KAAK,iBAAe,MAAG,KAAK,cAAY,MAAG,KAAK,uBAAqB,OAAG,KAAK,UAAQF,GAAE,KAAG,KAAG,GAAE,GAAE,KAAK,UAAQA,GAAE,KAAG,KAAG,GAAE,GAAE,KAAK,gBAAcA,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa,YAAW,KAAK,UAAQ,GAAE,KAAK,eAAa,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,gBAAc,OAAG,KAAK,oBAAkB,MAAG,KAAK,6BAA2B,OAAG,KAAK,gBAAc,OAAG,KAAK,gBAAc,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,KAAI,KAAI,GAAG,GAAE,KAAK,eAAa,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,cAAY,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,OAAG,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,gBAAc,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,GAAE,CAAC,GAAE,KAAK,yBAAuBG,GAAE,GAAE,KAAK,mBAAiB,OAAG,KAAK,kBAAgB,CAAC,GAAE,KAAK,qBAAmB,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,aAAW,MAAG,KAAK,kBAAgBA,IAAE,MAAK,KAAK,mBAAiBC,GAAE,OAAM,KAAK,qBAAmBC,IAAE,KAAK,4BAA0B,OAAG,KAAK,eAAa,OAAG,KAAK,iBAAeC,GAAE;AAAA,EAAO;AAAC;AAAC,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,wBAAsB,KAAK;AAAA,EAAM;AAAC;AAAC,IAAMC,KAAN,MAAM,WAAUN,IAAC;AAAA,EAAC,wBAAwBA,KAAEO,KAAE;AAAC,IAAAA,IAAE,mBAAiBP,IAAE,KAAK,SAAOH,GAAE,QAAOU,IAAE,YAAUP,IAAE,gBAAc,EAAE,QAAOO,IAAE,qCAAmCP,IAAE,KAAK,WAAW,mCAAmC,QAAOO,IAAE,wBAAsBA,IAAE,mBAAiBA,IAAE,+BAA6BA,IAAE,sBAAoBA,IAAE,uBAAqBA,IAAE,mBAAiB,EAAE,UAAQ,EAAE,MAAKA,IAAE,iCAA+BA,IAAE;AAAA,EAAS;AAAA,EAAC,kBAAkBP,KAAE;AAAC,WAAO,KAAK,mBAAmBA,KAAE,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBA,KAAEO,KAAE;AAAC,WAAO,IAAIL,GAAEF,IAAE,MAAKO,IAAE,IAAI,EAAE,MAAM,KAAK,aAAa,GAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BJ,KAAE;AAAC,WAAOA,QAAIA,IAAE,SAAO,EAAE,SAAO,EAAE;AAAA,EAAI;AAAA,EAAC,cAAcA,KAAEO,KAAE;AAAC,UAAMC,KAAE,KAAK,eAAcC,KAAET,QAAIE,GAAE,MAAKL,MAAEG,QAAIE,GAAE;AAAU,WAAO,EAAE,EAAC,UAASM,GAAE,WAASE,GAAE,SAAOF,GAAE,WAASE,GAAE,SAAO,CAACF,GAAE,cAAY,OAAKC,KAAEN,KAAEQ,GAAEX,GAAC,GAAE,SAAQY,GAAEJ,EAAC,IAAEE,GAAEF,GAAE,QAAQ,IAAE,MAAK,WAAU,EAAC,MAAKK,GAAEb,KAAE,KAAK,0BAA0BQ,GAAE,eAAe,CAAC,EAAC,GAAE,aAAYC,MAAGZ,QAAIW,GAAE,aAAWT,KAAE,MAAK,YAAW,GAAE,cAAaS,GAAE,eAAaR,MAAE,MAAK,aAAYQ,GAAE,eAAaD,MAAEL,KAAEN,KAAE,MAAK,eAAca,MAAGZ,MAAE,OAAKE,GAAES,GAAE,YAAY,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,KAAK,yBAAuB,KAAK,cAAc,KAAK,cAAc,sBAAqB,IAAE,GAAE,KAAK,cAAc,KAAK,cAAc,sBAAqB,KAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBR,KAAEO,KAAE;AAAC,WAAOA,MAAE,KAAK,yBAAuB,MAAM,iBAAiBP,KAAEO,GAAC;AAAA,EAAC;AAAC;AAAC,SAASK,GAAEZ,KAAE;AAAC,SAAOA,IAAE,aAAWF,GAAE,QAAM,CAACE,IAAE,kBAAgB,CAACA,IAAE,eAAa,CAACA,IAAE;AAAgB;AAACM,GAAE,SAAO,IAAIC,GAAE,GAAG,MAAI,OAAO,oCAA2B,CAAE;;;ACA7vH,IAAMO,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAOC,GAAE,OAAM,KAAK,mBAAiBC,GAAE,QAAO,KAAK,kBAAgBC,GAAE,MAAK,KAAK,UAAQC,GAAE,UAAS,KAAK,WAASC,GAAE,MAAK,KAAK,uBAAqBC,GAAE,MAAK,KAAK,aAAWC,GAAE,WAAU,KAAK,wBAAsB,EAAE,MAAK,KAAK,kBAAgBC,IAAE,MAAK,KAAK,YAAU,OAAG,KAAK,kBAAgB,OAAG,KAAK,kBAAgB,OAAG,KAAK,oBAAkB,OAAG,KAAK,gBAAc,OAAG,KAAK,oBAAkB,MAAG,KAAK,kBAAgB,OAAG,KAAK,8BAA4B,OAAG,KAAK,qBAAmB,OAAG,KAAK,sBAAoB,OAAG,KAAK,mBAAiB,OAAG,KAAK,2BAAyB,OAAG,KAAK,oBAAkB,OAAG,KAAK,eAAa,OAAG,KAAK,sBAAoB,OAAG,KAAK,yBAAuB,OAAG,KAAK,kBAAgB,OAAG,KAAK,SAAO,OAAG,KAAK,UAAQ,OAAG,KAAK,iBAAe,OAAG,KAAK,0BAAwB,OAAG,KAAK,4BAA0B,OAAG,KAAK,YAAU,OAAG,KAAK,iBAAe,OAAG,KAAK,iCAA+B,OAAG,KAAK,2BAAyB,OAAG,KAAK,qCAAmC,OAAG,KAAK,aAAW,MAAG,KAAK,cAAY,OAAG,KAAK,eAAa,MAAG,KAAK,kBAAgB,OAAG,KAAK,YAAU,OAAG,KAAK,2BAAyB,OAAG,KAAK,8BAA4B,OAAG,KAAK,4BAA0B,OAAG,KAAK,+BAA6B,OAAG,KAAK,uCAAqC;AAAA,EAAE;AAAC;AAAC,EAAE,CAACC,IAAE,EAAC,OAAMR,GAAE,MAAK,CAAC,CAAC,GAAEF,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMP,GAAE,MAAK,CAAC,CAAC,GAAEH,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMN,GAAE,MAAK,CAAC,CAAC,GAAEJ,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAML,GAAE,MAAK,CAAC,CAAC,GAAEL,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMJ,GAAE,MAAK,CAAC,CAAC,GAAEN,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMH,GAAE,MAAK,CAAC,CAAC,GAAEP,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMF,GAAE,MAAK,CAAC,CAAC,GAAER,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAM,EAAE,MAAK,CAAC,CAAC,GAAEV,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,OAAMD,IAAE,MAAK,CAAC,CAAC,GAAET,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,+BAA8B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,kCAAiC,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,sCAAqC,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,+BAA8B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAACU,IAAE,CAAC,GAAEV,GAAE,WAAU,wCAAuC,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEV,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEV,GAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,YAAW,MAAE,CAAC,CAAC,GAAEV,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAACU,IAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEV,GAAE,WAAU,iBAAgB,MAAM;;;ACA7xI,IAAMW,MAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,wBAAwBC,IAAEC,IAAE;AAAC,UAAM,wBAAwBD,IAAEC,EAAC,GAAEA,GAAE,8BAA4B,OAAGA,GAAE,qBAAmB,OAAGA,GAAE,sBAAoB,OAAGA,GAAE,mBAAiB,OAAGA,GAAE,yBAAuB,OAAGA,GAAE,aAAWA,GAAE,WAAUA,GAAE,kBAAgBD,GAAE,cAAaC,GAAE,oBAAkB;AAAA,EAAE;AAAA,EAAC,kBAAkBC,KAAE;AAAC,WAAO,KAAK,mBAAmBA,KAAE,GAAE,MAAM;AAAA,EAAC;AAAC;AAACJ,IAAE,SAAO,IAAIA,GAAE,GAAG,MAAI,OAAO,kCAAyB,CAAE;;;ACAwrB,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAMA,KAAE,CAAC,GAAE,KAAK,gBAAc,MAAG,KAAK,iBAAe,IAAIC,MAAE,KAAK,sBAAoBC,GAAE,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,mBAAmBF,KAAE;AAAC,WAAOA,QAAID,GAAE,UAAQC,QAAID,GAAE,0BAAwBC,QAAID,GAAE,mBAAiB,KAAK,WAAW;AAAA,EAAW;AAAA,EAAC,YAAW;AAAC,UAAMI,MAAE,KAAK;AAAW,QAAG,CAAC,MAAM,UAAU,KAAG,MAAIA,IAAE,aAAa,QAAM;AAAG,UAAK,EAAC,WAAUC,KAAE,iBAAgBC,IAAE,iBAAgBC,IAAE,gBAAeC,GAAC,IAAEJ,KAAEK,KAAE,EAAEJ,GAAC,KAAGA,IAAE,SAAS,OAAO,GAAEK,KAAE,cAAYN,IAAE,cAAaJ,KAAEI,IAAE,UAAQ,GAAEF,KAAEE,IAAE,iBAAeA,IAAE,cAAc,CAAC,IAAE;AAAE,WAAOE,OAAIG,MAAGD,MAAGD,MAAG,CAAC,CAACG,MAAGV,KAAEM,KAAEI,KAAER,KAAEF,KAAES,MAAGD,MAAGD,KAAE,CAAC,CAACG,MAAGV,KAAEU,KAAER,KAAEF;AAAA,EAAC;AAAA,EAAC,iBAAiBI,KAAEC,KAAE;AAAC,WAAO,KAAK,eAAe,SAAOD,KAAE,KAAK,eAAe,mBAAiB,CAAC,CAAC,KAAK,WAAW,iBAAgB,KAAK,eAAe,kBAAgB,CAAC,CAAC,KAAK,WAAW,WAAU,KAAK,eAAe,oBAAkB,KAAK,WAAW,mBAAkB,KAAK,eAAe,YAAU,CAAC,CAAC,KAAK,WAAW,WAAU,KAAK,eAAe,2BAAyB,KAAK,WAAW,0BAAyB,KAAK,eAAe,SAAO,KAAK,WAAW,eAAc,KAAK,eAAe,oBAAkB,EAAE,KAAK,WAAW,cAAc,GAAE,KAAK,eAAe,2BAAyB,EAAE,KAAK,WAAW,qBAAqB,GAAE,KAAK,eAAe,gBAAc,KAAK,WAAW,eAAc,KAAK,eAAe,oBAAkB,KAAK,WAAW,mBAAkB,KAAK,eAAe,mBAAiB,KAAK,WAAW,kBAAiB,KAAK,eAAe,aAAW,KAAK,WAAW,YAAW,KAAK,eAAe,cAAY,KAAK,WAAW,aAAY,KAAK,eAAe,aAAW,KAAK,WAAW,YAAW,EAAE,KAAK,WAAW,eAAe,MAAI,KAAK,eAAe,kBAAgB,KAAK,WAAW,kBAAiB,KAAK,eAAe,eAAa,KAAK,WAAW,cAAa,KAAK,eAAe,WAAS,KAAK,WAAW,gBAAcM,GAAE,OAAK,KAAK,WAAW,UAAS,KAAK,eAAe,sBAAoBL,IAAE,iBAAiB,SAAQ,KAAK,eAAe,kBAAgBA,IAAE,iBAAiB,iBAAgB,KAAK,eAAe,yBAAuB,EAAE,KAAK,WAAW,mBAAmB,GAAED,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,UAAQ,KAAK,eAAe,kBAAgB,KAAK,WAAW,iBAAgB,KAAK,eAAe,kBAAgB,KAAK,WAAW,iBAAgB,KAAK,WAAW,gBAAc,KAAK,eAAe,kBAAgBQ,GAAE,eAAa,KAAK,eAAe,kBAAgB,KAAK,WAAW,eAAa,aAAW,KAAK,WAAW,kBAAgBA,GAAE,OAAK,KAAK,WAAW,eAAa,oBAAkB,KAAK,WAAW,kBAAgBA,GAAE,eAAaA,GAAE,MAAK,KAAK,eAAe,iBAAe,EAAE,KAAK,WAAW,SAAS,KAAG,KAAK,WAAW,UAAU,SAAS,OAAO,GAAE,KAAK,eAAe,iBAAe,KAAK,WAAW,kBAAgB,KAAK,WAAW,sBAAqB,KAAK,eAAe,0BAAwB,CAAC,CAACH,IAAE,WAAW,UAAQ,KAAK,WAAW,aAAY,KAAK,eAAe,UAAQ,KAAK,WAAW,gBAAe,KAAK,eAAe,4BAA0B,CAAC,CAAC,KAAK,WAAW,2BAA0B,KAAK,eAAe,UAAQ,KAAK,WAAW,SAAO,KAAK,WAAW,cAAYM,GAAE,YAAUA,GAAE,SAAOA,GAAE,UAAS,KAAK,eAAe,8BAA4B,CAAC,CAAC,KAAK,WAAW,4BAA2B,KAAK,eAAe,qBAAmB,CAAC,CAAC,KAAK,WAAW,mBAAkB,KAAK,eAAe,sBAAoB,CAAC,CAAC,KAAK,WAAW,oBAAmB,KAAK,eAAe,kBAAgB,EAAE,CAAC,KAAK,WAAW,eAAa,CAAC,KAAK,WAAW,6BAA4B,KAAK,eAAe,uBAAqBN,IAAE,sBAAqB,KAAK,eAAe,eAAaA,IAAE,OAAO,oBAAkBO,IAAE,KAAK,eAAe,YAAU,KAAK,aAAaP,GAAC,GAAE,KAAK,eAAe,2BAAyB,CAAC,CAAC,KAAK,WAAW,6BAA4B,KAAK,eAAe,4BAA0B,CAAC,CAAC,KAAK,WAAW,8BAA6B,KAAK,eAAe,8BAA4B,CAAC,CAAC,KAAK,WAAW,gCAA+B,KAAK,eAAe,+BAA6B,CAAC,CAAC,KAAK,WAAW,iCAAgC,KAAK,eAAe,uCAAqC,CAAC,CAAC,KAAK,WAAW,0CAAyC,KAAK;AAAA,EAAc;AAAA,EAAC,aAAaD,KAAE;AAAC,WAAO,EAAEA,IAAE,OAAO,KAAGA,IAAE,kBAAgB,YAAUA,IAAE,QAAQ,QAAM,cAAYA,IAAE,QAAQ;AAAA,EAAS;AAAA,EAAC,UAAUF,IAAEW,IAAEC,IAAE,GAAEH,IAAEI,IAAE;AAAC,QAAG,EAAE,KAAK,WAAW,cAAc,GAAE;AAAC,YAAMd,MAAEa,GAAE;AAAO,QAAEE,IAAEH,GAAE,EAAE,GAAEA,GAAE,EAAE,GAAEA,GAAE,EAAE,CAAC;AAAE,UAAIX,KAAE;AAAK,cAAOY,GAAE,aAAY;AAAA,QAAC,KAAK,EAAE;AAAO,UAAAZ,KAAE,EAAE,GAAEc,EAAC;AAAE;AAAA,QAAM,KAAK,EAAE;AAAM,UAAAd,KAAEG,GAAE,GAAEY,EAAC;AAAA,MAAC;AAAC,UAAIF,KAAE;AAAE,YAAMG,KAAEjB,GAAEkB,IAAEH,IAAEf,IAAE,GAAG,GAAEmB,KAAEb,GAAEW,EAAC,GAAEG,KAAE,EAAEH,IAAEA,IAAE,IAAEE,EAAC;AAAE,UAAIE,KAAE;AAAK,WAAK,WAAW,0BAAwBA,KAAE,EAAEpB,IAAEmB,EAAC,IAAGN,MAAG,EAAEd,KAAEmB,IAAE,KAAK,WAAW,gBAAeE,MAAG,GAAE,KAAK,WAAW,qBAAqB,GAAE,EAAEpB,IAAEA,IAAEa,EAAC,GAAE,EAAE,GAAEb,IAAEY,GAAE,UAAU,eAAe,GAAE,IAAEb,GAAEsB,IAAE,GAAE,CAAC,GAAEZ,KAAEV,GAAE,GAAEU,IAAE,CAAC;AAAA,IAAC;AAAC,IAAAW,GAAEpB,IAAEY,IAAE,GAAEH,IAAE,EAAEG,GAAE,cAAc,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAad,KAAEG,KAAE;AAAC,QAAGA,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,SAAOI,QAAIJ,GAAE,UAAQI,QAAIJ,GAAE,UAAQI,QAAIJ,GAAE,mBAAiBI,QAAIJ,GAAE,0BAAwBI,QAAIJ,GAAE,aAAWI,QAAIJ,GAAE,uBAAsB;AAAC,aAAOC,SAAK,KAAK,WAAW,cAAY,KAAK,WAAW,aAAWF,GAAE,uBAAqBA,GAAE,4CAA0CA,GAAE,oBAAkBE,QAAIF,GAAE;AAAA,IAAe;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBE,KAAE;AAAC,WAAO,IAAIuB,GAAEvB,GAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,WAAO,IAAII,IAAE,KAAK,mBAAmB;AAAA,EAAC;AAAC;AAAC,IAAMmB,KAAN,cAAgBxB,GAAC;AAAA,EAAC,YAAYC,KAAE;AAAC,UAAM,EAAC,GAAGA,KAAE,GAAGA,IAAE,SAAS,WAAU,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,KAAE;AAAC,IAAAA,IAAE,UAAU,YAAU,KAAK,UAAU,WAAW,wBAAsB,KAAK,UAAU,cAAc,EAAC,sBAAqBA,IAAE,UAAU,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,KAAE;AAAC,IAAAA,IAAE,iBAAe,KAAK,UAAU,WAAW,gBAAc,KAAK,UAAU,cAAc,EAAC,cAAaA,IAAE,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,KAAE;AAAC,SAAK,YAAUD,GAAE,SAAO,KAAK,YAAUA,GAAE,UAAQ,KAAK,mBAAmBC,GAAC,GAAE,KAAK,qBAAqBA,GAAC;AAAG,UAAMI,MAAE,KAAK,UAAU;AAAW,SAAK,cAAcA,IAAE,SAAS;AAAE,UAAMC,KAAEL,IAAE,OAAO;AAA2B,WAAO,EAAEI,IAAE,QAAOC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,EAAE,CAAC,GAAE,KAAK,UAAU,cAAc,KAAK,qBAAqB,GAAE,KAAK,gBAAgBD,IAAE,gBAAcD,MAAEiB,IAAEpB,GAAC;AAAA,EAAC;AAAC;AAAC,IAAMwB,KAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,yBAAuB,OAAG,KAAK,gBAAc,OAAG,KAAK,oBAAkB;AAAA,EAAE;AAAC;AAAC,IAAM,IAAE,IAAIA;AAAE,SAAStB,GAAEF,KAAE;AAAC,QAAMG,MAAE,EAAE,EAAE,MAAMsB,GAAE,QAAQ,EAAE,MAAMA,GAAE,MAAM,GAAErB,MAAEJ,IAAE,aAAWA,IAAE,mBAAiBA,IAAE,8BAA4BA,IAAE,qBAAmBA,IAAE;AAAmB,SAAOA,IAAE,qBAAmBG,IAAE,MAAMsB,GAAE,OAAO,GAAErB,OAAGD,IAAE,MAAMsB,GAAE,GAAG,GAAEzB,IAAE,mBAAiBG,IAAE,OAAOsB,GAAE,KAAK,GAAEzB,IAAE,mBAAiBG,IAAE,OAAOsB,GAAE,WAAW,GAAE,IAAI,2CAA2C,KAAGtB,IAAE,OAAOsB,GAAE,qBAAqB,GAAEtB;AAAC;AAAC,IAAMmB,KAAE,EAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkBN,KAAEZ,GAAE,GAAE,GAAE,CAAC;AAA3B,IAA6B,IAAE,EAAE;AAAjC,IAAmC,IAAE,EAAE;AAAvC,IAAyCW,KAAE,EAAE;AAA7C,IAA+CG,KAAE,EAAE;;;ACArwN,IAAMQ,KAAE,EAAE,UAAU,mDAAmD;AAAE,eAAeC,GAAEC,KAAEC,KAAE;AAAC,QAAMC,KAAE,MAAM,EAAEF,KAAEC,GAAC,GAAEE,KAAE,MAAMC,GAAEF,GAAE,sBAAoB,CAAC,GAAED,GAAC;AAAE,MAAII,KAAE;AAAE,aAAUC,OAAKH,GAAE,KAAGA,GAAE,eAAeG,GAAC,GAAE;AAAC,UAAMN,MAAEG,GAAEG,GAAC;AAAE,IAAAD,OAAGL,OAAA,gBAAAA,IAAG,SAAMA,IAAE,MAAM,QAAMA,IAAE,MAAM,SAAO,IAAE;AAAA,EAAC;AAAC,SAAM,EAAC,UAASE,IAAE,UAASC,IAAE,MAAKE,KAAEL,GAAEE,EAAC,EAAC;AAAC;AAAC,eAAe,EAAEI,KAAEJ,IAAE;AAAC,QAAMC,KAAE,EAAED,EAAC,KAAGA,GAAE;AAAoB,MAAGC,GAAE,QAAOI,GAAED,KAAEH,IAAED,EAAC;AAAE,QAAMM,KAAE,MAAM,EAAE,EAAEF,KAAEN,GAAEE,EAAC,CAAC,CAAC;AAAE,MAAG,SAAKM,GAAE,GAAG,QAAOA,GAAE,MAAM;AAAK,IAAEA,GAAE,KAAK,GAAEC,GAAED,GAAE,KAAK;AAAC;AAAC,eAAeD,GAAEP,KAAEM,KAAEJ,IAAE;AAAC,QAAMC,KAAE,MAAM,EAAEG,IAAE,QAAQN,KAAE,QAAOE,EAAC,CAAC;AAAE,MAAG,SAAKC,GAAE,GAAG,QAAOA,GAAE;AAAM,IAAEA,GAAE,KAAK,GAAEM,GAAEN,GAAE,MAAM,QAAQ,GAAG;AAAC;AAAC,SAASM,GAAET,KAAE;AAAC,QAAM,IAAIG,GAAE,IAAG,uCAAuCH,GAAC,EAAE;AAAC;AAAC,SAASU,GAAEV,KAAE;AAAC,QAAMC,MAAED,IAAE,QAAOM,MAAEL,IAAE;AAAS,MAAIC,KAAE;AAAG,UAAOD,IAAE,qBAAmBH,GAAE,KAAK,yCAAyC,GAAEI,KAAE,QAAID,IAAE,UAAS;AAAA,IAAC,KAAI;AAAoB;AAAA,IAAM,KAAI;AAAA,IAAU,KAAK;AAAA,IAAK,KAAK,QAAO;AAAC,YAAMD,MAAEC,IAAE;AAAM,UAAGD,KAAE;AAAC,YAAGC,IAAE,iBAAiB,YAAUK,OAAKL,IAAE,kBAAiB;AAAC,gBAAMA,MAAED,IAAEM,GAAC;AAAE,UAAAL,OAAGA,IAAE,UAAQ,QAAMA,IAAE,aAAW,aAAWA,IAAE,cAAYH,GAAE,KAAK,8CAA8CG,IAAE,SAAS,uCAAuC,GAAEC,KAAE,QAAI,QAAMD,IAAE,oBAAkB,MAAIA,IAAE,qBAAmBH,GAAE,KAAK,oDAAoDG,IAAE,gBAAgB,kCAAkC,GAAEC,KAAE,WAAMJ,GAAE,KAAK,uDAAuDQ,GAAC,aAAa,GAAEJ,KAAE;AAAA,QAAG;AAAA,MAAC,MAAM,CAAAJ,GAAE,KAAK,uCAAuC,GAAEI,KAAE;AAAG;AAAA,IAAK;AAAA,IAAC;AAAQ,MAAAJ,GAAE,KAAK,yBAAyBQ,GAAC,GAAG,GAAEJ,KAAE;AAAA,EAAE;AAAC,EAAAF,IAAE,OAAO,aAAWF,GAAE,KAAK,4BAA4B,GAAEI,KAAE;AAAI,QAAMC,KAAEH,IAAE,OAAO;AAAiB,aAAUK,MAAKF,IAAE;AAAC,IAAAA,GAAEE,EAAC,EAAE,WAASP,GAAE,KAAK,qEAAqE,GAAEI,KAAE;AAAA,EAAG;AAAC,SAAOA;AAAC;AAAC,SAASS,GAAEX,KAAEC,KAAE;AAAC,QAAMK,MAAE,IAAI,SAAMJ,KAAE,IAAI,SAAMC,KAAE,IAAI,SAAMS,KAAE,IAAIX,MAAEY,KAAEb,IAAE,UAASc,KAAER,GAAE,MAAMO,GAAE,WAAS,OAAM,MAAM;AAAE,EAAAE,GAAE,SAASD,EAAC;AAAE,QAAME,KAAEH,GAAE,MAAM,MAAKI,KAAEJ,GAAE,MAAM,YAAWK,KAAEL,GAAE,uBAAqB,CAAC,GAAEM,KAAEnB,IAAE;AAAS,MAAIF,KAAE;AAAE,QAAMC,KAAE,oBAAI;AAAI,WAAQqB,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,UAAMpB,MAAEiB,GAAEG,EAAC;AAAE,QAAG,CAACV,GAAEV,GAAC,EAAE;AAAS,UAAMa,KAAEQ,GAAErB,GAAC,GAAEsB,KAAEtB,IAAE,OAAO,kBAAiBc,KAAE,CAAC;AAAE,eAAUb,OAAKqB,IAAE;AAAC,YAAMtB,MAAEsB,GAAErB,GAAC,GAAEK,MAAEN,IAAE;AAAO,MAAAc,GAAE,KAAK,CAACb,KAAE,IAAIE,GAAEG,KAAEN,IAAE,kBAAiB,IAAE,CAAC,CAAC;AAAA,IAAC;AAAC,UAAMgB,KAAE,CAAC;AAAE,QAAG,wBAAsBhB,IAAE,OAAO,UAAS;AAAC,YAAMC,MAAED,IAAE,OAAO;AAAM,iBAAUA,OAAKC,IAAE,CAAAe,GAAE,KAAK,CAAChB,KAAEC,IAAED,GAAC,EAAE,MAAM,CAAC;AAAA,IAAC;AAAC,UAAMuB,KAAEV,GAAE,SAAQN,KAAEY,MAAGA,GAAEI,EAAC;AAAE,QAAGhB,MAAG,CAACR,GAAE,IAAIwB,EAAC,GAAE;AAAC,YAAK,EAAC,OAAMvB,KAAE,QAAOC,IAAC,IAAEM,IAAED,MAAE,IAAI,EAAEN,KAAEC,GAAC;AAAE,MAAAC,GAAE,KAAKI,GAAC,GAAEP,GAAE,IAAIwB,IAAEjB,GAAC;AAAA,IAAC;AAAC,UAAMG,KAAEV,GAAE,IAAIwB,EAAC,GAAEZ,KAAEF,KAAEA,GAAE,KAAG,QAAOe,KAAEX,GAAE;AAAS,QAAIT,KAAEQ,GAAE,IAAIY,IAAED,EAAC;AAAE,QAAG,EAAEnB,EAAC,GAAE;AAAC,YAAMJ,MAAEkB,GAAEM,GAAE,UAAUA,GAAE,YAAY,GAAG,IAAE,CAAC,CAAC,EAAE;AAAO,YAAIxB,IAAE,iBAAeA,IAAE,eAAa;AAAG,YAAMM,MAAEC,MAAGA,GAAE,mBAAkBL,KAAEF,IAAE,eAAa,KAAG,mBAAiBM,OAAG,0BAAwBA,KAAEH,KAAEI,KAAE,EAAEA,GAAE,iBAAiB,IAAE,QAAOC,KAAE,EAAC,SAAQR,GAAEA,IAAE,OAAO,GAAE,SAAQA,GAAEA,IAAE,OAAO,GAAE,SAAQ,KAAGA,IAAE,gBAAc,IAAG,aAAYE,IAAE,kBAAiBC,IAAE,oBAAmB,MAAI,WAAUQ,IAAE,wBAAuB,MAAG,aAAY,MAAG,UAAST,GAAE,MAAK,cAAaF,IAAE,wBAAsB,QAAO,2BAA0B,CAAC,CAACO,MAAG,CAAC,CAACA,GAAE,OAAO,iBAAgB;AAAE,QAAEN,GAAC,KAAGA,IAAE,uBAAqB,OAAO,OAAOO,IAAEP,IAAE,mBAAmB,GAAEG,KAAE,IAAIM,GAAEF,EAAC,GAAEI,GAAE,IAAIY,IAAED,IAAEnB,EAAC;AAAA,IAAC;AAAC,IAAAD,GAAE,KAAKC,EAAC;AAAE,UAAMW,KAAE,IAAI,EAAEX,IAAEU,IAAEE,EAAC;AAAE,IAAAlB,MAAGkB,GAAE,WAASA,GAAE,SAAS,SAAO,GAAEV,IAAE,KAAKS,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,iBAAgB,CAAC,EAAC,MAAKC,IAAE,gBAAe,EAAC,UAASd,IAAE,WAAUC,IAAE,YAAWG,IAAC,GAAE,aAAYO,GAAE,MAAM,aAAY,kBAAiBf,IAAE,cAAa,KAAI,CAAC,GAAE,sBAAqB0B,GAAElB,GAAC,EAAC;AAAC;AAAC,SAASkB,GAAExB,KAAE;AAAC,QAAMC,MAAEwB,GAAE;AAAE,SAAOzB,IAAE,QAAS,CAAAA,QAAG;AAAC,UAAMM,MAAEN,IAAE;AAAa,MAAEM,GAAC,MAAI,EAAEL,KAAEK,IAAE,KAAK,GAAE,EAAEL,KAAEK,IAAE,KAAK;AAAA,EAAE,CAAE,GAAEL;AAAC;AAAC,eAAeG,GAAEJ,KAAEC,KAAE;AAAC,QAAMK,MAAE,CAAC;AAAE,aAAUM,MAAKZ,KAAE;AAAC,UAAME,KAAEF,IAAEY,EAAC,GAAET,KAAED,GAAE,OAAO,CAAC,EAAE;AAAK,QAAG,CAACC,IAAE;AAAC,MAAAL,GAAE,KAAK,yDAAyD;AAAE;AAAA,IAAQ;AAAC,UAAMU,KAAEN,GAAE,WAAS,aAAWC,IAAEiB,KAAE,yBAAuBR,IAAEC,KAAE,WAASX,GAAE,WAASA,GAAE,qBAAmB,iBAAe,QAAOoB,KAAE,EAAC,cAAa,MAAG,MAAK,EAAC,GAAE,EAAE,QAAO,GAAE,EAAE,OAAM,GAAE,kBAAiB,EAAET,EAAC,MAAIO,GAAE,OAAM,GAAE,IAAE,EAAEnB,GAAC,KAAGA,IAAE,kBAAgB,QAAQ,QAAQ,IAAI,IAAEA,GAAEO,IAAEP,GAAC;AAAE,IAAAK,IAAE,KAAK,EAAE,KAAM,CAAAN,SAAI,EAAC,OAAMoB,IAAE,OAAMpB,KAAE,QAAOsB,IAAE,mBAAkBT,GAAC,EAAG,CAAC;AAAA,EAAC;AAAC,QAAMX,KAAE,MAAM,QAAQ,IAAII,GAAC,GAAEH,KAAE,CAAC;AAAE,aAAUE,MAAKH,GAAE,CAAAC,GAAEE,GAAE,KAAK,IAAEA;AAAE,SAAOF;AAAC;AAAC,SAAS,EAAEH,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAO,aAAOoB,GAAE;AAAA,IAAK,KAAI;AAAsB,aAAOA,GAAE;AAAA,IAAU,KAAI;AAAO,aAAOA,GAAE;AAAA,IAAO;AAAQ,aAAOA,GAAE;AAAA,EAAK;AAAC;AAAC,SAASC,GAAErB,KAAE;AAAC,QAAMC,MAAED,IAAE;AAAO,SAAM,EAAC,IAAG,GAAE,UAASC,IAAE,UAAS,SAAQA,IAAE,SAAQ,QAAOA,IAAE,QAAO;AAAC;AAAC,IAAMc,KAAE,IAAIT,GAAE,GAAE,GAAE,MAAM;;;ACA19F,eAAe,GAAGoB,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAG,EAAEF,EAAC,CAAC;AAAE,MAAG,WAASE,GAAE,UAAS;AAAC,UAAMC,MAAE,OAAMF,GAAE,QAAMA,GAAE,MAAM,SAASC,GAAE,KAAID,EAAC,IAAEG,GAAEF,GAAE,KAAID,EAAC,IAAG,EAAC,iBAAgBI,KAAE,sBAAqBC,IAAC,IAAEC,GAAEJ,KAAEF,EAAC;AAAE,WAAM,EAAC,MAAKI,KAAE,sBAAqBC,KAAE,sBAAqB,OAAG,QAAO,KAAE;AAAA,EAAC;AAAC,QAAME,KAAE,OAAMP,GAAE,QAAMA,GAAE,MAAM,SAASC,GAAE,KAAID,IAAE,CAAC,CAACA,GAAE,MAAM,IAAE,EAAE,IAAIO,GAAEP,GAAE,mBAAmB,GAAEC,GAAE,KAAID,IAAEA,GAAE,MAAM,IAAGQ,KAAE,EAAED,GAAE,MAAM,MAAK,qBAAqB,GAAEE,KAAEF,GAAE,KAAK,wBAAsB,EAAEC,EAAC,KAAGD,GAAE,KAAK,IAAI,SAAS,kBAAkB;AAAE,EAAAE,MAAG,CAACF,GAAE,WAAW,sBAAoBA,GAAE,WAAW,oBAAkB,MAAG,GAAGA,IAAEC,EAAC;AAAG,QAAME,KAAE,CAAC,CAACV,GAAE,QAAOW,KAAEJ,GAAE,KAAK,uBAAqB,EAAC,QAAOG,IAAE,aAAY,OAAG,eAAcD,IAAE,YAAW,CAAC,GAAE,GAAE,GAAE,EAAC,IAAE,EAAC,QAAOC,IAAE,aAAY,OAAG,eAAc,OAAG,YAAW,CAAC,GAAE,GAAE,GAAE,EAAC,GAAEE,KAAE,EAAC,GAAGZ,GAAE,qBAAoB,eAAcS,GAAC,GAAE,EAAC,iBAAgBI,IAAE,sBAAqBC,GAAC,IAAE,GAAGP,IAAEI,IAAEC,IAAEZ,GAAE,gBAAc,QAAMC,GAAE,oBAAkB,EAAC,cAAa,KAAE,IAAE,EAAC,cAAa,OAAG,gBAAeA,GAAE,kBAAiB,CAAC;AAAE,SAAM,EAAC,MAAKY,IAAE,sBAAqBC,IAAE,sBAAqBP,GAAE,KAAK,sBAAqB,QAAO,MAAE;AAAC;AAAC,SAAS,GAAGL,KAAE;AAAC,QAAME,MAAEF,IAAE,MAAM,oCAAoC;AAAE,MAAGE,IAAE,QAAM,EAAC,UAAS,QAAO,KAAIA,IAAE,CAAC,GAAE,mBAAkB,QAAMA,IAAE,CAAC,IAAE,OAAOA,IAAE,CAAC,CAAC,IAAE,KAAI;AAAE,SAAOF,IAAE,MAAM,wBAAwB,IAAE,EAAC,UAAS,QAAO,KAAIA,KAAE,mBAAkB,KAAI,IAAE,EAAC,UAAS,WAAU,KAAIA,KAAE,mBAAkB,KAAI;AAAC;AAAC,SAAS,GAAGA,KAAEE,KAAEL,IAAEC,IAAE;AAAC,QAAMC,KAAEC,IAAE,OAAMK,KAAE,IAAI,SAAMC,KAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAIC,KAAET,GAAE,KAAK,QAAOU,KAAEI,GAAE;AAAE,SAAOd,GAAE,KAAK,QAAS,CAACC,KAAEU,OAAI;AAAC,UAAMC,KAAE,SAAKb,GAAE,iBAAeU,KAAE,KAAG,MAAIE,MAAGF,KAAE,KAAG,MAAIE,OAAI,UAAKZ,GAAE,gBAAc,QAAMA,GAAE,kBAAgBY,OAAIZ,GAAE;AAAe,QAAGa,MAAG,MAAID,GAAE;AAAO,UAAME,KAAE,IAAIV,GAAEF,IAAE,MAAKA,IAAE,cAAa,CAAC,GAAE,GAAE,CAAC,CAAC;AAAE,IAAAA,IAAE,MAAM,QAAS,CAAAA,QAAG;AAAC,YAAMF,KAAEa,KAAE,IAAIG,GAAE,CAAC,CAAC,IAAE,GAAGf,IAAEC,KAAEY,IAAEV,KAAEL,IAAES,IAAEC,EAAC,GAAE,EAAC,UAASF,IAAE,aAAYG,GAAC,IAAE,GAAGR,KAAE,EAAEF,EAAC,IAAEA,KAAE,IAAIgB,GAAE,CAAC,CAAC,CAAC,GAAE,IAAET,GAAE;AAAa,QAAE,CAAC,KAAG,MAAIK,OAAI,EAAED,IAAE,EAAE,KAAK,GAAE,EAAEA,IAAE,EAAE,KAAK,IAAG,EAAEX,EAAC,MAAIc,GAAE,eAAe,WAAW,KAAKP,EAAC,GAAEO,GAAE,oBAAkBJ;AAAA,IAAE,CAAE,GAAEG,MAAGN,GAAE,KAAKO,EAAC;AAAA,EAAC,CAAE,GAAE,EAAC,iBAAgBP,IAAE,sBAAqBI,GAAC;AAAC;AAAC,SAAS,GAAGT,KAAEE,KAAEJ,IAAEC,IAAEM,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEN,IAAE,YAAUA,IAAE,WAAW,SAAO,YAAU,OAAKA,IAAE,WAAW,QAAM,WAAS,OAAKA,IAAE,WAAW,YAAU,eAAa,OAAKA,IAAE,WAAW,UAAQ,aAAW,KAAIO,KAAET,IAAE,UAAU,IAAIE,IAAE,QAAQ,GAAEQ,KAAE,EAAER,IAAE,WAAW,SAAS,GAAES,KAAE,EAAET,IAAE,WAAW,MAAM;AAAE,MAAG,EAAEO,EAAC,EAAE,QAAO;AAAK,QAAMG,KAAE,GAAGH,GAAE,SAAS;AAAE,MAAG,CAACH,GAAE,IAAIE,EAAC,GAAE;AAAC,QAAGE,IAAE;AAAC,YAAMR,MAAE,CAACA,KAAEL,MAAE,UAAK;AAAC,YAAG,EAAEK,GAAC,KAAG,CAACK,GAAE,IAAIL,GAAC,GAAE;AAAC,gBAAMJ,KAAEE,IAAE,SAAS,IAAIE,GAAC;AAAE,cAAG,EAAEJ,EAAC,GAAE;AAAC,kBAAME,MAAEF,GAAE;AAAK,YAAAS,GAAE,IAAIL,KAAE,IAAI,EAAEA,GAAEF,GAAC,IAAEA,IAAE,OAAKA,KAAE,EAAC,GAAGF,GAAE,YAAW,kBAAiB,CAACI,GAAEF,GAAC,KAAGH,KAAE,UAASK,GAAEF,GAAC,KAAG,EAAEA,IAAE,QAAQ,IAAEA,IAAE,WAAS,OAAM,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAE,MAAAE,IAAEO,GAAE,cAAaG,OAAIL,GAAE,MAAM,GAAEL,IAAEO,GAAE,aAAa,GAAEP,IAAEO,GAAE,gBAAgB,GAAEP,IAAEO,GAAE,eAAe,GAAEP,IAAEO,GAAE,wBAAwB;AAAA,IAAC;AAAC,UAAMZ,KAAEY,GAAE,MAAM,CAAC,MAAI,IAAEZ,KAAGC,KAAEW,GAAE,MAAM,CAAC,MAAI,IAAEZ,KAAGkB,KAAEN,GAAE,MAAM,CAAC,MAAI,IAAEZ,KAAGmB,KAAEP,GAAE,eAAe,CAAC,MAAI,IAAEZ,KAAGoB,KAAER,GAAE,eAAe,CAAC,MAAI,IAAEZ,KAAGqB,KAAET,GAAE,eAAe,CAAC,MAAI,IAAEZ,KAAGsB,KAAE,EAAEV,GAAE,YAAY,KAAGC,KAAEH,GAAE,IAAIE,GAAE,YAAY,IAAE;AAAK,IAAAH,GAAE,IAAIE,IAAE,IAAIM,GAAE,EAAC,GAAGf,IAAE,aAAYa,OAAIL,GAAE,OAAM,iBAAgBP,IAAE,QAAO,kBAAiBY,IAAE,oBAAmBH,GAAE,aAAY,SAAQ,CAACZ,IAAEC,IAAEiB,EAAC,GAAE,SAAQ,CAAClB,IAAEC,IAAEiB,EAAC,GAAE,SAAQN,GAAE,SAAQ,aAAYA,GAAE,aAAY,iBAAgB,iBAAgB,UAASA,GAAE,cAAYJ,GAAE,OAAKA,GAAE,MAAK,iBAAgB,CAAC,CAACH,IAAE,WAAW,OAAM,mBAAkB,CAAC,CAACA,IAAE,WAAW,SAAQ,YAAWS,KAAEL,GAAE,YAAUA,GAAE,kBAAiB,aAAY,MAAG,aAAY,MAAG,WAAU,EAAEa,EAAC,IAAEA,GAAE,KAAG,QAAO,cAAaV,GAAE,cAAa,iBAAgB,EAAEA,GAAE,aAAa,KAAGC,KAAEH,GAAE,IAAIE,GAAE,aAAa,EAAE,KAAG,QAAO,2BAA0B,EAAEU,EAAC,KAAG,CAAC,CAACA,GAAE,OAAO,kBAAiB,oBAAmB,EAAEV,GAAE,gBAAgB,KAAGC,KAAEH,GAAE,IAAIE,GAAE,gBAAgB,EAAE,KAAG,QAAO,mBAAkB,EAAEA,GAAE,eAAe,KAAGC,KAAEH,GAAE,IAAIE,GAAE,eAAe,EAAE,KAAG,QAAO,4BAA2B,EAAEA,GAAE,wBAAwB,KAAGC,KAAEH,GAAE,IAAIE,GAAE,wBAAwB,EAAE,KAAG,QAAO,gBAAe,CAACO,IAAEC,IAAEC,EAAC,GAAE,YAAW,CAACT,GAAE,gBAAeA,GAAE,iBAAgBV,GAAE,WAAW,CAAC,CAAC,GAAE,aAAY,OAAG,6BAA4BU,GAAEA,GAAE,qBAAqB,GAAE,8BAA6BA,GAAEA,GAAE,sBAAsB,GAAE,iCAAgCA,GAAEA,GAAE,yBAAyB,GAAE,gCAA+BA,GAAEA,GAAE,wBAAwB,GAAE,yCAAwCA,GAAEA,GAAE,iCAAiC,GAAE,GAAGJ,GAAC,CAAC,CAAC;AAAA,EAAC;AAAC,QAAM,IAAEC,GAAE,IAAIE,EAAC;AAAE,MAAGV,GAAE,eAAe,UAAU,KAAK,CAAC,GAAEY,IAAE;AAAC,UAAMV,MAAE,CAAAA,QAAG;AAAC,QAAEA,GAAC,KAAGF,GAAE,eAAe,SAAS,KAAKS,GAAE,IAAIP,GAAC,CAAC;AAAA,IAAC;AAAE,IAAAA,IAAES,GAAE,YAAY,GAAET,IAAES,GAAE,aAAa,GAAET,IAAES,GAAE,gBAAgB,GAAET,IAAES,GAAE,eAAe,GAAET,IAAES,GAAE,wBAAwB;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAAS,GAAGT,KAAEE,KAAE;AAAC,QAAML,KAAEG,IAAE,WAAW,SAAS,OAAMD,KAAE,GAAGC,IAAE,WAASH,IAAEG,IAAE,aAAa,GAAEK,KAAEF,GAAEJ,IAAEF,EAAC;AAAE,EAAAK,GAAEG,IAAEL,IAAE,WAAW,UAASA,IAAE,SAAS;AAAE,QAAMM,KAAE,CAAC,CAACc,GAAE,UAAS,IAAItB,GAAEO,GAAE,aAAYA,GAAE,cAAa,IAAE,CAAC,CAAC,GAAEE,KAAE,CAAC,CAACa,GAAE,UAASrB,EAAC,CAAC;AAAE,MAAG,EAAEC,IAAE,WAAW,MAAM,GAAE;AAAC,UAAME,MAAEC,GAAEJ,IAAEF,EAAC;AAAE,IAAAmB,GAAE,IAAGhB,IAAE,SAAS,GAAEG,GAAED,KAAEF,IAAE,WAAW,QAAO,EAAE,GAAEM,GAAE,KAAK,CAACc,GAAE,QAAO,IAAItB,GAAEI,IAAE,aAAYA,IAAE,cAAa,IAAE,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACa,GAAE,QAAOrB,EAAC,CAAC;AAAA,EAAC;AAAC,MAAG,EAAEC,IAAE,WAAW,OAAO,GAAE;AAAC,UAAME,MAAEC,GAAEM,IAAEZ,EAAC;AAAE,IAAAmB,GAAE,IAAGhB,IAAE,SAAS,GAAEG,GAAED,KAAEF,IAAE,WAAW,SAAQ,EAAE,GAAEM,GAAE,KAAK,CAACc,GAAE,SAAQ,IAAItB,GAAEI,IAAE,aAAYA,IAAE,cAAa,IAAE,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACa,GAAE,SAAQrB,EAAC,CAAC;AAAA,EAAC;AAAC,MAAG,EAAEC,IAAE,WAAW,SAAS,GAAE;AAAC,UAAME,MAAEC,GAAE,GAAEN,EAAC;AAAE,IAAAQ,GAAEH,KAAEF,IAAE,WAAW,SAAS,GAAEM,GAAE,KAAK,CAACc,GAAE,KAAI,IAAItB,GAAEI,IAAE,aAAYA,IAAE,cAAa,IAAE,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACa,GAAE,KAAIrB,EAAC,CAAC;AAAA,EAAC;AAAC,MAAG,EAAEC,IAAE,WAAW,KAAK,GAAE;AAAC,UAAME,MAAEC,GAAEe,IAAErB,EAAC;AAAE,QAAG,MAAIG,IAAE,WAAW,MAAM,aAAa,CAAAA,IAAE,WAAW,iBAAiBS,KAAEZ,GAAEK,KAAEF,IAAE,WAAW,OAAM,GAAG,IAAEA,IAAE,WAAW,iBAAiBkB,KAAElB,GAAEE,KAAEF,IAAE,WAAW,KAAK,IAAEA,IAAE,WAAW,iBAAiB,KAAGH,GAAEK,KAAEF,IAAE,WAAW,OAAM,IAAE,GAAG;AAAA,SAAM;AAAC,MAAAE,GAAEA,KAAE,KAAI,KAAI,KAAI,GAAG;AAAE,YAAMC,MAAE,IAAIiB,GAAElB,IAAE,QAAO,GAAE,CAAC;AAAE,MAAAF,IAAE,WAAW,iBAAiBD,KAAE,EAAEI,KAAEH,IAAE,WAAW,OAAM,GAAG,IAAEA,IAAE,WAAW,iBAAiBoB,KAAEpB,GAAGG,KAAEH,IAAE,WAAW,KAAK,IAAEA,IAAE,WAAW,iBAAiB,KAAG,EAAEG,KAAEH,IAAE,WAAW,OAAM,IAAE,GAAG;AAAA,IAAC;AAAC,IAAAM,GAAE,KAAK,CAACc,GAAE,OAAM,IAAItB,GAAEI,IAAE,aAAYA,IAAE,cAAa,IAAE,CAAC,CAAC,GAAEK,GAAE,KAAK,CAACa,GAAE,OAAMrB,EAAC,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,UAAS,IAAI,EAAEG,KAAEI,IAAEC,EAAC,GAAE,aAAYV,GAAC;AAAC;AAAC,IAAM,KAAGG,GAAE;AAAE,SAAS,GAAGA,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAQ,aAAOO,GAAE;AAAA,IAAM,KAAI;AAAO,aAAOA,GAAE;AAAA,IAAK,KAAI;AAAA,IAAS,KAAK;AAAA,IAAK,KAAK;AAAO,aAAOA,GAAE;AAAA,EAAM;AAAC;AAAC,SAAS,GAAGP,KAAEE,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAKY,GAAE;AAAU,aAAOjB,GAAEG,GAAC;AAAA,IAAE,KAAKc,GAAE;AAAe,aAAOH,GAAEX,GAAC;AAAA,IAAE,KAAKc,GAAE;AAAa,aAAOf,GAAEC,GAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,KAAEE,KAAE;AAAC,WAAQC,MAAE,GAAEA,MAAEH,IAAE,MAAM,KAAK,QAAO,EAAEG,KAAE;AAAC,UAAML,KAAEE,IAAE,MAAM,KAAKG,GAAC;AAAE,eAAUJ,MAAKD,GAAE,OAAM;AAAC,YAAMA,KAAEC,GAAE,WAAW;AAAO,UAAG,EAAED,EAAC,EAAE;AAAO,YAAMkB,KAAEjB,GAAE,WAAW,UAASkB,KAAED,GAAE,OAAMG,KAAE,EAAE,GAAEE,KAAE,EAAE,GAAEC,KAAE,EAAE,GAAEC,KAAEpB,GAAEe,IAAED,EAAC,GAAEO,KAAErB,GAAEJ,IAAEkB,EAAC,GAAEQ,KAAE,EAAEzB,GAAE,GAAED,GAAE,SAAS;AAAE,eAAQF,KAAE,GAAEA,KAAEoB,IAAEpB,MAAI;AAAC,QAAAmB,GAAE,OAAOnB,IAAEwB,EAAC,GAAEvB,GAAE,OAAOD,IAAEsB,EAAC,GAAE,EAAEE,IAAEA,IAAEtB,GAAE,SAAS,GAAEC,GAAEsB,IAAED,IAAEnB,IAAE,MAAM,GAAE,EAAEoB,IAAEA,IAAEpB,IAAE,MAAM;AAAE,cAAMG,KAAEiB,GAAE,CAAC,GAAEhB,KAAER,GAAEwB,EAAC,GAAE,IAAE,KAAK,IAAI,OAAI,OAAIhB,KAAEA,IAAE,CAAC;AAAE,UAAEgB,IAAEA,IAAEpB,IAAE,MAAM,GAAE,SAAOuB,MAAG,EAAEH,IAAEA,IAAEG,EAAC,GAAE,EAAEH,IAAEA,EAAC,GAAEnB,MAAE,MAAIH,IAAE,MAAM,KAAK,UAAQA,IAAE,MAAM,KAAK,SAAO,KAAG,EAAEsB,IAAEA,IAAEH,IAAEd,KAAE,KAAG,MAAG,KAAK,IAAI,KAAGA,KAAE,KAAI,CAAC,CAAC,GAAEmB,GAAE,OAAO3B,IAAEyB,EAAC,GAAEC,GAAE,IAAI1B,IAAE,GAAE,MAAI,CAAC,GAAE0B,GAAE,IAAI1B,IAAE,GAAE,MAAI,CAAC,GAAE0B,GAAE,IAAI1B,IAAE,GAAE,MAAI,CAAC,GAAE0B,GAAE,IAAI1B,IAAE,GAAE,GAAG;AAAA,MAAC;AAAC,MAAAE,GAAE,WAAW,SAAOyB,IAAEzB,GAAE,WAAW,QAAMwB;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["c", "m", "e", "i", "h", "t", "u", "j", "s", "t", "e", "r", "f", "r", "n", "a", "e", "u", "o", "c", "E", "v", "_", "t", "i", "s", "h", "A", "N", "l", "c", "s", "h", "u", "i", "d", "n", "o", "a", "e", "r", "t", "_", "i", "a", "e", "E", "h", "e", "c", "L", "t", "r", "a", "s", "i", "o", "n", "d", "S", "l", "m", "f", "z", "N", "g", "G", "T", "_", "x", "D", "I", "P", "O", "j", "A", "e", "t", "n", "s", "k", "o", "r", "P", "i", "U", "E", "I", "a", "l", "m", "R", "f", "d", "b", "v", "u", "O", "c", "M", "T", "S", "o", "s", "i", "e", "A", "t", "r", "I", "n", "a", "u", "l", "c", "m", "f", "d", "S", "E", "p", "g", "b", "x", "h", "O", "T", "w", "j", "M", "v"]}