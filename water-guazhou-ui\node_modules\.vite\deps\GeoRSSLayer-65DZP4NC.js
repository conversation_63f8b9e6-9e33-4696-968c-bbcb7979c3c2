import {
  p
} from "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c as c2
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  n
} from "./chunk-MIA6BJ32.js";
import {
  t
} from "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  c,
  f,
  v
} from "./chunk-VJW7RCN7.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import {
  S
} from "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  m
} from "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  G
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import {
  n as n2
} from "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  qt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import {
  a as a2
} from "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/GeoRSSLayer.js
var L = ["atom", "xml"];
var R = { base: a2, key: "type", typeMap: { "simple-line": m }, errorContext: "symbol" };
var k = { base: a2, key: "type", typeMap: { "picture-marker": n2, "simple-marker": y2 }, errorContext: "symbol" };
var _2 = { base: a2, key: "type", typeMap: { "simple-fill": S }, errorContext: "symbol" };
var w3 = class extends n(p(c2(_(t(O(b)))))) {
  constructor(...e2) {
    super(...e2), this.description = null, this.fullExtent = null, this.legendEnabled = true, this.lineSymbol = null, this.pointSymbol = null, this.polygonSymbol = null, this.operationalLayerType = "GeoRSS", this.url = null, this.type = "geo-rss";
  }
  normalizeCtorArgs(e2, o2) {
    return "string" == typeof e2 ? { url: e2, ...o2 } : e2;
  }
  readFeatureCollections(e2, o2) {
    return o2.featureCollection.layers.forEach((e3) => {
      var _a;
      const o3 = e3.layerDefinition.drawingInfo.renderer.symbol;
      o3 && "esriSFS" === o3.type && ((_a = o3.outline) == null ? void 0 : _a.style.includes("esriSFS")) && (o3.outline.style = "esriSLSSolid");
    }), o2.featureCollection.layers;
  }
  get hasPoints() {
    return this._hasGeometry("esriGeometryPoint");
  }
  get hasPolylines() {
    return this._hasGeometry("esriGeometryPolyline");
  }
  get hasPolygons() {
    return this._hasGeometry("esriGeometryPolygon");
  }
  get title() {
    const e2 = this._get("title");
    return e2 && "defaults" !== this.originOf("title") ? e2 : this.url ? qt(this.url, L) || "GeoRSS" : e2 || "";
  }
  set title(e2) {
    this._set("title", e2);
  }
  load(e2) {
    const o2 = r(e2) ? e2.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Map Service", "Feature Service", "Feature Collection", "Scene Service"] }, e2).catch(w).then(() => this._fetchService(o2)).then((e3) => {
      this.read(e3, { origin: "service" });
    })), Promise.resolve(this);
  }
  async hasDataChanged() {
    const e2 = await this._fetchService();
    return this.read(e2, { origin: "service", ignoreDefaults: true }), true;
  }
  async _fetchService(e2) {
    const t2 = this.spatialReference, { data: s2 } = await U(s.geoRSSServiceUrl, { query: { url: this.url, refresh: !!this.loaded || void 0, outSR: G(t2) ? void 0 : t2.wkid ?? JSON.stringify(t2) }, signal: e2 });
    return s2;
  }
  _hasGeometry(e2) {
    var _a;
    return ((_a = this.featureCollections) == null ? void 0 : _a.some((o2) => {
      var _a2, _b;
      return ((_a2 = o2.featureSet) == null ? void 0 : _a2.geometryType) === e2 && ((_b = o2.featureSet.features) == null ? void 0 : _b.length) > 0;
    })) ?? false;
  }
};
e([y()], w3.prototype, "description", void 0), e([y()], w3.prototype, "featureCollections", void 0), e([o("service", "featureCollections", ["featureCollection.layers"])], w3.prototype, "readFeatureCollections", null), e([y({ type: w2, json: { name: "lookAtExtent" } })], w3.prototype, "fullExtent", void 0), e([y(v)], w3.prototype, "id", void 0), e([y(c)], w3.prototype, "legendEnabled", void 0), e([y({ types: R, json: { write: true } })], w3.prototype, "lineSymbol", void 0), e([y({ type: ["show", "hide"] })], w3.prototype, "listMode", void 0), e([y({ types: k, json: { write: true } })], w3.prototype, "pointSymbol", void 0), e([y({ types: _2, json: { write: true } })], w3.prototype, "polygonSymbol", void 0), e([y({ type: ["GeoRSS"] })], w3.prototype, "operationalLayerType", void 0), e([y(f)], w3.prototype, "url", void 0), e([y({ json: { origins: { service: { read: { source: "name", reader: (e2) => e2 || void 0 } } } } })], w3.prototype, "title", null), e([y({ readOnly: true, json: { read: false }, value: "geo-rss" })], w3.prototype, "type", void 0), w3 = e([a("esri.layers.GeoRSSLayer")], w3);
var F = w3;
export {
  F as default
};
//# sourceMappingURL=GeoRSSLayer-65DZP4NC.js.map
