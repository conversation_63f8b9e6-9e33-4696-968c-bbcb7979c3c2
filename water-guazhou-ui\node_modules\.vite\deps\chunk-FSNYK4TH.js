import {
  o as o3
} from "./chunk-3WUI7ZKG.js";
import {
  S
} from "./chunk-VNYCO3JG.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2,
  s
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  d,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/support/FeatureExpressionInfo.js
var i;
var p = i = class extends l {
  constructor(r3) {
    super(r3);
  }
  async collectRequiredFields(r3, e2) {
    return S(r3, e2, this.expression);
  }
  clone() {
    return new i({ expression: this.expression, title: this.title });
  }
  equals(r3) {
    return this.expression === r3.expression && this.title === r3.title;
  }
};
e([y({ type: String, json: { write: true } })], p.prototype, "expression", void 0), e([y({ type: String, json: { write: true } })], p.prototype, "title", void 0), p = i = e([a("esri.layers.support.FeatureExpressionInfo")], p);
var c = p;

// node_modules/@arcgis/core/symbols/support/ElevationInfo.js
var m;
var d2 = o2()({ onTheGround: "on-the-ground", relativeToGround: "relative-to-ground", relativeToScene: "relative-to-scene", absoluteHeight: "absolute-height" });
var c2 = new s({ foot: "feet", kilometer: "kilometers", meter: "meters", mile: "miles", "us-foot": "us-feet", yard: "yards" });
var h = m = class extends l {
  constructor(e2) {
    super(e2), this.offset = null;
  }
  readFeatureExpressionInfo(e2, r3) {
    return null != e2 ? e2 : r3.featureExpression && 0 === r3.featureExpression.value ? { expression: "0" } : void 0;
  }
  writeFeatureExpressionInfo(e2, r3, o4, t) {
    r3[o4] = e2.write({}, t), "0" === e2.expression && (r3.featureExpression = { value: 0 });
  }
  get mode() {
    const { offset: e2, featureExpressionInfo: r3 } = this;
    return this._isOverridden("mode") ? this._get("mode") : r(e2) || r3 ? "relative-to-ground" : "on-the-ground";
  }
  set mode(e2) {
    this._override("mode", e2);
  }
  set unit(e2) {
    this._set("unit", e2);
  }
  write(e2, r3) {
    return this.offset || this.mode || this.featureExpressionInfo || this.unit ? super.write(e2, r3) : null;
  }
  clone() {
    return new m({ mode: this.mode, offset: this.offset, featureExpressionInfo: this.featureExpressionInfo ? this.featureExpressionInfo.clone() : void 0, unit: this.unit });
  }
  equals(e2) {
    return this.mode === e2.mode && this.offset === e2.offset && this.unit === e2.unit && d(this.featureExpressionInfo, e2.featureExpressionInfo);
  }
};
e([y({ type: c, json: { write: true } })], h.prototype, "featureExpressionInfo", void 0), e([o("featureExpressionInfo", ["featureExpressionInfo", "featureExpression"])], h.prototype, "readFeatureExpressionInfo", null), e([r2("featureExpressionInfo", { featureExpressionInfo: { type: c }, "featureExpression.value": { type: [0] } })], h.prototype, "writeFeatureExpressionInfo", null), e([y({ type: d2.apiValues, nonNullable: true, json: { type: d2.jsonValues, read: d2.read, write: { writer: d2.write, isRequired: true } } })], h.prototype, "mode", null), e([y({ type: Number, json: { write: true } })], h.prototype, "offset", void 0), e([y({ type: o3, json: { type: String, read: c2.read, write: c2.write } })], h.prototype, "unit", null), h = m = e([a("esri.layers.support.ElevationInfo")], h);
var x = h;

export {
  x
};
//# sourceMappingURL=chunk-FSNYK4TH.js.map
