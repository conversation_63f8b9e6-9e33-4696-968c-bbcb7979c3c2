{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/MultiOriginStore.js", "../../@arcgis/core/core/ReadOnlyMultiOriginJSONSupport.js", "../../@arcgis/core/core/MultiOriginJSONSupport.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../lang.js\";import{assumeNonNull as s}from\"../maybe.js\";import{OriginIdNum as e,OriginId as r}from\"./PropertyOrigin.js\";class i{constructor(){this._propertyOriginMap=new Map,this._originStores=new Array(e),this._values=new Map,this.multipleOriginsSupported=!0}clone(s){const o=new i,n=this._originStores[r.DEFAULTS];n&&n.forEach(((s,e)=>{o.set(e,t(s),r.DEFAULTS)}));for(let i=r.SERVICE;i<e;i++){const e=this._originStores[i];e&&e.forEach(((e,r)=>{s&&s.has(r)||o.set(r,t(e),i)}))}return o}get(t,s){const e=void 0===s?this._values:this._originStores[s];return e?e.get(t):void 0}keys(t){const s=null==t?this._values:this._originStores[t];return s?[...s.keys()]:[]}set(t,e,i=r.USER){let o=this._originStores[i];if(o||(o=new Map,this._originStores[i]=o),o.set(t,e),!this._values.has(t)||s(this._propertyOriginMap.get(t))<=i){const s=this._values.get(t);return this._values.set(t,e),this._propertyOriginMap.set(t,i),s!==e}return!1}delete(t,s=r.USER){const e=this._originStores[s];if(!e)return;const i=e.get(t);if(e.delete(t),this._values.has(t)&&this._propertyOriginMap.get(t)===s){this._values.delete(t);for(let e=s-1;e>=0;e--){const s=this._originStores[e];if(s&&s.has(t)){this._values.set(t,s.get(t)),this._propertyOriginMap.set(t,e);break}}}return i}has(t,s){const e=void 0===s?this._values:this._originStores[s];return!!e&&e.has(t)}revert(t,s){for(;s>0&&!this.has(t,s);)--s;const e=this._originStores[s],r=e&&e.get(t),i=this._values.get(t);return this._values.set(t,r),this._propertyOriginMap.set(t,s),i!==r}originOf(t){return this._propertyOriginMap.get(t)||r.DEFAULTS}forEach(t){this._values.forEach(t)}}export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import t from\"./Accessor.js\";import{assumeNonNull as s}from\"./maybe.js\";import{setupConstructedDefaults as o}from\"./accessorSupport/defaultsStoreUtils.js\";import e from\"./accessorSupport/MultiOriginStore.js\";import{nameToId as i,idToName as c}from\"./accessorSupport/PropertyOrigin.js\";import{read as n}from\"./accessorSupport/read.js\";import{getProperties as p}from\"./accessorSupport/utils.js\";import{subclass as u}from\"./accessorSupport/decorators/subclass.js\";const a=t=>{let a=class extends t{constructor(...r){super(...r);const t=s(p(this)),i=t.store,c=new e;t.store=c,o(t,i,c)}read(r,t){n(this,r,t)}getAtOrigin(r,t){const s=m(this),o=i(t);if(\"string\"==typeof r)return s.get(r,o);const e={};return r.forEach((r=>{e[r]=s.get(r,o)})),e}originOf(r){return c(this.originIdOf(r))}originIdOf(r){return m(this).originOf(r)}revert(r,t){const s=m(this),o=i(t),e=p(this);let c;c=\"string\"==typeof r?\"*\"===r?s.keys(o):[r]:r,c.forEach((r=>{e.invalidate(r),s.revert(r,o),e.commit(r)}))}};return a=r([u(\"esri.core.ReadOnlyMultiOriginJSONSupport\")],a),a};function m(r){return p(r).store}let f=class extends(a(t)){};f=r([u(\"esri.core.ReadOnlyMultiOriginJSONSupport\")],f);export{a as ReadOnlyMultiOriginJSONMixin,f as ReadOnlyMultiOriginJSONSupport};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import t from\"./Accessor.js\";import{ReadOnlyMultiOriginJSONMixin as s}from\"./ReadOnlyMultiOriginJSONSupport.js\";import{nameToId as e,OriginId as o,OriginIdNum as i}from\"./accessorSupport/PropertyOrigin.js\";import{getProperties as c}from\"./accessorSupport/utils.js\";import{write as n}from\"./accessorSupport/write.js\";import{subclass as p}from\"./accessorSupport/decorators/subclass.js\";const u=t=>{let s=class extends t{constructor(...r){super(...r)}clear(r,t=\"user\"){return l(this).delete(r,e(t))}write(r={},t){return n(this,r=r||{},t),r}setAtOrigin(r,t,s){c(this).setAtOrigin(r,t,e(s))}removeOrigin(r){const t=l(this),s=e(r),i=t.keys(s);for(const e of i)t.originOf(e)===s&&t.set(e,t.get(e,s),o.USER)}updateOrigin(r,t){const s=l(this),o=e(t),c=this.get(r);for(let e=o+1;e<i;++e)s.delete(r,e);s.set(r,c,o)}toJSON(r){return this.write({},r)}};return s=r([p(\"esri.core.WriteableMultiOriginJSONSupport\")],s),s.prototype.toJSON.isDefaultToJSON=!0,s};function l(r){return c(r).store}const O=t=>{let e=class extends(u(s(t))){constructor(...r){super(...r)}};return e=r([p(\"esri.core.MultiOriginJSONSupport\")],e),e};let S=class extends(O(t)){};S=r([p(\"esri.core.MultiOriginJSONSupport\")],S);export{O as MultiOriginJSONMixin,S as MultiOriginJSONSupport};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+I,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,qBAAmB,oBAAI,OAAI,KAAK,gBAAc,IAAI,MAAM,CAAC,GAAE,KAAK,UAAQ,oBAAI,OAAI,KAAK,2BAAyB;AAAA,EAAE;AAAA,EAAC,MAAM,GAAE;AAAC,UAAM,IAAE,IAAI,MAAEA,KAAE,KAAK,cAAc,EAAE,QAAQ;AAAE,IAAAA,MAAGA,GAAE,QAAS,CAACC,IAAEC,OAAI;AAAC,QAAE,IAAIA,IAAE,EAAED,EAAC,GAAE,EAAE,QAAQ;AAAA,IAAC,CAAE;AAAE,aAAQE,KAAE,EAAE,SAAQA,KAAE,GAAEA,MAAI;AAAC,YAAMD,KAAE,KAAK,cAAcC,EAAC;AAAE,MAAAD,MAAGA,GAAE,QAAS,CAACA,IAAEE,OAAI;AAAC,aAAG,EAAE,IAAIA,EAAC,KAAG,EAAE,IAAIA,IAAE,EAAEF,EAAC,GAAEC,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,IAAIE,IAAE,GAAE;AAAC,UAAMH,KAAE,WAAS,IAAE,KAAK,UAAQ,KAAK,cAAc,CAAC;AAAE,WAAOA,KAAEA,GAAE,IAAIG,EAAC,IAAE;AAAA,EAAM;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAM,IAAE,QAAMA,KAAE,KAAK,UAAQ,KAAK,cAAcA,EAAC;AAAE,WAAO,IAAE,CAAC,GAAG,EAAE,KAAK,CAAC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAEH,IAAEC,KAAE,EAAE,MAAK;AAAC,QAAI,IAAE,KAAK,cAAcA,EAAC;AAAE,QAAG,MAAI,IAAE,oBAAI,OAAI,KAAK,cAAcA,EAAC,IAAE,IAAG,EAAE,IAAIE,IAAEH,EAAC,GAAE,CAAC,KAAK,QAAQ,IAAIG,EAAC,KAAG,EAAE,KAAK,mBAAmB,IAAIA,EAAC,CAAC,KAAGF,IAAE;AAAC,YAAM,IAAE,KAAK,QAAQ,IAAIE,EAAC;AAAE,aAAO,KAAK,QAAQ,IAAIA,IAAEH,EAAC,GAAE,KAAK,mBAAmB,IAAIG,IAAEF,EAAC,GAAE,MAAID;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOG,IAAE,IAAE,EAAE,MAAK;AAAC,UAAMH,KAAE,KAAK,cAAc,CAAC;AAAE,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAED,GAAE,IAAIG,EAAC;AAAE,QAAGH,GAAE,OAAOG,EAAC,GAAE,KAAK,QAAQ,IAAIA,EAAC,KAAG,KAAK,mBAAmB,IAAIA,EAAC,MAAI,GAAE;AAAC,WAAK,QAAQ,OAAOA,EAAC;AAAE,eAAQH,KAAE,IAAE,GAAEA,MAAG,GAAEA,MAAI;AAAC,cAAMD,KAAE,KAAK,cAAcC,EAAC;AAAE,YAAGD,MAAGA,GAAE,IAAII,EAAC,GAAE;AAAC,eAAK,QAAQ,IAAIA,IAAEJ,GAAE,IAAII,EAAC,CAAC,GAAE,KAAK,mBAAmB,IAAIA,IAAEH,EAAC;AAAE;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,IAAIE,IAAE,GAAE;AAAC,UAAMH,KAAE,WAAS,IAAE,KAAK,UAAQ,KAAK,cAAc,CAAC;AAAE,WAAM,CAAC,CAACA,MAAGA,GAAE,IAAIG,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE,GAAE;AAAC,WAAK,IAAE,KAAG,CAAC,KAAK,IAAIA,IAAE,CAAC,IAAG,GAAE;AAAE,UAAMH,KAAE,KAAK,cAAc,CAAC,GAAEE,KAAEF,MAAGA,GAAE,IAAIG,EAAC,GAAEF,KAAE,KAAK,QAAQ,IAAIE,EAAC;AAAE,WAAO,KAAK,QAAQ,IAAIA,IAAED,EAAC,GAAE,KAAK,mBAAmB,IAAIC,IAAE,CAAC,GAAEF,OAAIC;AAAA,EAAC;AAAA,EAAC,SAASC,IAAE;AAAC,WAAO,KAAK,mBAAmB,IAAIA,EAAC,KAAG,EAAE;AAAA,EAAQ;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,QAAQ,QAAQA,EAAC;AAAA,EAAC;AAAC;;;ACAnmC,IAAMC,KAAE,CAAAC,OAAG;AAAC,MAAID,KAAE,cAAcC,GAAC;AAAA,IAAC,eAAeC,IAAE;AAAC,YAAM,GAAGA,EAAC;AAAE,YAAMD,KAAE,EAAEE,GAAE,IAAI,CAAC,GAAEC,KAAEH,GAAE,OAAM,IAAE,IAAI;AAAE,MAAAA,GAAE,QAAM,GAAEE,GAAEF,IAAEG,IAAE,CAAC;AAAA,IAAC;AAAA,IAAC,KAAKF,IAAED,IAAE;AAAC,QAAE,MAAKC,IAAED,EAAC;AAAA,IAAC;AAAA,IAAC,YAAYC,IAAED,IAAE;AAAC,YAAM,IAAE,EAAE,IAAI,GAAE,IAAE,EAAEA,EAAC;AAAE,UAAG,YAAU,OAAOC,GAAE,QAAO,EAAE,IAAIA,IAAE,CAAC;AAAE,YAAMC,KAAE,CAAC;AAAE,aAAOD,GAAE,QAAS,CAAAA,OAAG;AAAC,QAAAC,GAAED,EAAC,IAAE,EAAE,IAAIA,IAAE,CAAC;AAAA,MAAC,CAAE,GAAEC;AAAA,IAAC;AAAA,IAAC,SAASD,IAAE;AAAC,aAAO,EAAE,KAAK,WAAWA,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,WAAWA,IAAE;AAAC,aAAO,EAAE,IAAI,EAAE,SAASA,EAAC;AAAA,IAAC;AAAA,IAAC,OAAOA,IAAED,IAAE;AAAC,YAAM,IAAE,EAAE,IAAI,GAAE,IAAE,EAAEA,EAAC,GAAEE,KAAEA,GAAE,IAAI;AAAE,UAAI;AAAE,UAAE,YAAU,OAAOD,KAAE,QAAMA,KAAE,EAAE,KAAK,CAAC,IAAE,CAACA,EAAC,IAAEA,IAAE,EAAE,QAAS,CAAAA,OAAG;AAAC,QAAAC,GAAE,WAAWD,EAAC,GAAE,EAAE,OAAOA,IAAE,CAAC,GAAEC,GAAE,OAAOD,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAE,SAAOF,KAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAEA,EAAC,GAAEA;AAAC;AAAE,SAAS,EAAEE,IAAE;AAAC,SAAOC,GAAED,EAAC,EAAE;AAAK;AAAC,IAAI,IAAE,cAAcF,GAAE,CAAC,EAAE;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;;;ACAnwB,IAAMK,KAAE,CAAAC,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,eAAeC,IAAE;AAAC,YAAM,GAAGA,EAAC;AAAA,IAAC;AAAA,IAAC,MAAMA,IAAED,KAAE,QAAO;AAAC,aAAO,EAAE,IAAI,EAAE,OAAOC,IAAE,EAAED,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,MAAMC,KAAE,CAAC,GAAED,IAAE;AAAC,aAAO,EAAE,MAAKC,KAAEA,MAAG,CAAC,GAAED,EAAC,GAAEC;AAAA,IAAC;AAAA,IAAC,YAAYA,IAAED,IAAEE,IAAE;AAAC,MAAAC,GAAE,IAAI,EAAE,YAAYF,IAAED,IAAE,EAAEE,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,aAAaD,IAAE;AAAC,YAAMD,KAAE,EAAE,IAAI,GAAEE,KAAE,EAAED,EAAC,GAAEG,KAAEJ,GAAE,KAAKE,EAAC;AAAE,iBAAUC,MAAKC,GAAE,CAAAJ,GAAE,SAASG,EAAC,MAAID,MAAGF,GAAE,IAAIG,IAAEH,GAAE,IAAIG,IAAED,EAAC,GAAE,EAAE,IAAI;AAAA,IAAC;AAAA,IAAC,aAAaD,IAAED,IAAE;AAAC,YAAME,KAAE,EAAE,IAAI,GAAE,IAAE,EAAEF,EAAC,GAAE,IAAE,KAAK,IAAIC,EAAC;AAAE,eAAQE,KAAE,IAAE,GAAEA,KAAE,GAAE,EAAEA,GAAE,CAAAD,GAAE,OAAOD,IAAEE,EAAC;AAAE,MAAAD,GAAE,IAAID,IAAE,GAAE,CAAC;AAAA,IAAC;AAAA,IAAC,OAAOA,IAAE;AAAC,aAAO,KAAK,MAAM,CAAC,GAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,IAAE,EAAE,CAAC,EAAE,2CAA2C,CAAC,GAAE,CAAC,GAAE,EAAE,UAAU,OAAO,kBAAgB,MAAG;AAAC;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAOE,GAAEF,EAAC,EAAE;AAAK;AAAC,IAAM,IAAE,CAAAD,OAAG;AAAC,MAAIG,KAAE,cAAcJ,GAAEM,GAAEL,EAAC,CAAC,EAAE;AAAA,IAAC,eAAeC,IAAE;AAAC,YAAM,GAAGA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAOE,KAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAEA,EAAC,GAAEA;AAAC;AAAE,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAE,CAAC;", "names": ["n", "s", "e", "i", "r", "t", "a", "t", "r", "e", "i", "u", "t", "r", "s", "e", "i", "a"]}