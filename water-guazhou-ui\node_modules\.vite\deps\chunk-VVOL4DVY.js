import {
  b,
  e as e3,
  i as i2,
  i2 as i3,
  p,
  s as s2
} from "./chunk-ZSGF3UOS.js";
import {
  h as h2,
  n,
  p as p2,
  u as u2
} from "./chunk-ZKWNW26M.js";
import {
  i
} from "./chunk-MNPAHSMX.js";
import {
  s
} from "./chunk-TDGDXUFC.js";
import {
  e as e4
} from "./chunk-C2ZE76VJ.js";
import {
  a
} from "./chunk-F26DNX7C.js";
import {
  a2,
  d2 as d,
  h2 as h3,
  n as n2,
  o as o3,
  o3 as o7,
  v as v2,
  x
} from "./chunk-XVTFHFM3.js";
import {
  r
} from "./chunk-REGYRSW7.js";
import {
  n as n3
} from "./chunk-Y424ZXTG.js";
import {
  d as d2,
  o as o5
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  c,
  v
} from "./chunk-N3S5O3YO.js";
import {
  e as e2
} from "./chunk-32BGXH4N.js";
import {
  o as o4
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  o as o6
} from "./chunk-TUB4N6LD.js";
import {
  f
} from "./chunk-YV4RKNU4.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/chunks/RealisticTree.glsl.js
function I(I2) {
  const R2 = new o2(), { vertex: z, fragment: k, varyings: G } = R2;
  return v(z, I2), R2.include(o5), G.add("vpos", "vec3"), R2.include(s, I2), R2.include(p, I2), R2.include(a, I2), I2.output !== h.Color && I2.output !== h.Alpha || (c(R2.vertex, I2), R2.include(i, I2), R2.include(r, I2), I2.offsetBackfaces && R2.include(e3), I2.instancedColor && R2.attributes.add(O.INSTANCECOLOR, "vec4"), G.add("vNormalWorld", "vec3"), G.add("localvpos", "vec3"), I2.hasMultipassTerrain && G.add("depth", "float"), R2.include(o3, I2), R2.include(d2, I2), R2.include(i2, I2), R2.include(e4, I2), z.uniforms.add(new e("externalColor", (e5) => e5.externalColor)), G.add("vcolorExt", "vec4"), z.code.add(o`
        void main(void) {
          forwardNormalizedVertexColor();
          vcolorExt = externalColor;
          ${I2.instancedColor ? "vcolorExt *= instanceColor;" : ""}
          vcolorExt *= vvColor();
          vcolorExt *= getSymbolColor();
          forwardColorMixMode();

          if (vcolorExt.a < ${o.float(t)}) {
            gl_Position = vec4(1e38, 1e38, 1e38, 1.0);
          } else {
            vpos = calculateVPos();
            localvpos = vpos - view[3].xyz;
            vpos = subtractOrigin(vpos);
            vNormalWorld = dpNormal(vvLocalNormal(normalModel()));
            vpos = addVerticalOffset(vpos, localOrigin);
            gl_Position = transformPosition(proj, view, vpos);
            ${I2.offsetBackfaces ? "gl_Position = offsetBackfacingClipPosition(gl_Position, vpos, vNormalWorld, cameraPosition);" : ""}
          }
          ${I2.hasMultipassTerrain ? o`depth = (view * vec4(vpos, 1.0)).z;` : ""}
          forwardLinearDepth();
          forwardTextureCoordinates();
        }
      `)), I2.output === h.Alpha && (R2.include(u, I2), R2.include(s2, I2), R2.include(n3, I2), k.uniforms.add([new o6("opacity", (e5) => e5.opacity), new o6("layerOpacity", (e5) => e5.layerOpacity)]), I2.hasColorTexture && k.uniforms.add(new f("tex", (e5) => e5.texture)), k.include(i3), k.code.add(o`
      void main() {
        discardBySlice(vpos);
        ${I2.hasMultipassTerrain ? o`terrainDepthTest(gl_FragCoord, depth);` : ""}
        ${I2.hasColorTexture ? o`
                vec4 texColor = texture2D(tex, ${I2.hasColorTextureTransform ? o`colorUV` : o`vuv0`});
                ${I2.textureAlphaPremultiplied ? "texColor.rgb /= texColor.a;" : ""}
                discardOrAdjustAlpha(texColor);` : o`vec4 texColor = vec4(1.0);`}
        ${I2.hasVertexColors ? o`float opacity_ = layerOpacity * mixExternalOpacity(vColor.a * opacity, texColor.a, vcolorExt.a, int(colorMixMode));` : o`float opacity_ = layerOpacity * mixExternalOpacity(opacity, texColor.a, vcolorExt.a, int(colorMixMode));`}

        gl_FragColor = vec4(opacity_);
      }
    `)), I2.output === h.Color && (R2.include(u, I2), R2.include(p2, I2), R2.include(n, I2), R2.include(s2, I2), R2.include(I2.instancedDoublePrecision ? h3 : v2, I2), R2.include(n3, I2), c(R2.fragment, I2), o7(k), h2(k), u2(k), k.uniforms.add([z.uniforms.get("localOrigin"), z.uniforms.get("view"), new e2("ambient", (e5) => e5.ambient), new e2("diffuse", (e5) => e5.diffuse), new o6("opacity", (e5) => e5.opacity), new o6("layerOpacity", (e5) => e5.layerOpacity)]), I2.hasColorTexture && k.uniforms.add(new f("tex", (e5) => e5.texture)), R2.include(x, I2), R2.include(n2, I2), k.include(i3), R2.extensions.add("GL_OES_standard_derivatives"), a2(k), k.code.add(o`
      void main() {
        discardBySlice(vpos);
        ${I2.hasMultipassTerrain ? o`terrainDepthTest(gl_FragCoord, depth);` : ""}
        ${I2.hasColorTexture ? o`
                vec4 texColor = texture2D(tex, ${I2.hasColorTextureTransform ? o`colorUV` : o`vuv0`});
                ${I2.textureAlphaPremultiplied ? "texColor.rgb /= texColor.a;" : ""}
                discardOrAdjustAlpha(texColor);` : o`vec4 texColor = vec4(1.0);`}
        vec3 viewDirection = normalize(vpos - cameraPosition);
        ${I2.pbrMode === d.Normal ? "applyPBRFactors();" : ""}
        float ssao = evaluateAmbientOcclusionInverse();
        ssao *= getBakedOcclusion();

        float additionalAmbientScale = additionalDirectedAmbientLight(vpos + localOrigin);
        vec3 additionalLight = ssao * mainLightIntensity * additionalAmbientScale * ambientBoostFactor * lightingGlobalFactor;
        ${I2.receiveShadows ? "float shadow = readShadowMap(vpos, linearDepth);" : I2.spherical ? "float shadow = lightingGlobalFactor * (1.0 - additionalAmbientScale);" : "float shadow = 0.0;"}
        vec3 matColor = max(ambient, diffuse);
        ${I2.hasVertexColors ? o`
                vec3 albedo = mixExternalColor(vColor.rgb * matColor, texColor.rgb, vcolorExt.rgb, int(colorMixMode));
                float opacity_ = layerOpacity * mixExternalOpacity(vColor.a * opacity, texColor.a, vcolorExt.a, int(colorMixMode));` : o`
                vec3 albedo = mixExternalColor(matColor, texColor.rgb, vcolorExt.rgb, int(colorMixMode));
                float opacity_ = layerOpacity * mixExternalOpacity(opacity, texColor.a, vcolorExt.a, int(colorMixMode));`}
        ${I2.snowCover ? o`albedo = mix(albedo, vec3(1), 0.9);` : o``}
        ${o`
            vec3 shadingNormal = normalize(vNormalWorld);
            albedo *= 1.2;
            vec3 viewForward = vec3(view[0][2], view[1][2], view[2][2]);
            float alignmentLightView = clamp(dot(viewForward, -mainLightDirection), 0.0, 1.0);
            float transmittance = 1.0 - clamp(dot(viewForward, shadingNormal), 0.0, 1.0);
            float treeRadialFalloff = vColor.r;
            float backLightFactor = 0.5 * treeRadialFalloff * alignmentLightView * transmittance * (1.0 - shadow);
            additionalLight += backLightFactor * mainLightIntensity;`}
        ${I2.pbrMode === d.Normal || I2.pbrMode === d.Schematic ? I2.spherical ? o`vec3 normalGround = normalize(vpos + localOrigin);` : o`vec3 normalGround = vec3(0.0, 0.0, 1.0);` : o``}
        ${I2.pbrMode === d.Normal || I2.pbrMode === d.Schematic ? o`
                float additionalAmbientIrradiance = additionalAmbientIrradianceFactor * mainLightIntensity[2];
                ${I2.snowCover ? o`
                        mrr = vec3(0.0, 1.0, 0.04);
                        emission = vec3(0.0);` : ""}

                vec3 shadedColor = evaluateSceneLightingPBR(shadingNormal, albedo, shadow, 1.0 - ssao, additionalLight, viewDirection, normalGround, mrr, emission, additionalAmbientIrradiance);` : o`vec3 shadedColor = evaluateSceneLighting(shadingNormal, albedo, shadow, 1.0 - ssao, additionalLight);`}
        gl_FragColor = highlightSlice(vec4(shadedColor, opacity_), vpos);
        ${I2.transparencyPassType === o4.Color ? o`gl_FragColor = premultiplyAlpha(gl_FragColor);` : o``}
      }
    `)), R2.include(b, I2), R2;
}
var R = Object.freeze(Object.defineProperty({ __proto__: null, build: I }, Symbol.toStringTag, { value: "Module" }));

export {
  I,
  R
};
//# sourceMappingURL=chunk-VVOL4DVY.js.map
