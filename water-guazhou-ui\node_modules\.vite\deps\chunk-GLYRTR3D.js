import {
  n
} from "./chunk-WCRONQ5Z.js";
import {
  T
} from "./chunk-WAPZ634R.js";
import {
  _
} from "./chunk-4M3AMTD4.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/views/2d/layers/graphics/HighlightGraphicContainer.js
var i = class extends n {
  doRender(e2) {
    e2.drawPhase === T.HIGHLIGHT && super.doRender(e2);
  }
  renderChildren(e2) {
    if (this.attributeView.update(), !this.children.some((e3) => e3.hasData)) return;
    this.attributeView.bindTextures(e2.context), super.renderChildren(e2);
    const { painter: r } = e2, s = r.effects.highlight;
    s.bind(e2), e2.context.setColorMask(true, true, true, true), e2.context.clear(_.COLOR_BUFFER_BIT), this._renderChildren(e2, s.defines.concat(["highlightAll"])), s.draw(e2), s.unbind();
  }
};
i = e([a("esri.views.2d.layers.support.HighlightGraphicContainer")], i);
var n2 = i;

export {
  n2 as n
};
//# sourceMappingURL=chunk-GLYRTR3D.js.map
