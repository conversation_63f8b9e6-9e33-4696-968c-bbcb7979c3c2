import {
  E
} from "./chunk-4AKIERTZ.js";
import {
  o as o2
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import {
  x as x2
} from "./chunk-5O73RHQV.js";
import "./chunk-F6EHUXJS.js";
import "./chunk-PX6TFO4X.js";
import "./chunk-UMW4I2EJ.js";
import "./chunk-XZ2UVSB4.js";
import "./chunk-NSJUSNRV.js";
import "./chunk-YMY3DTA5.js";
import "./chunk-TNP2LXZZ.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  i
} from "./chunk-TVTTDN54.js";
import {
  g
} from "./chunk-HTXGAKOK.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-W3CLOCDX.js";
import "./chunk-554JGJWA.js";
import "./chunk-6T5FEO66.js";
import "./chunk-KE7SPCM7.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-4GVJIP3E.js";
import {
  T
} from "./chunk-N7ADFPOO.js";
import {
  g as g2
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  r as r2
} from "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import {
  V,
  ot
} from "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import {
  s as s2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  has,
  o
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/FeatureLayerSource.js
var E2 = new s2({ originalAndCurrentFeatures: "original-and-current-features", none: "none" });
async function S(e3) {
  if ("string" == typeof e3) {
    const t2 = ot(e3);
    return t2 || { data: e3 };
  }
  return new Promise((t2, s3) => {
    const a2 = new FileReader();
    a2.readAsDataURL(e3), a2.onload = () => t2(ot(a2.result)), a2.onerror = (e4) => s3(e4);
  });
}
var O = /* @__PURE__ */ new Set(["Feature Layer", "Table"]);
var j = new s2({ Started: "published", Publishing: "publishing", Stopped: "unavailable" });
var T2 = class extends m {
  constructor() {
    super(...arguments), this.type = "feature-layer", this.refresh = x(async () => {
      var _a, _b;
      await this.load();
      const e3 = (_a = this.sourceJSON.editingInfo) == null ? void 0 : _a.lastEditDate;
      if (null == e3) return { dataChanged: true, updates: {} };
      try {
        await this._fetchService(null);
      } catch {
        return { dataChanged: true, updates: {} };
      }
      const t2 = e3 !== ((_b = this.sourceJSON.editingInfo) == null ? void 0 : _b.lastEditDate);
      return { dataChanged: t2, updates: t2 ? { editingInfo: this.sourceJSON.editingInfo, extent: this.sourceJSON.extent } : null };
    });
  }
  load(e3) {
    const t2 = r(e3) ? e3.signal : null, s3 = this.layer.sourceJSON;
    return this.addResolvingPromise(this._fetchService(s3, t2)), Promise.resolve(this);
  }
  get queryTask() {
    var _a;
    const { capabilities: e3, parsedUrl: t2, dynamicDataSource: s3, infoFor3D: a2, gdbVersion: r3, spatialReference: o3, fieldsIndex: i2 } = this.layer, n = has("featurelayer-pbf") && (e3 == null ? void 0 : e3.query.supportsFormatPBF) && t(a2), u = ((_a = e3 == null ? void 0 : e3.operations) == null ? void 0 : _a.supportsQueryAttachments) ?? false;
    return new x2({ url: t2.path, pbfSupported: n, fieldsIndex: i2, infoFor3D: a2, dynamicDataSource: s3, gdbVersion: r3, sourceSpatialReference: o3, queryAttachmentsSupported: u });
  }
  async addAttachment(e3, t2) {
    await this.load();
    const s3 = e3.attributes[this.layer.objectIdField], r3 = this.layer.parsedUrl.path + "/" + s3 + "/addAttachment", o3 = this._getLayerRequestOptions(), i2 = this._getFormDataForAttachment(t2, o3.query);
    try {
      const e4 = await U(r3, { body: i2 });
      return this._createFeatureEditResult(e4.data.addAttachmentResult);
    } catch (n) {
      throw this._createAttachmentErrorResult(s3, n);
    }
  }
  async updateAttachment(e3, t2, s3) {
    await this.load();
    const r3 = e3.attributes[this.layer.objectIdField], o3 = this.layer.parsedUrl.path + "/" + r3 + "/updateAttachment", i2 = this._getLayerRequestOptions({ query: { attachmentId: t2 } }), n = this._getFormDataForAttachment(s3, i2.query);
    try {
      const e4 = await U(o3, { body: n });
      return this._createFeatureEditResult(e4.data.updateAttachmentResult);
    } catch (u) {
      throw this._createAttachmentErrorResult(r3, u);
    }
  }
  async applyEdits(e3, t2) {
    var _a, _b, _c, _d, _e, _f;
    await this.load();
    const r3 = this.layer.infoFor3D, o3 = r(r3), i2 = o3 || ((t2 == null ? void 0 : t2.globalIdUsed) ?? false), n = ((_a = e3.addFeatures) == null ? void 0 : _a.map((e4) => this._serializeFeature(e4, r3)).filter(r)) ?? [], l = ((_b = e3.updateFeatures) == null ? void 0 : _b.map((e4) => this._serializeFeature(e4, r3)).filter(r)) ?? [], c = this._getFeatureIds(e3.deleteFeatures, i2);
    i(n, l, this.layer.spatialReference);
    const p = [], h = [], y2 = [...e3.deleteAttachments ?? []];
    for (const s3 of e3.addAttachments ?? []) p.push(await this._serializeAttachment(s3));
    for (const s3 of e3.updateAttachments ?? []) h.push(await this._serializeAttachment(s3));
    const m2 = p.length || h.length || y2.length ? { adds: p, updates: h, deletes: y2 } : null;
    let f2, g3 = null;
    if (o3) {
      g3 = /* @__PURE__ */ new Map();
      const t3 = [];
      for (const a2 of e3.addAssets ?? []) t3.push(this._serializeAssetMapEditAndUploadAssets(a2, g3));
      const s3 = await Promise.all(t3);
      f2 = s3.length ? { adds: s3, updates: [], deletes: [] } : void 0;
    }
    const R = { gdbVersion: (t2 == null ? void 0 : t2.gdbVersion) || this.layer.gdbVersion, rollbackOnFailure: t2 == null ? void 0 : t2.rollbackOnFailureEnabled, useGlobalIds: i2, returnEditMoment: t2 == null ? void 0 : t2.returnEditMoment, usePreviousEditMoment: t2 == null ? void 0 : t2.usePreviousEditMoment, sessionId: t2 == null ? void 0 : t2.sessionId };
    (t2 == null ? void 0 : t2.returnServiceEditsOption) ? (R.edits = JSON.stringify([{ id: this.layer.layerId, adds: n, updates: l, deletes: c, attachments: m2, assetMaps: e2(f2) }]), R.returnServiceEditsOption = E2.toJSON(t2 == null ? void 0 : t2.returnServiceEditsOption), R.returnServiceEditsInSourceSR = t2 == null ? void 0 : t2.returnServiceEditsInSourceSR) : (R.adds = n.length ? JSON.stringify(n) : null, R.updates = l.length ? JSON.stringify(l) : null, R.deletes = c.length ? i2 ? JSON.stringify(c) : c.join(",") : null, R.attachments = m2 && JSON.stringify(m2), R.assetMaps = r(f2) ? JSON.stringify(f2) : void 0);
    const F = this._getLayerRequestOptions({ method: "post", query: R }), b = (t2 == null ? void 0 : t2.returnServiceEditsOption) ? this.layer.url : this.layer.parsedUrl.path, q = await U(b + "/applyEdits", F);
    if (!((_c = this.layer.capabilities.operations) == null ? void 0 : _c.supportsEditing) && ((_e = (_d = this.layer.effectiveCapabilities) == null ? void 0 : _d.operations) == null ? void 0 : _e.supportsEditing)) {
      const e4 = (_f = r2) == null ? void 0 : _f.findCredential(this.layer.url);
      await (e4 == null ? void 0 : e4.refreshToken());
    }
    if (o3 && null != q.data && null != q.data.assetMaps) {
      const e4 = q.data, t3 = this.layer.objectIdField, s3 = [];
      for (const a2 of e4.addResults) a2.success && s3.push(a2.objectId);
      for (const a2 of e4.updateResults) a2.success && s3.push(a2.objectId);
      const r4 = this._createRequestQueryOptions(), o4 = await U(b + "/query", { ...r4, query: { f: "json", formatOf3DObjects: "3D_glb", where: `OBJECTID IN (${s3.join(",")})`, outFields: `${t3}` } });
      if (o4 && o4.data && o4.data.assetMaps && r(g3)) {
        const e5 = o4.data.assetMaps;
        for (const t4 of e5) {
          const e6 = g3.get(t4.parentGlobalId).geometry;
          r(e6) && "mesh" === e6.type && e6.updateExternalSource({ source: [{ name: t4.assetName, source: t4.assetName }], extent: e6.extent });
        }
      }
    }
    return this._createEditsResult(q);
  }
  async deleteAttachments(e3, t2) {
    await this.load();
    const s3 = e3.attributes[this.layer.objectIdField], r3 = this.layer.parsedUrl.path + "/" + s3 + "/deleteAttachments";
    try {
      return (await U(r3, this._getLayerRequestOptions({ query: { attachmentIds: t2.join(",") }, method: "post" }))).data.deleteAttachmentResults.map(this._createFeatureEditResult);
    } catch (o3) {
      throw this._createAttachmentErrorResult(s3, o3);
    }
  }
  fetchRecomputedExtents(e3 = {}) {
    const t2 = e3.signal;
    return this.load({ signal: t2 }).then(async () => {
      const t3 = this._getLayerRequestOptions({ ...e3, query: { returnUpdates: true } }), { layerId: s3, url: o3 } = this.layer, { data: i2 } = await U(`${o3}/${s3}`, t3), { id: n, extent: u, fullExtent: l, timeExtent: d } = i2, c = u || l;
      return { id: n, fullExtent: c && w.fromJSON(c), timeExtent: d && T.fromJSON({ start: d[0], end: d[1] }) };
    });
  }
  async queryAttachments(e3, t2 = {}) {
    await this.load();
    const s3 = this._getLayerRequestOptions(t2);
    return this.queryTask.executeAttachmentQuery(e3, s3);
  }
  async queryFeatures(e3, t2) {
    return await this.load(), this.queryTask.execute(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryFeaturesJSON(e3, t2) {
    return await this.load(), this.queryTask.executeJSON(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryObjectIds(e3, t2) {
    return await this.load(), this.queryTask.executeForIds(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryFeatureCount(e3, t2) {
    return await this.load(), this.queryTask.executeForCount(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryExtent(e3, t2) {
    return await this.load(), this.queryTask.executeForExtent(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryRelatedFeatures(e3, t2) {
    return await this.load(), this.queryTask.executeRelationshipQuery(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryRelatedFeaturesCount(e3, t2) {
    return await this.load(), this.queryTask.executeRelationshipQueryForCount(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryTopFeatures(e3, t2) {
    return await this.load(), this.queryTask.executeTopFeaturesQuery(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryTopObjectIds(e3, t2) {
    return await this.load(), this.queryTask.executeForTopIds(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryTopExtents(e3, t2) {
    return await this.load(), this.queryTask.executeForTopExtents(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async queryTopCount(e3, t2) {
    return await this.load(), this.queryTask.executeForTopCount(e3, { ...t2, query: this._createRequestQueryOptions(t2) });
  }
  async fetchPublishingStatus() {
    if (!g(this.layer.url)) return "unavailable";
    const e3 = V(this.layer.url, "status"), t2 = await U(e3, { query: { f: "json" } });
    return j.fromJSON(t2.data.status);
  }
  _createRequestQueryOptions(e3) {
    const t2 = { ...this.layer.customParameters, token: this.layer.apiKey, ...e3 == null ? void 0 : e3.query };
    return this.layer.datesInUnknownTimezone && (t2.timeReferenceUnknownClient = true), t2;
  }
  async _fetchService(e3, t2) {
    if (!e3) {
      const { data: s4 } = await U(this.layer.parsedUrl.path, this._getLayerRequestOptions({ query: has("featurelayer-advanced-symbols") ? { returnAdvancedSymbols: true } : {}, signal: t2 }));
      e3 = s4;
    }
    this.sourceJSON = this._patchServiceJSON(e3);
    const s3 = e3.type;
    if (!O.has(s3)) throw new s("feature-layer-source:unsupported-type", `Source type "${s3}" is not supported`);
  }
  _patchServiceJSON(e3) {
    var _a;
    if ("Table" !== e3.type && e3.geometryType && !((_a = e3 == null ? void 0 : e3.drawingInfo) == null ? void 0 : _a.renderer) && !e3.defaultSymbol) {
      const t2 = o2(e3.geometryType).renderer;
      o("drawingInfo.renderer", t2, e3);
    }
    return "esriGeometryMultiPatch" === e3.geometryType && e3.infoFor3D && (e3.geometryType = "mesh"), e3;
  }
  _serializeFeature(e3, t2) {
    const { geometry: s3, attributes: a2 } = e3;
    if (r(t2) && r(e3.geometry) && "mesh" === e3.geometry.type) {
      const s4 = { ...a2 }, r3 = e3.geometry, o3 = r3.origin, i2 = r3.transform;
      if (s4[t2.transformFieldRoles.originX] = o3.x, s4[t2.transformFieldRoles.originY] = o3.y, s4[t2.transformFieldRoles.originZ] = o3.z, r(i2)) {
        const e4 = i2.translation, a3 = i2.scale, r4 = i2.rotation;
        s4[t2.transformFieldRoles.translationX] = e4[0], s4[t2.transformFieldRoles.translationY] = -e4[2], s4[t2.transformFieldRoles.translationZ] = e4[1], s4[t2.transformFieldRoles.scaleX] = a3[0], s4[t2.transformFieldRoles.scaleY] = a3[1], s4[t2.transformFieldRoles.scaleZ] = a3[2], s4[t2.transformFieldRoles.rotationX] = r4[0], s4[t2.transformFieldRoles.rotationY] = r4[2], s4[t2.transformFieldRoles.rotationZ] = r4[1], s4[t2.transformFieldRoles.rotationDeg] = r4[3];
      }
      return { geometry: null, attributes: s4 };
    }
    return t(s3) ? { attributes: a2 } : "mesh" === s3.type || "extent" === s3.type ? null : { geometry: s3.toJSON(), attributes: a2 };
  }
  async _serializeAttachment(e3) {
    const { feature: t2, attachment: s3 } = e3, { globalId: a2, name: r3, contentType: o3, data: i2, uploadId: n } = s3, u = { globalId: a2, parentGlobalId: null, contentType: null, name: null, uploadId: null, data: null };
    if (t2 && (u.parentGlobalId = "attributes" in t2 ? t2.attributes && t2.attributes[this.layer.globalIdField] : t2.globalId), n) u.uploadId = n;
    else if (i2) {
      const e4 = await S(i2);
      e4 && (u.contentType = e4.mediaType, u.data = e4.data), i2 instanceof File && (u.name = i2.name);
    }
    return r3 && (u.name = r3), o3 && (u.contentType = o3), u;
  }
  async _serializeAssetMapEditAndUploadAssets(e3, t2) {
    const s3 = this.layer.url;
    let r3 = null;
    try {
      const t3 = new Blob([e3.data], { type: e3.mimeType }), i3 = new FormData();
      i3.append("f", "json"), i3.append("file", t3, `${e3.assetName}`);
      const n2 = { body: i3, method: "post", responseType: "json" }, { data: u2 } = await U(`${s3}/uploads/upload`, n2);
      if (!u2.success) throw new s("feature-layer-source:upload-failure", "Expected upload to be successfull.");
      r3 = { assetType: e3.assetType, assetUploadId: u2.item.itemID };
    } catch (p) {
      r3 = null;
    }
    if (t(r3)) {
      const t3 = await S(new Blob([e3.data]));
      if (!t3.isBase64) throw new s("feature-layer-source:uploadAssets-failure", "Expected gltf data in base64 format after conversion.");
      r3 = { assetType: e3.assetType, assetData: t3.data };
    }
    if (t(r3)) throw new s("feature-layer-source:uploadAssets-failure", "Unable to prepare uploadAsset request options.");
    const i2 = { method: "post", query: { f: "json", assets: JSON.stringify([r3]) }, responseType: "json" }, n = await U(V(this.layer.parsedUrl.path, "uploadAssets"), i2);
    if (1 !== n.data.uploadResults.length || !n.data.uploadResults[0].success) throw new s("feature-layer-source:uploadAssets-failure", "Bad response.");
    const u = n.data.uploadResults[0].assetHash, d = [];
    e3.flags & E.PROJECT_VERTICES && d.push("PROJECT_VERTICES");
    const c = { globalId: e3.assetMapGlobalId, parentGlobalId: e3.featureGlobalId, assetName: e3.assetName, assetHash: u, flags: d };
    return t2.set(e3.featureGlobalId, e3.feature), c;
  }
  _getFeatureIds(e3, t2) {
    const s3 = e3 == null ? void 0 : e3[0];
    return s3 ? this._canUseGlobalIds(t2, e3) ? this._getGlobalIdsFromFeatureIdentifier(e3) : "objectId" in s3 ? this._getObjectIdsFromFeatureIdentifier(e3) : this._getIdsFromFeatures(e3) : [];
  }
  _getIdsFromFeatures(e3) {
    const t2 = this.layer.objectIdField;
    return e3.map((e4) => e4.attributes && e4.attributes[t2]);
  }
  _canUseGlobalIds(e3, t2) {
    return e3 && "globalId" in t2[0];
  }
  _getObjectIdsFromFeatureIdentifier(e3) {
    return e3.map((e4) => e4.objectId);
  }
  _getGlobalIdsFromFeatureIdentifier(e3) {
    return e3.map((e4) => e4.globalId);
  }
  _createEditsResult(e3) {
    var _a, _b, _c, _d, _e, _f;
    const t2 = e3.data, { layerId: s3 } = this.layer, a2 = [];
    let r3 = null;
    if (Array.isArray(t2)) for (const n of t2) a2.push({ id: n.id, editedFeatures: n.editedFeatures }), n.id === s3 && (r3 = { addResults: n.addResults ?? [], updateResults: n.updateResults ?? [], deleteResults: n.deleteResults ?? [], attachments: n.attachments, editMoment: n.editMoment });
    else r3 = t2;
    const o3 = r3 == null ? void 0 : r3.attachments, i2 = { addFeatureResults: ((_a = r3 == null ? void 0 : r3.addResults) == null ? void 0 : _a.map(this._createFeatureEditResult, this)) ?? [], updateFeatureResults: ((_b = r3 == null ? void 0 : r3.updateResults) == null ? void 0 : _b.map(this._createFeatureEditResult, this)) ?? [], deleteFeatureResults: ((_c = r3 == null ? void 0 : r3.deleteResults) == null ? void 0 : _c.map(this._createFeatureEditResult, this)) ?? [], addAttachmentResults: o3 && o3.addResults ? o3.addResults.map(this._createFeatureEditResult, this) : [], updateAttachmentResults: o3 && o3.updateResults ? o3.updateResults.map(this._createFeatureEditResult, this) : [], deleteAttachmentResults: o3 && o3.deleteResults ? o3.deleteResults.map(this._createFeatureEditResult, this) : [] };
    if ((r3 == null ? void 0 : r3.editMoment) && (i2.editMoment = r3.editMoment), a2.length > 0) {
      i2.editedFeatureResults = [];
      for (const e4 of a2) {
        const { editedFeatures: t3 } = e4, s4 = (t3 == null ? void 0 : t3.spatialReference) ? new f(t3.spatialReference) : null;
        i2.editedFeatureResults.push({ layerId: e4.id, editedFeatures: { adds: ((_d = t3 == null ? void 0 : t3.adds) == null ? void 0 : _d.map((e5) => this._createEditedFeature(e5, s4))) || [], updates: ((_e = t3 == null ? void 0 : t3.updates) == null ? void 0 : _e.map((e5) => ({ original: this._createEditedFeature(e5[0], s4), current: this._createEditedFeature(e5[1], s4) }))) || [], deletes: ((_f = t3 == null ? void 0 : t3.deletes) == null ? void 0 : _f.map((e5) => this._createEditedFeature(e5, s4))) || [], spatialReference: s4 } });
      }
    }
    return i2;
  }
  _createEditedFeature(e3, s3) {
    return new g2({ attributes: e3.attributes, geometry: v({ ...e3.geometry, spatialReference: s3 }) });
  }
  _createFeatureEditResult(e3) {
    const t2 = true === e3.success ? null : e3.error || { code: void 0, description: void 0 };
    return { objectId: e3.objectId, globalId: e3.globalId, error: t2 ? new s("feature-layer-source:edit-failure", t2.description, { code: t2.code }) : null };
  }
  _createAttachmentErrorResult(e3, t2) {
    const s3 = t2.details.messages && t2.details.messages[0] || t2.message, a2 = t2.details.httpStatus || t2.details.messageCode;
    return { objectId: e3, globalId: null, error: new s("feature-layer-source:attachment-failure", s3, { code: a2 }) };
  }
  _getFormDataForAttachment(e3, t2) {
    const s3 = e3 instanceof FormData ? e3 : e3 && e3.elements ? new FormData(e3) : null;
    if (s3) for (const a2 in t2) {
      const e4 = t2[a2];
      null != e4 && (s3.set ? s3.set(a2, e4) : s3.append(a2, e4));
    }
    return s3;
  }
  _getLayerRequestOptions(e3 = {}) {
    const { parsedUrl: t2, gdbVersion: s3, dynamicDataSource: a2 } = this.layer;
    return { ...e3, query: { gdbVersion: s3, layer: a2 ? JSON.stringify({ source: a2 }) : void 0, ...t2.query, f: "json", ...this._createRequestQueryOptions(e3) }, responseType: "json" };
  }
};
e([y()], T2.prototype, "type", void 0), e([y({ constructOnly: true })], T2.prototype, "layer", void 0), e([y({ readOnly: true })], T2.prototype, "queryTask", null), T2 = e([a("esri.layers.graphics.sources.FeatureLayerSource")], T2);
var A = T2;
export {
  A as default
};
//# sourceMappingURL=FeatureLayerSource-DOQSD7M2.js.map
