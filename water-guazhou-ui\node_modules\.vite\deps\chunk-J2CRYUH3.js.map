{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/shaders/RibbonLineTechniqueConfiguration.js", "../../@arcgis/core/chunks/RibbonLine.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../../../chunks/tslib.es6.js\";import{ShaderOutput as t}from\"../core/shaderLibrary/ShaderOutput.js\";import{parameter as e}from\"../core/shaderTechnique/ShaderTechniqueConfiguration.js\";import{TransparencyPassType as i}from\"../lib/TransparencyPassType.js\";import{DefaultTechniqueConfiguration as p}from\"../materials/DefaultTechniqueConfiguration.js\";var r;!function(o){o[o.BUTT=0]=\"BUTT\",o[o.SQUARE=1]=\"SQUARE\",o[o.ROUND=2]=\"ROUND\",o[o.COUNT=3]=\"COUNT\"}(r||(r={}));class s extends p{constructor(){super(...arguments),this.output=t.Color,this.capType=r.BUTT,this.transparencyPassType=i.NONE,this.occluder=!1,this.hasSlicePlane=!1,this.hasPolygonOffset=!1,this.writeDepth=!1,this.draped=!1,this.stippleEnabled=!1,this.stippleOffColorEnabled=!1,this.stippleScaleWithLineWidth=!1,this.stipplePreferContinuous=!0,this.roundJoins=!1,this.applyMarkerOffset=!1,this.vvSize=!1,this.vvColor=!1,this.vvOpacity=!1,this.falloffEnabled=!1,this.innerColorEnabled=!1,this.hasOccludees=!1,this.hasMultipassTerrain=!1,this.cullAboveGround=!1,this.wireframe=!1,this.objectAndLayerIdColorInstanced=!1}}o([e({count:t.COUNT})],s.prototype,\"output\",void 0),o([e({count:r.COUNT})],s.prototype,\"capType\",void 0),o([e({count:i.COUNT})],s.prototype,\"transparencyPassType\",void 0),o([e()],s.prototype,\"occluder\",void 0),o([e()],s.prototype,\"hasSlicePlane\",void 0),o([e()],s.prototype,\"hasPolygonOffset\",void 0),o([e()],s.prototype,\"writeDepth\",void 0),o([e()],s.prototype,\"draped\",void 0),o([e()],s.prototype,\"stippleEnabled\",void 0),o([e()],s.prototype,\"stippleOffColorEnabled\",void 0),o([e()],s.prototype,\"stippleScaleWithLineWidth\",void 0),o([e()],s.prototype,\"stipplePreferContinuous\",void 0),o([e()],s.prototype,\"roundJoins\",void 0),o([e()],s.prototype,\"applyMarkerOffset\",void 0),o([e()],s.prototype,\"vvSize\",void 0),o([e()],s.prototype,\"vvColor\",void 0),o([e()],s.prototype,\"vvOpacity\",void 0),o([e()],s.prototype,\"falloffEnabled\",void 0),o([e()],s.prototype,\"innerColorEnabled\",void 0),o([e()],s.prototype,\"hasOccludees\",void 0),o([e()],s.prototype,\"hasMultipassTerrain\",void 0),o([e()],s.prototype,\"cullAboveGround\",void 0),o([e()],s.prototype,\"wireframe\",void 0),o([e({constValue:!0})],s.prototype,\"stippleRequiresClamp\",void 0),o([e({constValue:!0})],s.prototype,\"stippleRequiresStretchMeasure\",void 0),o([e({constValue:!0})],s.prototype,\"hasVvInstancing\",void 0),o([e({constValue:!0})],s.prototype,\"hasSliceTranslatedView\",void 0),o([e()],s.prototype,\"objectAndLayerIdColorInstanced\",void 0);export{r as CapType,s as RibbonLineTechniqueConfiguration};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as e}from\"../core/maybe.js\";import{addLinearDepth as i,addCalculateLinearDepth as t}from\"../views/3d/webgl-engine/core/shaderLibrary/ForwardLinearDepth.glsl.js\";import{ShaderOutput as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as r}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{ObjectAndLayerIdColor as n}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/ObjectAndLayerIdColor.glsl.js\";import{RibbonVertexPosition as a}from\"../views/3d/webgl-engine/core/shaderLibrary/attributes/RibbonVertexPosition.glsl.js\";import{OutputDepth as s}from\"../views/3d/webgl-engine/core/shaderLibrary/output/OutputDepth.glsl.js\";import{LineStipple as l,computePixelSize as d}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/LineStipple.glsl.js\";import{MarkerSizing as p}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MarkerSizing.glsl.js\";import{multipassTerrainTest as c}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{PiUtils as g}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/PiUtils.glsl.js\";import{symbolAlphaCutoff as m}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as v}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as f}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float2PassUniform as h}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{Float4PassUniform as u}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as D}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as b}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{Matrix4PassUniform as S}from\"../views/3d/webgl-engine/core/shaderModules/Matrix4PassUniform.js\";import{ShaderBuilder as x}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as L}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as w}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";import{LineMarkerSpace as y}from\"../views/3d/webgl-engine/shaders/LineMarkerTechniqueConfiguration.js\";import{CapType as C}from\"../views/3d/webgl-engine/shaders/RibbonLineTechniqueConfiguration.js\";const j=1;function R(R){const A=new x,{vertex:P,fragment:F}=A,z=R.hasMultipassTerrain&&(R.output===o.Color||R.output===o.Alpha);A.include(g),A.include(a,R),A.include(l,R);const E=R.applyMarkerOffset&&!R.draped;E&&(P.uniforms.add(new D(\"markerScale\",(e=>e.markerScale))),A.include(p,{space:y.World})),R.output===o.Depth&&A.include(s,R),A.include(n,R),f(P,R),P.uniforms.add([new S(\"inverseProjectionMatrix\",((e,i)=>i.camera.inverseProjectionMatrix)),new h(\"nearFar\",((e,i)=>i.camera.nearFar)),new D(\"miterLimit\",(e=>\"miter\"!==e.join?0:e.miterLimit)),new u(\"viewport\",((e,i)=>i.camera.fullViewport))]),P.constants.add(\"LARGE_HALF_FLOAT\",\"float\",65500),A.attributes.add(w.POSITION,\"vec3\"),A.attributes.add(w.SUBDIVISIONFACTOR,\"float\"),A.attributes.add(w.UV0,\"vec2\"),A.attributes.add(w.AUXPOS1,\"vec3\"),A.attributes.add(w.AUXPOS2,\"vec3\"),A.varyings.add(\"vColor\",\"vec4\"),A.varyings.add(\"vpos\",\"vec3\"),i(A),z&&A.varyings.add(\"depth\",\"float\");const T=R.capType===C.ROUND,W=R.stippleEnabled&&R.stippleScaleWithLineWidth||T;W&&A.varyings.add(\"vLineWidth\",\"float\");const O=R.stippleEnabled&&R.stippleScaleWithLineWidth;O&&A.varyings.add(\"vLineSizeInv\",\"float\");const V=R.innerColorEnabled||T;V&&A.varyings.add(\"vLineDistance\",\"float\");const N=R.stippleEnabled&&T,I=R.falloffEnabled||N;I&&A.varyings.add(\"vLineDistanceNorm\",\"float\"),T&&(A.varyings.add(\"vSegmentSDF\",\"float\"),A.varyings.add(\"vReverseSegmentSDF\",\"float\")),P.code.add(b`#define PERPENDICULAR(v) vec2(v.y, -v.x);\nfloat interp(float ncp, vec4 a, vec4 b) {\nreturn (-ncp - a.z) / (b.z - a.z);\n}\nvec2 rotate(vec2 v, float a) {\nfloat s = sin(a);\nfloat c = cos(a);\nmat2 m = mat2(c, -s, s, c);\nreturn m * v;\n}`),P.code.add(b`vec4 projectAndScale(vec4 pos) {\nvec4 posNdc = proj * pos;\nposNdc.xy *= viewport.zw / posNdc.w;\nreturn posNdc;\n}`),t(A),P.code.add(b`\n    void clipAndTransform(inout vec4 pos, inout vec4 prev, inout vec4 next, in bool isStartVertex) {\n      float vnp = nearFar[0] * 0.99;\n\n      if(pos.z > -nearFar[0]) {\n        //current pos behind ncp --> we need to clip\n        if (!isStartVertex) {\n          if(prev.z < -nearFar[0]) {\n            //previous in front of ncp\n            pos = mix(prev, pos, interp(vnp, prev, pos));\n            next = pos;\n          } else {\n            pos = vec4(0.0, 0.0, 0.0, 1.0);\n          }\n        } else {\n          if(next.z < -nearFar[0]) {\n            //next in front of ncp\n            pos = mix(pos, next, interp(vnp, pos, next));\n            prev = pos;\n          } else {\n            pos = vec4(0.0, 0.0, 0.0, 1.0);\n          }\n        }\n      } else {\n        //current position visible\n        if (prev.z > -nearFar[0]) {\n          //previous behind ncp\n          prev = mix(pos, prev, interp(vnp, pos, prev));\n        }\n        if (next.z > -nearFar[0]) {\n          //next behind ncp\n          next = mix(next, pos, interp(vnp, next, pos));\n        }\n      }\n\n      ${z?\"depth = pos.z;\":\"\"}\n      linearDepth = calculateLinearDepth(nearFar,pos.z);\n\n      pos = projectAndScale(pos);\n      next = projectAndScale(next);\n      prev = projectAndScale(prev);\n    }\n  `),P.uniforms.add(new D(\"pixelRatio\",((e,i)=>i.camera.pixelRatio))),P.code.add(b`\n  void main(void) {\n    // unpack values from uv0.y\n    bool isStartVertex = abs(abs(uv0.y)-3.0) == 1.0;\n\n    float coverage = 1.0;\n\n    // Check for special value of uv0.y which is used by the Renderer when graphics\n    // are removed before the VBO is recompacted. If this is the case, then we just\n    // project outside of clip space.\n    if (uv0.y == 0.0) {\n      // Project out of clip space\n      gl_Position = vec4(1e038, 1e038, 1e038, 1.0);\n    }\n    else {\n      bool isJoin = abs(uv0.y) < 3.0;\n\n      float lineSize = getSize();\n      float lineWidth = lineSize * pixelRatio;\n\n      ${W?b`vLineWidth = lineWidth;`:\"\"}\n      ${O?b`vLineSizeInv = 1.0 / lineSize;`:\"\"}\n\n      // convert sub-pixel coverage to alpha\n      if (lineWidth < 1.0) {\n        coverage = lineWidth;\n        lineWidth = 1.0;\n      }else{\n        // Ribbon lines cannot properly render non-integer sizes. Round width to integer size if\n        // larger than one for better quality. Note that we do render < 1 pixels more or less correctly\n        // so we only really care to round anything larger than 1.\n        lineWidth = floor(lineWidth + 0.5);\n      }\n\n      vec4 pos  = view * vec4(position.xyz, 1.0);\n      vec4 prev = view * vec4(auxpos1.xyz, 1.0);\n      vec4 next = view * vec4(auxpos2.xyz, 1.0);\n  `),E&&P.code.add(b`vec4 other = isStartVertex ? next : prev;\nbool markersHidden = areWorldMarkersHidden(pos, other);\nif(!isJoin && !markersHidden) {\npos.xyz += normalize(other.xyz - pos.xyz) * getWorldMarkerSize(pos) * 0.5;\n}`),P.code.add(b`clipAndTransform(pos, prev, next, isStartVertex);\nvec2 left = (pos.xy - prev.xy);\nvec2 right = (next.xy - pos.xy);\nfloat leftLen = length(left);\nfloat rightLen = length(right);`);(R.stippleEnabled||T)&&P.code.add(b`\n      float isEndVertex = float(!isStartVertex);\n      vec2 segmentOrigin = mix(pos.xy, prev.xy, isEndVertex);\n      vec2 segment = mix(right, left, isEndVertex);\n      ${T?b`vec2 segmentEnd = mix(next.xy, pos.xy, isEndVertex);`:\"\"}\n    `),P.code.add(b`left = (leftLen > 0.001) ? left/leftLen : vec2(0.0, 0.0);\nright = (rightLen > 0.001) ? right/rightLen : vec2(0.0, 0.0);\nvec2 capDisplacementDir = vec2(0, 0);\nvec2 joinDisplacementDir = vec2(0, 0);\nfloat displacementLen = lineWidth;\nif (isJoin) {\nbool isOutside = (left.x * right.y - left.y * right.x) * uv0.y > 0.0;\njoinDisplacementDir = normalize(left + right);\njoinDisplacementDir = PERPENDICULAR(joinDisplacementDir);\nif (leftLen > 0.001 && rightLen > 0.001) {\nfloat nDotSeg = dot(joinDisplacementDir, left);\ndisplacementLen /= length(nDotSeg * left - joinDisplacementDir);\nif (!isOutside) {\ndisplacementLen = min(displacementLen, min(leftLen, rightLen)/abs(nDotSeg));\n}\n}\nif (isOutside && (displacementLen > miterLimit * lineWidth)) {`),R.roundJoins?P.code.add(b`\n        vec2 startDir = leftLen < 0.001 ? right : left;\n        startDir = PERPENDICULAR(startDir);\n\n        vec2 endDir = rightLen < 0.001 ? left : right;\n        endDir = PERPENDICULAR(endDir);\n\n        float factor = ${R.stippleEnabled?b`min(1.0, subdivisionFactor * ${b.float((j+2)/(j+1))})`:b`subdivisionFactor`};\n\n        float rotationAngle = acos(clamp(dot(startDir, endDir), -1.0, 1.0));\n        joinDisplacementDir = rotate(startDir, -sign(uv0.y) * factor * rotationAngle);\n      `):P.code.add(b`if (leftLen < 0.001) {\njoinDisplacementDir = right;\n}\nelse if (rightLen < 0.001) {\njoinDisplacementDir = left;\n}\nelse {\njoinDisplacementDir = (isStartVertex || subdivisionFactor > 0.0) ? right : left;\n}\njoinDisplacementDir = PERPENDICULAR(joinDisplacementDir);`);const _=R.capType!==C.BUTT;return P.code.add(b`\n        displacementLen = lineWidth;\n      }\n    } else {\n      // CAP handling ---------------------------------------------------\n      joinDisplacementDir = isStartVertex ? right : left;\n      joinDisplacementDir = PERPENDICULAR(joinDisplacementDir);\n\n      ${_?b`capDisplacementDir = isStartVertex ? -right : left;`:\"\"}\n    }\n  `),P.code.add(b`\n    // Displacement (in pixels) caused by join/or cap\n    vec2 dpos = joinDisplacementDir * sign(uv0.y) * displacementLen + capDisplacementDir * displacementLen;\n\n    ${I||V?b`float lineDistNorm = sign(uv0.y) * pos.w;`:\"\"}\n\n    ${V?b`vLineDistance = lineWidth * lineDistNorm;`:\"\"}\n    ${I?b`vLineDistanceNorm = lineDistNorm;`:\"\"}\n\n    pos.xy += dpos;\n  `),T&&P.code.add(b`vec2 segmentDir = normalize(segment);\nvSegmentSDF = (isJoin && isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentOrigin, segmentDir) * pos.w) ;\nvReverseSegmentSDF = (isJoin && !isStartVertex) ? LARGE_HALF_FLOAT : (dot(pos.xy - segmentEnd, -segmentDir) * pos.w);`),R.stippleEnabled&&(R.draped?P.uniforms.add(new D(\"worldToScreenRatio\",((e,i)=>1/i.screenToPCSRatio))):P.code.add(b`vec3 segmentCenter = mix((auxpos2 + position) * 0.5, (position + auxpos1) * 0.5, isEndVertex);\nfloat worldToScreenRatio = computeWorldToScreenRatio(segmentCenter);`),P.code.add(b`float segmentLengthScreenDouble = length(segment);\nfloat segmentLengthScreen = segmentLengthScreenDouble * 0.5;\nfloat discreteWorldToScreenRatio = discretizeWorldToScreenRatio(worldToScreenRatio);\nfloat segmentLengthRender = length(mix(auxpos2 - position, position - auxpos1, isEndVertex));\nvStipplePatternStretch = worldToScreenRatio / discreteWorldToScreenRatio;`),R.draped?P.code.add(b`float segmentLengthPseudoScreen = segmentLengthScreen / pixelRatio * discreteWorldToScreenRatio / worldToScreenRatio;\nfloat startPseudoScreen = uv0.x * discreteWorldToScreenRatio - mix(0.0, segmentLengthPseudoScreen, isEndVertex);`):P.code.add(b`float startPseudoScreen = mix(uv0.x, uv0.x - segmentLengthRender, isEndVertex) * discreteWorldToScreenRatio;\nfloat segmentLengthPseudoScreen = segmentLengthRender * discreteWorldToScreenRatio;`),P.uniforms.add(new D(\"stipplePatternPixelSize\",(e=>d(e)))),P.code.add(b`\n      float patternLength = ${R.stippleScaleWithLineWidth?\"lineSize * \":\"\"} stipplePatternPixelSize;\n\n      // Compute the coordinates at both start and end of the line segment, because we need both to clamp to in the fragment shader\n      vStippleDistanceLimits = computeStippleDistanceLimits(startPseudoScreen, segmentLengthPseudoScreen, segmentLengthScreen, patternLength);\n\n      vStippleDistance = mix(vStippleDistanceLimits.x, vStippleDistanceLimits.y, isEndVertex);\n\n      // Adjust the coordinate to the displaced position (the pattern is shortened/overextended on the in/outside of joins)\n      if (segmentLengthScreenDouble >= 0.001) {\n        // Project the actual vertex position onto the line segment. Note that the resulting factor is within [0..1] at the\n        // original vertex positions, and slightly outside of that range at the displaced positions\n        vec2 stippleDisplacement = pos.xy - segmentOrigin;\n        float stippleDisplacementFactor = dot(segment, stippleDisplacement) / (segmentLengthScreenDouble * segmentLengthScreenDouble);\n\n        // Apply this offset to the actual vertex coordinate (can be screen or pseudo-screen space)\n        vStippleDistance += (stippleDisplacementFactor - isEndVertex) * (vStippleDistanceLimits.y - vStippleDistanceLimits.x);\n      }\n\n      // Cancel out perspective correct interpolation because we want this length the really represent the screen distance\n      vStippleDistanceLimits *= pos.w;\n      vStippleDistance *= pos.w;\n\n      // Disable stipple distance limits on caps\n      vStippleDistanceLimits = isJoin ?\n                                 vStippleDistanceLimits :\n                                 isStartVertex ?\n                                  vec2(-1e038, vStippleDistanceLimits.y) :\n                                  vec2(vStippleDistanceLimits.x, 1e038);\n    `)),P.code.add(b`\n      // Convert back into NDC\n      pos.xy = (pos.xy / viewport.zw) * pos.w;\n\n      vColor = getColor();\n      vColor.a *= coverage;\n\n      ${R.wireframe&&!R.draped?\"pos.z -= 0.001 * pos.w;\":\"\"}\n\n      // transform final position to camera space for slicing\n      vpos = (inverseProjectionMatrix * pos).xyz;\n      gl_Position = pos;\n      forwardObjectAndLayerIdColor();\n    }\n  }\n  `),z&&A.include(c,R),A.include(r,R),F.include(v),F.code.add(b`\n  void main() {\n    discardBySlice(vpos);\n    ${z?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n  `),R.wireframe?F.code.add(b`vec4 finalColor = vec4(1.0, 0.0, 1.0, 1.0);`):(T&&F.code.add(b`\n      float sdf = min(vSegmentSDF, vReverseSegmentSDF);\n      vec2 fragmentPosition = vec2(\n        min(sdf, 0.0),\n        vLineDistance\n      ) * gl_FragCoord.w;\n\n      float fragmentRadius = length(fragmentPosition);\n      float fragmentCapSDF = (fragmentRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale\n      float capCoverage = clamp(0.5 - fragmentCapSDF, 0.0, 1.0);\n\n      if (capCoverage < ${b.float(m)}) {\n        discard;\n      }\n    `),N?F.code.add(b`\n      vec2 stipplePosition = vec2(\n        min(getStippleSDF() * 2.0 - 1.0, 0.0),\n        vLineDistanceNorm * gl_FragCoord.w\n      );\n      float stippleRadius = length(stipplePosition * vLineWidth);\n      float stippleCapSDF = (stippleRadius - vLineWidth) * 0.5; // Divide by 2 to transform from double pixel scale\n      float stippleCoverage = clamp(0.5 - stippleCapSDF, 0.0, 1.0);\n      float stippleAlpha = step(${b.float(m)}, stippleCoverage);\n      `):F.code.add(b`float stippleAlpha = getStippleAlpha();`),F.uniforms.add(new u(\"intrinsicColor\",(e=>e.color))),R.output!==o.ObjectAndLayerIdColor&&F.code.add(b`discardByStippleAlpha(stippleAlpha, stippleAlphaColorDiscard);`),F.code.add(b`vec4 color = intrinsicColor * vColor;`),R.innerColorEnabled&&(F.uniforms.add(new u(\"innerColor\",(i=>e(i.innerColor,i.color)))),F.uniforms.add(new D(\"innerWidth\",((e,i)=>e.innerWidth*i.camera.pixelRatio))),F.code.add(b`float distToInner = abs(vLineDistance * gl_FragCoord.w) - innerWidth;\nfloat innerAA = clamp(0.5 - distToInner, 0.0, 1.0);\nfloat innerAlpha = innerColor.a + color.a * (1.0 - innerColor.a);\ncolor = mix(color, vec4(innerColor.rgb, innerAlpha), innerAA);`)),F.code.add(b`vec4 finalColor = blendStipple(color, stippleAlpha);`),R.falloffEnabled&&(F.uniforms.add(new D(\"falloff\",(e=>e.falloff))),F.code.add(b`finalColor.a *= pow(max(0.0, 1.0 - abs(vLineDistanceNorm * gl_FragCoord.w)), falloff);`))),F.code.add(b`\n    ${R.output===o.ObjectAndLayerIdColor?b`finalColor.a = 1.0;`:\"\"}\n\n    if (finalColor.a < ${b.float(m)}) {\n      discard;\n    }\n\n    ${R.output===o.Alpha?b`gl_FragColor = vec4(finalColor.a);`:\"\"}\n    ${R.output===o.Color?b`gl_FragColor = highlightSlice(finalColor, vpos);`:\"\"}\n    ${R.output===o.Color&&R.transparencyPassType===L.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}\n    ${R.output===o.Highlight?b`gl_FragColor = vec4(1.0);`:\"\"}\n    ${R.output===o.Depth?b`outputDepth(linearDepth);`:\"\"}\n    ${R.output===o.ObjectAndLayerIdColor?b`outputObjectAndLayerIdColor();`:\"\"}\n  }\n  `),A}const A=Object.freeze(Object.defineProperty({__proto__:null,RIBBONLINE_NUM_ROUND_JOIN_SUBDIVISIONS:j,build:R},Symbol.toStringTag,{value:\"Module\"}));export{j as R,A as a,R as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIiX,IAAIA;AAAE,CAAC,SAASC,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE;AAAO,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAME,KAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO,EAAE,OAAM,KAAK,UAAQF,GAAE,MAAK,KAAK,uBAAqBC,GAAE,MAAK,KAAK,WAAS,OAAG,KAAK,gBAAc,OAAG,KAAK,mBAAiB,OAAG,KAAK,aAAW,OAAG,KAAK,SAAO,OAAG,KAAK,iBAAe,OAAG,KAAK,yBAAuB,OAAG,KAAK,4BAA0B,OAAG,KAAK,0BAAwB,MAAG,KAAK,aAAW,OAAG,KAAK,oBAAkB,OAAG,KAAK,SAAO,OAAG,KAAK,UAAQ,OAAG,KAAK,YAAU,OAAG,KAAK,iBAAe,OAAG,KAAK,oBAAkB,OAAG,KAAK,eAAa,OAAG,KAAK,sBAAoB,OAAG,KAAK,kBAAgB,OAAG,KAAK,YAAU,OAAG,KAAK,iCAA+B;AAAA,EAAE;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,OAAM,EAAE,MAAK,CAAC,CAAC,GAAEC,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMF,GAAE,MAAK,CAAC,CAAC,GAAEE,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMD,GAAE,MAAK,CAAC,CAAC,GAAEC,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iCAAgC,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,YAAW,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,kCAAiC,MAAM;;;ACAxF,IAAM,IAAE;AAAE,SAAS,EAAEC,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAE,EAAC,QAAO,GAAE,UAAS,EAAC,IAAED,IAAE,IAAED,GAAE,wBAAsBA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE;AAAO,EAAAC,GAAE,QAAQE,EAAC,GAAEF,GAAE,QAAQG,IAAEJ,EAAC,GAAEC,GAAE,QAAQI,IAAEL,EAAC;AAAE,QAAM,IAAEA,GAAE,qBAAmB,CAACA,GAAE;AAAO,QAAI,EAAE,SAAS,IAAI,IAAIE,GAAE,eAAe,CAAAI,OAAGA,GAAE,WAAY,CAAC,GAAEL,GAAE,QAAQ,GAAE,EAAC,OAAM,EAAE,MAAK,CAAC,IAAGD,GAAE,WAAS,EAAE,SAAOC,GAAE,QAAQC,IAAEF,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,GAAEA,EAAC,GAAE,EAAE,SAAS,IAAI,CAAC,IAAIM,GAAE,2BAA2B,CAACA,IAAEC,OAAIA,GAAE,OAAO,uBAAwB,GAAE,IAAID,GAAE,WAAW,CAACA,IAAEC,OAAIA,GAAE,OAAO,OAAQ,GAAE,IAAIL,GAAE,cAAc,CAAAI,OAAG,YAAUA,GAAE,OAAK,IAAEA,GAAE,UAAW,GAAE,IAAIA,GAAE,YAAY,CAACA,IAAEC,OAAIA,GAAE,OAAO,YAAa,CAAC,CAAC,GAAE,EAAE,UAAU,IAAI,oBAAmB,SAAQ,KAAK,GAAEN,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,mBAAkB,OAAO,GAAEA,GAAE,WAAW,IAAI,EAAE,KAAI,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,SAAQ,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,SAAQ,MAAM,GAAEA,GAAE,SAAS,IAAI,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,QAAO,MAAM,GAAEE,GAAEF,EAAC,GAAE,KAAGA,GAAE,SAAS,IAAI,SAAQ,OAAO;AAAE,QAAM,IAAED,GAAE,YAAUQ,GAAE,OAAM,IAAER,GAAE,kBAAgBA,GAAE,6BAA2B;AAAE,OAAGC,GAAE,SAAS,IAAI,cAAa,OAAO;AAAE,QAAMQ,KAAET,GAAE,kBAAgBA,GAAE;AAA0B,EAAAS,MAAGR,GAAE,SAAS,IAAI,gBAAe,OAAO;AAAE,QAAM,IAAED,GAAE,qBAAmB;AAAE,OAAGC,GAAE,SAAS,IAAI,iBAAgB,OAAO;AAAE,QAAM,IAAED,GAAE,kBAAgB,GAAE,IAAEA,GAAE,kBAAgB;AAAE,OAAGC,GAAE,SAAS,IAAI,qBAAoB,OAAO,GAAE,MAAIA,GAAE,SAAS,IAAI,eAAc,OAAO,GAAEA,GAAE,SAAS,IAAI,sBAAqB,OAAO,IAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS9uH,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIb,GAAE,EAAEA,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAmCZ,IAAE,mBAAiB,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAO1B,GAAE,EAAE,SAAS,IAAI,IAAIC,GAAE,cAAc,CAACI,IAAEC,OAAIA,GAAE,OAAO,UAAW,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAoBzE,IAAE,6BAA2B,EAAE;AAAA,QAC/BE,KAAE,oCAAkC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAgB3C,GAAE,KAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,EAIjB,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,gCAIiB;AAAE,GAACT,GAAE,kBAAgB,MAAI,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,QAI5D,IAAE,0DAAwD,EAAE;AAAA,KAC/D,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+DAgB6C,GAAEA,GAAE,aAAW,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAOhEA,GAAE,iBAAe,iCAAiC,EAAE,OAAO,IAAE,MAAI,IAAE,EAAE,CAAC,MAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA,OAIhH,IAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0DASsC;AAAE,QAAM,IAAEA,GAAE,YAAUQ,GAAE;AAAK,SAAO,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQjG,IAAE,yDAAuD,EAAE;AAAA;AAAA,GAEhE,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,MAIV,KAAG,IAAE,+CAA6C,EAAE;AAAA;AAAA,MAEpD,IAAE,+CAA6C,EAAE;AAAA,MACjD,IAAE,uCAAqC,EAAE;AAAA;AAAA;AAAA,GAG5C,GAAE,KAAG,EAAE,KAAK,IAAI;AAAA;AAAA,sHAEmG,GAAER,GAAE,mBAAiBA,GAAE,SAAO,EAAE,SAAS,IAAI,IAAIE,GAAE,sBAAsB,CAACI,IAAEC,OAAI,IAAEA,GAAE,gBAAiB,CAAC,IAAE,EAAE,KAAK,IAAI;AAAA,qEACpK,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA,0EAIR,GAAEP,GAAE,SAAO,EAAE,KAAK,IAAI;AAAA,iHACiB,IAAE,EAAE,KAAK,IAAI;AAAA,oFAC1C,GAAE,EAAE,SAAS,IAAI,IAAIE,GAAE,2BAA2B,CAAAI,OAAG,EAAEA,EAAC,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA,8BAC9HN,GAAE,4BAA0B,gBAAc,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KA4BrE,IAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOXA,GAAE,aAAW,CAACA,GAAE,SAAO,4BAA0B,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAQxD,GAAE,KAAGC,GAAE,QAAQS,IAAEV,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC,GAAE,EAAE,QAAQM,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,MAGxD,IAAE,2CAAyC,EAAE;AAAA,GAChD,GAAEN,GAAE,YAAU,EAAE,KAAK,IAAI,8CAA8C,KAAG,KAAG,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAWjE,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA,KAG/B,GAAE,IAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAQc,EAAE,MAAM,CAAC,CAAC;AAAA,OACrC,IAAE,EAAE,KAAK,IAAI,0CAA0C,GAAE,EAAE,SAAS,IAAI,IAAIM,GAAE,kBAAkB,CAAAA,OAAGA,GAAE,KAAM,CAAC,GAAEN,GAAE,WAAS,EAAE,yBAAuB,EAAE,KAAK,IAAI,iEAAiE,GAAE,EAAE,KAAK,IAAI,wCAAwC,GAAEA,GAAE,sBAAoB,EAAE,SAAS,IAAI,IAAIM,GAAE,cAAc,CAAAC,OAAG,EAAEA,GAAE,YAAWA,GAAE,KAAK,CAAE,CAAC,GAAE,EAAE,SAAS,IAAI,IAAIL,GAAE,cAAc,CAACI,IAAEC,OAAID,GAAE,aAAWC,GAAE,OAAO,UAAW,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,+DAG7Y,IAAG,EAAE,KAAK,IAAI,uDAAuD,GAAEP,GAAE,mBAAiB,EAAE,SAAS,IAAI,IAAIE,GAAE,WAAW,CAAAI,OAAGA,GAAE,OAAQ,CAAC,GAAE,EAAE,KAAK,IAAI,yFAAyF,KAAI,EAAE,KAAK,IAAI;AAAA,MACtTN,GAAE,WAAS,EAAE,wBAAsB,yBAAuB,EAAE;AAAA;AAAA,yBAEzC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,MAI7BA,GAAE,WAAS,EAAE,QAAM,wCAAsC,EAAE;AAAA,MAC3DA,GAAE,WAAS,EAAE,QAAM,sDAAoD,EAAE;AAAA,MACzEA,GAAE,WAAS,EAAE,SAAOA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE;AAAA,MACxGF,GAAE,WAAS,EAAE,YAAU,+BAA6B,EAAE;AAAA,MACtDA,GAAE,WAAS,EAAE,QAAM,+BAA6B,EAAE;AAAA,MAClDA,GAAE,WAAS,EAAE,wBAAsB,oCAAkC,EAAE;AAAA;AAAA,GAE1E,GAAEC;AAAC;AAAC,IAAM,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,wCAAuC,GAAE,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["r", "o", "s", "R", "A", "o", "t", "s", "u", "e", "i", "r", "O", "n"]}