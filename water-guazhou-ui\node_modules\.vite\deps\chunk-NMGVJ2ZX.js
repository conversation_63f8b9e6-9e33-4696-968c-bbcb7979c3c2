import {
  O,
  n as n2
} from "./chunk-INH5JU5P.js";
import {
  u
} from "./chunk-UQWZJZ2S.js";
import {
  n
} from "./chunk-5S4W3ME5.js";

// node_modules/@arcgis/core/views/interactive/snapping/candidates/DrapedEdgeSnappingCandidate.js
var s = class extends n2 {
  constructor(n3) {
    super({ ...n3, isDraped: true, constraint: new O(n3.edgeStart, n3.edgeEnd, n3.getGroundElevation) });
  }
  get hints() {
    return [new n(u.REFERENCE, this.constraint.start, this.constraint.end, this.isDraped, this.domain)];
  }
};

export {
  s
};
//# sourceMappingURL=chunk-NMGVJ2ZX.js.map
