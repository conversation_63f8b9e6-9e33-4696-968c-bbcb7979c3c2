import {
  r,
  t
} from "./chunk-KVEZ26WH.js";
import {
  a
} from "./chunk-LHO3WKNH.js";
import {
  e
} from "./chunk-NDCSRZLO.js";

// node_modules/@arcgis/core/views/3d/webgl-engine/materials/DefaultTechniqueConfiguration.js
var s = class extends t {
  constructor() {
    super(...arguments), this.hasWebGL2Context = false;
  }
};
e([r({ constValue: true })], s.prototype, "hasSliceHighlight", void 0), e([r({ constValue: false })], s.prototype, "hasSliceInVertexProgram", void 0), e([r({ constValue: false })], s.prototype, "instancedDoublePrecision", void 0), e([r({ constValue: false })], s.prototype, "useLegacyTerrainShading", void 0), e([r({ constValue: false })], s.prototype, "hasModelTransformation", void 0), e([r({ constValue: a.Pass })], s.prototype, "pbrTextureBindType", void 0), e([r()], s.prototype, "hasWebGL2Context", void 0);

export {
  s
};
//# sourceMappingURL=chunk-FQZM46ZM.js.map
