import {
  M as M2,
  _,
  a as a2,
  a3 as a4,
  k,
  m as m2,
  n,
  o as o4,
  u,
  u2,
  x as x2,
  y2
} from "./chunk-6EHSQNHE.js";
import {
  A,
  M,
  a as a3,
  f,
  g,
  k as k2,
  m as m3,
  o as o3,
  p as p4,
  r as r3,
  s as s5,
  x
} from "./chunk-Z27XYQOC.js";
import {
  s as s4
} from "./chunk-YACF4WM5.js";
import {
  p as p3
} from "./chunk-O2BYTJI4.js";
import {
  m,
  p as p2
} from "./chunk-3M3FTH72.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  w
} from "./chunk-63M4K32A.js";
import {
  s as s3
} from "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  D
} from "./chunk-EIGTETCG.js";
import {
  o as o2
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  s as s2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/rasterFunctions/BaseFunctionArguments.js
var e2 = class extends l {
  constructor() {
    super(...arguments), this.raster = void 0;
  }
};
e([y({ json: { write: true } })], e2.prototype, "raster", void 0), e2 = e([a("esri.layers.support.rasterFunctions.AspectFunctionArguments")], e2);
var p5 = e2;

// node_modules/@arcgis/core/layers/support/rasterFunctions/AspectFunctionArguments.js
var e3;
var t2 = e3 = class extends p5 {
  clone() {
    return new e3({ raster: this.raster });
  }
};
t2 = e3 = e([a("esri.layers.support.rasterFunctions.AspectFunctionArguments")], t2);
var c = t2;

// node_modules/@arcgis/core/layers/support/rasterFunctions/BaseRasterFunction.js
var p6 = class extends l {
  constructor() {
    super(...arguments), this.functionArguments = null, this.readingBufferSize = 0, this.id = -1, this.isNoopProcess = false, this.rawInputBandIds = [], this.isInputBandIdsSwizzled = false, this.swizzledBandSelection = [], this.isBranch = false, this._bindingResult = null;
  }
  get supportsGPU() {
    return this._bindingResult.supportsGPU;
  }
  bind(t6, s10 = false, r7 = -1) {
    this.id = r7 + 1;
    const i14 = this._getRasterValues();
    let n14 = true;
    for (let o10 = 0; o10 < i14.length; o10++) {
      const r8 = i14[o10];
      if (r(r8) && this._isRasterFunctionValue(r8)) {
        const e8 = r8.bind(t6, s10, this.id + o10);
        if (!e8.success) return this._bindingResult = e8, e8;
        n14 = n14 && e8.supportsGPU;
      }
    }
    return !this.rasterInfo || s10 ? (this.sourceRasterInfos = this._getSourceRasterInfos(t6), this._bindingResult = this._bindSourceRasters(), this._bindingResult.supportsGPU = n14 && this._bindingResult.supportsGPU, this.processInputBandIds(), this._bindingResult) : (this._bindingResult = { success: true, supportsGPU: true }, this.processInputBandIds(), this._bindingResult);
  }
  process(t6) {
    const s10 = this._getRasterValues(), e8 = 0 === s10.length ? t6.pixelBlocks ?? t6.primaryPixelBlocks : s10.map((s11) => this._readRasterValue(s11, t6));
    return this._processPixels({ ...t6, pixelBlocks: e8 });
  }
  processInputBandIds() {
    const t6 = this._getRasterValues().filter(this._isRasterFunctionValue);
    let s10;
    if (t6.length > 1) {
      const s11 = t6.map((t7) => t7.processInputBandIds()[0]);
      this.rawInputBandIds = s11, this.isInputBandIdsSwizzled = this.rawInputBandIds.some((t7, s12) => t7 !== s12);
      const e9 = t6.filter((t7) => "ExtractBand" === t7.functionName);
      return e9.length && e9.forEach((t7, s12) => {
        t7.isInputBandIdsSwizzled = true, t7.swizzledBandSelection = [s12, s12, s12];
      }), this.rawInputBandIds;
    }
    const e8 = t6[0];
    if (e8) {
      if (s10 = e8.processInputBandIds(), e8.isInputBandIdsSwizzled) return this.rawInputBandIds = s10, s10;
    } else {
      s10 = [];
      const { bandCount: t7 } = this.sourceRasterInfos[0];
      for (let e9 = 0; e9 < t7; e9++) s10.push(e9);
    }
    const r7 = this._getInputBandIds(s10);
    return this.isInputBandIdsSwizzled = r7.some((t7, s11) => t7 !== s11), this.rawInputBandIds = r7, this.rawInputBandIds;
  }
  getPrimaryRasters() {
    const t6 = [], s10 = [];
    return this._getPrimaryRasters(this, t6, s10), { rasters: t6, rasterIds: s10 };
  }
  getWebGLProcessorDefinition() {
    const t6 = this._getWebGLParameters(), { raster: s10, rasters: e8 } = this.functionArguments;
    return e8 && Array.isArray(e8) && e8.length ? (t6.rasters = e8.map((t7) => this._isRasterFunctionValue(t7) ? t7.getWebGLProcessorDefinition() : "number" == typeof t7 ? { name: "Constant", parameters: { value: t7 }, pixelType: "f32", id: -1, isNoopProcess: false } : { name: "Identity", parameters: { value: t7 }, pixelType: "f32", id: -1, isNoopProcess: false }), t6.rasters.some((t7) => null != t7) || (t6.rasters = null)) : this._isRasterFunctionValue(s10) && (t6.raster = s10.getWebGLProcessorDefinition()), { name: this.functionName, parameters: t6, pixelType: this.outputPixelType, id: this.id, isNoopProcess: this.isNoopProcess };
  }
  getFlatWebGLFunctionChain() {
    const t6 = this.getWebGLProcessorDefinition();
    if (!t6) return null;
    const s10 = [t6], { parameters: e8 } = t6;
    let r7 = e8.rasters || e8.raster && [e8.raster];
    for (; r7 == null ? void 0 : r7.length; ) {
      s10.unshift(...r7);
      const t7 = [];
      for (let s11 = 0; s11 < r7.length; s11++) {
        const { parameters: e9 } = r7[s11], i15 = e9.rasters || e9.raster && [e9.raster];
        (i15 == null ? void 0 : i15.length) && t7.push(...i15);
      }
      r7 = t7;
    }
    for (let n14 = s10.length - 1; n14 >= 0; n14--) s10[n14].isNoopProcess && s10.splice(n14, 1);
    let i14 = false;
    for (let n14 = 0; n14 < s10.length; n14++) {
      const t7 = s10[n14];
      t7.id = s10.length - n14 - 1;
      const { rasters: e9 } = t7.parameters;
      i14 = i14 || null != e9 && e9.length > 1;
    }
    return { hasBranches: i14, functions: s10 };
  }
  _getOutputPixelType(t6) {
    return "unknown" === this.outputPixelType ? t6 : this.outputPixelType ?? t6;
  }
  _getWebGLParameters() {
    return {};
  }
  _getInputBandIds(t6) {
    return t6;
  }
  _isOutputRoundingNeeded() {
    const { outputPixelType: t6 } = this;
    return ((t6 == null ? void 0 : t6.startsWith("u")) || (t6 == null ? void 0 : t6.startsWith("s"))) ?? false;
  }
  _getRasterValues() {
    const { rasterArgumentNames: t6 } = this;
    return "rasters" === t6[0] ? this.functionArguments.rasters ?? [] : t6.map((t7) => this.functionArguments[t7]);
  }
  _getSourceRasterInfos(t6) {
    const s10 = this._getRasterValues(), { rasterInfos: e8, rasterIds: r7 } = t6;
    if (0 === s10.length) return e8;
    const i14 = s10.map((t7) => t7 && "object" == typeof t7 && "bind" in t7 && t7.rasterInfo ? t7.rasterInfo : "string" == typeof t7 && r7.includes(t7) ? e8[r7.indexOf(t7)] : "number" != typeof t7 ? e8[0] : void 0), n14 = i14.find((t7) => t7) ?? e8[0];
    return i14.forEach((t7, s11) => {
      void 0 === t7 && (i14[s11] = n14);
    }), i14;
  }
  _getPrimaryRasterId(t6) {
    return t6 == null ? void 0 : t6.url;
  }
  _getPrimaryRasters(t6, s10 = [], e8 = []) {
    for (let r7 = 0; r7 < t6.sourceRasters.length; r7++) {
      const i14 = t6.sourceRasters[r7];
      if ("number" != typeof i14) if ("bind" in i14) this._getPrimaryRasters(i14, s10, e8);
      else {
        const t7 = i14, r8 = this._getPrimaryRasterId(t7);
        if (null == r8) continue;
        e8.includes(r8) || (this.mainPrimaryRasterId === r8 ? (s10.unshift(t7), e8.unshift(r8)) : (s10.push(t7), e8.push(r8)));
      }
    }
  }
  _isRasterFunctionValue(t6) {
    return null != t6 && "object" == typeof t6 && "getWebGLProcessorDefinition" in t6;
  }
  _readRasterValue(t6, s10) {
    const { primaryPixelBlocks: e8 } = s10;
    if (t(t6) || "$$" === t6) {
      const t7 = e8[0];
      return t(t7) ? null : t7.clone();
    }
    if ("string" == typeof t6) {
      const r7 = s10.primaryRasterIds.indexOf(t6);
      return -1 === r7 ? null : e8[r7];
    }
    if ("number" == typeof t6) {
      const s11 = e8[0];
      if (t(s11)) return null;
      const { width: i14, height: n14, pixelType: o10, mask: u14 } = s11, p21 = u14 ? new Uint8Array(u14) : null, l12 = new Float32Array(i14 * n14);
      l12.fill(t6);
      const d4 = this.sourceRasterInfos[0].bandCount, c21 = new Array(d4).fill(l12);
      return new m3({ width: i14, height: n14, pixelType: o10, pixels: c21, mask: p21 });
    }
    return t6.process(s10);
  }
};
e([y({ json: { write: true } })], p6.prototype, "functionName", void 0), e([y({ json: { write: true } })], p6.prototype, "functionArguments", void 0), e([y()], p6.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } }), s3((t6) => t6 == null ? void 0 : t6.toLowerCase())], p6.prototype, "outputPixelType", void 0), e([y({ json: { write: true } })], p6.prototype, "mainPrimaryRasterId", void 0), e([y()], p6.prototype, "sourceRasters", void 0), e([y({ type: [u], json: { write: true } })], p6.prototype, "sourceRasterInfos", void 0), e([y({ json: { write: true } })], p6.prototype, "rasterInfo", void 0), e([y({ json: { write: true } })], p6.prototype, "readingBufferSize", void 0), e([y({ json: { write: true } })], p6.prototype, "id", void 0), e([y()], p6.prototype, "isNoopProcess", void 0), e([y()], p6.prototype, "supportsGPU", null), e([y()], p6.prototype, "rawInputBandIds", void 0), e([y()], p6.prototype, "isInputBandIdsSwizzled", void 0), e([y()], p6.prototype, "swizzledBandSelection", void 0), e([y()], p6.prototype, "isBranch", void 0), e([y()], p6.prototype, "_bindingResult", void 0), p6 = e([a("esri.layers.support.rasterFunctions.BaseRasterFunction")], p6);
var l2 = p6;

// node_modules/@arcgis/core/layers/support/rasterFunctions/AspectFunction.js
var p7 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Aspect", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.isGCS = false;
  }
  _bindSourceRasters() {
    var _a;
    const t6 = this.sourceRasterInfos[0];
    this.isGCS = ((_a = t6.spatialReference) == null ? void 0 : _a.isGeographic) ?? false, this.outputPixelType = this._getOutputPixelType("f32");
    const s10 = t6.clone();
    return s10.pixelType = this.outputPixelType, s10.statistics = [{ min: 0, max: 360, avg: 180, stddev: 30 }], s10.histograms = null, s10.colormap = null, s10.attributeTable = null, s10.bandCount = 1, this.rasterInfo = s10, { success: true, supportsGPU: true };
  }
  _processPixels(t6) {
    var _a;
    const e8 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(e8)) return null;
    const { extent: r7 } = t6, o10 = r7 ? { x: r7.width / e8.width, y: r7.height / e8.height } : { x: 1, y: 1 };
    return o4(e8, { resolution: o10 });
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], p7.prototype, "functionName", void 0), e([y({ type: c, json: { write: true, name: "rasterFunctionArguments" } })], p7.prototype, "functionArguments", void 0), e([y()], p7.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], p7.prototype, "isGCS", void 0), p7 = e([a("esri.layers.support.rasterFunctions.AspectFunction")], p7);
var c2 = p7;

// node_modules/@arcgis/core/layers/support/rasterFunctions/customBandIndexUtils.js
var e4 = /* @__PURE__ */ new Set(["+", "-", "*", "/", "(", ")"]);
function t3(t6, n14) {
  (t6 = t6.replace(/ /g, "")).startsWith("-") && (t6 = "0" + t6), t6.startsWith("+") && (t6 = t6.slice(1, t6.length));
  const r7 = t6.split(""), l12 = [], o10 = [];
  let s10 = "";
  for (let a21 = 0; a21 < r7.length; a21++) {
    const t7 = r7[a21];
    if (e4.has(t7)) "" !== s10 && o10.push(parseFloat(s10)), l12.push(t7), s10 = "";
    else {
      if ("b" === t7.toLowerCase()) {
        a21++, s10 = t7.concat(r7[a21]), o10.push(n14[parseInt(s10[1], 10) - 1]), s10 = "";
        continue;
      }
      s10 = s10.concat(t7), a21 === r7.length - 1 && o10.push(parseFloat(s10));
    }
  }
  return { ops: l12, nums: o10 };
}
function n2(e8, t6, n14, r7) {
  if ("number" == typeof n14 && "number" == typeof r7) return n14 + r7;
  let l12;
  if ("number" == typeof n14) {
    l12 = r7.length;
    const e9 = n14;
    (n14 = new Float32Array(l12)).fill(e9);
  } else if (l12 = n14.length, r7.constructor === Number) {
    const e9 = r7;
    (r7 = new Float32Array(l12)).fill(e9);
  }
  const o10 = new Float32Array(l12);
  switch (t6) {
    case "+":
      for (let t7 = 0; t7 < l12; t7++) (null == e8 || e8[t7]) && (o10[t7] = n14[t7] + r7[t7]);
      break;
    case "-":
      for (let t7 = 0; t7 < l12; t7++) (null == e8 || e8[t7]) && (o10[t7] = n14[t7] - r7[t7]);
      break;
    case "*":
      for (let t7 = 0; t7 < l12; t7++) (null == e8 || e8[t7]) && (o10[t7] = n14[t7] * r7[t7]);
      break;
    case "/":
      for (let t7 = 0; t7 < l12; t7++) (null == e8 || e8[t7]) && r7[t7] && (o10[t7] = n14[t7] / r7[t7]);
      break;
    case "(":
    case ")":
      throw new Error("encountered error with custom band index equation");
  }
  return o10;
}
function r4(e8, t6) {
  e8.splice(t6, 1);
  let n14 = 0, r7 = 0;
  do {
    n14 = 0, r7 = 0;
    for (let t7 = 0; t7 < e8.length; t7++) if ("(" === e8[t7]) n14 = t7;
    else if (")" === e8[t7]) {
      r7 = t7;
      break;
    }
    r7 === n14 + 1 && e8.splice(n14, 2);
  } while (r7 === n14 + 1);
  return e8;
}
function l3(e8) {
  if (1 === e8.length) return { opIndex: 0, numIndex: 0 };
  let t6 = 0, n14 = 0;
  for (let s10 = 0; s10 < e8.length; s10++) if ("(" === e8[s10]) t6 = s10;
  else if (")" === e8[s10]) {
    n14 = s10;
    break;
  }
  const r7 = 0 === n14 ? e8 : e8.slice(t6 + 1, n14);
  let l12 = -1;
  for (let s10 = 0; s10 < r7.length; s10++) if ("*" === r7[s10] || "/" === r7[s10]) {
    l12 = s10;
    break;
  }
  if (l12 > -1) n14 > 0 && (l12 += t6 + 1);
  else {
    for (let e9 = 0; e9 < r7.length; e9++) if ("+" === r7[e9] || "-" === r7[e9]) {
      l12 = e9;
      break;
    }
    n14 > 0 && (l12 += t6 + 1);
  }
  let o10 = 0;
  for (let s10 = 0; s10 < l12; s10++) "(" === e8[s10] && o10++;
  return { opIndex: l12, numIndex: l12 - o10 };
}
function o5(e8, o10, s10) {
  let a21, { ops: f6, nums: i14 } = t3(s10, o10);
  if (0 === f6.length) {
    const e9 = 1 === i14.length ? i14[0] : o10[0];
    if (e9 instanceof Float32Array) return [e9];
    const t6 = new Float32Array(o10[0].length);
    return "number" == typeof e9 ? t6.fill(e9) : t6.set(e9), [t6];
  }
  for (; f6.length > 0; ) {
    const { numIndex: t6, opIndex: o11 } = l3(f6);
    if (a21 = n2(e8, f6[o11], i14[t6], i14[t6 + 1]), 1 === f6.length) break;
    f6 = r4(f6, o11), i14.splice(t6, 2, a21);
  }
  return [a21];
}

// node_modules/@arcgis/core/layers/support/rasterFunctions/bandIndexUtils.js
var a5 = new s2({ 0: "custom", 1: "ndvi", 2: "savi", 3: "tsavi", 4: "msavi", 5: "gemi", 6: "pvi", 7: "gvitm", 8: "sultan", 9: "vari", 10: "gndvi", 11: "sr", 12: "ndvi-re", 13: "sr-re", 14: "mtvi2", 15: "rtvi-core", 16: "ci-re", 17: "ci-g", 18: "ndwi", 19: "evi", 20: "iron-oxide", 21: "ferrous-minerals", 22: "clay-minerals", 23: "wndwi", 24: "bai", 25: "nbr", 26: "ndbi", 27: "ndmi", 28: "ndsi", 29: "mndwi" }, { useNumericKeys: true });
function s6(n14, a21) {
  if (!a3(n14)) return n14;
  const { equation: s10, method: i14 } = a21, l12 = a21.bandIndexes.map((n15) => n15 - 1), { pixels: j3, mask: q2 } = n14;
  let M4;
  switch (i14) {
    case "gndvi":
    case "nbr":
    case "ndbi":
    case "ndvi":
    case "ndvi-re":
    case "ndsi":
    case "ndmi":
    case "mndwi":
      M4 = c3(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "ndwi":
      M4 = c3(q2, j3[l12[1]], j3[l12[0]]);
      break;
    case "sr":
    case "sr-re":
    case "iron-oxide":
    case "ferrous-minerals":
    case "clay-minerals":
      M4 = u3(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "ci-g":
    case "ci-re":
      M4 = f2(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "savi":
      M4 = w3(q2, j3[l12[0]], j3[l12[1]], l12[2] + 1);
      break;
    case "tsavi":
      M4 = m4(q2, j3[l12[0]], j3[l12[1]], l12[2] + 1, l12[3] + 1, l12[4] + 1);
      break;
    case "msavi":
      M4 = h(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "gemi":
      M4 = d(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "pvi":
      M4 = g2(q2, j3[l12[0]], j3[l12[1]], l12[2] + 1, l12[3] + 1);
      break;
    case "gvitm":
      M4 = y3(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]], j3[l12[3]], j3[l12[4]], j3[l12[5]]]);
      break;
    case "sultan":
      M4 = b(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]], j3[l12[3]], j3[l12[4]], j3[l12[5]]]);
      break;
    case "vari":
      M4 = v(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]]]);
      break;
    case "mtvi2":
      M4 = k3(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]]]);
      break;
    case "rtvi-core":
      M4 = A2(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]]]);
      break;
    case "evi":
      M4 = p8(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]]]);
      break;
    case "wndwi":
      M4 = F(q2, [j3[l12[0]], j3[l12[1]], j3[l12[2]]], l12[3] ? l12[3] + 1 : 0.5);
      break;
    case "bai":
      M4 = x3(q2, j3[l12[0]], j3[l12[1]]);
      break;
    case "custom":
      M4 = o5(q2, j3, s10);
      break;
    default:
      return n14;
  }
  const U2 = r(q2) ? new Uint8Array(q2.length) : null;
  r(q2) && r(U2) && U2.set(q2);
  const B3 = new m3({ width: n14.width, height: n14.height, pixelType: "f32", pixels: M4, mask: U2 });
  return B3.updateStatistics(), B3;
}
function i(n14, t6, r7, o10) {
  const { mask: a21, pixels: s10, width: i14, height: l12 } = n14, c21 = s10[r7], u14 = s10[t6], f6 = u14.length, w6 = o10 ? new Uint8Array(f6) : new Float32Array(f6), m11 = o10 ? 100 : 1, h6 = o10 ? 100.5 : 0;
  for (let e8 = 0; e8 < f6; e8++) if (null == a21 || a21[e8]) {
    const n15 = c21[e8], t7 = u14[e8], r8 = n15 + t7;
    r8 && (w6[e8] = (n15 - t7) / r8 * m11 + h6);
  }
  const d4 = new m3({ width: i14, height: l12, mask: a21, pixelType: o10 ? "u8" : "f32", pixels: [w6] });
  return d4.updateStatistics(), d4;
}
function l4(n14) {
  const t6 = new Float32Array(9);
  return t6[3 * n14[0]] = 1, t6[3 * n14[1] + 1] = 1, t6[3 * n14[2] + 2] = 1, t6;
}
function c3(n14, t6, e8) {
  const r7 = e8.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) {
    const n15 = t6[a21], r8 = e8[a21], s10 = n15 + r8;
    s10 && (o10[a21] = (n15 - r8) / s10);
  }
  return [o10];
}
function u3(n14, t6, e8) {
  const r7 = e8.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) {
    const n15 = t6[a21], r8 = e8[a21];
    r8 && (o10[a21] = n15 / r8);
  }
  return [o10];
}
function f2(n14, t6, e8) {
  const r7 = t6.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) {
    const n15 = t6[a21], r8 = e8[a21];
    r8 && (o10[a21] = n15 / r8 - 1);
  }
  return [o10];
}
function w3(n14, t6, e8, r7) {
  const o10 = e8.length, a21 = new Float32Array(o10);
  for (let s10 = 0; s10 < o10; s10++) if (null == n14 || n14[s10]) {
    const n15 = e8[s10], o11 = t6[s10], i14 = o11 + n15 + r7;
    i14 && (a21[s10] = (o11 - n15) / i14 * (1 + r7));
  }
  return [a21];
}
function m4(n14, t6, e8, r7, o10, a21) {
  const s10 = e8.length, i14 = new Float32Array(s10), l12 = -o10 * r7 + a21 * (1 + r7 * r7);
  for (let c21 = 0; c21 < s10; c21++) if (null == n14 || n14[c21]) {
    const n15 = e8[c21], a22 = t6[c21], s11 = o10 * a22 + n15 + l12;
    s11 && (i14[c21] = r7 * (a22 - r7 * n15 - o10) / s11);
  }
  return [i14];
}
function h(n14, t6, e8) {
  const r7 = e8.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) {
    const n15 = e8[a21], r8 = t6[a21];
    o10[a21] = 0.5 * (2 * (r8 + 1) - Math.sqrt((2 * r8 + 1) ** 2 - 8 * (r8 - n15)));
  }
  return [o10];
}
function d(n14, t6, e8) {
  const r7 = e8.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) {
    const n15 = e8[a21], r8 = t6[a21];
    if (1 !== n15) {
      const t7 = (2 * (r8 * r8 - n15 * n15) + 1.5 * r8 + 0.5 * n15) / (r8 + n15 + 0.5);
      o10[a21] = t7 * (1 - 0.25 * t7) - (n15 - 0.125) / (1 - n15);
    }
  }
  return [o10];
}
function g2(n14, t6, e8, r7, o10) {
  const a21 = e8.length, s10 = new Float32Array(a21), i14 = Math.sqrt(1 + r7 * r7);
  for (let l12 = 0; l12 < a21; l12++) if (null == n14 || n14[l12]) {
    const n15 = e8[l12], a22 = t6[l12];
    s10[l12] = (a22 - r7 * n15 - o10) / i14;
  }
  return [s10];
}
function y3(n14, t6) {
  const [e8, r7, o10, a21, s10, i14] = t6, l12 = e8.length, c21 = new Float32Array(l12);
  for (let u14 = 0; u14 < l12; u14++) (null == n14 || n14[u14]) && (c21[u14] = -0.2848 * e8[u14] - 0.2435 * r7[u14] - 0.5436 * o10[u14] + 0.7243 * a21[u14] + 0.084 * s10[u14] - 1.18 * i14[u14]);
  return [c21];
}
function b(n14, t6) {
  const [e8, , r7, o10, a21, s10] = t6, i14 = e8.length, l12 = new Float32Array(i14), c21 = new Float32Array(i14), u14 = new Float32Array(i14);
  for (let f6 = 0; f6 < i14; f6++) (null == n14 || n14[f6]) && (l12[f6] = s10[f6] ? a21[f6] / s10[f6] * 100 : 0, c21[f6] = e8[f6] ? a21[f6] / e8[f6] * 100 : 0, u14[f6] = o10[f6] ? r7[f6] / o10[f6] * (a21[f6] / o10[f6]) * 100 : 0);
  return [l12, c21, u14];
}
function v(n14, t6) {
  const [e8, r7, o10] = t6, a21 = e8.length, s10 = new Float32Array(a21);
  for (let i14 = 0; i14 < a21; i14++) if (null == n14 || n14[i14]) for (i14 = 0; i14 < a21; i14++) {
    const n15 = e8[i14], t7 = r7[i14], a22 = t7 + n15 - o10[i14];
    a22 && (s10[i14] = (t7 - n15) / a22);
  }
  return [s10];
}
function k3(n14, t6) {
  const [e8, r7, o10] = t6, a21 = e8.length, s10 = new Float32Array(a21);
  for (let i14 = 0; i14 < a21; i14++) if (null == n14 || n14[i14]) for (i14 = 0; i14 < a21; i14++) {
    const n15 = e8[i14], t7 = r7[i14], a22 = o10[i14], l12 = Math.sqrt((2 * n15 + 1) ** 2 - 6 * n15 - 5 * Math.sqrt(t7) - 0.5);
    s10[i14] = 1.5 * (1.2 * (n15 - a22) - 2.5 * (t7 - a22)) * l12;
  }
  return [s10];
}
function A2(n14, t6) {
  const [e8, r7, o10] = t6, a21 = e8.length, s10 = new Float32Array(a21);
  for (let i14 = 0; i14 < a21; i14++) if (null == n14 || n14[i14]) for (i14 = 0; i14 < a21; i14++) {
    const n15 = e8[i14], t7 = r7[i14], a22 = o10[i14];
    s10[i14] = 100 * (n15 - t7) - 10 * (n15 - a22);
  }
  return [s10];
}
function p8(n14, t6) {
  const [e8, r7, o10] = t6, a21 = e8.length, s10 = new Float32Array(a21);
  for (let i14 = 0; i14 < a21; i14++) if (null == n14 || n14[i14]) for (i14 = 0; i14 < a21; i14++) {
    const n15 = e8[i14], t7 = r7[i14], a22 = n15 + 6 * t7 - 7.5 * o10[i14] + 1;
    a22 && (s10[i14] = 2.5 * (n15 - t7) / a22);
  }
  return [s10];
}
function F(n14, t6, e8 = 0.5) {
  const [r7, o10, a21] = t6, s10 = o10.length, i14 = new Float32Array(s10);
  for (let l12 = 0; l12 < s10; l12++) if (null == n14 || n14[l12]) for (l12 = 0; l12 < s10; l12++) {
    const n15 = r7[l12], t7 = o10[l12], s11 = a21[l12], c21 = n15 + e8 * t7 + (1 - e8) * s11;
    c21 && (i14[l12] = (n15 - e8 * t7 - (1 - e8) * s11) / c21);
  }
  return [i14];
}
function x3(n14, t6, e8) {
  const r7 = e8.length, o10 = new Float32Array(r7);
  for (let a21 = 0; a21 < r7; a21++) if (null == n14 || n14[a21]) for (a21 = 0; a21 < r7; a21++) {
    const n15 = (0.1 - t6[a21]) ** 2 + (0.06 - e8[a21]) ** 2;
    n15 && (o10[a21] = 1 / n15);
  }
  return [o10];
}

// node_modules/@arcgis/core/layers/support/rasterFunctions/BandArithmeticFunctionArguments.js
var p9;
var a6 = p9 = class extends p5 {
  constructor() {
    super(...arguments), this.method = "custom";
  }
  clone() {
    return new p9({ method: this.method, bandIndexes: this.bandIndexes, raster: p(this.raster) });
  }
};
e([y({ json: { type: String, write: true } })], a6.prototype, "bandIndexes", void 0), e([o2(a5)], a6.prototype, "method", void 0), a6 = p9 = e([a("esri.layers.support.rasterFunctions.BandArithmeticFunctionArguments")], a6);
var i2 = a6;

// node_modules/@arcgis/core/layers/support/rasterFunctions/BandArithmeticFunction.js
var c4 = /* @__PURE__ */ new Set(["vari", "mtvi2", "rtvi-core", "evi"]);
var u4 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "BandArithmetic", this.functionArguments = null, this.rasterArgumentNames = ["raster"];
  }
  _bindSourceRasters() {
    this.outputPixelType = this._getOutputPixelType("f32");
    const e8 = this.sourceRasterInfos[0], s10 = e8.clone();
    s10.pixelType = this.outputPixelType, s10.statistics = null, s10.histograms = null, s10.bandCount = "sultan" === this.functionArguments.method ? e8.bandCount : 1, this.rasterInfo = s10;
    return { success: true, supportsGPU: !["custom", "gvitm", "sultan"].includes(this.functionArguments.method) };
  }
  _processPixels(e8) {
    var _a;
    const t6 = (_a = e8.pixelBlocks) == null ? void 0 : _a[0];
    if (t(t6)) return t6;
    const { method: r7, bandIndexes: n14 } = this.functionArguments, i14 = n14.split(" ").map((e9) => parseFloat(e9));
    return s6(t6, { method: r7, bandIndexes: i14, equation: n14 });
  }
  _getWebGLParameters() {
    const e8 = this.functionArguments.bandIndexes.split(" ").map((e9) => parseFloat(e9) - 1);
    2 === e8.length && e8.push(0);
    const s10 = this.isInputBandIdsSwizzled ? [0, 1, 2] : e8;
    let t6, r7;
    const n14 = new Float32Array(3), { method: a21 } = this.functionArguments;
    switch (a21) {
      case "gndvi":
      case "nbr":
      case "ndbi":
      case "ndvi":
      case "ndvi-re":
      case "ndsi":
      case "ndmi":
      case "mndwi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "ndxi";
        break;
      case "ndwi":
        t6 = l4([s10[1], s10[0], 0]), r7 = "ndxi";
        break;
      case "sr":
      case "sr-re":
      case "iron-oxide":
      case "ferrous-minerals":
      case "clay-minerals":
        t6 = l4([s10[0], s10[1], 0]), r7 = "sr";
        break;
      case "ci-g":
      case "ci-re":
        t6 = l4([s10[0], s10[1], 0]), r7 = "ci";
        break;
      case "savi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "savi", n14[0] = e8[2] + 1;
        break;
      case "tsavi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "tsavi", n14[0] = e8[2] + 1, n14[1] = e8[3] + 1, n14[2] = e8[4] + 1;
        break;
      case "msavi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "msavi";
        break;
      case "gemi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "gemi";
        break;
      case "pvi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "tsavi", n14[0] = e8[2] + 1, n14[1] = e8[3] + 1;
        break;
      case "vari":
        t6 = l4([s10[0], s10[1], s10[2]]), r7 = "vari";
        break;
      case "mtvi2":
        t6 = l4([s10[0], s10[1], s10[2]]), r7 = "mtvi2";
        break;
      case "rtvi-core":
        t6 = l4([s10[0], s10[1], s10[2]]), r7 = "rtvicore";
        break;
      case "evi":
        t6 = l4([s10[0], s10[1], s10[2]]), r7 = "evi";
        break;
      case "wndwi":
        t6 = l4([s10[0], s10[1], 0]), r7 = "wndwi", n14[0] = e8[3] ? e8[3] + 1 : 0.5;
        break;
      case "bai":
        t6 = l4([s10[1], s10[0], 0]), r7 = "bai";
        break;
      default:
        t6 = l4([0, 1, 2]), r7 = "custom";
    }
    return { bandIndexMat3: t6, indexType: r7, adjustments: n14 };
  }
  _getInputBandIds(e8) {
    if ("custom" === this.functionArguments.method) return e8;
    const s10 = this.functionArguments.bandIndexes.split(" ").map((e9) => parseFloat(e9) - 1), t6 = e8.length, r7 = s10.map((e9) => e9 >= t6 ? t6 - 1 : e9), n14 = c4.has(this.functionArguments.method) ? 3 : 2, a21 = r7.slice(0, n14).map((s11) => e8[s11]);
    return 2 === a21.length && a21.push(0), a21;
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], u4.prototype, "functionName", void 0), e([y({ type: i2, json: { write: true, name: "rasterFunctionArguments" } })], u4.prototype, "functionArguments", void 0), e([y()], u4.prototype, "rasterArgumentNames", void 0), u4 = e([a("esri.layers.support.rasterFunctions.BandArithmeticFunction")], u4);
var m5 = u4;

// node_modules/@arcgis/core/layers/support/rasterFunctions/ColormapFunctionArguments.js
var i3;
var u5 = i3 = class extends p5 {
  castColormapName(o10) {
    if (!o10) return null;
    const r7 = o10.toLowerCase();
    return n.includes(r7) ? r7 : null;
  }
  readColorRamp(o10) {
    return p2(o10);
  }
  readColorRampName(o10, r7) {
    if (!o10) return null;
    const e8 = a2.jsonValues.find((r8) => r8.toLowerCase() === o10.toLowerCase());
    return e8 ? a2.fromJSON(e8) : null;
  }
  clone() {
    var _a;
    return new i3({ colormap: p(this.colormap), colormapName: this.colormapName, colorRamp: (_a = this.colorRamp) == null ? void 0 : _a.clone(), colorRampName: this.colorRampName });
  }
};
e([y({ type: [[Number]], json: { write: true } })], u5.prototype, "colormap", void 0), e([y({ type: String, json: { write: true } })], u5.prototype, "colormapName", void 0), e([s3("colormapName")], u5.prototype, "castColormapName", null), e([y({ types: m, json: { write: true } })], u5.prototype, "colorRamp", void 0), e([o("colorRamp")], u5.prototype, "readColorRamp", null), e([y({ type: a2.apiValues, json: { type: a2.jsonValues, write: a2.write } })], u5.prototype, "colorRampName", void 0), e([o("colorRampName")], u5.prototype, "readColorRampName", null), u5 = i3 = e([a("esri.layers.support.rasterFunctions.ColormapFunctionArguments")], u5);
var d2 = u5;

// node_modules/@arcgis/core/layers/support/rasterFunctions/colormaps.js
var t4 = [[36, 0, 255], [36, 0, 255], [36, 0, 255], [36, 0, 255], [112, 75, 3], [113, 76, 3], [114, 77, 3], [115, 77, 3], [116, 78, 3], [117, 79, 3], [118, 79, 3], [119, 80, 3], [121, 81, 4], [122, 82, 4], [123, 82, 4], [124, 83, 4], [125, 84, 4], [126, 84, 4], [127, 85, 4], [128, 86, 4], [129, 86, 4], [130, 87, 4], [131, 88, 4], [132, 89, 4], [133, 89, 4], [134, 90, 4], [135, 91, 4], [136, 91, 4], [137, 92, 4], [138, 93, 4], [139, 94, 4], [140, 94, 4], [142, 95, 5], [143, 96, 5], [144, 96, 5], [145, 97, 5], [146, 98, 5], [147, 99, 5], [148, 99, 5], [149, 100, 5], [150, 101, 5], [151, 101, 5], [152, 102, 5], [153, 103, 5], [154, 104, 5], [155, 104, 5], [156, 105, 5], [157, 106, 5], [158, 106, 5], [159, 107, 5], [160, 108, 5], [161, 108, 5], [162, 109, 5], [164, 110, 6], [165, 111, 6], [166, 111, 6], [167, 112, 6], [168, 113, 6], [169, 113, 6], [170, 114, 6], [171, 115, 6], [172, 116, 6], [173, 116, 6], [174, 117, 6], [245, 0, 0], [245, 5, 0], [245, 10, 0], [246, 15, 0], [246, 20, 0], [246, 25, 0], [246, 30, 0], [247, 35, 0], [247, 40, 0], [247, 45, 0], [247, 50, 0], [247, 55, 0], [248, 60, 0], [248, 65, 0], [248, 70, 0], [248, 75, 0], [249, 81, 0], [249, 86, 0], [249, 91, 0], [249, 96, 0], [250, 101, 0], [250, 106, 0], [250, 111, 0], [250, 116, 0], [250, 121, 0], [251, 126, 0], [251, 131, 0], [251, 136, 0], [251, 141, 0], [252, 146, 0], [252, 151, 0], [252, 156, 0], [252, 156, 0], [251, 159, 0], [250, 162, 0], [249, 165, 0], [248, 168, 0], [247, 171, 0], [246, 174, 0], [245, 177, 0], [245, 179, 0], [244, 182, 0], [243, 185, 0], [242, 188, 0], [241, 191, 0], [240, 194, 0], [239, 197, 0], [238, 200, 0], [237, 203, 0], [236, 206, 0], [235, 209, 0], [234, 212, 0], [233, 215, 0], [232, 218, 0], [231, 221, 0], [230, 224, 0], [230, 226, 0], [229, 229, 0], [228, 232, 0], [227, 235, 0], [226, 238, 0], [225, 241, 0], [224, 244, 0], [223, 247, 0], [165, 247, 0], [163, 244, 0], [161, 240, 0], [158, 237, 0], [156, 233, 1], [154, 230, 1], [152, 227, 1], [149, 223, 1], [147, 220, 1], [145, 216, 1], [143, 213, 1], [140, 210, 2], [138, 206, 2], [136, 203, 2], [134, 200, 2], [132, 196, 2], [129, 193, 2], [127, 189, 2], [125, 186, 3], [123, 183, 3], [120, 179, 3], [118, 176, 3], [116, 172, 3], [114, 169, 3], [111, 166, 3], [109, 162, 4], [107, 159, 4], [105, 155, 4], [103, 152, 4], [100, 149, 4], [98, 145, 4], [96, 142, 4], [94, 138, 5], [91, 135, 5], [89, 132, 5], [87, 128, 5], [85, 125, 5], [82, 121, 5], [80, 118, 5], [78, 115, 6], [76, 111, 6], [73, 108, 6], [71, 105, 6], [69, 101, 6], [67, 98, 6], [65, 94, 6], [62, 91, 7], [60, 88, 7], [58, 84, 7], [56, 81, 7], [53, 77, 7], [51, 74, 7], [49, 71, 7], [47, 67, 8], [44, 64, 8], [42, 60, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8], [40, 57, 8]];
var o6 = [[36, 0, 255], [36, 0, 255], [36, 0, 255], [36, 0, 255], [245, 20, 0], [245, 24, 0], [245, 29, 0], [245, 31, 0], [247, 33, 0], [247, 33, 0], [247, 37, 0], [247, 41, 0], [247, 41, 0], [247, 41, 0], [247, 45, 0], [247, 45, 0], [247, 47, 0], [247, 49, 0], [247, 49, 0], [247, 54, 0], [247, 54, 0], [247, 56, 0], [247, 58, 0], [247, 58, 0], [250, 62, 0], [250, 62, 0], [250, 62, 0], [250, 67, 0], [250, 67, 0], [250, 67, 0], [250, 69, 0], [250, 71, 0], [250, 71, 0], [250, 75, 0], [250, 75, 0], [250, 78, 0], [250, 79, 0], [250, 79, 0], [250, 79, 0], [250, 81, 0], [250, 83, 0], [250, 83, 0], [250, 87, 0], [250, 87, 0], [250, 90, 0], [250, 92, 0], [252, 93, 0], [252, 93, 0], [252, 97, 0], [252, 97, 0], [252, 97, 0], [252, 97, 0], [252, 101, 0], [252, 101, 0], [252, 101, 0], [252, 101, 0], [252, 105, 0], [252, 105, 0], [252, 107, 0], [252, 109, 0], [252, 109, 0], [252, 113, 13], [255, 118, 20], [255, 119, 23], [255, 121, 25], [255, 126, 33], [255, 132, 38], [255, 133, 40], [255, 135, 43], [255, 141, 48], [255, 144, 54], [255, 150, 59], [255, 152, 61], [255, 153, 64], [255, 159, 69], [255, 163, 77], [255, 165, 79], [255, 168, 82], [255, 174, 87], [255, 176, 92], [255, 181, 97], [255, 183, 99], [255, 186, 102], [255, 191, 107], [255, 197, 115], [255, 201, 120], [255, 203, 123], [255, 205, 125], [255, 209, 130], [255, 214, 138], [255, 216, 141], [255, 218, 143], [255, 224, 150], [255, 228, 156], [255, 234, 163], [255, 236, 165], [255, 238, 168], [255, 243, 173], [255, 248, 181], [255, 252, 186], [253, 252, 186], [250, 252, 187], [244, 250, 180], [238, 247, 176], [234, 246, 173], [231, 245, 169], [223, 240, 163], [217, 237, 157], [211, 235, 150], [205, 233, 146], [200, 230, 142], [195, 227, 136], [189, 224, 132], [184, 222, 126], [180, 220, 123], [174, 217, 119], [169, 214, 114], [163, 212, 108], [160, 210, 105], [154, 207, 101], [148, 204, 96], [143, 201, 93], [138, 199, 88], [134, 197, 84], [130, 194, 81], [126, 191, 77], [117, 189, 70], [115, 186, 68], [112, 184, 64], [106, 181, 60], [100, 179, 55], [94, 176, 49], [92, 174, 47], [90, 173, 45], [81, 168, 37], [75, 166, 33], [71, 163, 28], [66, 160, 24], [62, 158, 21], [56, 156, 14], [51, 153, 0], [51, 153, 0], [51, 153, 0], [50, 150, 0], [50, 150, 0], [50, 150, 0], [50, 150, 0], [49, 148, 0], [49, 148, 0], [49, 148, 0], [48, 145, 0], [48, 145, 0], [48, 145, 0], [48, 145, 0], [48, 143, 0], [48, 143, 0], [48, 143, 0], [48, 143, 0], [47, 140, 0], [47, 140, 0], [47, 140, 0], [47, 140, 0], [46, 138, 0], [46, 138, 0], [46, 138, 0], [46, 138, 0], [45, 135, 0], [45, 135, 0], [45, 135, 0], [45, 135, 0], [44, 133, 0], [44, 133, 0], [44, 133, 0], [43, 130, 0], [43, 130, 0], [43, 130, 0], [43, 130, 0], [43, 130, 0], [43, 130, 0], [42, 128, 0], [42, 128, 0], [42, 128, 0], [42, 125, 0], [42, 125, 0], [42, 125, 0], [42, 125, 0], [41, 122, 0], [41, 122, 0], [41, 122, 0], [41, 122, 0], [40, 120, 0], [40, 120, 0], [40, 120, 0], [40, 120, 0], [40, 120, 0], [39, 117, 0], [39, 117, 0], [39, 117, 0], [39, 117, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0], [38, 115, 0]];
function e5(t6, o10) {
  const e8 = [], n14 = [];
  for (let r7 = 0; r7 < t6.length - 1; r7++) e8.push({ type: "algorithmic", algorithm: "esriHSVAlgorithm", fromColor: t6[r7].slice(1), toColor: t6[r7 + 1].slice(1) }), n14.push(t6[r7 + 1][0] - t6[r7][0]);
  const s10 = t6[t6.length - 1][0];
  return k({ type: "multipart", colorRamps: e8 }, { numColors: s10, weights: o10 = o10 ?? n14 });
}
function n3() {
  return e5([[0, 0, 191, 191], [51, 0, 255, 0], [102, 255, 255, 0], [153, 255, 127, 0], [204, 191, 127, 63], [256, 20, 20, 20]]);
}
function s7() {
  const r7 = e5([[0, 255, 255, 255], [70, 0, 0, 255], [80, 205, 193, 173], [100, 150, 150, 150], [110, 120, 100, 51], [130, 120, 200, 100], [140, 28, 144, 3], [160, 6, 55, 0], [180, 10, 30, 25], [201, 6, 27, 7]]);
  for (let t6 = r7.length; t6 < 256; t6++) r7.push([6, 27, 7]);
  return r7;
}
function l5() {
  return k({ type: "algorithmic", algorithm: "esriHSVAlgorithm", fromColor: [0, 0, 0], toColor: [255, 255, 255] });
}
function i4() {
  const r7 = [];
  for (let t6 = 0; t6 < 256; t6++) {
    const t7 = [];
    for (let r8 = 0; r8 < 3; r8++) t7.push(Math.round(255 * Math.random()));
    r7.push(t7);
  }
  return r7;
}
function a7() {
  return e5([[0, 38, 54, 41], [69, 79, 90, 82], [131, 156, 156, 156], [256, 253, 241, 253]], [0.268, 0.238, 0.495]);
}
function c5(r7) {
  let e8;
  switch (r7) {
    case "elevation":
      e8 = n3();
      break;
    case "gray":
      e8 = l5();
      break;
    case "hillshade":
      e8 = a7();
      break;
    case "ndvi":
      e8 = t4;
      break;
    case "ndvi2":
      e8 = s7();
      break;
    case "ndvi3":
      e8 = o6;
      break;
    case "random":
      e8 = i4();
  }
  return e8 ? (e8 = e8.map((r8, t6) => [t6, ...r8]), e8) : null;
}

// node_modules/@arcgis/core/layers/support/rasterFunctions/ColormapFunction.js
var c6 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Colormap", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.isNoopProcess = true;
  }
  _bindSourceRasters() {
    const o10 = this.sourceRasterInfos[0];
    if (o10.bandCount > 1) return { success: false, supportsGPU: false, error: "colormap-function: source data must be single band" };
    let { colormap: r7, colormapName: s10, colorRamp: t6, colorRampName: e8 } = this.functionArguments;
    if (!(r7 == null ? void 0 : r7.length)) if (t6) r7 = _(t6, { interpolateAlpha: true });
    else if (e8) {
      const o11 = m2(e8);
      o11 && (r7 = _(o11));
    } else s10 && (r7 = c5(s10));
    if (!(r7 == null ? void 0 : r7.length)) return { success: false, supportsGPU: false, error: "colormap-function: missing colormap argument" };
    this.outputPixelType = this._getOutputPixelType("u8");
    const c21 = o10.clone();
    return c21.pixelType = this.outputPixelType, c21.colormap = r7, c21.bandCount = 1, this.rasterInfo = c21, { success: true, supportsGPU: true };
  }
  _processPixels(o10) {
    var _a;
    return (_a = o10.pixelBlocks) == null ? void 0 : _a[0];
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], c6.prototype, "functionName", void 0), e([y({ type: d2, json: { write: true, name: "rasterFunctionArguments" } })], c6.prototype, "functionArguments", void 0), e([y()], c6.prototype, "rasterArgumentNames", void 0), e([y()], c6.prototype, "isNoopProcess", void 0), e([y({ json: { write: true } })], c6.prototype, "indexedColormap", void 0), c6 = e([a("esri.layers.support.rasterFunctions.ColormapFunction")], c6);
var u6 = c6;

// node_modules/@arcgis/core/layers/support/rasterFunctions/CompositeBandFunctionArguments.js
var a8;
var c7 = a8 = class extends p5 {
  constructor() {
    super(...arguments), this.rasters = [];
  }
  writeRasters(r7, s10) {
    s10.rasters = r7.map((r8) => "number" == typeof r8 || "string" == typeof r8 ? r8 : r8.toJSON());
  }
  clone() {
    return new a8({ rasters: p(this.rasters) });
  }
};
e([y({ json: { write: true } })], c7.prototype, "rasters", void 0), e([r2("rasters")], c7.prototype, "writeRasters", null), c7 = a8 = e([a("esri.layers.support.rasterFunctions.CompositeBandFunctionArguments")], c7);
var n4 = c7;

// node_modules/@arcgis/core/layers/support/rasterFunctions/CompositeBandFunction.js
var u7 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "CompositeBand", this.functionArguments = null, this.rasterArgumentNames = ["rasters"];
  }
  _bindSourceRasters() {
    const { sourceRasterInfos: t6 } = this, r7 = t6[0];
    this.outputPixelType = this._getOutputPixelType(r7.pixelType);
    const e8 = r7.clone();
    if (e8.attributeTable = null, e8.colormap = null, e8.pixelType = this.outputPixelType, e8.bandCount = t6.map(({ bandCount: t7 }) => t7).reduce((t7, s10) => t7 + s10), t6.every(({ statistics: t7 }) => r(t7) && t7.length)) {
      const r8 = [];
      t6.forEach(({ statistics: t7 }) => r(t7) && r8.push(...t7)), e8.statistics = r8;
    }
    if (t6.every(({ histograms: t7 }) => r(t7) && t7.length)) {
      const r8 = [];
      t6.forEach(({ histograms: t7 }) => r(t7) && r8.push(...t7)), e8.histograms = r8;
    }
    e8.bandCount > 1 && (e8.colormap = null, e8.attributeTable = null), this.rasterInfo = e8;
    return { success: true, supportsGPU: e8.bandCount <= 3 };
  }
  _processPixels(t6) {
    const { pixelBlocks: s10 } = t6;
    if (!s10) return null;
    const e8 = s10 == null ? void 0 : s10[0];
    return t(e8) ? null : f(s10);
  }
  _getWebGLParameters() {
    return { bandCount: this.rasterInfo.bandCount };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], u7.prototype, "functionName", void 0), e([y({ type: n4, json: { write: true, name: "rasterFunctionArguments" } })], u7.prototype, "functionArguments", void 0), e([y()], u7.prototype, "rasterArgumentNames", void 0), u7 = e([a("esri.layers.support.rasterFunctions.CompositeBandFunction")], u7);
var p10 = u7;

// node_modules/@arcgis/core/layers/support/rasterFunctionConstants.js
var a9 = { userDefined: -1, lineDetectionHorizontal: 0, lineDetectionVertical: 1, lineDetectionLeftDiagonal: 2, lineDetectionRightDiagonal: 3, gradientNorth: 4, gradientWest: 5, gradientEast: 6, gradientSouth: 7, gradientNorthEast: 8, gradientNorthWest: 9, smoothArithmeticMean: 10, smoothing3x3: 11, smoothing5x5: 12, sharpening3x3: 13, sharpening5x5: 14, laplacian3x3: 15, laplacian5x5: 16, sobelHorizontal: 17, sobelVertical: 18, sharpen: 19, sharpen2: 20, pointSpread: 21, none: 255 };
var g3 = { plus: 1, minus: 2, times: 3, sqrt: 4, power: 5, abs: 10, divide: 23, exp: 25, exp10: 26, exp2: 27, int: 30, float: 32, ln: 35, log10: 36, log2: 37, mod: 44, negate: 45, roundDown: 48, roundUp: 49, square: 53, floatDivide: 64, floorDivide: 65 };
var D2 = { bitwiseAnd: 11, bitwiseLeftShift: 12, bitwiseNot: 13, bitwiseOr: 14, bitwiseRightShift: 15, bitwiseXOr: 16, booleanAnd: 17, booleanNot: 18, booleanOr: 19, booleanXOr: 20, equalTo: 24, greaterThan: 28, greaterThanEqual: 29, lessThan: 33, lessThanEqual: 34, isNull: 31, notEqual: 46 };
var h2 = { acos: 6, asin: 7, atan: 8, atanh: 9, cos: 21, cosh: 22, sin: 51, sinh: 52, tan: 56, tanh: 57, acosh: 59, asinh: 60, atan2: 61 };
var u8 = { majority: 38, max: 39, mean: 40, med: 41, min: 42, minority: 43, range: 47, stddev: 54, sum: 55, variety: 58, majorityIgnoreNoData: 66, maxIgnoreNoData: 67, meanIgnoreNoData: 68, medIgnoreNoData: 69, minIgnoreNoData: 70, minorityIgnoreNoData: 71, rangeIgnoreNoData: 72, stddevIgnoreNoData: 73, sumIgnoreNoData: 74, varietyIgnoreNoData: 75 };
var s8 = { setNull: 50, conditional: 78 };
var p11 = { ...g3, ...D2, ...h2, ...u8, ...s8 };

// node_modules/@arcgis/core/layers/support/rasterFunctions/convolutionUtils.js
var n5 = /* @__PURE__ */ new Map();
function o7(t6) {
  const e8 = Math.sqrt(t6.length), n14 = t6.slice(0, e8), o10 = [1];
  for (let l12 = 1; l12 < e8; l12++) {
    let n15 = null;
    for (let o11 = 0; o11 < e8; o11++) {
      const r7 = t6[o11 + l12 * e8], s10 = t6[o11];
      if (null == n15) if (0 === s10) {
        if (r7) return { separable: false, row: null, col: null };
      } else n15 = r7 / s10;
      else if (r7 / s10 !== n15) return { separable: false, row: null, col: null };
    }
    if (null == n15) return { separable: false, row: null, col: null };
    o10.push(n15);
  }
  return { separable: true, row: n14, col: o10 };
}
function l6(t6, e8, n14, o10, l12, r7, s10) {
  const i14 = new Float32Array(e8 * n14), a21 = r7.length, c21 = s10 ? 0 : o10, h6 = s10 ? o10 : 0, f6 = s10 ? 1 : e8;
  for (let p21 = c21; p21 < n14 - c21; p21++) {
    const n15 = p21 * e8;
    for (let s11 = h6; s11 < e8 - h6; s11++) {
      if (l12 && !l12[n15 + s11]) continue;
      let e9 = 0;
      for (let l13 = 0; l13 < a21; l13++) e9 += t6[n15 + s11 + (l13 - o10) * f6] * r7[l13];
      i14[n15 + s11] = e9;
    }
  }
  return i14;
}
function r5(t6, e8, n14, o10, l12, r7, s10) {
  const i14 = new Float32Array(e8 * n14), a21 = Math.floor(o10 / 2), c21 = Math.floor(l12 / 2);
  for (let h6 = a21; h6 < n14 - a21; h6++) {
    const n15 = h6 * e8;
    for (let h7 = c21; h7 < e8 - c21; h7++) {
      if (r7 && !r7[n15 + h7]) continue;
      let f6 = 0;
      for (let r8 = 0; r8 < o10; r8++) for (let o11 = 0; o11 < l12; o11++) f6 += t6[n15 + h7 + (r8 - a21) * e8 + o11 - c21] * s10[r8 * l12 + o11];
      i14[n15 + h7] = f6;
    }
  }
  return i14;
}
function s9(e8, n14, o10 = true) {
  const { pixels: l12, width: s10, height: a21, pixelType: c21, mask: h6 } = e8, f6 = l12.length, p21 = [], { kernel: u14, rows: g5, cols: x6 } = n14;
  for (let t6 = 0; t6 < f6; t6++) {
    const e9 = r5(l12[t6], s10, a21, g5, x6, h6, u14);
    o10 && i5(e9, s10, a21, g5, x6), p21.push(e9);
  }
  return new m3({ width: s10, height: a21, pixelType: c21, pixels: p21, mask: h6 });
}
function i5(t6, e8, n14, o10, l12) {
  const r7 = Math.floor(o10 / 2);
  for (let i14 = 0; i14 < r7; i14++) for (let o11 = 0; o11 < e8; o11++) t6[i14 * e8 + o11] = t6[(l12 - 1 - i14) * e8 + o11], t6[(n14 - 1 - i14) * e8 + o11] = t6[(n14 - l12 + i14) * e8 + o11];
  const s10 = Math.floor(l12 / 2);
  for (let i14 = 0; i14 < n14; i14++) {
    const n15 = i14 * e8;
    for (let o11 = 0; o11 < s10; o11++) t6[n15 + o11] = t6[n15 + l12 - 1 - o11], t6[n15 + e8 - o11 - 1] = t6[n15 + e8 + o11 - l12];
  }
}
function a10(e8, n14, o10, r7 = true) {
  const { pixels: s10, width: a21, height: c21, pixelType: h6, mask: f6 } = e8, p21 = s10.length, u14 = [], g5 = n14.length, x6 = o10.length, w6 = Math.floor(g5 / 2), m11 = Math.floor(x6 / 2);
  for (let t6 = 0; t6 < p21; t6++) {
    let e9 = l6(s10[t6], a21, c21, w6, f6, n14, true);
    e9 = l6(e9, a21, c21, m11, f6, o10, false), r7 && i5(e9, a21, c21, g5, x6), u14.push(e9);
  }
  return new m3({ width: a21, height: c21, pixelType: h6, pixels: u14, mask: f6 });
}
function c8(t6, e8) {
  const n14 = o7(e8.kernel), l12 = false !== e8.mirrorEdges, r7 = n14.separable ? a10(t6, n14.row, n14.col, l12) : s9(t6, e8, l12), { outputPixelType: i14 } = e8;
  return i14 && r7.clamp(i14), r7;
}
n5.set(a9.none, [0, 0, 0, 0, 1, 0, 0, 0, 0]), n5.set(a9.lineDetectionHorizontal, [-1, -1, -1, 2, 2, 2, -1, -1, -1]), n5.set(a9.lineDetectionVertical, [-1, 2, -1, -1, 2, -1, -1, 2, -1]), n5.set(a9.lineDetectionLeftDiagonal, [2, -1, -1, -1, 2, -1, -1, -1, 2]), n5.set(a9.lineDetectionRightDiagonal, [-1, -1, 2, -1, 2, -1, 2, -1, -1]), n5.set(a9.gradientNorth, [-1, -2, -1, 0, 0, 0, 1, 2, 1]), n5.set(a9.gradientWest, [-1, 0, 1, -2, 0, 2, -1, 0, 1]), n5.set(a9.gradientEast, [1, 0, -1, 2, 0, -2, 1, 0, -1]), n5.set(a9.gradientSouth, [1, 2, 1, 0, 0, 0, -1, -2, -1]), n5.set(a9.gradientNorthEast, [0, -1, -2, 1, 0, -1, 2, 1, 0]), n5.set(a9.gradientNorthWest, [-2, -1, 0, -1, 0, 1, 0, 1, 2]), n5.set(a9.smoothArithmeticMean, [0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111, 0.111111111111]), n5.set(a9.smoothing3x3, [0.0625, 0.125, 0.0625, 0.125, 0.25, 0.125, 0.0625, 0.125, 0.0625]), n5.set(a9.smoothing5x5, [1, 1, 1, 1, 1, 1, 4, 4, 4, 1, 1, 4, 12, 4, 1, 1, 4, 4, 4, 1, 1, 1, 1, 1, 1]), n5.set(a9.sharpening3x3, [-1, -1, -1, -1, 9, -1, -1, -1, -1]), n5.set(a9.sharpening5x5, [-1, -3, -4, -3, -1, -3, 0, 6, 0, -3, -4, 6, 21, 6, -4, -3, 0, 6, 0, -3, -1, -3, -4, -3, -1]), n5.set(a9.laplacian3x3, [0, -1, 0, -1, 4, -1, 0, -1, 0]), n5.set(a9.laplacian5x5, [0, 0, -1, 0, 0, 0, -1, -2, -1, 0, -1, -2, 17, -2, -1, 0, -1, -2, -1, 0, 0, 0, -1, 0, 0]), n5.set(a9.sobelHorizontal, [-1, -2, -1, 0, 0, 0, 1, 2, 1]), n5.set(a9.sobelVertical, [-1, 0, 1, -2, 0, 2, -1, 0, 1]), n5.set(a9.sharpen, [0, -0.25, 0, -0.25, 2, -0.25, 0, -0.25, 0]), n5.set(a9.sharpen2, [-0.25, -0.25, -0.25, -0.25, 3, -0.25, -0.25, -0.25, -0.25]), n5.set(a9.pointSpread, [-0.627, 0.352, -0.627, 0.352, 2.923, 0.352, -0.627, 0.352, -0.627]);

// node_modules/@arcgis/core/layers/support/rasterFunctions/ConvolutionFunctionArguments.js
var p12;
var c9 = p12 = class extends p5 {
  constructor() {
    super(...arguments), this.rows = 3, this.cols = 3, this.kernel = [0, 0, 0, 0, 1, 0, 0, 0, 0];
  }
  set convolutionType(o10) {
    this._set("convolutionType", o10);
    const t6 = n5.get(o10);
    if (!t6 || o10 === a9.userDefined || o10 === a9.none) return;
    const s10 = Math.sqrt(t6.length);
    this._set("kernel", t6), this._set("cols", s10), this._set("rows", s10);
  }
  clone() {
    return new p12({ cols: this.cols, rows: this.rows, kernel: [...this.kernel], convolutionType: this.convolutionType, raster: p(this.raster) });
  }
};
e([y({ json: { type: Number, write: true } })], c9.prototype, "rows", void 0), e([y({ json: { type: Number, write: true } })], c9.prototype, "cols", void 0), e([y({ json: { name: "type", type: Number, write: true } })], c9.prototype, "convolutionType", null), e([y({ json: { type: [Number], write: true } })], c9.prototype, "kernel", void 0), c9 = p12 = e([a("esri.layers.support.rasterFunctions.ConvolutionFunctionArguments")], c9);
var u9 = c9;

// node_modules/@arcgis/core/layers/support/rasterFunctions/ConvolutionFunction.js
var p13 = 25;
var l7 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Convolution", this.rasterArgumentNames = ["raster"];
  }
  _bindSourceRasters() {
    const { convolutionType: t6, rows: e8, cols: o10, kernel: s10 } = this.functionArguments;
    if (!Object.values(a9).includes(t6)) return { success: false, supportsGPU: false, error: `convolution-function: the specified kernel type is not supported ${t6}` };
    if (t6 !== a9.none && e8 * o10 !== s10.length) return { success: false, supportsGPU: false, error: "convolution-function: the specified rows and cols do not match the length of the kernel" };
    const n14 = this.sourceRasterInfos[0];
    this.outputPixelType = this._getOutputPixelType(n14.pixelType);
    const i14 = n14.clone();
    i14.pixelType = this.outputPixelType;
    const u14 = [a9.none, a9.sharpen, a9.sharpen2, a9.sharpening3x3, a9.sharpening5x5];
    "u8" === this.outputPixelType || u14.includes(t6) || (i14.statistics = null, i14.histograms = null), i14.colormap = null, i14.attributeTable = null, this.rasterInfo = i14;
    return { success: true, supportsGPU: s10.length <= p13 };
  }
  _processPixels(t6) {
    var _a;
    const o10 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(o10) || this.functionArguments.convolutionType === a9.none) return o10;
    let { kernel: s10, rows: n14, cols: i14 } = this.functionArguments;
    const u14 = s10.reduce((t7, e8) => t7 + e8);
    return 0 !== u14 && 1 !== u14 && (s10 = s10.map((t7) => t7 / u14)), c8(o10, { kernel: s10, rows: n14, cols: i14, outputPixelType: this.outputPixelType });
  }
  _getWebGLParameters() {
    let { kernel: t6 } = this.functionArguments;
    const e8 = t6.reduce((t7, e9) => t7 + e9);
    0 !== e8 && 1 !== e8 && (t6 = t6.map((t7) => t7 / e8));
    const o10 = new Float32Array(p13);
    return o10.set(t6), { kernelRows: this.functionArguments.rows, kernelCols: this.functionArguments.cols, kernel: o10, clampRange: s4(this.outputPixelType) };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], l7.prototype, "functionName", void 0), e([y({ type: u9, json: { write: true, name: "rasterFunctionArguments" } })], l7.prototype, "functionArguments", void 0), e([y()], l7.prototype, "rasterArgumentNames", void 0), l7 = e([a("esri.layers.support.rasterFunctions.ConvolutionFunction")], l7);
var a11 = l7;

// node_modules/@arcgis/core/layers/support/rasterFunctions/ExtractBandFunctionArguments.js
var i6;
var n6 = i6 = class extends p5 {
  constructor() {
    super(...arguments), this.bandIds = [], this.missingBandAction = s5.bestMatch;
  }
  clone() {
    return new i6({ bandIds: [...this.bandIds], missingBandAction: this.missingBandAction });
  }
};
e([y({ json: { write: true } })], n6.prototype, "bandIds", void 0), e([y({ json: { write: true } })], n6.prototype, "missingBandAction", void 0), n6 = i6 = e([a("esri.layers.support.rasterFunctions.ExtractBandFunctionArguments")], n6);
var c10 = n6;

// node_modules/@arcgis/core/layers/support/rasterFunctions/ExtractBandFunction.js
var c11 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "ExtractBand", this.functionArguments = null, this.rasterArgumentNames = ["raster"];
  }
  _bindSourceRasters() {
    const { sourceRasterInfos: t6 } = this, e8 = t6[0], { bandCount: n14 } = e8, { bandIds: r7, missingBandAction: o10 } = this.functionArguments;
    if (o10 === s5.fail && r7.some((t7) => t7 < 0 || t7 >= n14)) return { success: false, supportsGPU: false, error: "extract-band-function: invalid bandIds" };
    this.outputPixelType = this._getOutputPixelType("f32");
    const i14 = e8.clone();
    i14.pixelType = this.outputPixelType, i14.bandCount = r7.length;
    const { statistics: a21, histograms: c21 } = i14;
    r(a21) && a21.length && (i14.statistics = r7.map((t7) => a21[t7] || a21[a21.length - 1])), r(c21) && c21.length && (i14.histograms = r7.map((t7) => c21[t7] || c21[c21.length - 1])), this.rasterInfo = i14;
    return { success: true, supportsGPU: i14.bandCount <= 3 };
  }
  _processPixels(t6) {
    var _a;
    const s10 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(s10)) return null;
    const n14 = s10.pixels.length, r7 = this.functionArguments.bandIds.map((t7) => t7 >= n14 ? n14 - 1 : t7);
    return s10.extractBands(r7);
  }
  _getWebGLParameters() {
    let t6;
    if (this.isInputBandIdsSwizzled) t6 = this.swizzledBandSelection.length ? this.swizzledBandSelection : [0, 1, 2];
    else {
      t6 = [...this.functionArguments.bandIds], 0 === t6.length ? t6 = [0, 1, 2] : t6.length < 3 && (t6[1] = t6[1] ?? t6[0], t6[2] = t6[2] ?? t6[1]);
      for (let s10 = 0; s10 < 3; s10++) t6[s10] = Math.min(t6[s10], 2);
    }
    return { bandIndexMat3: l4(t6) };
  }
  _getInputBandIds(t6) {
    const s10 = t6.length;
    return this.functionArguments.bandIds.map((t7) => t7 >= s10 ? s10 - 1 : t7).map((s11) => t6[s11]);
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], c11.prototype, "functionName", void 0), e([y({ type: c10, json: { write: true, name: "rasterFunctionArguments" } })], c11.prototype, "functionArguments", void 0), e([y()], c11.prototype, "rasterArgumentNames", void 0), c11 = e([a("esri.layers.support.rasterFunctions.ExtractBandFunction")], c11);
var p14 = c11;

// node_modules/@arcgis/core/layers/support/rasterFunctions/LocalFunctionArguments.js
var a12;
var i7 = a12 = class extends p5 {
  constructor() {
    super(...arguments), this.rasters = [], this.processAsMultiband = true;
  }
  writeRasters(r7, s10) {
    s10.rasters = r7.map((r8) => "number" == typeof r8 || "string" == typeof r8 ? r8 : r8.toJSON());
  }
  clone() {
    return new a12({ operation: this.operation, processAsMultiband: this.processAsMultiband, rasters: p(this.rasters) });
  }
};
e([y({ json: { write: true } })], i7.prototype, "operation", void 0), e([y({ json: { write: true } })], i7.prototype, "rasters", void 0), e([r2("rasters")], i7.prototype, "writeRasters", null), e([y({ json: { write: true } })], i7.prototype, "processAsMultiband", void 0), i7 = a12 = e([a("esri.layers.support.rasterFunctions.LocalFunctionArguments")], i7);
var c12 = i7;

// node_modules/@arcgis/core/layers/support/rasterFunctions/localUtils.js
var a13 = /* @__PURE__ */ new Map();
function l8(t6) {
  return a13.get(t6);
}
a13.set(h2.acos, [0, Math.PI]), a13.set(h2.asin, [-Math.PI / 2, Math.PI / 2]), a13.set(h2.atan, [-Math.PI / 2, Math.PI / 2]), a13.set(h2.cos, [-1, 1]), a13.set(h2.sin, [-1, 1]), a13.set(D2.booleanAnd, [0, 1]), a13.set(D2.booleanNot, [0, 1]), a13.set(D2.booleanOr, [0, 1]), a13.set(D2.booleanXOr, [0, 1]), a13.set(D2.equalTo, [0, 1]), a13.set(D2.notEqual, [0, 1]), a13.set(D2.greaterThan, [0, 1]), a13.set(D2.greaterThanEqual, [0, 1]), a13.set(D2.lessThan, [0, 1]), a13.set(D2.lessThanEqual, [0, 1]), a13.set(D2.isNull, [0, 1]);
var c13 = [0, 2, 2, 2, 1, 2, 1, 1, 1, 1, 1, 2, 2, 1, 2, 2, 2, 2, 1, 2, 2, 1, 1, 2, 2, 1, 1, 1, 2, 2, 1, 1, 1, 2, 2, 1, 1, 1, 999, 999, 999, 999, 999, 999, 2, 1, 2, 999, 1, 1, 2, 1, 1, 1, 999, 999, 1, 1, 999, 1, 1, 2, 999, 999, 2, 2, 999, 999, 999, 999, 999, 999, 999, 999, 999, 999, 3, 999, 3];
function f3(e8, n14 = false) {
  const r7 = e8.map((t6) => t6.mask), o10 = r7.filter((e9) => r(e9)), s10 = e8[0].pixels[0].length;
  if (0 === o10.length) return new Uint8Array(s10).fill(255);
  const a21 = o10[0], l12 = new Uint8Array(a21);
  if (1 === o10.length) return l12;
  if (!n14) {
    for (let t6 = 1; t6 < o10.length; t6++) {
      const e9 = o10[t6];
      for (let t7 = 0; t7 < l12.length; t7++) l12[t7] && (l12[t7] = e9[t7] ? 255 : 0);
    }
    return l12;
  }
  if (o10.length !== r7.length) return new Uint8Array(s10).fill(255);
  for (let t6 = 1; t6 < o10.length; t6++) {
    const e9 = o10[t6];
    for (let t7 = 0; t7 < l12.length; t7++) 0 === l12[t7] && (l12[t7] = e9[t7] ? 255 : 0);
  }
  return l12;
}
function u10(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] + s10[e8]);
  return l12;
}
function h3(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand("f32", s10);
  return a21.set(o10), a21;
}
function i8(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] - s10[e8]);
  return l12;
}
function g4(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] * s10[e8]);
  return l12;
}
function p15(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand(r7, s10);
  for (let e8 = 0; e8 < s10; e8++) n14 && !n14[e8] || (a21[e8] = Math.sign(o10[e8]) * Math.floor(Math.abs(o10[e8])));
  return a21;
}
function m6(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] / s10[e8]);
  return l12;
}
function y4(t6, e8, n14) {
  return m6(t6, e8, "f32");
}
function d3(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = Math.floor(o10[e8] / s10[e8]));
  return l12;
}
function M3(t6, r7, o10, s10) {
  const a21 = t6[0], l12 = a21.length, c21 = m3.createEmptyBand(o10, l12);
  if (s10 === h2.atanh) {
    for (let t7 = 0; t7 < l12; t7++) if (r7[t7]) {
      const e8 = a21[t7];
      Math.abs(e8) >= 1 ? r7[t7] = 0 : c21[t7] = Math.atanh(e8);
    }
    return c21;
  }
  const f6 = s10 === h2.asin ? Math.asin : Math.acos;
  for (let e8 = 0; e8 < l12; e8++) if (r7[e8]) {
    const t7 = a21[e8];
    Math.abs(t7) > 1 ? r7[e8] = 0 : c21[e8] = f6(t7);
  }
  return c21;
}
function E(t6, n14, r7, o10) {
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10(s10[e8]));
  return l12;
}
function B(t6, n14, r7, o10) {
  const [s10, a21] = t6, l12 = s10.length, c21 = m3.createEmptyBand(r7, l12);
  for (let e8 = 0; e8 < l12; e8++) n14 && !n14[e8] || (c21[e8] = o10(s10[e8], a21[e8]));
  return c21;
}
function w4(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] & s10[e8]);
  return l12;
}
function b2(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] << s10[e8]);
  return l12;
}
function x4(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand(r7, s10);
  for (let e8 = 0; e8 < s10; e8++) n14 && !n14[e8] || (a21[e8] = ~o10[e8]);
  return a21;
}
function A3(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] | s10[e8]);
  return l12;
}
function k4(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] >> s10[e8]);
  return l12;
}
function N(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] ^ s10[e8]);
  return l12;
}
function P(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] && s10[e8] ? 1 : 0);
  return l12;
}
function T(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand(r7, s10);
  for (let e8 = 0; e8 < s10; e8++) n14 && !n14[e8] || (a21[e8] = o10[e8] ? 0 : 1);
  return a21;
}
function q(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] || s10[e8] ? 1 : 0);
  return l12;
}
function U(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = (o10[e8] ? 1 : 0) ^ (s10[e8] ? 1 : 0));
  return l12;
}
function I(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] === s10[e8] ? 1 : 0);
  return l12;
}
function j(t6, n14, r7, o10) {
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21), c21 = o10 === Math.E;
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = c21 ? Math.exp(s10[e8]) : o10 ** s10[e8]);
  return l12;
}
function F2(t6, e8, n14) {
  return j(t6, e8, n14, 10);
}
function z(t6, e8, n14) {
  return j(t6, e8, n14, 2);
}
function O(t6, e8, n14) {
  return j(t6, e8, n14, Math.E);
}
function W(t6, n14, r7, o10) {
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (s10[e8] <= 0 ? n14[e8] = 0 : l12[e8] = o10(s10[e8]));
  return l12;
}
function C(t6, e8, n14) {
  return W(t6, e8, n14, Math.log10);
}
function R(t6, e8, n14) {
  return W(t6, e8, n14, Math.log2);
}
function S(t6, e8, n14) {
  return W(t6, e8, n14, Math.log);
}
function X(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] > s10[e8] ? 1 : 0);
  return l12;
}
function v2(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] >= s10[e8] ? 1 : 0);
  return l12;
}
function D3(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] < s10[e8] ? 1 : 0);
  return l12;
}
function G(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] <= s10[e8] ? 1 : 0);
  return l12;
}
function H(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand(r7, s10);
  if (!n14) return a21;
  for (let e8 = 0; e8 < s10; e8++) a21[e8] = n14[e8] ? 0 : 1;
  return a21;
}
function J(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] % s10[e8]);
  return l12;
}
function K(t6, n14, r7) {
  const [o10] = t6, s10 = o10.length, a21 = m3.createEmptyBand(r7, s10);
  for (let e8 = 0; e8 < s10; e8++) n14 && !n14[e8] || (a21[e8] = -o10[e8]);
  return a21;
}
function L(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14 && !n14[e8] || (l12[e8] = o10[e8] === s10[e8] ? 0 : 1);
  return l12;
}
function Q(t6, n14, r7) {
  const [o10, s10] = t6, a21 = o10.length, l12 = m3.createEmptyBand(r7, a21), c21 = new Uint8Array(a21);
  for (let e8 = 0; e8 < a21; e8++) null != n14 && !n14[e8] || 0 !== o10[e8] || (l12[e8] = s10[e8], c21[e8] = 255);
  return { band: l12, mask: c21 };
}
function V(t6, n14, r7) {
  const [o10, s10, a21] = t6, l12 = o10.length, c21 = m3.createEmptyBand(r7, l12);
  for (let e8 = 0; e8 < l12; e8++) n14 && !n14[e8] || (c21[e8] = o10[e8] ? s10[e8] : a21[e8]);
  return c21;
}
function Y(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    let n15 = s10[e8];
    for (let r8 = 1; r8 < o10; r8++) {
      const o11 = t6[r8][e8];
      n15 < o11 && (n15 = o11);
    }
    l12[e8] = n15;
  }
  return l12;
}
function Z(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    let n15 = s10[e8];
    for (let r8 = 1; r8 < o10; r8++) {
      const o11 = t6[r8][e8];
      n15 > o11 && (n15 = o11);
    }
    l12[e8] = n15;
  }
  return l12;
}
function $(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    let n15 = s10[e8], r8 = n15;
    for (let s11 = 1; s11 < o10; s11++) {
      const o11 = t6[s11][e8];
      r8 < o11 ? r8 = o11 : n15 > o11 && (n15 = o11);
    }
    l12[e8] = r8 - n15;
  }
  return l12;
}
function _2(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    let n15 = 0;
    for (let r8 = 0; r8 < o10; r8++) n15 += t6[r8][e8];
    l12[e8] = n15 / o10;
  }
  return l12;
}
function tt(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) for (let n15 = 0; n15 < o10; n15++) {
    const r8 = t6[n15];
    l12[e8] += r8[e8];
  }
  return l12;
}
function et(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    const n15 = new Float32Array(o10);
    let r8 = 0;
    for (let a22 = 0; a22 < o10; a22++) {
      const o11 = t6[a22];
      r8 += o11[e8], n15[a22] = o11[e8];
    }
    r8 /= o10;
    let s11 = 0;
    for (let t7 = 0; t7 < o10; t7++) s11 += (n15[t7] - r8) ** 2;
    l12[e8] = Math.sqrt(s11 / o10);
  }
  return l12;
}
function nt(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const s10 = Math.floor(o10 / 2), [a21] = t6, l12 = a21.length, c21 = m3.createEmptyBand(r7, l12), f6 = new Float32Array(o10), u14 = o10 % 2 == 1;
  for (let e8 = 0; e8 < l12; e8++) if (!n14 || n14[e8]) {
    for (let n15 = 0; n15 < o10; n15++) f6[n15] = t6[n15][e8];
    f6.sort(), c21[e8] = u14 ? f6[s10] : (f6[s10] + f6[s10 - 1]) / 2;
  }
  return c21;
}
function rt(t6, n14, r7) {
  const [o10, s10] = t6;
  if (null == s10) return o10;
  const a21 = o10.length, l12 = m3.createEmptyBand(r7, a21);
  for (let e8 = 0; e8 < a21; e8++) n14[e8] && (o10[e8] === s10[e8] ? l12[e8] = o10[e8] : n14[e8] = 0);
  return l12;
}
function ot(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 <= 2) return rt(t6, n14, r7);
  const s10 = t6[0].length, a21 = m3.createEmptyBand(r7, s10), l12 = /* @__PURE__ */ new Map();
  for (let e8 = 0; e8 < s10; e8++) if (!n14 || n14[e8]) {
    let n15;
    l12.clear();
    for (let a22 = 0; a22 < o10; a22++) n15 = t6[a22][e8], l12.set(n15, l12.has(n15) ? l12.get(n15) + 1 : 1);
    let r8 = 0, s11 = 0;
    for (const t7 of l12.keys()) r8 = l12.get(t7), r8 > s11 && (s11 = r8, n15 = t7);
    a21[e8] = n15;
  }
  return a21;
}
function st(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 <= 2) return rt(t6, n14, r7);
  const s10 = t6[0].length, a21 = m3.createEmptyBand(r7, s10), l12 = /* @__PURE__ */ new Map();
  for (let e8 = 0; e8 < s10; e8++) if (!n14 || n14[e8]) {
    let n15;
    l12.clear();
    for (let a22 = 0; a22 < o10; a22++) n15 = t6[a22][e8], l12.set(n15, l12.has(n15) ? l12.get(n15) + 1 : 1);
    let r8 = 0, s11 = t6.length;
    for (const t7 of l12.keys()) r8 = l12.get(t7), r8 < s11 && (s11 = r8, n15 = t7);
    a21[e8] = n15;
  }
  return a21;
}
function at(t6, n14, r7) {
  const o10 = t6.length;
  if (o10 < 2) return t6[0];
  const [s10] = t6, a21 = s10.length, l12 = m3.createEmptyBand(r7, a21), c21 = /* @__PURE__ */ new Set();
  for (let e8 = 0; e8 < a21; e8++) if (!n14 || n14[e8]) {
    let n15;
    c21.clear();
    for (let r8 = 0; r8 < o10; r8++) n15 = t6[r8][e8], c21.add(n15);
    l12[e8] = c21.size;
  }
  return l12;
}
var lt = /* @__PURE__ */ new Map();
var ct = /* @__PURE__ */ new Map();
var ft = /* @__PURE__ */ new Map();
var ut = /* @__PURE__ */ new Map();
function ht() {
  lt.size || (lt.set(4, Math.sqrt), lt.set(6, Math.acos), lt.set(7, Math.asin), lt.set(8, Math.atan), lt.set(9, Math.atanh), lt.set(10, Math.abs), lt.set(21, Math.cos), lt.set(22, Math.cosh), lt.set(48, Math.floor), lt.set(49, Math.ceil), lt.set(51, Math.sin), lt.set(52, Math.sinh), lt.set(56, Math.tan), lt.set(57, Math.tanh), lt.set(59, Math.acosh), lt.set(60, Math.asinh), lt.set(65, Math.floor), ct.set(5, Math.pow), ct.set(61, Math.atan2), ft.set(1, u10), ft.set(2, i8), ft.set(3, g4), ft.set(11, w4), ft.set(12, b2), ft.set(12, b2), ft.set(13, x4), ft.set(14, A3), ft.set(15, k4), ft.set(16, N), ft.set(17, P), ft.set(18, T), ft.set(19, q), ft.set(20, U), ft.set(23, m6), ft.set(24, I), ft.set(25, O), ft.set(26, F2), ft.set(27, z), ft.set(28, X), ft.set(29, v2), ft.set(30, p15), ft.set(31, H), ft.set(32, h3), ft.set(33, D3), ft.set(34, G), ft.set(35, S), ft.set(36, C), ft.set(37, R), ft.set(44, J), ft.set(45, K), ft.set(46, L), ft.set(64, y4), ft.set(65, d3), ft.set(76, V), ft.set(78, V), ut.set(38, ot), ut.set(39, Y), ut.set(40, _2), ut.set(41, nt), ut.set(42, Z), ut.set(43, st), ut.set(47, $), ut.set(54, et), ut.set(55, tt), ut.set(58, at), ut.set(66, ot), ut.set(67, Y), ut.set(68, _2), ut.set(69, nt), ut.set(70, Z), ut.set(71, st), ut.set(72, $), ut.set(73, et), ut.set(74, tt), ut.set(75, at));
}
function it(t6, e8, n14, r7) {
  let [o10, a21] = s4(n14);
  const l12 = n14.startsWith("u") || n14.startsWith("s");
  l12 && (o10 -= 1e-5, a21 += 1e-5);
  for (let s10 = 0; s10 < e8.length; s10++) if (e8[s10]) {
    const n15 = t6[s10];
    isNaN(n15) || n15 < o10 || n15 > a21 ? e8[s10] = 0 : r7[s10] = l12 ? Math.round(n15) : n15;
  }
}
function gt(t6, s10, l12 = {}) {
  ht();
  let c21 = f3(t6, s10 >= 66 && s10 <= 75);
  const { outputPixelType: u14 = "f32" } = l12, h6 = !ut.has(s10) || l12.processAsMultiband, i14 = h6 ? t6[0].pixels.length : 1, g5 = [];
  for (let f6 = 0; f6 < i14; f6++) {
    const l13 = ut.has(s10) && !h6 ? t6.flatMap((t7) => t7.pixels) : t6.map((t7) => t7.pixels[f6]);
    let i15, p22 = true;
    if (s10 === s8.setNull) {
      const t7 = Q(l13, c21, u14);
      i15 = t7.band, c21 = t7.mask, p22 = false;
    } else if (ft.has(s10)) {
      i15 = ft.get(s10)(l13, c21, "f64");
    } else if (lt.has(s10)) i15 = s10 === h2.asin || s10 === h2.acos || s10 === h2.atanh ? M3(l13, c21, "f64", s10) : E(l13, c21, "f64", lt.get(s10));
    else if (ct.has(s10)) i15 = B(l13, c21, "f64", ct.get(s10));
    else if (ut.has(s10)) {
      i15 = ut.get(s10)(l13, c21, "f64");
    } else i15 = l13[0], p22 = false;
    if (p22 && s10 !== D2.isNull && !a13.has(s10)) {
      const t7 = m3.createEmptyBand(u14, i15.length);
      c21 || (c21 = new Uint8Array(i15.length).fill(255)), it(i15, c21, u14, t7), i15 = t7;
    }
    g5.push(i15);
  }
  const p21 = t6[0];
  return new m3({ width: p21.width, height: p21.height, pixelType: u14, mask: s10 === D2.isNull ? null : c21, pixels: g5 });
}

// node_modules/@arcgis/core/layers/support/rasterFunctions/LocalFunction.js
var m7 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Local", this.functionArguments = null, this.rasterArgumentNames = ["rasters"];
  }
  _bindSourceRasters() {
    const { sourceRasterInfos: t6 } = this, s10 = t6[0], { bandCount: o10 } = s10, { processAsMultiband: e8 } = this.functionArguments;
    if (t6.some((t7) => t7.bandCount !== o10)) return { success: false, supportsGPU: false, error: "local-function: input rasters do not have same band count" };
    const { operation: r7, rasters: n14 } = this.functionArguments, i14 = c13[r7];
    if (!(999 === i14 || n14.length === i14 || n14.length <= 1 && 1 === i14)) return { success: false, supportsGPU: false, error: `local-function: the length of functionArguments.rasters does not match operation's requirement: ${i14}` };
    this.outputPixelType = this._getOutputPixelType("f32");
    const u14 = s10.clone();
    u14.pixelType = this.outputPixelType, u14.statistics = null, u14.histograms = null, u14.colormap = null, u14.attributeTable = null, u14.bandCount = 999 !== i14 || e8 ? o10 : 1;
    const p21 = l8(r7);
    if (p21) {
      u14.statistics = [];
      for (let t7 = 0; t7 < u14.bandCount; t7++) u14.statistics[t7] = { min: p21[0], max: p21[1], avg: (p21[0] + p21[1]) / 2, stddev: (p21[0] + p21[1]) / 10 };
    }
    this.rasterInfo = u14;
    return { success: true, supportsGPU: 1 === u14.bandCount && i14 <= 3 && (r7 < 11 || r7 > 16) };
  }
  _processPixels(t6) {
    const { pixelBlocks: o10 } = t6;
    return t(o10) || o10.some((t7) => t(t7)) ? null : gt(o10, this.functionArguments.operation, { processAsMultiband: this.functionArguments.processAsMultiband, outputPixelType: this.outputPixelType ?? void 0 });
  }
  _getWebGLParameters() {
    var _a;
    const { operation: t6 } = this.functionArguments, s10 = c13[t6], o10 = ((_a = Object.keys(p11).find((s11) => p11[s11] === t6)) == null ? void 0 : _a.toLowerCase()) ?? "undefined", e8 = this.outputPixelType ?? "f32";
    let [i14, u14] = s4(e8);
    const c21 = e8.startsWith("u") || e8.startsWith("s");
    return c21 && (i14 -= 1e-4, u14 += 1e-4), { imageCount: s10, operationName: o10, domainRange: [i14, u14], isOutputRounded: c21 };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], m7.prototype, "functionName", void 0), e([y({ type: c12, json: { write: true, name: "rasterFunctionArguments" } })], m7.prototype, "functionArguments", void 0), e([y()], m7.prototype, "rasterArgumentNames", void 0), m7 = e([a("esri.layers.support.rasterFunctions.LocalFunction")], m7);
var l9 = m7;

// node_modules/@arcgis/core/layers/support/rasterFunctions/MaskFunctionArguments.js
var n7;
var a14 = n7 = class extends p5 {
  constructor() {
    super(...arguments), this.includedRanges = null, this.noDataValues = null, this.noDataInterpretation = o3.matchAny;
  }
  get normalizedNoDataValues() {
    const { noDataValues: t6 } = this;
    if (!(t6 == null ? void 0 : t6.length)) return null;
    let e8 = false;
    const r7 = t6.map((t7) => {
      if ("number" == typeof t7) return e8 = true, [t7];
      if ("string" == typeof t7) {
        const r8 = t7.trim().split(" ").filter((t8) => "" !== t8.trim()).map((t8) => Number(t8));
        return e8 = e8 || r8.length > 0, 0 === r8.length ? null : r8;
      }
      return null;
    });
    return e8 ? r7 : null;
  }
  clone() {
    var _a, _b;
    return new n7({ includedRanges: ((_a = this.includedRanges) == null ? void 0 : _a.slice()) ?? [], noDataValues: ((_b = this.noDataValues) == null ? void 0 : _b.slice()) ?? [], noDataInterpretation: this.noDataInterpretation });
  }
};
e([y({ json: { write: true } })], a14.prototype, "includedRanges", void 0), e([y({ json: { write: true } })], a14.prototype, "noDataValues", void 0), e([y()], a14.prototype, "normalizedNoDataValues", null), e([y({ json: { write: true } })], a14.prototype, "noDataInterpretation", void 0), a14 = n7 = e([a("esri.layers.support.rasterFunctions.MaskFunctionArguments")], a14);
var i9 = a14;

// node_modules/@arcgis/core/layers/support/rasterFunctions/MaskFunction.js
var p16 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Mask", this.functionArguments = null, this.rasterArgumentNames = ["raster"];
  }
  _bindSourceRasters() {
    const t6 = this.sourceRasterInfos[0].clone(), { pixelType: e8 } = t6;
    this.outputPixelType = this._getOutputPixelType(e8), t6.pixelType = this.outputPixelType, this.rasterInfo = t6;
    const { includedRanges: s10, normalizedNoDataValues: o10 } = this.functionArguments;
    if (!(s10 == null ? void 0 : s10.length) && !(o10 == null ? void 0 : o10.length)) return { success: false, supportsGPU: false, error: "missing includedRanges or noDataValues argument" };
    let r7 = [];
    for (let u14 = 0; u14 < t6.bandCount; u14++) {
      const t7 = A(e8, s10 == null ? void 0 : s10.slice(2 * u14, 2 * u14 + 2), o10 == null ? void 0 : o10[u14]);
      if (null == t7) {
        r7 = null;
        break;
      }
      r7.push(t7);
    }
    this.lookups = r7;
    const n14 = null != o10 && o10.every((t7) => {
      var _a;
      return (t7 == null ? void 0 : t7.length) === ((_a = o10[0]) == null ? void 0 : _a.length);
    });
    return { success: true, supportsGPU: (!s10 || s10.length <= 2 * r3) && (!o10 || n14 && o10[0].length <= r3) };
  }
  _processPixels(t6) {
    var _a;
    const e8 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(e8)) return null;
    const { outputPixelType: o10, lookups: r7 } = this, { includedRanges: n14, noDataInterpretation: u14, normalizedNoDataValues: a21 } = this.functionArguments, i14 = u14 === o3.matchAll;
    return k2(e8, { includedRanges: n14, noDataValues: a21, outputPixelType: o10, matchAll: i14, lookups: r7 });
  }
  _getWebGLParameters() {
    var _a;
    const { includedRanges: t6, normalizedNoDataValues: s10 } = this.functionArguments, o10 = new Float32Array(r3);
    o10.fill(D), ((_a = s10 == null ? void 0 : s10[0]) == null ? void 0 : _a.length) && o10.set(s10[0]);
    const r7 = new Float32Array(r3);
    for (let n14 = 0; n14 < r7.length; n14 += 2) r7[n14] = (t6 == null ? void 0 : t6[n14]) ?? -D, r7[n14 + 1] = (t6 == null ? void 0 : t6[n14 + 1]) ?? D;
    return t6 && t6.length && r7.set(t6), { bandCount: this.sourceRasterInfos[0].bandCount, noDataValues: o10, includedRanges: r7 };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], p16.prototype, "functionName", void 0), e([y({ type: i9, json: { write: true, name: "rasterFunctionArguments" } })], p16.prototype, "functionArguments", void 0), e([y()], p16.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], p16.prototype, "lookups", void 0), p16 = e([a("esri.layers.support.rasterFunctions.MaskFunction")], p16);
var m8 = p16;

// node_modules/@arcgis/core/layers/support/rasterFunctions/NDVIFunctionArguments.js
var o8;
var i10 = o8 = class extends p5 {
  constructor() {
    super(...arguments), this.visibleBandID = 0, this.infraredBandID = 1, this.scientificOutput = false;
  }
  clone() {
    const { visibleBandID: r7, infraredBandID: t6, scientificOutput: s10 } = this;
    return new o8({ visibleBandID: r7, infraredBandID: t6, scientificOutput: s10 });
  }
};
e([y({ json: { write: true } })], i10.prototype, "visibleBandID", void 0), e([y({ json: { write: true } })], i10.prototype, "infraredBandID", void 0), e([y({ json: { write: true } })], i10.prototype, "scientificOutput", void 0), i10 = o8 = e([a("esri.layers.support.rasterFunctions.NDVIFunctionArguments")], i10);
var n8 = i10;

// node_modules/@arcgis/core/layers/support/rasterFunctions/NDVIFunction.js
var c14 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "NDVI", this.functionArguments = null, this.rasterArgumentNames = ["raster"];
  }
  _bindSourceRasters() {
    const { scientificOutput: t6 } = this.functionArguments;
    this.outputPixelType = this._getOutputPixelType(t6 ? "f32" : "u8");
    const s10 = this.sourceRasterInfos[0].clone();
    s10.pixelType = this.outputPixelType, s10.colormap = null, s10.histograms = null, s10.bandCount = 1;
    const [r7, e8, n14, o10] = t6 ? [-1, 1, 0, 0.1] : [0, 200, 100, 10];
    return s10.statistics = [{ min: r7, max: e8, avg: n14, stddev: o10 }], this.rasterInfo = s10, { success: true, supportsGPU: true };
  }
  _processPixels(t6) {
    var _a;
    const r7 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(r7)) return null;
    const { visibleBandID: e8, infraredBandID: o10, scientificOutput: i14 } = this.functionArguments;
    return i(r7, e8, o10, !i14);
  }
  _getWebGLParameters() {
    const { visibleBandID: t6, infraredBandID: s10, scientificOutput: r7 } = this.functionArguments, e8 = this.isInputBandIdsSwizzled ? [0, 1, 2] : [s10, t6, 0];
    return { bandIndexMat3: l4(e8), scaled: !r7 };
  }
  _getInputBandIds(t6) {
    const { visibleBandID: s10, infraredBandID: r7 } = this.functionArguments;
    return [r7, s10, 0].map((s11) => t6[s11]);
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], c14.prototype, "functionName", void 0), e([y({ type: n8, json: { write: true, name: "rasterFunctionArguments" } })], c14.prototype, "functionArguments", void 0), e([y()], c14.prototype, "rasterArgumentNames", void 0), c14 = e([a("esri.layers.support.rasterFunctions.NDVIFunction")], c14);
var a15 = c14;

// node_modules/@arcgis/core/layers/support/rasterFunctions/RemapFunctionArguments.js
var n9;
var a16 = n9 = class extends p5 {
  constructor() {
    super(...arguments), this.inputRanges = null, this.outputValues = null, this.noDataRanges = null, this.allowUnmatched = false, this.isLastInputRangeInclusive = false;
  }
  clone() {
    return new n9({ inputRanges: [...this.inputRanges], outputValues: [...this.outputValues], noDataRanges: [...this.noDataRanges], allowUnmatched: this.allowUnmatched, isLastInputRangeInclusive: this.isLastInputRangeInclusive });
  }
};
e([y({ json: { write: true } })], a16.prototype, "inputRanges", void 0), e([y({ json: { write: true } })], a16.prototype, "outputValues", void 0), e([y({ json: { write: true } })], a16.prototype, "noDataRanges", void 0), e([y({ json: { write: true } })], a16.prototype, "allowUnmatched", void 0), e([y({ json: { write: true } })], a16.prototype, "isLastInputRangeInclusive", void 0), a16 = n9 = e([a("esri.layers.support.rasterFunctions.RemapFunctionArguments")], a16);
var r6 = a16;

// node_modules/@arcgis/core/layers/support/rasterFunctions/RemapFunction.js
var h4 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Remap", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.lookup = null;
  }
  _bindSourceRasters() {
    const t6 = this.sourceRasterInfos[0].clone(), { pixelType: e8 } = t6;
    this.outputPixelType = this._getOutputPixelType(e8), t6.pixelType = this.outputPixelType, t6.colormap = null, t6.histograms = null, t6.bandCount = 1, t6.attributeTable = null;
    const { statistics: n14 } = t6, { allowUnmatched: o10, outputValues: a21, inputRanges: r7, noDataRanges: u14, isLastInputRangeInclusive: p21 } = this.functionArguments;
    if (r(n14) && n14.length && (a21 == null ? void 0 : a21.length)) if (o10) {
      const e9 = Math.min.apply(null, [...a21, n14[0].min]), s10 = Math.max.apply(null, [...a21, n14[0].max]);
      t6.statistics = [{ ...n14[0], min: e9, max: s10 }];
    } else {
      let e9 = a21[0], s10 = e9;
      for (let t7 = 0; t7 < a21.length; t7++) e9 = e9 > a21[t7] ? a21[t7] : e9, s10 = s10 > a21[t7] ? s10 : a21[t7];
      t6.statistics = [{ ...n14[0], min: e9, max: s10 }];
    }
    this.rasterInfo = t6, this.lookup = o10 ? null : M({ srcPixelType: e8, inputRanges: r7, outputValues: a21, noDataRanges: u14, allowUnmatched: o10, isLastInputRangeInclusive: p21, outputPixelType: this.outputPixelType });
    return { success: true, supportsGPU: (!a21 || a21.length <= r3) && (!u14 || u14.length <= r3) };
  }
  _processPixels(t6) {
    var _a;
    const e8 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(e8)) return null;
    const { lookup: o10, outputPixelType: a21 } = this;
    if (o10) {
      const t7 = p4(e8, { lut: [o10.lut], offset: o10.offset, outputPixelType: a21 });
      return r(t7) && o10.mask && (t7.mask = x(e8.pixels[0], e8.mask, o10.mask, o10.offset, "u8")), t7;
    }
    const { inputRanges: r7, outputValues: u14, noDataRanges: i14, allowUnmatched: l12, isLastInputRangeInclusive: g5 } = this.functionArguments;
    return g(e8, { inputRanges: r7, outputValues: u14, noDataRanges: i14, outputPixelType: a21, allowUnmatched: l12, isLastInputRangeInclusive: g5 });
  }
  _getWebGLParameters() {
    const { allowUnmatched: t6, inputRanges: s10, outputValues: n14, noDataRanges: o10, isLastInputRangeInclusive: a21 } = this.functionArguments, u14 = new Float32Array(3 * r3), i14 = 1e-5, p21 = n14.length;
    if (s10 == null ? void 0 : s10.length) {
      let t7 = 0, o11 = 0;
      for (let r7 = 0; r7 < u14.length; r7 += 3) u14[r7] = s10[t7++] ?? D - 1, u14[r7 + 1] = s10[t7++] ?? D, u14[r7 + 2] = n14[o11++] ?? 0, o11 <= p21 && (r7 > 0 && (u14[r7] -= i14), (o11 < p21 || !a21) && (u14[r7 + 1] -= i14));
    }
    const c21 = new Float32Array(2 * r3);
    c21.fill(D), (o10 == null ? void 0 : o10.length) && c21.set(o10);
    return { allowUnmatched: t6, rangeMaps: u14, noDataRanges: c21, clampRange: s4(this.outputPixelType) };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], h4.prototype, "functionName", void 0), e([y({ type: r6, json: { write: true, name: "rasterFunctionArguments" } })], h4.prototype, "functionArguments", void 0), e([y()], h4.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], h4.prototype, "lookup", void 0), h4 = e([a("esri.layers.support.rasterFunctions.RemapFunction")], h4);
var f4 = h4;

// node_modules/@arcgis/core/layers/support/rasterFunctions/SlopeFunctionArguments.js
var i11;
var c15 = new s2({ 1: "degree", 2: "percent-rise", 3: "adjusted" }, { useNumericKeys: true });
var a17 = i11 = class extends p5 {
  constructor() {
    super(...arguments), this.slopeType = "degree", this.zFactor = 1, this.pixelSizePower = 0.664, this.pixelSizeFactor = 0.024, this.removeEdgeEffect = false;
  }
  clone() {
    return new i11({ slopeType: this.slopeType, zFactor: this.zFactor, pixelSizePower: this.pixelSizePower, pixelSizeFactor: this.pixelSizeFactor, removeEdgeEffect: this.removeEdgeEffect, raster: this.raster });
  }
};
e([o2(c15)], a17.prototype, "slopeType", void 0), e([y({ type: Number, json: { write: true } })], a17.prototype, "zFactor", void 0), e([y({ type: Number, json: { name: "psPower", write: true } })], a17.prototype, "pixelSizePower", void 0), e([y({ type: Number, json: { name: "psZFactor", write: true } })], a17.prototype, "pixelSizeFactor", void 0), e([y({ type: Boolean, json: { write: true } })], a17.prototype, "removeEdgeEffect", void 0), a17 = i11 = e([a("esri.layers.support.rasterFunctions.SlopeFunctionArguments")], a17);
var n10 = a17;

// node_modules/@arcgis/core/layers/support/rasterFunctions/SlopeFunction.js
var p17 = 1 / 111e3;
var c16 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Slope", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.isGCS = false;
  }
  _bindSourceRasters() {
    var _a;
    this.outputPixelType = this._getOutputPixelType("f32");
    const e8 = this.sourceRasterInfos[0].clone();
    return e8.pixelType = this.outputPixelType, e8.statistics = "percent-rise" !== this.functionArguments.slopeType ? [{ min: 0, max: 90, avg: 1, stddev: 1 }] : null, e8.histograms = null, e8.colormap = null, e8.attributeTable = null, e8.bandCount = 1, this.rasterInfo = e8, this.isGCS = ((_a = e8.spatialReference) == null ? void 0 : _a.isGeographic) ?? false, { success: true, supportsGPU: true };
  }
  _processPixels(e8) {
    var _a;
    const s10 = (_a = e8.pixelBlocks) == null ? void 0 : _a[0];
    if (t(s10)) return null;
    const { zFactor: r7, slopeType: o10, pixelSizePower: i14, pixelSizeFactor: p21 } = this.functionArguments, { isGCS: c21 } = this, { extent: u14 } = e8, a21 = u14 ? { x: u14.width / s10.width, y: u14.height / s10.height } : { x: 1, y: 1 };
    return a4(s10, { zFactor: r7, slopeType: o10, pixelSizePower: i14, pixelSizeFactor: p21, isGCS: c21, resolution: a21 });
  }
  _getWebGLParameters() {
    const { zFactor: e8, slopeType: t6, pixelSizeFactor: s10, pixelSizePower: r7 } = this.functionArguments;
    return { zFactor: this.isGCS && e8 >= 1 ? e8 * p17 : e8, slopeType: t6, pixelSizeFactor: s10 ?? 0, pixelSizePower: r7 ?? 0 };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], c16.prototype, "functionName", void 0), e([y({ type: n10, json: { write: true, name: "rasterFunctionArguments" } })], c16.prototype, "functionArguments", void 0), e([y()], c16.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], c16.prototype, "isGCS", void 0), c16 = e([a("esri.layers.support.rasterFunctions.SlopeFunction")], c16);
var u11 = c16;

// node_modules/@arcgis/core/layers/support/rasterFunctions/StatisticsHistogramFunctionArguments.js
var c17;
var n11 = c17 = class extends p5 {
  constructor() {
    super(...arguments), this.statistics = null, this.histograms = null;
  }
  readStatistics(t6, s10) {
    if (!(t6 == null ? void 0 : t6.length)) return null;
    const r7 = [];
    return t6.forEach((t7) => {
      const s11 = { min: t7.min, max: t7.max, avg: t7.avg ?? t7.mean, stddev: t7.stddev ?? t7.standardDeviation };
      r7.push(s11);
    }), r7;
  }
  writeStatistics(t6, s10, r7) {
    if (!(t6 == null ? void 0 : t6.length)) return;
    const o10 = [];
    t6.forEach((t7) => {
      const s11 = { ...t7, mean: t7.avg, standardDeviation: t7.stddev };
      delete s11.avg, delete s11.stddev, o10.push(s11);
    }), s10[r7] = o10;
  }
  clone() {
    return new c17({ statistics: p(this.statistics), histograms: p(this.histograms) });
  }
};
e([y({ json: { write: true } })], n11.prototype, "statistics", void 0), e([o("statistics")], n11.prototype, "readStatistics", null), e([r2("statistics")], n11.prototype, "writeStatistics", null), e([y({ json: { write: true } })], n11.prototype, "histograms", void 0), n11 = c17 = e([a("esri.layers.support.rasterFunctions.StatisticsHistogramFunctionArguments")], n11);
var p18 = n11;

// node_modules/@arcgis/core/layers/support/rasterFunctions/StatisticsHistogramFunction.js
var i12 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "StatisticsHistogram", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.isNoopProcess = true;
  }
  _bindSourceRasters() {
    const s10 = this.sourceRasterInfos[0];
    this.outputPixelType = this._getOutputPixelType("u8");
    const t6 = s10.clone(), { statistics: r7, histograms: o10 } = this.functionArguments;
    return o10 && (t6.histograms = o10), r7 && (t6.statistics = r7), this.rasterInfo = t6, { success: true, supportsGPU: true };
  }
  _processPixels(s10) {
    var _a;
    return (_a = s10.pixelBlocks) == null ? void 0 : _a[0];
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], i12.prototype, "functionName", void 0), e([y({ type: p18, json: { write: true, name: "rasterFunctionArguments" } })], i12.prototype, "functionArguments", void 0), e([y()], i12.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], i12.prototype, "indexedColormap", void 0), e([y()], i12.prototype, "isNoopProcess", void 0), i12 = e([a("esri.layers.support.rasterFunctions.StatisticsHistogramFunction")], i12);
var n12 = i12;

// node_modules/@arcgis/core/layers/support/rasterFunctions/StretchFunctionArguments.js
var n13;
var p19 = new s2({ 0: "none", 3: "standard-deviation", 4: "histogram-equalization", 5: "min-max", 6: "percent-clip", 9: "sigmoid" }, { useNumericKeys: true });
var c18 = n13 = class extends p5 {
  constructor() {
    super(...arguments), this.computeGamma = false, this.dynamicRangeAdjustment = false, this.gamma = [], this.histograms = null, this.statistics = null, this.stretchType = "none", this.useGamma = false;
  }
  writeStatistics(t6, e8, o10) {
    (t6 == null ? void 0 : t6.length) && (Array.isArray(t6[0]) || (t6 = t6.map((t7) => [t7.min, t7.max, t7.avg, t7.stddev])), e8[o10] = t6);
  }
  clone() {
    return new n13({ stretchType: this.stretchType, outputMin: this.outputMin, outputMax: this.outputMax, useGamma: this.useGamma, computeGamma: this.computeGamma, statistics: p(this.statistics), gamma: p(this.gamma), sigmoidStrengthLevel: this.sigmoidStrengthLevel, numberOfStandardDeviations: this.numberOfStandardDeviations, minPercent: this.minPercent, maxPercent: this.maxPercent, histograms: p(this.histograms), dynamicRangeAdjustment: this.dynamicRangeAdjustment, raster: this.raster });
  }
};
e([y({ type: Boolean, json: { write: true } })], c18.prototype, "computeGamma", void 0), e([y({ type: Boolean, json: { name: "dra", write: true } })], c18.prototype, "dynamicRangeAdjustment", void 0), e([y({ type: [Number], json: { write: true } })], c18.prototype, "gamma", void 0), e([y()], c18.prototype, "histograms", void 0), e([y({ type: Number, json: { write: true } })], c18.prototype, "maxPercent", void 0), e([y({ type: Number, json: { write: true } })], c18.prototype, "minPercent", void 0), e([y({ type: Number, json: { write: true } })], c18.prototype, "numberOfStandardDeviations", void 0), e([y({ type: Number, json: { name: "max", write: true } })], c18.prototype, "outputMax", void 0), e([y({ type: Number, json: { name: "min", write: true } })], c18.prototype, "outputMin", void 0), e([y({ type: Number, json: { write: true } })], c18.prototype, "sigmoidStrengthLevel", void 0), e([y({ json: { type: [[Number]], write: true } })], c18.prototype, "statistics", void 0), e([r2("statistics")], c18.prototype, "writeStatistics", null), e([o2(p19)], c18.prototype, "stretchType", void 0), e([y({ type: Boolean, json: { write: true } })], c18.prototype, "useGamma", void 0), c18 = n13 = e([a("esri.layers.support.rasterFunctions.StretchFunctionArguments")], c18);
var u12 = c18;

// node_modules/@arcgis/core/layers/support/rasterFunctions/StretchFunction.js
var m9 = class extends l2 {
  constructor() {
    super(...arguments), this.functionName = "Stretch", this.functionArguments = null, this.rasterArgumentNames = ["raster"], this.lookup = null, this.cutOffs = null;
  }
  _bindSourceRasters() {
    this.lookup = null, this.cutOffs = null;
    const t6 = this.sourceRasterInfos[0], { pixelType: s10 } = t6, { functionArguments: e8 } = this, { dynamicRangeAdjustment: o10, gamma: r7, useGamma: u14 } = e8;
    if (!o10 && ["u8", "u16", "s8", "s16"].includes(s10)) {
      const o11 = x2(e8.toJSON(), { rasterInfo: t6 }), n15 = this._isOutputRoundingNeeded() ? "round" : "float";
      this.lookup = u2({ pixelType: s10, ...o11, gamma: u14 ? r7 : null, rounding: n15 }), this.cutOffs = o11;
    } else o10 || (this.cutOffs = x2(e8.toJSON(), { rasterInfo: t6 }));
    this.outputPixelType = this._getOutputPixelType(s10);
    const n14 = t6.clone();
    n14.pixelType = this.outputPixelType, n14.statistics = null, n14.histograms = null, n14.colormap = null, n14.attributeTable = null, "u8" === this.outputPixelType && (n14.keyProperties.DataType = "processed"), this.rasterInfo = n14;
    return { success: true, supportsGPU: !o10 };
  }
  _processPixels(t6) {
    var _a;
    const e8 = (_a = t6.pixelBlocks) == null ? void 0 : _a[0];
    if (t(e8)) return e8;
    const { lookup: o10 } = this;
    if (o10) return p4(e8, { ...o10, outputPixelType: this.rasterInfo.pixelType });
    const { functionArguments: r7 } = this, n14 = this.cutOffs || x2(r7.toJSON(), { rasterInfo: this.sourceRasterInfos[0], pixelBlock: e8 }), a21 = r7.useGamma ? r7.gamma : null;
    return y2(e8, { ...n14, gamma: a21, outputPixelType: this.outputPixelType });
  }
  _getWebGLParameters() {
    const { outputMin: t6 = 0, outputMax: s10 = 255, gamma: e8, useGamma: o10 } = this.functionArguments, r7 = this.rasterInfo.bandCount >= 2 ? 3 : 1, u14 = o10 && e8 && e8.length ? M2(r7, e8) : [1, 1, 1], { minCutOff: n14, maxCutOff: i14 } = this.cutOffs ?? { minCutOff: [0, 0, 0], maxCutOff: [255, 255, 255] };
    1 === n14.length && (n14[1] = n14[2] = n14[0], i14[1] = i14[2] = i14[0]);
    const a21 = new Float32Array(r7);
    let p21;
    for (p21 = 0; p21 < r7; p21++) a21[p21] = (s10 - t6) / (i14[p21] - n14[p21]);
    const m11 = this._isOutputRoundingNeeded();
    return { bandCount: r7, outMin: t6, outMax: s10, minCutOff: n14, maxCutOff: i14, factor: a21, useGamma: o10, gamma: o10 && e8 ? e8 : [1, 1, 1], gammaCorrection: o10 && u14 ? u14 : [1, 1, 1], stretchType: this.functionArguments.stretchType, isOutputRounded: m11, type: "stretch" };
  }
};
e([y({ json: { write: true, name: "rasterFunction" } })], m9.prototype, "functionName", void 0), e([y({ type: u12, json: { write: true, name: "rasterFunctionArguments" } })], m9.prototype, "functionArguments", void 0), e([y()], m9.prototype, "rasterArgumentNames", void 0), e([y({ json: { write: true } })], m9.prototype, "lookup", void 0), e([y({ json: { write: true } })], m9.prototype, "cutOffs", void 0), m9 = e([a("esri.layers.support.rasterFunctions.StretchFunction")], m9);
var l10 = m9;

// node_modules/@arcgis/core/layers/support/rasterFunctions/rasterFunctionHelper.js
var A4 = /* @__PURE__ */ new Map();
function h5(t6, r7) {
  const { rasterFunctionArguments: e8 } = t6;
  if (!e8) return;
  (e8.rasters || [e8.raster]).forEach((t7) => {
    t7 && "number" != typeof t7 && ("string" == typeof t7 ? t7.startsWith("http") && (r7.includes(t7) || r7.push(t7)) : "rasterFunctionArguments" in t7 && h5(t7, r7));
  });
}
function C2(t6, n14) {
  if (n14 = n14 ?? {}, "function" in (t6 = p(t6)) && "arguments" in t6 && t6.arguments && (t6 = D4(t6, n14)), "rasterFunction" in t6) return v3(t6 = S2(t6), n14);
  throw new s("raster-function-helper", "unsupported raster function json.");
}
function N2(t6, r7) {
  return "rasters" === r7[0] && Array.isArray(t6.rasters) ? t6.rasters : r7.map((r8) => t6[r8]);
}
function b3(t6) {
  return !!(t6 && "object" == typeof t6 && t6.rasterFunction && t6.rasterFunctionArguments);
}
function S2(t6) {
  var _a;
  const { rasterFunction: r7, rasterFunctionArguments: e8 } = t6, n14 = {};
  for (const o10 in e8) {
    let t7 = e8[o10];
    const r8 = o10.toLowerCase();
    if ("rasters" === r8 && Array.isArray(t7)) n14.rasters = t7.map((t8) => b3(t8) ? S2(t8) : t8);
    else switch (b3(t7) && (t7 = S2(t7)), r8) {
      case "dra":
        n14.dra = t7;
        break;
      case "pspower":
        n14.psPower = t7;
        break;
      case "pszfactor":
        n14.psZFactor = t7;
        break;
      case "bandids":
        n14.bandIds = t7;
        break;
      default:
        n14[o10[0].toLowerCase() + o10.slice(1)] = t7;
    }
  }
  return "Local" !== r7 || ((_a = n14.rasters) == null ? void 0 : _a.length) || (n14.rasters = ["$$"]), { ...t6, rasterFunctionArguments: n14 };
}
function v3(t6, e8) {
  var _a, _b;
  const { rasterFunction: n14, rasterFunctionArguments: o10 } = t6, s10 = (_a = t6.outputPixelType) == null ? void 0 : _a.toLowerCase();
  if (null == n14 || !A4.has(n14)) throw new s("raster-function-helper", `unsupported raster function: ${n14}`);
  const a21 = A4.get(n14), i14 = ("function" == typeof a21.ctor ? a21.ctor : a21.ctor.default).fromJSON({ ...t6, outputPixelType: s10 }), { rasterArgumentNames: u14 } = i14, c21 = [], m11 = N2(o10, u14), l12 = "rasters" === u14[0], p21 = [];
  for (let r7 = 0; r7 < m11.length; r7++) {
    const t7 = m11[r7];
    let n15;
    null == t7 || "string" == typeof t7 && t7.startsWith("$") ? c21.push(e8 == null ? void 0 : e8.raster) : "string" == typeof t7 ? e8[t7] && c21.push(e8[t7]) : "number" != typeof t7 && "rasterFunction" in t7 && (n15 = v3(t7, e8), l12 || (i14.functionArguments[u14[r7]] = n15), c21.push(n15)), l12 && p21.push(n15 ?? t7);
  }
  if (l12 && (i14.functionArguments.rasters = p21), e8) {
    i14.sourceRasters = c21;
    const t7 = (_b = e8.raster) == null ? void 0 : _b.url;
    t7 && (i14.mainPrimaryRasterId = t7);
  }
  return i14;
}
function j2(t6, r7) {
  if (t6 && r7) for (const e8 in t6) {
    const n14 = t6[e8];
    n14 && "object" == typeof n14 && (n14.function && n14.arguments ? j2(n14.arguments, r7) : "RasterFunctionVariable" === n14.type && null != r7[n14.name] && (n14.value = r7[n14.name]));
  }
}
function R2(t6) {
  var _a;
  if (!t6 || "object" != typeof t6) return t6;
  if (Array.isArray(t6) && 0 === t6.length) return 0 === t6.length ? null : ["number", "string"].includes(typeof t6[0]) ? t6 : t6.map((t7) => R2(t7));
  if ("value" in t6 && ["number", "string", "boolean"].includes(typeof t6.value)) return t6.value;
  if (!("type" in t6)) return t6;
  switch (t6.type) {
    case "Scalar":
      return t6.value;
    case "AlgorithmicColorRamp":
      return w5(t6);
    case "MultiPartColorRamp":
      return { type: "multipart", colorRamps: t6.ArrayOfColorRamp.map(w5) };
    case "ArgumentArray":
      return ((_a = t6.elements) == null ? void 0 : _a.length) ? "RasterStatistics" === t6.elements[0].type ? t6.elements : "RasterFunctionVariable" === t6.elements[0].type ? t6.elements.map((t7) => null != t7.value ? R2(t7.value) : t7.name.toLowerCase().includes("raster") ? "$$" : null) : t6 : t6.elements;
    default:
      return t6;
  }
}
function w5(r7) {
  const e8 = r7.algorithm ?? "esriHSVAlgorithm";
  let { FromColor: n14, ToColor: o10 } = r7;
  if (!Array.isArray(n14)) {
    const { r: r8, g: e9, b: o11 } = p3({ h: n14.Hue, s: n14.Saturation, v: n14.Value });
    n14 = [r8, e9, o11, n14.AlphaValue];
  }
  if (!Array.isArray(o10)) {
    const { r: r8, g: e9, b: n15 } = p3({ h: o10.Hue, s: o10.Saturation, v: o10.Value });
    o10 = [r8, e9, n15, o10.AlphaValue];
  }
  return { type: "algorithmic", algorithm: e8, fromColor: n14, toColor: o10 };
}
function D4(t6, r7) {
  r7 && j2(t6, r7);
  const e8 = {};
  return V2(t6, e8), e8;
}
function V2(t6, r7) {
  if (!t6 || !r7) return;
  const { function: e8, arguments: n14 } = t6;
  if (!e8 || !n14) return;
  r7.rasterFunction = e8.type.replace("Function", ""), r7.outputPixelType = e8.pixelType;
  const o10 = {};
  r7.rasterFunctionArguments = o10;
  for (const s10 in n14) {
    const t7 = n14[s10];
    "object" == typeof t7 && ("function" in t7 && t7.function && t7.arguments ? (r7.rasterFunctionArguments[s10] = {}, V2(t7, r7.rasterFunctionArguments[s10])) : "value" in t7 && (o10[s10] = R2(t7.value)));
  }
  switch (o10.DEM && !o10.Raster && (o10.Raster = o10.DEM, delete o10.DEM), r7.rasterFunction) {
    case "Stretch":
      k5(o10);
      break;
    case "Colormap":
      T2(o10);
      break;
    case "Convolution":
      x5(o10);
      break;
    case "Mask":
      B2(o10);
  }
}
function k5(t6) {
  var _a;
  ((_a = t6.Statistics) == null ? void 0 : _a.length) && "object" == typeof t6.Statistics && (t6.Statistics = t6.Statistics.map((t7) => [t7.min, t7.max, t7.mean, t7.standardDeviation])), null != t6.NumberOfStandardDeviation && (t6.NumberOfStandardDeviations = t6.NumberOfStandardDeviation, delete t6.NumberOfStandardDeviation);
}
function T2(t6) {
  var _a, _b;
  "randomcolorramp" === ((_b = (_a = t6.ColorRamp) == null ? void 0 : _a.type) == null ? void 0 : _b.toLowerCase()) && (delete t6.ColorRamp, t6.ColormapName = "Random"), 0 === t6.ColorSchemeType && delete t6.ColorRamp;
}
function x5(t6) {
  null != t6.ConvolutionType && (t6.Type = t6.ConvolutionType, delete t6.ConvolutionType);
}
function B2(t6) {
  var _a;
  ((_a = t6.NoDataValues) == null ? void 0 : _a.length) && "string" == typeof t6.NoDataValues[0] && (t6.NoDataValues = t6.NoDataValues.filter((t7) => "" !== t7).map((t7) => Number(t7)));
}
A4.set("Aspect", { desc: "Aspect Function", ctor: c2, rasterArgumentNames: ["raster"] }), A4.set("BandArithmetic", { desc: "Band Arithmetic Function", ctor: m5, rasterArgumentNames: ["raster"] }), A4.set("Colormap", { desc: "Colormap Function", ctor: u6, rasterArgumentNames: ["raster"] }), A4.set("CompositeBand", { desc: "CompositeBand Function", ctor: p10, rasterArgumentNames: ["rasters"] }), A4.set("Convolution", { desc: "Convolution Function", ctor: a11, rasterArgumentNames: ["raster"] }), A4.set("ExtractBand", { desc: "ExtractBand Function", ctor: p14, rasterArgumentNames: ["raster"] }), A4.set("Local", { desc: "Local Function", ctor: l9, rasterArgumentNames: ["rasters"] }), A4.set("Mask", { desc: "Mask Function", ctor: m8, rasterArgumentNames: ["raster"] }), A4.set("NDVI", { desc: "NDVI Function", ctor: a15, rasterArgumentNames: ["raster"] }), A4.set("Remap", { desc: "Remap Function", ctor: f4, rasterArgumentNames: ["raster"] }), A4.set("Slope", { desc: "Slope Function", ctor: u11, rasterArgumentNames: ["raster"] }), A4.set("StatisticsHistogram", { desc: "Statistics Histogram Function", ctor: n12, rasterArgumentNames: ["raster"] }), A4.set("Stretch", { desc: "Stretch Function", ctor: l10, rasterArgumentNames: ["raster"] });

// node_modules/@arcgis/core/layers/support/rasterTransforms/BaseRasterTransform.js
var t5 = class extends l {
  get affectsPixelSize() {
    return false;
  }
  forwardTransform(r7) {
    return r7;
  }
  inverseTransform(r7) {
    return r7;
  }
};
e([y()], t5.prototype, "affectsPixelSize", null), e([y({ json: { write: true } })], t5.prototype, "spatialReference", void 0), t5 = e([a("esri.layers.support.rasterTransforms.BaseRasterTransform")], t5);
var a18 = t5;

// node_modules/@arcgis/core/layers/support/rasterTransforms/GCSShiftTransform.js
var a19 = class extends a18 {
  constructor() {
    super(...arguments), this.type = "gcs-shift", this.tolerance = 1e-8;
  }
  forwardTransform(r7) {
    return "point" === (r7 = r7.clone()).type ? (r7.x > 180 + this.tolerance && (r7.x -= 360), r7) : (r7.xmin >= 180 - this.tolerance ? (r7.xmax -= 360, r7.xmin -= 360) : r7.xmax > 180 + this.tolerance && (r7.xmin = -180, r7.xmax = 180), r7);
  }
  inverseTransform(r7) {
    return "point" === (r7 = r7.clone()).type ? (r7.x < -this.tolerance && (r7.x += 360), r7) : (r7.xmin < -this.tolerance && (r7.xmin += 360, r7.xmax += 360), r7);
  }
};
e([o2({ GCSShiftXform: "gcs-shift" })], a19.prototype, "type", void 0), e([y()], a19.prototype, "tolerance", void 0), a19 = e([a("esri.layers.support.rasterTransforms.GCSShiftTransform")], a19);
var c19 = a19;

// node_modules/@arcgis/core/layers/support/rasterTransforms/IdentityTransform.js
var e6 = class extends a18 {
  constructor() {
    super(...arguments), this.type = "identity";
  }
};
e([o2({ IdentityXform: "identity" })], e6.prototype, "type", void 0), e6 = e([a("esri.layers.support.rasterTransforms.IdentityTransform")], e6);
var p20 = e6;

// node_modules/@arcgis/core/layers/support/rasterTransforms/PolynomialTransform.js
function l11(e8, r7, o10) {
  const { x: t6, y: s10 } = r7;
  if (o10 < 2) {
    return { x: e8[0] + t6 * e8[2] + s10 * e8[4], y: e8[1] + t6 * e8[3] + s10 * e8[5] };
  }
  if (2 === o10) {
    const r8 = t6 * t6, o11 = s10 * s10, n15 = t6 * s10;
    return { x: e8[0] + t6 * e8[2] + s10 * e8[4] + r8 * e8[6] + n15 * e8[8] + o11 * e8[10], y: e8[1] + t6 * e8[3] + s10 * e8[5] + r8 * e8[7] + n15 * e8[9] + o11 * e8[11] };
  }
  const n14 = t6 * t6, i14 = s10 * s10, f6 = t6 * s10, p21 = n14 * t6, l12 = n14 * s10, a21 = t6 * i14, c21 = s10 * i14;
  return { x: e8[0] + t6 * e8[2] + s10 * e8[4] + n14 * e8[6] + f6 * e8[8] + i14 * e8[10] + p21 * e8[12] + l12 * e8[14] + a21 * e8[16] + c21 * e8[18], y: e8[1] + t6 * e8[3] + s10 * e8[5] + n14 * e8[7] + f6 * e8[9] + i14 * e8[11] + p21 * e8[13] + l12 * e8[15] + a21 * e8[17] + c21 * e8[19] };
}
function a20(e8, r7, o10) {
  const { xmin: t6, ymin: s10, xmax: n14, ymax: i14, spatialReference: f6 } = r7;
  let a21 = [];
  if (o10 < 2) a21.push({ x: t6, y: i14 }), a21.push({ x: n14, y: i14 }), a21.push({ x: t6, y: s10 }), a21.push({ x: n14, y: s10 });
  else {
    let e9 = 10;
    for (let r8 = 0; r8 < e9; r8++) a21.push({ x: t6, y: s10 + (i14 - s10) * r8 / (e9 - 1) }), a21.push({ x: n14, y: s10 + (i14 - s10) * r8 / (e9 - 1) });
    e9 = 8;
    for (let r8 = 1; r8 <= e9; r8++) a21.push({ x: t6 + (n14 - t6) * r8 / e9, y: s10 }), a21.push({ x: t6 + (n14 - t6) * r8 / e9, y: i14 });
  }
  a21 = a21.map((r8) => l11(e8, r8, o10));
  const c21 = a21.map((e9) => e9.x), u14 = a21.map((e9) => e9.y);
  return new w2({ xmin: Math.min.apply(null, c21), xmax: Math.max.apply(null, c21), ymin: Math.min.apply(null, u14), ymax: Math.max.apply(null, u14), spatialReference: f6 });
}
function c20(e8) {
  const [r7, o10, t6, s10, n14, i14] = e8, f6 = t6 * i14 - n14 * s10, p21 = n14 * s10 - t6 * i14;
  return [(n14 * o10 - r7 * i14) / f6, (t6 * o10 - r7 * s10) / p21, i14 / f6, s10 / p21, -n14 / f6, -t6 / p21];
}
var u13 = class extends a18 {
  constructor() {
    super(...arguments), this.polynomialOrder = 1, this.type = "polynomial";
  }
  readForwardCoefficients(e8, r7) {
    const { coeffX: o10, coeffY: t6 } = r7;
    if (!(o10 == null ? void 0 : o10.length) || !(t6 == null ? void 0 : t6.length) || o10.length !== t6.length) return null;
    const s10 = [];
    for (let n14 = 0; n14 < o10.length; n14++) s10.push(o10[n14]), s10.push(t6[n14]);
    return s10;
  }
  writeForwardCoefficients(e8, r7, o10) {
    const t6 = [], s10 = [];
    for (let n14 = 0; n14 < (e8 == null ? void 0 : e8.length); n14++) n14 % 2 == 0 ? t6.push(e8[n14]) : s10.push(e8[n14]);
    r7.coeffX = t6, r7.coeffY = s10;
  }
  get inverseCoefficients() {
    let e8 = this._get("inverseCoefficients");
    const r7 = this._get("forwardCoefficients");
    return !e8 && r7 && this.polynomialOrder < 2 && (e8 = c20(r7)), e8;
  }
  set inverseCoefficients(e8) {
    this._set("inverseCoefficients", e8);
  }
  readInverseCoefficients(e8, r7) {
    const { inverseCoeffX: o10, inverseCoeffY: t6 } = r7;
    if (!(o10 == null ? void 0 : o10.length) || !(t6 == null ? void 0 : t6.length) || o10.length !== t6.length) return null;
    const s10 = [];
    for (let n14 = 0; n14 < o10.length; n14++) s10.push(o10[n14]), s10.push(t6[n14]);
    return s10;
  }
  writeInverseCoefficients(e8, r7, o10) {
    const t6 = [], s10 = [];
    for (let n14 = 0; n14 < (e8 == null ? void 0 : e8.length); n14++) n14 % 2 == 0 ? t6.push(e8[n14]) : s10.push(e8[n14]);
    r7.inverseCoeffX = t6, r7.inverseCoeffY = s10;
  }
  get affectsPixelSize() {
    return this.polynomialOrder > 0;
  }
  forwardTransform(e8) {
    if ("point" === e8.type) {
      const r7 = l11(this.forwardCoefficients, e8, this.polynomialOrder);
      return new w({ x: r7.x, y: r7.y, spatialReference: e8.spatialReference });
    }
    return a20(this.forwardCoefficients, e8, this.polynomialOrder);
  }
  inverseTransform(e8) {
    if ("point" === e8.type) {
      const r7 = l11(this.inverseCoefficients, e8, this.polynomialOrder);
      return new w({ x: r7.x, y: r7.y, spatialReference: e8.spatialReference });
    }
    return a20(this.inverseCoefficients, e8, this.polynomialOrder);
  }
};
e([y({ json: { write: true } })], u13.prototype, "polynomialOrder", void 0), e([y()], u13.prototype, "forwardCoefficients", void 0), e([o("forwardCoefficients", ["coeffX", "coeffY"])], u13.prototype, "readForwardCoefficients", null), e([r2("forwardCoefficients")], u13.prototype, "writeForwardCoefficients", null), e([y({ json: { write: true } })], u13.prototype, "inverseCoefficients", null), e([o("inverseCoefficients", ["inverseCoeffX", "inverseCoeffY"])], u13.prototype, "readInverseCoefficients", null), e([r2("inverseCoefficients")], u13.prototype, "writeInverseCoefficients", null), e([y()], u13.prototype, "affectsPixelSize", null), e([o2({ PolynomialXform: "polynomial" })], u13.prototype, "type", void 0), u13 = e([a("esri.layers.support.rasterTransforms.PolynomialTransform")], u13);
var m10 = u13;

// node_modules/@arcgis/core/layers/support/rasterTransforms/utils.js
var o9 = { GCSShiftXform: c19, IdentityXform: p20, PolynomialXform: m10 };
var e7 = Object.keys(o9);
function f5(r7) {
  const t6 = r7 == null ? void 0 : r7.type;
  return !r7 || e7.includes(t6);
}
function i13(r7) {
  const t6 = r7 == null ? void 0 : r7.type;
  if (!t6) return null;
  const n14 = o9[r7 == null ? void 0 : r7.type];
  if (n14) {
    const t7 = new n14();
    return t7.read(r7), t7;
  }
  return null;
}

export {
  h5 as h,
  C2 as C,
  c19 as c,
  m10 as m,
  f5 as f,
  i13 as i
};
//# sourceMappingURL=chunk-NQSCWFLI.js.map
