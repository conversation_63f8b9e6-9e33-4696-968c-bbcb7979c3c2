{"version": 3, "sources": ["../../@arcgis/core/Camera.js", "../../@arcgis/core/Viewpoint.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"./chunks/tslib.es6.js\";import{ClonableMixin as r}from\"./core/Clonable.js\";import{cyclicalDegrees as t}from\"./core/Cyclical.js\";import{JSONSupport as e}from\"./core/JSONSupport.js\";import{clamp as s}from\"./core/mathUtils.js\";import{isNone as i}from\"./core/maybe.js\";import{property as p}from\"./core/accessorSupport/decorators/property.js\";import{cast as n}from\"./core/accessorSupport/decorators/cast.js\";import\"./core/arrayUtils.js\";import{reader as a}from\"./core/accessorSupport/decorators/reader.js\";import{subclass as c}from\"./core/accessorSupport/decorators/subclass.js\";import{writer as l}from\"./core/accessorSupport/decorators/writer.js\";import{ensureNumber as m}from\"./core/accessorSupport/ensureType.js\";import u from\"./geometry/Point.js\";let d=class extends(r(e)){constructor(...o){super(...o),this.position=new u([0,0,0]),this.heading=0,this.tilt=0,this.fov=55}normalizeCtorArgs(o,r,t,e){if(o&&\"object\"==typeof o&&(\"x\"in o||Array.isArray(o))){const s={position:o};return null!=r&&(s.heading=r),null!=t&&(s.tilt=t),null!=e&&(s.fov=e),s}return o}writePosition(o,r,t,e){const s=o.clone();s.x=m(o.x||0),s.y=m(o.y||0),s.z=o.hasZ?m(o.z||0):o.z,r[t]=s.write({},e)}readPosition(o,r){const t=new u;return t.read(o,r),t.x=m(t.x||0),t.y=m(t.y||0),t.z=t.hasZ?m(t.z||0):t.z,t}equals(o){return!i(o)&&(this.tilt===o.tilt&&this.heading===o.heading&&this.fov===o.fov&&this.position.equals(o.position))}};o([p({type:u,json:{write:{isRequired:!0}}})],d.prototype,\"position\",void 0),o([l(\"position\")],d.prototype,\"writePosition\",null),o([a(\"position\")],d.prototype,\"readPosition\",null),o([p({type:Number,nonNullable:!0,json:{write:{isRequired:!0}}}),n((o=>t.normalize(m(o))))],d.prototype,\"heading\",void 0),o([p({type:Number,nonNullable:!0,json:{write:{isRequired:!0}}}),n((o=>s(m(o),-180,180)))],d.prototype,\"tilt\",void 0),o([p({type:Number,nonNullable:!0,json:{read:!1,write:!1}})],d.prototype,\"fov\",void 0),d=o([c(\"esri.Camera\")],d);const y=d;export{y as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"./chunks/tslib.es6.js\";import t from\"./Camera.js\";import{geometryTypes as e}from\"./geometry.js\";import{JSONSupport as o}from\"./core/JSONSupport.js\";import{isSome as s}from\"./core/maybe.js\";import{property as i}from\"./core/accessorSupport/decorators/property.js\";import{cast as a}from\"./core/accessorSupport/decorators/cast.js\";import\"./core/arrayUtils.js\";import{subclass as c}from\"./core/accessorSupport/decorators/subclass.js\";import{fromJSON as p}from\"./geometry/support/jsonUtils.js\";var m;let n=m=class extends o{constructor(r){super(r),this.rotation=0,this.scale=0,this.targetGeometry=null,this.camera=null}castRotation(r){return(r%=360)<0&&(r+=360),r}clone(){return new m({rotation:this.rotation,scale:this.scale,targetGeometry:s(this.targetGeometry)?this.targetGeometry.clone():null,camera:s(this.camera)?this.camera.clone():null})}};function l(){return{enabled:!this.camera}}r([i({type:Number,json:{write:!0,origins:{\"web-map\":{default:0,write:!0},\"web-scene\":{write:{overridePolicy:l}}}}})],n.prototype,\"rotation\",void 0),r([a(\"rotation\")],n.prototype,\"castRotation\",null),r([i({type:Number,json:{write:!0,origins:{\"web-map\":{default:0,write:!0},\"web-scene\":{write:{overridePolicy:l}}}}})],n.prototype,\"scale\",void 0),r([i({types:e,json:{read:p,write:!0,origins:{\"web-scene\":{read:p,write:{overridePolicy:l}}}}})],n.prototype,\"targetGeometry\",void 0),r([i({type:t,json:{write:!0}})],n.prototype,\"camera\",void 0),n=m=r([c(\"esri.Viewpoint\")],n);const u=n;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4vB,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,WAAS,IAAI,EAAE,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,OAAK,GAAE,KAAK,MAAI;AAAA,EAAE;AAAA,EAAC,kBAAkBA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAGH,MAAG,YAAU,OAAOA,OAAI,OAAMA,MAAG,MAAM,QAAQA,EAAC,IAAG;AAAC,YAAMI,KAAE,EAAC,UAASJ,GAAC;AAAE,aAAO,QAAMC,OAAIG,GAAE,UAAQH,KAAG,QAAMC,OAAIE,GAAE,OAAKF,KAAG,QAAMC,OAAIC,GAAE,MAAID,KAAGC;AAAA,IAAC;AAAC,WAAOJ;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEJ,GAAE,MAAM;AAAE,IAAAI,GAAE,IAAE,EAAEJ,GAAE,KAAG,CAAC,GAAEI,GAAE,IAAE,EAAEJ,GAAE,KAAG,CAAC,GAAEI,GAAE,IAAEJ,GAAE,OAAK,EAAEA,GAAE,KAAG,CAAC,IAAEA,GAAE,GAAEC,GAAEC,EAAC,IAAEE,GAAE,MAAM,CAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaH,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAE,WAAOA,GAAE,KAAKF,IAAEC,EAAC,GAAEC,GAAE,IAAE,EAAEA,GAAE,KAAG,CAAC,GAAEA,GAAE,IAAE,EAAEA,GAAE,KAAG,CAAC,GAAEA,GAAE,IAAEA,GAAE,OAAK,EAAEA,GAAE,KAAG,CAAC,IAAEA,GAAE,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,WAAM,CAAC,EAAEA,EAAC,MAAI,KAAK,SAAOA,GAAE,QAAM,KAAK,YAAUA,GAAE,WAAS,KAAK,QAAMA,GAAE,OAAK,KAAK,SAAS,OAAOA,GAAE,QAAQ;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,EAAC,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACC,GAAE,UAAU,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,EAAC,YAAW,KAAE,EAAC,EAAC,CAAC,GAAE,EAAG,CAAAD,OAAGI,GAAE,UAAU,EAAEJ,EAAC,CAAC,CAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,EAAC,YAAW,KAAE,EAAC,EAAC,CAAC,GAAE,EAAG,CAAAA,OAAGK,GAAE,EAAEL,EAAC,GAAE,MAAK,GAAG,CAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,IAAE,EAAE,CAACK,GAAE,aAAa,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;;;ACAp6C,IAAI;AAAE,IAAIC,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,GAAE,KAAK,QAAM,GAAE,KAAK,iBAAe,MAAK,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,aAAaA,IAAE;AAAC,YAAOA,MAAG,OAAK,MAAIA,MAAG,MAAKA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,UAAS,KAAK,UAAS,OAAM,KAAK,OAAM,gBAAe,EAAE,KAAK,cAAc,IAAE,KAAK,eAAe,MAAM,IAAE,MAAK,QAAO,EAAE,KAAK,MAAM,IAAE,KAAK,OAAO,MAAM,IAAE,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,SAASC,KAAG;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,OAAM;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,WAAU,EAAC,SAAQ,GAAE,OAAM,KAAE,GAAE,aAAY,EAAC,OAAM,EAAC,gBAAeA,GAAC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAEA,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,MAAG,SAAQ,EAAC,WAAU,EAAC,SAAQ,GAAE,OAAM,KAAE,GAAE,aAAY,EAAC,OAAM,EAAC,gBAAeE,GAAC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAM,GAAE,MAAK,EAAC,MAAK,GAAE,OAAM,MAAG,SAAQ,EAAC,aAAY,EAAC,MAAK,GAAE,OAAM,EAAC,gBAAeE,GAAC,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKG,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,IAAE,EAAE,CAACI,GAAE,gBAAgB,CAAC,GAAEJ,EAAC;AAAE,IAAM,IAAEA;", "names": ["o", "r", "t", "e", "s", "a", "y", "n", "r", "l", "y", "a"]}