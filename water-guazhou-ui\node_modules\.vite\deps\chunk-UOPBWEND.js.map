{"version": 3, "sources": ["../../@arcgis/core/arcade/functions/geomasync.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{version as n}from\"../../kernel.js\";import{cloneGeometry as t,convertSquareUnitsToCode as e,convertLinearUnitsToCode as r}from\"../kernel.js\";import{G as i,y as a,k as o,j as s,m as l,x as u,q as c,T as f,A as d,J as m,H as w,K as h,g as y,h as p,C as g}from\"../../chunks/languageUtils.js\";import{centroidPolyline as v,centroidMultiPoint as P,getMetersPerVerticalUnitForSR as I,segmentLength3d as A}from\"./centroid.js\";import F from\"../../geometry/Extent.js\";import R from\"../../geometry/Geometry.js\";import{disjoint as x,intersects as N,touches as j,crosses as b,within as S,contains as O,overlaps as k,equals as C,relate as J,intersect as M,union as Z,difference as D,symmetricDifference as U,clip as z,cut as E,planarArea as L,geodesicArea as T,planarLength as q,geodesicLength as W,distance as G,densify as H,geodesicDensify as K,generalize as V,buffer as B,geodesicBuffer as Q,offset as X,rotate as Y,simplify as $,isSimple as _,convexHull as nn}from\"../../geometry/geometryEngineAsync.js\";import tn from\"../../geometry/Multipoint.js\";import en from\"../../geometry/Point.js\";import rn from\"../../geometry/Polygon.js\";import an from\"../../geometry/Polyline.js\";import{fromJSON as on}from\"../../geometry/support/jsonUtils.js\";import{ArcadeExecutionError as sn,ExecutionErrorCodes as ln}from\"../executionError.js\";import un from\"../Dictionary.js\";import cn from\"../ArcadePortal.js\";import{getPortal as fn,lookupUser as dn}from\"../portalUtils.js\";import{getMetersPerUnitForSR as mn}from\"../../core/unitUtils.js\";function wn(t){return 0===n.indexOf(\"4.\")?rn.fromExtent(t):new rn({spatialReference:t.spatialReference,rings:[[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]]})}function hn(n,t,e){if(a(n,2,2,t,e),n[0]instanceof R&&n[1]instanceof R);else if(n[0]instanceof R&&null===n[1]);else if(n[1]instanceof R&&null===n[0]);else if(null!==n[0]||null!==n[1])throw new sn(t,ln.InvalidParameter,e)}async function yn(n,t){if(\"polygon\"!==n.type&&\"polyline\"!==n.type&&\"extent\"!==n.type)return 0;let e=1;if(n.spatialReference.vcsWkid||n.spatialReference.latestVcsWkid){e=I(n.spatialReference)/mn(n.spatialReference)}let r=0;if(\"polyline\"===n.type)for(const a of n.paths)for(let n=1;n<a.length;n++)r+=A(a[n],a[n-1],e);else if(\"polygon\"===n.type)for(const a of n.rings){for(let n=1;n<a.length;n++)r+=A(a[n],a[n-1],e);(a[0][0]!==a[a.length-1][0]||a[0][1]!==a[a.length-1][1]||void 0!==a[0][2]&&a[0][2]!==a[a.length-1][2])&&(r+=A(a[0],a[a.length-1],e))}else\"extent\"===n.type&&(r+=2*A([n.xmin,n.ymin,0],[n.xmax,n.ymin,0],e),r+=2*A([n.xmin,n.ymin,0],[n.xmin,n.ymax,0],e),r*=2,r+=4*Math.abs(d(n.zmax,0)*e-d(n.zmin,0)*e));const i=new an({hasZ:!1,hasM:!1,spatialReference:n.spatialReference,paths:[[0,0],[0,r]]});return q(i,t)}function pn(n){\"async\"===n.mode&&(n.functions.disjoint=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null===a[0]||null===a[1]||x(a[0],a[1]))))},n.functions.intersects=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&N(a[0],a[1]))))},n.functions.touches=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&j(a[0],a[1]))))},n.functions.crosses=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&b(a[0],a[1]))))},n.functions.within=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&S(a[0],a[1]))))},n.functions.contains=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&O(a[0],a[1]))))},n.functions.overlaps=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null!==a[0]&&null!==a[1]&&k(a[0],a[1]))))},n.functions.equals=function(t,e){return n.standardFunctionAsync(t,e,((n,r,i)=>(a(i,2,2,t,e),i[0]===i[1]||(i[0]instanceof R&&i[1]instanceof R?C(i[0],i[1]):!(!o(i[0])||!o(i[1]))&&i[0].equals(i[1])))))},n.functions.relate=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{if(o=i(o),a(o,3,3,t,e),o[0]instanceof R&&o[1]instanceof R)return J(o[0],o[1],s(o[2]));if(o[0]instanceof R&&null===o[1])return!1;if(o[1]instanceof R&&null===o[0])return!1;if(null===o[0]&&null===o[1])return!1;throw new sn(t,ln.InvalidParameter,e)}))},n.functions.intersection=function(t,e){return n.standardFunctionAsync(t,e,((n,r,a)=>(hn(a=i(a),t,e),null===a[0]||null===a[1]?null:M(a[0],a[1]))))},n.functions.union=function(e,r){return n.standardFunctionAsync(e,r,((n,a,o)=>{const s=[];if(0===(o=i(o)).length)throw new sn(e,ln.WrongNumberOfParameters,r);if(1===o.length)if(l(o[0])){const n=i(o[0]);for(let t=0;t<n.length;t++)if(null!==n[t]){if(!(n[t]instanceof R))throw new sn(e,ln.InvalidParameter,r);s.push(n[t])}}else{if(!u(o[0])){if(o[0]instanceof R)return c(t(o[0]),e.spatialReference);if(null===o[0])return null;throw new sn(e,ln.InvalidParameter,r)}{const n=i(o[0].toArray());for(let t=0;t<n.length;t++)if(null!==n[t]){if(!(n[t]instanceof R))throw new sn(e,ln.InvalidParameter,r);s.push(n[t])}}}else for(let t=0;t<o.length;t++)if(null!==o[t]){if(!(o[t]instanceof R))throw new sn(e,ln.InvalidParameter,r);s.push(o[t])}return 0===s.length?null:Z(s)}))},n.functions.difference=function(e,r){return n.standardFunctionAsync(e,r,((n,a,o)=>(hn(o=i(o),e,r),null!==o[0]&&null===o[1]?t(o[0]):null===o[0]?null:D(o[0],o[1]))))},n.functions.symmetricdifference=function(e,r){return n.standardFunctionAsync(e,r,((n,a,o)=>(hn(o=i(o),e,r),null===o[0]&&null===o[1]?null:null===o[0]?t(o[1]):null===o[1]?t(o[0]):U(o[0],o[1]))))},n.functions.clip=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{if(o=i(o),a(o,2,2,t,e),!(o[1]instanceof F)&&null!==o[1])throw new sn(t,ln.InvalidParameter,e);if(null===o[0])return null;if(!(o[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return null===o[1]?null:z(o[0],o[1])}))},n.functions.cut=function(e,r){return n.standardFunctionAsync(e,r,((n,o,s)=>{if(s=i(s),a(s,2,2,e,r),!(s[1]instanceof an)&&null!==s[1])throw new sn(e,ln.InvalidParameter,r);if(null===s[0])return[];if(!(s[0]instanceof R))throw new sn(e,ln.InvalidParameter,r);return null===s[1]?[t(s[0])]:E(s[0],s[1])}))},n.functions.area=function(t,r){return n.standardFunctionAsync(t,r,(async(n,o,s)=>{if(a(s,1,2,t,r),null===(s=i(s))[0])return 0;if(f(s[0])){const n=await s[0].sumArea(e(d(s[1],-1)),!1,t.abortSignal);if(t.abortSignal.aborted)throw new sn(t,ln.Cancelled,r);return n}if(l(s[0])||u(s[0])){const n=m(s[0],t.spatialReference);return null===n?0:L(n,e(d(s[1],-1)))}if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,r);return L(s[0],e(d(s[1],-1)))}))},n.functions.areageodetic=function(t,r){return n.standardFunctionAsync(t,r,(async(n,o,s)=>{if(a(s,1,2,t,r),null===(s=i(s))[0])return 0;if(f(s[0])){const n=await s[0].sumArea(e(d(s[1],-1)),!0,t.abortSignal);if(t.abortSignal.aborted)throw new sn(t,ln.Cancelled,r);return n}if(l(s[0])||u(s[0])){const n=m(s[0],t.spatialReference);return null===n?0:T(n,e(d(s[1],-1)))}if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,r);return T(s[0],e(d(s[1],-1)))}))},n.functions.length=function(t,e){return n.standardFunctionAsync(t,e,(async(n,o,s)=>{if(a(s,1,2,t,e),null===(s=i(s))[0])return 0;if(f(s[0])){const n=await s[0].sumLength(r(d(s[1],-1)),!1,t.abortSignal);if(t.abortSignal.aborted)throw new sn(t,ln.Cancelled,e);return n}if(l(s[0])||u(s[0])){const n=w(s[0],t.spatialReference);return null===n?0:q(n,r(d(s[1],-1)))}if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return q(s[0],r(d(s[1],-1)))}))},n.functions.length3d=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{if(a(s,1,2,t,e),null===(s=i(s))[0])return 0;if(l(s[0])||u(s[0])){const n=w(s[0],t.spatialReference);return null===n?0:!0===n.hasZ?yn(n,r(d(s[1],-1))):q(n,r(d(s[1],-1)))}if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return!0===s[0].hasZ?yn(s[0],r(d(s[1],-1))):q(s[0],r(d(s[1],-1)))}))},n.functions.lengthgeodetic=function(t,e){return n.standardFunctionAsync(t,e,(async(n,o,s)=>{if(a(s,1,2,t,e),null===(s=i(s))[0])return 0;if(f(s[0])){const n=await s[0].sumLength(r(d(s[1],-1)),!0,t.abortSignal);if(t.abortSignal.aborted)throw new sn(t,ln.Cancelled,e);return n}if(l(s[0])||u(s[0])){const n=w(s[0],t.spatialReference);return null===n?0:W(n,r(d(s[1],-1)))}if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return W(s[0],r(d(s[1],-1)))}))},n.functions.distance=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{s=i(s),a(s,2,3,t,e);let c=s[0];(l(s[0])||u(s[0]))&&(c=h(s[0],t.spatialReference));let f=s[1];if((l(s[1])||u(s[1]))&&(f=h(s[1],t.spatialReference)),!(c instanceof R))throw new sn(t,ln.InvalidParameter,e);if(!(f instanceof R))throw new sn(t,ln.InvalidParameter,e);return G(c,f,r(d(s[2],-1)))}))},n.functions.distancegeodetic=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{s=i(s),a(s,2,3,t,e);const l=s[0],u=s[1];if(!(l instanceof en))throw new sn(t,ln.InvalidParameter,e);if(!(u instanceof en))throw new sn(t,ln.InvalidParameter,e);const c=new an({paths:[],spatialReference:l.spatialReference});return c.addPath([l,u]),W(c,r(d(s[2],-1)))}))},n.functions.densify=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{if(s=i(s),a(s,2,3,t,e),null===s[0])return null;if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);const l=y(s[1]);if(isNaN(l))throw new sn(t,ln.InvalidParameter,e);if(l<=0)throw new sn(t,ln.InvalidParameter,e);return s[0]instanceof rn||s[0]instanceof an?H(s[0],l,r(d(s[2],-1))):s[0]instanceof F?H(wn(s[0]),l,r(d(s[2],-1))):s[0]}))},n.functions.densifygeodetic=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{if(s=i(s),a(s,2,3,t,e),null===s[0])return null;if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);const l=y(s[1]);if(isNaN(l))throw new sn(t,ln.InvalidParameter,e);if(l<=0)throw new sn(t,ln.InvalidParameter,e);return s[0]instanceof rn||s[0]instanceof an?K(s[0],l,r(d(s[2],-1))):s[0]instanceof F?K(wn(s[0]),l,r(d(s[2],-1))):s[0]}))},n.functions.generalize=function(t,e){return n.standardFunctionAsync(t,e,((n,o,s)=>{if(s=i(s),a(s,2,4,t,e),null===s[0])return null;if(!(s[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);const l=y(s[1]);if(isNaN(l))throw new sn(t,ln.InvalidParameter,e);return V(s[0],l,p(d(s[2],!0)),r(d(s[3],-1)))}))},n.functions.buffer=function(e,o){return n.standardFunctionAsync(e,o,((n,s,l)=>{if(l=i(l),a(l,2,3,e,o),null===l[0])return null;if(!(l[0]instanceof R))throw new sn(e,ln.InvalidParameter,o);const u=y(l[1]);if(isNaN(u))throw new sn(e,ln.InvalidParameter,o);return 0===u?t(l[0]):B(l[0],u,r(d(l[2],-1)))}))},n.functions.buffergeodetic=function(e,o){return n.standardFunctionAsync(e,o,((n,s,l)=>{if(l=i(l),a(l,2,3,e,o),null===l[0])return null;if(!(l[0]instanceof R))throw new sn(e,ln.InvalidParameter,o);const u=y(l[1]);if(isNaN(u))throw new sn(e,ln.InvalidParameter,o);return 0===u?t(l[0]):Q(l[0],u,r(d(l[2],-1)))}))},n.functions.offset=function(t,e){return n.standardFunctionAsync(t,e,((n,o,l)=>{if(l=i(l),a(l,2,6,t,e),null===l[0])return null;if(!(l[0]instanceof rn||l[0]instanceof an))throw new sn(t,ln.InvalidParameter,e);const u=y(l[1]);if(isNaN(u))throw new sn(t,ln.InvalidParameter,e);const c=y(d(l[4],10));if(isNaN(c))throw new sn(t,ln.InvalidParameter,e);const f=y(d(l[5],0));if(isNaN(f))throw new sn(t,ln.InvalidParameter,e);return X(l[0],u,r(d(l[2],-1)),s(d(l[3],\"round\")).toLowerCase(),c,f)}))},n.functions.rotate=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{o=i(o),a(o,2,3,t,e);let s=o[0];if(null===s)return null;if(!(s instanceof R))throw new sn(t,ln.InvalidParameter,e);s instanceof F&&(s=rn.fromExtent(s));const l=y(o[1]);if(isNaN(l))throw new sn(t,ln.InvalidParameter,e);const u=d(o[2],null);if(null===u)return Y(s,l);if(u instanceof en)return Y(s,l,u);throw new sn(t,ln.InvalidParameter,e)}))},n.functions.centroid=function(e,r){return n.standardFunctionAsync(e,r,((n,o,s)=>{if(s=i(s),a(s,1,1,e,r),null===s[0])return null;let f=s[0];if((l(s[0])||u(s[0]))&&(f=h(s[0],e.spatialReference)),null===f)return null;if(!(f instanceof R))throw new sn(e,ln.InvalidParameter,r);return f instanceof en?c(t(s[0]),e.spatialReference):f instanceof rn?f.centroid:f instanceof an?v(f):f instanceof tn?P(f):f instanceof F?f.center:null}))},n.functions.multiparttosinglepart=function(e,r){return n.standardFunctionAsync(e,r,(async(n,o,s)=>{s=i(s),a(s,1,1,e,r);const l=[];if(null===s[0])return null;if(!(s[0]instanceof R))throw new sn(e,ln.InvalidParameter,r);if(s[0]instanceof en)return[c(t(s[0]),e.spatialReference)];if(s[0]instanceof F)return[c(t(s[0]),e.spatialReference)];const u=await $(s[0]);if(u instanceof rn){const n=[],t=[];for(let e=0;e<u.rings.length;e++)if(u.isClockwise(u.rings[e])){const t=on({rings:[u.rings[e]],hasZ:!0===u.hasZ,hazM:!0===u.hasM,spatialReference:u.spatialReference.toJSON()});n.push(t)}else t.push({ring:u.rings[e],pt:u.getPoint(e,0)});for(let e=0;e<t.length;e++)for(let r=0;r<n.length;r++)if(n[r].contains(t[e].pt)){n[r].addRing(t[e].ring);break}return n}if(u instanceof an){const n=[];for(let t=0;t<u.paths.length;t++){const e=on({paths:[u.paths[t]],hasZ:!0===u.hasZ,hazM:!0===u.hasM,spatialReference:u.spatialReference.toJSON()});n.push(e)}return n}if(s[0]instanceof tn){const n=c(t(s[0]),e.spatialReference);for(let t=0;t<n.points.length;t++)l.push(n.getPoint(t));return l}return null}))},n.functions.issimple=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{if(o=i(o),a(o,1,1,t,e),null===o[0])return!0;if(!(o[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return _(o[0])}))},n.functions.simplify=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{if(o=i(o),a(o,1,1,t,e),null===o[0])return null;if(!(o[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return $(o[0])}))},n.functions.convexhull=function(t,e){return n.standardFunctionAsync(t,e,((n,r,o)=>{if(o=i(o),a(o,1,1,t,e),null===o[0])return null;if(!(o[0]instanceof R))throw new sn(t,ln.InvalidParameter,e);return nn(o[0])}))},n.functions.getuser=function(t,e){return n.standardFunctionAsync(t,e,(async(n,r,i)=>{a(i,0,2,t,e);let o=d(i[1],\"\"),l=!0===o;if(o=!0===o||!1===o?\"\":s(o),0===i.length||i[0]instanceof cn){let n=null;t.services&&t.services.portal&&(n=t.services.portal),i.length>0&&(n=fn(i[0],n));const e=await dn(n,o,l);if(e){const n=JSON.parse(JSON.stringify(e));for(const t of[\"lastLogin\",\"created\",\"modified\"])void 0!==n[t]&&null!==n[t]&&(n[t]=new Date(n[t]));return un.convertObjectToArcadeDictionary(n,g(t))}return null}let u=null;if(f(i[0])&&(u=i[0]),u){if(l=!1,o)return null;await u.load();const n=await u.getOwningSystemUrl();if(!n){if(!o){const n=await u.getIdentityUser();return n?un.convertObjectToArcadeDictionary({username:n},g(t)):null}return null}let e=null;t.services&&t.services.portal&&(e=t.services.portal),e=fn(new cn(n),e);const r=await dn(e,o,l);if(r){const n=JSON.parse(JSON.stringify(r));for(const t of[\"lastLogin\",\"created\",\"modified\"])void 0!==n[t]&&null!==n[t]&&(n[t]=new Date(n[t]));return un.convertObjectToArcadeDictionary(n,g(t))}return null}throw new sn(t,ln.InvalidParameter,e)}))})}export{pn as registerFunctions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8+C,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAI,EAAE,QAAQ,IAAI,IAAE,EAAG,WAAWA,EAAC,IAAE,IAAI,EAAG,EAAC,kBAAiBA,GAAE,kBAAiB,OAAM,CAAC,CAAC,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC,CAAC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAG,GAAEA,IAAEC,IAAE;AAAC,MAAG,EAAE,GAAE,GAAE,GAAED,IAAEC,EAAC,GAAE,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,EAAE;AAAA,WAAS,EAAE,CAAC,aAAY,KAAG,SAAO,EAAE,CAAC,EAAE;AAAA,WAAS,EAAE,CAAC,aAAY,KAAG,SAAO,EAAE,CAAC,EAAE;AAAA,WAAS,SAAO,EAAE,CAAC,KAAG,SAAO,EAAE,CAAC,EAAE,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAC;AAAC,eAAe,GAAG,GAAED,IAAE;AAAC,MAAG,cAAY,EAAE,QAAM,eAAa,EAAE,QAAM,aAAW,EAAE,KAAK,QAAO;AAAE,MAAIC,KAAE;AAAE,MAAG,EAAE,iBAAiB,WAAS,EAAE,iBAAiB,eAAc;AAAC,IAAAA,KAAE,EAAE,EAAE,gBAAgB,IAAE,EAAG,EAAE,gBAAgB;AAAA,EAAC;AAAC,MAAIC,KAAE;AAAE,MAAG,eAAa,EAAE,KAAK,YAAUC,MAAK,EAAE,MAAM,UAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAF,MAAGD,GAAEE,GAAEC,EAAC,GAAED,GAAEC,KAAE,CAAC,GAAEH,EAAC;AAAA,WAAU,cAAY,EAAE,KAAK,YAAUE,MAAK,EAAE,OAAM;AAAC,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAF,MAAGD,GAAEE,GAAEC,EAAC,GAAED,GAAEC,KAAE,CAAC,GAAEH,EAAC;AAAE,KAACE,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,KAAG,WAASA,GAAE,CAAC,EAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,CAAC,MAAIA,GAAEA,GAAE,SAAO,CAAC,EAAE,CAAC,OAAKD,MAAGD,GAAEE,GAAE,CAAC,GAAEA,GAAEA,GAAE,SAAO,CAAC,GAAEF,EAAC;AAAA,EAAE;AAAA,MAAK,cAAW,EAAE,SAAOC,MAAG,IAAED,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAEA,EAAC,GAAEC,MAAG,IAAED,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,MAAK,CAAC,GAAEA,EAAC,GAAEC,MAAG,GAAEA,MAAG,IAAE,KAAK,IAAI,EAAE,EAAE,MAAK,CAAC,IAAED,KAAE,EAAE,EAAE,MAAK,CAAC,IAAEA,EAAC;AAAG,QAAM,IAAE,IAAI,EAAG,EAAC,MAAK,OAAG,MAAK,OAAG,kBAAiB,EAAE,kBAAiB,OAAM,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAEC,EAAC,CAAC,EAAC,CAAC;AAAE,SAAO,EAAE,GAAEF,EAAC;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,cAAU,EAAE,SAAO,EAAE,UAAU,WAAS,SAASA,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAGE,GAAEF,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAGG,GAAEH,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,KAAG,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASH,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,OAAK,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,EAAE,CAAC,MAAI,EAAE,CAAC,MAAI,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAG,CAAC,EAAE,EAAE,CAAC,CAAC,MAAI,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,GAAI;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,EAAE,CAAC,aAAY,KAAG,EAAE,CAAC,aAAY,EAAE,QAAO,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAE,CAAC,CAAC,CAAC;AAAE,UAAG,EAAE,CAAC,aAAY,KAAG,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,UAAG,EAAE,CAAC,aAAY,KAAG,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,UAAG,SAAO,EAAE,CAAC,KAAG,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,YAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,eAAa,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAEC,QAAK,GAAGA,KAAE,GAAEA,EAAC,GAAEH,IAAEC,EAAC,GAAE,SAAOE,GAAE,CAAC,KAAG,SAAOA,GAAE,CAAC,IAAE,OAAK,EAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,QAAM,SAASF,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACE,IAAED,IAAE,MAAI;AAAC,YAAMI,KAAE,CAAC;AAAE,UAAG,OAAK,IAAE,GAAE,CAAC,GAAG,OAAO,OAAM,IAAI,EAAGN,IAAE,EAAG,yBAAwBC,EAAC;AAAE,UAAG,MAAI,EAAE,OAAO,KAAG,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAME,KAAE,GAAE,EAAE,CAAC,CAAC;AAAE,iBAAQJ,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,KAAI,KAAG,SAAOI,GAAEJ,EAAC,GAAE;AAAC,cAAG,EAAEI,GAAEJ,EAAC,aAAY,GAAG,OAAM,IAAI,EAAGC,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAAK,GAAE,KAAKH,GAAEJ,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC,OAAK;AAAC,YAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAE;AAAC,cAAG,EAAE,CAAC,aAAY,EAAE,QAAO,GAAE,EAAE,EAAE,CAAC,CAAC,GAAEC,GAAE,gBAAgB;AAAE,cAAG,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,gBAAM,IAAI,EAAGA,IAAE,EAAG,kBAAiBC,EAAC;AAAA,QAAC;AAAC;AAAC,gBAAME,KAAE,GAAE,EAAE,CAAC,EAAE,QAAQ,CAAC;AAAE,mBAAQJ,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,KAAI,KAAG,SAAOI,GAAEJ,EAAC,GAAE;AAAC,gBAAG,EAAEI,GAAEJ,EAAC,aAAY,GAAG,OAAM,IAAI,EAAGC,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAAK,GAAE,KAAKH,GAAEJ,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,UAAM,UAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,KAAI,KAAG,SAAO,EAAEA,EAAC,GAAE;AAAC,YAAG,EAAE,EAAEA,EAAC,aAAY,GAAG,OAAM,IAAI,EAAGC,IAAE,EAAG,kBAAiBC,EAAC;AAAE,QAAAK,GAAE,KAAK,EAAEP,EAAC,CAAC;AAAA,MAAC;AAAC,aAAO,MAAIO,GAAE,SAAO,OAAK,EAAEA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAASN,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACE,IAAED,IAAE,OAAK,GAAG,IAAE,GAAE,CAAC,GAAEF,IAAEC,EAAC,GAAE,SAAO,EAAE,CAAC,KAAG,SAAO,EAAE,CAAC,IAAE,EAAE,EAAE,CAAC,CAAC,IAAE,SAAO,EAAE,CAAC,IAAE,OAAK,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,sBAAoB,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACE,IAAED,IAAE,OAAK,GAAG,IAAE,GAAE,CAAC,GAAEF,IAAEC,EAAC,GAAE,SAAO,EAAE,CAAC,KAAG,SAAO,EAAE,CAAC,IAAE,OAAK,SAAO,EAAE,CAAC,IAAE,EAAE,EAAE,CAAC,CAAC,IAAE,SAAO,EAAE,CAAC,IAAE,EAAE,EAAE,CAAC,CAAC,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC,EAAG;AAAA,EAAC,GAAE,EAAE,UAAU,OAAK,SAASF,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,EAAE,EAAE,CAAC,aAAYO,OAAI,SAAO,EAAE,CAAC,EAAE,OAAM,IAAI,EAAGR,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAG,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,SAAO,EAAE,CAAC,IAAE,OAAK,EAAE,EAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,MAAI,SAASA,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACE,IAAE,GAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEN,IAAEC,EAAC,GAAE,EAAEK,GAAE,CAAC,aAAY,MAAK,SAAOA,GAAE,CAAC,EAAE,OAAM,IAAI,EAAGN,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAG,SAAOK,GAAE,CAAC,EAAE,QAAM,CAAC;AAAE,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGN,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,SAAOK,GAAE,CAAC,IAAE,CAAC,EAAEA,GAAE,CAAC,CAAC,CAAC,IAAEC,GAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,OAAK,SAASP,IAAEE,IAAE;AAAC,WAAO,EAAE,sBAAsBF,IAAEE,IAAG,OAAME,IAAE,GAAEG,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAEP,IAAEE,EAAC,GAAE,UAAQK,KAAE,GAAEA,EAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,MAAMG,GAAE,CAAC,EAAE,QAAQ,EAAE,EAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,OAAGP,GAAE,WAAW;AAAE,YAAGA,GAAE,YAAY,QAAQ,OAAM,IAAI,EAAGA,IAAE,EAAG,WAAUE,EAAC;AAAE,eAAOE;AAAA,MAAC;AAAC,UAAG,EAAEG,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,GAAEG,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAE,eAAO,SAAOI,KAAE,IAAE,EAAEA,IAAE,EAAE,EAAEG,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBE,EAAC;AAAE,aAAO,EAAEK,GAAE,CAAC,GAAE,EAAE,EAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,eAAa,SAASP,IAAEE,IAAE;AAAC,WAAO,EAAE,sBAAsBF,IAAEE,IAAG,OAAME,IAAE,GAAEG,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAEP,IAAEE,EAAC,GAAE,UAAQK,KAAE,GAAEA,EAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,MAAMG,GAAE,CAAC,EAAE,QAAQ,EAAE,EAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,MAAGP,GAAE,WAAW;AAAE,YAAGA,GAAE,YAAY,QAAQ,OAAM,IAAI,EAAGA,IAAE,EAAG,WAAUE,EAAC;AAAE,eAAOE;AAAA,MAAC;AAAC,UAAG,EAAEG,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,GAAEG,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAE,eAAO,SAAOI,KAAE,IAAE,EAAEA,IAAE,EAAE,EAAEG,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBE,EAAC;AAAE,aAAO,EAAEK,GAAE,CAAC,GAAE,EAAE,EAAEA,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,OAAMG,IAAE,GAAEG,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,UAAQM,KAAE,GAAEA,EAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,MAAMG,GAAE,CAAC,EAAE,UAAUJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,OAAGP,GAAE,WAAW;AAAE,YAAGA,GAAE,YAAY,QAAQ,OAAM,IAAI,EAAGA,IAAE,EAAG,WAAUC,EAAC;AAAE,eAAOG;AAAA,MAAC;AAAC,UAAG,EAAEG,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,GAAEG,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAE,eAAO,SAAOI,KAAE,IAAE,EAAEA,IAAED,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,EAAEM,GAAE,CAAC,GAAEJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,UAAQM,KAAE,GAAEA,EAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,GAAEG,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAE,eAAO,SAAOI,KAAE,IAAE,SAAKA,GAAE,OAAK,GAAGA,IAAED,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAEH,IAAED,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAM,SAAKM,GAAE,CAAC,EAAE,OAAK,GAAGA,GAAE,CAAC,GAAEJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAE,EAAEA,GAAE,CAAC,GAAEJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,iBAAe,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,OAAMG,IAAE,GAAEG,OAAI;AAAC,UAAG,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,UAAQM,KAAE,GAAEA,EAAC,GAAG,CAAC,EAAE,QAAO;AAAE,UAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,MAAMG,GAAE,CAAC,EAAE,UAAUJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,MAAGP,GAAE,WAAW;AAAE,YAAGA,GAAE,YAAY,QAAQ,OAAM,IAAI,EAAGA,IAAE,EAAG,WAAUC,EAAC;AAAE,eAAOG;AAAA,MAAC;AAAC,UAAG,EAAEG,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,GAAE;AAAC,cAAMH,KAAE,GAAEG,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAE,eAAO,SAAOI,KAAE,IAAE,EAAEA,IAAED,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,EAAEM,GAAE,CAAC,GAAEJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,MAAAA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC;AAAE,UAAIQ,KAAEF,GAAE,CAAC;AAAE,OAAC,EAAEA,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,OAAKE,KAAE,GAAEF,GAAE,CAAC,GAAEP,GAAE,gBAAgB;AAAG,UAAI,IAAEO,GAAE,CAAC;AAAE,WAAI,EAAEA,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,OAAK,IAAE,GAAEA,GAAE,CAAC,GAAEP,GAAE,gBAAgB,IAAG,EAAES,cAAa,GAAG,OAAM,IAAI,EAAGT,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAG,EAAE,aAAa,GAAG,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,EAAEQ,IAAE,GAAEN,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,mBAAiB,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,MAAAA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC;AAAE,YAAMS,KAAEH,GAAE,CAAC,GAAEI,KAAEJ,GAAE,CAAC;AAAE,UAAG,EAAEG,cAAa,GAAI,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAG,EAAEU,cAAa,GAAI,OAAM,IAAI,EAAGX,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMQ,KAAE,IAAI,EAAG,EAAC,OAAM,CAAC,GAAE,kBAAiBC,GAAE,iBAAgB,CAAC;AAAE,aAAOD,GAAE,QAAQ,CAACC,IAAEC,EAAC,CAAC,GAAE,EAAEF,IAAEN,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMS,KAAE,GAAEH,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMG,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAGS,MAAG,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAOM,GAAE,CAAC,aAAY,KAAIA,GAAE,CAAC,aAAY,IAAG,EAAEA,GAAE,CAAC,GAAEG,IAAEP,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,aAAYC,KAAE,EAAE,GAAGD,GAAE,CAAC,CAAC,GAAEG,IAAEP,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAEA,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,kBAAgB,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMS,KAAE,GAAEH,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMG,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAGS,MAAG,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAOM,GAAE,CAAC,aAAY,KAAIA,GAAE,CAAC,aAAY,IAAGK,GAAEL,GAAE,CAAC,GAAEG,IAAEP,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,aAAYC,KAAEI,GAAE,GAAGL,GAAE,CAAC,CAAC,GAAEG,IAAEP,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC,IAAEA,GAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEP,IAAEC,EAAC,GAAE,SAAOM,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMS,KAAE,GAAEH,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMG,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAOY,GAAEN,GAAE,CAAC,GAAEG,IAAE,GAAE,EAAEH,GAAE,CAAC,GAAE,IAAE,CAAC,GAAEJ,GAAE,EAAEI,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASN,IAAE,GAAE;AAAC,WAAO,EAAE,sBAAsBA,IAAE,GAAG,CAACG,IAAEG,IAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAET,IAAE,CAAC,GAAE,SAAOS,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGT,IAAE,EAAG,kBAAiB,CAAC;AAAE,YAAMU,KAAE,GAAED,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMC,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiB,CAAC;AAAE,aAAO,MAAIU,KAAE,EAAED,GAAE,CAAC,CAAC,IAAE,EAAEA,GAAE,CAAC,GAAEC,IAAER,GAAE,EAAEO,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,iBAAe,SAAST,IAAE,GAAE;AAAC,WAAO,EAAE,sBAAsBA,IAAE,GAAG,CAACG,IAAEG,IAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAET,IAAE,CAAC,GAAE,SAAOS,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGT,IAAE,EAAG,kBAAiB,CAAC;AAAE,YAAMU,KAAE,GAAED,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMC,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiB,CAAC;AAAE,aAAO,MAAIU,KAAE,EAAED,GAAE,CAAC,CAAC,IAAE,EAAEA,GAAE,CAAC,GAAEC,IAAER,GAAE,EAAEO,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAASV,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAE,GAAEM,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEV,IAAEC,EAAC,GAAE,SAAOS,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,KAAIA,GAAE,CAAC,aAAY,GAAI,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMU,KAAE,GAAED,GAAE,CAAC,CAAC;AAAE,UAAG,MAAMC,EAAC,EAAE,OAAM,IAAI,EAAGX,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMQ,KAAE,GAAE,EAAEC,GAAE,CAAC,GAAE,EAAE,CAAC;AAAE,UAAG,MAAMD,EAAC,EAAE,OAAM,IAAI,EAAGT,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAM,IAAE,GAAE,EAAES,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,UAAG,MAAM,CAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAOa,GAAEJ,GAAE,CAAC,GAAEC,IAAER,GAAE,EAAEO,GAAE,CAAC,GAAE,EAAE,CAAC,GAAE,GAAE,EAAEA,GAAE,CAAC,GAAE,OAAO,CAAC,EAAE,YAAY,GAAED,IAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,SAAO,SAAST,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC;AAAE,UAAIM,KAAE,EAAE,CAAC;AAAE,UAAG,SAAOA,GAAE,QAAO;AAAK,UAAG,EAAEA,cAAa,GAAG,OAAM,IAAI,EAAGP,IAAE,EAAG,kBAAiBC,EAAC;AAAE,MAAAM,cAAaC,OAAID,KAAE,EAAG,WAAWA,EAAC;AAAG,YAAMG,KAAE,GAAE,EAAE,CAAC,CAAC;AAAE,UAAG,MAAMA,EAAC,EAAE,OAAM,IAAI,EAAGV,IAAE,EAAG,kBAAiBC,EAAC;AAAE,YAAMU,KAAE,EAAE,EAAE,CAAC,GAAE,IAAI;AAAE,UAAG,SAAOA,GAAE,QAAO,EAAEJ,IAAEG,EAAC;AAAE,UAAGC,cAAa,EAAG,QAAO,EAAEJ,IAAEG,IAAEC,EAAC;AAAE,YAAM,IAAI,EAAGX,IAAE,EAAG,kBAAiBC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASA,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACE,IAAE,GAAEG,OAAI;AAAC,UAAGA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEN,IAAEC,EAAC,GAAE,SAAOK,GAAE,CAAC,EAAE,QAAO;AAAK,UAAI,IAAEA,GAAE,CAAC;AAAE,WAAI,EAAEA,GAAE,CAAC,CAAC,KAAG,EAAEA,GAAE,CAAC,CAAC,OAAK,IAAE,GAAEA,GAAE,CAAC,GAAEN,GAAE,gBAAgB,IAAG,SAAO,EAAE,QAAO;AAAK,UAAG,EAAE,aAAa,GAAG,OAAM,IAAI,EAAGA,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,aAAa,IAAG,GAAE,EAAEK,GAAE,CAAC,CAAC,GAAEN,GAAE,gBAAgB,IAAE,aAAa,IAAG,EAAE,WAAS,aAAa,IAAG,EAAE,CAAC,IAAE,aAAa,IAAGU,GAAE,CAAC,IAAE,aAAaH,KAAE,EAAE,SAAO;AAAA,IAAI,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,wBAAsB,SAASP,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,OAAME,IAAE,GAAEG,OAAI;AAAC,MAAAA,KAAE,GAAEA,EAAC,GAAE,EAAEA,IAAE,GAAE,GAAEN,IAAEC,EAAC;AAAE,YAAMQ,KAAE,CAAC;AAAE,UAAG,SAAOH,GAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAEA,GAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGN,IAAE,EAAG,kBAAiBC,EAAC;AAAE,UAAGK,GAAE,CAAC,aAAY,EAAG,QAAM,CAAC,GAAE,EAAEA,GAAE,CAAC,CAAC,GAAEN,GAAE,gBAAgB,CAAC;AAAE,UAAGM,GAAE,CAAC,aAAYC,GAAE,QAAM,CAAC,GAAE,EAAED,GAAE,CAAC,CAAC,GAAEN,GAAE,gBAAgB,CAAC;AAAE,YAAMU,KAAE,MAAM,EAAEJ,GAAE,CAAC,CAAC;AAAE,UAAGI,cAAa,GAAG;AAAC,cAAMP,KAAE,CAAC,GAAEJ,KAAE,CAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAEU,GAAE,MAAM,QAAOV,KAAI,KAAGU,GAAE,YAAYA,GAAE,MAAMV,EAAC,CAAC,GAAE;AAAC,gBAAMD,KAAEc,GAAG,EAAC,OAAM,CAACH,GAAE,MAAMV,EAAC,CAAC,GAAE,MAAK,SAAKU,GAAE,MAAK,MAAK,SAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAiB,OAAO,EAAC,CAAC;AAAE,UAAAP,GAAE,KAAKJ,EAAC;AAAA,QAAC,MAAM,CAAAA,GAAE,KAAK,EAAC,MAAKW,GAAE,MAAMV,EAAC,GAAE,IAAGU,GAAE,SAASV,IAAE,CAAC,EAAC,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,UAAQC,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,KAAGE,GAAEF,EAAC,EAAE,SAASF,GAAEC,EAAC,EAAE,EAAE,GAAE;AAAC,UAAAG,GAAEF,EAAC,EAAE,QAAQF,GAAEC,EAAC,EAAE,IAAI;AAAE;AAAA,QAAK;AAAC,eAAOG;AAAA,MAAC;AAAC,UAAGO,cAAa,GAAG;AAAC,cAAMP,KAAE,CAAC;AAAE,iBAAQJ,KAAE,GAAEA,KAAEW,GAAE,MAAM,QAAOX,MAAI;AAAC,gBAAMC,KAAEa,GAAG,EAAC,OAAM,CAACH,GAAE,MAAMX,EAAC,CAAC,GAAE,MAAK,SAAKW,GAAE,MAAK,MAAK,SAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAiB,OAAO,EAAC,CAAC;AAAE,UAAAP,GAAE,KAAKH,EAAC;AAAA,QAAC;AAAC,eAAOG;AAAA,MAAC;AAAC,UAAGG,GAAE,CAAC,aAAY,GAAG;AAAC,cAAMH,KAAE,GAAE,EAAEG,GAAE,CAAC,CAAC,GAAEN,GAAE,gBAAgB;AAAE,iBAAQD,KAAE,GAAEA,KAAEI,GAAE,OAAO,QAAOJ,KAAI,CAAAU,GAAE,KAAKN,GAAE,SAASJ,EAAC,CAAC;AAAE,eAAOU;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASV,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAM;AAAG,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAOc,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,WAAS,SAASf,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,EAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,aAAW,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,CAACG,IAAEF,IAAE,MAAI;AAAC,UAAG,IAAE,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC,GAAE,SAAO,EAAE,CAAC,EAAE,QAAO;AAAK,UAAG,EAAE,EAAE,CAAC,aAAY,GAAG,OAAM,IAAI,EAAGD,IAAE,EAAG,kBAAiBC,EAAC;AAAE,aAAO,EAAG,EAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC,GAAE,EAAE,UAAU,UAAQ,SAASD,IAAEC,IAAE;AAAC,WAAO,EAAE,sBAAsBD,IAAEC,IAAG,OAAMG,IAAEF,IAAE,MAAI;AAAC,QAAE,GAAE,GAAE,GAAEF,IAAEC,EAAC;AAAE,UAAI,IAAE,EAAE,EAAE,CAAC,GAAE,EAAE,GAAES,KAAE,SAAK;AAAE,UAAG,IAAE,SAAK,KAAG,UAAK,IAAE,KAAG,GAAE,CAAC,GAAE,MAAI,EAAE,UAAQ,EAAE,CAAC,aAAYV,IAAG;AAAC,YAAII,KAAE;AAAK,QAAAJ,GAAE,YAAUA,GAAE,SAAS,WAASI,KAAEJ,GAAE,SAAS,SAAQ,EAAE,SAAO,MAAII,KAAEJ,GAAG,EAAE,CAAC,GAAEI,EAAC;AAAG,cAAMH,KAAE,MAAMM,GAAGH,IAAE,GAAEM,EAAC;AAAE,YAAGT,IAAE;AAAC,gBAAMG,KAAE,KAAK,MAAM,KAAK,UAAUH,EAAC,CAAC;AAAE,qBAAUD,MAAI,CAAC,aAAY,WAAU,UAAU,EAAE,YAASI,GAAEJ,EAAC,KAAG,SAAOI,GAAEJ,EAAC,MAAII,GAAEJ,EAAC,IAAE,IAAI,KAAKI,GAAEJ,EAAC,CAAC;AAAG,iBAAO,EAAG,gCAAgCI,IAAE,GAAEJ,EAAC,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI;AAAC,UAAIW,KAAE;AAAK,UAAG,EAAE,EAAE,CAAC,CAAC,MAAIA,KAAE,EAAE,CAAC,IAAGA,IAAE;AAAC,YAAGD,KAAE,OAAG,EAAE,QAAO;AAAK,cAAMC,GAAE,KAAK;AAAE,cAAMP,KAAE,MAAMO,GAAE,mBAAmB;AAAE,YAAG,CAACP,IAAE;AAAC,cAAG,CAAC,GAAE;AAAC,kBAAMA,KAAE,MAAMO,GAAE,gBAAgB;AAAE,mBAAOP,KAAE,EAAG,gCAAgC,EAAC,UAASA,GAAC,GAAE,GAAEJ,EAAC,CAAC,IAAE;AAAA,UAAI;AAAC,iBAAO;AAAA,QAAI;AAAC,YAAIC,KAAE;AAAK,QAAAD,GAAE,YAAUA,GAAE,SAAS,WAASC,KAAED,GAAE,SAAS,SAAQC,KAAED,GAAG,IAAIA,GAAGI,EAAC,GAAEH,EAAC;AAAE,cAAMC,KAAE,MAAMK,GAAGN,IAAE,GAAES,EAAC;AAAE,YAAGR,IAAE;AAAC,gBAAME,KAAE,KAAK,MAAM,KAAK,UAAUF,EAAC,CAAC;AAAE,qBAAUF,MAAI,CAAC,aAAY,WAAU,UAAU,EAAE,YAASI,GAAEJ,EAAC,KAAG,SAAOI,GAAEJ,EAAC,MAAII,GAAEJ,EAAC,IAAE,IAAI,KAAKI,GAAEJ,EAAC,CAAC;AAAG,iBAAO,EAAG,gCAAgCI,IAAE,GAAEJ,EAAC,CAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI;AAAC,YAAM,IAAI,EAAGA,IAAE,EAAG,kBAAiBC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAE;", "names": ["t", "e", "r", "a", "n", "m", "p", "s", "w", "c", "l", "u", "U", "B", "v", "J"]}