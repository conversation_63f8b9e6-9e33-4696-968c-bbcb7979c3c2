import {
  s as s3
} from "./chunk-4W7HU754.js";
import {
  d,
  s as s2
} from "./chunk-6NIKJYUX.js";
import {
  f
} from "./chunk-7I556A2J.js";
import {
  u
} from "./chunk-SIWJOTKY.js";
import {
  r as r3
} from "./chunk-N35UHD63.js";
import {
  n
} from "./chunk-YBNKNHCD.js";
import {
  a as a2
} from "./chunk-QUHG7NMD.js";
import {
  g
} from "./chunk-TLKX5XIJ.js";
import {
  i
} from "./chunk-57XIOVP5.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import {
  $
} from "./chunk-JXLVNWKF.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import {
  E,
  x
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  has
} from "./chunk-REW33H3I.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/views/layers/support/MapServiceLayerViewHelper.js
var G = null;
function P(e2, t2) {
  return "tile" === t2.type || "map-image" === t2.type;
}
var S = class extends v {
  constructor(e2) {
    super(e2), this._featuresResolutions = /* @__PURE__ */ new WeakMap(), this.highlightGraphics = null, this.highlightGraphicUpdated = null, this.updateHighlightedFeatures = x(async (e3) => {
      this.destroyed || this.updatingHandles.addPromise(this._updateHighlightedFeaturesGeometries(e3).catch(() => {
      }));
    });
  }
  initialize() {
    const e2 = (e3) => {
      this.updatingHandles.addPromise(this._updateHighlightedFeaturesSymbols(e3).catch(() => {
      })), this.updateHighlightedFeatures(this._highlightGeometriesResolution);
    };
    this.addHandles([a2(() => this.highlightGraphics, "change", (t2) => e2(t2.added), { onListenerAdd: (t2) => e2(t2) })]);
  }
  async fetchPopupFeatures(e2, t2) {
    var _a, _b;
    const { layerView: { layer: r4, view: { scale: s4 } } } = this;
    if (!e2) throw new s("fetchPopupFeatures:invalid-area", "Nothing to fetch without area", { layer: r4 });
    const o = _(r4.sublayers, s4, t2);
    if (!o.length) return [];
    const a3 = await R(r4, o);
    if (!((((_b = (_a = r4.capabilities) == null ? void 0 : _a.operations) == null ? void 0 : _b.supportsIdentify) ?? true) && r4.version >= 10.5) && !a3) throw new s("fetchPopupFeatures:not-supported", "query operation is disabled for this service", { layer: r4 });
    return a3 ? this._fetchPopupFeaturesUsingQueries(e2, o, t2) : this._fetchPopupFeaturesUsingIdentify(e2, o, t2);
  }
  clearHighlights() {
    var _a;
    (_a = this.highlightGraphics) == null ? void 0 : _a.removeAll();
  }
  highlight(e2) {
    const r4 = this.highlightGraphics;
    if (!r4) return { remove() {
    } };
    let i2 = null;
    if (e2 instanceof g ? i2 = [e2] : j.isCollection(e2) && e2.length > 0 ? i2 = e2.toArray() : Array.isArray(e2) && e2.length > 0 && (i2 = e2), i2 = i2 == null ? void 0 : i2.filter(r), !i2 || !i2.length) return { remove: () => {
    } };
    for (const t2 of i2) {
      const e3 = t2.sourceLayer;
      null != e3 && "geometryType" in e3 && "point" === e3.geometryType && (t2.visible = false);
    }
    return r4.addMany(i2), { remove: () => {
      r4.removeMany(i2 ?? []);
    } };
  }
  async _updateHighlightedFeaturesSymbols(e2) {
    const { layerView: { view: t2 }, highlightGraphics: r4, highlightGraphicUpdated: s4 } = this;
    if (r4 && s4) for (const i2 of e2) {
      const e3 = i2.sourceLayer && "renderer" in i2.sourceLayer && i2.sourceLayer.renderer;
      i2.sourceLayer && "geometryType" in i2.sourceLayer && "point" === i2.sourceLayer.geometryType && e3 && "getSymbolAsync" in e3 && e3.getSymbolAsync(i2).then(async (o) => {
        var _a;
        o || (o = new y2());
        let a3 = null;
        const n2 = "visualVariables" in e3 ? (_a = e3.visualVariables) == null ? void 0 : _a.find((e4) => "size" === e4.type) : void 0;
        n2 && (G || (G = (await import("./visualVariableUtils-572M5EGS.js")).getSize), a3 = G(n2, i2, { view: t2.type, scale: t2.scale, shape: "simple-marker" === o.type ? o.style : null })), a3 || (a3 = "width" in o && "height" in o && null != o.width && null != o.height ? Math.max(o.width, o.height) : "size" in o ? o.size : 16), r4.includes(i2) && (i2.symbol = new y2({ style: "square", size: a3, xoffset: "xoffset" in o ? o.xoffset : 0, yoffset: "yoffset" in o ? o.yoffset : 0 }), s4(i2, "symbol"), i2.visible = true);
      });
    }
  }
  async _updateHighlightedFeaturesGeometries(e2) {
    const { layerView: { layer: t2, view: r4 }, highlightGraphics: s4, highlightGraphicUpdated: i2 } = this;
    if (this._highlightGeometriesResolution = e2, !i2 || !(s4 == null ? void 0 : s4.length) || !t2.capabilities.operations.supportsQuery) return;
    const a3 = this._getTargetResolution(e2), n2 = /* @__PURE__ */ new Map();
    for (const c of s4) if (!this._featuresResolutions.has(c) || this._featuresResolutions.get(c) > a3) {
      const e3 = c.sourceLayer;
      r2(n2, e3, () => /* @__PURE__ */ new Map()).set(c.getObjectId(), c);
    }
    const l = Array.from(n2, ([e3, t3]) => {
      const s5 = e3.createQuery();
      return s5.objectIds = [...t3.keys()], s5.outFields = [e3.objectIdField], s5.returnGeometry = true, s5.maxAllowableOffset = a3, s5.outSpatialReference = r4.spatialReference, e3.queryFeatures(s5);
    }), p = await Promise.all(l);
    if (!this.destroyed) for (const { features: o } of p) for (const e3 of o) {
      const t3 = e3.sourceLayer, r5 = n2.get(t3).get(e3.getObjectId());
      r5 && s4.includes(r5) && (r5.geometry = e3.geometry, i2(r5, "geometry"), this._featuresResolutions.set(r5, a3));
    }
  }
  _getTargetResolution(e2) {
    const t2 = e2 * $(this.layerView.view.spatialReference), r4 = t2 / 16;
    return r4 <= 10 ? 0 : e2 / t2 * r4;
  }
  async _fetchPopupFeaturesUsingIdentify(e2, t2, r4) {
    const s4 = await this._createIdentifyParameters(e2, t2, r4);
    if (t(s4)) return [];
    const { results: i2 } = await f(this.layerView.layer.parsedUrl, s4);
    return i2.map((e3) => e3.feature);
  }
  async _createIdentifyParameters(e2, t2, r4) {
    const { floors: s4, layer: i2, timeExtent: o, view: { spatialReference: n2, scale: l } } = this.layerView, p = r(r4) ? r4.event : null;
    if (!t2.length) return null;
    await Promise.all(t2.map(({ sublayer: e3 }) => e3.load().catch(() => {
    })));
    const c = Math.min(has("mapservice-popup-identify-max-tolerance"), i2.allSublayers.reduce((e3, t3) => t3.renderer ? s3({ renderer: t3.renderer, event: p }) : e3, 2)), u2 = this.createFetchPopupFeaturesQueryGeometry(e2, c), h = r3(l, n2), y3 = Math.round(u2.width / h), d2 = new w({ xmin: u2.center.x - h * y3, ymin: u2.center.y - h * y3, xmax: u2.center.x + h * y3, ymax: u2.center.y + h * y3, spatialReference: u2.spatialReference });
    return new u({ floors: s4, gdbVersion: "gdbVersion" in i2 ? i2.gdbVersion : void 0, geometry: e2, height: y3, layerOption: "popup", mapExtent: d2, returnGeometry: true, spatialReference: n2, sublayers: i2.sublayers, timeExtent: o, tolerance: c, width: y3 });
  }
  async _fetchPopupFeaturesUsingQueries(e2, t2, r4) {
    const { layerView: { floors: s4, timeExtent: i2 } } = this, o = r(r4) ? r4.event : null, n2 = t2.map(async ({ sublayer: t3, popupTemplate: r5 }) => {
      if (await t3.load().catch(() => {
      }), t3.capabilities && !t3.capabilities.operations.supportsQuery) return [];
      const n3 = t3.createQuery(), l = s3({ renderer: t3.renderer, event: o }), p = this.createFetchPopupFeaturesQueryGeometry(e2, l);
      if (n3.geometry = p, n3.outFields = await d(t3, r5), n3.timeExtent = i2, s4) {
        const e3 = s4.clone(), r6 = n(e3, t3);
        r(r6) && (n3.where = n3.where ? `(${n3.where}) AND (${r6})` : r6);
      }
      const c = this._getTargetResolution(p.width / l), u2 = await U(r5), h = "point" === t3.geometryType || u2 && u2.arcadeUtils.hasGeometryOperations(r5);
      h || (n3.maxAllowableOffset = c);
      let { features: y3 } = await t3.queryFeatures(n3);
      const m = h ? 0 : c;
      y3 = await A(t3, y3);
      for (const e3 of y3) this._featuresResolutions.set(e3, m);
      return y3;
    });
    return (await E(n2)).reverse().reduce((e3, t3) => t3.value ? [...e3, ...t3.value] : e3, []).filter((e3) => null != e3);
  }
};
function _(e2, t2, r4) {
  const s4 = [], i2 = (e3) => {
    const o = 0 === e3.minScale || t2 <= e3.minScale, n2 = 0 === e3.maxScale || t2 >= e3.maxScale;
    if (e3.visible && o && n2) {
      if (e3.sublayers) e3.sublayers.forEach(i2);
      else if (e3.popupEnabled) {
        const t3 = s2(e3, { ...r4, defaultPopupTemplateEnabled: false });
        r(t3) && s4.unshift({ sublayer: e3, popupTemplate: t3 });
      }
    }
  };
  return ((e2 == null ? void 0 : e2.toArray()) ?? []).reverse().map(i2), s4;
}
function U(e2) {
  var _a;
  return ((_a = e2.expressionInfos) == null ? void 0 : _a.length) || Array.isArray(e2.content) && e2.content.some((e3) => "expression" === e3.type) ? i() : Promise.resolve();
}
async function R(e2, t2) {
  var _a, _b;
  if ((_b = (_a = e2.capabilities) == null ? void 0 : _a.operations) == null ? void 0 : _b.supportsQuery) return true;
  try {
    return await Promise.any(t2.map(({ sublayer: e3 }) => e3.load().then(() => e3.capabilities.operations.supportsQuery)));
  } catch {
    return false;
  }
}
async function A(e2, t2) {
  const r4 = e2.renderer;
  return r4 && "defaultSymbol" in r4 && !r4.defaultSymbol && (t2 = r4.valueExpression ? await Promise.all(t2.map((e3) => r4.getSymbolAsync(e3).then((t3) => t3 ? e3 : null))).then((e3) => e3.filter((e4) => null != e4)) : t2.filter((e3) => null != r4.getSymbol(e3))), t2;
}
e([y({ constructOnly: true })], S.prototype, "createFetchPopupFeaturesQueryGeometry", void 0), e([y({ constructOnly: true })], S.prototype, "layerView", void 0), e([y({ constructOnly: true })], S.prototype, "highlightGraphics", void 0), e([y({ constructOnly: true })], S.prototype, "highlightGraphicUpdated", void 0), e([y({ constructOnly: true })], S.prototype, "updatingHandles", void 0), S = e([a("esri.views.layers.support.MapService")], S);

export {
  P,
  S
};
//# sourceMappingURL=chunk-MJS64MES.js.map
