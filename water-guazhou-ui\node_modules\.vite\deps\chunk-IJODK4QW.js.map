{"version": 3, "sources": ["../../@arcgis/core/layers/support/StreamConnection.js", "../../@arcgis/core/layers/graphics/sources/connections/WebSocketConnection.js", "../../@arcgis/core/layers/graphics/sources/connections/GeoEventConnection.js", "../../@arcgis/core/layers/support/ClientSideConnection.js", "../../@arcgis/core/layers/graphics/sources/connections/createConnection.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import e from\"../../core/Error.js\";import o from\"../../core/Evented.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";let c=class extends o.EventedAccessor{destroy(){this.emit(\"destroy\")}get connectionError(){return this.errorString?new e(\"stream-connection\",this.errorString):null}onFeature(r){this.emit(\"data-received\",r)}onMessage(r){this.emit(\"message-received\",r)}};r([t({readOnly:!0})],c.prototype,\"connectionError\",null),c=r([s(\"esri.layers.support.StreamConnection\")],c);const n=c;export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Error.js\";import o from\"../../../../core/Logger.js\";import{isSome as s,isNone as r}from\"../../../../core/maybe.js\";import{after as n,createResolver as c}from\"../../../../core/promiseUtils.js\";import{addQueryParameters as i}from\"../../../../core/urlUtils.js\";import{property as a}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as l}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{getGeometryZScaler as d}from\"../../../../geometry/support/zscale.js\";import h from\"../../../support/StreamConnection.js\";var g;!function(e){e[e.CONNECTING=0]=\"CONNECTING\",e[e.OPEN=1]=\"OPEN\",e[e.CLOSING=2]=\"CLOSING\",e[e.CLOSED=3]=\"CLOSED\"}(g||(g={}));let u=class extends h{constructor(e){super(),this._outstandingMessages=[],this.errorString=null;const{geometryType:t,spatialReference:o,sourceSpatialReference:s}=e;this._config=e,this._featureZScaler=d(t,s,o),this._open()}async _open(){await this._tryCreateWebSocket(),this.destroyed||await this._handshake()}destroy(){super.destroy(),s(this._websocket)&&(this._websocket.onopen=null,this._websocket.onclose=null,this._websocket.onerror=null,this._websocket.onmessage=null,this._websocket.close()),this._websocket=null}get connectionStatus(){if(r(this._websocket))return\"disconnected\";switch(this._websocket.readyState){case g.CONNECTING:case g.OPEN:return\"connected\";case g.CLOSING:case g.CLOSED:return\"disconnected\"}}sendMessageToSocket(e){r(this._websocket)?this._outstandingMessages.push(e):this._websocket.send(JSON.stringify(e))}sendMessageToClient(e){this._onMessage(e)}updateCustomParameters(e){this._config.customParameters=e,s(this._websocket)&&this._websocket.close()}async _tryCreateWebSocket(e=this._config.source.path,s=1e3,r=0){try{if(this.destroyed)return;const t=i(e,this._config.customParameters??{});this._websocket=await this._createWebSocket(t),this.notifyChange(\"connectionStatus\")}catch(c){const i=s/1e3;return this._config.maxReconnectionAttempts&&r>=this._config.maxReconnectionAttempts?(o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Exceeded maxReconnectionAttempts attempts. No further attempts will be made\")),void this.destroy()):(o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",`Failed to connect. Attempting to reconnect in ${i}s`,c)),await n(s),this._tryCreateWebSocket(e,Math.min(1.5*s,1e3*this._config.maxReconnectionInterval),r+1))}}_setWebSocketJSONParseHandler(e){e.onmessage=e=>{try{const t=JSON.parse(e.data);this._onMessage(t)}catch(s){return void o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Failed to parse message, invalid JSON\",{error:s}))}}}_createWebSocket(e){return new Promise(((t,o)=>{const s=new WebSocket(e);s.onopen=()=>{if(s.onopen=null,this.destroyed)return s.onclose=null,void s.close();s.onclose=e=>this._onClose(e),s.onerror=e=>this._onError(e),this._setWebSocketJSONParseHandler(s),t(s)},s.onclose=e=>{s.onopen=s.onclose=null,o(e)}}))}async _handshake(e=1e4){const s=this._websocket;if(r(s))return;const n=c(),i=s.onmessage,{filter:a,outFields:l,spatialReference:d}=this._config;return n.timeout(e),s.onmessage=e=>{let r=null;try{r=JSON.parse(e.data)}catch(c){}r&&\"object\"==typeof r||(o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Protocol violation. Handshake failed - malformed message\",e.data)),n.reject(),this.destroy()),r.spatialReference?.wkid!==d?.wkid&&(o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",`Protocol violation. Handshake failed - expected wkid of ${d.wkid}`,e.data)),n.reject(),this.destroy()),\"json\"!==r.format&&(o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Protocol violation. Handshake failed - format is not set\",e.data)),n.reject(),this.destroy()),a&&r.filter!==a&&o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Tried to set filter, but server doesn't support it\")),l&&r.outFields!==l&&o.getLogger(this.declaredClass).error(new t(\"websocket-connection\",\"Tried to set outFields, but server doesn't support it\")),s.onmessage=i;for(const t of this._outstandingMessages)s.send(JSON.stringify(t));this._outstandingMessages=[],n.resolve()},s.send(JSON.stringify({filter:a,outFields:l,format:\"json\",spatialReference:{wkid:d.wkid}})),n.promise}_onMessage(e){if(this.onMessage(e),\"type\"in e)switch(e.type){case\"features\":case\"featureResult\":for(const t of e.features)s(this._featureZScaler)&&this._featureZScaler(t.geometry),this.onFeature(t)}}_onError(e){const t=\"Encountered an error over WebSocket connection\";this._set(\"errorString\",t),o.getLogger(this.declaredClass).error(\"websocket-connection\",t)}_onClose(e){this._websocket=null,this.notifyChange(\"connectionStatus\"),1e3!==e.code&&o.getLogger(this.declaredClass).error(\"websocket-connection\",`WebSocket closed unexpectedly with error code ${e.code}`),this.destroyed||this._open()}};e([a()],u.prototype,\"connectionStatus\",null),e([a()],u.prototype,\"errorString\",void 0),u=e([l(\"esri.layers.graphics.sources.connections.WebSocketConnection\")],u);export{u as WebSocketConnection};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import\"../../../../geometry.js\";import t from\"../../../../request.js\";import r from\"../../../../core/Error.js\";import s from\"../../../../core/Logger.js\";import{isSome as o,isNone as i,unwrapOr as n,get as a}from\"../../../../core/maybe.js\";import{createResolver as c}from\"../../../../core/promiseUtils.js\";import{addQueryParameters as u}from\"../../../../core/urlUtils.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import\"../../../../core/has.js\";import{subclass as d}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{WebSocketConnection as l}from\"./WebSocketConnection.js\";import{executeQuery as f}from\"../../../../rest/query/operations/query.js\";import h from\"../../../../rest/support/Query.js\";import{fromJSON as g}from\"../../../../geometry/support/jsonUtils.js\";import y from\"../../../../geometry/SpatialReference.js\";const m=1e4,p={maxQueryDepth:5,maxRecordCountFactor:3};let _=class extends l{constructor(e){super({...p,...e}),this._buddyServicesQuery=null,this._relatedFeatures=null}async _open(){const e=await this._fetchServiceDefinition(this._config.source);e.timeInfo.trackIdField||s.getLogger(this.declaredClass).warn(\"GeoEvent service was configured without a TrackIdField. This may result in certain functionality being disabled. The purgeOptions.maxObservations property will have no effect.\");const t=this._fetchWebSocketUrl(e.streamUrls,this._config.spatialReference);this._buddyServicesQuery||(this._buddyServicesQuery=this._queryBuddyServices()),await this._buddyServicesQuery,await this._tryCreateWebSocket(t);const{filter:r,outFields:o}=this._config;this.destroyed||this._setFilter(r,o)}_onMessage(e){if(\"attributes\"in e){let i;try{i=this._enrich(e),o(this._featureZScaler)&&this._featureZScaler(i.geometry)}catch(t){return void s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Failed to parse message\",t))}this.onFeature(i)}else this.onMessage(e)}async _fetchServiceDefinition(e){const r={f:\"json\",...this._config.customParameters},s=t(e.path,{query:r,responseType:\"json\"}),o=(await s).data;return this._serviceDefinition=o,o}_fetchWebSocketUrl(e,t){const r=e[0],{urls:s,token:o}=r,i=this._inferWebSocketBaseUrl(s);return u(`${i}/subscribe`,{outSR:\"\"+t.wkid,token:o})}_inferWebSocketBaseUrl(e){if(1===e.length)return e[0];for(const t of e)if(t.includes(\"wss\"))return t;return s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Unable to infer WebSocket url\",e)),null}async _setFilter(e,t){const o=this._websocket;if(i(o)||i(e)&&i(t))return;const n=JSON.stringify({filter:this._serializeFilter(e,t)});let a=!1;const u=c(),d=()=>{a||(this.destroyed||this._websocket!==o||s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Server timed out when setting filter\")),u.reject())},l=e=>{const t=JSON.parse(e.data);t.filter&&(t.error&&(s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Failed to set service filter\",t.error)),this._set(\"errorString\",`Could not set service filter - ${t.error}`),u.reject(t.error)),this._setWebSocketJSONParseHandler(o),a=!0,u.resolve())};return o.onmessage=l,o.send(n),setTimeout(d,m),u.promise}_serializeFilter(e,t){const n={};if(i(e)&&i(t))return n;if(o(e)&&e.geometry)try{const t=g(e.geometry);if(\"extent\"!==t.type)throw new r(`Expected extent but found type ${t.type}`);n.geometry=JSON.stringify(t.shiftCentralMeridian())}catch(a){s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Encountered an error when setting connection geometryDefinition\",a))}return o(e)&&e.where&&\"1 = 1\"!==e.where&&\"1=1\"!==e.where&&(n.where=e.where),o(t)&&(n.outFields=t.join(\",\")),n}_enrich(e){if(!this._relatedFeatures)return e;const t=this._serviceDefinition.relatedFeatures.joinField,o=e.attributes[t],i=this._relatedFeatures.get(o);if(!i)return s.getLogger(this.declaredClass).warn(\"geoevent-connection\",\"Feature join failed. Is the join field configured correctly?\",e),e;const{attributes:n,geometry:a}=i;for(const r in n)e.attributes[r]=n[r];return a&&(e.geometry=a),e.geometry||e.centroid||s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Found malformed feature - no geometry found\",e)),e}async _queryBuddyServices(){try{const{relatedFeatures:e,keepLatestArchive:t}=this._serviceDefinition,r=this._queryRelatedFeatures(e),s=this._queryArchive(t);await r;const o=await s;if(!o)return;for(const i of o.features)this.onFeature(this._enrich(i))}catch(e){s.getLogger(this.declaredClass).error(new r(\"geoevent-connection\",\"Encountered an error when querying buddy services\",{error:e}))}}async _queryRelatedFeatures(e){if(!e)return;const t=await this._queryBuddy(e.featuresUrl);this._addRelatedFeatures(t)}async _queryArchive(e){if(e)return this._queryBuddy(e.featuresUrl)}async _queryBuddy(e){const t=new((await import(\"../../../FeatureLayer.js\")).default)({url:e}),{capabilities:r}=await t.load(),s=r.query.supportsMaxRecordCountFactor,o=r.query.supportsPagination,i=r.query.supportsCentroid,c=this._config.maxRecordCountFactor,u=t.capabilities.query.maxRecordCount,d=s?u*c:u,l=new h;if(l.outFields=n(this._config.outFields,[\"*\"]),l.where=n(a(this._config.filter,\"where\"),\"1=1\"),l.returnGeometry=!0,l.returnExceededLimitFeatures=!0,l.outSpatialReference=y.fromJSON(this._config.spatialReference),i&&(l.returnCentroid=!0),s&&(l.maxRecordCountFactor=c),o)return l.num=d,t.destroy(),this._queryPages(e,l);const g=await f(e,l,this._config.sourceSpatialReference);return t.destroy(),g.data}async _queryPages(e,t,r=[],s=0){t.start=o(t.num)?s*t.num:null;const{data:i}=await f(e,t,this._config.sourceSpatialReference);return i.exceededTransferLimit&&s<(this._config.maxQueryDepth??0)?(i.features.forEach((e=>r.push(e))),this._queryPages(e,t,r,s+1)):(r.forEach((e=>i.features.push(e))),i)}_addRelatedFeatures(e){const t=new Map,r=e.features,s=this._serviceDefinition.relatedFeatures.joinField;for(const o of r){const e=o.attributes[s];t.set(e,o)}this._relatedFeatures=t}};_=e([d(\"esri.layers.graphics.sources.connections.GeoEventConnection\")],_);const w=_;export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{isSome as r}from\"../../core/maybe.js\";import{property as t}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as o}from\"../../core/accessorSupport/decorators/subclass.js\";import{getGeometryZScaler as s}from\"../../geometry/support/zscale.js\";import c from\"./StreamConnection.js\";let a=class extends c{constructor(e){super(),this.connectionStatus=\"connected\",this.errorString=null;const{geometryType:r,spatialReference:t,sourceSpatialReference:o}=e;this._featureZScaler=s(r,o,t)}updateCustomParameters(e){}sendMessageToSocket(e){}sendMessageToClient(e){if(\"type\"in e)switch(e.type){case\"features\":case\"featureResult\":for(const t of e.features)r(this._featureZScaler)&&this._featureZScaler(t.geometry),this.onFeature(t)}this.onMessage(e)}};e([t()],a.prototype,\"connectionStatus\",void 0),e([t()],a.prototype,\"errorString\",void 0),a=e([o(\"esri.layers.support.ClientSideConnection\")],a);export{a as ClientSideConnection};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"./GeoEventConnection.js\";import{WebSocketConnection as t}from\"./WebSocketConnection.js\";import{ClientSideConnection as n}from\"../../../support/ClientSideConnection.js\";function o(o,r,s,i,c,a,m,p){const f={source:o,sourceSpatialReference:r,spatialReference:s,geometryType:i,filter:c,maxReconnectionAttempts:a,maxReconnectionInterval:m,customParameters:p};if(!o)return new n(f);return o.path.startsWith(\"wss://\")||o.path.startsWith(\"ws://\")?new t(f):new e(f)}export{o as createConnection};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkW,IAAIA,KAAE,cAAc,EAAE,gBAAe;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,IAAI,kBAAiB;AAAC,WAAO,KAAK,cAAY,IAAIC,GAAE,qBAAoB,KAAK,WAAW,IAAE;AAAA,EAAI;AAAA,EAAC,UAAUC,IAAE;AAAC,SAAK,KAAK,iBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,SAAK,KAAK,oBAAmBA,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEF,GAAE,WAAU,mBAAkB,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAEA,EAAC;AAAE,IAAMG,KAAEH;;;ACAM,IAAI;AAAE,CAAC,SAASI,IAAE;AAAC,EAAAA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAI,IAAE,cAAcC,GAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAM,GAAE,KAAK,uBAAqB,CAAC,GAAE,KAAK,cAAY;AAAK,UAAK,EAAC,cAAaE,IAAE,kBAAiBC,IAAE,wBAAuBC,GAAC,IAAEJ;AAAE,SAAK,UAAQA,IAAE,KAAK,kBAAgB,EAAEE,IAAEE,IAAED,EAAC,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,QAAO;AAAC,UAAM,KAAK,oBAAoB,GAAE,KAAK,aAAW,MAAM,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,EAAE,KAAK,UAAU,MAAI,KAAK,WAAW,SAAO,MAAK,KAAK,WAAW,UAAQ,MAAK,KAAK,WAAW,UAAQ,MAAK,KAAK,WAAW,YAAU,MAAK,KAAK,WAAW,MAAM,IAAG,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,IAAI,mBAAkB;AAAC,QAAG,EAAE,KAAK,UAAU,EAAE,QAAM;AAAe,YAAO,KAAK,WAAW,YAAW;AAAA,MAAC,KAAK,EAAE;AAAA,MAAW,KAAK,EAAE;AAAK,eAAM;AAAA,MAAY,KAAK,EAAE;AAAA,MAAQ,KAAK,EAAE;AAAO,eAAM;AAAA,IAAc;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAE;AAAC,MAAE,KAAK,UAAU,IAAE,KAAK,qBAAqB,KAAKA,EAAC,IAAE,KAAK,WAAW,KAAK,KAAK,UAAUA,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAE;AAAC,SAAK,QAAQ,mBAAiBA,IAAE,EAAE,KAAK,UAAU,KAAG,KAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBA,KAAE,KAAK,QAAQ,OAAO,MAAKI,KAAE,KAAIC,KAAE,GAAE;AAAC,QAAG;AAAC,UAAG,KAAK,UAAU;AAAO,YAAMH,KAAE,GAAEF,IAAE,KAAK,QAAQ,oBAAkB,CAAC,CAAC;AAAE,WAAK,aAAW,MAAM,KAAK,iBAAiBE,EAAC,GAAE,KAAK,aAAa,kBAAkB;AAAA,IAAC,SAAOI,IAAE;AAAC,YAAM,IAAEF,KAAE;AAAI,aAAO,KAAK,QAAQ,2BAAyBC,MAAG,KAAK,QAAQ,2BAAyB,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAID,GAAE,wBAAuB,6EAA6E,CAAC,GAAE,KAAK,KAAK,QAAQ,MAAI,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIA,GAAE,wBAAuB,iDAAiD,CAAC,KAAIE,EAAC,CAAC,GAAE,MAAM,EAAEF,EAAC,GAAE,KAAK,oBAAoBJ,IAAE,KAAK,IAAI,MAAII,IAAE,MAAI,KAAK,QAAQ,uBAAuB,GAAEC,KAAE,CAAC;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,8BAA8BL,IAAE;AAAC,IAAAA,GAAE,YAAU,CAAAA,OAAG;AAAC,UAAG;AAAC,cAAME,KAAE,KAAK,MAAMF,GAAE,IAAI;AAAE,aAAK,WAAWE,EAAC;AAAA,MAAC,SAAOE,IAAE;AAAC,eAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIA,GAAE,wBAAuB,yCAAwC,EAAC,OAAMA,GAAC,CAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBJ,IAAE;AAAC,WAAO,IAAI,QAAS,CAACE,IAAEC,OAAI;AAAC,YAAMC,KAAE,IAAI,UAAUJ,EAAC;AAAE,MAAAI,GAAE,SAAO,MAAI;AAAC,YAAGA,GAAE,SAAO,MAAK,KAAK,UAAU,QAAOA,GAAE,UAAQ,MAAK,KAAKA,GAAE,MAAM;AAAE,QAAAA,GAAE,UAAQ,CAAAJ,OAAG,KAAK,SAASA,EAAC,GAAEI,GAAE,UAAQ,CAAAJ,OAAG,KAAK,SAASA,EAAC,GAAE,KAAK,8BAA8BI,EAAC,GAAEF,GAAEE,EAAC;AAAA,MAAC,GAAEA,GAAE,UAAQ,CAAAJ,OAAG;AAAC,QAAAI,GAAE,SAAOA,GAAE,UAAQ,MAAKD,GAAEH,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWA,KAAE,KAAI;AAAC,UAAMI,KAAE,KAAK;AAAW,QAAG,EAAEA,EAAC,EAAE;AAAO,UAAMH,KAAE,EAAE,GAAE,IAAEG,GAAE,WAAU,EAAC,QAAOG,IAAE,WAAUC,IAAE,kBAAiB,EAAC,IAAE,KAAK;AAAQ,WAAOP,GAAE,QAAQD,EAAC,GAAEI,GAAE,YAAU,CAAAJ,OAAG;AAJ9tG;AAI+tG,UAAIK,KAAE;AAAK,UAAG;AAAC,QAAAA,KAAE,KAAK,MAAML,GAAE,IAAI;AAAA,MAAC,SAAOM,IAAE;AAAA,MAAC;AAAC,MAAAD,MAAG,YAAU,OAAOA,OAAI,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAID,GAAE,wBAAuB,4DAA2DJ,GAAE,IAAI,CAAC,GAAEC,GAAE,OAAO,GAAE,KAAK,QAAQ,MAAG,KAAAI,GAAE,qBAAF,mBAAoB,WAAO,uBAAG,UAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAID,GAAE,wBAAuB,2DAA2D,EAAE,IAAI,IAAGJ,GAAE,IAAI,CAAC,GAAEC,GAAE,OAAO,GAAE,KAAK,QAAQ,IAAG,WAASI,GAAE,WAAS,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAID,GAAE,wBAAuB,4DAA2DJ,GAAE,IAAI,CAAC,GAAEC,GAAE,OAAO,GAAE,KAAK,QAAQ,IAAGM,MAAGF,GAAE,WAASE,MAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIH,GAAE,wBAAuB,oDAAoD,CAAC,GAAEI,MAAGH,GAAE,cAAYG,MAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIJ,GAAE,wBAAuB,uDAAuD,CAAC,GAAEA,GAAE,YAAU;AAAE,iBAAUF,MAAK,KAAK,qBAAqB,CAAAE,GAAE,KAAK,KAAK,UAAUF,EAAC,CAAC;AAAE,WAAK,uBAAqB,CAAC,GAAED,GAAE,QAAQ;AAAA,IAAC,GAAEG,GAAE,KAAK,KAAK,UAAU,EAAC,QAAOG,IAAE,WAAUC,IAAE,QAAO,QAAO,kBAAiB,EAAC,MAAK,EAAE,KAAI,EAAC,CAAC,CAAC,GAAEP,GAAE;AAAA,EAAO;AAAA,EAAC,WAAWD,IAAE;AAAC,QAAG,KAAK,UAAUA,EAAC,GAAE,UAASA,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAW,KAAI;AAAgB,mBAAUE,MAAKF,GAAE,SAAS,GAAE,KAAK,eAAe,KAAG,KAAK,gBAAgBE,GAAE,QAAQ,GAAE,KAAK,UAAUA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,UAAME,KAAE;AAAiD,SAAK,KAAK,eAAcA,EAAC,GAAE,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,wBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,SAAK,aAAW,MAAK,KAAK,aAAa,kBAAkB,GAAE,QAAMA,GAAE,QAAM,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,wBAAuB,iDAAiDA,GAAE,IAAI,EAAE,GAAE,KAAK,aAAW,KAAK,MAAM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,8DAA8D,CAAC,GAAE,CAAC;;;ACA9oI,IAAM,IAAE;AAAR,IAAY,IAAE,EAAC,eAAc,GAAE,sBAAqB,EAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYS,IAAE;AAAC,UAAM,EAAC,GAAG,GAAE,GAAGA,GAAC,CAAC,GAAE,KAAK,sBAAoB,MAAK,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,MAAM,QAAO;AAAC,UAAMA,KAAE,MAAM,KAAK,wBAAwB,KAAK,QAAQ,MAAM;AAAE,IAAAA,GAAE,SAAS,gBAAc,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,iLAAiL;AAAE,UAAMC,KAAE,KAAK,mBAAmBD,GAAE,YAAW,KAAK,QAAQ,gBAAgB;AAAE,SAAK,wBAAsB,KAAK,sBAAoB,KAAK,oBAAoB,IAAG,MAAM,KAAK,qBAAoB,MAAM,KAAK,oBAAoBC,EAAC;AAAE,UAAK,EAAC,QAAOC,IAAE,WAAUC,GAAC,IAAE,KAAK;AAAQ,SAAK,aAAW,KAAK,WAAWD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAE;AAAC,QAAG,gBAAeA,IAAE;AAAC,UAAI;AAAE,UAAG;AAAC,YAAE,KAAK,QAAQA,EAAC,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,EAAE,QAAQ;AAAA,MAAC,SAAOC,IAAE;AAAC,eAAO,KAAK,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIG,GAAE,uBAAsB,2BAA0BH,EAAC,CAAC;AAAA,MAAC;AAAC,WAAK,UAAU,CAAC;AAAA,IAAC,MAAM,MAAK,UAAUD,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAwBA,IAAE;AAAC,UAAME,KAAE,EAAC,GAAE,QAAO,GAAG,KAAK,QAAQ,iBAAgB,GAAEE,KAAEC,GAAEL,GAAE,MAAK,EAAC,OAAME,IAAE,cAAa,OAAM,CAAC,GAAEC,MAAG,MAAMC,IAAG;AAAK,WAAO,KAAK,qBAAmBD,IAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE,CAAC,GAAE,EAAC,MAAKI,IAAE,OAAMD,GAAC,IAAED,IAAE,IAAE,KAAK,uBAAuBE,EAAC;AAAE,WAAO,GAAE,GAAG,CAAC,cAAa,EAAC,OAAM,KAAGH,GAAE,MAAK,OAAME,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBH,IAAE;AAAC,QAAG,MAAIA,GAAE,OAAO,QAAOA,GAAE,CAAC;AAAE,eAAUC,MAAKD,GAAE,KAAGC,GAAE,SAAS,KAAK,EAAE,QAAOA;AAAE,WAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIG,GAAE,uBAAsB,iCAAgCJ,EAAC,CAAC,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,WAAWA,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK;AAAW,QAAG,EAAEA,EAAC,KAAG,EAAEH,EAAC,KAAG,EAAEC,EAAC,EAAE;AAAO,UAAMK,KAAE,KAAK,UAAU,EAAC,QAAO,KAAK,iBAAiBN,IAAEC,EAAC,EAAC,CAAC;AAAE,QAAIM,KAAE;AAAG,UAAMC,KAAE,EAAE,GAAE,IAAE,MAAI;AAAC,MAAAD,OAAI,KAAK,aAAW,KAAK,eAAaJ,MAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIC,GAAE,uBAAsB,sCAAsC,CAAC,GAAEI,GAAE,OAAO;AAAA,IAAE,GAAEC,KAAE,CAAAT,OAAG;AAAC,YAAMC,KAAE,KAAK,MAAMD,GAAE,IAAI;AAAE,MAAAC,GAAE,WAASA,GAAE,UAAQ,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIG,GAAE,uBAAsB,gCAA+BH,GAAE,KAAK,CAAC,GAAE,KAAK,KAAK,eAAc,kCAAkCA,GAAE,KAAK,EAAE,GAAEO,GAAE,OAAOP,GAAE,KAAK,IAAG,KAAK,8BAA8BE,EAAC,GAAEI,KAAE,MAAGC,GAAE,QAAQ;AAAA,IAAE;AAAE,WAAOL,GAAE,YAAUM,IAAEN,GAAE,KAAKG,EAAC,GAAE,WAAW,GAAE,CAAC,GAAEE,GAAE;AAAA,EAAO;AAAA,EAAC,iBAAiBR,IAAEC,IAAE;AAAC,UAAMK,KAAE,CAAC;AAAE,QAAG,EAAEN,EAAC,KAAG,EAAEC,EAAC,EAAE,QAAOK;AAAE,QAAG,EAAEN,EAAC,KAAGA,GAAE,SAAS,KAAG;AAAC,YAAMC,KAAE,EAAED,GAAE,QAAQ;AAAE,UAAG,aAAWC,GAAE,KAAK,OAAM,IAAIG,GAAE,kCAAkCH,GAAE,IAAI,EAAE;AAAE,MAAAK,GAAE,WAAS,KAAK,UAAUL,GAAE,qBAAqB,CAAC;AAAA,IAAC,SAAOM,IAAE;AAAC,QAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAIH,GAAE,uBAAsB,mEAAkEG,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAEP,EAAC,KAAGA,GAAE,SAAO,YAAUA,GAAE,SAAO,UAAQA,GAAE,UAAQM,GAAE,QAAMN,GAAE,QAAO,EAAEC,EAAC,MAAIK,GAAE,YAAUL,GAAE,KAAK,GAAG,IAAGK;AAAA,EAAC;AAAA,EAAC,QAAQN,IAAE;AAAC,QAAG,CAAC,KAAK,iBAAiB,QAAOA;AAAE,UAAMC,KAAE,KAAK,mBAAmB,gBAAgB,WAAUE,KAAEH,GAAE,WAAWC,EAAC,GAAE,IAAE,KAAK,iBAAiB,IAAIE,EAAC;AAAE,QAAG,CAAC,EAAE,QAAO,EAAE,UAAU,KAAK,aAAa,EAAE,KAAK,uBAAsB,gEAA+DH,EAAC,GAAEA;AAAE,UAAK,EAAC,YAAWM,IAAE,UAASC,GAAC,IAAE;AAAE,eAAUL,MAAKI,GAAE,CAAAN,GAAE,WAAWE,EAAC,IAAEI,GAAEJ,EAAC;AAAE,WAAOK,OAAIP,GAAE,WAASO,KAAGP,GAAE,YAAUA,GAAE,YAAU,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAII,GAAE,uBAAsB,+CAA8CJ,EAAC,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAqB;AAAC,QAAG;AAAC,YAAK,EAAC,iBAAgBA,IAAE,mBAAkBC,GAAC,IAAE,KAAK,oBAAmBC,KAAE,KAAK,sBAAsBF,EAAC,GAAEI,KAAE,KAAK,cAAcH,EAAC;AAAE,YAAMC;AAAE,YAAMC,KAAE,MAAMC;AAAE,UAAG,CAACD,GAAE;AAAO,iBAAU,KAAKA,GAAE,SAAS,MAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AAAA,IAAC,SAAOH,IAAE;AAAC,QAAE,UAAU,KAAK,aAAa,EAAE,MAAM,IAAII,GAAE,uBAAsB,qDAAoD,EAAC,OAAMJ,GAAC,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBA,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAE,MAAM,KAAK,YAAYD,GAAE,WAAW;AAAE,SAAK,oBAAoBC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,cAAcD,IAAE;AAAC,QAAGA,GAAE,QAAO,KAAK,YAAYA,GAAE,WAAW;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYA,IAAE;AAAC,UAAMC,KAAE,KAAK,MAAM,OAAO,2CAA0B,GAAG,QAAS,EAAC,KAAID,GAAC,CAAC,GAAE,EAAC,cAAaE,GAAC,IAAE,MAAMD,GAAE,KAAK,GAAEG,KAAEF,GAAE,MAAM,8BAA6BC,KAAED,GAAE,MAAM,oBAAmB,IAAEA,GAAE,MAAM,kBAAiBQ,KAAE,KAAK,QAAQ,sBAAqBF,KAAEP,GAAE,aAAa,MAAM,gBAAe,IAAEG,KAAEI,KAAEE,KAAEF,IAAEC,KAAE,IAAIE;AAAE,QAAGF,GAAE,YAAU,EAAE,KAAK,QAAQ,WAAU,CAAC,GAAG,CAAC,GAAEA,GAAE,QAAM,EAAE,EAAE,KAAK,QAAQ,QAAO,OAAO,GAAE,KAAK,GAAEA,GAAE,iBAAe,MAAGA,GAAE,8BAA4B,MAAGA,GAAE,sBAAoB,EAAE,SAAS,KAAK,QAAQ,gBAAgB,GAAE,MAAIA,GAAE,iBAAe,OAAIL,OAAIK,GAAE,uBAAqBC,KAAGP,GAAE,QAAOM,GAAE,MAAI,GAAER,GAAE,QAAQ,GAAE,KAAK,YAAYD,IAAES,EAAC;AAAE,UAAMG,KAAE,MAAM,EAAEZ,IAAES,IAAE,KAAK,QAAQ,sBAAsB;AAAE,WAAOR,GAAE,QAAQ,GAAEW,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,YAAYZ,IAAEC,IAAEC,KAAE,CAAC,GAAEE,KAAE,GAAE;AAAC,IAAAH,GAAE,QAAM,EAAEA,GAAE,GAAG,IAAEG,KAAEH,GAAE,MAAI;AAAK,UAAK,EAAC,MAAK,EAAC,IAAE,MAAM,EAAED,IAAEC,IAAE,KAAK,QAAQ,sBAAsB;AAAE,WAAO,EAAE,yBAAuBG,MAAG,KAAK,QAAQ,iBAAe,MAAI,EAAE,SAAS,QAAS,CAAAJ,OAAGE,GAAE,KAAKF,EAAC,CAAE,GAAE,KAAK,YAAYA,IAAEC,IAAEC,IAAEE,KAAE,CAAC,MAAIF,GAAE,QAAS,CAAAF,OAAG,EAAE,SAAS,KAAKA,EAAC,CAAE,GAAE;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAE;AAAC,UAAMC,KAAE,oBAAI,OAAIC,KAAEF,GAAE,UAASI,KAAE,KAAK,mBAAmB,gBAAgB;AAAU,eAAUD,MAAKD,IAAE;AAAC,YAAMF,KAAEG,GAAE,WAAWC,EAAC;AAAE,MAAAH,GAAE,IAAID,IAAEG,EAAC;AAAA,IAAC;AAAC,SAAK,mBAAiBF;AAAA,EAAC;AAAC;AAAE,IAAE,EAAE,CAAC,EAAE,6DAA6D,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACA/hL,IAAIY,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,mBAAiB,aAAY,KAAK,cAAY;AAAK,UAAK,EAAC,cAAaC,IAAE,kBAAiBC,IAAE,wBAAuBC,GAAC,IAAEH;AAAE,SAAK,kBAAgB,EAAEC,IAAEE,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,QAAG,UAASA,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAA,MAAW,KAAI;AAAgB,mBAAUE,MAAKF,GAAE,SAAS,GAAE,KAAK,eAAe,KAAG,KAAK,gBAAgBE,GAAE,QAAQ,GAAE,KAAK,UAAUA,EAAC;AAAA,IAAC;AAAC,SAAK,UAAUF,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAEA,EAAC;;;ACAr1B,SAASM,GAAEA,IAAEC,IAAEC,IAAE,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,EAAC,QAAOP,IAAE,wBAAuBC,IAAE,kBAAiBC,IAAE,cAAa,GAAE,QAAOC,IAAE,yBAAwBC,IAAE,yBAAwBC,IAAE,kBAAiBC,GAAC;AAAE,MAAG,CAACN,GAAE,QAAO,IAAII,GAAEG,EAAC;AAAE,SAAOP,GAAE,KAAK,WAAW,QAAQ,KAAGA,GAAE,KAAK,WAAW,OAAO,IAAE,IAAI,EAAEO,EAAC,IAAE,IAAI,EAAEA,EAAC;AAAC;", "names": ["c", "s", "r", "n", "e", "n", "t", "o", "s", "r", "c", "a", "l", "e", "t", "r", "o", "s", "U", "n", "a", "u", "l", "c", "x", "g", "a", "n", "e", "r", "t", "o", "o", "r", "s", "c", "a", "m", "p", "f"]}