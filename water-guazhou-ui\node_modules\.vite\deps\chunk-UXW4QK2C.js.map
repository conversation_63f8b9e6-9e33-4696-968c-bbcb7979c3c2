{"version": 3, "sources": ["../../@arcgis/core/ground/NavigationConstraint.js", "../../@arcgis/core/Ground.js", "../../@arcgis/core/layers/support/editableLayers.js", "../../@arcgis/core/support/groundUtils.js", "../../@arcgis/core/Map.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../core/JSONSupport.js\";import\"../core/Logger.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import\"../core/Error.js\";import\"../core/has.js\";import{enumeration as e}from\"../core/accessorSupport/decorators/enumeration.js\";import{subclass as s}from\"../core/accessorSupport/decorators/subclass.js\";var t;let p=t=class extends o{constructor(r){super(r),this.type=\"none\"}clone(){return new t({type:this.type})}};r([e({none:\"none\",stayAbove:\"stay-above\"})],p.prototype,\"type\",void 0),p=t=r([s(\"esri.ground.NavigationConstraint\")],p);export{p as NavigationConstraint};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"./chunks/tslib.es6.js\";import e from\"./Color.js\";import o from\"./core/Collection.js\";import{referenceSetter as t}from\"./core/collectionUtils.js\";import{typeCast as s}from\"./core/compilerUtils.js\";import a from\"./core/Error.js\";import{JSONSupportMixin as i}from\"./core/JSONSupport.js\";import{clone as n}from\"./core/lang.js\";import l from\"./core/Loadable.js\";import{loadAll as p}from\"./core/loadAll.js\";import c from\"./core/Logger.js\";import{throwIfAborted as y,eachAlways as u}from\"./core/promiseUtils.js\";import{property as d}from\"./core/accessorSupport/decorators/property.js\";import{Integer as m}from\"./core/accessorSupport/ensureType.js\";import{subclass as f}from\"./core/accessorSupport/decorators/subclass.js\";import{writer as h}from\"./core/accessorSupport/decorators/writer.js\";import{NavigationConstraint as g}from\"./ground/NavigationConstraint.js\";import v from\"./views/3d/support/debugFlags.js\";import{transparencyToOpacity as j,opacityToTransparency as w}from\"./webdoc/support/opacityUtils.js\";var C;let S=C=class extends(i(l)){constructor(r){super(r),this.opacity=1,this.shading=!v.TERRAIN_USE_LEGACY_SHADING,this.surfaceColor=null,this.navigationConstraint=null,this.layers=new o;const e=r=>{r.parent&&r.parent!==this&&\"remove\"in r.parent&&r.parent.remove(r),r.parent=this,\"elevation\"!==r.type&&\"base-elevation\"!==r.type&&c.getLogger(this.declaredClass).error(`Layer '${r.title}, id:${r.id}' of type '${r.type}' is not supported as a ground layer and will therefore be ignored. Only layers of type 'elevation' are supported.`)},t=r=>{r.parent=null};this.layers.on(\"after-add\",(r=>e(r.item))),this.layers.on(\"after-remove\",(r=>t(r.item)))}initialize(){this.when().catch((r=>{c.getLogger(this.declaredClass).error(\"#load()\",\"Failed to load ground\",r)})),this.resourceInfo&&this.read(this.resourceInfo.data,this.resourceInfo.context)}destroy(){const r=this.layers.removeAll();for(const e of r)e.destroy();this.layers.destroy()}normalizeCtorArgs(r){return r&&\"resourceInfo\"in r&&(this._set(\"resourceInfo\",r.resourceInfo),delete(r={...r}).resourceInfo),r}set layers(r){this._set(\"layers\",t(r,this._get(\"layers\")))}writeLayers(r,e,o,t){const i=[];r?(t={...t,layerContainerType:\"ground\"},r.forEach((r=>{if(\"write\"in r){const e={};s(r)().write(e,t)&&i.push(e)}else t&&t.messages&&t.messages.push(new a(\"layer:unsupported\",`Layers (${r.title}, ${r.id}) of type '${r.declaredClass}' cannot be persisted in the ground`,{layer:r}))})),e.layers=i):e.layers=i}load(r){return this.addResolvingPromise(this._loadFromSource(r)),Promise.resolve(this)}loadAll(){return p(this,(r=>{r(this.layers)}))}async queryElevation(r,e){await this.load({signal:e?.signal});const{ElevationQuery:o}=await import(\"./layers/support/ElevationQuery.js\");y(e);const t=new o,s=this.layers.filter(A).toArray();return t.queryAll(s,r,e)}async createElevationSampler(r,e){await this.load({signal:e?.signal});const{ElevationQuery:o}=await import(\"./layers/support/ElevationQuery.js\");y(e);const t=new o,s=this.layers.filter(A).toArray();return t.createSamplerAll(s,r,e)}clone(){const r={opacity:this.opacity,surfaceColor:n(this.surfaceColor),navigationConstraint:n(this.navigationConstraint),layers:this.layers.slice()};return this.loaded&&(r.loadStatus=\"loaded\"),new C({resourceInfo:this.resourceInfo}).set(r)}read(r,e){this.resourceInfo||this._set(\"resourceInfo\",{data:r,context:e}),super.read(r,e)}_loadFromSource(r){const e=this.resourceInfo;return e?this._loadLayersFromJSON(e.data,e.context,r):Promise.resolve()}_loadLayersFromJSON(r,e,o){const t=e&&e.origin||\"web-scene\",s=e&&e.portal||null,a=e&&e.url||null;return import(\"./layers/support/layersCreator.js\").then((({populateOperationalLayers:e})=>{y(o);const i=[];if(r.layers&&Array.isArray(r.layers)){const o={context:{origin:t,url:a,portal:s,layerContainerType:\"ground\"},defaultLayerType:\"ArcGISTiledElevationServiceLayer\"};i.push(e(this.layers,r.layers,o))}return u(i)})).then((()=>{}))}};function I(r){return r&&\"createElevationSampler\"in r}function A(r){return\"elevation\"===r.type||I(r)}r([d({json:{read:!1}})],S.prototype,\"layers\",null),r([h(\"layers\")],S.prototype,\"writeLayers\",null),r([d({readOnly:!0})],S.prototype,\"resourceInfo\",void 0),r([d({type:Number,nonNullable:!0,range:{min:0,max:1},json:{type:m,read:{reader:j,source:\"transparency\"},write:{writer:(r,e)=>{e.transparency=w(r)},target:\"transparency\"}}})],S.prototype,\"opacity\",void 0),r([d({type:Boolean,nonNullable:!0,json:{read:!1}})],S.prototype,\"shading\",void 0),r([d({type:e,json:{type:[m],write:(r,e)=>{e.surfaceColor=r.toJSON().slice(0,3)}}})],S.prototype,\"surfaceColor\",void 0),r([d({type:g,json:{write:!0}})],S.prototype,\"navigationConstraint\",void 0),S=C=r([f(\"esri.Ground\")],S);const L=S;export{L as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{getEffectiveLayerCapabilities as i,getEffectiveEditingEnabled as t}from\"./layerUtils.js\";function n(n){return!(!n?.loaded||!i(n)?.operations?.supportsEditing||\"editingEnabled\"in n&&!t(n))}export{n as isEditableLayer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Ground.js\";import r from\"../core/Logger.js\";import{ensureType as o}from\"../core/accessorSupport/ensureType.js\";const i={\"world-elevation\":{id:\"worldElevation\",url:\"//elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/Terrain3D/ImageServer\",layerType:\"ArcGISTiledElevationServiceLayer\"},\"world-topobathymetry\":{id:\"worldTopoBathymetry\",url:\"//elevation3d.arcgis.com/arcgis/rest/services/WorldElevation3D/TopoBathy3D/ImageServer\",layerType:\"ArcGISTiledElevationServiceLayer\"}};function t(t){let a=null;if(\"string\"==typeof t)if(t in i){const r=i[t];a=new e({resourceInfo:{data:{layers:[r]}}})}else r.getLogger(\"esri.support.groundUtils\").warn(`Unable to find ground definition for: ${t}. Try \"world-elevation\"`);else a=o(e,t);return a}export{t as ensureType,i as groundElevationLayers};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"./chunks/tslib.es6.js\";import e from\"./Basemap.js\";import r from\"./Ground.js\";import t from\"./core/Accessor.js\";import o from\"./core/CollectionFlattener.js\";import a from\"./core/Evented.js\";import{isNone as i}from\"./core/maybe.js\";import{property as p}from\"./core/accessorSupport/decorators/property.js\";import{cast as l}from\"./core/accessorSupport/decorators/cast.js\";import\"./core/arrayUtils.js\";import{subclass as n}from\"./core/accessorSupport/decorators/subclass.js\";import{isEditableLayer as m}from\"./layers/support/editableLayers.js\";import{createCache as d,destroyCache as c,ensureType as y}from\"./support/basemapUtils.js\";import{createFlattenedTablesCollection as u}from\"./support/collectionUtils.js\";import{ensureType as h}from\"./support/groundUtils.js\";import{LayersMixin as b}from\"./support/LayersMixin.js\";import{TablesMixin as f}from\"./support/TablesMixin.js\";let j=class extends(f(b(a.EventedMixin(t)))){constructor(s){super(s),this.allLayers=new o({getCollections:()=>[this.basemap?.baseLayers,this.ground?.layers,this.layers,this.basemap?.referenceLayers],getChildrenFunction:s=>\"layers\"in s?s.layers:null}),this.allTables=u(this),this.basemap=null,this.editableLayers=new o({getCollections:()=>[this.allLayers],itemFilterFunction:m}),this.ground=new r,this._basemapCache=d()}destroy(){this.allLayers.destroy(),this.allTables.destroy(),this.editableLayers.destroy(),this.ground?.destroy(),this.basemap?.destroy(),c(this._basemapCache),this._basemapCache=null}castBasemap(s){return y(s,this._basemapCache)}castGround(s){const e=h(s);return i(e)?this._get(\"ground\"):e}findLayerById(s){return this.allLayers.find((e=>e.id===s))}findTableById(s){return this.allTables.find((e=>e.id===s))}};s([p({readOnly:!0,dependsOn:[]})],j.prototype,\"allLayers\",void 0),s([p({readOnly:!0})],j.prototype,\"allTables\",void 0),s([p({type:e})],j.prototype,\"basemap\",void 0),s([l(\"basemap\")],j.prototype,\"castBasemap\",null),s([p({readOnly:!0})],j.prototype,\"editableLayers\",void 0),s([p({type:r,nonNullable:!0})],j.prototype,\"ground\",void 0),s([l(\"ground\")],j.prototype,\"castGround\",null),j=s([n(\"esri.Map\")],j);const L=j;export{L as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgZ,IAAIA;AAAE,IAAIC,KAAED,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK;AAAA,EAAM;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAC,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,WAAU,aAAY,CAAC,CAAC,GAAEC,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC,GAAEC,EAAC;;;ACAwY,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,UAAQ,CAACC,GAAE,4BAA2B,KAAK,eAAa,MAAK,KAAK,uBAAqB,MAAK,KAAK,SAAO,IAAI;AAAE,UAAMC,KAAE,CAAAF,OAAG;AAAC,MAAAA,GAAE,UAAQA,GAAE,WAAS,QAAM,YAAWA,GAAE,UAAQA,GAAE,OAAO,OAAOA,EAAC,GAAEA,GAAE,SAAO,MAAK,gBAAcA,GAAE,QAAM,qBAAmBA,GAAE,QAAM,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,UAAUA,GAAE,KAAK,QAAQA,GAAE,EAAE,cAAcA,GAAE,IAAI,oHAAoH;AAAA,IAAC,GAAEC,KAAE,CAAAD,OAAG;AAAC,MAAAA,GAAE,SAAO;AAAA,IAAI;AAAE,SAAK,OAAO,GAAG,aAAa,CAAAA,OAAGE,GAAEF,GAAE,IAAI,CAAE,GAAE,KAAK,OAAO,GAAG,gBAAgB,CAAAA,OAAGC,GAAED,GAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,KAAK,EAAE,MAAO,CAAAA,OAAG;AAAC,QAAE,UAAU,KAAK,aAAa,EAAE,MAAM,WAAU,yBAAwBA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,gBAAc,KAAK,KAAK,KAAK,aAAa,MAAK,KAAK,aAAa,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,KAAK,OAAO,UAAU;AAAE,eAAUE,MAAKF,GAAE,CAAAE,GAAE,QAAQ;AAAE,SAAK,OAAO,QAAQ;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAE;AAAC,WAAOA,MAAG,kBAAiBA,OAAI,KAAK,KAAK,gBAAeA,GAAE,YAAY,GAAE,QAAOA,KAAE,EAAC,GAAGA,GAAC,GAAG,eAAcA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAOA,IAAE;AAAC,SAAK,KAAK,UAASG,GAAEH,IAAE,KAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEE,IAAEE,IAAEH,IAAE;AAAC,UAAMI,KAAE,CAAC;AAAE,IAAAL,MAAGC,KAAE,EAAC,GAAGA,IAAE,oBAAmB,SAAQ,GAAED,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAG,WAAUA,IAAE;AAAC,cAAME,KAAE,CAAC;AAAE,QAAAE,GAAEJ,EAAC,EAAE,EAAE,MAAME,IAAED,EAAC,KAAGI,GAAE,KAAKH,EAAC;AAAA,MAAC,MAAM,CAAAD,MAAGA,GAAE,YAAUA,GAAE,SAAS,KAAK,IAAIK,GAAE,qBAAoB,WAAWN,GAAE,KAAK,KAAKA,GAAE,EAAE,cAAcA,GAAE,aAAa,uCAAsC,EAAC,OAAMA,GAAC,CAAC,CAAC;AAAA,IAAC,CAAE,GAAEE,GAAE,SAAOG,MAAGH,GAAE,SAAOG;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAE;AAAC,WAAO,KAAK,oBAAoB,KAAK,gBAAgBA,EAAC,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAOO,GAAE,MAAM,CAAAP,OAAG;AAAC,MAAAA,GAAE,KAAK,MAAM;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,IAAEE,IAAE;AAAC,UAAM,KAAK,KAAK,EAAC,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,UAAK,EAAC,gBAAeE,GAAC,IAAE,MAAM,OAAO,8BAAoC;AAAE,MAAEF,EAAC;AAAE,UAAMD,KAAE,IAAIG,MAAEE,KAAE,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ;AAAE,WAAOL,GAAE,SAASK,IAAEN,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBF,IAAEE,IAAE;AAAC,UAAM,KAAK,KAAK,EAAC,QAAOA,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,UAAK,EAAC,gBAAeE,GAAC,IAAE,MAAM,OAAO,8BAAoC;AAAE,MAAEF,EAAC;AAAE,UAAMD,KAAE,IAAIG,MAAEE,KAAE,KAAK,OAAO,OAAO,CAAC,EAAE,QAAQ;AAAE,WAAOL,GAAE,iBAAiBK,IAAEN,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMF,KAAE,EAAC,SAAQ,KAAK,SAAQ,cAAa,EAAE,KAAK,YAAY,GAAE,sBAAqB,EAAE,KAAK,oBAAoB,GAAE,QAAO,KAAK,OAAO,MAAM,EAAC;AAAE,WAAO,KAAK,WAASA,GAAE,aAAW,WAAU,IAAI,EAAE,EAAC,cAAa,KAAK,aAAY,CAAC,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAEE,IAAE;AAAC,SAAK,gBAAc,KAAK,KAAK,gBAAe,EAAC,MAAKF,IAAE,SAAQE,GAAC,CAAC,GAAE,MAAM,KAAKF,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAE;AAAC,UAAME,KAAE,KAAK;AAAa,WAAOA,KAAE,KAAK,oBAAoBA,GAAE,MAAKA,GAAE,SAAQF,EAAC,IAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEE,IAAEE,IAAE;AAAC,UAAMH,KAAEC,MAAGA,GAAE,UAAQ,aAAYI,KAAEJ,MAAGA,GAAE,UAAQ,MAAKM,KAAEN,MAAGA,GAAE,OAAK;AAAK,WAAO,OAAO,6BAAmC,EAAE,KAAM,CAAC,EAAC,2BAA0BA,GAAC,MAAI;AAAC,QAAEE,EAAC;AAAE,YAAMC,KAAE,CAAC;AAAE,UAAGL,GAAE,UAAQ,MAAM,QAAQA,GAAE,MAAM,GAAE;AAAC,cAAMI,KAAE,EAAC,SAAQ,EAAC,QAAOH,IAAE,KAAIO,IAAE,QAAOF,IAAE,oBAAmB,SAAQ,GAAE,kBAAiB,mCAAkC;AAAE,QAAAD,GAAE,KAAKH,GAAE,KAAK,QAAOF,GAAE,QAAOI,EAAC,CAAC;AAAA,MAAC;AAAC,aAAO,EAAEC,EAAC;AAAA,IAAC,CAAE,EAAE,KAAM,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;AAAE,SAAS,EAAEL,IAAE;AAAC,SAAOA,MAAG,4BAA2BA;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAM,gBAAcA,GAAE,QAAM,EAAEA,EAAC;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,GAAE,MAAK,EAAC,MAAK,GAAE,MAAK,EAAC,QAAOA,IAAE,QAAO,eAAc,GAAE,OAAM,EAAC,QAAO,CAACA,IAAEE,OAAI;AAAC,EAAAA,GAAE,eAAaC,GAAEH,EAAC;AAAC,GAAE,QAAO,eAAc,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,aAAY,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKO,IAAE,MAAK,EAAC,MAAK,CAAC,CAAC,GAAE,OAAM,CAACP,IAAEE,OAAI;AAAC,EAAAA,GAAE,eAAaF,GAAE,OAAO,EAAE,MAAM,GAAE,CAAC;AAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKS,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,aAAa,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAngJ,SAASC,GAAEA,IAAE;AAJ7G;AAI8G,SAAM,EAAE,EAACA,MAAA,gBAAAA,GAAG,WAAQ,GAAC,WAAAC,GAAED,EAAC,MAAH,mBAAM,eAAN,mBAAkB,oBAAiB,oBAAmBA,MAAG,CAAC,EAAEA,EAAC;AAAE;;;ACAlE,IAAM,IAAE,EAAC,mBAAkB,EAAC,IAAG,kBAAiB,KAAI,wFAAuF,WAAU,mCAAkC,GAAE,wBAAuB,EAAC,IAAG,uBAAsB,KAAI,0FAAyF,WAAU,mCAAkC,EAAC;AAAE,SAASE,GAAEA,IAAE;AAAC,MAAIC,KAAE;AAAK,MAAG,YAAU,OAAOD,GAAE,KAAGA,MAAK,GAAE;AAAC,UAAME,KAAE,EAAEF,EAAC;AAAE,IAAAC,KAAE,IAAI,EAAE,EAAC,cAAa,EAAC,MAAK,EAAC,QAAO,CAACC,EAAC,EAAC,EAAC,EAAC,CAAC;AAAA,EAAC,MAAM,GAAE,UAAU,0BAA0B,EAAE,KAAK,yCAAyCF,EAAC,yBAAyB;AAAA,MAAO,CAAAC,KAAE,EAAE,GAAED,EAAC;AAAE,SAAOC;AAAC;;;ACAsI,IAAIE,KAAE,cAAcC,GAAE,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,YAAU,IAAID,GAAE,EAAC,gBAAe,MAAE;AAJx+B;AAI0+B,eAAC,UAAK,YAAL,mBAAc,aAAW,UAAK,WAAL,mBAAa,QAAO,KAAK,SAAO,UAAK,YAAL,mBAAc,eAAe;AAAA,OAAE,qBAAoB,CAAAC,OAAG,YAAWA,KAAEA,GAAE,SAAO,KAAI,CAAC,GAAE,KAAK,YAAUC,GAAE,IAAI,GAAE,KAAK,UAAQ,MAAK,KAAK,iBAAe,IAAIF,GAAE,EAAC,gBAAe,MAAI,CAAC,KAAK,SAAS,GAAE,oBAAmBG,GAAC,CAAC,GAAE,KAAK,SAAO,IAAI,KAAE,KAAK,gBAAcC,GAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAJxyC;AAIyyC,SAAK,UAAU,QAAQ,GAAE,KAAK,UAAU,QAAQ,GAAE,KAAK,eAAe,QAAQ,IAAE,UAAK,WAAL,mBAAa,YAAU,UAAK,YAAL,mBAAc,WAAUC,GAAE,KAAK,aAAa,GAAE,KAAK,gBAAc;AAAA,EAAI;AAAA,EAAC,YAAYJ,IAAE;AAAC,WAAOK,GAAEL,IAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMM,KAAEL,GAAED,EAAC;AAAE,WAAO,EAAEM,EAAC,IAAE,KAAK,KAAK,QAAQ,IAAEA;AAAA,EAAC;AAAA,EAAC,cAAcN,IAAE;AAAC,WAAO,KAAK,UAAU,KAAM,CAAAM,OAAGA,GAAE,OAAKN,EAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,KAAK,UAAU,KAAM,CAAAM,OAAGA,GAAE,OAAKN,EAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,WAAU,CAAC,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACE,GAAE,SAAS,CAAC,GAAEF,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,aAAY,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACE,GAAE,QAAQ,CAAC,GAAEF,GAAE,WAAU,cAAa,IAAI,GAAEA,KAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAEA,EAAC;AAAE,IAAMS,KAAET;", "names": ["t", "p", "r", "r", "t", "e", "n", "o", "i", "s", "l", "a", "p", "n", "v", "t", "a", "r", "j", "l", "s", "t", "n", "p", "y", "m", "e", "L"]}