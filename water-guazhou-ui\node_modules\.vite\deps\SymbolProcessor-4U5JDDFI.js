import {
  E,
  n,
  n2,
  o as o2,
  x
} from "./chunk-PRPYE2YO.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-RCQLO7DH.js";
import {
  i
} from "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  p as p2
} from "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import {
  p
} from "./chunk-6AXSIDWW.js";
import "./chunk-TFWV44LH.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-JCXMTMKU.js";
import {
  S
} from "./chunk-WAPZ634R.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-RURSJOSG.js";
import "./chunk-CNABSQRP.js";
import "./chunk-ZVGXJHEK.js";
import "./chunk-U6IEQ6CF.js";
import "./chunk-ZL54NZ7B.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-GDMKZBSE.js";
import {
  a as a2,
  m
} from "./chunk-KCSQWRUD.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-MCIIPWB6.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-V6NQCXYQ.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-UV4E33V4.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import {
  f2
} from "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e as e2
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a
} from "./chunk-R4CPW7J5.js";
import "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import {
  f,
  j
} from "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  c,
  e,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/features/support/ResourceManagerProxy.js
var t2 = class {
  constructor(e3) {
    this._remoteClient = e3, this._resourceMap = /* @__PURE__ */ new Map(), this._inFlightResourceMap = /* @__PURE__ */ new Map(), this.geometryEngine = null, this.geometryEnginePromise = null;
  }
  destroy() {
  }
  async fetchResource(t3, r2) {
    const s = this._resourceMap, i2 = s.get(t3);
    if (i2) return i2;
    let n3 = this._inFlightResourceMap.get(t3);
    if (n3) return n3;
    try {
      n3 = this._remoteClient.invoke("tileRenderer.fetchResource", { url: t3 }, { ...r2 }), this._inFlightResourceMap.set(t3, n3), n3.then((e3) => (this._inFlightResourceMap.delete(t3), s.set(t3, e3), e3));
    } catch (o3) {
      return j(o3) ? null : { width: 0, height: 0 };
    }
    return n3;
  }
  getResource(e3) {
    return this._resourceMap.get(e3) ?? null;
  }
};

// node_modules/@arcgis/core/views/2d/layers/features/processors/SymbolProcessor.js
function I(e3, t3) {
  return (!e3.minScale || e3.minScale >= t3) && (!e3.maxScale || e3.maxScale <= t3);
}
function v(e3) {
  const t3 = e3.message, r2 = { message: { data: {}, tileKey: t3.tileKey, tileKeyOrigin: t3.tileKeyOrigin, version: t3.version }, transferList: new Array() };
  for (const a3 in t3.data) {
    const e4 = t3.data[a3];
    if (r2.message.data[a3] = null, r(e4)) {
      const t4 = e4.stride, i2 = e4.indices.slice(0), o3 = e4.vertices.slice(0), n3 = e4.records.slice(0), l = { stride: t4, indices: i2, vertices: o3, records: n3, metrics: o(e4.metrics, (e5) => e5.slice(0)) };
      r2.transferList.push(i2, o3, n3), r2.message.data[a3] = l;
    }
  }
  return r2;
}
var M = class extends p {
  constructor() {
    super(...arguments), this.type = "symbol", this._matchers = { feature: null, aggregate: null }, this._bufferData = /* @__PURE__ */ new Map(), this._bufferIds = /* @__PURE__ */ new Map();
  }
  initialize() {
    this.handles.add([this.tileStore.on("update", this.onTileUpdate.bind(this))]), this._resourceManagerProxy = new t2(this.remoteClient);
  }
  destroy() {
    this._resourceManagerProxy.destroy();
  }
  get supportsTileUpdates() {
    return true;
  }
  forEachBufferId(e3) {
    this._bufferIds.forEach((t3) => {
      t3.forEach(e3);
    });
  }
  async update(e3, t3) {
    var _a;
    const s = t3.schema.processors[0];
    if ("symbol" !== s.type) return;
    const r2 = m(this._schema, s);
    (a2(r2, "mesh") || a2(r2, "target")) && (e3.mesh = true, (_a = e3.why) == null ? void 0 : _a.mesh.push("Symbology changed"), this._schema = s, this._factory = this._createFactory(s), this._factory.update(s, this.tileStore.tileScheme.tileInfo));
  }
  onTileMessage(e3, t3, s, r2) {
    return f(r2), this._onTileData(e3, t3, s, r2);
  }
  onTileClear(e3) {
    const t3 = { clear: true };
    return this._bufferData.delete(e3.key.id), this._bufferIds.delete(e3.key.id), this.remoteClient.invoke("tileRenderer.onTileData", { tileKey: e3.id, data: t3 });
  }
  onTileError(e3, t3, s) {
    const r2 = s.signal, i2 = { tileKey: e3.id, error: t3 };
    return this.remoteClient.invoke("tileRenderer.onTileError", i2, { signal: r2 });
  }
  onTileUpdate(e3) {
    for (const t3 of e3.removed) this._bufferData.has(t3.key.id) && this._bufferData.delete(t3.key.id), this._bufferIds.has(t3.key.id) && this._bufferIds.delete(t3.key.id);
    for (const t3 of e3.added) this._bufferData.forEach((e4) => {
      for (const s of e4) s.message.tileKey === t3.id && this._updateTileMesh("append", t3, v(s), [], false, false, null);
    });
  }
  _addBufferData(e3, t3) {
    var _a;
    this._bufferData.has(e3) || this._bufferData.set(e3, []), (_a = this._bufferData.get(e3)) == null ? void 0 : _a.push(v(t3));
  }
  _createFactory(e3) {
    const { geometryType: t3, objectIdField: r2, fields: i2 } = this.service, a3 = (e4, t4) => this.remoteClient.invoke("tileRenderer.getMaterialItems", e4, t4), o3 = { geometryType: t3, fields: i2, spatialReference: f2.fromJSON(this.spatialReference) }, n3 = new x(a3, this.tileStore.tileScheme.tileInfo), { matcher: l, aggregateMatcher: c2 } = e3.mesh;
    return this._store = n3, this._matchers.feature = o2(l, n3, o3, this._resourceManagerProxy), this._matchers.aggregate = o(c2, (e4) => o2(e4, n3, o3, this._resourceManagerProxy)), new n2(t3, r2, n3);
  }
  async _onTileData(e3, t3, s, a3) {
    var _a;
    f(a3);
    const { type: o3, addOrUpdate: l, remove: c2, clear: d, end: h } = t3, f3 = !!this._schema.mesh.sortKey;
    if (!l) {
      const t4 = { type: o3, addOrUpdate: null, remove: c2, clear: d, end: h, sort: f3 };
      return this.remoteClient.invoke("tileRenderer.onTileData", { tileKey: e3.id, data: t4 }, a3);
    }
    const m2 = this._processFeatures(e3, l, s, a3, (_a = t3.status) == null ? void 0 : _a.version);
    try {
      const s2 = await m2;
      if (t(s2)) {
        const t4 = { type: o3, addOrUpdate: null, remove: c2, clear: d, end: h, sort: f3 };
        return this.remoteClient.invoke("tileRenderer.onTileData", { tileKey: e3.id, data: t4 }, a3);
      }
      const n3 = [];
      for (const t4 of s2) {
        let s3 = false;
        const r2 = t4.message.bufferIds, a4 = e3.key.id, o4 = t4.message.tileKey;
        if (a4 !== o4 && r(r2)) {
          if (!this.tileStore.get(o4)) {
            this._addBufferData(a4, t4), n3.push(t4);
            continue;
          }
          let e4 = this._bufferIds.get(o4);
          e4 || (e4 = /* @__PURE__ */ new Set(), this._bufferIds.set(o4, e4));
          const i2 = Array.from(r2);
          for (const t5 of i2) {
            if (e4.has(t5)) {
              s3 = true;
              break;
            }
            e4.add(t5);
          }
        }
        s3 || (this._addBufferData(a4, t4), n3.push(t4));
      }
      await Promise.all(n3.map((s3) => {
        const r2 = e3.key.id === s3.message.tileKey, i2 = r2 ? t3.remove : [], n4 = r2 && t3.end;
        return this._updateTileMesh(o3, e3, s3, i2, n4, !!t3.clear, a3.signal);
      }));
    } catch (u) {
      this._handleError(e3, u, a3);
    }
  }
  async _updateTileMesh(e3, t3, r2, i2, a3, l, c2) {
    const d = e3, h = r2.message.tileKey, f3 = !!this._schema.mesh.sortKey;
    h !== t3.key.id && (a3 = false);
    const m2 = o(r2, (e4) => e4.message), u = o(r2, (e4) => e4.transferList) || [], g = { type: d, addOrUpdate: m2, remove: i2, clear: l, end: a3, sort: f3 }, p3 = { transferList: e(u) || [], signal: c2 };
    return f(p3), this.remoteClient.invoke("tileRenderer.onTileData", { tileKey: h, data: g }, p3);
  }
  async _processFeatures(e3, t3, s, i2, a3) {
    if (t(t3) || !t3.hasFeatures) return null;
    const o3 = { transform: e3.transform, hasZ: false, hasM: false }, l = this._factory, c2 = { viewingMode: "", scale: e3.scale }, d = await this._matchers.feature, h = await this._matchers.aggregate;
    f(i2);
    const f3 = this._getLabelInfos(e3, t3);
    return await l.analyze(t3.getCursor(), this._resourceManagerProxy, d, h, o3, c2), f(i2), this._writeFeatureSet(e3, t3, o3, f3, l, s, a3);
  }
  _writeFeatureSet(e3, t3, s, r2, a3, o3, n3) {
    const l = t3.getSize(), c2 = this._schema.mesh.matcher.symbologyType, d = new E(e3.key.id, { features: l, records: l, metrics: 0 }, c2, o3, c2 !== S.HEATMAP, n3), h = { viewingMode: "", scale: e3.scale }, f3 = t3.getCursor();
    for (; f3.next(); ) try {
      const t4 = f3.getDisplayId(), o4 = r(r2) ? r2.get(t4) : null;
      a3.writeCursor(d, f3, s, h, e3.level, o4, this._resourceManagerProxy);
    } catch (p3) {
    }
    const m2 = e3.tileInfoView.tileInfo.isWrappable;
    return d.serialize(m2);
  }
  _handleError(e3, t3, s) {
    if (!j(t3)) {
      const r2 = { tileKey: e3.id, error: t3.message };
      return this.remoteClient.invoke("tileRenderer.onTileError", r2, { signal: s.signal });
    }
    return Promise.resolve();
  }
  _getLabelingSchemaForScale(e3) {
    const t3 = this._schema.mesh.labels;
    if (t(t3)) return null;
    if ("subtype" === t3.type) {
      const s2 = { type: "subtype", classes: {} };
      let r2 = false;
      for (const i2 in t3.classes) {
        const a3 = t3.classes[i2].filter((t4) => I(t4, e3.scale));
        r2 = r2 || !!a3.length, s2.classes[i2] = a3;
      }
      return r2 ? s2 : null;
    }
    const s = t3.classes.filter((t4) => I(t4, e3.scale));
    return s.length ? { type: "simple", classes: s } : null;
  }
  _getLabels(e3, t3) {
    if ("subtype" === t3.type) {
      const s = this.service.subtypeField, r2 = c(s, "Expected to find subtype Field"), i2 = e3.readAttribute(r2);
      return null == i2 ? [] : t3.classes[i2] ?? [];
    }
    return t3.classes;
  }
  _getLabelInfos(e3, s) {
    const i2 = this._getLabelingSchemaForScale(e3);
    if (t(i2)) return null;
    const a3 = /* @__PURE__ */ new Map(), o3 = s.getCursor();
    for (; o3.next(); ) {
      const e4 = o3.getDisplayId(), s2 = [], r2 = p2(e4), n3 = r2 && 1 !== o3.readAttribute("cluster_count") ? "aggregate" : "feature", l = this._getLabels(o3, i2);
      for (const i3 of l) {
        if (i3.target !== n3) continue;
        const a4 = o3.getStorage(), l2 = r2 && "feature" === n3 ? a4.getComputedStringAtIndex(o3.readAttribute("referenceId"), i3.fieldIndex) : a4.getComputedStringAtIndex(e4, i3.fieldIndex);
        if (!l2) continue;
        const c2 = i(l2.toString()), d = c2[0], h = c2[1];
        this._store.getMosaicItem(i3.symbol, n(d)).then((e5) => {
          s2[i3.index] = { glyphs: e5.glyphMosaicItems ?? [], rtl: h, index: i3.index };
        });
      }
      a3.set(e4, s2);
    }
    return a3;
  }
};
M = e2([a("esri.views.2d.layers.features.processors.SymbolProcessor")], M);
var T = M;
export {
  T as default
};
//# sourceMappingURL=SymbolProcessor-4U5JDDFI.js.map
