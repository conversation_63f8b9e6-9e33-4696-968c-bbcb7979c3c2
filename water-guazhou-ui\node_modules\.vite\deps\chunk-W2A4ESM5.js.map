{"version": 3, "sources": ["../../@arcgis/core/widgets/Legend/styles/support/relationshipUtils.js", "../../@arcgis/core/renderers/support/numberUtils.js", "../../@arcgis/core/widgets/Legend/support/utils.js", "../../@arcgis/core/widgets/Legend/support/colorRampUtils.js", "../../@arcgis/core/widgets/Legend/support/heatmapRampUtils.js", "../../@arcgis/core/widgets/Legend/support/relationshipRampUtils.js", "../../@arcgis/core/symbols/support/symbolUtils.js", "../../@arcgis/core/renderers/support/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{generateFillAttributes as e,generateStrokeAttributes as l,renderDef as a,renderShape as r,getBoundingBox as t,computeBBox as s,getTransformMatrix as o}from\"../../../../symbols/support/svgUtils.js\";import{getCSSFilterFromEffectList as i}from\"../../../../symbols/support/utils.js\";import{isRTL as n,classes as d}from\"../../../support/widgetUtils.js\";import{tsx as m}from\"../../../support/jsxFactory.js\";const b=\"esri-relationship-ramp\",u=`${b}--diamond`,c=`${b}--square`,f=\"http://www.w3.org/2000/svg\",p={diamondContainer:`${u}__container`,diamondLeftCol:`${u}__left-column`,diamondRightCol:`${u}__right-column`,diamondMidCol:`${u}__middle-column`,diamondMidColLabel:`${u}__middle-column--label`,diamondMidColRamp:`${u}__middle-column--ramp`,squareTable:`${c}__table`,squareTableRow:`${c}__table-row`,squareTableCell:`${c}__table-cell`,squareTableLabel:`${c}__table-label`,squareTableLabelLeftBottom:`${c}__table-label--left-bottom`,squareTableLabelRightBottom:`${c}__table-label--right-bottom`,squareTableLabelLeftTop:`${c}__table-label--left-top`,squareTableLabelRightTop:`${c}__table-label--right-top`};function h(e,l,a,r){const{focus:t,labels:s}=e,o=!!t,i=T(e,l,a,r),b={justifyContent:\"center\"},u=n();return o?m(\"div\",{class:p.diamondContainer,styles:b},m(\"div\",{class:p.diamondLeftCol},u?s.right:s.left),m(\"div\",{class:p.diamondMidCol},m(\"div\",{class:p.diamondMidColLabel},s.top),i,m(\"div\",{class:p.diamondMidColLabel},s.bottom)),m(\"div\",{class:p.diamondRightCol},u?s.left:s.right)):m(\"div\",{class:p.squareTable},m(\"div\",{class:p.squareTableRow},m(\"div\",{class:d(p.squareTableCell,p.squareTableLabel,p.squareTableLabelRightBottom)},u?s.top:s.left),m(\"div\",{class:p.squareTableCell}),m(\"div\",{class:d(p.squareTableCell,p.squareTableLabel,p.squareTableLabelLeftBottom)},u?s.left:s.top)),m(\"div\",{class:p.squareTableRow},m(\"div\",{class:p.squareTableCell}),i,m(\"div\",{class:p.squareTableCell})),m(\"div\",{class:p.squareTableRow},m(\"div\",{class:d(p.squareTableCell,p.squareTableLabel,p.squareTableLabelRightTop)},u?s.right:s.bottom),m(\"div\",{class:p.squareTableCell}),m(\"div\",{class:d(p.squareTableCell,p.squareTableLabel,p.squareTableLabelLeftTop)},u?s.bottom:s.right)))}function k(e,l,a){const r=`${a}_arrowStart`,t=`${a}_arrowEnd`,s=\"left\"===e,o={markerStart:null,markerEnd:null};switch(l){case\"HL\":s?o.markerStart=`url(#${t})`:o.markerEnd=`url(#${r})`;break;case\"LL\":o.markerStart=`url(#${t})`;break;case\"LH\":s?o.markerEnd=`url(#${r})`:o.markerStart=`url(#${t})`;break;default:o.markerEnd=`url(#${r})`}return o}function T(n,d,b,u,c=60){const{focus:h,numClasses:T,colors:_,rotation:q}=n,$=!!h,g=Math.sqrt(c**2+c**2)+($?0:5),L=[],C=[],v=[],w=(c||75)/T;for(let s=0;s<T;s++){const o=s*w;for(let i=0;i<T;i++){const n=i*w,d=e(_[s][i]),m=l(null),b={type:\"rect\",x:n,y:o,width:w,height:w},u=a(d);u&&L.push(u);const c=r(b,d.fill,m,null);c&&C.push(c),v.push(t(b))}}const y=10,x=15,R=15,E=10;let M=null;$||(M=\"margin: -15px -15px -18px -15px\");const S=k(\"left\",h,d),j=k(\"right\",h,d),B=s(v),H=o(B,g,g,0,!1,q,!1),U=o(B,g,g,0,!1,$?-45:null,!1),W={filter:i(u),opacity:null==b?\"\":`${b}`};return m(\"div\",{styles:W,class:$?p.diamondMidColRamp:p.squareTableCell},m(\"svg\",{xmlns:f,width:g,height:g,style:M},m(\"defs\",null,m(\"marker\",{id:`${d}_arrowStart`,markerWidth:\"10\",markerHeight:\"10\",refX:\"5\",refY:\"5\",markerUnits:\"strokeWidth\",orient:\"auto\"},m(\"polyline\",{points:\"0,0 5,5 0,10\",fill:\"none\",stroke:\"#555555\",\"stroke-width\":\"1\"})),m(\"marker\",{id:`${d}_arrowEnd`,markerWidth:\"10\",markerHeight:\"10\",refX:\"0\",refY:\"5\",markerUnits:\"strokeWidth\",orient:\"auto\"},m(\"polyline\",{points:\"5,0 0,5 5,10\",fill:\"none\",stroke:\"#555555\",\"stroke-width\":\"1\"})),L),m(\"g\",{transform:H},C),m(\"g\",{transform:U},m(\"line\",{fill:\"none\",stroke:\"#555555\",\"stroke-width\":\"1\",\"marker-start\":S.markerStart,\"marker-end\":S.markerEnd,x1:-y,y1:c-x,x2:-y,y2:x}),m(\"line\",{fill:\"none\",stroke:\"#555555\",\"stroke-width\":\"1\",\"marker-start\":j.markerStart,\"marker-end\":j.markerEnd,x1:R,y1:c+E,x2:c-R,y2:c+E}))))}export{h as renderRelationshipRamp};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../intl.js\";import{formatNumber as t}from\"../../intl/number.js\";const n=/^-?(\\d+)(\\.(\\d+))?$/i;function e(t,n){return t-n}function r(t,n){let e,r;return e=Number(t.toFixed(n)),e<t?r=e+1/10**n:(r=e,e-=1/10**n),e=Number(e.toFixed(n)),r=Number(r.toFixed(n)),[e,r]}function o(t,n,e,r,o){const i=l(t,n,e,r),u=null==i.previous||i.previous<=o,s=null==i.next||i.next<=o;return u&&s||i.previous+i.next<=2*o}function i(t){const e=String(t),r=e.match(n);if(r&&r[1])return{integer:r[1].split(\"\").length,fractional:r[3]?r[3].split(\"\").length:0};if(e.toLowerCase().includes(\"e\")){const t=e.split(\"e\"),n=t[0],r=t[1];if(n&&r){const t=Number(n);let e=Number(r);const o=e>0;o||(e=Math.abs(e));const l=i(t);return o?(l.integer+=e,e>l.fractional?l.fractional=0:l.fractional-=e):(l.fractional+=e,e>l.integer?l.integer=1:l.integer-=e),l}}return{integer:0,fractional:0}}function l(t,n,e,r){const o={previous:null,next:null};if(null!=e){const r=t-e,i=n-e-r;o.previous=Math.floor(Math.abs(100*i/r))}if(null!=r){const e=r-t,i=r-n-e;o.next=Math.floor(Math.abs(100*i/e))}return o}function u(t,n={}){const l=t.slice(0),{tolerance:u=2,strictBounds:s=!1,indexes:c=l.map(((t,n)=>n))}=n;c.sort(e);for(let e=0;e<c.length;e++){const t=c[e],n=l[t],a=0===t?null:l[t-1],f=t===l.length-1?null:l[t+1],m=i(n).fractional;if(m){let i,c=0,g=!1;for(;c<=m&&!g;){const t=r(n,c);i=s&&0===e?t[1]:t[0],g=o(n,i,a,f,u),c++}g&&(l[t]=i)}}return l}const s={maximumFractionDigits:20};function c(n){return t(n,s)}export{c as format,i as numDigits,l as percentChange,u as round};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../symbols.js\";import{formatDate as A,convertDateFormatToIntlOptions as l}from\"../../../intl/date.js\";import{format as t}from\"../../../renderers/support/numberUtils.js\";import a from\"../../../symbols/SimpleLineSymbol.js\";const s=\"<\",i=\">\",r=l(\"short-date\");function o(l,a,o,n){let e=\"\";0===a?e=`${s} `:a===o&&(e=`${i} `);let u=null;return u=n?A(l,r):t(l),e+u}const n=[\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwcdIkqhiWn5fHwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu6JrzFUAAAAASUVORK5CYII=\",\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwaeIkqhiWl5/HwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu6sKxboAAAAASUVORK5CYII=\",\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAAAAAAAAAHqZRakAAAANUlEQVQ4jWPMy8v7z0BFwMLAwMAwadJEqhiWl5fPwEQVk5DAqIGjBo4aOGrgqIEQwEjtKgAATl0Hu75+IUcAAAAASUVORK5CYII=\"];async function e(A){if(!(\"visualVariables\"in A)||!A.visualVariables)return null;const l=A.visualVariables.find((A=>\"color\"===A.type));if(!l)return null;let t=null,a=null;if(l.stops){if(1===l.stops.length)return l.stops[0].color;t=l.stops[0].value,a=l.stops[l.stops.length-1].value}const s=null!=t&&null!=a?t+(a-t)/2:0,{getColor:i}=await import(\"../../../renderers/visualVariables/support/visualVariableUtils.js\");return i(l,s)??null}async function u(A,l){const t=A.trailCap,s=A.trailWidth||1,i=l||await e(A)||A.color;return new a({cap:t,color:i,width:s})}export{n as RGB_IMG_SOURCE,i as SPECIAL_CHARS_GREATER_THAN,s as SPECIAL_CHARS_LESS_THAN,o as createStopLabel,e as getMedianColor,u as getSymbolForFlowRenderer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport l from\"../../../Color.js\";import{createStopLabel as e}from\"./utils.js\";const o=new l([64,64,64]);function n(l,o){const n=[],r=l.length-1;return 5===l.length?n.push(0,2,4):n.push(0,r),l.map(((l,t)=>n.includes(t)?e(l,t,r,o):null))}async function r(l,e,o){let r=!1,a=[],u=[];if(l.stops){const e=l.stops;a=e.map((l=>l.value)),r=e.some((l=>!!l.label)),r&&(u=e.map((l=>l.label)))}const s=a[0],i=a[a.length-1];if(null==s&&null==i)return null;const c=r?null:n(a,o??!1);return(await Promise.all(a.map((async(o,n)=>({value:o,color:\"opacity\"===l.type?await t(o,l,e):(await import(\"../../../renderers/visualVariables/support/visualVariableUtils.js\")).getColor(l,o),label:r?u[n]:c?.[n]??\"\"}))))).reverse()}async function t(e,n,r=o){const t=new l(r),a=(await import(\"../../../renderers/visualVariables/support/visualVariableUtils.js\")).getOpacity(n,e);return null!=a&&(t.a=a),t}function a(l){let e=!1,o=[],r=[];o=l.map((l=>l.value)),e=l.some((l=>!!l.label)),e&&(r=l.map((l=>l.label??\"\")));const t=o[0],a=o[o.length-1];if(null==t&&null==a)return null;const s=e?null:n(o,!1);return o.map(((o,n)=>({value:o,color:u(o,l),label:e?r[n]:s?.[n]??\"\"}))).reverse()}function u(e,o){const{startIndex:n,endIndex:r,weight:t}=s(e,o);if(n===r)return o[n].color;const a=l.blendColors(o[n].color,o[r].color,t);return new l(a)}function s(l,e){let o=0,n=e.length-1;return e.some(((e,r)=>l<e.value?(n=r,!0):(o=r,!1))),{startIndex:o,endIndex:n,weight:(l-e[o].value)/(e[n].value-e[o].value)}}function i(e,o){let n=[];if(e&&\"multipart\"===e.type)e.colorRamps.reverse().forEach(((r,t)=>{0===t?n.push({value:o.max,color:new l(r.toColor),label:\"high\"}):n.push({value:null,color:new l(r.toColor),label:\"\"}),t===e.colorRamps.length-1?n.push({value:o.min,color:new l(r.fromColor),label:\"low\"}):n.push({value:null,color:new l(r.fromColor),label:\"\"})}));else{let r,t;e&&\"algorithmic\"===e.type?(r=e.fromColor,t=e.toColor):(r=[0,0,0,1],t=[255,255,255,1]),n=[{value:o.max,color:new l(t),label:\"high\"},{value:o.min,color:new l(r),label:\"low\"}]}return n}export{u as getColorFromPointCloudStops,r as getRampStops,a as getRampStopsForPointCloud,i as getStrectchRampStops};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport o from\"../../../renderers/support/HeatmapColorStop.js\";function r(r){if(!r.colorStops)return[];const e=[...r.colorStops].filter((o=>o.color?.a>0));let t=e.length-1;if(e&&e[0]){const r=e[t];r&&1!==r.ratio&&(e.push(new o({ratio:1,color:r.color})),t++)}return e.map(((o,e)=>{let l=\"\";return 0===e?l=r.legendOptions?.minLabel||\"low\":e===t&&(l=r.legendOptions?.maxLabel||\"high\"),{color:o.color,label:l,ratio:o.ratio}})).reverse()}export{r as getHeatmapRampStops};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clone as t}from\"../../../core/lang.js\";import{getStroke as e,getFill as o}from\"../../../symbols/support/gfxUtils.js\";import{getSymbolLayerFill as r}from\"../../../symbols/support/previewSymbol3D.js\";const s={HH:315,HL:45,LL:135,LH:225},l={2:[[\"HL\",\"HH\"],[\"LL\",\"LH\"]],3:[[\"HL\",\"HM\",\"HH\"],[\"ML\",\"MM\",\"MH\"],[\"LL\",\"LM\",\"LH\"]],4:[[\"HL\",\"HM1\",\"HM2\",\"HH\"],[\"M2L\",\"M2M1\",\"M2M2\",\"M2H\"],[\"M1L\",\"M1M1\",\"M1M2\",\"M1H\"],[\"LL\",\"LM1\",\"LM2\",\"LH\"]]};function n(t){if(!t)return;const{type:s}=t;if(s.includes(\"3d\"))return r(t.symbolLayers.getItemAt(0));if(\"simple-line\"===s){const o=e(t);return o&&o.color}if(\"simple-marker\"===t.type&&(\"x\"===t.style||\"cross\"===t.style)){const o=e(t);return o&&o.color}return o(t)}function H(t,e){const o=e.HH.label,r=e.LL.label,s=e.HL.label,l=e.LH.label;switch(t){case\"HH\":default:return{top:o,bottom:r,left:s,right:l};case\"HL\":return{top:s,bottom:l,left:r,right:o};case\"LL\":return{top:r,bottom:o,left:l,right:s};case\"LH\":return{top:l,bottom:s,left:o,right:r}}}function L(e,o){const r=[],s=e.length**.5,l=t(e),n=(o||\"HH\").split(\"\"),H=n[0],L=\"H\"===n[1];for(;l.length;){const t=[];for(;t.length<s;)t.push(l.shift());L&&t.reverse(),r.push(t)}return\"L\"===H&&r.reverse(),r}function i(t){const{focus:e,infos:o,numClasses:r}=t,s=l[r],L={};o.forEach((t=>{L[t.value]={label:t.label,fill:n(t.symbol)}}));const i=[];for(let l=0;l<r;l++){const t=[];for(let e=0;e<r;e++){const o=L[s[l][e]];t.push(o.fill)}i.push(t)}return{type:\"relationship-ramp\",numClasses:r,focus:e,colors:i,labels:H(e,L),rotation:u(e)}}function u(t){let e=s[t];return t&&null==e&&(e=s.HH),e||0}export{L as getRelationshipRampColors2D,i as getRelationshipRampElement,u as getRotationAngleForFocus};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import{isSome as e,isNone as t,unwrap as l}from\"../../core/maybe.js\";import{loadArcade as r}from\"../../support/arcadeOnDemand.js\";import{getStroke as i}from\"./gfxUtils.js\";import{SymbolSizeDefaults as s}from\"./previewUtils.js\";import{renderSymbol as o,renderOnce as n}from\"./renderUtils.js\";import{getCSSFilterFromEffectList as a,fetchWebStyleSymbol as c,applyColorToSymbol as u,applySizesToSymbol as f,applyRotationToSymbol as p,getColorFromSymbol as h,applyOpacityToColor as y}from\"./utils.js\";import{renderRelationshipRamp as d}from\"../../widgets/Legend/styles/support/relationshipUtils.js\";import{getRelationshipRampElement as m}from\"../../widgets/Legend/support/relationshipRampUtils.js\";let g=null;const b=[255,255,255];function w(e,t){return Math.floor(Math.random()*(t-e+1)+e)}function S(e,t,l){const{backgroundColor:r,outline:i,dotSize:o}=e,n=l&&l.swatchSize||s.size,a=.8,c=Math.round(n*n/o**2*a),u=window.devicePixelRatio,f=document.createElement(\"canvas\"),p=n*u;f.width=p,f.height=p,f.style.width=f.width/u+\"px\",f.style.height=f.height/u+\"px\";const h=f.getContext(\"2d\");if(r&&(h.fillStyle=r.toCss(!0),h.fillRect(0,0,p,p),h.fill()),h.fillStyle=t.toCss(!0),g&&g.length/2===c)for(let s=0;s<2*c;s+=2){const e=g[s],t=g[s+1];h.fillRect(e,t,o*u,o*u),h.fill()}else{g=[];for(let e=0;e<2*c;e+=2){const e=w(0,p),t=w(0,p);g.push(e,t),h.fillRect(e,t,o*u,o*u),h.fill()}}i&&(i.color&&(h.strokeStyle=i.color.toCss(!0)),h.lineWidth=i.width,h.strokeRect(0,0,p,p));const y=new Image(n,n);return y.src=f.toDataURL(),y}function v(e,t={}){const l=t.radius||40,r=2*Math.PI*l,s=e.length,n=r/s,a=[],c=i(t.outline);null!=c?.width&&(c.width*=2),(c||t.backgroundColor)&&a.push({shape:{type:\"circle\",cx:l,cy:l,r:l},fill:t.backgroundColor,stroke:c,offset:[0,0]});const u=t.values,f=u&&u.length===s&&100===u.reduce(((e,t)=>e+t),0),p=[0];for(let i=0;i<s;i++){let t=null;f&&(t=u[i]*r/100,p.push(t+p[i])),a.push({shape:{type:\"circle\",cx:l,cy:l,r:l/2},fill:[0,0,0,0],stroke:{width:l,dashArray:`${(t??n)/2} ${r}`,dashoffset:\"-\"+(f?p[i]/2:i*(n/2)),color:e[i]},offset:[0,0]})}let h=null;const y=2*l+(c?.width||0),d=t.holePercentage;if(d){c&&a.push({shape:{type:\"circle\",cx:l,cy:l,r:l*d},fill:null,stroke:c,offset:[0,0]});const e=y/2;h=[[{shape:{type:\"circle\",cx:e,cy:e,r:e},fill:b,stroke:c?{...c,color:b}:null,offset:[0,0]},{shape:{type:\"circle\",cx:e,cy:e,r:l*d},fill:[0,0,0],stroke:null,offset:[0,0]}]]}return o([a],[y,y],{effectView:t.effectList,ignoreStrokeWidth:!0,masking:h,rotation:-90})}function V(e,t={}){const l=e?.authoringInfo;if(!(\"relationship\"===l?.type)||!l?.numClasses||!e.uniqueValueInfos)return null;const{focus:r,numClasses:i}=l,s=m({focus:r,numClasses:i,infos:e.uniqueValueInfos}),o=t&&t.node||document.createElement(\"div\");return n(o,(()=>d(s,t.id||\"relationship\",t.opacity||1,t.effectList))),o}function x(e,t={}){const l=24,r=75,i=\"horizontal\"===t.align,s=i?r:l,o=i?l:r,{width:n=s,height:a=o,gradient:c=!0}=t,u=window.devicePixelRatio,f=n*u,p=a*u,h=document.createElement(\"canvas\");h.width=f,h.height=p,h.style.width=`${n}px`,h.style.height=`${a}px`;const y=h.getContext(\"2d\"),d=i?f:0,m=i?0:p;if(c){const t=y.createLinearGradient(0,0,d,m),l=e.length,r=1===l?0:1/(l-1);e.forEach(((e,l)=>t.addColorStop(l*r,e.toString()))),y.fillStyle=t,y.fillRect(0,0,f,p)}else{const t=i?f/e.length:f,l=i?p:p/e.length;let r=0,s=0;for(const o of e)y.fillStyle=o.toString(),y.fillRect(r,s,t,l),r=i?r+t:0,s=i?0:s+l}const g=document.createElement(\"div\");return g.style.width=`${n}px`,g.style.height=`${a}px`,k(g,t?.effectList),g.appendChild(h),g}function k(e,t){if(!t)return;e.style.filter=a(t);const l=t.effects;if(l)for(const r of l)if(\"drop-shadow\"===r?.type){r.offsetX<0?e.style.marginLeft=`${Math.abs(r.offsetX)}px`:e.style.marginRight=`${r.offsetX}px`;break}}async function C(e,t){switch(e.type){case\"web-style\":{const{previewWebStyleSymbol:l}=await import(\"./previewWebStyleSymbol.js\");return l(e,C,t)}case\"label-3d\":case\"line-3d\":case\"mesh-3d\":case\"point-3d\":case\"polygon-3d\":{const{previewSymbol3D:l}=await import(\"./previewSymbol3D.js\");return l(e,t)}case\"simple-marker\":case\"simple-line\":case\"simple-fill\":case\"picture-marker\":case\"picture-fill\":case\"text\":{const{previewSymbol2D:l}=await import(\"./previewSymbol2D.js\");return l(e,t)}case\"cim\":{const{previewCIMSymbol:l}=await import(\"./previewCIMSymbol.js\");return l(e,t)}default:return}}function j(e){return e&&\"opacity\"in e?e.opacity*j(e.parent):1}async function R(i,s){if(!i)return;const o=i.sourceLayer,n=(e(s)&&s.useSourceLayer?o:i.layer)??o,a=j(n);if(e(i.symbol)&&(!e(s)||!0!==s.ignoreGraphicSymbol)){const t=\"web-style\"===i.symbol.type?await c(i.symbol,{...s,cache:e(s)?s.webStyleCache:null}):i.symbol.clone();return u(t,null,a),t}const h=(e(s)?s.renderer:null)??(n&&\"renderer\"in n?n.renderer:null);let y=h&&\"getSymbolAsync\"in h?await h.getSymbolAsync(i,s):null;if(!y)return;if(y=\"web-style\"===y.type?await y.fetchSymbol({...s,cache:e(s)?s.webStyleCache:null}):y.clone(),!(h&&\"visualVariables\"in h&&h.visualVariables&&h.visualVariables.length))return u(y,null,a),y;if(\"arcadeRequiredForVisualVariables\"in h&&h.arcadeRequiredForVisualVariables&&(t(s)||t(s.arcade))){const e={...l(s)};e.arcade=await r(),s=e}const d=await import(\"../../renderers/visualVariables/support/visualVariableUtils.js\"),m=[],g=[],b=[],w=[];for(const e of h.visualVariables)switch(e.type){case\"color\":m.push(e);break;case\"opacity\":g.push(e);break;case\"rotation\":w.push(e);break;case\"size\":e.target||b.push(e)}const S=!!m.length&&m[m.length-1],v=S?d.getColor(S,i,s):null,V=!!g.length&&g[g.length-1];let x=V?d.getOpacity(V,i,s):null;if(null!=a&&(x=null!=x?x*a:a),u(y,v,x),b.length){const e=d.getAllSizes(b,i,s);await f(y,e)}for(const e of w)p(y,d.getRotationAngle(e,i,s),e.axis);return y}async function L(i,s){if(!i)return;const o=j(i.layer||i.sourceLayer);if(e(i.symbol)&&(!e(s)||!0!==s.ignoreGraphicSymbol)){const e=\"web-style\"===i.symbol.type?await c(i.symbol,s):i.symbol.clone();return h(e,o)}const n=e(s)&&s.renderer||i.get(\"layer.renderer\")||i.get(\"sourceLayer.renderer\");let a=await n.getSymbolAsync(i,s);if(!a)return;a=\"web-style\"===a.type?await c(a,s):a.clone();const u=h(a,o);if(!(\"visualVariables\"in n)||\"visualVariables\"in n&&!n.visualVariables||\"visualVariables\"in n&&!n.visualVariables?.length)return u;if(n.arcadeRequiredForVisualVariables&&(t(s)||t(s.arcade))){const e={...l(s)};e.arcade=await r(),s=e}const f=await import(\"../../renderers/visualVariables/support/visualVariableUtils.js\"),p=[],d=[];for(const e of n.visualVariables)switch(e.type){case\"color\":p.push(e);break;case\"opacity\":d.push(e)}const m=p.length>0?p[p.length-1]:null,g=m?f.getColor(m,i,s):u,b=d.length>0?d[d.length-1]:null;let w=b?f.getOpacity(b,i,s):null;return null!=o&&(w=null!=w?w*o:o),g?y(g,w):null}export{L as getDisplayedColor,R as getDisplayedSymbol,x as renderColorRampPreviewHTML,S as renderDotDensityPreviewHTML,v as renderPieChartPreviewHTML,C as renderPreviewHTML,V as renderRelationshipRampPreviewHTML};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unique as e}from\"../../core/arrayUtils.js\";import t from\"../../core/Logger.js\";import{getOrCreateMapValue as l}from\"../../core/MapUtils.js\";import{isSome as i}from\"../../core/maybe.js\";import{convertDateFormatToIntlOptions as o,formatDate as n}from\"../../intl/date.js\";import{format as a,round as r}from\"./numberUtils.js\";import s from\"../visualVariables/support/ColorStop.js\";import{getColorFromSymbol as u}from\"../../symbols/support/utils.js\";import{getRampStops as f}from\"../../widgets/Legend/support/colorRampUtils.js\";import{getHeatmapRampStops as d}from\"../../widgets/Legend/support/heatmapRampUtils.js\";const m=t.getLogger(\"esri.renderers.support.utils\"),p={lte:\"<=\",gte:\">=\",lt:\"<\",gt:\">\",pct:\"%\",ld:\"–\"},c={millisecond:0,second:1,minute:2,hour:3,day:4,month:5,year:6},y={millisecond:\"long-month-day-year-long-time\",second:\"long-month-day-year-long-time\",minute:\"long-month-day-year-short-time\",hour:\"long-month-day-year-short-time\",day:\"long-month-day-year\",month:\"long-month-day-year\",year:\"year\"},g=o(\"short-date\");async function h(e,t,i){l(e,t,(()=>[])).push(...i)}async function b(t){const l=new Map;if(!t)return l;if(\"visualVariables\"in t&&t.visualVariables){const e=t.visualVariables.filter((e=>\"color\"===e.type));for(const t of e){const e=(await f(t)??[]).map((e=>e.color));await h(l,t.field||t.valueExpression,e)}}if(\"heatmap\"===t.type){const e=d(t).map((e=>e.color));await h(l,t.field||t.valueExpression,e)}else if(\"pie-chart\"===t.type){for(const e of t.attributes)await h(l,e.field||e.valueExpression,[e.color]);await h(l,\"default\",[t?.othersCategory?.color,u(t.backgroundFillSymbol,null)])}else if(\"dot-density\"===t.type){for(const e of t.attributes)await h(l,e.field||e.valueExpression,[e.color]);await h(l,\"default\",[t.backgroundColor])}else if(\"unique-value\"===t.type)if(\"predominance\"===t.authoringInfo?.type)for(const e of t.uniqueValueInfos??[])await h(l,e.value.toString(),[u(e.symbol,null)]);else{const e=(t.uniqueValueInfos??[]).map((e=>u(e.symbol,null))),{field:i,field2:o,field3:n,valueExpression:a}=t;(i||a)&&await h(l,i||a,e),o&&await h(l,o,e),n&&await h(l,n,e)}else if(\"class-breaks\"===t.type){const e=t.classBreakInfos.map((e=>u(e.symbol,null))),{field:i,valueExpression:o}=t;await h(l,i??o,e)}else\"simple\"===t.type&&await h(l,\"default\",[u(t.symbol,null)]);return\"defaultSymbol\"in t&&t.defaultSymbol&&await h(l,\"default\",[u(t.defaultSymbol,null)]),l.forEach(((t,i)=>{const o=e(t.filter(Boolean),((e,t)=>JSON.stringify(e)===JSON.stringify(t)));l.set(i,o)})),l}function v(e,t,l){let i=\"\";return 0===t?i=p.lt+\" \":t===l&&(i=p.gt+\" \"),i+e}function x(e){const{values:t,colors:l,labelIndexes:i,isDate:o,dateFormatOptions:r}=e;return t.map(((e,u)=>{let f=null;if(!i||i.includes(u)){let l;l=o?n(e,r):a(e),l&&(f=v(l,u,t.length-1))}return new s({value:e,color:l[u],label:f})}))}function V(e){let t=e.minValue,l=e.maxValue;const i=e.isFirstBreak?\"\":p.gt+\" \",o=\"percent-of-total\"===e.normalizationType?p.pct:\"\";return t=null==t?\"\":a(t),l=null==l?\"\":a(l),i+t+o+\" \"+p.ld+\" \"+l+o}function w(e){const t=e.classBreakInfos,l=e.normalizationType;let i=[];if(t&&t.length)if(\"standard-deviation\"!==e.classificationMethod)if(e.round){i.push(t[0].minValue);for(const e of t)i.push(e.maxValue);i=r(i),t.forEach(((e,t)=>{e.label=V({minValue:0===t?i[0]:i[t],maxValue:i[t+1],isFirstBreak:0===t,normalizationType:l})}))}else t.forEach(((e,t)=>{e.label=V({minValue:e.minValue,maxValue:e.maxValue,isFirstBreak:0===t,normalizationType:l})}));else m.warn(\"setLabelsForClassBreaks\",\"cannot set labels for class breaks generated using 'standard-deviation' method.\")}function F(e){const t=e.map((e=>new Date(e))),l=t.length;let i=1/0,o=null;for(let n=0;n<l-1;n++){const e=t[n];let a=1/0,r=null;for(let i=n+1;i<l;i++){const l=t[i],o=(e.getFullYear()!==l.getFullYear()?\"year\":e.getMonth()!==l.getMonth()&&\"month\")||e.getDate()!==l.getDate()&&\"day\"||e.getHours()!==l.getHours()&&\"hour\"||e.getMinutes()!==l.getMinutes()&&\"minute\"||e.getSeconds()!==l.getSeconds()&&\"second\"||\"millisecond\",n=c[o];n<a&&(a=n,r=o)}a<i&&(i=a,o=r)}return o}function z(e){const{value:t,domain:l,fieldInfo:i,dateFormatInterval:r}=e;let s=String(t);const u=l&&\"codedValues\"in l&&l.codedValues?l.getName(t):null;return u?s=u:\"number\"==typeof t&&(s=i&&\"date\"===i.type?n(t,r&&o(y[r])):a(t)),s}function E(e,t){return\"normalizationField\"in e&&e.normalizationField?I(e.field,e.normalizationField):\"field\"in e&&e.field?k(e.field):\"valueExpression\"in e&&e.valueExpression?S(e.valueExpression,e.valueExpressionTitle,t):null}function k(e){return{type:\"field\",field:e}}function I(e,t){return{type:\"normalized-field\",field:e,normalizationField:t}}function S(e,t,l){return{type:\"expression\",expression:e,title:t,returnType:l}}function j(t,l){const o=[];if(\"class-breaks\"===t.type||\"heatmap\"===t.type)o.push(E(t,\"number\"));else if(\"unique-value\"===t.type){const e=t.authoringInfo;if(e&&\"relationship\"===e.type){if(e.field1&&e.field2){const t=e.field1.field,l=e.field2.field,i=e.field1.normalizationField,n=e.field2.normalizationField;o.push(E({field:t,normalizationField:i})),o.push(E({field:l,normalizationField:n}))}}else{const e=t.uniqueValueInfos?.[0];let l=null;if(e&&e.value){const e=typeof t.uniqueValueInfos[0].value;\"string\"!==e&&\"number\"!==e||(l=e)}o.push(E(t,l)),[t.field2,t.field3].forEach((e=>e&&o.push(k(e))))}}else\"attributes\"in t&&t.attributes?.forEach((e=>o.push(E(e,\"number\"))));const n=l?l(t):\"visualVariables\"in t?t.visualVariables:null;return n&&n.forEach((e=>o.push(E(e,\"number\")))),e(o.filter(i),((e,t)=>\"field\"===e.type&&\"field\"===t.type?e.field===t.field:\"normalized-field\"===e.type&&\"normalized-field\"===t.type?e.field===t.field&&e.normalizationField===t.normalizationField:\"expression\"===e.type&&\"expression\"===t.type&&e.expression===t.expression))}export{F as calculateDateFormatInterval,V as createClassBreakLabel,x as createColorStops,z as createUniqueValueLabel,E as getAttribute,j as getAttributes,b as getColorsFromRenderer,w as setLabelsForClassBreaks,g as timelineDateFormatOptions};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwZ,IAAM,IAAE;AAAR,IAAiCA,KAAE,GAAG,CAAC;AAAvC,IAAmD,IAAE,GAAG,CAAC;AAAzD,IAAoEC,KAAE;AAAtE,IAAmGC,KAAE,EAAC,kBAAiB,GAAGF,EAAC,eAAc,gBAAe,GAAGA,EAAC,iBAAgB,iBAAgB,GAAGA,EAAC,kBAAiB,eAAc,GAAGA,EAAC,mBAAkB,oBAAmB,GAAGA,EAAC,0BAAyB,mBAAkB,GAAGA,EAAC,yBAAwB,aAAY,GAAG,CAAC,WAAU,gBAAe,GAAG,CAAC,eAAc,iBAAgB,GAAG,CAAC,gBAAe,kBAAiB,GAAG,CAAC,iBAAgB,4BAA2B,GAAG,CAAC,8BAA6B,6BAA4B,GAAG,CAAC,+BAA8B,yBAAwB,GAAG,CAAC,2BAA0B,0BAAyB,GAAG,CAAC,2BAA0B;AAAE,SAAS,EAAEG,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEL,IAAEM,KAAE,CAAC,CAACF,IAAEG,KAAEC,GAAER,IAAEC,IAAEC,IAAEC,EAAC,GAAEM,KAAE,EAAC,gBAAe,SAAQ,GAAEZ,KAAE,EAAE;AAAE,SAAOS,KAAE,EAAE,OAAM,EAAC,OAAMP,GAAE,kBAAiB,QAAOU,GAAC,GAAE,EAAE,OAAM,EAAC,OAAMV,GAAE,eAAc,GAAEF,KAAEQ,GAAE,QAAMA,GAAE,IAAI,GAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,cAAa,GAAE,EAAE,OAAM,EAAC,OAAMA,GAAE,mBAAkB,GAAEM,GAAE,GAAG,GAAEE,IAAE,EAAE,OAAM,EAAC,OAAMR,GAAE,mBAAkB,GAAEM,GAAE,MAAM,CAAC,GAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,gBAAe,GAAEF,KAAEQ,GAAE,OAAKA,GAAE,KAAK,CAAC,IAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,YAAW,GAAE,EAAE,OAAM,EAAC,OAAMA,GAAE,eAAc,GAAE,EAAE,OAAM,EAAC,OAAM,EAAEA,GAAE,iBAAgBA,GAAE,kBAAiBA,GAAE,2BAA2B,EAAC,GAAEF,KAAEQ,GAAE,MAAIA,GAAE,IAAI,GAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,gBAAe,CAAC,GAAE,EAAE,OAAM,EAAC,OAAM,EAAEA,GAAE,iBAAgBA,GAAE,kBAAiBA,GAAE,0BAA0B,EAAC,GAAEF,KAAEQ,GAAE,OAAKA,GAAE,GAAG,CAAC,GAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,eAAc,GAAE,EAAE,OAAM,EAAC,OAAMA,GAAE,gBAAe,CAAC,GAAEQ,IAAE,EAAE,OAAM,EAAC,OAAMR,GAAE,gBAAe,CAAC,CAAC,GAAE,EAAE,OAAM,EAAC,OAAMA,GAAE,eAAc,GAAE,EAAE,OAAM,EAAC,OAAM,EAAEA,GAAE,iBAAgBA,GAAE,kBAAiBA,GAAE,wBAAwB,EAAC,GAAEF,KAAEQ,GAAE,QAAMA,GAAE,MAAM,GAAE,EAAE,OAAM,EAAC,OAAMN,GAAE,gBAAe,CAAC,GAAE,EAAE,OAAM,EAAC,OAAM,EAAEA,GAAE,iBAAgBA,GAAE,kBAAiBA,GAAE,uBAAuB,EAAC,GAAEF,KAAEQ,GAAE,SAAOA,GAAE,KAAK,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEL,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC,eAAcE,KAAE,GAAGF,EAAC,aAAYG,KAAE,WAASL,IAAEM,KAAE,EAAC,aAAY,MAAK,WAAU,KAAI;AAAE,UAAOL,IAAE;AAAA,IAAC,KAAI;AAAK,MAAAI,KAAEC,GAAE,cAAY,QAAQF,EAAC,MAAIE,GAAE,YAAU,QAAQH,EAAC;AAAI;AAAA,IAAM,KAAI;AAAK,MAAAG,GAAE,cAAY,QAAQF,EAAC;AAAI;AAAA,IAAM,KAAI;AAAK,MAAAC,KAAEC,GAAE,YAAU,QAAQH,EAAC,MAAIG,GAAE,cAAY,QAAQF,EAAC;AAAI;AAAA,IAAM;AAAQ,MAAAE,GAAE,YAAU,QAAQH,EAAC;AAAA,EAAG;AAAC,SAAOG;AAAC;AAAC,SAASE,GAAEE,IAAE,GAAED,IAAEZ,IAAEc,KAAE,IAAG;AAAC,QAAK,EAAC,OAAMC,IAAE,YAAWJ,IAAE,QAAO,GAAE,UAASK,GAAC,IAAEH,IAAE,IAAE,CAAC,CAACE,IAAEE,KAAE,KAAK,KAAKH,MAAG,IAAEA,MAAG,CAAC,KAAG,IAAE,IAAE,IAAGI,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEC,MAAGP,MAAG,MAAIH;AAAE,WAAQH,KAAE,GAAEA,KAAEG,IAAEH,MAAI;AAAC,UAAMC,KAAED,KAAEa;AAAE,aAAQX,KAAE,GAAEA,KAAEC,IAAED,MAAI;AAAC,YAAMG,KAAEH,KAAEW,IAAEC,KAAE,EAAE,EAAEd,EAAC,EAAEE,EAAC,CAAC,GAAEa,KAAEC,GAAE,IAAI,GAAEZ,KAAE,EAAC,MAAK,QAAO,GAAEC,IAAE,GAAEJ,IAAE,OAAMY,IAAE,QAAOA,GAAC,GAAErB,KAAEyB,GAAEH,EAAC;AAAE,MAAAtB,MAAGkB,GAAE,KAAKlB,EAAC;AAAE,YAAMc,KAAE,EAAEF,IAAEU,GAAE,MAAKC,IAAE,IAAI;AAAE,MAAAT,MAAGK,GAAE,KAAKL,EAAC,GAAEM,GAAE,KAAK,EAAER,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,QAAMc,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAE;AAAG,MAAIC,KAAE;AAAK,QAAIA,KAAE;AAAmC,QAAMN,KAAE,EAAE,QAAOT,IAAE,CAAC,GAAEgB,KAAE,EAAE,SAAQhB,IAAE,CAAC,GAAE,IAAE,EAAEK,EAAC,GAAEY,KAAE,EAAE,GAAEf,IAAEA,IAAE,GAAE,OAAGD,IAAE,KAAE,GAAE,IAAE,EAAE,GAAEC,IAAEA,IAAE,GAAE,OAAG,IAAE,MAAI,MAAK,KAAE,GAAE,IAAE,EAAC,QAAO,EAAEjB,EAAC,GAAE,SAAQ,QAAMY,KAAE,KAAG,GAAGA,EAAC,GAAE;AAAE,SAAO,EAAE,OAAM,EAAC,QAAO,GAAE,OAAM,IAAEV,GAAE,oBAAkBA,GAAE,gBAAe,GAAE,EAAE,OAAM,EAAC,OAAMD,IAAE,OAAMgB,IAAE,QAAOA,IAAE,OAAMa,GAAC,GAAE,EAAE,QAAO,MAAK,EAAE,UAAS,EAAC,IAAG,GAAG,CAAC,eAAc,aAAY,MAAK,cAAa,MAAK,MAAK,KAAI,MAAK,KAAI,aAAY,eAAc,QAAO,OAAM,GAAE,EAAE,YAAW,EAAC,QAAO,gBAAe,MAAK,QAAO,QAAO,WAAU,gBAAe,IAAG,CAAC,CAAC,GAAE,EAAE,UAAS,EAAC,IAAG,GAAG,CAAC,aAAY,aAAY,MAAK,cAAa,MAAK,MAAK,KAAI,MAAK,KAAI,aAAY,eAAc,QAAO,OAAM,GAAE,EAAE,YAAW,EAAC,QAAO,gBAAe,MAAK,QAAO,QAAO,WAAU,gBAAe,IAAG,CAAC,CAAC,GAAEZ,EAAC,GAAE,EAAE,KAAI,EAAC,WAAUc,GAAC,GAAEb,EAAC,GAAE,EAAE,KAAI,EAAC,WAAU,EAAC,GAAE,EAAE,QAAO,EAAC,MAAK,QAAO,QAAO,WAAU,gBAAe,KAAI,gBAAeK,GAAE,aAAY,cAAaA,GAAE,WAAU,IAAG,CAACE,IAAE,IAAGZ,KAAEa,IAAE,IAAG,CAACD,IAAE,IAAGC,GAAC,CAAC,GAAE,EAAE,QAAO,EAAC,MAAK,QAAO,QAAO,WAAU,gBAAe,KAAI,gBAAeI,GAAE,aAAY,cAAaA,GAAE,WAAU,IAAGH,IAAE,IAAGd,KAAEe,IAAE,IAAGf,KAAEc,IAAE,IAAGd,KAAEe,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC;;;ACA9yH,IAAMI,KAAE;AAAuB,SAASC,GAAEC,IAAEF,IAAE;AAAC,SAAOE,KAAEF;AAAC;AAAC,SAASG,GAAED,IAAEF,IAAE;AAAC,MAAIC,IAAEE;AAAE,SAAOF,KAAE,OAAOC,GAAE,QAAQF,EAAC,CAAC,GAAEC,KAAEC,KAAEC,KAAEF,KAAE,IAAE,MAAID,MAAGG,KAAEF,IAAEA,MAAG,IAAE,MAAID,KAAGC,KAAE,OAAOA,GAAE,QAAQD,EAAC,CAAC,GAAEG,KAAE,OAAOA,GAAE,QAAQH,EAAC,CAAC,GAAE,CAACC,IAAEE,EAAC;AAAC;AAAC,SAAS,EAAED,IAAEF,IAAEC,IAAEE,IAAEC,IAAE;AAAC,QAAMC,KAAEC,GAAEJ,IAAEF,IAAEC,IAAEE,EAAC,GAAEI,KAAE,QAAMF,GAAE,YAAUA,GAAE,YAAUD,IAAEI,KAAE,QAAMH,GAAE,QAAMA,GAAE,QAAMD;AAAE,SAAOG,MAAGC,MAAGH,GAAE,WAASA,GAAE,QAAM,IAAED;AAAC;AAAC,SAASC,GAAEH,IAAE;AAAC,QAAMD,KAAE,OAAOC,EAAC,GAAEC,KAAEF,GAAE,MAAMD,EAAC;AAAE,MAAGG,MAAGA,GAAE,CAAC,EAAE,QAAM,EAAC,SAAQA,GAAE,CAAC,EAAE,MAAM,EAAE,EAAE,QAAO,YAAWA,GAAE,CAAC,IAAEA,GAAE,CAAC,EAAE,MAAM,EAAE,EAAE,SAAO,EAAC;AAAE,MAAGF,GAAE,YAAY,EAAE,SAAS,GAAG,GAAE;AAAC,UAAMC,KAAED,GAAE,MAAM,GAAG,GAAED,KAAEE,GAAE,CAAC,GAAEC,KAAED,GAAE,CAAC;AAAE,QAAGF,MAAGG,IAAE;AAAC,YAAMD,KAAE,OAAOF,EAAC;AAAE,UAAIC,KAAE,OAAOE,EAAC;AAAE,YAAMC,KAAEH,KAAE;AAAE,MAAAG,OAAIH,KAAE,KAAK,IAAIA,EAAC;AAAG,YAAMK,KAAED,GAAEH,EAAC;AAAE,aAAOE,MAAGE,GAAE,WAASL,IAAEA,KAAEK,GAAE,aAAWA,GAAE,aAAW,IAAEA,GAAE,cAAYL,OAAIK,GAAE,cAAYL,IAAEA,KAAEK,GAAE,UAAQA,GAAE,UAAQ,IAAEA,GAAE,WAASL,KAAGK;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,EAAC,SAAQ,GAAE,YAAW,EAAC;AAAC;AAAC,SAASA,GAAEJ,IAAEF,IAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAE,EAAC,UAAS,MAAK,MAAK,KAAI;AAAE,MAAG,QAAMH,IAAE;AAAC,UAAME,KAAED,KAAED,IAAEI,KAAEL,KAAEC,KAAEE;AAAE,IAAAC,GAAE,WAAS,KAAK,MAAM,KAAK,IAAI,MAAIC,KAAEF,EAAC,CAAC;AAAA,EAAC;AAAC,MAAG,QAAMA,IAAE;AAAC,UAAMF,KAAEE,KAAED,IAAEG,KAAEF,KAAEH,KAAEC;AAAE,IAAAG,GAAE,OAAK,KAAK,MAAM,KAAK,IAAI,MAAIC,KAAEJ,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAASG,GAAEL,IAAEF,KAAE,CAAC,GAAE;AAAC,QAAMM,KAAEJ,GAAE,MAAM,CAAC,GAAE,EAAC,WAAUK,KAAE,GAAE,cAAaC,KAAE,OAAG,SAAQC,KAAEH,GAAE,IAAK,CAACJ,IAAEF,OAAIA,EAAE,EAAC,IAAEA;AAAE,EAAAS,GAAE,KAAKR,EAAC;AAAE,WAAQA,KAAE,GAAEA,KAAEQ,GAAE,QAAOR,MAAI;AAAC,UAAMC,KAAEO,GAAER,EAAC,GAAED,KAAEM,GAAEJ,EAAC,GAAEQ,KAAE,MAAIR,KAAE,OAAKI,GAAEJ,KAAE,CAAC,GAAES,KAAET,OAAII,GAAE,SAAO,IAAE,OAAKA,GAAEJ,KAAE,CAAC,GAAEU,KAAEP,GAAEL,EAAC,EAAE;AAAW,QAAGY,IAAE;AAAC,UAAIP,IAAEI,KAAE,GAAEI,KAAE;AAAG,aAAKJ,MAAGG,MAAG,CAACC,MAAG;AAAC,cAAMX,KAAEC,GAAEH,IAAES,EAAC;AAAE,QAAAJ,KAAEG,MAAG,MAAIP,KAAEC,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEW,KAAE,EAAEb,IAAEK,IAAEK,IAAEC,IAAEJ,EAAC,GAAEE;AAAA,MAAG;AAAC,MAAAI,OAAIP,GAAEJ,EAAC,IAAEG;AAAA,IAAE;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,IAAME,KAAE,EAAC,uBAAsB,GAAE;AAAE,SAASC,GAAET,IAAE;AAAC,SAAO,EAAEA,IAAEQ,EAAC;AAAC;;;ACA/sC,IAAMM,KAAE;AAAR,IAAYC,KAAE;AAAd,IAAkBC,KAAE,EAAE,YAAY;AAAE,SAASC,GAAEC,IAAEC,IAAEF,IAAEG,IAAE;AAAC,MAAIC,KAAE;AAAG,QAAIF,KAAEE,KAAE,GAAGP,EAAC,MAAIK,OAAIF,OAAII,KAAE,GAAGN,EAAC;AAAK,MAAIO,KAAE;AAAK,SAAOA,KAAEF,KAAE,EAAEF,IAAEF,EAAC,IAAEO,GAAEL,EAAC,GAAEG,KAAEC;AAAC;AAAC,IAAMF,KAAE,CAAC,0MAAyM,0MAAyM,wMAAwM;AAAE,eAAeC,GAAEG,IAAE;AAAC,MAAG,EAAE,qBAAoBA,OAAI,CAACA,GAAE,gBAAgB,QAAO;AAAK,QAAMN,KAAEM,GAAE,gBAAgB,KAAM,CAAAA,OAAG,YAAUA,GAAE,IAAK;AAAE,MAAG,CAACN,GAAE,QAAO;AAAK,MAAIO,KAAE,MAAKN,KAAE;AAAK,MAAGD,GAAE,OAAM;AAAC,QAAG,MAAIA,GAAE,MAAM,OAAO,QAAOA,GAAE,MAAM,CAAC,EAAE;AAAM,IAAAO,KAAEP,GAAE,MAAM,CAAC,EAAE,OAAMC,KAAED,GAAE,MAAMA,GAAE,MAAM,SAAO,CAAC,EAAE;AAAA,EAAK;AAAC,QAAMJ,KAAE,QAAMW,MAAG,QAAMN,KAAEM,MAAGN,KAAEM,MAAG,IAAE,GAAE,EAAC,UAASV,GAAC,IAAE,MAAM,OAAO,mCAAmE;AAAE,SAAOA,GAAEG,IAAEJ,EAAC,KAAG;AAAI;AAAC,eAAeQ,GAAEE,IAAEN,IAAE;AAAC,QAAMO,KAAED,GAAE,UAASV,KAAEU,GAAE,cAAY,GAAET,KAAEG,MAAG,MAAMG,GAAEG,EAAC,KAAGA,GAAE;AAAM,SAAO,IAAIE,GAAE,EAAC,KAAID,IAAE,OAAMV,IAAE,OAAMD,GAAC,CAAC;AAAC;;;ACAr7C,IAAMa,KAAE,IAAI,EAAE,CAAC,IAAG,IAAG,EAAE,CAAC;AAAE,SAASC,GAAEC,IAAEF,IAAE;AAAC,QAAMC,KAAE,CAAC,GAAEE,KAAED,GAAE,SAAO;AAAE,SAAO,MAAIA,GAAE,SAAOD,GAAE,KAAK,GAAE,GAAE,CAAC,IAAEA,GAAE,KAAK,GAAEE,EAAC,GAAED,GAAE,IAAK,CAACA,IAAEE,OAAIH,GAAE,SAASG,EAAC,IAAEJ,GAAEE,IAAEE,IAAED,IAAEH,EAAC,IAAE,IAAK;AAAC;AAAC,eAAeG,GAAED,IAAEG,IAAEL,IAAE;AAAC,MAAIG,KAAE,OAAGG,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,MAAGL,GAAE,OAAM;AAAC,UAAMG,KAAEH,GAAE;AAAM,IAAAI,KAAED,GAAE,IAAK,CAAAH,OAAGA,GAAE,KAAM,GAAEC,KAAEE,GAAE,KAAM,CAAAH,OAAG,CAAC,CAACA,GAAE,KAAM,GAAEC,OAAII,KAAEF,GAAE,IAAK,CAAAH,OAAGA,GAAE,KAAM;AAAA,EAAE;AAAC,QAAMM,KAAEF,GAAE,CAAC,GAAEG,KAAEH,GAAEA,GAAE,SAAO,CAAC;AAAE,MAAG,QAAME,MAAG,QAAMC,GAAE,QAAO;AAAK,QAAMC,KAAEP,KAAE,OAAKF,GAAEK,IAAEN,MAAG,KAAE;AAAE,UAAO,MAAM,QAAQ,IAAIM,GAAE,IAAK,OAAMN,IAAEC,QAAK,EAAC,OAAMD,IAAE,OAAM,cAAYE,GAAE,OAAK,MAAME,GAAEJ,IAAEE,IAAEG,EAAC,KAAG,MAAM,OAAO,mCAAmE,GAAG,SAASH,IAAEF,EAAC,GAAE,OAAMG,KAAEI,GAAEN,EAAC,KAAES,MAAA,gBAAAA,GAAIT,QAAI,GAAE,EAAG,CAAC,GAAG,QAAQ;AAAC;AAAC,eAAeG,GAAEC,IAAEJ,IAAEE,KAAEH,IAAE;AAAC,QAAMI,KAAE,IAAI,EAAED,EAAC,GAAEG,MAAG,MAAM,OAAO,mCAAmE,GAAG,WAAWL,IAAEI,EAAC;AAAE,SAAO,QAAMC,OAAIF,GAAE,IAAEE,KAAGF;AAAC;AAAC,SAASE,GAAEJ,IAAE;AAAC,MAAIG,KAAE,OAAGL,KAAE,CAAC,GAAEG,KAAE,CAAC;AAAE,EAAAH,KAAEE,GAAE,IAAK,CAAAA,OAAGA,GAAE,KAAM,GAAEG,KAAEH,GAAE,KAAM,CAAAA,OAAG,CAAC,CAACA,GAAE,KAAM,GAAEG,OAAIF,KAAED,GAAE,IAAK,CAAAA,OAAGA,GAAE,SAAO,EAAG;AAAG,QAAME,KAAEJ,GAAE,CAAC,GAAEM,KAAEN,GAAEA,GAAE,SAAO,CAAC;AAAE,MAAG,QAAMI,MAAG,QAAME,GAAE,QAAO;AAAK,QAAME,KAAEH,KAAE,OAAKJ,GAAED,IAAE,KAAE;AAAE,SAAOA,GAAE,IAAK,CAACA,IAAEC,QAAK,EAAC,OAAMD,IAAE,OAAMO,GAAEP,IAAEE,EAAC,GAAE,OAAMG,KAAEF,GAAEF,EAAC,KAAEO,MAAA,gBAAAA,GAAIP,QAAI,GAAE,EAAG,EAAE,QAAQ;AAAC;AAAC,SAASM,GAAEF,IAAEL,IAAE;AAAC,QAAK,EAAC,YAAWC,IAAE,UAASE,IAAE,QAAOC,GAAC,IAAEI,GAAEH,IAAEL,EAAC;AAAE,MAAGC,OAAIE,GAAE,QAAOH,GAAEC,EAAC,EAAE;AAAM,QAAMK,KAAE,EAAE,YAAYN,GAAEC,EAAC,EAAE,OAAMD,GAAEG,EAAC,EAAE,OAAMC,EAAC;AAAE,SAAO,IAAI,EAAEE,EAAC;AAAC;AAAC,SAASE,GAAEN,IAAEG,IAAE;AAAC,MAAIL,KAAE,GAAEC,KAAEI,GAAE,SAAO;AAAE,SAAOA,GAAE,KAAM,CAACA,IAAEF,OAAID,KAAEG,GAAE,SAAOJ,KAAEE,IAAE,SAAKH,KAAEG,IAAE,MAAI,GAAE,EAAC,YAAWH,IAAE,UAASC,IAAE,SAAQC,KAAEG,GAAEL,EAAC,EAAE,UAAQK,GAAEJ,EAAC,EAAE,QAAMI,GAAEL,EAAC,EAAE,OAAM;AAAC;AAAC,SAASS,GAAEJ,IAAEL,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,MAAGI,MAAG,gBAAcA,GAAE,KAAK,CAAAA,GAAE,WAAW,QAAQ,EAAE,QAAS,CAACF,IAAEC,OAAI;AAAC,UAAIA,KAAEH,GAAE,KAAK,EAAC,OAAMD,GAAE,KAAI,OAAM,IAAI,EAAEG,GAAE,OAAO,GAAE,OAAM,OAAM,CAAC,IAAEF,GAAE,KAAK,EAAC,OAAM,MAAK,OAAM,IAAI,EAAEE,GAAE,OAAO,GAAE,OAAM,GAAE,CAAC,GAAEC,OAAIC,GAAE,WAAW,SAAO,IAAEJ,GAAE,KAAK,EAAC,OAAMD,GAAE,KAAI,OAAM,IAAI,EAAEG,GAAE,SAAS,GAAE,OAAM,MAAK,CAAC,IAAEF,GAAE,KAAK,EAAC,OAAM,MAAK,OAAM,IAAI,EAAEE,GAAE,SAAS,GAAE,OAAM,GAAE,CAAC;AAAA,EAAC,CAAE;AAAA,OAAM;AAAC,QAAIA,IAAEC;AAAE,IAAAC,MAAG,kBAAgBA,GAAE,QAAMF,KAAEE,GAAE,WAAUD,KAAEC,GAAE,YAAUF,KAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAEC,KAAE,CAAC,KAAI,KAAI,KAAI,CAAC,IAAGH,KAAE,CAAC,EAAC,OAAMD,GAAE,KAAI,OAAM,IAAI,EAAEI,EAAC,GAAE,OAAM,OAAM,GAAE,EAAC,OAAMJ,GAAE,KAAI,OAAM,IAAI,EAAEG,EAAC,GAAE,OAAM,MAAK,CAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;;;ACA15D,SAASU,GAAEA,IAAE;AAAC,MAAG,CAACA,GAAE,WAAW,QAAM,CAAC;AAAE,QAAMC,KAAE,CAAC,GAAGD,GAAE,UAAU,EAAE,OAAQ,CAAAE,OAAC;AAJzI;AAI2I,kBAAAA,GAAE,UAAF,mBAAS,KAAE;AAAA,GAAE;AAAE,MAAIC,KAAEF,GAAE,SAAO;AAAE,MAAGA,MAAGA,GAAE,CAAC,GAAE;AAAC,UAAMD,KAAEC,GAAEE,EAAC;AAAE,IAAAH,MAAG,MAAIA,GAAE,UAAQC,GAAE,KAAK,IAAI,EAAE,EAAC,OAAM,GAAE,OAAMD,GAAE,MAAK,CAAC,CAAC,GAAEG;AAAA,EAAI;AAAC,SAAOF,GAAE,IAAK,CAACC,IAAED,OAAI;AAJtR;AAIuR,QAAIG,KAAE;AAAG,WAAO,MAAIH,KAAEG,OAAE,KAAAJ,GAAE,kBAAF,mBAAiB,aAAU,QAAMC,OAAIE,OAAIC,OAAE,KAAAJ,GAAE,kBAAF,mBAAiB,aAAU,SAAQ,EAAC,OAAME,GAAE,OAAM,OAAME,IAAE,OAAMF,GAAE,MAAK;AAAA,EAAC,CAAE,EAAE,QAAQ;AAAC;;;ACAlO,IAAMG,KAAE,EAAC,IAAG,KAAI,IAAG,IAAG,IAAG,KAAI,IAAG,IAAG;AAAnC,IAAqCC,KAAE,EAAC,GAAE,CAAC,CAAC,MAAK,IAAI,GAAE,CAAC,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,CAAC,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,IAAI,CAAC,GAAE,GAAE,CAAC,CAAC,MAAK,OAAM,OAAM,IAAI,GAAE,CAAC,OAAM,QAAO,QAAO,KAAK,GAAE,CAAC,OAAM,QAAO,QAAO,KAAK,GAAE,CAAC,MAAK,OAAM,OAAM,IAAI,CAAC,EAAC;AAAE,SAASC,GAAEC,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,QAAK,EAAC,MAAKH,GAAC,IAAEG;AAAE,MAAGH,GAAE,SAAS,IAAI,EAAE,QAAO,EAAEG,GAAE,aAAa,UAAU,CAAC,CAAC;AAAE,MAAG,kBAAgBH,IAAE;AAAC,UAAMI,KAAE,EAAED,EAAC;AAAE,WAAOC,MAAGA,GAAE;AAAA,EAAK;AAAC,MAAG,oBAAkBD,GAAE,SAAO,QAAMA,GAAE,SAAO,YAAUA,GAAE,QAAO;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,WAAOC,MAAGA,GAAE;AAAA,EAAK;AAAC,SAAOC,GAAEF,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEG,IAAE;AAAC,QAAMF,KAAEE,GAAE,GAAG,OAAMC,KAAED,GAAE,GAAG,OAAMN,KAAEM,GAAE,GAAG,OAAML,KAAEK,GAAE,GAAG;AAAM,UAAOH,IAAE;AAAA,IAAC,KAAI;AAAA,IAAK;AAAQ,aAAM,EAAC,KAAIC,IAAE,QAAOG,IAAE,MAAKP,IAAE,OAAMC,GAAC;AAAA,IAAE,KAAI;AAAK,aAAM,EAAC,KAAID,IAAE,QAAOC,IAAE,MAAKM,IAAE,OAAMH,GAAC;AAAA,IAAE,KAAI;AAAK,aAAM,EAAC,KAAIG,IAAE,QAAOH,IAAE,MAAKH,IAAE,OAAMD,GAAC;AAAA,IAAE,KAAI;AAAK,aAAM,EAAC,KAAIC,IAAE,QAAOD,IAAE,MAAKI,IAAE,OAAMG,GAAC;AAAA,EAAC;AAAC;AAAgN,SAASC,GAAEC,IAAE;AAAC,QAAK,EAAC,OAAMC,IAAE,OAAMC,IAAE,YAAWC,GAAC,IAAEH,IAAEI,KAAEC,GAAEF,EAAC,GAAEG,KAAE,CAAC;AAAE,EAAAJ,GAAE,QAAS,CAAAF,OAAG;AAAC,IAAAM,GAAEN,GAAE,KAAK,IAAE,EAAC,OAAMA,GAAE,OAAM,MAAKO,GAAEP,GAAE,MAAM,EAAC;AAAA,EAAC,CAAE;AAAE,QAAMD,KAAE,CAAC;AAAE,WAAQM,KAAE,GAAEA,KAAEF,IAAEE,MAAI;AAAC,UAAML,KAAE,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,YAAMC,KAAEI,GAAEF,GAAEC,EAAC,EAAEJ,EAAC,CAAC;AAAE,MAAAD,GAAE,KAAKE,GAAE,IAAI;AAAA,IAAC;AAAC,IAAAH,GAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,MAAK,qBAAoB,YAAWG,IAAE,OAAMF,IAAE,QAAOF,IAAE,QAAO,EAAEE,IAAEK,EAAC,GAAE,UAASE,GAAEP,EAAC,EAAC;AAAC;AAAC,SAASO,GAAER,IAAE;AAAC,MAAIC,KAAEG,GAAEJ,EAAC;AAAE,SAAOA,MAAG,QAAMC,OAAIA,KAAEG,GAAE,KAAIH,MAAG;AAAC;;;ACAl1B,IAAI,IAAE;AAAK,IAAMQ,KAAE,CAAC,KAAI,KAAI,GAAG;AAAE,SAAS,EAAEC,IAAEC,IAAE;AAAC,SAAO,KAAK,MAAM,KAAK,OAAO,KAAGA,KAAED,KAAE,KAAGA,EAAC;AAAC;AAAC,SAASE,GAAEF,IAAEC,IAAEE,IAAE;AAAC,QAAK,EAAC,iBAAgBC,IAAE,SAAQC,IAAE,SAAQC,GAAC,IAAEN,IAAEO,KAAEJ,MAAGA,GAAE,cAAYF,GAAE,MAAKO,KAAE,KAAGC,KAAE,KAAK,MAAMF,KAAEA,KAAED,MAAG,IAAEE,EAAC,GAAEE,KAAE,OAAO,kBAAiBC,KAAE,SAAS,cAAc,QAAQ,GAAEC,KAAEL,KAAEG;AAAE,EAAAC,GAAE,QAAMC,IAAED,GAAE,SAAOC,IAAED,GAAE,MAAM,QAAMA,GAAE,QAAMD,KAAE,MAAKC,GAAE,MAAM,SAAOA,GAAE,SAAOD,KAAE;AAAK,QAAMG,KAAEF,GAAE,WAAW,IAAI;AAAE,MAAGP,OAAIS,GAAE,YAAUT,GAAE,MAAM,IAAE,GAAES,GAAE,SAAS,GAAE,GAAED,IAAEA,EAAC,GAAEC,GAAE,KAAK,IAAGA,GAAE,YAAUZ,GAAE,MAAM,IAAE,GAAE,KAAG,EAAE,SAAO,MAAIQ,GAAE,UAAQK,KAAE,GAAEA,KAAE,IAAEL,IAAEK,MAAG,GAAE;AAAC,UAAMd,KAAE,EAAEc,EAAC,GAAEb,KAAE,EAAEa,KAAE,CAAC;AAAE,IAAAD,GAAE,SAASb,IAAEC,IAAEK,KAAEI,IAAEJ,KAAEI,EAAC,GAAEG,GAAE,KAAK;AAAA,EAAC;AAAA,OAAK;AAAC,QAAE,CAAC;AAAE,aAAQb,KAAE,GAAEA,KAAE,IAAES,IAAET,MAAG,GAAE;AAAC,YAAMA,KAAE,EAAE,GAAEY,EAAC,GAAEX,KAAE,EAAE,GAAEW,EAAC;AAAE,QAAE,KAAKZ,IAAEC,EAAC,GAAEY,GAAE,SAASb,IAAEC,IAAEK,KAAEI,IAAEJ,KAAEI,EAAC,GAAEG,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAR,OAAIA,GAAE,UAAQQ,GAAE,cAAYR,GAAE,MAAM,MAAM,IAAE,IAAGQ,GAAE,YAAUR,GAAE,OAAMQ,GAAE,WAAW,GAAE,GAAED,IAAEA,EAAC;AAAG,QAAMG,KAAE,IAAI,MAAMR,IAAEA,EAAC;AAAE,SAAOQ,GAAE,MAAIJ,GAAE,UAAU,GAAEI;AAAC;AAAC,SAAS,EAAEf,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAME,KAAEF,GAAE,UAAQ,IAAGG,KAAE,IAAE,KAAK,KAAGD,IAAEW,KAAEd,GAAE,QAAOO,KAAEH,KAAEU,IAAEN,KAAE,CAAC,GAAEC,KAAE,EAAER,GAAE,OAAO;AAAE,WAAMQ,MAAA,gBAAAA,GAAG,WAAQA,GAAE,SAAO,KAAIA,MAAGR,GAAE,oBAAkBO,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAS,IAAGL,IAAE,IAAGA,IAAE,GAAEA,GAAC,GAAE,MAAKF,GAAE,iBAAgB,QAAOQ,IAAE,QAAO,CAAC,GAAE,CAAC,EAAC,CAAC;AAAE,QAAMC,KAAET,GAAE,QAAOU,KAAED,MAAGA,GAAE,WAASI,MAAG,QAAMJ,GAAE,OAAQ,CAACV,IAAEC,OAAID,KAAEC,IAAG,CAAC,GAAEW,KAAE,CAAC,CAAC;AAAE,WAAQP,KAAE,GAAEA,KAAES,IAAET,MAAI;AAAC,QAAIJ,KAAE;AAAK,IAAAU,OAAIV,KAAES,GAAEL,EAAC,IAAED,KAAE,KAAIQ,GAAE,KAAKX,KAAEW,GAAEP,EAAC,CAAC,IAAGG,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAS,IAAGL,IAAE,IAAGA,IAAE,GAAEA,KAAE,EAAC,GAAE,MAAK,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,QAAO,EAAC,OAAMA,IAAE,WAAU,IAAIF,MAAGM,MAAG,CAAC,IAAIH,EAAC,IAAG,YAAW,OAAKO,KAAEC,GAAEP,EAAC,IAAE,IAAEA,MAAGE,KAAE,KAAI,OAAMP,GAAEK,EAAC,EAAC,GAAE,QAAO,CAAC,GAAE,CAAC,EAAC,CAAC;AAAA,EAAC;AAAC,MAAIQ,KAAE;AAAK,QAAME,KAAE,IAAEZ,OAAGM,MAAA,gBAAAA,GAAG,UAAO,IAAG,IAAER,GAAE;AAAe,MAAG,GAAE;AAAC,IAAAQ,MAAGD,GAAE,KAAK,EAAC,OAAM,EAAC,MAAK,UAAS,IAAGL,IAAE,IAAGA,IAAE,GAAEA,KAAE,EAAC,GAAE,MAAK,MAAK,QAAOM,IAAE,QAAO,CAAC,GAAE,CAAC,EAAC,CAAC;AAAE,UAAMT,KAAEe,KAAE;AAAE,IAAAF,KAAE,CAAC,CAAC,EAAC,OAAM,EAAC,MAAK,UAAS,IAAGb,IAAE,IAAGA,IAAE,GAAEA,GAAC,GAAE,MAAKD,IAAE,QAAOU,KAAE,EAAC,GAAGA,IAAE,OAAMV,GAAC,IAAE,MAAK,QAAO,CAAC,GAAE,CAAC,EAAC,GAAE,EAAC,OAAM,EAAC,MAAK,UAAS,IAAGC,IAAE,IAAGA,IAAE,GAAEG,KAAE,EAAC,GAAE,MAAK,CAAC,GAAE,GAAE,CAAC,GAAE,QAAO,MAAK,QAAO,CAAC,GAAE,CAAC,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOA,GAAE,CAACK,EAAC,GAAE,CAACO,IAAEA,EAAC,GAAE,EAAC,YAAWd,GAAE,YAAW,mBAAkB,MAAG,SAAQY,IAAE,UAAS,IAAG,CAAC;AAAC;AAAmU,SAAS,EAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAMC,KAAE,IAAGC,KAAE,IAAGC,KAAE,iBAAeH,GAAE,OAAMI,KAAED,KAAED,KAAED,IAAEI,KAAEF,KAAEF,KAAEC,IAAE,EAAC,OAAMI,KAAEF,IAAE,QAAOG,KAAEF,IAAE,UAASG,KAAE,KAAE,IAAER,IAAES,KAAE,OAAO,kBAAiBC,KAAEJ,KAAEG,IAAEE,KAAEJ,KAAEE,IAAEG,KAAE,SAAS,cAAc,QAAQ;AAAE,EAAAA,GAAE,QAAMF,IAAEE,GAAE,SAAOD,IAAEC,GAAE,MAAM,QAAM,GAAGN,EAAC,MAAKM,GAAE,MAAM,SAAO,GAAGL,EAAC;AAAK,QAAMM,KAAED,GAAE,WAAW,IAAI,GAAE,IAAET,KAAEO,KAAE,GAAEI,KAAEX,KAAE,IAAEQ;AAAE,MAAGH,IAAE;AAAC,UAAMR,KAAEa,GAAE,qBAAqB,GAAE,GAAE,GAAEC,EAAC,GAAEb,KAAEF,GAAE,QAAOG,KAAE,MAAID,KAAE,IAAE,KAAGA,KAAE;AAAG,IAAAF,GAAE,QAAS,CAACA,IAAEE,OAAID,GAAE,aAAaC,KAAEC,IAAEH,GAAE,SAAS,CAAC,CAAE,GAAEc,GAAE,YAAUb,IAAEa,GAAE,SAAS,GAAE,GAAEH,IAAEC,EAAC;AAAA,EAAC,OAAK;AAAC,UAAMX,KAAEG,KAAEO,KAAEX,GAAE,SAAOW,IAAET,KAAEE,KAAEQ,KAAEA,KAAEZ,GAAE;AAAO,QAAIG,KAAE,GAAEE,KAAE;AAAE,eAAUC,MAAKN,GAAE,CAAAc,GAAE,YAAUR,GAAE,SAAS,GAAEQ,GAAE,SAASX,IAAEE,IAAEJ,IAAEC,EAAC,GAAEC,KAAEC,KAAED,KAAEF,KAAE,GAAEI,KAAED,KAAE,IAAEC,KAAEH;AAAA,EAAC;AAAC,QAAMc,KAAE,SAAS,cAAc,KAAK;AAAE,SAAOA,GAAE,MAAM,QAAM,GAAGT,EAAC,MAAKS,GAAE,MAAM,SAAO,GAAGR,EAAC,MAAKS,GAAED,IAAEf,MAAA,gBAAAA,GAAG,UAAU,GAAEe,GAAE,YAAYH,EAAC,GAAEG;AAAC;AAAC,SAASC,GAAEjB,IAAEC,IAAE;AAAC,MAAG,CAACA,GAAE;AAAO,EAAAD,GAAE,MAAM,SAAO,EAAEC,EAAC;AAAE,QAAMC,KAAED,GAAE;AAAQ,MAAGC;AAAE,eAAUC,MAAKD,GAAE,KAAG,mBAAgBC,MAAA,gBAAAA,GAAG,OAAK;AAAC,MAAAA,GAAE,UAAQ,IAAEH,GAAE,MAAM,aAAW,GAAG,KAAK,IAAIG,GAAE,OAAO,CAAC,OAAKH,GAAE,MAAM,cAAY,GAAGG,GAAE,OAAO;AAAK;AAAA,IAAK;AAAA;AAAC;AAAC,eAAee,GAAElB,IAAEC,IAAE;AAAC,UAAOD,GAAE,MAAK;AAAA,IAAC,KAAI,aAAY;AAAC,YAAK,EAAC,uBAAsBE,GAAC,IAAE,MAAM,OAAO,qCAA4B;AAAE,aAAOA,GAAEF,IAAEkB,IAAEjB,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI;AAAA,IAAW,KAAI;AAAA,IAAU,KAAI;AAAA,IAAU,KAAI;AAAA,IAAW,KAAI,cAAa;AAAC,YAAK,EAAC,iBAAgBC,GAAC,IAAE,MAAM,OAAO,+BAAsB;AAAE,aAAOA,GAAEF,IAAEC,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI;AAAA,IAAgB,KAAI;AAAA,IAAc,KAAI;AAAA,IAAc,KAAI;AAAA,IAAiB,KAAI;AAAA,IAAe,KAAI,QAAO;AAAC,YAAK,EAAC,iBAAgBC,GAAC,IAAE,MAAM,OAAO,+BAAsB;AAAE,aAAOA,GAAEF,IAAEC,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI,OAAM;AAAC,YAAK,EAAC,kBAAiBC,GAAC,IAAE,MAAM,OAAO,gCAAuB;AAAE,aAAOA,GAAEF,IAAEC,EAAC;AAAA,IAAC;AAAA,IAAC;AAAQ;AAAA,EAAM;AAAC;AAAC,SAASkB,GAAEnB,IAAE;AAAC,SAAOA,MAAG,aAAYA,KAAEA,GAAE,UAAQmB,GAAEnB,GAAE,MAAM,IAAE;AAAC;AAAC,eAAe,EAAEI,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE;AAAO,QAAME,KAAEF,GAAE,aAAYG,MAAG,EAAEF,EAAC,KAAGA,GAAE,iBAAeC,KAAEF,GAAE,UAAQE,IAAEE,KAAEW,GAAEZ,EAAC;AAAE,MAAG,EAAEH,GAAE,MAAM,MAAI,CAAC,EAAEC,EAAC,KAAG,SAAKA,GAAE,sBAAqB;AAAC,UAAMJ,KAAE,gBAAcG,GAAE,OAAO,OAAK,MAAM,EAAEA,GAAE,QAAO,EAAC,GAAGC,IAAE,OAAM,EAAEA,EAAC,IAAEA,GAAE,gBAAc,KAAI,CAAC,IAAED,GAAE,OAAO,MAAM;AAAE,WAAOgB,GAAEnB,IAAE,MAAKO,EAAC,GAAEP;AAAA,EAAC;AAAC,QAAMY,MAAG,EAAER,EAAC,IAAEA,GAAE,WAAS,UAAQE,MAAG,cAAaA,KAAEA,GAAE,WAAS;AAAM,MAAIO,KAAED,MAAG,oBAAmBA,KAAE,MAAMA,GAAE,eAAeT,IAAEC,EAAC,IAAE;AAAK,MAAG,CAACS,GAAE;AAAO,MAAGA,KAAE,gBAAcA,GAAE,OAAK,MAAMA,GAAE,YAAY,EAAC,GAAGT,IAAE,OAAM,EAAEA,EAAC,IAAEA,GAAE,gBAAc,KAAI,CAAC,IAAES,GAAE,MAAM,GAAE,EAAED,MAAG,qBAAoBA,MAAGA,GAAE,mBAAiBA,GAAE,gBAAgB,QAAQ,QAAOO,GAAEN,IAAE,MAAKN,EAAC,GAAEM;AAAE,MAAG,sCAAqCD,MAAGA,GAAE,qCAAmC,EAAER,EAAC,KAAG,EAAEA,GAAE,MAAM,IAAG;AAAC,UAAML,KAAE,EAAC,GAAG,EAAEK,EAAC,EAAC;AAAE,IAAAL,GAAE,SAAO,MAAM,EAAE,GAAEK,KAAEL;AAAA,EAAC;AAAC,QAAM,IAAE,MAAM,OAAO,mCAAgE,GAAEe,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEK,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,aAAUtB,MAAKa,GAAE,gBAAgB,SAAOb,GAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,MAAAe,GAAE,KAAKf,EAAC;AAAE;AAAA,IAAM,KAAI;AAAU,MAAAgB,GAAE,KAAKhB,EAAC;AAAE;AAAA,IAAM,KAAI;AAAW,MAAAsB,GAAE,KAAKtB,EAAC;AAAE;AAAA,IAAM,KAAI;AAAO,MAAAA,GAAE,UAAQqB,GAAE,KAAKrB,EAAC;AAAA,EAAC;AAAC,QAAMoB,KAAE,CAAC,CAACL,GAAE,UAAQA,GAAEA,GAAE,SAAO,CAAC,GAAEQ,KAAEH,KAAE,EAAE,SAASA,IAAEhB,IAAEC,EAAC,IAAE,MAAKmB,KAAE,CAAC,CAACR,GAAE,UAAQA,GAAEA,GAAE,SAAO,CAAC;AAAE,MAAIS,KAAED,KAAE,EAAE,WAAWA,IAAEpB,IAAEC,EAAC,IAAE;AAAK,MAAG,QAAMG,OAAIiB,KAAE,QAAMA,KAAEA,KAAEjB,KAAEA,KAAGY,GAAEN,IAAES,IAAEE,EAAC,GAAEJ,GAAE,QAAO;AAAC,UAAMrB,KAAE,EAAE,YAAYqB,IAAEjB,IAAEC,EAAC;AAAE,UAAM,EAAES,IAAEd,EAAC;AAAA,EAAC;AAAC,aAAUA,MAAKsB,GAAE,GAAER,IAAE,EAAE,iBAAiBd,IAAEI,IAAEC,EAAC,GAAEL,GAAE,IAAI;AAAE,SAAOc;AAAC;;;ACAp/J,IAAMY,KAAE,EAAE,UAAU,8BAA8B;AAAlD,IAA8YC,KAAE,EAAE,YAAY;AAAE,eAAeC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,EAAAC,GAAEH,IAAEC,IAAG,MAAI,CAAC,CAAE,EAAE,KAAK,GAAGC,EAAC;AAAC;AAAC,eAAeE,GAAEH,IAAE;AAJ/kC;AAIglC,QAAMI,KAAE,oBAAI;AAAI,MAAG,CAACJ,GAAE,QAAOI;AAAE,MAAG,qBAAoBJ,MAAGA,GAAE,iBAAgB;AAAC,UAAMD,KAAEC,GAAE,gBAAgB,OAAQ,CAAAD,OAAG,YAAUA,GAAE,IAAK;AAAE,eAAUC,MAAKD,IAAE;AAAC,YAAMA,MAAG,MAAMG,GAAEF,EAAC,KAAG,CAAC,GAAG,IAAK,CAAAD,OAAGA,GAAE,KAAM;AAAE,YAAMD,GAAEM,IAAEJ,GAAE,SAAOA,GAAE,iBAAgBD,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAG,cAAYC,GAAE,MAAK;AAAC,UAAMD,KAAEG,GAAEF,EAAC,EAAE,IAAK,CAAAD,OAAGA,GAAE,KAAM;AAAE,UAAMD,GAAEM,IAAEJ,GAAE,SAAOA,GAAE,iBAAgBD,EAAC;AAAA,EAAC,WAAS,gBAAcC,GAAE,MAAK;AAAC,eAAUD,MAAKC,GAAE,WAAW,OAAMF,GAAEM,IAAEL,GAAE,SAAOA,GAAE,iBAAgB,CAACA,GAAE,KAAK,CAAC;AAAE,UAAMD,GAAEM,IAAE,WAAU,EAAC,KAAAJ,MAAA,gBAAAA,GAAG,mBAAH,mBAAmB,OAAM,EAAEA,GAAE,sBAAqB,IAAI,CAAC,CAAC;AAAA,EAAC,WAAS,kBAAgBA,GAAE,MAAK;AAAC,eAAUD,MAAKC,GAAE,WAAW,OAAMF,GAAEM,IAAEL,GAAE,SAAOA,GAAE,iBAAgB,CAACA,GAAE,KAAK,CAAC;AAAE,UAAMD,GAAEM,IAAE,WAAU,CAACJ,GAAE,eAAe,CAAC;AAAA,EAAC,WAAS,mBAAiBA,GAAE,KAAK,KAAG,qBAAiB,KAAAA,GAAE,kBAAF,mBAAiB,MAAK,YAAUD,MAAKC,GAAE,oBAAkB,CAAC,EAAE,OAAMF,GAAEM,IAAEL,GAAE,MAAM,SAAS,GAAE,CAAC,EAAEA,GAAE,QAAO,IAAI,CAAC,CAAC;AAAA,OAAM;AAAC,UAAMA,MAAGC,GAAE,oBAAkB,CAAC,GAAG,IAAK,CAAAD,OAAG,EAAEA,GAAE,QAAO,IAAI,CAAE,GAAE,EAAC,OAAME,IAAE,QAAOI,IAAE,QAAOC,IAAE,iBAAgBC,GAAC,IAAEP;AAAE,KAACC,MAAGM,OAAI,MAAMT,GAAEM,IAAEH,MAAGM,IAAER,EAAC,GAAEM,MAAG,MAAMP,GAAEM,IAAEC,IAAEN,EAAC,GAAEO,MAAG,MAAMR,GAAEM,IAAEE,IAAEP,EAAC;AAAA,EAAC;AAAA,WAAS,mBAAiBC,GAAE,MAAK;AAAC,UAAMD,KAAEC,GAAE,gBAAgB,IAAK,CAAAD,OAAG,EAAEA,GAAE,QAAO,IAAI,CAAE,GAAE,EAAC,OAAME,IAAE,iBAAgBI,GAAC,IAAEL;AAAE,UAAMF,GAAEM,IAAEH,MAAGI,IAAEN,EAAC;AAAA,EAAC,MAAK,cAAWC,GAAE,QAAM,MAAMF,GAAEM,IAAE,WAAU,CAAC,EAAEJ,GAAE,QAAO,IAAI,CAAC,CAAC;AAAE,SAAM,mBAAkBA,MAAGA,GAAE,iBAAe,MAAMF,GAAEM,IAAE,WAAU,CAAC,EAAEJ,GAAE,eAAc,IAAI,CAAC,CAAC,GAAEI,GAAE,QAAS,CAACJ,IAAEC,OAAI;AAAC,UAAMI,KAAE,EAAEL,GAAE,OAAO,OAAO,GAAG,CAACD,IAAEC,OAAI,KAAK,UAAUD,EAAC,MAAI,KAAK,UAAUC,EAAC,CAAE;AAAE,IAAAI,GAAE,IAAIH,IAAEI,EAAC;AAAA,EAAC,CAAE,GAAED;AAAC;", "names": ["u", "f", "p", "e", "l", "a", "r", "t", "s", "o", "i", "T", "b", "n", "c", "h", "q", "g", "L", "C", "v", "w", "d", "m", "S", "N", "y", "x", "R", "E", "M", "j", "H", "n", "e", "t", "r", "o", "i", "l", "u", "s", "c", "a", "f", "m", "g", "s", "i", "r", "o", "l", "a", "n", "e", "u", "c", "A", "t", "m", "o", "n", "l", "r", "t", "e", "a", "u", "s", "i", "c", "r", "e", "o", "t", "l", "s", "l", "n", "t", "o", "f", "e", "r", "i", "t", "e", "o", "r", "s", "l", "L", "n", "u", "b", "e", "t", "S", "l", "r", "i", "o", "n", "a", "c", "u", "f", "p", "h", "s", "y", "e", "t", "l", "r", "i", "s", "o", "n", "a", "c", "u", "f", "p", "h", "y", "m", "g", "k", "C", "j", "S", "b", "w", "v", "V", "x", "m", "g", "h", "e", "t", "i", "r", "b", "l", "o", "n", "a"]}