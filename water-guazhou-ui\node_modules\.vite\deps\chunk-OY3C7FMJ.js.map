{"version": 3, "sources": ["../../@arcgis/core/core/unitFormatUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ByteSizeUnit as t}from\"./byteSizeEstimations.js\";import{cyclicalDegrees as n}from\"./Cyclical.js\";import{clamp as r}from\"./mathUtils.js\";import{unwrapOr as i}from\"./maybe.js\";import{replace as e}from\"./string.js\";import{preferredMetricLengthUnit as o,convertUnit as u,preferredMetricVerticalLengthUnit as s,preferredImperialLengthUnit as c,preferredImperialVerticalLengthUnit as a,preferredMetricAreaUnit as m,preferredImperialAreaUnit as f}from\"./unitUtils.js\";import{formatNumber as b}from\"../intl/number.js\";function l(t,n,r){return t.units[n][r]}function g(t,n,r,i=2,e=\"abbr\"){return`${b(n,{minimumFractionDigits:i,maximumFractionDigits:i,signDisplay:n>0?\"never\":\"exceptZero\"})} ${l(t,r,e)}`}function p(t,n,r,i=2,e=\"abbr\"){return`${b(n,{minimumFractionDigits:i,maximumFractionDigits:i,signDisplay:\"exceptZero\"})} ${l(t,r,e)}`}function D(t,n,r,i=2,e=\"abbr\"){const s=o(n,r);return g(t,u(n,r,s),s,i,e)}function x(t,n,r,i=2,e=\"abbr\"){const s=o(n,r);return p(t,u(n,r,s),s,i,e)}function y(t,n,r,i=2,e=\"abbr\"){const o=s(n,r);return g(t,u(n,r,o),o,i,e)}function F(t,n,r,i=2,e=\"abbr\"){const o=s(n,r);return p(t,u(n,r,o),o,i,e)}function h(t,n,r,i=2,e=\"abbr\"){const o=c(n,r);return g(t,u(n,r,o),o,i,e)}function d(t,n,r,i=2,e=\"abbr\"){const o=c(n,r);return p(t,u(n,r,o),o,i,e)}function j(t,n,r,i=2,e=\"abbr\"){const o=a(n,r);return g(t,u(n,r,o),o,i,e)}function B(t,n,r,i=2,e=\"abbr\"){const o=a(n,r);return p(t,u(n,r,o),o,i,e)}function M(t,n,r,i=2,e=\"abbr\"){const o=m(n,r);return g(t,u(n,r,o),o,i,e)}function $(t,n,r,i=2,e=\"abbr\"){const o=f(n,r);return g(t,u(n,r,o),o,i,e)}function S(t,n,r=2){let i=u(t,n,\"degrees\"),e=i-Math.floor(i);i-=e,e*=60;let o=e-Math.floor(e);return e-=o,o*=60,`${i.toFixed()}° ${e.toFixed()}' ${o.toFixed(r)}\"`}function w(t,r,e,o,s){s=i(s,2);let c=n.normalize(I(u(t,r,\"degrees\"),e,o),0,!0);const a={style:\"unit\",unitDisplay:\"narrow\",unit:\"degree\",maximumFractionDigits:s,minimumFractionDigits:s,signDisplay:c>0?\"never\":\"exceptZero\"};return c=E(c,a),b(c,a)}function Z(t,n,r,e,o){r!==e&&(t=-t);const s={style:\"unit\",unitDisplay:\"narrow\",unit:\"degree\",maximumFractionDigits:o=i(o,2),minimumFractionDigits:o,signDisplay:\"exceptZero\"};let c=u(t,n,\"degrees\")%360;return c=E(c,s),b(c,s)}const z=new Map;function E(t,n){const r=JSON.stringify(n);let i=z.get(r);return i||(i=new Intl.NumberFormat(\"en-US\",n),z.set(r,i)),/\\-?\\+?360/.test(i.format(t))?0:t}function I(t,n,r){if(n===r)return t;switch(r){case\"arithmetic\":return O(t);case\"geographic\":return T(t)}}function O(t){return 90-t}function T(t){return-t-90}const U=[\"B\",\"kB\",\"MB\",\"GB\",\"TB\"];function v(n,i){let o=0===i?0:Math.floor(Math.log(i)/Math.log(t.KILOBYTES));o=r(o,0,U.length-1);const u=b(i/t.KILOBYTES**o,{maximumFractionDigits:2});return e(n.units.bytes[U[o]],{fileSize:u})}export{w as formatAngleDegrees,S as formatDMS,g as formatDecimal,v as formatFileSize,$ as formatImperialArea,h as formatImperialLength,d as formatImperialRelativeLength,B as formatImperialRelativeVerticalLength,j as formatImperialVerticalLength,M as formatMetricArea,D as formatMetricLength,x as formatMetricRelativeLength,F as formatMetricRelativeVerticalLength,y as formatMetricVerticalLength,Z as formatRelativeAngleDegrees,p as formatRelativeDecimal,l as unitName};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIqgB,SAASA,GAAE,GAAE,GAAEC,IAAE;AAAC,SAAO,EAAE,MAAM,CAAC,EAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,SAAM,GAAG,EAAE,GAAE,EAAC,uBAAsB,GAAE,uBAAsB,GAAE,aAAY,IAAE,IAAE,UAAQ,aAAY,CAAC,CAAC,IAAID,GAAE,GAAEC,IAAE,CAAC,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,SAAM,GAAG,EAAE,GAAE,EAAC,uBAAsB,GAAE,uBAAsB,GAAE,aAAY,aAAY,CAAC,CAAC,IAAID,GAAE,GAAEC,IAAE,CAAC,CAAC;AAAE;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAMC,KAAE,EAAE,GAAED,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAEC,EAAC,GAAEA,IAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAED,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAMC,KAAE,EAAE,GAAED,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAEC,EAAC,GAAEA,IAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAED,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE,IAAE,GAAE,IAAE,QAAO;AAAC,QAAM,IAAE,EAAE,GAAEA,EAAC;AAAE,SAAO,EAAE,GAAE,EAAE,GAAEA,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC;AAAC;AAAoK,SAAS,EAAE,GAAEE,IAAE,GAAE,GAAEC,IAAE;AAAC,EAAAA,KAAE,EAAEA,IAAE,CAAC;AAAE,MAAI,IAAEA,GAAE,UAAU,EAAE,EAAE,GAAED,IAAE,SAAS,GAAE,GAAE,CAAC,GAAE,GAAE,IAAE;AAAE,QAAME,KAAE,EAAC,OAAM,QAAO,aAAY,UAAS,MAAK,UAAS,uBAAsBD,IAAE,uBAAsBA,IAAE,aAAY,IAAE,IAAE,UAAQ,aAAY;AAAE,SAAO,IAAE,EAAE,GAAEC,EAAC,GAAE,EAAE,GAAEA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEF,IAAE,GAAE,GAAE;AAAC,EAAAA,OAAI,MAAI,IAAE,CAAC;AAAG,QAAMC,KAAE,EAAC,OAAM,QAAO,aAAY,UAAS,MAAK,UAAS,uBAAsB,IAAE,EAAE,GAAE,CAAC,GAAE,uBAAsB,GAAE,aAAY,aAAY;AAAE,MAAI,IAAE,EAAE,GAAE,GAAE,SAAS,IAAE;AAAI,SAAO,IAAE,EAAE,GAAEA,EAAC,GAAE,EAAE,GAAEA,EAAC;AAAC;AAAC,IAAM,IAAE,oBAAI;AAAI,SAAS,EAAE,GAAE,GAAE;AAAC,QAAMD,KAAE,KAAK,UAAU,CAAC;AAAE,MAAI,IAAE,EAAE,IAAIA,EAAC;AAAE,SAAO,MAAI,IAAE,IAAI,KAAK,aAAa,SAAQ,CAAC,GAAE,EAAE,IAAIA,IAAE,CAAC,IAAG,YAAY,KAAK,EAAE,OAAO,CAAC,CAAC,IAAE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,MAAG,MAAIA,GAAE,QAAO;AAAE,UAAOA,IAAE;AAAA,IAAC,KAAI;AAAa,aAAOG,GAAE,CAAC;AAAA,IAAE,KAAI;AAAa,aAAOC,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAASD,GAAE,GAAE;AAAC,SAAO,KAAG;AAAC;AAAC,SAASC,GAAE,GAAE;AAAC,SAAM,CAAC,IAAE;AAAE;AAAC,IAAM,IAAE,CAAC,KAAI,MAAK,MAAK,MAAK,IAAI;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,MAAI,IAAE,MAAI,IAAE,IAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAE,KAAK,IAAI,EAAE,SAAS,CAAC;AAAE,MAAE,EAAE,GAAE,GAAE,EAAE,SAAO,CAAC;AAAE,QAAM,IAAE,EAAE,IAAE,EAAE,aAAW,GAAE,EAAC,uBAAsB,EAAC,CAAC;AAAE,SAAO,EAAE,EAAE,MAAM,MAAM,EAAE,CAAC,CAAC,GAAE,EAAC,UAAS,EAAC,CAAC;AAAC;", "names": ["l", "r", "s", "r", "s", "a", "O", "T"]}