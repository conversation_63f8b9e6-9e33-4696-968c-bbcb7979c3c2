{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/EditBusLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Evented.js\";import{clone as s}from\"../../core/lang.js\";import\"../../core/Logger.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/Error.js\";import\"../../core/has.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";const d=new t.EventEmitter,i=\"esri.layers.mixins.EditBusLayer\",l=Symbol(i);function o(e){return null!=e&&\"object\"==typeof e&&l in e}const a=t=>{var o;let a=class extends t{constructor(...e){super(...e),this[o]=!0,this.when().then((()=>{this.own([d.on(\"edits\",(e=>{const t=\"layer\"in e?e.layer:null,r=\"layer\"in e?e.layer?.url:e.serviceUrl,d=\"layer\"in e?e.layer?.layerId:e.layerId,i=e.event;if(t===this||r!==this.url)return;if(null!=d&&null!=this.layerId&&d===this.layerId)return void this.emit(\"edits\",s(i));const l=i.editedFeatures?.find((({layerId:e})=>e===this.layerId));if(l){const{adds:e,updates:t,deletes:r}=l.editedFeatures,d={edits:null,addedAttachments:[],deletedAttachments:[],updatedAttachments:[],addedFeatures:e?e.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],deletedFeatures:r?r.map((({attributes:e})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],updatedFeatures:t?t.map((({current:{attributes:e}})=>({objectId:this.objectIdField&&e[this.objectIdField],globalId:this.globalIdField&&e[this.globalIdField]}))):[],editedFeatures:s(i.editedFeatures),exceededTransferLimit:!1};this.emit(\"edits\",d)}}))])}),(()=>{}))}};return o=l,a=e([r(i)],a),a};export{a as EditBusLayer,d as editEventBus,o as isEditBusLayer};\n"], "mappings": ";;;;;;;;;;;;;;AAI+U,IAAM,IAAE,IAAI,EAAE;AAAd,IAA2B,IAAE;AAA7B,IAA+D,IAAE,OAAO,CAAC;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,MAAG,KAAKA;AAAC;AAAC,IAAMC,KAAE,OAAG;AAAC,MAAIC;AAAE,MAAID,KAAE,cAAc,EAAC;AAAA,IAAC,eAAeD,IAAE;AAAC,YAAM,GAAGA,EAAC,GAAE,KAAKE,EAAC,IAAE,MAAG,KAAK,KAAK,EAAE,KAAM,MAAI;AAAC,aAAK,IAAI,CAAC,EAAE,GAAG,SAAS,CAAAF,OAAG;AAJtlB;AAIulB,gBAAMG,KAAE,WAAUH,KAAEA,GAAE,QAAM,MAAK,IAAE,WAAUA,MAAE,KAAAA,GAAE,UAAF,mBAAS,MAAIA,GAAE,YAAWI,KAAE,WAAUJ,MAAE,KAAAA,GAAE,UAAF,mBAAS,UAAQA,GAAE,SAAQK,KAAEL,GAAE;AAAM,cAAGG,OAAI,QAAM,MAAI,KAAK,IAAI;AAAO,cAAG,QAAMC,MAAG,QAAM,KAAK,WAASA,OAAI,KAAK,QAAQ,QAAO,KAAK,KAAK,KAAK,SAAQ,EAAEC,EAAC,CAAC;AAAE,gBAAMC,MAAE,KAAAD,GAAE,mBAAF,mBAAkB,KAAM,CAAC,EAAC,SAAQL,GAAC,MAAIA,OAAI,KAAK;AAAU,cAAGM,IAAE;AAAC,kBAAK,EAAC,MAAKN,IAAE,SAAQG,IAAE,SAAQI,GAAC,IAAED,GAAE,gBAAeF,KAAE,EAAC,OAAM,MAAK,kBAAiB,CAAC,GAAE,oBAAmB,CAAC,GAAE,oBAAmB,CAAC,GAAE,eAAcJ,KAAEA,GAAE,IAAK,CAAC,EAAC,YAAWA,GAAC,OAAK,EAAC,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,GAAE,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,EAAC,EAAG,IAAE,CAAC,GAAE,iBAAgBO,KAAEA,GAAE,IAAK,CAAC,EAAC,YAAWP,GAAC,OAAK,EAAC,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,GAAE,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,EAAC,EAAG,IAAE,CAAC,GAAE,iBAAgBG,KAAEA,GAAE,IAAK,CAAC,EAAC,SAAQ,EAAC,YAAWH,GAAC,EAAC,OAAK,EAAC,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,GAAE,UAAS,KAAK,iBAAeA,GAAE,KAAK,aAAa,EAAC,EAAG,IAAE,CAAC,GAAE,gBAAe,EAAEK,GAAE,cAAc,GAAE,uBAAsB,MAAE;AAAE,iBAAK,KAAK,SAAQD,EAAC;AAAA,UAAC;AAAA,QAAC,CAAE,CAAC,CAAC;AAAA,MAAC,GAAI,MAAI;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAE,SAAOF,KAAE,GAAED,KAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["e", "a", "o", "t", "d", "i", "l", "r"]}