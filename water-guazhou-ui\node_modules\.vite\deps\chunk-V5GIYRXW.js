import {
  h2
} from "./chunk-SRBBUKOI.js";
import {
  h
} from "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  p as p2,
  w
} from "./chunk-63M4K32A.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  p
} from "./chunk-REW33H3I.js";

// node_modules/@arcgis/core/geometry/Polyline.js
var c;
function u(t) {
  return !Array.isArray(t[0]);
}
var f2 = c = class extends p2 {
  constructor(...t) {
    super(...t), this.paths = [], this.type = "polyline";
  }
  normalizeCtorArgs(t, e2) {
    let s, r2, i = null, a2 = null;
    return t && !Array.isArray(t) ? (i = t.paths ? t.paths : null, e2 || (t.spatialReference ? e2 = t.spatialReference : t.paths || (e2 = t)), s = t.hasZ, r2 = t.hasM) : i = t, i = i || [], e2 = e2 || f.WGS84, i.length && i[0] && null != i[0][0] && "number" == typeof i[0][0] && (i = [i]), a2 = i[0] && i[0][0], a2 && (void 0 === s && void 0 === r2 ? (s = a2.length > 2, r2 = false) : void 0 === s ? s = !r2 && a2.length > 3 : void 0 === r2 && (r2 = !s && a2.length > 3)), { paths: i, spatialReference: e2, hasZ: s, hasM: r2 };
  }
  get cache() {
    return this.commitProperty("paths"), this.commitProperty("hasZ"), this.commitProperty("hasM"), this.commitProperty("spatialReference"), {};
  }
  get extent() {
    const { spatialReference: t } = this, e2 = h2(this);
    if (!e2) return null;
    const s = new w2(e2);
    return s.spatialReference = t, s;
  }
  writePaths(t, s) {
    s.paths = p(this.paths);
  }
  addPath(t) {
    if (!t) return;
    const e2 = this.paths, s = e2.length;
    if (u(t)) {
      const r2 = [];
      for (let e3 = 0, s2 = t.length; e3 < s2; e3++) r2[e3] = t[e3].toArray();
      e2[s] = r2;
    } else e2[s] = t.concat();
    return this.notifyChange("paths"), this;
  }
  clone() {
    const t = new c();
    return t.spatialReference = this.spatialReference, t.paths = p(this.paths), t.hasZ = this.hasZ, t.hasM = this.hasM, t;
  }
  getPoint(t, e2) {
    if (!this._validateInputs(t, e2)) return null;
    const s = this.paths[t][e2], r2 = this.hasZ, i = this.hasM;
    return r2 && !i ? new w(s[0], s[1], s[2], void 0, this.spatialReference) : i && !r2 ? new w(s[0], s[1], void 0, s[2], this.spatialReference) : r2 && i ? new w(s[0], s[1], s[2], s[3], this.spatialReference) : new w(s[0], s[1], this.spatialReference);
  }
  insertPoint(t, e2, s) {
    return this._validateInputs(t, e2, true) ? (h(this, s), Array.isArray(s) || (s = s.toArray()), this.paths[t].splice(e2, 0, s), this.notifyChange("paths"), this) : this;
  }
  removePath(t) {
    if (!this._validateInputs(t, null)) return null;
    const e2 = this.paths.splice(t, 1)[0], s = this.spatialReference, r2 = e2.map((t2) => new w(t2, s));
    return this.notifyChange("paths"), r2;
  }
  removePoint(t, e2) {
    if (!this._validateInputs(t, e2)) return null;
    const s = new w(this.paths[t].splice(e2, 1)[0], this.spatialReference);
    return this.notifyChange("paths"), s;
  }
  setPoint(t, e2, s) {
    return this._validateInputs(t, e2) ? (h(this, s), Array.isArray(s) || (s = s.toArray()), this.paths[t][e2] = s, this.notifyChange("paths"), this) : this;
  }
  _validateInputs(t, e2, s = false) {
    if (null == t || t < 0 || t >= this.paths.length) return false;
    if (null != e2) {
      const r2 = this.paths[t];
      if (s && (e2 < 0 || e2 > r2.length)) return false;
      if (!s && (e2 < 0 || e2 >= r2.length)) return false;
    }
    return true;
  }
  toJSON(t) {
    return this.write({}, t);
  }
};
e([y({ readOnly: true })], f2.prototype, "cache", null), e([y({ readOnly: true })], f2.prototype, "extent", null), e([y({ type: [[[Number]]], json: { write: { isRequired: true } } })], f2.prototype, "paths", void 0), e([r("paths")], f2.prototype, "writePaths", null), f2 = c = e([a("esri.geometry.Polyline")], f2), f2.prototype.toJSON.isDefaultToJSON = true;
var m = f2;

export {
  m
};
//# sourceMappingURL=chunk-V5GIYRXW.js.map
