{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/hydratedFeatures.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import e from\"../../Graphic.js\";import\"../../core/has.js\";import{clone as t}from\"../../core/lang.js\";import{isNone as r}from\"../../core/maybe.js\";import{isFloat32Array as n,isFloat64Array as a}from\"../../core/typedArrayUtil.js\";import{fromJSON as s}from\"../../geometry/support/jsonUtils.js\";import{makeDehydratedPoint as i}from\"./dehydratedFeatures.js\";import o from\"../../geometry/SpatialReference.js\";function m(e){return\"declaredClass\"in e}function c(e){return\"declaredClass\"in e}function l(e){return\"declaredClass\"in e}function u(r,n){return r?l(r)?r:new e({layer:n,sourceLayer:n,visible:r.visible,symbol:t(r.symbol),attributes:t(r.attributes),geometry:f(r.geometry)}):null}function f(e){return r(e)?null:m(e)?e:s(p(e))}function p(e){const{wkid:t,wkt:r,latestWkid:n}=e.spatialReference,a={wkid:t,wkt:r,latestWkid:n};switch(e.type){case\"point\":{const{x:t,y:r,z:n,m:s}=e;return{x:t,y:r,z:n,m:s,spatialReference:a}}case\"polygon\":{const{rings:t,hasZ:r,hasM:n}=e;return{rings:h(t),hasZ:r,hasM:n,spatialReference:a}}case\"polyline\":{const{paths:t,hasZ:r,hasM:n}=e;return{paths:h(t),hasZ:r,hasM:n,spatialReference:a}}case\"extent\":{const{xmin:t,xmax:r,ymin:n,ymax:s,zmin:i,zmax:o,mmin:m,mmax:c,hasZ:l,hasM:u}=e;return{xmin:t,xmax:r,ymin:n,ymax:s,zmin:i,zmax:o,mmin:m,mmax:c,hasZ:l,hasM:u,spatialReference:a}}case\"multipoint\":{const{points:t,hasZ:r,hasM:n}=e;return{points:x(t)?y(t):t,hasZ:r,hasM:n,spatialReference:a}}default:return}}function h(e){return d(e)?e.map((e=>y(e))):e}function y(e){return e.map((e=>Array.from(e)))}function d(e){for(const t of e)if(0!==t.length)return x(t);return!1}function x(e){return e.length>0&&(n(e[0])||a(e[0]))}function M(e,t){if(!e)return null;let r;if(c(e)){if(null==t)return e.clone();if(c(t))return t.copy(e)}return null!=t?(r=t,r.x=e.x,r.y=e.y,r.spatialReference=e.spatialReference,e.hasZ?(r.z=e.z,r.hasZ=e.hasZ):(r.z=void 0,r.hasZ=!1),e.hasM?(r.m=e.m,r.hasM=!0):(r.m=void 0,r.hasM=!1)):(r=i(e.x,e.y,e.z,e.spatialReference),e.hasM&&(r.m=e.m,r.hasM=!0)),r}function k(e){const{wkid:t,wkt:r,latestWkid:n}=e,a={wkid:t,wkt:r,latestWkid:n};return o.fromJSON(a)}export{M as clonePoint,f as hydrateGeometry,u as hydrateGraphic,k as hydratedSpatialReference,m as isHydratedGeometry,l as isHydratedGraphic,c as isHydratedPoint};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAI6a,SAAS,EAAE,GAAE;AAAC,SAAM,mBAAkB;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,mBAAkB;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,mBAAkB;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,SAAO,IAAE,EAAE,CAAC,IAAE,IAAE,IAAI,EAAE,EAAC,OAAM,GAAE,aAAY,GAAE,SAAQ,EAAE,SAAQ,QAAO,EAAE,EAAE,MAAM,GAAE,YAAW,EAAE,EAAE,UAAU,GAAE,UAASA,GAAE,EAAE,QAAQ,EAAC,CAAC,IAAE;AAAI;AAAC,SAASA,GAAE,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,OAAK,EAAE,CAAC,IAAE,IAAE,EAAEC,GAAE,CAAC,CAAC;AAAC;AAAC,SAASA,GAAE,GAAE;AAAC,QAAK,EAAC,MAAKC,IAAE,KAAI,GAAE,YAAW,EAAC,IAAE,EAAE,kBAAiB,IAAE,EAAC,MAAKA,IAAE,KAAI,GAAE,YAAW,EAAC;AAAE,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI,SAAQ;AAAC,YAAK,EAAC,GAAEA,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAE,EAAC,IAAE;AAAE,aAAM,EAAC,GAAEF,IAAE,GAAEC,IAAE,GAAEC,IAAE,GAAE,GAAE,kBAAiB,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI,WAAU;AAAC,YAAK,EAAC,OAAMF,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE;AAAE,aAAM,EAAC,OAAM,EAAEF,EAAC,GAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI,YAAW;AAAC,YAAK,EAAC,OAAMF,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE;AAAE,aAAM,EAAC,OAAM,EAAEF,EAAC,GAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI,UAAS;AAAC,YAAK,EAAC,MAAKF,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAK,GAAE,MAAKC,IAAE,MAAK,GAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE;AAAE,aAAM,EAAC,MAAKP,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAK,GAAE,MAAKC,IAAE,MAAK,GAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAC;AAAA,IAAC;AAAA,IAAC,KAAI,cAAa;AAAC,YAAK,EAAC,QAAOP,IAAE,MAAKC,IAAE,MAAKC,GAAC,IAAE;AAAE,aAAM,EAAC,QAAO,EAAEF,EAAC,IAAE,EAAEA,EAAC,IAAEA,IAAE,MAAKC,IAAE,MAAKC,IAAE,kBAAiB,EAAC;AAAA,IAAC;AAAA,IAAC;AAAQ;AAAA,EAAM;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,CAAC,IAAE,EAAE,IAAK,CAAAM,OAAG,EAAEA,EAAC,CAAE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,IAAK,CAAAA,OAAG,MAAM,KAAKA,EAAC,CAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,aAAUR,MAAK,EAAE,KAAG,MAAIA,GAAE,OAAO,QAAO,EAAEA,EAAC;AAAE,SAAM;AAAE;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,SAAO,MAAI,EAAE,EAAE,CAAC,CAAC,KAAG,EAAE,EAAE,CAAC,CAAC;AAAE;AAAC,SAASS,GAAE,GAAET,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,MAAI;AAAE,MAAG,EAAE,CAAC,GAAE;AAAC,QAAG,QAAMA,GAAE,QAAO,EAAE,MAAM;AAAE,QAAG,EAAEA,EAAC,EAAE,QAAOA,GAAE,KAAK,CAAC;AAAA,EAAC;AAAC,SAAO,QAAMA,MAAG,IAAEA,IAAE,EAAE,IAAE,EAAE,GAAE,EAAE,IAAE,EAAE,GAAE,EAAE,mBAAiB,EAAE,kBAAiB,EAAE,QAAM,EAAE,IAAE,EAAE,GAAE,EAAE,OAAK,EAAE,SAAO,EAAE,IAAE,QAAO,EAAE,OAAK,QAAI,EAAE,QAAM,EAAE,IAAE,EAAE,GAAE,EAAE,OAAK,SAAK,EAAE,IAAE,QAAO,EAAE,OAAK,WAAM,IAAE,EAAE,EAAE,GAAE,EAAE,GAAE,EAAE,GAAE,EAAE,gBAAgB,GAAE,EAAE,SAAO,EAAE,IAAE,EAAE,GAAE,EAAE,OAAK,QAAK;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,MAAKA,IAAE,KAAI,GAAE,YAAW,EAAC,IAAE,GAAE,IAAE,EAAC,MAAKA,IAAE,KAAI,GAAE,YAAW,EAAC;AAAE,SAAOF,GAAE,SAAS,CAAC;AAAC;", "names": ["f", "p", "t", "r", "n", "i", "m", "c", "l", "u", "e", "M"]}