{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/Offset.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/InstancedDoublePrecision.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/collections/Component/Material/shader/DecodeSymbolColor.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/attributes/SymbolColor.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/DiscardOrAdjustAlphaBlend.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/AlphaDiscard.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/default/DefaultMaterialAuxiliaryPasses.glsl.js", "../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/util/MixExternalColor.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{glsl as o}from\"../shaderModules/interfaces.js\";function e(e){e.vertex.code.add(o`vec4 offsetBackfacingClipPosition(vec4 posClip, vec3 posWorld, vec3 normalWorld, vec3 camPosWorld) {\nvec3 camToVert = posWorld - camPosWorld;\nbool isBackface = dot(camToVert, normalWorld) > 0.0;\nif (isBackface) {\nposClip.z += 0.0000003 * posClip.w;\n}\nreturn posClip;\n}`)}export{e as Offset};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../../chunks/tslib.es6.js\";import{s as r}from\"../../../../../../chunks/vec3.js\";import{c as i}from\"../../../../../../chunks/vec3f64.js\";import{ShaderOutput as o}from\"../ShaderOutput.js\";import{DoublePrecision as n}from\"../util/DoublePrecision.glsl.js\";import{addViewNormal as a}from\"../util/View.glsl.js\";import{Float3DrawUniform as t}from\"../../shaderModules/Float3DrawUniform.js\";import{glsl as s}from\"../../shaderModules/interfaces.js\";import{ShaderTechniqueConfiguration as c,parameter as d}from\"../../shaderTechnique/ShaderTechniqueConfiguration.js\";import{VertexAttribute as l}from\"../../../lib/VertexAttribute.js\";import{encodeDoubleHi as m,encodeDoubleLo as u}from\"../../../../../webgl/doublePrecisionUtils.js\";class v extends c{constructor(){super(...arguments),this.instancedDoublePrecision=!1}}function p(e,i){i.instanced&&i.instancedDoublePrecision&&(e.attributes.add(l.MODELORIGINHI,\"vec3\"),e.attributes.add(l.MODELORIGINLO,\"vec3\"),e.attributes.add(l.MODEL,\"mat3\"),e.attributes.add(l.MODELNORMAL,\"mat3\"));const c=e.vertex;i.instancedDoublePrecision&&(c.include(n,i),c.uniforms.add(new t(\"viewOriginHi\",((e,i)=>m(r(b,i.camera.viewInverseTransposeMatrix[3],i.camera.viewInverseTransposeMatrix[7],i.camera.viewInverseTransposeMatrix[11]),b)))),c.uniforms.add(new t(\"viewOriginLo\",((e,i)=>u(r(b,i.camera.viewInverseTransposeMatrix[3],i.camera.viewInverseTransposeMatrix[7],i.camera.viewInverseTransposeMatrix[11]),b))))),c.code.add(s`\n    vec3 calculateVPos() {\n      ${i.instancedDoublePrecision?\"return model * localPosition().xyz;\":\"return localPosition().xyz;\"}\n    }\n    `),c.code.add(s`\n    vec3 subtractOrigin(vec3 _pos) {\n      ${i.instancedDoublePrecision?s`\n          vec3 originDelta = dpAdd(viewOriginHi, viewOriginLo, -modelOriginHi, -modelOriginLo);\n          return _pos - originDelta;`:\"return vpos;\"}\n    }\n    `),c.code.add(s`\n    vec3 dpNormal(vec4 _normal) {\n      ${i.instancedDoublePrecision?\"return normalize(modelNormal * _normal.xyz);\":\"return normalize(_normal.xyz);\"}\n    }\n    `),i.output===o.Normal&&(a(c),c.code.add(s`\n    vec3 dpNormalView(vec4 _normal) {\n      ${i.instancedDoublePrecision?\"return normalize((viewNormal * vec4(modelNormal * _normal.xyz, 1.0)).xyz);\":\"return normalize((viewNormal * _normal).xyz);\"}\n    }\n    `)),i.hasVertexTangents&&c.code.add(s`\n    vec4 dpTransformVertexTangent(vec4 _tangent) {\n      ${i.instancedDoublePrecision?\"return vec4(modelNormal * _tangent.xyz, _tangent.w);\":\"return _tangent;\"}\n\n    }\n    `)}e([d()],v.prototype,\"instancedDoublePrecision\",void 0);const b=i();export{v as InstancedDoubleConfiguration,p as InstancedDoublePrecision};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ColorMixModeEnum as o}from\"../../../../../layers/support/symbolColorUtils.js\";import{glsl as l}from\"../../../../core/shaderModules/interfaces.js\";function e(e){e.vertex.code.add(l`\n    vec4 decodeSymbolColor(vec4 symbolColor, out int colorMixMode) {\n      float symbolAlpha = 0.0;\n\n      const float maxTint = 85.0;\n      const float maxReplace = 170.0;\n      const float scaleAlpha = 3.0;\n\n      if (symbolColor.a > maxReplace) {\n        colorMixMode = ${l.int(o.Multiply)};\n        symbolAlpha = scaleAlpha * (symbolColor.a - maxReplace);\n      } else if (symbolColor.a > maxTint) {\n        colorMixMode = ${l.int(o.Replace)};\n        symbolAlpha = scaleAlpha * (symbolColor.a - maxTint);\n      } else if (symbolColor.a > 0.0) {\n        colorMixMode = ${l.int(o.Tint)};\n        symbolAlpha = scaleAlpha * symbolColor.a;\n      } else {\n        colorMixMode = ${l.int(o.Multiply)};\n        symbolAlpha = 0.0;\n      }\n\n      return vec4(symbolColor.r, symbolColor.g, symbolColor.b, symbolAlpha);\n    }\n  `)}export{e as DecodeSymbolColor};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DecodeSymbolColor as o}from\"../../../collections/Component/Material/shader/DecodeSymbolColor.glsl.js\";import{IntegerPassUniform as r}from\"../../shaderModules/IntegerPassUniform.js\";import{glsl as e}from\"../../shaderModules/interfaces.js\";import{VertexAttribute as l}from\"../../../lib/VertexAttribute.js\";import{colorMixModes as d}from\"../../../materials/internal/MaterialUtil.js\";function i(i,t){t.hasSymbolColors?(i.include(o),i.attributes.add(l.SYMBOLCOLOR,\"vec4\"),i.varyings.add(\"colorMixMode\",\"mediump float\"),i.vertex.code.add(e`int symbolColorMixMode;\nvec4 getSymbolColor() {\nreturn decodeSymbolColor(symbolColor, symbolColorMixMode) * 0.003921568627451;\n}\nvoid forwardColorMixMode() {\ncolorMixMode = float(symbolColorMixMode) + 0.5;\n}`)):(i.fragment.uniforms.add(new r(\"colorMixMode\",(o=>d[o.colorMixMode]))),i.vertex.code.add(e`vec4 getSymbolColor() { return vec4(1.0); }\nvoid forwardColorMixMode() {}`))}export{i as SymbolColor};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{symbol<PERSON>l<PERSON><PERSON><PERSON>ff as o}from\"./AlphaCutoff.js\";import{glsl as r}from\"../../shaderModules/interfaces.js\";function d(d){d.fragment.code.add(r`\n    #define discardOrAdjustAlpha(color) { if (color.a < ${r.float(o)}) { discard; } }\n  `)}export{d as DiscardOrAdjustAlphaBlend};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DiscardOrAdjustAlphaBlend as a}from\"./DiscardOrAdjustAlphaBlend.glsl.js\";import{FloatDrawUniform as e}from\"../../shaderModules/FloatDrawUniform.js\";import{FloatPassUniform as r}from\"../../shaderModules/FloatPassUniform.js\";import{glsl as o}from\"../../shaderModules/interfaces.js\";import{AlphaDiscardMode as d}from\"../../../lib/basicInterfaces.js\";function s(a,e){l(a,e,new r(\"textureAlphaCutoff\",(a=>a.textureAlphaCutoff)))}function t(a,r){l(a,r,new e(\"textureAlphaCutoff\",(a=>a.textureAlphaCutoff)))}function l(e,r,s){const t=e.fragment;switch(r.alphaDiscardMode!==d.Mask&&r.alphaDiscardMode!==d.MaskBlend||t.uniforms.add(s),r.alphaDiscardMode){case d.Blend:return e.include(a);case d.Opaque:t.code.add(o`void discardOrAdjustAlpha(inout vec4 color) {\ncolor.a = 1.0;\n}`);break;case d.Mask:t.code.add(o`#define discardOrAdjustAlpha(color) { if (color.a < textureAlphaCutoff) { discard; } else { color.a = 1.0; } }`);break;case d.MaskBlend:e.fragment.code.add(o`#define discardOrAdjustAlpha(color) { if (color.a < textureAlphaCutoff) { discard; } }`)}}export{t as DiscardOrAdjustAlphaDraw,s as DiscardOrAdjustAlphaPass};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as o}from\"../../../../../../core/maybe.js\";import{I as r}from\"../../../../../../chunks/mat4f64.js\";import{addNearFar as e}from\"../ForwardLinearDepth.glsl.js\";import{ShaderOutput as t}from\"../ShaderOutput.js\";import{SliceDraw as i}from\"../Slice.glsl.js\";import{Transform as a}from\"../Transform.glsl.js\";import{NormalAttribute as s,NormalAttributeType as l}from\"../attributes/NormalAttribute.glsl.js\";import{ObjectAndLayerIdColor as d}from\"../attributes/ObjectAndLayerIdColor.glsl.js\";import{TextureCoordinateAttribute as n}from\"../attributes/TextureCoordinateAttribute.glsl.js\";import{VertexNormal as c}from\"../attributes/VertexNormal.glsl.js\";import{OutputDepth as u}from\"../output/OutputDepth.glsl.js\";import{OutputHighlight as m}from\"../output/OutputHighlight.glsl.js\";import{VisualVariables as p}from\"../shading/VisualVariables.glsl.js\";import{DiscardOrAdjustAlphaPass as v}from\"../util/AlphaDiscard.glsl.js\";import{addProjViewLocalOrigin as f}from\"../util/View.glsl.js\";import{glsl as g}from\"../../shaderModules/interfaces.js\";import{Matrix4PassUniform as h}from\"../../shaderModules/Matrix4PassUniform.js\";import{Texture2DPassUniform as x}from\"../../shaderModules/Texture2DPassUniform.js\";import{AlphaDiscardMode as j}from\"../../../lib/basicInterfaces.js\";function b(b,O){const{vertex:w,fragment:C}=b,T=O.hasModelTransformation;T&&w.uniforms.add(new h(\"model\",(e=>o(e.modelTransformation)?e.modelTransformation:r)));const V=O.hasColorTexture&&O.alphaDiscardMode!==j.Opaque;switch(O.output){case t.Depth:case t.Shadow:case t.ShadowHighlight:case t.ShadowExcludeHighlight:case t.ObjectAndLayerIdColor:f(w,O),b.include(a,O),b.include(n,O),b.include(p,O),b.include(u,O),b.include(i,O),b.include(d,O),e(b),b.varyings.add(\"depth\",\"float\"),V&&C.uniforms.add(new x(\"tex\",(o=>o.texture))),w.code.add(g`\n          void main(void) {\n            vpos = calculateVPos();\n            vpos = subtractOrigin(vpos);\n            vpos = addVerticalOffset(vpos, localOrigin);\n            gl_Position = transformPositionWithDepth(proj, view, ${T?\"model,\":\"\"} vpos, nearFar, depth);\n            forwardTextureCoordinates();\n            forwardObjectAndLayerIdColor();\n          }\n        `),b.include(v,O),C.code.add(g`\n          void main(void) {\n            discardBySlice(vpos);\n            ${V?g`\n                    vec4 texColor = texture2D(tex, ${O.hasColorTextureTransform?g`colorUV`:g`vuv0`});\n                    discardOrAdjustAlpha(texColor);`:\"\"}\n            ${O.output===t.ObjectAndLayerIdColor?g`outputObjectAndLayerIdColor();`:g`outputDepth(depth);`}\n          }\n        `);break;case t.Normal:f(w,O),b.include(a,O),b.include(s,O),b.include(c,O),b.include(n,O),b.include(p,O),V&&C.uniforms.add(new x(\"tex\",(o=>o.texture))),b.varyings.add(\"vPositionView\",\"vec3\"),w.code.add(g`\n          void main(void) {\n            vpos = calculateVPos();\n            vpos = subtractOrigin(vpos);\n            ${O.normalType===l.Attribute?g`\n            vNormalWorld = dpNormalView(vvLocalNormal(normalModel()));`:\"\"}\n            vpos = addVerticalOffset(vpos, localOrigin);\n            gl_Position = transformPosition(proj, view, ${T?\"model,\":\"\"} vpos);\n            forwardTextureCoordinates();\n          }\n        `),b.include(i,O),b.include(v,O),C.code.add(g`\n          void main() {\n            discardBySlice(vpos);\n            ${V?g`\n                    vec4 texColor = texture2D(tex, ${O.hasColorTextureTransform?g`colorUV`:g`vuv0`});\n                    discardOrAdjustAlpha(texColor);`:\"\"}\n\n            ${O.normalType===l.ScreenDerivative?g`\n                vec3 normal = screenDerivativeNormal(vPositionView);`:g`\n                vec3 normal = normalize(vNormalWorld);\n                if (gl_FrontFacing == false) normal = -normal;`}\n            gl_FragColor = vec4(vec3(0.5) + 0.5 * normal, 1.0);\n          }\n        `);break;case t.Highlight:f(w,O),b.include(a,O),b.include(n,O),b.include(p,O),V&&C.uniforms.add(new x(\"tex\",(o=>o.texture))),w.code.add(g`\n          void main(void) {\n            vpos = calculateVPos();\n            vpos = subtractOrigin(vpos);\n            vpos = addVerticalOffset(vpos, localOrigin);\n            gl_Position = transformPosition(proj, view, ${T?\"model,\":\"\"} vpos);\n            forwardTextureCoordinates();\n          }\n        `),b.include(i,O),b.include(v,O),b.include(m,O),C.code.add(g`\n          void main() {\n            discardBySlice(vpos);\n            ${V?g`\n                    vec4 texColor = texture2D(tex, ${O.hasColorTextureTransform?g`colorUV`:g`vuv0`});\n                    discardOrAdjustAlpha(texColor);`:\"\"}\n            outputHighlight();\n          }\n        `)}}export{b as DefaultMaterialAuxiliaryPasses};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ColorMixModeEnum as e}from\"../../../../layers/support/symbolColorUtils.js\";import{ColorConversion as t}from\"./ColorConversion.glsl.js\";import{glsl as r}from\"../../shaderModules/interfaces.js\";function i(i){i.include(t),i.code.add(r`\n    vec3 mixExternalColor(vec3 internalColor, vec3 textureColor, vec3 externalColor, int mode) {\n      // workaround for artifacts in OSX using Intel Iris Pro\n      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/10475\n      vec3 internalMixed = internalColor * textureColor;\n      vec3 allMixed = internalMixed * externalColor;\n\n      if (mode == ${r.int(e.Multiply)}) {\n        return allMixed;\n      }\n      if (mode == ${r.int(e.Ignore)}) {\n        return internalMixed;\n      }\n      if (mode == ${r.int(e.Replace)}) {\n        return externalColor;\n      }\n\n      // tint (or something invalid)\n      float vIn = rgb2v(internalMixed);\n      vec3 hsvTint = rgb2hsv(externalColor);\n      vec3 hsvOut = vec3(hsvTint.x, hsvTint.y, vIn * hsvTint.z);\n      return hsv2rgb(hsvOut);\n    }\n\n    float mixExternalOpacity(float internalOpacity, float textureOpacity, float externalOpacity, int mode) {\n      // workaround for artifacts in OSX using Intel Iris Pro\n      // see: https://devtopia.esri.com/WebGIS/arcgis-js-api/issues/10475\n      float internalMixed = internalOpacity * textureOpacity;\n      float allMixed = internalMixed * externalOpacity;\n\n      if (mode == ${r.int(e.Ignore)}) {\n        return internalMixed;\n      }\n      if (mode == ${r.int(e.Replace)}) {\n        return externalOpacity;\n      }\n\n      // multiply or tint (or something invalid)\n      return allMixed;\n    }\n  `)}export{i as MixExternalColor};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsD,SAASA,GAAEA,IAAE;AAAC,EAAAA,GAAE,OAAO,KAAK,IAAIC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpF;AAAC;;;ACP2uB,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,2BAAyB;AAAA,EAAE;AAAC;AAAC,SAAS,EAAEC,IAAEC,IAAE;AAAC,EAAAA,GAAE,aAAWA,GAAE,6BAA2BD,GAAE,WAAW,IAAI,EAAE,eAAc,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,eAAc,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,OAAM,MAAM,GAAEA,GAAE,WAAW,IAAI,EAAE,aAAY,MAAM;AAAG,QAAME,KAAEF,GAAE;AAAO,EAAAC,GAAE,6BAA2BC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,IAAIC,GAAE,gBAAgB,CAACH,IAAEC,OAAIE,GAAE,EAAE,GAAEF,GAAE,OAAO,2BAA2B,CAAC,GAAEA,GAAE,OAAO,2BAA2B,CAAC,GAAEA,GAAE,OAAO,2BAA2B,EAAE,CAAC,GAAE,CAAC,CAAE,CAAC,GAAEC,GAAE,SAAS,IAAI,IAAIC,GAAE,gBAAgB,CAACH,IAAEC,OAAIG,GAAE,EAAE,GAAEH,GAAE,OAAO,2BAA2B,CAAC,GAAEA,GAAE,OAAO,2BAA2B,CAAC,GAAEA,GAAE,OAAO,2BAA2B,EAAE,CAAC,GAAE,CAAC,CAAE,CAAC,IAAGC,GAAE,KAAK,IAAIC;AAAA;AAAA,QAEx7CF,GAAE,2BAAyB,wCAAsC,6BAA6B;AAAA;AAAA,KAEjG,GAAEC,GAAE,KAAK,IAAIC;AAAA;AAAA,QAEVF,GAAE,2BAAyBE;AAAA;AAAA,wCAEG,cAAc;AAAA;AAAA,KAE/C,GAAED,GAAE,KAAK,IAAIC;AAAA;AAAA,QAEVF,GAAE,2BAAyB,iDAA+C,gCAAgC;AAAA;AAAA,KAE7G,GAAEA,GAAE,WAAS,EAAE,WAAS,EAAEC,EAAC,GAAEA,GAAE,KAAK,IAAIC;AAAA;AAAA,QAErCF,GAAE,2BAAyB,+EAA6E,+CAA+C;AAAA;AAAA,KAE1J,IAAGA,GAAE,qBAAmBC,GAAE,KAAK,IAAIC;AAAA;AAAA,QAEhCF,GAAE,2BAAyB,yDAAuD,kBAAkB;AAAA;AAAA;AAAA,KAGvG;AAAC;AAAC,EAAE,CAACG,GAAE,CAAC,GAAEN,GAAE,WAAU,4BAA2B,MAAM;AAAE,IAAM,IAAE,EAAE;;;ACvBiF,SAASO,GAAEA,IAAE;AAAC,EAAAA,GAAE,OAAO,KAAK,IAAIC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAShKA,GAAE,IAAIC,GAAE,QAAQ,CAAC;AAAA;AAAA;AAAA,yBAGjBD,GAAE,IAAIC,GAAE,OAAO,CAAC;AAAA;AAAA;AAAA,yBAGhBD,GAAE,IAAIC,GAAE,IAAI,CAAC;AAAA;AAAA;AAAA,yBAGbD,GAAE,IAAIC,GAAE,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMvC;AAAC;;;ACxB+X,SAASC,GAAEA,IAAEC,IAAE;AAAC,EAAAA,GAAE,mBAAiBD,GAAE,QAAQE,EAAC,GAAEF,GAAE,WAAW,IAAI,EAAE,aAAY,MAAM,GAAEA,GAAE,SAAS,IAAI,gBAAe,eAAe,GAAEA,GAAE,OAAO,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzhB,MAAIH,GAAE,SAAS,SAAS,IAAI,IAAIE,GAAE,gBAAgB,CAAAC,OAAG,EAAEA,GAAE,YAAY,CAAE,CAAC,GAAEH,GAAE,OAAO,KAAK,IAAIG;AAAA,8BAChE;AAAE;;;ACP8E,SAASC,GAAEA,IAAE;AAAC,EAAAA,GAAE,SAAS,KAAK,IAAIC;AAAA,0DACtFA,GAAE,MAAM,CAAC,CAAC;AAAA,GACjE;AAAC;;;ACF8V,SAASC,GAAEC,IAAEC,IAAE;AAAC,IAAED,IAAEC,IAAE,IAAIC,GAAE,sBAAsB,CAAAF,OAAGA,GAAE,kBAAmB,CAAC;AAAC;AAA8E,SAAS,EAAEG,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAS,UAAOC,GAAE,qBAAmB,EAAE,QAAMA,GAAE,qBAAmB,EAAE,aAAWE,GAAE,SAAS,IAAID,EAAC,GAAED,GAAE,kBAAiB;AAAA,IAAC,KAAK,EAAE;AAAM,aAAOD,GAAE,QAAQI,EAAC;AAAA,IAAE,KAAK,EAAE;AAAO,MAAAD,GAAE,KAAK,IAAIE;AAAA;AAAA,EAErsB;AAAE;AAAA,IAAM,KAAK,EAAE;AAAK,MAAAF,GAAE,KAAK,IAAIE,kHAAiH;AAAE;AAAA,IAAM,KAAK,EAAE;AAAU,MAAAL,GAAE,SAAS,KAAK,IAAIK,0FAAyF;AAAA,EAAC;AAAC;;;ACFg+B,SAASC,GAAEA,IAAEC,IAAE;AAAC,QAAK,EAAC,QAAO,GAAE,UAAS,EAAC,IAAED,IAAE,IAAEC,GAAE;AAAuB,OAAG,EAAE,SAAS,IAAI,IAAIC,GAAE,SAAS,CAAAA,OAAG,EAAEA,GAAE,mBAAmB,IAAEA,GAAE,sBAAoBC,EAAE,CAAC;AAAE,QAAM,IAAEF,GAAE,mBAAiBA,GAAE,qBAAmB,EAAE;AAAO,UAAOA,GAAE,QAAO;AAAA,IAAC,KAAK,EAAE;AAAA,IAAM,KAAK,EAAE;AAAA,IAAO,KAAK,EAAE;AAAA,IAAgB,KAAK,EAAE;AAAA,IAAuB,KAAK,EAAE;AAAsB,QAAE,GAAEA,EAAC,GAAED,GAAE,QAAQI,IAAEH,EAAC,GAAED,GAAE,QAAQG,IAAEF,EAAC,GAAED,GAAE,QAAQ,GAAEC,EAAC,GAAED,GAAE,QAAQG,IAAEF,EAAC,GAAED,GAAE,QAAQK,IAAEJ,EAAC,GAAED,GAAE,QAAQM,IAAEL,EAAC,GAAEM,GAAEP,EAAC,GAAEA,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAE,KAAG,EAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAG,OAAGA,GAAE,OAAQ,CAAC,GAAE,EAAE,KAAK,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA,mEAK9sD,IAAE,WAAS,EAAE;AAAA;AAAA;AAAA;AAAA,SAIvE,GAAEH,GAAE,QAAQQ,IAAEP,EAAC,GAAE,EAAE,KAAK,IAAIE;AAAA;AAAA;AAAA,cAGvB,IAAEA;AAAA,qDACqCF,GAAE,2BAAyBE,cAAWA,QAAO;AAAA,uDAC7C,EAAE;AAAA,cACzCF,GAAE,WAAS,EAAE,wBAAsBE,qCAAkCA,uBAAsB;AAAA;AAAA,SAEhG;AAAE;AAAA,IAAM,KAAK,EAAE;AAAO,QAAE,GAAEF,EAAC,GAAED,GAAE,QAAQI,IAAEH,EAAC,GAAED,GAAE,QAAQ,GAAEC,EAAC,GAAED,GAAE,QAAQS,IAAER,EAAC,GAAED,GAAE,QAAQG,IAAEF,EAAC,GAAED,GAAE,QAAQ,GAAEC,EAAC,GAAE,KAAG,EAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAE,OAAGA,GAAE,OAAQ,CAAC,GAAEH,GAAE,SAAS,IAAI,iBAAgB,MAAM,GAAE,EAAE,KAAK,IAAIG;AAAA;AAAA;AAAA;AAAA,cAIpMF,GAAE,eAAa,EAAE,YAAUE;AAAA,0EAC+B,EAAE;AAAA;AAAA,0DAEhB,IAAE,WAAS,EAAE;AAAA;AAAA;AAAA,SAG9D,GAAEH,GAAE,QAAQK,IAAEJ,EAAC,GAAED,GAAE,QAAQQ,IAAEP,EAAC,GAAE,EAAE,KAAK,IAAIE;AAAA;AAAA;AAAA,cAGtC,IAAEA;AAAA,qDACqCF,GAAE,2BAAyBE,cAAWA,QAAO;AAAA,uDAC7C,EAAE;AAAA;AAAA,cAEzCF,GAAE,eAAa,EAAE,mBAAiBE;AAAA,wEACsBA;AAAA;AAAA,+DAEP;AAAA;AAAA;AAAA,SAGtD;AAAE;AAAA,IAAM,KAAK,EAAE;AAAU,QAAE,GAAEF,EAAC,GAAED,GAAE,QAAQI,IAAEH,EAAC,GAAED,GAAE,QAAQG,IAAEF,EAAC,GAAED,GAAE,QAAQ,GAAEC,EAAC,GAAE,KAAG,EAAE,SAAS,IAAI,IAAI,EAAE,OAAO,CAAAE,OAAGA,GAAE,OAAQ,CAAC,GAAE,EAAE,KAAK,IAAIA;AAAA;AAAA;AAAA;AAAA;AAAA,0DAKtF,IAAE,WAAS,EAAE;AAAA;AAAA;AAAA,SAG9D,GAAEH,GAAE,QAAQK,IAAEJ,EAAC,GAAED,GAAE,QAAQQ,IAAEP,EAAC,GAAED,GAAE,QAAQU,IAAET,EAAC,GAAE,EAAE,KAAK,IAAIE;AAAA;AAAA;AAAA,cAGrD,IAAEA;AAAA,qDACqCF,GAAE,2BAAyBE,cAAWA,QAAO;AAAA,uDAC7C,EAAE;AAAA;AAAA;AAAA,SAG9C;AAAA,EAAC;AAAC;;;ACxD4L,SAASQ,GAAEA,IAAE;AAAC,EAAAA,GAAE,QAAQC,EAAC,GAAED,GAAE,KAAK,IAAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAOzNA,GAAE,IAAIC,GAAE,QAAQ,CAAC;AAAA;AAAA;AAAA,oBAGjBD,GAAE,IAAIC,GAAE,MAAM,CAAC;AAAA;AAAA;AAAA,oBAGfD,GAAE,IAAIC,GAAE,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAiBhBD,GAAE,IAAIC,GAAE,MAAM,CAAC;AAAA;AAAA;AAAA,oBAGfD,GAAE,IAAIC,GAAE,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAOjC;AAAC;", "names": ["e", "o", "v", "t", "e", "i", "c", "o", "r", "e", "o", "r", "i", "t", "e", "o", "d", "o", "s", "a", "e", "o", "e", "r", "s", "t", "d", "o", "b", "O", "e", "o", "r", "u", "d", "i", "s", "c", "a", "i", "e", "o", "r"]}