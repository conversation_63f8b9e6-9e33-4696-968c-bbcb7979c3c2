<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量导入涵养水位数据"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-content">
      <!-- 导入说明 -->
      <el-alert
        title="导入说明"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      >
        <template #default>
          <div>
            <p>1. 请下载模板文件，按照模板格式填写数据</p>
            <p>2. 支持Excel文件格式(.xlsx, .xls)</p>
            <p>3. 必填字段：测点ID、原水液位、地下水位、记录时间</p>
            <p>4. 单次最多导入1000条数据</p>
          </div>
        </template>
      </el-alert>

      <!-- 模板下载 -->
      <div class="template-section">
        <el-button type="primary" icon="Download" @click="downloadTemplate">
          下载导入模板
        </el-button>
      </div>

      <!-- 文件上传 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传Excel文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 数据预览 -->
      <div v-if="previewData.length > 0" class="preview-section">
        <div class="preview-header">
          <span class="preview-title">数据预览（前10条）</span>
          <span class="preview-count">共 {{ previewData.length }} 条数据</span>
        </div>
        
        <el-table
          :data="previewData.slice(0, 10)"
          border
          size="small"
          max-height="300"
        >
          <el-table-column prop="stationId" label="测点ID" width="100" />
          <el-table-column prop="rawWaterLevel" label="原水液位(m)" width="120" />
          <el-table-column prop="groundwaterLevel" label="地下水位(m)" width="120" />
          <el-table-column prop="rainfallAmount" label="降雨量(mm)" width="100" />
          <el-table-column prop="evaporationAmount" label="蒸发量(mm)" width="100" />
          <el-table-column prop="extractionAmount" label="开采量(m³)" width="120" />
          <el-table-column prop="recordTime" label="记录时间" width="160" />
          <el-table-column prop="remark" label="备注" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessages.length > 0" class="error-section">
        <el-alert
          title="数据验证错误"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="error-list">
              <div v-for="(error, index) in errorMessages.slice(0, 10)" :key="index">
                第{{ error.row }}行：{{ error.message }}
              </div>
              <div v-if="errorMessages.length > 10">
                ...还有{{ errorMessages.length - 10 }}个错误
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleImport" 
          :loading="loading"
          :disabled="previewData.length === 0 || errorMessages.length > 0"
        >
          确认导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadInstance, UploadRawFile } from 'element-plus'
import * as XLSX from 'xlsx'
import { batchImportWaterLevel, type ConservationWaterLevel } from '@/api/conservationWaterLevel'

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
}>()

// 响应式数据
const loading = ref(false)
const uploadRef = ref<UploadInstance>()
const previewData = ref<ConservationWaterLevel[]>([])
const errorMessages = ref<{ row: number; message: string }[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 下载模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    {
      '测点ID': 'station001',
      '原水液位(米)': 10.5,
      '地下水位(米)': 8.2,
      '降雨量(毫米)': 25.5,
      '蒸发量(毫米)': 5.2,
      '地表径流量(立方米)': 1000,
      '地下水开采量(立方米)': 500,
      '土壤含水率(%)': 35.5,
      '渗透系数': 0.0025,
      '记录时间': '2024-01-01 10:00:00',
      '备注': '示例数据'
    }
  ]

  // 创建工作簿
  const ws = XLSX.utils.json_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '涵养水位数据')

  // 下载文件
  XLSX.writeFile(wb, '涵养水位数据导入模板.xlsx')
}

// 文件选择
const handleFileChange = (file: any) => {
  const rawFile = file.raw as UploadRawFile
  if (!rawFile) return

  // 验证文件类型
  const isExcel = rawFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  rawFile.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    return
  }

  // 验证文件大小
  const isLt10M = rawFile.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return
  }

  // 读取文件
  readExcelFile(rawFile)
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 读取Excel文件
const readExcelFile = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target?.result as ArrayBuffer)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet)

      parseExcelData(jsonData)
    } catch (error) {
      console.error('读取文件失败:', error)
      ElMessage.error('文件格式错误，请检查文件内容')
    }
  }
  reader.readAsArrayBuffer(file)
}

// 解析Excel数据
const parseExcelData = (jsonData: any[]) => {
  const parsedData: ConservationWaterLevel[] = []
  const errors: { row: number; message: string }[] = []

  jsonData.forEach((row, index) => {
    const rowNum = index + 2 // Excel行号从2开始（第1行是标题）
    
    try {
      const item: ConservationWaterLevel = {
        stationId: row['测点ID']?.toString().trim(),
        rawWaterLevel: parseFloat(row['原水液位(米)']),
        groundwaterLevel: parseFloat(row['地下水位(米)']),
        rainfallAmount: row['降雨量(毫米)'] ? parseFloat(row['降雨量(毫米)']) : undefined,
        evaporationAmount: row['蒸发量(毫米)'] ? parseFloat(row['蒸发量(毫米)']) : undefined,
        surfaceRunoff: row['地表径流量(立方米)'] ? parseFloat(row['地表径流量(立方米)']) : undefined,
        extractionAmount: row['地下水开采量(立方米)'] ? parseFloat(row['地下水开采量(立方米)']) : undefined,
        soilMoisture: row['土壤含水率(%)'] ? parseFloat(row['土壤含水率(%)']) : undefined,
        permeabilityCoefficient: row['渗透系数'] ? parseFloat(row['渗透系数']) : undefined,
        recordTime: row['记录时间']?.toString().trim(),
        remark: row['备注']?.toString().trim(),
        dataSource: 1 // 手动录入
      }

      // 数据验证
      const validationErrors = validateRowData(item, rowNum)
      if (validationErrors.length > 0) {
        errors.push(...validationErrors)
      } else {
        parsedData.push(item)
      }
    } catch (error) {
      errors.push({ row: rowNum, message: '数据格式错误' })
    }
  })

  previewData.value = parsedData
  errorMessages.value = errors

  if (errors.length > 0) {
    ElMessage.warning(`发现 ${errors.length} 个数据错误，请修正后重新上传`)
  } else {
    ElMessage.success(`成功解析 ${parsedData.length} 条数据`)
  }
}

// 验证行数据
const validateRowData = (item: ConservationWaterLevel, rowNum: number) => {
  const errors: { row: number; message: string }[] = []

  if (!item.stationId) {
    errors.push({ row: rowNum, message: '测点ID不能为空' })
  }

  if (isNaN(item.rawWaterLevel) || item.rawWaterLevel < 0) {
    errors.push({ row: rowNum, message: '原水液位必须是大于等于0的数字' })
  }

  if (isNaN(item.groundwaterLevel) || item.groundwaterLevel < 0) {
    errors.push({ row: rowNum, message: '地下水位必须是大于等于0的数字' })
  }

  if (!item.recordTime) {
    errors.push({ row: rowNum, message: '记录时间不能为空' })
  } else {
    // 验证时间格式
    const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
    if (!timeRegex.test(item.recordTime)) {
      errors.push({ row: rowNum, message: '记录时间格式错误，应为：YYYY-MM-DD HH:mm:ss' })
    }
  }

  return errors
}

// 确认导入
const handleImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('没有可导入的数据')
    return
  }

  if (errorMessages.value.length > 0) {
    ElMessage.error('请先修正数据错误')
    return
  }

  try {
    loading.value = true
    const response = await batchImportWaterLevel(previewData.value)
    
    if (response.code === 200) {
      ElMessage.success(`成功导入 ${previewData.value.length} 条数据`)
      emit('confirm')
      handleClose()
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  previewData.value = []
  errorMessages.value = []
  uploadRef.value?.clearFiles()
  emit('update:visible', false)
}
</script>

<style scoped>
.import-content {
  padding: 0 10px;
}

.template-section {
  margin-bottom: 20px;
  text-align: center;
}

.upload-section {
  margin-bottom: 20px;
}

.preview-section {
  margin-bottom: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.preview-title {
  font-weight: 600;
  color: #303133;
}

.preview-count {
  color: #909399;
  font-size: 14px;
}

.error-section {
  margin-bottom: 20px;
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-list div {
  margin-bottom: 5px;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
