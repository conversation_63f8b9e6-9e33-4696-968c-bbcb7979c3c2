import {
  h
} from "./chunk-UGCLDPAC.js";
import {
  s as s2
} from "./chunk-KMJSEPHA.js";
import "./chunk-DPVQQ4NR.js";
import "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-P2G4OGHI.js";
import {
  v
} from "./chunk-ZJC3GHA7.js";
import "./chunk-IKGI4J4I.js";
import "./chunk-MNWHGD3K.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import {
  p
} from "./chunk-KTB2COPC.js";
import "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import "./chunk-64RWCMSJ.js";
import {
  f as f2
} from "./chunk-VJW7RCN7.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-XVA5SA7P.js";
import {
  A
} from "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y as y2
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  f,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e as e2,
  r,
  t,
  y
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/ElevationTileData.js
var e3 = class {
  constructor(a2, t2, s4, e4) {
    this._hasNoDataValues = null, this._minValue = null, this._maxValue = null, "pixelData" in a2 ? (this.values = a2.pixelData, this.width = a2.width, this.height = a2.height, this.noDataValue = a2.noDataValue) : (this.values = a2, this.width = t2, this.height = s4, this.noDataValue = e4);
  }
  get hasNoDataValues() {
    if (t(this._hasNoDataValues)) {
      const a2 = this.noDataValue;
      this._hasNoDataValues = this.values.includes(a2);
    }
    return this._hasNoDataValues;
  }
  get minValue() {
    return this._ensureBounds(), e2(this._minValue);
  }
  get maxValue() {
    return this._ensureBounds(), e2(this._maxValue);
  }
  _ensureBounds() {
    if (r(this._minValue)) return;
    const { noDataValue: a2, values: t2 } = this;
    let e4 = 1 / 0, i = -1 / 0, h2 = true;
    for (const s4 of t2) s4 === a2 ? this._hasNoDataValues = true : (e4 = s4 < e4 ? s4 : e4, i = s4 > i ? s4 : i, h2 = false);
    h2 ? (this._minValue = 0, this._maxValue = 0) : (this._minValue = e4, this._maxValue = i > -3e38 ? i : 0);
  }
};

// node_modules/@arcgis/core/layers/support/LercDecoder.js
var s3 = class extends h {
  constructor(e4 = null) {
    super("LercWorker", "_decode", { _decode: (e5) => [e5.buffer] }, e4, { strategy: "dedicated" }), this.schedule = e4, this.ref = 0;
  }
  decode(e4, r2, t2) {
    return e4 && 0 !== e4.byteLength ? this.invoke({ buffer: e4, options: r2 }, t2) : Promise.resolve(null);
  }
  release() {
    --this.ref <= 0 && (o2.forEach((e4, r2) => {
      e4 === this && o2.delete(r2);
    }), this.destroy());
  }
};
var o2 = /* @__PURE__ */ new Map();
function n(t2 = null) {
  let n2 = o2.get(e2(t2));
  return n2 || (r(t2) ? (n2 = new s3((e4) => t2.immediate.schedule(e4)), o2.set(t2, n2)) : (n2 = new s3(), o2.set(null, n2))), ++n2.ref, n2;
}

// node_modules/@arcgis/core/layers/ElevationLayer.js
var w2 = class extends s2(p(c(_(O(b))))) {
  constructor(...e4) {
    super(...e4), this.copyright = null, this.heightModelInfo = null, this.path = null, this.minScale = void 0, this.maxScale = void 0, this.opacity = 1, this.operationalLayerType = "ArcGISTiledElevationServiceLayer", this.sourceJSON = null, this.type = "elevation", this.url = null, this.version = null, this._lercDecoder = n();
  }
  normalizeCtorArgs(e4, r2) {
    return "string" == typeof e4 ? { url: e4, ...r2 } : e4;
  }
  destroy() {
    this._lercDecoder = y(this._lercDecoder);
  }
  readVersion(e4, r2) {
    let t2 = r2.currentVersion;
    return t2 || (t2 = 9.3), t2;
  }
  load(e4) {
    const r2 = r(e4) ? e4.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Image Service"], supportsData: false, validateItem: (e5) => {
      for (let r3 = 0; r3 < e5.typeKeywords.length; r3++) if ("elevation 3d layer" === e5.typeKeywords[r3].toLowerCase()) return true;
      throw new s("portal:invalid-layer-item-type", "Invalid layer item type '${type}', expected '${expectedType}' ", { type: "Image Service", expectedType: "Image Service Elevation 3D Layer" });
    } }, e4).catch(w).then(() => this._fetchImageService(r2))), Promise.resolve(this);
  }
  fetchTile(e4, t2, o3, s4) {
    const a2 = r((s4 = s4 || { signal: null }).signal) ? s4.signal : s4.signal = new AbortController().signal, l = { responseType: "array-buffer", signal: a2 }, p2 = { noDataValue: s4.noDataValue, returnFileInfo: true };
    return this.load().then(() => this._fetchTileAvailability(e4, t2, o3, s4)).then(() => U(this.getTileUrl(e4, t2, o3), l)).then((e5) => this._lercDecoder.decode(e5.data, p2, a2)).then((e5) => new e3(e5));
  }
  getTileUrl(e4, r2, t2) {
    const o3 = !this.tilemapCache && this.supportsBlankTile, i = A({ ...this.parsedUrl.query, blankTile: !o3 && null });
    return `${this.parsedUrl.path}/tile/${e4}/${r2}/${t2}${i ? "?" + i : ""}`;
  }
  async queryElevation(e4, r2) {
    const { ElevationQuery: t2 } = await import("./ElevationQuery-4T42SFZD.js");
    f(r2);
    return new t2().query(this, e4, r2);
  }
  async createElevationSampler(e4, r2) {
    const { ElevationQuery: t2 } = await import("./ElevationQuery-4T42SFZD.js");
    f(r2);
    return new t2().createSampler(this, e4, r2);
  }
  _fetchTileAvailability(e4, r2, t2, o3) {
    return this.tilemapCache ? this.tilemapCache.fetchAvailability(e4, r2, t2, o3) : Promise.resolve("unknown");
  }
  async _fetchImageService(e4) {
    var _a;
    if (this.sourceJSON) return this.sourceJSON;
    const t2 = { query: { f: "json", ...this.parsedUrl.query }, responseType: "json", signal: e4 }, o3 = await U(this.parsedUrl.path, t2);
    o3.ssl && (this.url = (_a = this.url) == null ? void 0 : _a.replace(/^http:/i, "https:")), this.sourceJSON = o3.data, this.read(o3.data, { origin: "service", url: this.parsedUrl });
  }
  get hasOverriddenFetchTile() {
    return !this.fetchTile.__isDefault__;
  }
};
e([y2({ json: { read: { source: "copyrightText" } } })], w2.prototype, "copyright", void 0), e([y2({ readOnly: true, type: v })], w2.prototype, "heightModelInfo", void 0), e([y2({ type: String, json: { origins: { "web-scene": { read: true, write: true } }, read: false } })], w2.prototype, "path", void 0), e([y2({ type: ["show", "hide"] })], w2.prototype, "listMode", void 0), e([y2({ json: { read: false, write: false, origins: { service: { read: false, write: false }, "portal-item": { read: false, write: false }, "web-document": { read: false, write: false } } }, readOnly: true })], w2.prototype, "minScale", void 0), e([y2({ json: { read: false, write: false, origins: { service: { read: false, write: false }, "portal-item": { read: false, write: false }, "web-document": { read: false, write: false } } }, readOnly: true })], w2.prototype, "maxScale", void 0), e([y2({ json: { read: false, write: false, origins: { "web-document": { read: false, write: false } } } })], w2.prototype, "opacity", void 0), e([y2({ type: ["ArcGISTiledElevationServiceLayer"] })], w2.prototype, "operationalLayerType", void 0), e([y2()], w2.prototype, "sourceJSON", void 0), e([y2({ json: { read: false }, value: "elevation", readOnly: true })], w2.prototype, "type", void 0), e([y2(f2)], w2.prototype, "url", void 0), e([y2()], w2.prototype, "version", void 0), e([o("version", ["currentVersion"])], w2.prototype, "readVersion", null), w2 = e([a("esri.layers.ElevationLayer")], w2), w2.prototype.fetchTile.__isDefault__ = true;
var T = w2;
export {
  T as default
};
//# sourceMappingURL=ElevationLayer-CKIV3SWN.js.map
