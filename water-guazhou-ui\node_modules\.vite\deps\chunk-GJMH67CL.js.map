{"version": 3, "sources": ["../../@arcgis/core/geometry/support/MeshTexture.js", "../../@arcgis/core/geometry/support/MeshMaterial.js", "../../@arcgis/core/geometry/support/MeshMaterialMetallicRoughness.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import\"../../core/has.js\";import{wrapImageData as e}from\"../../core/imageUtils.js\";import{JSONSupport as a}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import{ensureClass as s}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as o}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as n}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as i}from\"../../core/accessorSupport/decorators/writer.js\";import{t as c,w as l}from\"../../chunks/persistableUrlUtils.js\";var p;const d=new WeakMap;let h=0,u=p=class extends a{constructor(t){super(t),this.wrap=\"repeat\"}get url(){return this._get(\"url\")||null}set url(t){this._set(\"url\",t),t&&this._set(\"data\",null)}get data(){return this._get(\"data\")||null}set data(t){this._set(\"data\",t),t&&this._set(\"url\",null)}writeData(t,e,a,r){if(t instanceof HTMLImageElement){const s={type:\"image-element\",src:c(t.src,r),crossOrigin:t.crossOrigin};e[a]=s}else if(t instanceof HTMLCanvasElement){const r=t.getContext(\"2d\").getImageData(0,0,t.width,t.height),s={type:\"canvas-element\",imageData:this._encodeImageData(r)};e[a]=s}else if(t instanceof HTMLVideoElement){const s={type:\"video-element\",src:c(t.src,r),autoplay:t.autoplay,loop:t.loop,muted:t.muted,crossOrigin:t.crossOrigin,preload:t.preload};e[a]=s}else if(t instanceof ImageData){const r={type:\"image-data\",imageData:this._encodeImageData(t)};e[a]=r}}readData(t){switch(t.type){case\"image-element\":{const e=new Image;return e.src=t.src,e.crossOrigin=t.crossOrigin,e}case\"canvas-element\":{const e=this._decodeImageData(t.imageData),a=document.createElement(\"canvas\");return a.width=e.width,a.height=e.height,a.getContext(\"2d\").putImageData(e,0,0),a}case\"image-data\":return this._decodeImageData(t.imageData);case\"video-element\":{const e=document.createElement(\"video\");return e.src=t.src,e.crossOrigin=t.crossOrigin,e.autoplay=t.autoplay,e.loop=t.loop,e.muted=t.muted,e.preload=t.preload,e}default:return}}get transparent(){const t=this.data,e=this.url;if(t instanceof HTMLCanvasElement)return this._imageDataContainsTransparent(t.getContext(\"2d\").getImageData(0,0,t.width,t.height));if(t instanceof ImageData)return this._imageDataContainsTransparent(t);if(e){const t=e.substr(e.length-4,4).toLowerCase(),a=e.substr(0,15).toLocaleLowerCase();if(\".png\"===t||\"data:image/png;\"===a)return!0}return!1}set transparent(t){this._overrideIfSome(\"transparent\",t)}get contentHash(){const t=\"string\"==typeof this.wrap?this.wrap:\"object\"==typeof this.wrap?`${this.wrap.horizontal}/${this.wrap.vertical}`:\"\",e=(e=\"\")=>`d:${e},t:${this.transparent},w:${t}`;return null!=this.url?e(this.url):null!=this.data?this.data instanceof HTMLImageElement||this.data instanceof HTMLVideoElement?e(this.data.src):(d.has(this.data)||d.set(this.data,++h),e(d.get(this.data))):e()}clone(){const t={url:this.url,data:this.data,wrap:this._cloneWrap()};return new p(t)}cloneWithDeduplication(t){const e=t.get(this);if(e)return e;const a=this.clone();return t.set(this,a),a}_cloneWrap(){return\"string\"==typeof this.wrap?this.wrap:{horizontal:this.wrap.horizontal,vertical:this.wrap.vertical}}_encodeImageData(t){let e=\"\";for(let a=0;a<t.data.length;a++)e+=String.fromCharCode(t.data[a]);return{data:btoa(e),width:t.width,height:t.height}}_decodeImageData(t){const a=atob(t.data),r=new Uint8ClampedArray(a.length);for(let e=0;e<a.length;e++)r[e]=a.charCodeAt(e);return e(r,t.width,t.height)}_imageDataContainsTransparent(t){for(let e=3;e<t.data.length;e+=4)if(255!==t.data[e])return!0;return!1}static from(t){return\"string\"==typeof t?new p({url:t}):t instanceof HTMLImageElement||t instanceof HTMLCanvasElement||t instanceof ImageData||t instanceof HTMLVideoElement?new p({data:t}):s(p,t)}};t([r({type:String,json:{write:l}})],u.prototype,\"url\",null),t([r({json:{write:{overridePolicy(){return{enabled:!this.url}}}}}),r()],u.prototype,\"data\",null),t([i(\"data\")],u.prototype,\"writeData\",null),t([o(\"data\")],u.prototype,\"readData\",null),t([r({type:Boolean,json:{write:{overridePolicy(){return{enabled:this._isOverridden(\"transparent\")}}}}})],u.prototype,\"transparent\",null),t([r({json:{write:!0}})],u.prototype,\"wrap\",void 0),t([r({readOnly:!0})],u.prototype,\"contentHash\",null),u=p=t([n(\"esri.geometry.support.MeshTexture\")],u);const m=u;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import r from\"../../Color.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{isSome as t}from\"../../core/maybe.js\";import{property as l}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import i from\"./MeshTexture.js\";var n;let u=n=class extends e{constructor(o){super(o),this.color=null,this.colorTexture=null,this.normalTexture=null,this.alphaMode=\"auto\",this.alphaCutoff=.5,this.doubleSided=!0}clone(){return this.cloneWithDeduplication(null,new Map)}cloneWithDeduplication(o,r){const e=t(o)?o.get(this):null;if(e)return e;const l=new n(this.clonePropertiesWithDeduplication(r));return t(o)&&o.set(this,l),l}clonePropertiesWithDeduplication(o){return{color:t(this.color)?this.color.clone():null,colorTexture:t(this.colorTexture)?this.colorTexture.cloneWithDeduplication(o):null,normalTexture:t(this.normalTexture)?this.normalTexture.cloneWithDeduplication(o):null,alphaMode:this.alphaMode,alphaCutoff:this.alphaCutoff,doubleSided:this.doubleSided,colorTextureTransform:t(this.colorTextureTransform)?this.colorTextureTransform:null,normalTextureTransform:t(this.normalTextureTransform)?this.normalTextureTransform:null}}};o([l({type:r,json:{write:!0}})],u.prototype,\"color\",void 0),o([l({type:i,json:{write:!0}})],u.prototype,\"colorTexture\",void 0),o([l({type:i,json:{write:!0}})],u.prototype,\"normalTexture\",void 0),o([l({nonNullable:!0,json:{write:!0}})],u.prototype,\"alphaMode\",void 0),o([l({nonNullable:!0,json:{write:!0}})],u.prototype,\"alphaCutoff\",void 0),o([l({nonNullable:!0,json:{write:!0}})],u.prototype,\"doubleSided\",void 0),o([l()],u.prototype,\"colorTextureTransform\",void 0),o([l()],u.prototype,\"normalTextureTransform\",void 0),u=n=o([s(\"esri.geometry.support.MeshMaterial\")],u);const a=u;export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import s from\"../../Color.js\";import{isSome as t}from\"../../core/maybe.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as r}from\"../../core/accessorSupport/decorators/subclass.js\";import i from\"./MeshMaterial.js\";import l from\"./MeshTexture.js\";var n;let u=n=class extends i{constructor(e){super(e),this.emissiveColor=null,this.emissiveTexture=null,this.occlusionTexture=null,this.metallic=1,this.roughness=1,this.metallicRoughnessTexture=null}clone(){return this.cloneWithDeduplication(null,new Map)}cloneWithDeduplication(e,s){const o=t(e)?e.get(this):null;if(o)return o;const r=new n(this.clonePropertiesWithDeduplication(s));return t(e)&&e.set(this,r),r}clonePropertiesWithDeduplication(e){return{...super.clonePropertiesWithDeduplication(e),emissiveColor:t(this.emissiveColor)?this.emissiveColor.clone():null,emissiveTexture:t(this.emissiveTexture)?this.emissiveTexture.cloneWithDeduplication(e):null,occlusionTexture:t(this.occlusionTexture)?this.occlusionTexture.cloneWithDeduplication(e):null,metallic:this.metallic,roughness:this.roughness,metallicRoughnessTexture:t(this.metallicRoughnessTexture)?this.metallicRoughnessTexture.cloneWithDeduplication(e):null,occlusionTextureTransform:t(this.occlusionTextureTransform)?this.occlusionTextureTransform:null,emissiveTextureTransform:t(this.emissiveTextureTransform)?this.emissiveTextureTransform:null,metallicRoughnessTextureTransform:t(this.metallicRoughnessTextureTransform)?this.metallicRoughnessTextureTransform:null}}};e([o({type:s,json:{write:!0}})],u.prototype,\"emissiveColor\",void 0),e([o({type:l,json:{write:!0}})],u.prototype,\"emissiveTexture\",void 0),e([o({type:l,json:{write:!0}})],u.prototype,\"occlusionTexture\",void 0),e([o({type:Number,nonNullable:!0,json:{write:!0},range:{min:0,max:1}})],u.prototype,\"metallic\",void 0),e([o({type:Number,nonNullable:!0,json:{write:!0},range:{min:0,max:1}})],u.prototype,\"roughness\",void 0),e([o({type:l,json:{write:!0}})],u.prototype,\"metallicRoughnessTexture\",void 0),e([o()],u.prototype,\"occlusionTextureTransform\",void 0),e([o()],u.prototype,\"emissiveTextureTransform\",void 0),e([o()],u.prototype,\"metallicRoughnessTextureTransform\",void 0),u=n=e([r(\"esri.geometry.support.MeshMaterialMetallicRoughness\")],u);const c=u;export{c as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4oB,IAAI;AAAE,IAAM,IAAE,oBAAI;AAAQ,IAAI,IAAE;AAAN,IAAQ,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,MAAK;AAAC,WAAO,KAAK,KAAK,KAAK,KAAG;AAAA,EAAI;AAAA,EAAC,IAAI,IAAI,GAAE;AAAC,SAAK,KAAK,OAAM,CAAC,GAAE,KAAG,KAAK,KAAK,QAAO,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK,MAAM,KAAG;AAAA,EAAI;AAAA,EAAC,IAAI,KAAK,GAAE;AAAC,SAAK,KAAK,QAAO,CAAC,GAAE,KAAG,KAAK,KAAK,OAAM,IAAI;AAAA,EAAC;AAAA,EAAC,UAAU,GAAEA,IAAEC,IAAEC,IAAE;AAAC,QAAG,aAAa,kBAAiB;AAAC,YAAM,IAAE,EAAC,MAAK,iBAAgB,KAAI,EAAE,EAAE,KAAIA,EAAC,GAAE,aAAY,EAAE,YAAW;AAAE,MAAAF,GAAEC,EAAC,IAAE;AAAA,IAAC,WAAS,aAAa,mBAAkB;AAAC,YAAMC,KAAE,EAAE,WAAW,IAAI,EAAE,aAAa,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM,GAAE,IAAE,EAAC,MAAK,kBAAiB,WAAU,KAAK,iBAAiBA,EAAC,EAAC;AAAE,MAAAF,GAAEC,EAAC,IAAE;AAAA,IAAC,WAAS,aAAa,kBAAiB;AAAC,YAAM,IAAE,EAAC,MAAK,iBAAgB,KAAI,EAAE,EAAE,KAAIC,EAAC,GAAE,UAAS,EAAE,UAAS,MAAK,EAAE,MAAK,OAAM,EAAE,OAAM,aAAY,EAAE,aAAY,SAAQ,EAAE,QAAO;AAAE,MAAAF,GAAEC,EAAC,IAAE;AAAA,IAAC,WAAS,aAAa,WAAU;AAAC,YAAMC,KAAE,EAAC,MAAK,cAAa,WAAU,KAAK,iBAAiB,CAAC,EAAC;AAAE,MAAAF,GAAEC,EAAC,IAAEC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,SAAS,GAAE;AAAC,YAAO,EAAE,MAAK;AAAA,MAAC,KAAI,iBAAgB;AAAC,cAAMF,KAAE,IAAI;AAAM,eAAOA,GAAE,MAAI,EAAE,KAAIA,GAAE,cAAY,EAAE,aAAYA;AAAA,MAAC;AAAA,MAAC,KAAI,kBAAiB;AAAC,cAAMA,KAAE,KAAK,iBAAiB,EAAE,SAAS,GAAEC,KAAE,SAAS,cAAc,QAAQ;AAAE,eAAOA,GAAE,QAAMD,GAAE,OAAMC,GAAE,SAAOD,GAAE,QAAOC,GAAE,WAAW,IAAI,EAAE,aAAaD,IAAE,GAAE,CAAC,GAAEC;AAAA,MAAC;AAAA,MAAC,KAAI;AAAa,eAAO,KAAK,iBAAiB,EAAE,SAAS;AAAA,MAAE,KAAI,iBAAgB;AAAC,cAAMD,KAAE,SAAS,cAAc,OAAO;AAAE,eAAOA,GAAE,MAAI,EAAE,KAAIA,GAAE,cAAY,EAAE,aAAYA,GAAE,WAAS,EAAE,UAASA,GAAE,OAAK,EAAE,MAAKA,GAAE,QAAM,EAAE,OAAMA,GAAE,UAAQ,EAAE,SAAQA;AAAA,MAAC;AAAA,MAAC;AAAQ;AAAA,IAAM;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,UAAM,IAAE,KAAK,MAAKA,KAAE,KAAK;AAAI,QAAG,aAAa,kBAAkB,QAAO,KAAK,8BAA8B,EAAE,WAAW,IAAI,EAAE,aAAa,GAAE,GAAE,EAAE,OAAM,EAAE,MAAM,CAAC;AAAE,QAAG,aAAa,UAAU,QAAO,KAAK,8BAA8B,CAAC;AAAE,QAAGA,IAAE;AAAC,YAAMG,KAAEH,GAAE,OAAOA,GAAE,SAAO,GAAE,CAAC,EAAE,YAAY,GAAEC,KAAED,GAAE,OAAO,GAAE,EAAE,EAAE,kBAAkB;AAAE,UAAG,WAASG,MAAG,sBAAoBF,GAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,YAAY,GAAE;AAAC,SAAK,gBAAgB,eAAc,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,UAAM,IAAE,YAAU,OAAO,KAAK,OAAK,KAAK,OAAK,YAAU,OAAO,KAAK,OAAK,GAAG,KAAK,KAAK,UAAU,IAAI,KAAK,KAAK,QAAQ,KAAG,IAAGD,KAAE,CAACA,KAAE,OAAK,KAAKA,EAAC,MAAM,KAAK,WAAW,MAAM,CAAC;AAAG,WAAO,QAAM,KAAK,MAAIA,GAAE,KAAK,GAAG,IAAE,QAAM,KAAK,OAAK,KAAK,gBAAgB,oBAAkB,KAAK,gBAAgB,mBAAiBA,GAAE,KAAK,KAAK,GAAG,KAAG,EAAE,IAAI,KAAK,IAAI,KAAG,EAAE,IAAI,KAAK,MAAK,EAAE,CAAC,GAAEA,GAAE,EAAE,IAAI,KAAK,IAAI,CAAC,KAAGA,GAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAM,IAAE,EAAC,KAAI,KAAK,KAAI,MAAK,KAAK,MAAK,MAAK,KAAK,WAAW,EAAC;AAAE,WAAO,IAAI,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAE;AAAC,UAAMA,KAAE,EAAE,IAAI,IAAI;AAAE,QAAGA,GAAE,QAAOA;AAAE,UAAMC,KAAE,KAAK,MAAM;AAAE,WAAO,EAAE,IAAI,MAAKA,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAM,YAAU,OAAO,KAAK,OAAK,KAAK,OAAK,EAAC,YAAW,KAAK,KAAK,YAAW,UAAS,KAAK,KAAK,SAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAE;AAAC,QAAID,KAAE;AAAG,aAAQC,KAAE,GAAEA,KAAE,EAAE,KAAK,QAAOA,KAAI,CAAAD,MAAG,OAAO,aAAa,EAAE,KAAKC,EAAC,CAAC;AAAE,WAAM,EAAC,MAAK,KAAKD,EAAC,GAAE,OAAM,EAAE,OAAM,QAAO,EAAE,OAAM;AAAA,EAAC;AAAA,EAAC,iBAAiB,GAAE;AAAC,UAAMC,KAAE,KAAK,EAAE,IAAI,GAAEC,KAAE,IAAI,kBAAkBD,GAAE,MAAM;AAAE,aAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAE,GAAEF,EAAC,IAAEC,GAAE,WAAWD,EAAC;AAAE,WAAOE,GAAEA,IAAE,EAAE,OAAM,EAAE,MAAM;AAAA,EAAC;AAAA,EAAC,8BAA8B,GAAE;AAAC,aAAQF,KAAE,GAAEA,KAAE,EAAE,KAAK,QAAOA,MAAG,EAAE,KAAG,QAAM,EAAE,KAAKA,EAAC,EAAE,QAAM;AAAG,WAAM;AAAA,EAAE;AAAA,EAAC,OAAO,KAAK,GAAE;AAAC,WAAM,YAAU,OAAO,IAAE,IAAI,EAAE,EAAC,KAAI,EAAC,CAAC,IAAE,aAAa,oBAAkB,aAAa,qBAAmB,aAAa,aAAW,aAAa,mBAAiB,IAAI,EAAE,EAAC,MAAK,EAAC,CAAC,IAAE,EAAE,GAAE,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,IAAG;AAAC,EAAC,EAAC,EAAC,CAAC,GAAE,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAACE,GAAE,MAAM,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,MAAM,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,KAAK,cAAc,aAAa,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,mCAAmC,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;;;ACAh2H,IAAI;AAAE,IAAIC,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAK,KAAK,YAAU,QAAO,KAAK,cAAY,KAAG,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,WAAO,KAAK,uBAAuB,MAAK,oBAAI,KAAG;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAEF,EAAC,IAAEA,GAAE,IAAI,IAAI,IAAE;AAAK,QAAGE,GAAE,QAAOA;AAAE,UAAMC,KAAE,IAAI,EAAE,KAAK,iCAAiCF,EAAC,CAAC;AAAE,WAAO,EAAED,EAAC,KAAGA,GAAE,IAAI,MAAKG,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,iCAAiCH,IAAE;AAAC,WAAM,EAAC,OAAM,EAAE,KAAK,KAAK,IAAE,KAAK,MAAM,MAAM,IAAE,MAAK,cAAa,EAAE,KAAK,YAAY,IAAE,KAAK,aAAa,uBAAuBA,EAAC,IAAE,MAAK,eAAc,EAAE,KAAK,aAAa,IAAE,KAAK,cAAc,uBAAuBA,EAAC,IAAE,MAAK,WAAU,KAAK,WAAU,aAAY,KAAK,aAAY,aAAY,KAAK,aAAY,uBAAsB,EAAE,KAAK,qBAAqB,IAAE,KAAK,wBAAsB,MAAK,wBAAuB,EAAE,KAAK,sBAAsB,IAAE,KAAK,yBAAuB,KAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKG,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAEA,EAAC;AAAE,IAAMM,KAAEN;;;ACAp+C,IAAIO;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,kBAAgB,MAAK,KAAK,mBAAiB,MAAK,KAAK,WAAS,GAAE,KAAK,YAAU,GAAE,KAAK,2BAAyB;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,KAAK,uBAAuB,MAAK,oBAAI,KAAG;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAE,GAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,IAAI,IAAI,IAAE;AAAK,QAAGC,GAAE,QAAOA;AAAE,UAAMC,KAAE,IAAIL,GAAE,KAAK,iCAAiC,CAAC,CAAC;AAAE,WAAO,EAAEG,EAAC,KAAGA,GAAE,IAAI,MAAKE,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,iCAAiCF,IAAE;AAAC,WAAM,EAAC,GAAG,MAAM,iCAAiCA,EAAC,GAAE,eAAc,EAAE,KAAK,aAAa,IAAE,KAAK,cAAc,MAAM,IAAE,MAAK,iBAAgB,EAAE,KAAK,eAAe,IAAE,KAAK,gBAAgB,uBAAuBA,EAAC,IAAE,MAAK,kBAAiB,EAAE,KAAK,gBAAgB,IAAE,KAAK,iBAAiB,uBAAuBA,EAAC,IAAE,MAAK,UAAS,KAAK,UAAS,WAAU,KAAK,WAAU,0BAAyB,EAAE,KAAK,wBAAwB,IAAE,KAAK,yBAAyB,uBAAuBA,EAAC,IAAE,MAAK,2BAA0B,EAAE,KAAK,yBAAyB,IAAE,KAAK,4BAA0B,MAAK,0BAAyB,EAAE,KAAK,wBAAwB,IAAE,KAAK,2BAAyB,MAAK,mCAAkC,EAAE,KAAK,iCAAiC,IAAE,KAAK,oCAAkC,KAAI;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKG,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEN,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEN,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,GAAE,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,GAAE,OAAM,EAAC,KAAI,GAAE,KAAI,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKM,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEN,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,6BAA4B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,qCAAoC,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,qDAAqD,CAAC,GAAEC,EAAC;AAAE,IAAM,IAAEA;", "names": ["e", "a", "r", "t", "m", "u", "o", "r", "e", "l", "m", "a", "n", "u", "a", "e", "o", "r", "l", "m"]}