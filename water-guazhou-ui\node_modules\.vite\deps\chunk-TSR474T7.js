import {
  n as n2,
  o as o3
} from "./chunk-MWEMWROT.js";
import {
  s as s2
} from "./chunk-LDZI44QV.js";
import {
  _n,
  rn
} from "./chunk-UYAKJRPP.js";
import {
  y
} from "./chunk-NZB6EMKN.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  o as o2
} from "./chunk-7CPUVZNS.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  E as E2,
  c,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  M,
  mt,
  xt
} from "./chunk-U4SVMKOQ.js";
import {
  n,
  o
} from "./chunk-NDCSRZLO.js";
import {
  E
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/ogc/dateUtils.js
function e(e2) {
  return t2(e2) ?? n3(e2);
}
function n3(e2) {
  const n4 = new Date(e2).getTime();
  return Number.isNaN(n4) ? null : n4;
}
function t2(e2) {
  const n4 = s3.exec(e2);
  if (!(n4 == null ? void 0 : n4.groups)) return null;
  const t3 = n4.groups, u = +t3.year, o4 = +t3.month - 1, r2 = +t3.day, f2 = +(t3.hours ?? "0"), i = +(t3.minutes ?? "0"), d = +(t3.seconds ?? "0");
  if (f2 > 23) return null;
  if (i > 59) return null;
  if (d > 59) return null;
  const l = t3.ms ?? "0", c2 = l ? +l.padEnd(3, "0").substring(0, 3) : 0;
  let a;
  if (t3.isUTC) a = Date.UTC(u, o4, r2, f2, i, d, c2);
  else if (t3.offsetSign) {
    const e3 = +t3.offsetHours, n5 = +t3.offsetMinutes;
    a = 6e4 * ("+" === t3.offsetSign ? -1 : 1) * (60 * e3 + n5) + Date.UTC(u, o4, r2, f2, i, d, c2);
  } else a = new Date(u, o4, r2, f2, i, d, c2).getTime();
  return Number.isNaN(a) ? null : a;
}
var s3 = /^(?:(?<year>-?\d{4,})-(?<month>\d{2})-(?<day>\d{2}))(?:T(?<hours>\d{2}):(?<minutes>\d{2}):(?<seconds>\d{2})(?:\.(?<ms>\d+))?)?(?:(?<isUTC>Z)|(?:(?<offsetSign>\+|-)(?<offsetHours>\d{2}):(?<offsetMinutes>\d{2})))?$/;

// node_modules/@arcgis/core/layers/ogc/wfsUtils.js
var S = "xlink:href";
var x = "2.0.0";
var C = "__esri_wfs_id__";
var E3 = "wfs-layer:getWFSLayerTypeInfo-error";
var N = "wfs-layer:empty-service";
var P = "wfs-layer:feature-type-not-found";
var R = "wfs-layer:geojson-not-supported";
var j = "wfs-layer:kvp-encoding-not-supported";
var A = "wfs-layer:malformed-json";
var G = "wfs-layer:unknown-geometry-type";
var k = "wfs-layer:unknown-field-type";
var v = "wfs-layer:unsupported-spatial-reference";
var U2 = "wfs-layer:unsupported-wfs-version";
async function D(t3, r2) {
  const n4 = I((await U(t3, { responseType: "text", query: { SERVICE: "WFS", REQUEST: "GetCapabilities", VERSION: x, ...r2 == null ? void 0 : r2.customParameters }, signal: r2 == null ? void 0 : r2.signal })).data);
  return $(t3, n4), n4;
}
function I(e2) {
  const t3 = te(e2);
  ne(t3), ae(t3);
  const n4 = t3.firstElementChild, a = n(M2(n4));
  return { operations: L(n4), get featureTypes() {
    return Array.from(a());
  }, readFeatureTypes: a };
}
var O = /* @__PURE__ */ new Set(["json", "application/json", "geojson", "application/json; subtype=geojson"]);
function L(e2) {
  let r2 = false;
  const n4 = { GetCapabilities: { url: "" }, DescribeFeatureType: { url: "" }, GetFeature: { url: "", outputFormat: null, supportsPagination: false } };
  if (o3(e2, { OperationsMetadata: { Operation: (e3) => {
    switch (e3.getAttribute("name")) {
      case "GetCapabilities":
        return { DCP: { HTTP: { Get: (e4) => {
          n4.GetCapabilities.url = e4.getAttribute(S);
        } } } };
      case "DescribeFeatureType":
        return { DCP: { HTTP: { Get: (e4) => {
          n4.DescribeFeatureType.url = e4.getAttribute(S);
        } } } };
      case "GetFeature":
        return { DCP: { HTTP: { Get: (e4) => {
          n4.GetFeature.url = e4.getAttribute(S);
        } } }, Parameter: (e4) => {
          if ("outputFormat" === e4.getAttribute("name")) return { AllowedValues: { Value: (e5) => {
            const t3 = e5.textContent;
            t3 && O.has(t3.toLowerCase()) && (n4.GetFeature.outputFormat = t3);
          } } };
        } };
    }
  }, Constraint: (e3) => {
    switch (e3.getAttribute("name")) {
      case "KVPEncoding":
        return { DefaultValue: (e4) => {
          r2 = "true" === e4.textContent.toLowerCase();
        } };
      case "ImplementsResultPaging":
        return { DefaultValue: (e4) => {
          n4.GetFeature.supportsPagination = "true" === e4.textContent.toLowerCase();
        } };
    }
  } } }), !r2) throw new s(j, "WFS service doesn't support key/value pair (KVP) encoding");
  if (t(n4.GetFeature.outputFormat)) throw new s(R, "WFS service doesn't support GeoJSON output format");
  return n4;
}
function $(e2, t3) {
  mt(e2) && (M(e2, t3.operations.DescribeFeatureType.url, true) && (t3.operations.DescribeFeatureType.url = xt(t3.operations.DescribeFeatureType.url)), M(e2, t3.operations.GetFeature.url, true) && (t3.operations.GetFeature.url = xt(t3.operations.GetFeature.url)));
}
function M2(e2) {
  return n2(e2, { FeatureTypeList: { FeatureType: (e3) => {
    const t3 = { typeName: "undefined:undefined", name: "", title: "", description: "", extent: null, namespacePrefix: "", namespaceUri: "", supportedSpatialReferences: [] }, r2 = /* @__PURE__ */ new Set([4326]), n4 = (e4) => {
      var _a, _b, _c;
      const t4 = parseInt(((_c = (_b = (_a = e4.textContent) == null ? void 0 : _a.match(/(?<wkid>\d+$)/i)) == null ? void 0 : _b.groups) == null ? void 0 : _c.wkid) ?? "", 10);
      Number.isNaN(t4) || r2.add(t4);
    };
    return o3(e3, { Name: (e4) => {
      const { name: r3, prefix: n5 } = re(e4.textContent);
      t3.typeName = `${n5}:${r3}`, t3.name = r3, t3.namespacePrefix = n5, t3.namespaceUri = e4.lookupNamespaceURI(n5);
    }, Abstract: (e4) => {
      t3.description = e4.textContent;
    }, Title: (e4) => {
      t3.title = e4.textContent;
    }, WGS84BoundingBox: (e4) => {
      t3.extent = V(e4);
    }, DefaultSRS: n4, DefaultCRS: n4, OtherSRS: n4, OtherCRS: n4 }), t3.title || (t3.title = t3.name), t3.supportedSpatialReferences.push(...r2), t3;
  } } });
}
function V(e2) {
  let t3, r2, n4, a;
  for (const o4 of e2.children) switch (o4.localName) {
    case "LowerCorner":
      [t3, r2] = o4.textContent.split(" ").map((e3) => Number.parseFloat(e3));
      break;
    case "UpperCorner":
      [n4, a] = o4.textContent.split(" ").map((e3) => Number.parseFloat(e3));
  }
  return { xmin: t3, ymin: r2, xmax: n4, ymax: a, spatialReference: c };
}
function W(e2, t3, r2) {
  return o(e2, (e3) => r2 ? e3.name === t3 && e3.namespaceUri === r2 : e3.typeName === t3 || e3.name === t3);
}
async function X(e2, t3, r2, n4 = {}) {
  const { featureType: a, extent: o4 } = await Y(e2, t3, r2, n4), { fields: s4, geometryType: i, swapXY: p, objectIdField: u, geometryField: c2 } = await q(e2, a.typeName, n4);
  return { url: e2.operations.GetCapabilities.url, name: a.name, namespaceUri: a.namespaceUri, fields: s4, geometryField: c2, geometryType: i, objectIdField: u, spatialReference: n4.spatialReference ?? f.WGS84, extent: o4, swapXY: p, wfsCapabilities: e2, customParameters: n4.customParameters };
}
async function Y(e2, r2, n4, o4 = {}) {
  const { spatialReference: s4 = f.WGS84 } = o4, i = e2.readFeatureTypes(), p = r2 ? W(i, r2, n4) : i.next().value;
  if (t(p)) throw r2 ? new s(P, `The type '${r2}' could not be found in the service`) : new s(N, "The service is empty");
  let u = new w({ ...p.extent, spatialReference: s4 });
  if (!E2(s4, c)) try {
    await _n(c, s4, void 0, o4), u = rn(u, c);
  } catch {
    throw new s(v, "Projection not supported");
  }
  return { extent: u, spatialReference: s4, featureType: p };
}
async function q(e2, r2, n4 = {}) {
  var _a, _b, _c;
  const [o4, i] = await E([J(e2.operations.DescribeFeatureType.url, r2, n4), _(e2, r2, n4)]);
  if (o4.error || i.error) throw new s(E3, `An error occurred while getting info about the feature type '${r2}'`, { error: o4.error || i.error });
  const { fields: p, errors: u } = o4.value ?? {}, c2 = ((_a = o4.value) == null ? void 0 : _a.geometryType) || ((_b = i.value) == null ? void 0 : _b.geometryType), l = ((_c = i.value) == null ? void 0 : _c.swapXY) ?? false;
  if (t(c2)) throw new s(G, `The geometry type could not be determined for type '${r2}`, { typeName: r2, geometryType: c2, fields: p, errors: u });
  return { ...z(p ?? []), geometryType: c2, swapXY: l };
}
function z(e2) {
  const t3 = e2.find((e3) => "geometry" === e3.type);
  let r2 = e2.find((e3) => "oid" === e3.type);
  return e2 = e2.filter((e3) => "geometry" !== e3.type), r2 || (r2 = new y({ name: C, type: "oid", alias: C }), e2.unshift(r2)), { geometryField: (t3 == null ? void 0 : t3.name) ?? null, objectIdField: r2.name, fields: e2 };
}
async function _(t3, r2, n4 = {}) {
  var _a;
  let a, o4 = false;
  const [s4, i] = await Promise.all([K(t3.operations.GetFeature.url, r2, t3.operations.GetFeature.outputFormat, { ...n4, count: 1 }), U(t3.operations.GetFeature.url, { responseType: "text", query: Z(r2, void 0, { ...n4, count: 1 }), signal: n4 == null ? void 0 : n4.signal })]), p = "FeatureCollection" === s4.type && ((_a = s4.features[0]) == null ? void 0 : _a.geometry);
  if (p) {
    let e2;
    switch (a = o2.fromJSON(s2(p.type)), p.type) {
      case "Point":
        e2 = p.coordinates;
        break;
      case "LineString":
      case "MultiPoint":
        e2 = p.coordinates[0];
        break;
      case "MultiLineString":
      case "Polygon":
        e2 = p.coordinates[0][0];
        break;
      case "MultiPolygon":
        e2 = p.coordinates[0][0][0];
    }
    const t4 = /<[^>]*pos[^>]*> *(-?\d+(?:\.\d+)?) (-?\d+(?:\.\d+)?)/.exec(i.data);
    if (t4) {
      const r3 = e2[0].toFixed(3), n5 = e2[1].toFixed(3), a2 = parseFloat(t4[1]).toFixed(3);
      r3 === parseFloat(t4[2]).toFixed(3) && n5 === a2 && (o4 = true);
    }
  }
  return { geometryType: a, swapXY: o4 };
}
async function J(t3, r2, n4) {
  return Q(r2, (await U(t3, { responseType: "text", query: { SERVICE: "WFS", REQUEST: "DescribeFeatureType", VERSION: x, TYPENAME: r2, ...n4 == null ? void 0 : n4.customParameters }, signal: n4 == null ? void 0 : n4.signal })).data);
}
function Q(e2, r2) {
  const { name: a } = re(e2), s4 = te(r2);
  ae(s4);
  const i = o(n2(s4.firstElementChild, { element: (e3) => ({ name: e3.getAttribute("name"), typeName: re(e3.getAttribute("type")).name }) }), ({ name: e3 }) => e3 === a);
  if (r(i)) {
    const e3 = o(n2(s4.firstElementChild, { complexType: (e4) => e4 }), (e4) => e4.getAttribute("name") === i.typeName);
    if (r(e3)) return B(e3);
  }
  throw new s(P, `Type '${e2}' not found in document`, { document: new XMLSerializer().serializeToString(s4) });
}
var H = /* @__PURE__ */ new Set(["objectid", "fid"]);
function B(e2) {
  const r2 = [], n4 = [];
  let a;
  const o4 = n2(e2, { complexContent: { extension: { sequence: { element: (e3) => e3 } } } });
  for (const s4 of o4) {
    const o5 = s4.getAttribute("name");
    if (!o5) continue;
    let i, p;
    if (s4.hasAttribute("type") ? i = re(s4.getAttribute("type")).name : o3(s4, { simpleType: { restriction: (e3) => (i = re(e3.getAttribute("base")).name, { maxLength: (e4) => {
      p = +e4.getAttribute("value");
    } }) } }), !i) continue;
    const u = "true" === s4.getAttribute("nillable");
    let c2 = false;
    switch (i.toLowerCase()) {
      case "integer":
      case "nonpositiveinteger":
      case "negativeinteger":
      case "long":
      case "int":
      case "short":
      case "byte":
      case "nonnegativeinteger":
      case "unsignedlong":
      case "unsignedint":
      case "unsignedshort":
      case "unsignedbyte":
      case "positiveinteger":
        n4.push(new y({ name: o5, alias: o5, type: "integer", nullable: u }));
        break;
      case "float":
      case "double":
      case "decimal":
        n4.push(new y({ name: o5, alias: o5, type: "double", nullable: u }));
        break;
      case "boolean":
      case "string":
      case "gyearmonth":
      case "gyear":
      case "gmonthday":
      case "gday":
      case "gmonth":
      case "anyuri":
      case "qname":
      case "notation":
      case "normalizedstring":
      case "token":
      case "language":
      case "idrefs":
      case "entities":
      case "nmtoken":
      case "nmtokens":
      case "name":
      case "ncname":
      case "id":
      case "idref":
      case "entity":
      case "duration":
      case "time":
        n4.push(new y({ name: o5, alias: o5, type: "string", nullable: u, length: p ?? 255 }));
        break;
      case "datetime":
      case "date":
        n4.push(new y({ name: o5, alias: o5, type: "date", nullable: u, length: p ?? 36 }));
        break;
      case "pointpropertytype":
        a = "point", c2 = true;
        break;
      case "multipointpropertytype":
        a = "multipoint", c2 = true;
        break;
      case "curvepropertytype":
      case "multicurvepropertytype":
      case "multilinestringpropertytype":
        a = "polyline", c2 = true;
        break;
      case "surfacepropertytype":
      case "multisurfacepropertytype":
      case "multipolygonpropertytype":
        a = "polygon", c2 = true;
        break;
      case "geometrypropertytype":
      case "multigeometrypropertytype":
        c2 = true, r2.push(new s(G, `geometry type '${i}' is not supported`, { type: new XMLSerializer().serializeToString(e2) }));
        break;
      default:
        r2.push(new s(k, `Unknown field type '${i}'`, { type: new XMLSerializer().serializeToString(e2) }));
    }
    c2 && n4.push(new y({ name: o5, alias: o5, type: "geometry", nullable: u }));
  }
  for (const t3 of n4) if ("integer" === t3.type && !t3.nullable && H.has(t3.name.toLowerCase())) {
    t3.type = "oid";
    break;
  }
  return { geometryType: a, fields: n4, errors: r2 };
}
async function K(r2, n4, a, o4) {
  var _a;
  let { data: s4 } = await U(r2, { responseType: "text", query: Z(n4, a, o4), signal: o4 == null ? void 0 : o4.signal });
  s4 = s4.replace(/": +(-?\d+),(\d+)(,)?/g, '": $1.$2$3');
  try {
    if ((_a = o4 == null ? void 0 : o4.dateFields) == null ? void 0 : _a.length) {
      const e2 = new Set(o4.dateFields);
      return JSON.parse(s4, (t3, r3) => e2.has(t3) ? e(r3) : r3);
    }
    return JSON.parse(s4);
  } catch (i) {
    throw new s(A, "Error while parsing the response", { response: s4, error: i });
  }
}
function Z(e2, t3, r2) {
  return { SERVICE: "WFS", REQUEST: "GetFeature", VERSION: x, TYPENAMES: e2, OUTPUTFORMAT: t3, SRSNAME: "EPSG:4326", STARTINDEX: r2 == null ? void 0 : r2.startIndex, COUNT: r2 == null ? void 0 : r2.count, ...r2 == null ? void 0 : r2.customParameters };
}
function te(e2) {
  return new DOMParser().parseFromString(e2.trim(), "text/xml");
}
function re(e2) {
  const [t3, r2] = e2.split(":");
  return { prefix: r2 ? t3 : "", name: r2 ?? t3 };
}
function ne(e2) {
  var _a;
  const r2 = (_a = e2.firstElementChild) == null ? void 0 : _a.getAttribute("version");
  if (r2 && r2 !== x) throw new s(U2, `Unsupported WFS version ${r2}. Supported version: ${x}`);
}
function ae(e2) {
  let r2 = "", n4 = "";
  if (o3(e2.firstElementChild, { Exception: (e3) => (r2 = e3.getAttribute("exceptionCode"), { ExceptionText: (e4) => {
    n4 = e4.textContent;
  } }) }), r2) throw new s(`wfs-layer:${r2}`, n4);
}

export {
  C,
  D,
  W,
  X,
  z,
  K
};
//# sourceMappingURL=chunk-TSR474T7.js.map
