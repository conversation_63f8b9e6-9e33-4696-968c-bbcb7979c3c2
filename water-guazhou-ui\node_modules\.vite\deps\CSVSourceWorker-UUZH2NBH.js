import {
  g
} from "./chunk-5HHROPN6.js";
import "./chunk-TZ4YPYDT.js";
import {
  ee
} from "./chunk-D6BU5EUE.js";
import "./chunk-QYYMKGDW.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-ZK5O2DLX.js";
import "./chunk-CCFNWAA2.js";
import {
  a
} from "./chunk-WC4DQSYX.js";
import {
  i,
  o
} from "./chunk-D7BTYVTV.js";
import "./chunk-JLELSJK5.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-HLLJFAS4.js";
import "./chunk-6DAQTVXB.js";
import "./chunk-FUIIMETN.js";
import "./chunk-2CLVPBYJ.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-M4ZUXRA3.js";
import "./chunk-WJKHSSMC.js";
import {
  f as f2
} from "./chunk-U4SDSCWW.js";
import {
  t as t2
} from "./chunk-OEIEPNC6.js";
import "./chunk-2RO3UJ2R.js";
import "./chunk-KXA6I5TQ.js";
import "./chunk-ONE6GLG5.js";
import "./chunk-B4KDIR4O.js";
import "./chunk-RE7K5Z3I.js";
import {
  s as s3,
  t
} from "./chunk-SEO6KEGF.js";
import "./chunk-Z2LHI3D7.js";
import "./chunk-KUBJOT5K.js";
import "./chunk-HPMHGZUK.js";
import "./chunk-5AI3QK7R.js";
import "./chunk-XBS7QZIQ.js";
import {
  r
} from "./chunk-UCWK623G.js";
import {
  j as j2
} from "./chunk-P37TUI4J.js";
import "./chunk-3HW44BD3.js";
import {
  ln
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-AVKOL7OR.js";
import {
  M,
  p
} from "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import {
  y
} from "./chunk-R5MYQRRS.js";
import {
  E,
  c,
  f2 as f,
  k
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import {
  L,
  qt
} from "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/graphics/sources/csv/csv.js
var n = /^\s*"([\S\s]*)"\s*$/;
var i2 = /""/g;
var r2 = "\n";
var l = [",", " ", ";", "|", "	"];
function* o2(e, t3, n2) {
  let i3 = 0;
  for (; i3 <= e.length; ) {
    const r3 = e.indexOf(t3, i3), l2 = e.substring(i3, r3 > -1 ? r3 : void 0);
    i3 += l2.length + t3.length, n2 && !l2.trim() || (yield l2);
  }
}
function s4(e) {
  const t3 = e.includes("\r\n") ? "\r\n" : r2;
  return o2(e, t3, true);
}
function u(e, t3) {
  return o2(e, t3, false);
}
function c2(e, t3, n2) {
  e = e.trim(), t3 = t3 == null ? void 0 : t3.trim();
  const i3 = [], r3 = Array.from(/* @__PURE__ */ new Set([n2 == null ? void 0 : n2.delimiter, ...l])).filter((e2) => null != e2);
  for (const l2 of r3) {
    const n3 = d(e, l2).length, r4 = d(t3, l2).length ?? n3;
    n3 > 1 && i3.push({ weight: Math.min(n3, r4), delimiter: l2 });
  }
  const o3 = i3.sort(({ weight: e2 }, { weight: t4 }) => t4 - e2).map(({ delimiter: e2 }) => e2);
  for (const l2 of o3) {
    const t4 = m(f3(e, l2).names, n2 == null ? void 0 : n2.longitudeField, n2 == null ? void 0 : n2.latitudeField);
    if (t4.longitudeFieldName && t4.latitudeFieldName) return { delimiter: l2, locationInfo: t4 };
  }
  return { delimiter: o3[0], locationInfo: null };
}
function* a2(e, t3, l2, o3 = () => /* @__PURE__ */ Object.create(null)) {
  const c3 = s4(e);
  c3.next();
  let a3 = "", f4 = "", d2 = 0, m2 = o3(), p3 = 0;
  e: for (const s5 of c3) {
    const e2 = u(s5, l2);
    for (const r3 of e2) if (a3 += f4 + r3, f4 = "", d2 += g2(r3), d2 % 2 == 0) {
      if (d2 > 0) {
        const e3 = n.exec(a3);
        if (!e3) {
          m2 = o3(), p3 = 0, a3 = "", d2 = 0;
          continue e;
        }
        m2[t3[p3]] = e3[1].replace(i2, '"'), p3++;
      } else m2[t3[p3]] = a3, p3++;
      a3 = "", d2 = 0;
    } else f4 = l2;
    0 === d2 ? (yield m2, m2 = o3(), p3 = 0) : f4 = r2;
  }
}
function f3(e, n2) {
  const i3 = d(e, n2).filter((e2) => null != e2), r3 = i3.map((e2) => p(e2));
  for (let t3 = r3.length - 1; t3 >= 0; t3--) r3[t3] || (r3.splice(t3, 1), i3.splice(t3, 1));
  return { names: r3, aliases: i3 };
}
function d(e, t3) {
  if (!(e == null ? void 0 : e.length)) return [];
  const r3 = [];
  let l2 = "", o3 = "", s5 = 0;
  const c3 = u(e, t3);
  for (const u2 of c3) if (l2 += o3 + u2, o3 = "", s5 += g2(u2), s5 % 2 == 0) {
    if (s5 > 0) {
      const e2 = n.exec(l2);
      e2 && r3.push(e2[1].replace(i2, '"'));
    } else r3.push(l2);
    l2 = "", s5 = 0;
  } else o3 = t3;
  return r3;
}
function g2(e) {
  let t3 = 0, n2 = 0;
  for (n2 = e.indexOf('"', n2); n2 >= 0; ) t3++, n2 = e.indexOf('"', n2 + 1);
  return t3;
}
function m(e, n2, i3) {
  var _a, _b;
  n2 = (_a = p(n2)) == null ? void 0 : _a.toLowerCase(), i3 = (_b = p(i3)) == null ? void 0 : _b.toLowerCase();
  const r3 = e.map((e2) => e2.toLowerCase()), l2 = n2 ? e[r3.indexOf(n2)] : null, o3 = i3 ? e[r3.indexOf(i3)] : null;
  return { longitudeFieldName: l2 || e[r3.indexOf(w.find((e2) => r3.includes(e2)))], latitudeFieldName: o3 || e[r3.indexOf(F.find((e2) => r3.includes(e2)))] };
}
function p2(e, t3, n2, i3, r3) {
  const l2 = [], o3 = a2(e, n2, t3), s5 = [];
  for (const u2 of o3) {
    if (10 === s5.length) break;
    s5.push(u2);
  }
  for (let u2 = 0; u2 < n2.length; u2++) {
    const e2 = n2[u2], t4 = i3[u2];
    if (e2 === r3.longitudeFieldName || e2 === r3.latitudeFieldName) l2.push({ name: e2, type: "esriFieldTypeDouble", alias: t4 });
    else {
      let n3, i4;
      switch (b(s5.map((t5) => t5[e2]))) {
        case "integer":
          n3 = "esriFieldTypeInteger";
          break;
        case "double":
          n3 = "esriFieldTypeDouble";
          break;
        case "date":
          n3 = "esriFieldTypeDate", i4 = 36;
          break;
        default:
          n3 = "esriFieldTypeString", i4 = 255;
      }
      l2.push({ name: e2, type: n3, alias: t4, length: i4 });
    }
  }
  return l2;
}
function b(e) {
  if (!e.length) return "string";
  const t3 = /[^+-.,0-9]/;
  return e.map((e2) => {
    let n2 = false;
    if ("" !== e2) {
      if (t3.test(e2)) n2 = true;
      else {
        let t4 = N(e2);
        if (!isNaN(t4)) return /[.,]/.test(e2) || !Number.isInteger(t4) || t4 > 214783647 || t4 < -214783648 ? "double" : "integer";
        if (e2.includes("E")) {
          if (t4 = Number(e2), !isNaN(t4)) return "double";
          if (e2.includes(",")) {
            if (e2 = e2.replace(",", "."), t4 = Number(e2), !isNaN(t4)) return "double";
            n2 = true;
          } else n2 = true;
        } else n2 = true;
      }
      if (n2) {
        if (!/^[-]?\d*[.,]?\d*$/.test(e2)) {
          return h(new Date(e2), e2) ? "date" : "string";
        }
        return "string";
      }
      return "string";
    }
  }).reduce((e2, t4) => void 0 === e2 ? t4 : void 0 === t4 ? e2 : e2 === t4 ? t4 : "string" === e2 || "string" === t4 ? "string" : "double" === e2 || "double" === t4 ? "double" : void 0);
}
function h(e, t3) {
  if (!e || "[object Date]" !== Object.prototype.toString.call(e) || isNaN(e.getTime())) return false;
  let n2 = true;
  if (!y2 && /\d+\W*$/.test(t3)) {
    const e2 = t3.match(/[a-zA-Z]{2,}/);
    if (e2) {
      let t4 = false, i3 = 0;
      for (; !t4 && i3 <= e2.length; ) t4 = !x.test(e2[i3]), i3++;
      n2 = !t4;
    }
  }
  return n2;
}
var N = function() {
  const t3 = a(), n2 = new RegExp("^" + t3.regexp + "$"), i3 = new RegExp("[" + t3.group + "\\s\\xa0]", "g"), r3 = t3.factor;
  return (e) => {
    const l2 = n2.exec(e);
    if (t3.factor = r3, !l2) return NaN;
    let o3 = l2[1];
    if (!l2[1]) {
      if (!l2[2]) return NaN;
      o3 = l2[2], t3.factor *= -1;
    }
    return o3 = o3.replace(i3, "").replace(t3.decimal, "."), +o3 * t3.factor;
  };
}();
var x = /^((jan(uary)?)|(feb(ruary)?)|(mar(ch)?)|(apr(il)?)|(may)|(jun(e)?)|(jul(y)?)|(aug(ust)?)|(sep(tember)?)|(oct(ober)?)|(nov(ember)?)|(dec(ember)?)|(am)|(pm)|(gmt)|(utc))$/i;
var y2 = Number.isNaN((/* @__PURE__ */ new Date("technology 10")).getTime());
var F = ["lat", "latitude", "latitude83", "latdecdeg", "lat_dd", "y", "ycenter", "point_y"];
var w = ["lon", "lng", "long", "longitude", "longitude83", "longdecdeg", "long_dd", "x", "xcenter", "point_x"];

// node_modules/@arcgis/core/layers/graphics/sources/support/CSVSourceWorker.js
var C = o("esriGeometryPoint");
var v = ["csv"];
var O = [0, 0];
var D = class {
  constructor(e, t3) {
    this.x = e, this.y = t3;
  }
};
var k2 = class {
  constructor() {
    this._queryEngine = null, this._snapshotFeatures = async (e) => {
      const t3 = await this._fetch(e);
      return this._createFeatures(t3);
    };
  }
  destroy() {
    var _a;
    (_a = this._queryEngine) == null ? void 0 : _a.destroy(), this._queryEngine = null;
  }
  async load(e, t3 = {}) {
    var _a;
    this._loadOptions = e;
    const [i3] = await Promise.all([this._fetch(t3.signal), this._checkProjection((_a = e == null ? void 0 : e.parsingOptions) == null ? void 0 : _a.spatialReference)]), n2 = P(i3, e);
    this._locationInfo = n2.locationInfo, this._delimiter = n2.delimiter, this._queryEngine = this._createQueryEngine(n2);
    const r3 = await this._createFeatures(i3);
    this._queryEngine.featureStore.addMany(r3);
    const { fullExtent: s5, timeExtent: o3 } = await this._queryEngine.fetchRecomputedExtents();
    if (n2.layerDefinition.extent = s5, o3) {
      const { start: e2, end: t4 } = o3;
      n2.layerDefinition.timeInfo.timeExtent = [e2, t4];
    }
    return n2;
  }
  async applyEdits() {
    throw new s2("csv-layer:editing-not-supported", "applyEdits() is not supported on CSVLayer");
  }
  async queryFeatures(e = {}, t3 = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQuery(e, t3.signal);
  }
  async queryFeatureCount(e = {}, t3 = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForCount(e, t3.signal);
  }
  async queryObjectIds(e = {}, t3 = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForIds(e, t3.signal);
  }
  async queryExtent(e = {}, t3 = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForExtent(e, t3.signal);
  }
  async querySnapping(e, t3 = {}) {
    return await this._waitSnapshotComplete(), this._queryEngine.executeQueryForSnapping(e, t3.signal);
  }
  async refresh(e) {
    var _a;
    this._loadOptions.customParameters = e, (_a = this._snapshotTask) == null ? void 0 : _a.abort(), this._snapshotTask = j2(this._snapshotFeatures), this._snapshotTask.promise.then((e2) => {
      this._queryEngine.featureStore.clear(), e2 && this._queryEngine.featureStore.addMany(e2);
    }, (e2) => {
      this._queryEngine.featureStore.clear(), j(e2) || s.getLogger("esri.layers.CSVLayer").error(new s2("csv-layer:refresh", "An error occurred during refresh", { error: e2 }));
    }), await this._waitSnapshotComplete();
    const { fullExtent: s5, timeExtent: o3 } = await this._queryEngine.fetchRecomputedExtents();
    return { extent: s5, timeExtent: o3 };
  }
  async _waitSnapshotComplete() {
    if (this._snapshotTask && !this._snapshotTask.finished) {
      try {
        await this._snapshotTask.promise;
      } catch {
      }
      return this._waitSnapshotComplete();
    }
  }
  async _fetch(t3) {
    const { url: n2, customParameters: r3 } = this._loadOptions;
    if (!n2) throw new s2("csv-layer:invalid-source", "url not defined");
    const o3 = L(n2);
    return (await U(o3.path, { query: { ...o3.query, ...r3 }, responseType: "text", signal: t3 })).data;
  }
  _createQueryEngine(e) {
    const { objectIdField: t3, fields: i3, extent: n2, timeInfo: r3 } = e.layerDefinition, s5 = new g({ geometryType: "esriGeometryPoint", hasM: false, hasZ: false });
    return new ee({ fields: i3, geometryType: "esriGeometryPoint", hasM: false, hasZ: false, timeInfo: r3, objectIdField: t3, spatialReference: n2.spatialReference || { wkid: 4326 }, cacheSpatialQueries: true, featureStore: s5 });
  }
  async _createFeatures(e) {
    const { latitudeFieldName: t3, longitudeFieldName: i3 } = this._locationInfo, { objectIdField: n2, fieldsIndex: r3, spatialReference: s5 } = this._queryEngine;
    let o3 = [];
    const d2 = [], y3 = r3.fields.filter((e2) => e2.name !== n2).map((e2) => e2.name);
    let h2 = 0;
    const g3 = {};
    for (const a3 of r3.fields) if ("esriFieldTypeOID" !== a3.type && "esriFieldTypeGlobalID" !== a3.type) {
      const e2 = M(a3);
      void 0 !== e2 && (g3[a3.name] = e2);
    }
    const w2 = a2(e, y3, this._delimiter, i(g3, n2));
    for (const a3 of w2) {
      const e2 = this._parseCoordinateValue(a3[t3]), s6 = this._parseCoordinateValue(a3[i3]);
      if (null != s6 && null != e2 && !isNaN(e2) && !isNaN(s6)) {
        a3[t3] = e2, a3[i3] = s6;
        for (const e3 in a3) if (e3 !== t3 && e3 !== i3) {
          if (r3.isDateField(e3)) {
            const t4 = new Date(a3[e3]);
            a3[e3] = h(t4, a3[e3]) ? t4.getTime() : null;
          } else if (r3.isNumericField(e3)) {
            const t4 = N(a3[e3]);
            isNaN(t4) ? a3[e3] = null : a3[e3] = t4;
          }
        }
        a3[n2] = h2, h2++, o3.push(new D(s6, e2)), d2.push(a3);
      }
    }
    if (!E({ wkid: 4326 }, s5)) if (k(s5)) for (const a3 of o3) [a3.x, a3.y] = y(a3.x, a3.y, O);
    else o3 = ln(t2, o3, f.WGS84, s5, null, null);
    const E2 = [];
    for (let a3 = 0; a3 < o3.length; a3++) {
      const { x: e2, y: t4 } = o3[a3], i4 = d2[a3];
      i4[n2] = a3 + 1, E2.push(new s3(new t([], [e2, t4]), i4, null, i4[n2]));
    }
    return E2;
  }
  _parseCoordinateValue(e) {
    if (null == e || "" === e) return null;
    let t3 = N(e);
    return (isNaN(t3) || Math.abs(t3) > 181) && (t3 = parseFloat(e)), t3;
  }
  async _checkProjection(e) {
    try {
      await f2(c, e);
    } catch {
      throw new s2("csv-layer:projection-not-supported", "Projection not supported");
    }
  }
};
function P(e, t3) {
  var _a, _b, _c;
  const n2 = t3.parsingOptions || {}, r3 = { delimiter: n2.delimiter, layerDefinition: null, locationInfo: { latitudeFieldName: n2.latitudeField, longitudeFieldName: n2.longitudeField } }, s5 = r3.layerDefinition = { name: qt(t3.url, v) || "csv", drawingInfo: C, geometryType: "esriGeometryPoint", objectIdField: null, fields: [], timeInfo: n2.timeInfo, extent: { xmin: Number.POSITIVE_INFINITY, ymin: Number.POSITIVE_INFINITY, xmax: Number.NEGATIVE_INFINITY, ymax: Number.NEGATIVE_INFINITY, spatialReference: n2.spatialReference || { wkid: 4326 } } }, a3 = s4(e), l2 = (_a = a3.next().value) == null ? void 0 : _a.trim(), c3 = (_b = a3.next().value) == null ? void 0 : _b.trim();
  if (!l2) throw new s2("csv-layer:empty-csv", "CSV is empty", { csv: e });
  const { delimiter: m2, locationInfo: d2 } = c2(l2, c3, n2);
  if (!m2) throw new s2("csv-layer:invalid-delimiter", "Unable to detect the delimiter from CSV", { firstLine: l2, secondLine: c3, parsingOptions: n2 });
  if (!d2) throw new s2("csv-layer:location-fields-not-found", "Unable to identify latitude and longitude fields from the CSV file", { firstLine: l2, secondLine: c3, parsingOptions: n2 });
  r3.locationInfo = d2, r3.delimiter = m2;
  const { names: u2, aliases: p3 } = f3(l2, m2), f4 = p2(e, r3.delimiter, u2, p3, r3.locationInfo);
  if ((_c = n2.fields) == null ? void 0 : _c.length) {
    const e2 = new r(n2.fields);
    for (const t4 of f4) {
      const i3 = e2.get(t4.name);
      i3 && Object.assign(t4, i3);
    }
  }
  if (!f4.some((e2) => "esriFieldTypeOID" === e2.type && (s5.objectIdField = e2.name, true))) {
    const e2 = { name: "__OBJECTID", alias: "__OBJECTID", type: "esriFieldTypeOID", editable: false, nullable: false };
    s5.objectIdField = e2.name, f4.unshift(e2);
  }
  s5.fields = f4;
  const y3 = new r(s5.fields);
  if (r3.locationInfo && (r3.locationInfo.latitudeFieldName = y3.get(r3.locationInfo.latitudeFieldName).name, r3.locationInfo.longitudeFieldName = y3.get(r3.locationInfo.longitudeFieldName).name), s5.timeInfo) {
    const e2 = s5.timeInfo;
    if (e2.startTimeField) {
      const t4 = y3.get(e2.startTimeField);
      t4 ? (e2.startTimeField = t4.name, t4.type = "esriFieldTypeDate") : e2.startTimeField = null;
    }
    if (e2.endTimeField) {
      const t4 = y3.get(e2.endTimeField);
      t4 ? (e2.endTimeField = t4.name, t4.type = "esriFieldTypeDate") : e2.endTimeField = null;
    }
    if (e2.trackIdField) {
      const t4 = y3.get(e2.trackIdField);
      e2.trackIdField = t4 ? t4.name : null;
    }
    e2.startTimeField || e2.endTimeField || (s5.timeInfo = null);
  }
  return r3;
}
export {
  k2 as default
};
//# sourceMappingURL=CSVSourceWorker-UUZH2NBH.js.map
