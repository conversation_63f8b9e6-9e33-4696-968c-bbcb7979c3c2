{"version": 3, "sources": ["../../@arcgis/core/widgets/support/GoTo.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../../chunks/tslib.es6.js\";import{assertIsSome as r}from\"../../core/maybe.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";const t=t=>{let i=class extends t{constructor(...o){super(...o),this.goToOverride=null,this.view=null}callGoTo(o){const{view:e}=this;return r(e),this.goToOverride?this.goToOverride(e,o):e.goTo(o.target,o.options)}};return o([e()],i.prototype,\"goToOverride\",void 0),o([e()],i.prototype,\"view\",void 0),i=o([s(\"esri.widgets.support.GoTo\")],i),i};export{t as GoToMixin};\n"], "mappings": ";;;;;;;;;;;;AAI6U,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAIC,KAAE,cAAcD,GAAC;AAAA,IAAC,eAAe,GAAE;AAAC,YAAM,GAAG,CAAC,GAAE,KAAK,eAAa,MAAK,KAAK,OAAK;AAAA,IAAI;AAAA,IAAC,SAAS,GAAE;AAAC,YAAK,EAAC,MAAKE,GAAC,IAAE;AAAK,aAAO,EAAEA,EAAC,GAAE,KAAK,eAAa,KAAK,aAAaA,IAAE,CAAC,IAAEA,GAAE,KAAK,EAAE,QAAO,EAAE,OAAO;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,GAAED,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,GAAEA,EAAC,GAAEA;AAAC;", "names": ["t", "i", "e"]}