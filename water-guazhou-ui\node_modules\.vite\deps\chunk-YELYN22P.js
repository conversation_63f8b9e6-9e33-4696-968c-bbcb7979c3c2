import {
  n as n5
} from "./chunk-XSQFM27N.js";
import {
  n as n4
} from "./chunk-QYOAH6AO.js";
import {
  n as n3
} from "./chunk-A7PY25IH.js";
import {
  o as o2
} from "./chunk-FOE4ICAJ.js";
import {
  s as s3
} from "./chunk-P2G4OGHI.js";
import {
  p as p2
} from "./chunk-YEODPCXQ.js";
import {
  n as n2,
  r as r2,
  u as u3
} from "./chunk-NOZFLZZL.js";
import {
  L,
  l
} from "./chunk-EIGTETCG.js";
import {
  O,
  P,
  _,
  e,
  g,
  o,
  p,
  r,
  s as s2,
  u as u2,
  v,
  x,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n,
  t as t3,
  u
} from "./chunk-36FLFRUE.js";
import {
  a
} from "./chunk-RQXGVG3K.js";
import {
  t as t2
} from "./chunk-JEDE7445.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/Axis.js
var n6;
!function(n7) {
  n7[n7.X = 0] = "X", n7[n7.Y = 1] = "Y", n7[n7.Z = 2] = "Z";
}(n6 || (n6 = {}));

// node_modules/@arcgis/core/core/ObjectStack.js
var s4 = class {
  constructor(t5) {
    this._allocator = t5, this._items = [], this._itemsPtr = 0, this._grow();
  }
  get() {
    return 0 === this._itemsPtr && t2(() => this._reset()), this._itemsPtr === this._items.length && this._grow(), this._items[this._itemsPtr++];
  }
  _reset() {
    const t5 = Math.min(3 * Math.max(8, this._itemsPtr), this._itemsPtr + 3 * i);
    this._items.length = Math.min(t5, this._items.length), this._itemsPtr = 0;
  }
  _grow() {
    for (let t5 = 0; t5 < Math.max(8, Math.min(this._items.length, i)); t5++) this._items.push(this._allocator());
  }
};
var i = 1024;

// node_modules/@arcgis/core/core/VectorStack.js
var a2 = class _a {
  constructor(t5, e3, s5) {
    this._itemByteSize = t5, this._itemCreate = e3, this._buffers = new Array(), this._items = new Array(), this._itemsPtr = 0, this._itemsPerBuffer = Math.ceil(s5 / this._itemByteSize);
  }
  get() {
    0 === this._itemsPtr && t2(() => this._reset());
    const t5 = Math.floor(this._itemsPtr / this._itemsPerBuffer);
    for (; this._buffers.length <= t5; ) {
      const t6 = new ArrayBuffer(this._itemsPerBuffer * this._itemByteSize);
      for (let e3 = 0; e3 < this._itemsPerBuffer; ++e3) this._items.push(this._itemCreate(t6, e3 * this._itemByteSize));
      this._buffers.push(t6);
    }
    return this._items[this._itemsPtr++];
  }
  _reset() {
    const t5 = 2 * (Math.floor(this._itemsPtr / this._itemsPerBuffer) + 1);
    for (; this._buffers.length > t5; ) this._buffers.pop(), this._items.length = this._buffers.length * this._itemsPerBuffer;
    this._itemsPtr = 0;
  }
  static createVec2f64(t5 = c) {
    return new _a(16, o2, t5);
  }
  static createVec3f64(t5 = c) {
    return new _a(24, u, t5);
  }
  static createVec4f64(t5 = c) {
    return new _a(32, u3, t5);
  }
  static createMat3f64(t5 = c) {
    return new _a(72, n3, t5);
  }
  static createMat4f64(t5 = c) {
    return new _a(128, n4, t5);
  }
  static createQuatf64(t5 = c) {
    return new _a(32, n5, t5);
  }
  get test() {
    return { size: this._buffers.length * this._itemsPerBuffer * this._itemByteSize };
  }
};
var c = 4 * s3.KILOBYTES;

// node_modules/@arcgis/core/geometry/support/vectorStacks.js
var t4 = a2.createVec2f64();
var c2 = a2.createVec3f64();
var r3 = a2.createVec4f64();
var a3 = a2.createMat3f64();
var f = a2.createMat4f64();
var o3 = a2.createQuatf64();

// node_modules/@arcgis/core/geometry/support/ray.js
function d(i4) {
  return i4 ? m(t3(i4.origin), t3(i4.direction)) : m(n(), n());
}
function m(i4, n7) {
  return { origin: i4, direction: n7 };
}
function p3(i4, n7) {
  const r4 = S.get();
  return r4.origin = i4, r4.direction = n7, r4;
}
function k(i4, n7 = d()) {
  return v2(i4.origin, i4.direction, n7);
}
function h(i4, n7, o4 = d()) {
  return r(o4.origin, i4), e(o4.direction, n7, i4), o4;
}
function v2(i4, n7, t5 = d()) {
  return r(t5.origin, i4), r(t5.direction, n7), t5;
}
function b(i4, n7) {
  const r4 = _(c2.get(), z(c2.get(), i4.direction), e(c2.get(), n7, i4.origin));
  return P(r4, r4);
}
function q(i4, n7, r4) {
  const o4 = P(i4.direction, e(r4, n7, i4.origin));
  return u2(r4, i4.origin, g(r4, i4.direction, o4)), r4;
}
var S = new s4(() => d());

// node_modules/@arcgis/core/geometry/support/vector.js
function e2(n7, r4, s5) {
  const c3 = P(n7, r4) / P(n7, n7);
  return g(s5, n7, c3);
}
function f2(n7, o4) {
  return P(n7, o4) / s2(n7);
}
function a4(o4, s5) {
  const c3 = P(o4, s5) / (s2(o4) * s2(s5));
  return -l(c3);
}
function i3(o4, r4, u4) {
  z(m2, o4), z(h2, r4);
  const e3 = P(m2, h2), f3 = l(e3), a5 = _(m2, m2, h2);
  return P(a5, u4) < 0 ? 2 * Math.PI - f3 : f3;
}
var m2 = n();
var h2 = n();

// node_modules/@arcgis/core/chunks/sphere.js
function R() {
  return n2();
}
function _2(t5, r4 = R()) {
  return a(r4, t5);
}
function q2(t5, r4) {
  return r2(t5[0], t5[1], t5[2], r4);
}
function w(t5) {
  return t5;
}
function C(t5) {
  t5[0] = t5[1] = t5[2] = t5[3] = 0;
}
function O2(t5, r4) {
  return t5[0] = t5[1] = t5[2] = 0, t5[3] = r4, t5;
}
function T(t5) {
  return t5[3];
}
function k2(t5) {
  return t5;
}
function E(t5, r4, n7, e3) {
  return r2(t5, r4, n7, e3);
}
function L2(t5, r4, n7) {
  return t5 !== n7 && r(n7, t5), n7[3] = t5[3] + r4, n7;
}
function Z(r4, n7, e3) {
  return s.getLogger("esri.geometry.support.sphere").error("sphere.setExtent is not yet supported"), r4 === e3 ? e3 : _2(r4, e3);
}
function z2(t5, r4, n7) {
  if (t(r4)) return false;
  const { origin: o4, direction: s5 } = r4, i4 = U;
  i4[0] = o4[0] - t5[0], i4[1] = o4[1] - t5[1], i4[2] = o4[2] - t5[2];
  const a5 = s5[0] * s5[0] + s5[1] * s5[1] + s5[2] * s5[2];
  if (0 === a5) return false;
  const c3 = 2 * (s5[0] * i4[0] + s5[1] * i4[1] + s5[2] * i4[2]), u4 = c3 * c3 - 4 * a5 * (i4[0] * i4[0] + i4[1] * i4[1] + i4[2] * i4[2] - t5[3] * t5[3]);
  if (u4 < 0) return false;
  const f3 = Math.sqrt(u4);
  let m3 = (-c3 - f3) / (2 * a5);
  const p4 = (-c3 + f3) / (2 * a5);
  return (m3 < 0 || p4 < m3 && p4 > 0) && (m3 = p4), !(m3 < 0) && (n7 && (n7[0] = o4[0] + s5[0] * m3, n7[1] = o4[1] + s5[1] * m3, n7[2] = o4[2] + s5[2] * m3), true);
}
var U = n();
function V(t5, r4) {
  return z2(t5, r4, null);
}
function X(t5, r4, n7) {
  if (z2(t5, r4, n7)) return n7;
  const e3 = Y(t5, r4, c2.get());
  return u2(n7, r4.origin, g(c2.get(), r4.direction, x(r4.origin, e3) / s2(r4.direction))), n7;
}
function Y(t5, r4, n7) {
  const e3 = c2.get(), s5 = f.get();
  _(e3, r4.origin, r4.direction);
  const i4 = T(t5);
  _(n7, e3, r4.origin), g(n7, n7, 1 / s2(n7) * i4);
  const c3 = G(t5, r4.origin), p4 = a4(r4.origin, n7);
  return p2(s5, p4 + c3, e3), O(n7, n7, s5), n7;
}
function B(t5, r4, n7) {
  return z2(t5, r4, n7) ? n7 : (q(r4, k2(t5), n7), D(t5, n7, n7));
}
function D(t5, r4, n7) {
  const e3 = e(c2.get(), r4, k2(t5)), o4 = g(c2.get(), e3, t5[3] / s2(e3));
  return u2(n7, o4, k2(t5));
}
function F(t5, r4) {
  const n7 = e(c2.get(), r4, k2(t5)), e3 = v(n7), o4 = t5[3] * t5[3];
  return Math.sqrt(Math.abs(e3 - o4));
}
function G(t5, n7) {
  const e3 = e(c2.get(), n7, k2(t5)), o4 = s2(e3), s5 = T(t5), i4 = s5 + Math.abs(s5 - o4);
  return l(s5 / i4);
}
var H = n();
function I(t5, r4, e3, o4) {
  const s5 = e(H, r4, k2(t5));
  switch (e3) {
    case n6.X: {
      const t6 = L(s5, H)[2];
      return o(o4, -Math.sin(t6), Math.cos(t6), 0);
    }
    case n6.Y: {
      const t6 = L(s5, H), r5 = t6[1], e4 = t6[2], i4 = Math.sin(r5);
      return o(o4, -i4 * Math.cos(e4), -i4 * Math.sin(e4), Math.cos(r5));
    }
    case n6.Z:
      return z(o4, s5);
    default:
      return;
  }
}
function J(t5, r4) {
  const n7 = e(Q, r4, k2(t5));
  return s2(n7) - t5[3];
}
function K(t5, r4, n7, e3) {
  const o4 = J(t5, r4), s5 = I(t5, r4, n6.Z, Q), c3 = g(Q, s5, n7 - o4);
  return u2(e3, r4, c3);
}
function N(t5, r4) {
  const n7 = p(k2(t5), r4), e3 = T(t5);
  return n7 <= e3 * e3;
}
var Q = n();
var W = R();
var $ = Object.freeze(Object.defineProperty({ __proto__: null, altitudeAt: J, angleToSilhouette: G, axisAt: I, clear: C, closestPoint: B, closestPointOnSilhouette: Y, containsPoint: N, copy: _2, create: R, distanceToSilhouette: F, elevate: L2, fromCenterAndRadius: q2, fromRadius: O2, fromValues: E, getCenter: k2, getRadius: T, intersectRay: z2, intersectRayClosestSilhouette: X, intersectsRay: V, projectPoint: D, setAltitudeAt: K, setExtent: Z, tmpSphere: W, wrap: w }, Symbol.toStringTag, { value: "Module" }));

export {
  n6 as n,
  s4 as s,
  t4 as t,
  c2 as c,
  r3 as r,
  f,
  o3 as o,
  d,
  p3 as p,
  k,
  h,
  b,
  e2 as e,
  f2,
  a4 as a,
  i3 as i,
  R,
  _2 as _,
  w,
  C,
  T,
  k2,
  E,
  V,
  N
};
//# sourceMappingURL=chunk-YELYN22P.js.map
