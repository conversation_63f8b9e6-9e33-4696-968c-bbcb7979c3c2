import {
  S,
  s
} from "./chunk-AVKOL7OR.js";
import {
  D,
  x
} from "./chunk-I7WHRVHF.js";
import {
  E
} from "./chunk-JXLVNWKF.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/graphics/dehydratedFeatureComparison.js
function i(t3, r2) {
  return t3 === r2 || r(t3) && r(r2) && E(t3.spatialReference, r2.spatialReference) && t3.x === r2.x && t3.y === r2.y && t3.z === r2.z && t3.m === r2.m;
}

// node_modules/@arcgis/core/layers/graphics/dehydratedFeatures.js
var g = class {
  constructor(e2, t3, s2) {
    this.uid = e2, this.geometry = t3, this.attributes = s2, this.visible = true, this.objectId = null, this.centroid = null;
  }
};
function b(e2) {
  return r(e2.geometry);
}
var d = class {
  constructor() {
    this.exceededTransferLimit = false, this.features = [], this.fields = [], this.hasM = false, this.hasZ = false, this.geometryType = null, this.objectIdFieldName = null, this.globalIdFieldName = null, this.geometryProperties = null, this.geohashFieldName = null, this.spatialReference = null, this.transform = null;
  }
};
function M(e2, t3, s2, r2) {
  return { x: e2, y: t3, z: s2, hasZ: null != s2, hasM: false, spatialReference: r2, type: "point" };
}
function z(e2) {
  if (t(e2)) return 0;
  switch (e2.type) {
    case "point":
      return 1;
    case "polyline": {
      let t3 = 0;
      for (const s2 of e2.paths) t3 += s2.length;
      return t3;
    }
    case "polygon": {
      let t3 = 0;
      for (const s2 of e2.rings) t3 += s2.length;
      return t3;
    }
    case "multipoint":
      return e2.points.length;
    case "extent":
      return 2;
    case "mesh": {
      const t3 = e2.vertexAttributes && e2.vertexAttributes.position;
      return t3 ? t3.length / 3 : 0;
    }
    default:
      return;
  }
}
function I(e2, t3) {
  switch (S(t3), "mesh" === e2.type && (e2 = e2.extent), e2.type) {
    case "point":
      t3[0] = t3[3] = e2.x, t3[1] = t3[4] = e2.y, e2.hasZ && (t3[2] = t3[5] = e2.z);
      break;
    case "polyline":
      for (let s2 = 0; s2 < e2.paths.length; s2++) s(t3, e2.paths[s2], !!e2.hasZ);
      break;
    case "polygon":
      for (let s2 = 0; s2 < e2.rings.length; s2++) s(t3, e2.rings[s2], !!e2.hasZ);
      break;
    case "multipoint":
      s(t3, e2.points, !!e2.hasZ);
      break;
    case "extent":
      t3[0] = e2.xmin, t3[1] = e2.ymin, t3[3] = e2.xmax, t3[4] = e2.ymax, null != e2.zmin && (t3[2] = e2.zmin), null != e2.zmax && (t3[5] = e2.zmax);
  }
}
function v2(e2, t3) {
  switch (D(t3), "mesh" === e2.type && (e2 = e2.extent), e2.type) {
    case "point":
      t3[0] = t3[2] = e2.x, t3[1] = t3[3] = e2.y;
      break;
    case "polyline":
      for (let s2 = 0; s2 < e2.paths.length; s2++) x(t3, e2.paths[s2]);
      break;
    case "polygon":
      for (let s2 = 0; s2 < e2.rings.length; s2++) x(t3, e2.rings[s2]);
      break;
    case "multipoint":
      x(t3, e2.points);
      break;
    case "extent":
      t3[0] = e2.xmin, t3[1] = e2.ymin, t3[2] = e2.xmax, t3[3] = e2.ymax;
  }
}
function R(e2, t3) {
  return null != e2.objectId ? e2.objectId : e2.attributes && t3 ? e2.attributes[t3] : null;
}

export {
  i,
  g,
  b,
  d,
  M,
  z,
  I,
  v2 as v,
  R
};
//# sourceMappingURL=chunk-VHLK35TF.js.map
