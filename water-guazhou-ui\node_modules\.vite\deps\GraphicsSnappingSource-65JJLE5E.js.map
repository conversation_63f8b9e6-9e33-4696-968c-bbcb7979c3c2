{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/GraphicsSnappingSource.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import t from\"../../../../core/Accessor.js\";import{removeUnordered as o}from\"../../../../core/arrayUtils.js\";import{createTask as s}from\"../../../../core/asyncUtils.js\";import{HandleOwnerMixin as r}from\"../../../../core/HandleOwner.js\";import{isSome as i,unwrap as n,isNone as a}from\"../../../../core/maybe.js\";import{eachAlwaysValues as p,throwIfAborted as l,whenOrAbort as c}from\"../../../../core/promiseUtils.js\";import{initial as d,watch as h,on as u}from\"../../../../core/reactiveUtils.js\";import{property as y}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import{subclass as m}from\"../../../../core/accessorSupport/decorators/subclass.js\";import g from\"../../../../geometry/Polygon.js\";import{canProjectWithoutEngine as f,project as _,initializeProjection as v}from\"../../../../geometry/projection.js\";import{normalizeCentralMeridianForDisplay as S}from\"../../../../geometry/support/normalizeUtilsSync.js\";import{featureGeometryTypeKebabDictionary as w}from\"../../../../geometry/support/typeUtils.js\";import{convertFromGeometry as j}from\"../../../../layers/graphics/featureConversionUtils.js\";import{OptimizedFeature as b}from\"../../../../layers/graphics/OptimizedFeature.js\";import E from\"../../../../layers/graphics/data/FeatureStore.js\";import{QueryEngine as k}from\"../../../../layers/graphics/data/QueryEngine.js\";import{elevationContextAffectsAlignment as C}from\"../../../../support/elevationInfoUtils.js\";import{symbolHasExtrudeSymbolLayer as F}from\"../../../../symbols/support/utils.js\";import{sortCandidatesInPlace as A,makeSnappingQuery as P}from\"../snappingUtils.js\";import{convertSnappingCandidate as R,makeGetGroundElevation as G}from\"./queryEngineUtils.js\";import{getSnappingCandidateElevationAligner as I}from\"./snappingCandidateElevationAlignment.js\";import{getSnappingCandidateElevationFilter as O}from\"./snappingCandidateElevationFilter.js\";import{getSymbologySnappingCandidatesFetcher as U}from\"./symbologySnappingCandidates.js\";const x=\"graphics-collections\";let z=class extends(r(t)){get updating(){return this.updatingHandles.updating}get _hasZ(){const e=this.view;return i(e)&&\"3d\"===e.type&&\"map-notes\"!==this.layerSource.layer.type}get _snappingElevationAligner(){const{view:e}=this,{layer:t}=this.layerSource,o=i(e)&&\"3d\"===e.type;if(!o||\"map-notes\"===t.type)return I();const s=async(o,s)=>(await c(e.whenLayerView(t),s)).elevationAlignPointsInFeatures(o,s);return I(o,{elevationInfo:t.elevationInfo,alignPointsInFeatures:s,spatialReference:e.spatialReference})}get _snappingElevationFilter(){const{view:e}=this,t=i(e)&&\"3d\"===e.type&&\"map-notes\"!==this.layerSource.layer.type;return O(t)}get _symbologySnappingFetcher(){const{view:e}=this,{layer:t}=this.layerSource,o=i(e)&&\"3d\"===e.type,s=this._extrudedPolygonSymbolsCount>0;return o&&\"map-notes\"!==t.type&&s?U(s,(async(o,s)=>{const r=await e.whenLayerView(t);return l(s),r.queryForSymbologySnapping({candidates:o,spatialReference:e.spatialReference},s)})):U()}constructor(e){super(e),this.availability=1,this._sources={multipoint:null,point:null,polygon:null,polyline:null},this._loadedWkids=new Set,this._loadedWkts=new Set,this._pendingAdds=[],this._extrudedPolygonSymbolsCount=0}destroy(){for(const e of this._pendingAdds)e.task.abort();this._pendingAdds.length=0,this._mapSources((e=>this._destroySource(e)))}initialize(){this.updatingHandles.add((()=>this.getGraphicsLayers()),(e=>{this.updatingHandles.removeHandles(x);for(const t of e)this._addMany(t.graphics.toArray()),this.handles.add([t.on(\"graphic-update\",(e=>this._onGraphicUpdate(e))),this.updatingHandles.addOnCollectionChange((()=>t.graphics),(e=>this._onGraphicsChanged(e)))],x)}),d);const{view:e}=this,{layer:t}=this.layerSource;i(e)&&\"3d\"===e.type&&\"map-notes\"!==t.type&&this.addHandles([e.elevationProvider.on(\"elevation-change\",(({context:e})=>{C(e,t.elevationInfo)&&this._snappingElevationAligner.notifyElevationSourceChange()})),h((()=>t.elevationInfo),(()=>this._snappingElevationAligner.notifyElevationSourceChange()),d),u((()=>t),[\"edits\",\"apply-edits\",\"graphic-update\"],(()=>this._symbologySnappingFetcher.notifySymbologyChange()))])}async fetchCandidates(e,t){const{point:o}=e,s=await p(this._mapSources((o=>this._fetchCandidatesForSource(o,e,t))));l(t);const r=this._getGroundElevation,i=s.flat().map((e=>R(e,r)));return A(o,i),i}get _getGroundElevation(){return G(this.view)}async _fetchCandidatesForSource(e,t,o){const s=P(t,n(this.view)?.type??\"2d\"),r=await e.queryEngine.executeQueryForSnapping(s,o);l(o);const i=await this._snappingElevationAligner.alignCandidates(r.candidates,o);l(o);const a=await this._symbologySnappingFetcher.fetch(i,o);l(o);const p=0===a.length?i:[...i,...a];return this._snappingElevationFilter.filter(s,p)}refresh(){}_onGraphicUpdate(e){if(this.getGraphicsLayers().some((t=>t.graphics.includes(e.graphic))))switch(e.property){case\"geometry\":case\"visible\":this._remove(e.graphic),this._addMany([e.graphic])}}_onGraphicsChanged(e){for(const t of e.removed)this._remove(t);this._addMany(e.added)}_addMany(e){const t=[],o=new Map;for(const s of e)a(s.geometry)||(this._needsInitializeProjection(s.geometry.spatialReference)?(t.push(s.geometry.spatialReference),o.set(s.uid,s)):this._add(s));this._createPendingAdd(t,o)}_createPendingAdd(e,t){if(!e.length)return;const r=s((async o=>{await v(e.map((e=>({source:e,dest:this.spatialReference}))),{signal:o}),this._markLoadedSpatialReferences(e);for(const[,e]of t)this._add(e)}));this.updatingHandles.addPromise(r.promise);const i={task:r,graphics:t},n=()=>o(this._pendingAdds,i);r.promise.then(n,n),this._pendingAdds.push(i)}_markLoadedSpatialReferences(e){for(const t of e)null!=t.wkid&&this._loadedWkids.add(t.wkid),null!=t.wkt&&this._loadedWkts.add(t.wkt)}_add(e){if(a(e.geometry)||!e.visible)return;let t=e.geometry;if(\"mesh\"===t.type)return;\"extent\"===t.type&&(t=g.fromExtent(t));const o=this._ensureSource(t.type);if(a(o))return;const s=this._createOptimizedFeature(e.uid,t);a(s)||(o.featureStore.add(s),F(e.symbol)&&this._extrudedPolygonSymbolsCount++)}_needsInitializeProjection(e){return(null==e.wkid||!this._loadedWkids.has(e.wkid))&&((null==e.wkt||!this._loadedWkts.has(e.wkt))&&!f(e,this.spatialReference))}_createOptimizedFeature(e,t){const o=_(S(t),this.spatialReference);if(!o)return null;const s=this._ensureGeometryHasZ(o),r=j(s,this._hasZ,!1);return new b(r,{[H]:e},null,e)}_ensureGeometryHasZ(e){if(!this._hasZ)return e;const t=e=>{for(;e.length<3;)e.push(0)},o=e.clone();switch(o.hasZ=!0,o.type){case\"point\":o.z=o.z??0;break;case\"multipoint\":o.points.forEach(t);break;case\"polyline\":o.paths.forEach((e=>e.forEach(t)));break;case\"polygon\":o.rings.forEach((e=>e.forEach(t)))}return o}_ensureSource(e){const t=this._sources[e];if(i(t))return t;const o=this._createSource(e);return this._sources[e]=o,o}_createSource(e){const t=w.toJSON(e),o=this._hasZ,s=new E({geometryType:t,hasZ:o,hasM:!1});return{featureStore:s,queryEngine:new k({featureStore:s,fields:[{name:H,type:\"esriFieldTypeOID\",alias:H}],geometryType:t,hasM:!1,hasZ:o,objectIdField:H,spatialReference:this.spatialReference,scheduler:i(this.view)&&\"3d\"===this.view.type?this.view.resourceController.scheduler:null}),type:e}}_remove(e){this._mapSources((t=>this._removeFromSource(t,e)));for(const t of this._pendingAdds)t.graphics.delete(e.uid),0===t.graphics.size&&t.task.abort()}_removeFromSource(e,t){const o=t.uid;e.featureStore.has(o)&&(e.featureStore.removeById(t.uid),F(t.symbol)&&this._extrudedPolygonSymbolsCount--)}_destroySource(e){e.queryEngine.destroy(),this._sources[e.type]=null}_mapSources(e){const{point:t,polygon:o,polyline:s,multipoint:r}=this._sources,n=[];return i(t)&&n.push(e(t)),i(o)&&n.push(e(o)),i(s)&&n.push(e(s)),i(r)&&n.push(e(r)),n}};e([y()],z.prototype,\"getGraphicsLayers\",void 0),e([y({constructOnly:!0})],z.prototype,\"layerSource\",void 0),e([y({constructOnly:!0})],z.prototype,\"spatialReference\",void 0),e([y({constructOnly:!0})],z.prototype,\"view\",void 0),e([y({readOnly:!0})],z.prototype,\"updating\",null),e([y({readOnly:!0})],z.prototype,\"availability\",void 0),e([y()],z.prototype,\"_hasZ\",null),e([y()],z.prototype,\"_snappingElevationAligner\",null),e([y()],z.prototype,\"_snappingElevationFilter\",null),e([y()],z.prototype,\"_symbologySnappingFetcher\",null),e([y()],z.prototype,\"_extrudedPolygonSymbolsCount\",void 0),e([y()],z.prototype,\"_getGroundElevation\",null),z=e([m(\"esri.views.interactive.snapping.featureSources.GraphicsSnappingSource\")],z);const H=\"OBJECTID\";export{z as GraphicsSnappingSource};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI4hE,IAAM,IAAE;AAAuB,IAAI,IAAE,cAAcA,GAAEC,EAAC,EAAE;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,gBAAgB;AAAA,EAAQ;AAAA,EAAC,IAAI,QAAO;AAAC,UAAMC,KAAE,KAAK;AAAK,WAAO,EAAEA,EAAC,KAAG,SAAOA,GAAE,QAAM,gBAAc,KAAK,YAAY,MAAM;AAAA,EAAI;AAAA,EAAC,IAAI,4BAA2B;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAK,EAAC,OAAMC,GAAC,IAAE,KAAK,aAAYC,KAAE,EAAEF,EAAC,KAAG,SAAOA,GAAE;AAAK,QAAG,CAACE,MAAG,gBAAcD,GAAE,KAAK,QAAOE,GAAE;AAAE,UAAMC,KAAE,OAAMF,IAAEE,QAAK,MAAMC,GAAEL,GAAE,cAAcC,EAAC,GAAEG,EAAC,GAAG,+BAA+BF,IAAEE,EAAC;AAAE,WAAOD,GAAED,IAAE,EAAC,eAAcD,GAAE,eAAc,uBAAsBG,IAAE,kBAAiBJ,GAAE,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,2BAA0B;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAKC,KAAE,EAAED,EAAC,KAAG,SAAOA,GAAE,QAAM,gBAAc,KAAK,YAAY,MAAM;AAAK,WAAOG,GAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,4BAA2B;AAAC,UAAK,EAAC,MAAKD,GAAC,IAAE,MAAK,EAAC,OAAMC,GAAC,IAAE,KAAK,aAAYC,KAAE,EAAEF,EAAC,KAAG,SAAOA,GAAE,MAAKI,KAAE,KAAK,+BAA6B;AAAE,WAAOF,MAAG,gBAAcD,GAAE,QAAMG,KAAE,EAAEA,IAAG,OAAMF,IAAEE,OAAI;AAAC,YAAMD,KAAE,MAAMH,GAAE,cAAcC,EAAC;AAAE,aAAO,EAAEG,EAAC,GAAED,GAAE,0BAA0B,EAAC,YAAWD,IAAE,kBAAiBF,GAAE,iBAAgB,GAAEI,EAAC;AAAA,IAAC,CAAE,IAAE,EAAE;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,eAAa,GAAE,KAAK,WAAS,EAAC,YAAW,MAAK,OAAM,MAAK,SAAQ,MAAK,UAAS,KAAI,GAAE,KAAK,eAAa,oBAAI,OAAI,KAAK,cAAY,oBAAI,OAAI,KAAK,eAAa,CAAC,GAAE,KAAK,+BAA6B;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,eAAUA,MAAK,KAAK,aAAa,CAAAA,GAAE,KAAK,MAAM;AAAE,SAAK,aAAa,SAAO,GAAE,KAAK,YAAa,CAAAA,OAAG,KAAK,eAAeA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAgB,IAAK,MAAI,KAAK,kBAAkB,GAAI,CAAAA,OAAG;AAAC,WAAK,gBAAgB,cAAc,CAAC;AAAE,iBAAUC,MAAKD,GAAE,MAAK,SAASC,GAAE,SAAS,QAAQ,CAAC,GAAE,KAAK,QAAQ,IAAI,CAACA,GAAE,GAAG,kBAAkB,CAAAD,OAAG,KAAK,iBAAiBA,EAAC,CAAE,GAAE,KAAK,gBAAgB,sBAAuB,MAAIC,GAAE,UAAW,CAAAD,OAAG,KAAK,mBAAmBA,EAAC,CAAE,CAAC,GAAE,CAAC;AAAA,IAAC,GAAG,CAAC;AAAE,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAK,EAAC,OAAMC,GAAC,IAAE,KAAK;AAAY,MAAED,EAAC,KAAG,SAAOA,GAAE,QAAM,gBAAcC,GAAE,QAAM,KAAK,WAAW,CAACD,GAAE,kBAAkB,GAAG,oBAAoB,CAAC,EAAC,SAAQA,GAAC,MAAI;AAAC,MAAAK,GAAEL,IAAEC,GAAE,aAAa,KAAG,KAAK,0BAA0B,4BAA4B;AAAA,IAAC,CAAE,GAAE,EAAG,MAAIA,GAAE,eAAgB,MAAI,KAAK,0BAA0B,4BAA4B,GAAG,CAAC,GAAEH,GAAG,MAAIG,IAAG,CAAC,SAAQ,eAAc,gBAAgB,GAAG,MAAI,KAAK,0BAA0B,sBAAsB,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBD,IAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,GAAC,IAAEF,IAAEI,KAAE,MAAM,EAAE,KAAK,YAAa,CAAAF,OAAG,KAAK,0BAA0BA,IAAEF,IAAEC,EAAC,CAAE,CAAC;AAAE,MAAEA,EAAC;AAAE,UAAME,KAAE,KAAK,qBAAoBG,KAAEF,GAAE,KAAK,EAAE,IAAK,CAAAJ,OAAG,EAAEA,IAAEG,EAAC,CAAE;AAAE,WAAO,EAAED,IAAEI,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,IAAI,sBAAqB;AAAC,WAAOC,GAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,0BAA0BP,IAAEC,IAAEC,IAAE;AAJp5I;AAIq5I,UAAME,KAAEI,GAAEP,MAAE,KAAAD,GAAE,KAAK,IAAI,MAAX,mBAAc,SAAM,IAAI,GAAEG,KAAE,MAAMH,GAAE,YAAY,wBAAwBI,IAAEF,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMI,KAAE,MAAM,KAAK,0BAA0B,gBAAgBH,GAAE,YAAWD,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMJ,KAAE,MAAM,KAAK,0BAA0B,MAAMQ,IAAEJ,EAAC;AAAE,MAAEA,EAAC;AAAE,UAAMK,KAAE,MAAIT,GAAE,SAAOQ,KAAE,CAAC,GAAGA,IAAE,GAAGR,EAAC;AAAE,WAAO,KAAK,yBAAyB,OAAOM,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,iBAAiBP,IAAE;AAAC,QAAG,KAAK,kBAAkB,EAAE,KAAM,CAAAC,OAAGA,GAAE,SAAS,SAASD,GAAE,OAAO,CAAE,EAAE,SAAOA,GAAE,UAAS;AAAA,MAAC,KAAI;AAAA,MAAW,KAAI;AAAU,aAAK,QAAQA,GAAE,OAAO,GAAE,KAAK,SAAS,CAACA,GAAE,OAAO,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,eAAUC,MAAKD,GAAE,QAAQ,MAAK,QAAQC,EAAC;AAAE,SAAK,SAASD,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAE,oBAAI;AAAI,eAAUE,MAAKJ,GAAE,GAAEI,GAAE,QAAQ,MAAI,KAAK,2BAA2BA,GAAE,SAAS,gBAAgB,KAAGH,GAAE,KAAKG,GAAE,SAAS,gBAAgB,GAAEF,GAAE,IAAIE,GAAE,KAAIA,EAAC,KAAG,KAAK,KAAKA,EAAC;AAAG,SAAK,kBAAkBH,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEC,IAAE;AAAC,QAAG,CAACD,GAAE,OAAO;AAAO,UAAMG,KAAE,EAAG,OAAMD,OAAG;AAAC,YAAM,GAAEF,GAAE,IAAK,CAAAA,QAAI,EAAC,QAAOA,IAAE,MAAK,KAAK,iBAAgB,EAAG,GAAE,EAAC,QAAOE,GAAC,CAAC,GAAE,KAAK,6BAA6BF,EAAC;AAAE,iBAAS,CAAC,EAACA,EAAC,KAAIC,GAAE,MAAK,KAAKD,EAAC;AAAA,IAAC,CAAE;AAAE,SAAK,gBAAgB,WAAWG,GAAE,OAAO;AAAE,UAAMG,KAAE,EAAC,MAAKH,IAAE,UAASF,GAAC,GAAEQ,KAAE,MAAI,EAAE,KAAK,cAAaH,EAAC;AAAE,IAAAH,GAAE,QAAQ,KAAKM,IAAEA,EAAC,GAAE,KAAK,aAAa,KAAKH,EAAC;AAAA,EAAC;AAAA,EAAC,6BAA6BN,IAAE;AAAC,eAAUC,MAAKD,GAAE,SAAMC,GAAE,QAAM,KAAK,aAAa,IAAIA,GAAE,IAAI,GAAE,QAAMA,GAAE,OAAK,KAAK,YAAY,IAAIA,GAAE,GAAG;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAE;AAAC,QAAG,EAAEA,GAAE,QAAQ,KAAG,CAACA,GAAE,QAAQ;AAAO,QAAIC,KAAED,GAAE;AAAS,QAAG,WAASC,GAAE,KAAK;AAAO,iBAAWA,GAAE,SAAOA,KAAEF,GAAE,WAAWE,EAAC;AAAG,UAAMC,KAAE,KAAK,cAAcD,GAAE,IAAI;AAAE,QAAG,EAAEC,EAAC,EAAE;AAAO,UAAME,KAAE,KAAK,wBAAwBJ,GAAE,KAAIC,EAAC;AAAE,MAAEG,EAAC,MAAIF,GAAE,aAAa,IAAIE,EAAC,GAAE,EAAEJ,GAAE,MAAM,KAAG,KAAK;AAAA,EAA+B;AAAA,EAAC,2BAA2BA,IAAE;AAAC,YAAO,QAAMA,GAAE,QAAM,CAAC,KAAK,aAAa,IAAIA,GAAE,IAAI,QAAM,QAAMA,GAAE,OAAK,CAAC,KAAK,YAAY,IAAIA,GAAE,GAAG,MAAI,CAAC,GAAEA,IAAE,KAAK,gBAAgB;AAAA,EAAE;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAE,EAAED,EAAC,GAAE,KAAK,gBAAgB;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,UAAME,KAAE,KAAK,oBAAoBF,EAAC,GAAEC,KAAE,GAAEC,IAAE,KAAK,OAAM,KAAE;AAAE,WAAO,IAAI,EAAED,IAAE,EAAC,CAAC,CAAC,GAAEH,GAAC,GAAE,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,QAAG,CAAC,KAAK,MAAM,QAAOA;AAAE,UAAMC,KAAE,CAAAD,OAAG;AAAC,aAAKA,GAAE,SAAO,IAAG,CAAAA,GAAE,KAAK,CAAC;AAAA,IAAC,GAAEE,KAAEF,GAAE,MAAM;AAAE,YAAOE,GAAE,OAAK,MAAGA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAQ,QAAAA,GAAE,IAAEA,GAAE,KAAG;AAAE;AAAA,MAAM,KAAI;AAAa,QAAAA,GAAE,OAAO,QAAQD,EAAC;AAAE;AAAA,MAAM,KAAI;AAAW,QAAAC,GAAE,MAAM,QAAS,CAAAF,OAAGA,GAAE,QAAQC,EAAC,CAAE;AAAE;AAAA,MAAM,KAAI;AAAU,QAAAC,GAAE,MAAM,QAAS,CAAAF,OAAGA,GAAE,QAAQC,EAAC,CAAE;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAE;AAAC,UAAMC,KAAE,KAAK,SAASD,EAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAOA;AAAE,UAAMC,KAAE,KAAK,cAAcF,EAAC;AAAE,WAAO,KAAK,SAASA,EAAC,IAAEE,IAAEA;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAE;AAAC,UAAMC,KAAE,EAAE,OAAOD,EAAC,GAAEE,KAAE,KAAK,OAAME,KAAE,IAAI,EAAE,EAAC,cAAaH,IAAE,MAAKC,IAAE,MAAK,MAAE,CAAC;AAAE,WAAM,EAAC,cAAaE,IAAE,aAAY,IAAI,GAAE,EAAC,cAAaA,IAAE,QAAO,CAAC,EAAC,MAAK,GAAE,MAAK,oBAAmB,OAAM,EAAC,CAAC,GAAE,cAAaH,IAAE,MAAK,OAAG,MAAKC,IAAE,eAAc,GAAE,kBAAiB,KAAK,kBAAiB,WAAU,EAAE,KAAK,IAAI,KAAG,SAAO,KAAK,KAAK,OAAK,KAAK,KAAK,mBAAmB,YAAU,KAAI,CAAC,GAAE,MAAKF,GAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,YAAa,CAAAC,OAAG,KAAK,kBAAkBA,IAAED,EAAC,CAAE;AAAE,eAAUC,MAAK,KAAK,aAAa,CAAAA,GAAE,SAAS,OAAOD,GAAE,GAAG,GAAE,MAAIC,GAAE,SAAS,QAAMA,GAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE;AAAI,IAAAD,GAAE,aAAa,IAAIE,EAAC,MAAIF,GAAE,aAAa,WAAWC,GAAE,GAAG,GAAE,EAAEA,GAAE,MAAM,KAAG,KAAK;AAAA,EAA+B;AAAA,EAAC,eAAeD,IAAE;AAAC,IAAAA,GAAE,YAAY,QAAQ,GAAE,KAAK,SAASA,GAAE,IAAI,IAAE;AAAA,EAAI;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,SAAQC,IAAE,UAASE,IAAE,YAAWD,GAAC,IAAE,KAAK,UAASM,KAAE,CAAC;AAAE,WAAO,EAAER,EAAC,KAAGQ,GAAE,KAAKT,GAAEC,EAAC,CAAC,GAAE,EAAEC,EAAC,KAAGO,GAAE,KAAKT,GAAEE,EAAC,CAAC,GAAE,EAAEE,EAAC,KAAGK,GAAE,KAAKT,GAAEI,EAAC,CAAC,GAAE,EAAED,EAAC,KAAGM,GAAE,KAAKT,GAAEG,EAAC,CAAC,GAAEM;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,4BAA2B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,uEAAuE,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["a", "v", "e", "t", "o", "r", "s", "y", "i", "p", "l", "n"]}