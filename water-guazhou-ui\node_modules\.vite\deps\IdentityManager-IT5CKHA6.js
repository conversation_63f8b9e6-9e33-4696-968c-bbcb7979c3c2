import {
  e as e3
} from "./chunk-LGZ4BCMJ.js";
import "./chunk-LVUK4QJX.js";
import "./chunk-JP4EK4VO.js";
import "./chunk-MI2Z635K.js";
import {
  W,
  e2
} from "./chunk-NQQSL2QK.js";
import {
  n2 as n3,
  v as v3
} from "./chunk-IZLLLMFE.js";
import "./chunk-K4QGLA2K.js";
import "./chunk-5XZZKPPL.js";
import "./chunk-PCLDCFRI.js";
import {
  s as s3
} from "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-FZ7BG3VX.js";
import {
  l as l3
} from "./chunk-QUHG7NMD.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import {
  n as n2
} from "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  n as n4,
  r as r2
} from "./chunk-WXFAAYJL.js";
import "./chunk-7SWS36OI.js";
import {
  $,
  A,
  Et,
  F,
  J,
  K,
  L,
  M,
  et,
  t as t2,
  v as v2
} from "./chunk-U4SVMKOQ.js";
import {
  l as l2
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  n
} from "./chunk-2CM7MIII.js";
import {
  D,
  p,
  r2 as r,
  v
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  l,
  t
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/identity/IdentityForm.js
var a2 = "esri-identity-form";
var l4 = { base: a2, group: `${a2}__group`, label: `${a2}__label`, footer: `${a2}__footer`, esriInput: "esri-input", esriButton: "esri-button", esriButtonSecondary: "esri-button--secondary" };
var p2 = "ArcGIS Online";
var d = class extends W {
  constructor(s7, e5) {
    super(s7, e5), this._usernameInputNode = null, this._passwordInputNode = null, this.signingIn = false, this.server = null, this.resource = null, this.error = null, this.oAuthPrompt = false;
  }
  render() {
    const { error: s7, server: e5, resource: t4, signingIn: o, oAuthPrompt: n5, messages: a3 } = this, d3 = n3("div", { class: l4.group }, s3(n5 ? a3.oAuthInfo : a3.info, { server: e5 && /\.arcgis\.com/i.test(e5) ? p2 : e5, resource: `(${t4 || a3.lblItem})` })), c2 = n5 ? null : n3("div", { class: l4.group, key: "username" }, n3("label", { class: l4.label }, a3.lblUser, n3("input", { value: "", required: true, autocomplete: "off", spellcheck: false, type: "text", bind: this, afterCreate: v3, "data-node-ref": "_usernameInputNode", class: l4.esriInput }))), m = n5 ? null : n3("div", { class: l4.group, key: "password" }, n3("label", { class: l4.label }, a3.lblPwd, n3("input", { value: "", required: true, type: "password", bind: this, afterCreate: v3, "data-node-ref": "_passwordInputNode", class: l4.esriInput }))), h2 = n3("div", { class: this.classes(l4.group, l4.footer) }, n3("input", { type: "submit", disabled: !!o, value: o ? a3.lblSigning : a3.lblOk, class: l4.esriButton }), n3("input", { type: "button", value: a3.lblCancel, bind: this, onclick: this._cancel, class: this.classes(l4.esriButton, l4.esriButtonSecondary) })), b2 = s7 ? n3("div", null, s7.details && s7.details.httpStatus ? a3.invalidUser : a3.noAuthService) : null;
    return n3("form", { class: l4.base, bind: this, onsubmit: this._submit }, d3, b2, c2, m, h2);
  }
  _cancel() {
    this._set("signingIn", false), this._usernameInputNode && (this._usernameInputNode.value = ""), this._passwordInputNode && (this._passwordInputNode.value = ""), this.emit("cancel");
  }
  _submit(s7) {
    s7.preventDefault(), this._set("signingIn", true);
    const e5 = this.oAuthPrompt ? {} : { username: this._usernameInputNode && this._usernameInputNode.value, password: this._passwordInputNode && this._passwordInputNode.value };
    this.emit("submit", e5);
  }
};
e([y(), e2("esri/identity/t9n/identity")], d.prototype, "messages", void 0), e([y()], d.prototype, "signingIn", void 0), e([y()], d.prototype, "server", void 0), e([y()], d.prototype, "resource", void 0), e([y()], d.prototype, "error", void 0), e([y()], d.prototype, "oAuthPrompt", void 0), d = e([a("esri.identity.IdentityForm")], d);
var c = d;

// node_modules/tabbable/dist/index.esm.js
var candidateSelectors = ["input:not([inert])", "select:not([inert])", "textarea:not([inert])", "a[href]:not([inert])", "button:not([inert])", "[tabindex]:not(slot):not([inert])", "audio[controls]:not([inert])", "video[controls]:not([inert])", '[contenteditable]:not([contenteditable="false"]):not([inert])', "details>summary:first-of-type:not([inert])", "details:not([inert])"];
var candidateSelector = candidateSelectors.join(",");
var NoElement = typeof Element === "undefined";
var matches = NoElement ? function() {
} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
var getRootNode = !NoElement && Element.prototype.getRootNode ? function(element) {
  var _element$getRootNode;
  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);
} : function(element) {
  return element === null || element === void 0 ? void 0 : element.ownerDocument;
};
var isInert = function isInert2(node, lookUp) {
  var _node$getAttribute;
  if (lookUp === void 0) {
    lookUp = true;
  }
  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, "inert");
  var inert = inertAtt === "" || inertAtt === "true";
  var result = inert || lookUp && node && isInert2(node.parentNode);
  return result;
};
var isContentEditable = function isContentEditable2(node) {
  var _node$getAttribute2;
  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, "contenteditable");
  return attValue === "" || attValue === "true";
};
var getCandidates = function getCandidates2(el, includeContainer, filter) {
  if (isInert(el)) {
    return [];
  }
  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));
  if (includeContainer && matches.call(el, candidateSelector)) {
    candidates.unshift(el);
  }
  candidates = candidates.filter(filter);
  return candidates;
};
var getCandidatesIteratively = function getCandidatesIteratively2(elements, includeContainer, options) {
  var candidates = [];
  var elementsToCheck = Array.from(elements);
  while (elementsToCheck.length) {
    var element = elementsToCheck.shift();
    if (isInert(element, false)) {
      continue;
    }
    if (element.tagName === "SLOT") {
      var assigned = element.assignedElements();
      var content = assigned.length ? assigned : element.children;
      var nestedCandidates = getCandidatesIteratively2(content, true, options);
      if (options.flatten) {
        candidates.push.apply(candidates, nestedCandidates);
      } else {
        candidates.push({
          scopeParent: element,
          candidates: nestedCandidates
        });
      }
    } else {
      var validCandidate = matches.call(element, candidateSelector);
      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {
        candidates.push(element);
      }
      var shadowRoot = element.shadowRoot || // check for an undisclosed shadow
      typeof options.getShadowRoot === "function" && options.getShadowRoot(element);
      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));
      if (shadowRoot && validShadowRoot) {
        var _nestedCandidates = getCandidatesIteratively2(shadowRoot === true ? element.children : shadowRoot.children, true, options);
        if (options.flatten) {
          candidates.push.apply(candidates, _nestedCandidates);
        } else {
          candidates.push({
            scopeParent: element,
            candidates: _nestedCandidates
          });
        }
      } else {
        elementsToCheck.unshift.apply(elementsToCheck, element.children);
      }
    }
  }
  return candidates;
};
var hasTabIndex = function hasTabIndex2(node) {
  return !isNaN(parseInt(node.getAttribute("tabindex"), 10));
};
var getTabIndex = function getTabIndex2(node) {
  if (!node) {
    throw new Error("No node provided");
  }
  if (node.tabIndex < 0) {
    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {
      return 0;
    }
  }
  return node.tabIndex;
};
var getSortOrderTabIndex = function getSortOrderTabIndex2(node, isScope) {
  var tabIndex = getTabIndex(node);
  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {
    return 0;
  }
  return tabIndex;
};
var sortOrderedTabbables = function sortOrderedTabbables2(a3, b2) {
  return a3.tabIndex === b2.tabIndex ? a3.documentOrder - b2.documentOrder : a3.tabIndex - b2.tabIndex;
};
var isInput = function isInput2(node) {
  return node.tagName === "INPUT";
};
var isHiddenInput = function isHiddenInput2(node) {
  return isInput(node) && node.type === "hidden";
};
var isDetailsWithSummary = function isDetailsWithSummary2(node) {
  var r4 = node.tagName === "DETAILS" && Array.prototype.slice.apply(node.children).some(function(child) {
    return child.tagName === "SUMMARY";
  });
  return r4;
};
var getCheckedRadio = function getCheckedRadio2(nodes, form) {
  for (var i3 = 0; i3 < nodes.length; i3++) {
    if (nodes[i3].checked && nodes[i3].form === form) {
      return nodes[i3];
    }
  }
};
var isTabbableRadio = function isTabbableRadio2(node) {
  if (!node.name) {
    return true;
  }
  var radioScope = node.form || getRootNode(node);
  var queryRadios = function queryRadios2(name) {
    return radioScope.querySelectorAll('input[type="radio"][name="' + name + '"]');
  };
  var radioSet;
  if (typeof window !== "undefined" && typeof window.CSS !== "undefined" && typeof window.CSS.escape === "function") {
    radioSet = queryRadios(window.CSS.escape(node.name));
  } else {
    try {
      radioSet = queryRadios(node.name);
    } catch (err) {
      console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s", err.message);
      return false;
    }
  }
  var checked = getCheckedRadio(radioSet, node.form);
  return !checked || checked === node;
};
var isRadio = function isRadio2(node) {
  return isInput(node) && node.type === "radio";
};
var isNonTabbableRadio = function isNonTabbableRadio2(node) {
  return isRadio(node) && !isTabbableRadio(node);
};
var isNodeAttached = function isNodeAttached2(node) {
  var _nodeRoot;
  var nodeRoot = node && getRootNode(node);
  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;
  var attached = false;
  if (nodeRoot && nodeRoot !== node) {
    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;
    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));
    while (!attached && nodeRootHost) {
      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;
      nodeRoot = getRootNode(nodeRootHost);
      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;
      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));
    }
  }
  return attached;
};
var isZeroArea = function isZeroArea2(node) {
  var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;
  return width === 0 && height === 0;
};
var isHidden = function isHidden2(node, _ref) {
  var displayCheck = _ref.displayCheck, getShadowRoot = _ref.getShadowRoot;
  if (getComputedStyle(node).visibility === "hidden") {
    return true;
  }
  var isDirectSummary = matches.call(node, "details>summary:first-of-type");
  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;
  if (matches.call(nodeUnderDetails, "details:not([open]) *")) {
    return true;
  }
  if (!displayCheck || displayCheck === "full" || displayCheck === "legacy-full") {
    if (typeof getShadowRoot === "function") {
      var originalNode = node;
      while (node) {
        var parentElement = node.parentElement;
        var rootNode = getRootNode(node);
        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true) {
          return isZeroArea(node);
        } else if (node.assignedSlot) {
          node = node.assignedSlot;
        } else if (!parentElement && rootNode !== node.ownerDocument) {
          node = rootNode.host;
        } else {
          node = parentElement;
        }
      }
      node = originalNode;
    }
    if (isNodeAttached(node)) {
      return !node.getClientRects().length;
    }
    if (displayCheck !== "legacy-full") {
      return true;
    }
  } else if (displayCheck === "non-zero-area") {
    return isZeroArea(node);
  }
  return false;
};
var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {
  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {
    var parentNode = node.parentElement;
    while (parentNode) {
      if (parentNode.tagName === "FIELDSET" && parentNode.disabled) {
        for (var i3 = 0; i3 < parentNode.children.length; i3++) {
          var child = parentNode.children.item(i3);
          if (child.tagName === "LEGEND") {
            return matches.call(parentNode, "fieldset[disabled] *") ? true : !child.contains(node);
          }
        }
        return true;
      }
      parentNode = parentNode.parentElement;
    }
  }
  return false;
};
var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {
  if (node.disabled || // we must do an inert look up to filter out any elements inside an inert ancestor
  //  because we're limited in the type of selectors we can use in JSDom (see related
  //  note related to `candidateSelectors`)
  isInert(node) || isHiddenInput(node) || isHidden(node, options) || // For a details element with a summary, the summary element gets the focus
  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {
    return false;
  }
  return true;
};
var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {
  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {
    return false;
  }
  return true;
};
var isValidShadowRootTabbable = function isValidShadowRootTabbable2(shadowHostNode) {
  var tabIndex = parseInt(shadowHostNode.getAttribute("tabindex"), 10);
  if (isNaN(tabIndex) || tabIndex >= 0) {
    return true;
  }
  return false;
};
var sortByOrder = function sortByOrder2(candidates) {
  var regularTabbables = [];
  var orderedTabbables = [];
  candidates.forEach(function(item, i3) {
    var isScope = !!item.scopeParent;
    var element = isScope ? item.scopeParent : item;
    var candidateTabindex = getSortOrderTabIndex(element, isScope);
    var elements = isScope ? sortByOrder2(item.candidates) : element;
    if (candidateTabindex === 0) {
      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);
    } else {
      orderedTabbables.push({
        documentOrder: i3,
        tabIndex: candidateTabindex,
        item,
        isScope,
        content: elements
      });
    }
  });
  return orderedTabbables.sort(sortOrderedTabbables).reduce(function(acc, sortable) {
    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);
    return acc;
  }, []).concat(regularTabbables);
};
var tabbable = function tabbable2(container, options) {
  options = options || {};
  var candidates;
  if (options.getShadowRoot) {
    candidates = getCandidatesIteratively([container], options.includeContainer, {
      filter: isNodeMatchingSelectorTabbable.bind(null, options),
      flatten: false,
      getShadowRoot: options.getShadowRoot,
      shadowRootFilter: isValidShadowRootTabbable
    });
  } else {
    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));
  }
  return sortByOrder(candidates);
};
var focusable = function focusable2(container, options) {
  options = options || {};
  var candidates;
  if (options.getShadowRoot) {
    candidates = getCandidatesIteratively([container], options.includeContainer, {
      filter: isNodeMatchingSelectorFocusable.bind(null, options),
      flatten: true,
      getShadowRoot: options.getShadowRoot
    });
  } else {
    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));
  }
  return candidates;
};
var isTabbable = function isTabbable2(node, options) {
  options = options || {};
  if (!node) {
    throw new Error("No node provided");
  }
  if (matches.call(node, candidateSelector) === false) {
    return false;
  }
  return isNodeMatchingSelectorTabbable(options, node);
};
var focusableCandidateSelector = candidateSelectors.concat("iframe").join(",");
var isFocusable = function isFocusable2(node, options) {
  options = options || {};
  if (!node) {
    throw new Error("No node provided");
  }
  if (matches.call(node, focusableCandidateSelector) === false) {
    return false;
  }
  return isNodeMatchingSelectorFocusable(options, node);
};

// node_modules/focus-trap/dist/focus-trap.esm.js
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    enumerableOnly && (symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    })), keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread2(target) {
  for (var i3 = 1; i3 < arguments.length; i3++) {
    var source = null != arguments[i3] ? arguments[i3] : {};
    i3 % 2 ? ownKeys(Object(source), true).forEach(function(key) {
      _defineProperty(target, key, source[key]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {
      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
function _toPrimitive(input, hint) {
  if (typeof input !== "object" || input === null) return input;
  var prim = input[Symbol.toPrimitive];
  if (prim !== void 0) {
    var res = prim.call(input, hint || "default");
    if (typeof res !== "object") return res;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (hint === "string" ? String : Number)(input);
}
function _toPropertyKey(arg) {
  var key = _toPrimitive(arg, "string");
  return typeof key === "symbol" ? key : String(key);
}
var activeFocusTraps = {
  activateTrap: function activateTrap(trapStack, trap) {
    if (trapStack.length > 0) {
      var activeTrap = trapStack[trapStack.length - 1];
      if (activeTrap !== trap) {
        activeTrap.pause();
      }
    }
    var trapIndex = trapStack.indexOf(trap);
    if (trapIndex === -1) {
      trapStack.push(trap);
    } else {
      trapStack.splice(trapIndex, 1);
      trapStack.push(trap);
    }
  },
  deactivateTrap: function deactivateTrap(trapStack, trap) {
    var trapIndex = trapStack.indexOf(trap);
    if (trapIndex !== -1) {
      trapStack.splice(trapIndex, 1);
    }
    if (trapStack.length > 0) {
      trapStack[trapStack.length - 1].unpause();
    }
  }
};
var isSelectableInput = function isSelectableInput2(node) {
  return node.tagName && node.tagName.toLowerCase() === "input" && typeof node.select === "function";
};
var isEscapeEvent = function isEscapeEvent2(e5) {
  return e5.key === "Escape" || e5.key === "Esc" || e5.keyCode === 27;
};
var isTabEvent = function isTabEvent2(e5) {
  return e5.key === "Tab" || e5.keyCode === 9;
};
var isKeyForward = function isKeyForward2(e5) {
  return isTabEvent(e5) && !e5.shiftKey;
};
var isKeyBackward = function isKeyBackward2(e5) {
  return isTabEvent(e5) && e5.shiftKey;
};
var delay = function delay2(fn) {
  return setTimeout(fn, 0);
};
var findIndex = function findIndex2(arr, fn) {
  var idx = -1;
  arr.every(function(value, i3) {
    if (fn(value)) {
      idx = i3;
      return false;
    }
    return true;
  });
  return idx;
};
var valueOrHandler = function valueOrHandler2(value) {
  for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    params[_key - 1] = arguments[_key];
  }
  return typeof value === "function" ? value.apply(void 0, params) : value;
};
var getActualTarget = function getActualTarget2(event) {
  return event.target.shadowRoot && typeof event.composedPath === "function" ? event.composedPath()[0] : event.target;
};
var internalTrapStack = [];
var createFocusTrap = function createFocusTrap2(elements, userOptions) {
  var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;
  var trapStack = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.trapStack) || internalTrapStack;
  var config = _objectSpread2({
    returnFocusOnDeactivate: true,
    escapeDeactivates: true,
    delayInitialFocus: true,
    isKeyForward,
    isKeyBackward
  }, userOptions);
  var state = {
    // containers given to createFocusTrap()
    // @type {Array<HTMLElement>}
    containers: [],
    // list of objects identifying tabbable nodes in `containers` in the trap
    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap
    //  is active, but the trap should never get to a state where there isn't at least one group
    //  with at least one tabbable node in it (that would lead to an error condition that would
    //  result in an error being thrown)
    // @type {Array<{
    //   container: HTMLElement,
    //   tabbableNodes: Array<HTMLElement>, // empty if none
    //   focusableNodes: Array<HTMLElement>, // empty if none
    //   firstTabbableNode: HTMLElement|null,
    //   lastTabbableNode: HTMLElement|null,
    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined
    // }>}
    containerGroups: [],
    // same order/length as `containers` list
    // references to objects in `containerGroups`, but only those that actually have
    //  tabbable nodes in them
    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__
    //  the same length
    tabbableGroups: [],
    nodeFocusedBeforeActivation: null,
    mostRecentlyFocusedNode: null,
    active: false,
    paused: false,
    // timer ID for when delayInitialFocus is true and initial focus in this trap
    //  has been delayed during activation
    delayInitialFocusTimer: void 0
  };
  var trap;
  var getOption = function getOption2(configOverrideOptions, optionName, configOptionName) {
    return configOverrideOptions && configOverrideOptions[optionName] !== void 0 ? configOverrideOptions[optionName] : config[configOptionName || optionName];
  };
  var findContainerIndex = function findContainerIndex2(element) {
    return state.containerGroups.findIndex(function(_ref) {
      var container = _ref.container, tabbableNodes = _ref.tabbableNodes;
      return container.contains(element) || // fall back to explicit tabbable search which will take into consideration any
      //  web components if the `tabbableOptions.getShadowRoot` option was used for
      //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't
      //  look inside web components even if open)
      tabbableNodes.find(function(node) {
        return node === element;
      });
    });
  };
  var getNodeForOption = function getNodeForOption2(optionName) {
    var optionValue = config[optionName];
    if (typeof optionValue === "function") {
      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
        params[_key2 - 1] = arguments[_key2];
      }
      optionValue = optionValue.apply(void 0, params);
    }
    if (optionValue === true) {
      optionValue = void 0;
    }
    if (!optionValue) {
      if (optionValue === void 0 || optionValue === false) {
        return optionValue;
      }
      throw new Error("`".concat(optionName, "` was specified but was not a node, or did not return a node"));
    }
    var node = optionValue;
    if (typeof optionValue === "string") {
      node = doc.querySelector(optionValue);
      if (!node) {
        throw new Error("`".concat(optionName, "` as selector refers to no known node"));
      }
    }
    return node;
  };
  var getInitialFocusNode = function getInitialFocusNode2() {
    var node = getNodeForOption("initialFocus");
    if (node === false) {
      return false;
    }
    if (node === void 0) {
      if (findContainerIndex(doc.activeElement) >= 0) {
        node = doc.activeElement;
      } else {
        var firstTabbableGroup = state.tabbableGroups[0];
        var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;
        node = firstTabbableNode || getNodeForOption("fallbackFocus");
      }
    }
    if (!node) {
      throw new Error("Your focus-trap needs to have at least one focusable element");
    }
    return node;
  };
  var updateTabbableNodes = function updateTabbableNodes2() {
    state.containerGroups = state.containers.map(function(container) {
      var tabbableNodes = tabbable(container, config.tabbableOptions);
      var focusableNodes = focusable(container, config.tabbableOptions);
      return {
        container,
        tabbableNodes,
        focusableNodes,
        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,
        lastTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[tabbableNodes.length - 1] : null,
        /**
         * Finds the __tabbable__ node that follows the given node in the specified direction,
         *  in this container, if any.
         * @param {HTMLElement} node
         * @param {boolean} [forward] True if going in forward tab order; false if going
         *  in reverse.
         * @returns {HTMLElement|undefined} The next tabbable node, if any.
         */
        nextTabbableNode: function nextTabbableNode(node) {
          var forward = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
          var nodeIdx = focusableNodes.findIndex(function(n5) {
            return n5 === node;
          });
          if (nodeIdx < 0) {
            return void 0;
          }
          if (forward) {
            return focusableNodes.slice(nodeIdx + 1).find(function(n5) {
              return isTabbable(n5, config.tabbableOptions);
            });
          }
          return focusableNodes.slice(0, nodeIdx).reverse().find(function(n5) {
            return isTabbable(n5, config.tabbableOptions);
          });
        }
      };
    });
    state.tabbableGroups = state.containerGroups.filter(function(group) {
      return group.tabbableNodes.length > 0;
    });
    if (state.tabbableGroups.length <= 0 && !getNodeForOption("fallbackFocus")) {
      throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");
    }
  };
  var tryFocus = function tryFocus2(node) {
    if (node === false) {
      return;
    }
    if (node === doc.activeElement) {
      return;
    }
    if (!node || !node.focus) {
      tryFocus2(getInitialFocusNode());
      return;
    }
    node.focus({
      preventScroll: !!config.preventScroll
    });
    state.mostRecentlyFocusedNode = node;
    if (isSelectableInput(node)) {
      node.select();
    }
  };
  var getReturnFocusNode = function getReturnFocusNode2(previousActiveElement) {
    var node = getNodeForOption("setReturnFocus", previousActiveElement);
    return node ? node : node === false ? false : previousActiveElement;
  };
  var checkPointerDown = function checkPointerDown2(e5) {
    var target = getActualTarget(e5);
    if (findContainerIndex(target) >= 0) {
      return;
    }
    if (valueOrHandler(config.clickOutsideDeactivates, e5)) {
      trap.deactivate({
        // if, on deactivation, we should return focus to the node originally-focused
        //  when the trap was activated (or the configured `setReturnFocus` node),
        //  then assume it's also OK to return focus to the outside node that was
        //  just clicked, causing deactivation, as long as that node is focusable;
        //  if it isn't focusable, then return focus to the original node focused
        //  on activation (or the configured `setReturnFocus` node)
        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,
        //  which will result in the outside click setting focus to the node
        //  that was clicked, whether it's focusable or not; by setting
        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused
        //  on activation (or the configured `setReturnFocus` node)
        returnFocus: config.returnFocusOnDeactivate && !isFocusable(target, config.tabbableOptions)
      });
      return;
    }
    if (valueOrHandler(config.allowOutsideClick, e5)) {
      return;
    }
    e5.preventDefault();
  };
  var checkFocusIn = function checkFocusIn2(e5) {
    var target = getActualTarget(e5);
    var targetContained = findContainerIndex(target) >= 0;
    if (targetContained || target instanceof Document) {
      if (targetContained) {
        state.mostRecentlyFocusedNode = target;
      }
    } else {
      e5.stopImmediatePropagation();
      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());
    }
  };
  var checkKeyNav = function checkKeyNav2(event) {
    var isBackward = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    var target = getActualTarget(event);
    updateTabbableNodes();
    var destinationNode = null;
    if (state.tabbableGroups.length > 0) {
      var containerIndex = findContainerIndex(target);
      var containerGroup = containerIndex >= 0 ? state.containerGroups[containerIndex] : void 0;
      if (containerIndex < 0) {
        if (isBackward) {
          destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;
        } else {
          destinationNode = state.tabbableGroups[0].firstTabbableNode;
        }
      } else if (isBackward) {
        var startOfGroupIndex = findIndex(state.tabbableGroups, function(_ref2) {
          var firstTabbableNode = _ref2.firstTabbableNode;
          return target === firstTabbableNode;
        });
        if (startOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target, false))) {
          startOfGroupIndex = containerIndex;
        }
        if (startOfGroupIndex >= 0) {
          var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;
          var destinationGroup = state.tabbableGroups[destinationGroupIndex];
          destinationNode = destinationGroup.lastTabbableNode;
        } else if (!isTabEvent(event)) {
          destinationNode = containerGroup.nextTabbableNode(target, false);
        }
      } else {
        var lastOfGroupIndex = findIndex(state.tabbableGroups, function(_ref3) {
          var lastTabbableNode = _ref3.lastTabbableNode;
          return target === lastTabbableNode;
        });
        if (lastOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target))) {
          lastOfGroupIndex = containerIndex;
        }
        if (lastOfGroupIndex >= 0) {
          var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;
          var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];
          destinationNode = _destinationGroup.firstTabbableNode;
        } else if (!isTabEvent(event)) {
          destinationNode = containerGroup.nextTabbableNode(target);
        }
      }
    } else {
      destinationNode = getNodeForOption("fallbackFocus");
    }
    if (destinationNode) {
      if (isTabEvent(event)) {
        event.preventDefault();
      }
      tryFocus(destinationNode);
    }
  };
  var checkKey = function checkKey2(event) {
    if (isEscapeEvent(event) && valueOrHandler(config.escapeDeactivates, event) !== false) {
      event.preventDefault();
      trap.deactivate();
      return;
    }
    if (config.isKeyForward(event) || config.isKeyBackward(event)) {
      checkKeyNav(event, config.isKeyBackward(event));
    }
  };
  var checkClick = function checkClick2(e5) {
    var target = getActualTarget(e5);
    if (findContainerIndex(target) >= 0) {
      return;
    }
    if (valueOrHandler(config.clickOutsideDeactivates, e5)) {
      return;
    }
    if (valueOrHandler(config.allowOutsideClick, e5)) {
      return;
    }
    e5.preventDefault();
    e5.stopImmediatePropagation();
  };
  var addListeners = function addListeners2() {
    if (!state.active) {
      return;
    }
    activeFocusTraps.activateTrap(trapStack, trap);
    state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function() {
      tryFocus(getInitialFocusNode());
    }) : tryFocus(getInitialFocusNode());
    doc.addEventListener("focusin", checkFocusIn, true);
    doc.addEventListener("mousedown", checkPointerDown, {
      capture: true,
      passive: false
    });
    doc.addEventListener("touchstart", checkPointerDown, {
      capture: true,
      passive: false
    });
    doc.addEventListener("click", checkClick, {
      capture: true,
      passive: false
    });
    doc.addEventListener("keydown", checkKey, {
      capture: true,
      passive: false
    });
    return trap;
  };
  var removeListeners = function removeListeners2() {
    if (!state.active) {
      return;
    }
    doc.removeEventListener("focusin", checkFocusIn, true);
    doc.removeEventListener("mousedown", checkPointerDown, true);
    doc.removeEventListener("touchstart", checkPointerDown, true);
    doc.removeEventListener("click", checkClick, true);
    doc.removeEventListener("keydown", checkKey, true);
    return trap;
  };
  trap = {
    get active() {
      return state.active;
    },
    get paused() {
      return state.paused;
    },
    activate: function activate(activateOptions) {
      if (state.active) {
        return this;
      }
      var onActivate = getOption(activateOptions, "onActivate");
      var onPostActivate = getOption(activateOptions, "onPostActivate");
      var checkCanFocusTrap = getOption(activateOptions, "checkCanFocusTrap");
      if (!checkCanFocusTrap) {
        updateTabbableNodes();
      }
      state.active = true;
      state.paused = false;
      state.nodeFocusedBeforeActivation = doc.activeElement;
      if (onActivate) {
        onActivate();
      }
      var finishActivation = function finishActivation2() {
        if (checkCanFocusTrap) {
          updateTabbableNodes();
        }
        addListeners();
        if (onPostActivate) {
          onPostActivate();
        }
      };
      if (checkCanFocusTrap) {
        checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);
        return this;
      }
      finishActivation();
      return this;
    },
    deactivate: function deactivate(deactivateOptions) {
      if (!state.active) {
        return this;
      }
      var options = _objectSpread2({
        onDeactivate: config.onDeactivate,
        onPostDeactivate: config.onPostDeactivate,
        checkCanReturnFocus: config.checkCanReturnFocus
      }, deactivateOptions);
      clearTimeout(state.delayInitialFocusTimer);
      state.delayInitialFocusTimer = void 0;
      removeListeners();
      state.active = false;
      state.paused = false;
      activeFocusTraps.deactivateTrap(trapStack, trap);
      var onDeactivate = getOption(options, "onDeactivate");
      var onPostDeactivate = getOption(options, "onPostDeactivate");
      var checkCanReturnFocus = getOption(options, "checkCanReturnFocus");
      var returnFocus = getOption(options, "returnFocus", "returnFocusOnDeactivate");
      if (onDeactivate) {
        onDeactivate();
      }
      var finishDeactivation = function finishDeactivation2() {
        delay(function() {
          if (returnFocus) {
            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));
          }
          if (onPostDeactivate) {
            onPostDeactivate();
          }
        });
      };
      if (returnFocus && checkCanReturnFocus) {
        checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);
        return this;
      }
      finishDeactivation();
      return this;
    },
    pause: function pause() {
      if (state.paused || !state.active) {
        return this;
      }
      state.paused = true;
      removeListeners();
      return this;
    },
    unpause: function unpause() {
      if (!state.paused || !state.active) {
        return this;
      }
      state.paused = false;
      updateTabbableNodes();
      addListeners();
      return this;
    },
    updateContainerElements: function updateContainerElements(containerElements) {
      var elementsAsArray = [].concat(containerElements).filter(Boolean);
      state.containers = elementsAsArray.map(function(element) {
        return typeof element === "string" ? doc.querySelector(element) : element;
      });
      if (state.active) {
        updateTabbableNodes();
      }
      return this;
    }
  };
  trap.updateContainerElements(elements);
  return trap;
};

// node_modules/@arcgis/core/identity/IdentityModal.js
var d2 = "esri-identity-modal";
var p3 = { base: d2, open: `${d2}--open`, closed: `${d2}--closed`, title: `${d2}__title`, dialog: `${d2}__dialog`, content: `${d2}__content`, closeButton: `${d2}__close-button`, iconClose: "esri-icon-close" };
var u = class extends W {
  constructor(t4, e5) {
    super(t4, e5), this.container = document.createElement("div"), this.content = null, this.open = false, this._focusTrap = null, this._close = () => {
      this.open = false;
    }, document.body.appendChild(this.container), this.addHandles(l3(() => this.open, () => this._toggleFocusTrap()));
  }
  destroy() {
    this._destroyFocusTrap();
  }
  get title() {
    var _a;
    return (_a = this.messages) == null ? void 0 : _a.auth.signIn;
  }
  render() {
    const t4 = this.id, { open: e5, content: o, title: s7, messages: i3 } = this, r4 = e5 && !!o, n5 = { [p3.open]: r4, [p3.closed]: !r4 }, a3 = n3("button", { class: p3.closeButton, "aria-label": i3.close, title: i3.close, bind: this, onclick: this._close, type: "button" }, n3("span", { "aria-hidden": "true", class: p3.iconClose })), l5 = `${t4}_title`, d3 = `${t4}_content`, u2 = s7 ? n3("h1", { id: l5, class: p3.title }, s7) : null, h2 = r4 ? n3("div", { bind: this, class: p3.dialog, role: "dialog", "aria-labelledby": l5, "aria-describedby": d3, afterCreate: this._createFocusTrap }, a3, u2, this._renderContent(d3)) : null;
    return n3("div", { tabIndex: -1, class: this.classes(p3.base, n5) }, h2);
  }
  _destroyFocusTrap() {
    var _a;
    (_a = this._focusTrap) == null ? void 0 : _a.deactivate({ onDeactivate: () => {
    } }), this._focusTrap = null;
  }
  _toggleFocusTrap() {
    const { _focusTrap: t4, open: e5 } = this;
    t4 && (e5 ? t4.activate() : t4.deactivate());
  }
  _createFocusTrap(t4) {
    this._destroyFocusTrap();
    const o = requestAnimationFrame(() => {
      this._focusTrap = createFocusTrap(t4, { initialFocus: "input", onDeactivate: this._close }), this._toggleFocusTrap();
    });
    this.addHandles(n(() => cancelAnimationFrame(o)));
  }
  _renderContent(t4) {
    const e5 = this.content;
    return "string" == typeof e5 ? n3("div", { class: p3.content, id: t4, innerHTML: e5 }) : e3(e5) ? n3("div", { class: p3.content, id: t4 }, e5.render()) : e5 instanceof HTMLElement ? n3("div", { class: p3.content, id: t4, bind: e5, afterCreate: this._attachToNode }) : null;
  }
  _attachToNode(t4) {
    const e5 = this;
    t4.appendChild(e5);
  }
};
e([y({ readOnly: true })], u.prototype, "container", void 0), e([y()], u.prototype, "content", void 0), e([y()], u.prototype, "open", void 0), e([y(), e2("esri/t9n/common")], u.prototype, "messages", void 0), e([y()], u.prototype, "title", null), u = e([a("esri.identity.IdentityModal")], u);
var h = u;

// node_modules/@arcgis/core/identity/OAuthCredential.js
var t3 = "esriJSAPIOAuth";
var e4 = class {
  constructor(t4, e5) {
    this.oAuthInfo = null, this.storage = null, this.appId = null, this.codeVerifier = null, this.expires = null, this.refreshToken = null, this.ssl = null, this.stateUID = null, this.token = null, this.userId = null, this.oAuthInfo = t4, this.storage = e5, this._init();
  }
  isValid() {
    let t4 = false;
    if (this.oAuthInfo && this.userId && (this.refreshToken || this.token)) {
      if (null == this.expires && this.refreshToken) t4 = true;
      else if (this.expires) {
        const e5 = Date.now();
        if (this.expires > e5) {
          (this.expires - e5) / 1e3 > 60 * this.oAuthInfo.minTimeUntilExpiration && (t4 = true);
        }
      }
    }
    return t4;
  }
  save() {
    if (!this.storage) return false;
    const e5 = this._load(), s7 = this.oAuthInfo;
    if (s7 && s7.authNamespace && s7.portalUrl) {
      let r4 = e5[s7.authNamespace];
      r4 || (r4 = e5[s7.authNamespace] = {}), this.appId || (this.appId = s7.appId), r4[s7.portalUrl] = { appId: this.appId, codeVerifier: this.codeVerifier, expires: this.expires, refreshToken: this.refreshToken, ssl: this.ssl, stateUID: this.stateUID, token: this.token, userId: this.userId };
      try {
        this.storage.setItem(t3, JSON.stringify(e5));
      } catch (i3) {
        return console.warn(i3), false;
      }
      return true;
    }
    return false;
  }
  destroy() {
    const e5 = this._load(), s7 = this.oAuthInfo;
    if (s7 && s7.appId && s7.portalUrl && (null == this.expires || this.expires > Date.now()) && (this.refreshToken || this.token)) {
      const t4 = s7.portalUrl.replace(/^http:/i, "https:") + "/sharing/rest/oauth2/revokeToken", e6 = new FormData();
      if (e6.append("f", "json"), e6.append("auth_token", this.refreshToken || this.token), e6.append("client_id", s7.appId), e6.append("token_type_hint", this.refreshToken ? "refresh_token" : "access_token"), "function" == typeof navigator.sendBeacon) navigator.sendBeacon(t4, e6);
      else {
        const s8 = new XMLHttpRequest();
        s8.open("POST", t4), s8.send(e6);
      }
    }
    if (s7 && s7.authNamespace && s7.portalUrl && this.storage) {
      const r4 = e5[s7.authNamespace];
      if (r4) {
        delete r4[s7.portalUrl];
        try {
          this.storage.setItem(t3, JSON.stringify(e5));
        } catch (i3) {
          console.log(i3);
        }
      }
    }
    s7 && (s7._oAuthCred = null, this.oAuthInfo = null);
  }
  _init() {
    const t4 = this._load(), e5 = this.oAuthInfo;
    if (e5 && e5.authNamespace && e5.portalUrl) {
      let s7 = t4[e5.authNamespace];
      s7 && (s7 = s7[e5.portalUrl], s7 && (this.appId = s7.appId, this.codeVerifier = s7.codeVerifier, this.expires = s7.expires, this.refreshToken = s7.refreshToken, this.ssl = s7.ssl, this.stateUID = s7.stateUID, this.token = s7.token, this.userId = s7.userId));
    }
  }
  _load() {
    let e5 = {};
    if (this.storage) {
      const i3 = this.storage.getItem(t3);
      if (i3) try {
        e5 = JSON.parse(i3);
      } catch (s7) {
        console.warn(s7);
      }
    }
    return e5;
  }
};
e4.prototype.declaredClass = "esri.identity.OAuthCredential";

// node_modules/@arcgis/core/identity/OAuthInfo.js
var p4;
var s4 = p4 = class extends l2 {
  constructor(o) {
    super(o), this._oAuthCred = null, this.appId = null, this.authNamespace = "/", this.expiration = 20160, this.flowType = "auto", this.forceLogin = false, this.forceUserId = false, this.locale = null, this.minTimeUntilExpiration = 30, this.popup = false, this.popupCallbackUrl = "oauth-callback.html", this.popupWindowFeatures = "height=490,width=800,resizable,scrollbars,status", this.portalUrl = "https://www.arcgis.com", this.preserveUrlHash = false, this.userId = null;
  }
  clone() {
    return p4.fromJSON(this.toJSON());
  }
};
e([y({ json: { write: true } })], s4.prototype, "appId", void 0), e([y({ json: { write: true } })], s4.prototype, "authNamespace", void 0), e([y({ json: { write: true } })], s4.prototype, "expiration", void 0), e([y({ json: { write: true } })], s4.prototype, "flowType", void 0), e([y({ json: { write: true } })], s4.prototype, "forceLogin", void 0), e([y({ json: { write: true } })], s4.prototype, "forceUserId", void 0), e([y({ json: { write: true } })], s4.prototype, "locale", void 0), e([y({ json: { write: true } })], s4.prototype, "minTimeUntilExpiration", void 0), e([y({ json: { write: true } })], s4.prototype, "popup", void 0), e([y({ json: { write: true } })], s4.prototype, "popupCallbackUrl", void 0), e([y({ json: { write: true } })], s4.prototype, "popupWindowFeatures", void 0), e([y({ json: { write: true } })], s4.prototype, "portalUrl", void 0), e([y({ json: { write: true } })], s4.prototype, "preserveUrlHash", void 0), e([y({ json: { write: true } })], s4.prototype, "userId", void 0), s4 = p4 = e([a("esri.identity.OAuthInfo")], s4);
var i = s4;

// node_modules/@arcgis/core/identity/ServerInfo.js
var s5 = class extends l2 {
  constructor(o) {
    super(o), this.adminTokenServiceUrl = null, this.currentVersion = null, this.hasPortal = null, this.hasServer = null, this.owningSystemUrl = null, this.owningTenant = null, this.server = null, this.shortLivedTokenValidity = null, this.tokenServiceUrl = null, this.webTierAuth = null;
  }
};
e([y({ json: { write: true } })], s5.prototype, "adminTokenServiceUrl", void 0), e([y({ json: { write: true } })], s5.prototype, "currentVersion", void 0), e([y({ json: { write: true } })], s5.prototype, "hasPortal", void 0), e([y({ json: { write: true } })], s5.prototype, "hasServer", void 0), e([y({ json: { write: true } })], s5.prototype, "owningSystemUrl", void 0), e([y({ json: { write: true } })], s5.prototype, "owningTenant", void 0), e([y({ json: { write: true } })], s5.prototype, "server", void 0), e([y({ json: { write: true } })], s5.prototype, "shortLivedTokenValidity", void 0), e([y({ json: { write: true } })], s5.prototype, "tokenServiceUrl", void 0), e([y({ json: { write: true } })], s5.prototype, "webTierAuth", void 0), s5 = e([a("esri.identity.ServerInfo")], s5);
var i2 = s5;

// node_modules/@arcgis/core/identity/IdentityManagerBase.js
var C = {};
var b = (e5) => {
  const t4 = new $(e5.owningSystemUrl).host, r4 = new $(e5.server).host, s7 = /.+\.arcgis\.com$/i;
  return s7.test(t4) && s7.test(r4);
};
var D2 = (e5, t4) => !!(b(e5) && t4 && t4.some((t5) => t5.test(e5.server)));
var q = null;
var j = null;
try {
  q = window.localStorage, j = window.sessionStorage;
} catch {
}
var E = class extends n2 {
  constructor() {
    super(), this._portalConfig = globalThis.esriGeowConfig, this.serverInfos = [], this.oAuthInfos = [], this.credentials = [], this._soReqs = [], this._xoReqs = [], this._portals = [], this._defaultOAuthInfo = null, this._defaultTokenValidity = 60, this.dialog = null, this.formConstructor = c, this.tokenValidity = null, this.normalizeWebTierAuth = false, this._appOrigin = "null" !== window.origin ? window.origin : window.location.origin, this._appUrlObj = L(window.location.href), this._busy = null, this._rejectOnPersistedPageShow = false, this._oAuthLocationParams = null, this._gwTokenUrl = "/sharing/rest/generateToken", this._agsRest = "/rest/services", this._agsPortal = /\/sharing(\/|$)/i, this._agsAdmin = /(https?:\/\/[^\/]+\/[^\/]+)\/admin\/?(\/.*)?$/i, this._adminSvcs = /\/rest\/admin\/services(\/|$)/i, this._gwDomains = [{ regex: /^https?:\/\/www\.arcgis\.com/i, customBaseUrl: "maps.arcgis.com", tokenServiceUrl: "https://www.arcgis.com/sharing/rest/generateToken" }, { regex: /^https?:\/\/(?:dev|[a-z\d-]+\.mapsdev)\.arcgis\.com/i, customBaseUrl: "mapsdev.arcgis.com", tokenServiceUrl: "https://dev.arcgis.com/sharing/rest/generateToken" }, { regex: /^https?:\/\/(?:devext|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i, customBaseUrl: "mapsdevext.arcgis.com", tokenServiceUrl: "https://devext.arcgis.com/sharing/rest/generateToken" }, { regex: /^https?:\/\/(?:qaext|[a-z\d-]+\.mapsqa)\.arcgis\.com/i, customBaseUrl: "mapsqa.arcgis.com", tokenServiceUrl: "https://qaext.arcgis.com/sharing/rest/generateToken" }, { regex: /^https?:\/\/[a-z\d-]+\.maps\.arcgis\.com/i, customBaseUrl: "maps.arcgis.com", tokenServiceUrl: "https://www.arcgis.com/sharing/rest/generateToken" }], this._legacyFed = [], this._regexSDirUrl = /http.+\/rest\/services\/?/gi, this._regexServerType = /(\/(FeatureServer|GPServer|GeoDataServer|GeocodeServer|GeoenrichmentServer|GeometryServer|GlobeServer|ImageServer|KnowledgeGraphServer|MapServer|MissionServer|MobileServer|NAServer|NetworkDiagramServer|OGCFeatureServer|ParcelFabricServer|RelationalCatalogServer|SceneServer|StreamServer|UtilityNetworkServer|ValidationServer|VectorTileServer|VersionManagementServer|VideoServer)).*/gi, this._gwUser = /http.+\/users\/([^\/]+)\/?.*/i, this._gwItem = /http.+\/items\/([^\/]+)\/?.*/i, this._gwGroup = /http.+\/groups\/([^\/]+)\/?.*/i, this._rePortalTokenSvc = /\/sharing(\/rest)?\/generatetoken/i, this._createDefaultOAuthInfo = true, this._hasTestedIfAppIsOnPortal = false, this._getOAuthLocationParams(), window.addEventListener("pageshow", (e5) => {
      this._pageShowHandler(e5);
    });
  }
  registerServers(e5) {
    const t4 = this.serverInfos;
    t4 ? (e5 = e5.filter((e6) => !this.findServerInfo(e6.server)), this.serverInfos = t4.concat(e5)) : this.serverInfos = e5, e5.forEach((e6) => {
      e6.owningSystemUrl && this._portals.push(e6.owningSystemUrl), e6.hasPortal && this._portals.push(e6.server);
    });
  }
  registerOAuthInfos(e5) {
    const t4 = this.oAuthInfos;
    if (t4) {
      for (const r4 of e5) {
        const e6 = this.findOAuthInfo(r4.portalUrl);
        e6 && t4.splice(t4.indexOf(e6), 1);
      }
      this.oAuthInfos = t4.concat(e5);
    } else this.oAuthInfos = e5;
  }
  registerToken(e5) {
    e5 = { ...e5 };
    const t4 = this._sanitizeUrl(e5.server), r4 = this._isServerRsrc(t4);
    let s7, i3 = this.findServerInfo(t4), o = true;
    i3 || (i3 = new i2(), i3.server = this._getServerInstanceRoot(t4), r4 ? i3.hasServer = true : (i3.tokenServiceUrl = this._getTokenSvcUrl(t4), i3.hasPortal = true), this.registerServers([i3])), s7 = this._findCredential(t4), s7 ? (delete e5.server, Object.assign(s7, e5), o = false) : (s7 = new L2({ userId: e5.userId, server: i3.server, token: e5.token, expires: e5.expires, ssl: e5.ssl, scope: r4 ? "server" : "portal" }), s7.resources = [t4], this.credentials.push(s7)), s7.emitTokenChange(false), o || s7.refreshServerTokens();
  }
  toJSON() {
    return l({ serverInfos: this.serverInfos.map((e5) => e5.toJSON()), oAuthInfos: this.oAuthInfos.map((e5) => e5.toJSON()), credentials: this.credentials.map((e5) => e5.toJSON()) });
  }
  initialize(e5) {
    if (!e5) return;
    "string" == typeof e5 && (e5 = JSON.parse(e5));
    const t4 = e5.serverInfos, r4 = e5.oAuthInfos, s7 = e5.credentials;
    if (t4) {
      const e6 = [];
      t4.forEach((t5) => {
        t5.server && t5.tokenServiceUrl && e6.push(t5.declaredClass ? t5 : new i2(t5));
      }), e6.length && this.registerServers(e6);
    }
    if (r4) {
      const e6 = [];
      r4.forEach((t5) => {
        t5.appId && e6.push(t5.declaredClass ? t5 : new i(t5));
      }), e6.length && this.registerOAuthInfos(e6);
    }
    s7 && s7.forEach((e6) => {
      e6.server && e6.token && e6.expires && e6.expires > Date.now() && ((e6 = e6.declaredClass ? e6 : new L2(e6)).emitTokenChange(), this.credentials.push(e6));
    });
  }
  findServerInfo(e5) {
    let t4;
    e5 = this._sanitizeUrl(e5);
    for (const r4 of this.serverInfos) if (this._hasSameServerInstance(r4.server, e5)) {
      t4 = r4;
      break;
    }
    return t4;
  }
  findOAuthInfo(e5) {
    let t4;
    e5 = this._sanitizeUrl(e5);
    for (const r4 of this.oAuthInfos) if (this._hasSameServerInstance(r4.portalUrl, e5)) {
      t4 = r4;
      break;
    }
    return t4;
  }
  findCredential(e5, t4) {
    if (!e5) return;
    let r4;
    e5 = this._sanitizeUrl(e5);
    const s7 = this._isServerRsrc(e5) ? "server" : "portal";
    if (t4) {
      for (const i3 of this.credentials) if (this._hasSameServerInstance(i3.server, e5) && t4 === i3.userId && i3.scope === s7) {
        r4 = i3;
        break;
      }
    } else for (const i3 of this.credentials) if (this._hasSameServerInstance(i3.server, e5) && -1 !== this._getIdenticalSvcIdx(e5, i3) && i3.scope === s7) {
      r4 = i3;
      break;
    }
    return r4;
  }
  getCredential(e5, t4) {
    let r4, s7, o = true;
    t4 && (r4 = !!t4.token, s7 = t4.error, o = false !== t4.prompt), t4 = { ...t4 }, e5 = this._sanitizeUrl(e5);
    const n5 = new AbortController(), a3 = D();
    if (t4.signal && v(t4.signal, () => {
      n5.abort();
    }), v(n5, () => {
      a3.reject(new s2("identity-manager:user-aborted", "ABORTED"));
    }), p(n5)) return a3.promise;
    t4.signal = n5.signal;
    const h2 = this._isAdminResource(e5), u2 = r4 ? this.findCredential(e5) : null;
    let p5;
    if (u2 && s7 && s7.details && 498 === s7.details.httpStatus) u2.destroy();
    else if (u2) return p5 = new s2("identity-manager:not-authorized", "You are currently signed in as: '" + u2.userId + "'. You do not have access to this resource: " + e5, { error: s7 }), a3.reject(p5), a3.promise;
    const f = this._findCredential(e5, t4);
    if (f) return a3.resolve(f), a3.promise;
    let g = this.findServerInfo(e5);
    if (g) !g.hasServer && this._isServerRsrc(e5) && (g._restInfoPms = this._getTokenSvcUrl(e5), g.hasServer = true);
    else {
      const t5 = this._getTokenSvcUrl(e5);
      if (!t5) return p5 = new s2("identity-manager:unknown-resource", "Unknown resource - could not find token service endpoint."), a3.reject(p5), a3.promise;
      g = new i2(), g.server = this._getServerInstanceRoot(e5), "string" == typeof t5 ? (g.tokenServiceUrl = t5, g.hasPortal = true) : (g._restInfoPms = t5, g.hasServer = true), this.registerServers([g]);
    }
    return g.hasPortal && void 0 === g._selfReq && (o || M(g.tokenServiceUrl, this._appOrigin) || this._gwDomains.some((e6) => e6.tokenServiceUrl === g.tokenServiceUrl)) && (g._selfReq = { owningTenant: t4 && t4.owningTenant, selfDfd: this._getPortalSelf(g.tokenServiceUrl.replace(this._rePortalTokenSvc, "/sharing/rest/portals/self"), e5) }), this._enqueue(e5, g, t4, a3, h2);
  }
  getResourceName(e5) {
    return this._isRESTService(e5) ? e5.replace(this._regexSDirUrl, "").replace(this._regexServerType, "") || "" : this._gwUser.test(e5) && e5.replace(this._gwUser, "$1") || this._gwItem.test(e5) && e5.replace(this._gwItem, "$1") || this._gwGroup.test(e5) && e5.replace(this._gwGroup, "$1") || "";
  }
  generateToken(e5, t4, r4) {
    const o = this._rePortalTokenSvc.test(e5.tokenServiceUrl), n5 = new $(this._appOrigin), a3 = e5.shortLivedTokenValidity;
    let h2, l5, c2, d3, u2, p5, g, m;
    t4 && (m = this.tokenValidity || a3 || this._defaultTokenValidity, m > a3 && a3 > 0 && (m = a3)), r4 && (h2 = r4.isAdmin, l5 = r4.serverUrl, c2 = r4.token, p5 = r4.signal, g = r4.ssl, e5.customParameters = r4.customParameters), h2 ? d3 = e5.adminTokenServiceUrl : (d3 = e5.tokenServiceUrl, u2 = new $(d3.toLowerCase()), e5.webTierAuth && (r4 == null ? void 0 : r4.serverUrl) && !g && "http" === n5.scheme && (M(n5.uri, d3, true) || "https" === u2.scheme && n5.host === u2.host && "7080" === n5.port && "7443" === u2.port) && (d3 = d3.replace(/^https:/i, "http:").replace(/:7443/i, ":7080")));
    const v4 = { query: { request: "getToken", username: t4 == null ? void 0 : t4.username, password: t4 == null ? void 0 : t4.password, serverUrl: l5, token: c2, expiration: m, referer: h2 || o ? this._appOrigin : null, client: h2 ? "referer" : null, f: "json", ...e5.customParameters }, method: "post", authMode: "anonymous", useProxy: this._useProxy(e5, r4), signal: p5, ...r4 == null ? void 0 : r4.ioArgs };
    o || (v4.withCredentials = false);
    return U(d3, v4).then((r5) => {
      const s7 = r5.data;
      if (!s7 || !s7.token) return new s2("identity-manager:authentication-failed", "Unable to generate token");
      const o2 = e5.server;
      return C[o2] || (C[o2] = {}), t4 && (C[o2][t4.username] = t4.password), s7.validity = m, s7;
    });
  }
  isBusy() {
    return !!this._busy;
  }
  checkSignInStatus(e5) {
    return this.checkAppAccess(e5, "").then((e6) => e6.credential);
  }
  checkAppAccess(e5, t4, r4) {
    let o = false;
    return this.getCredential(e5, { prompt: false }).then((n5) => {
      let a3;
      const h2 = { f: "json" };
      if ("portal" === n5.scope) if (t4 && (this._doPortalSignIn(e5) || r4 && r4.force)) a3 = n5.server + "/sharing/rest/oauth2/validateAppAccess", h2.client_id = t4;
      else {
        if (!n5.token) return { credential: n5 };
        a3 = n5.server + "/sharing/rest";
      }
      else {
        if (!n5.token) return { credential: n5 };
        a3 = n5.server + "/rest/services";
      }
      return n5.token && (h2.token = n5.token), U(a3, { query: h2, authMode: "anonymous" }).then((e6) => {
        if (false === e6.data.valid) throw new s2("identity-manager:not-authorized", `You are currently signed in as: '${n5.userId}'.`, e6.data);
        return o = !!e6.data.viewOnlyUserTypeApp, { credential: n5 };
      }).catch((e6) => {
        if ("identity-manager:not-authorized" === e6.name) throw e6;
        const t5 = e6.details && e6.details.httpStatus;
        if (498 === t5) throw n5.destroy(), new s2("identity-manager:not-authenticated", "User is not signed in.");
        if (400 === t5) throw new s2("identity-manager:invalid-request");
        return { credential: n5 };
      });
    }).then((e6) => ({ credential: e6.credential, viewOnly: o }));
  }
  setOAuthResponseHash(e5) {
    e5 && ("#" === e5.charAt(0) && (e5 = e5.substring(1)), this._processOAuthPopupParams(v2(e5)));
  }
  setOAuthRedirectionHandler(e5) {
    this._oAuthRedirectFunc = e5;
  }
  setProtocolErrorHandler(e5) {
    this._protocolFunc = e5;
  }
  signIn(e5, t4, r4 = {}) {
    const s7 = D(), o = () => {
      var _a;
      h2 == null ? void 0 : h2.remove(), d3 == null ? void 0 : d3.remove(), p5 == null ? void 0 : p5.remove(), a3 == null ? void 0 : a3.destroy(), (_a = this.dialog) == null ? void 0 : _a.destroy(), this.dialog = a3 = h2 = d3 = p5 = null;
    }, n5 = () => {
      o(), this._oAuthDfd = null, s7.reject(new s2("identity-manager:user-aborted", "ABORTED"));
    };
    r4.signal && v(r4.signal, () => {
      n5();
    });
    let a3 = new this.formConstructor();
    a3.resource = this.getResourceName(e5), a3.server = t4.server, this.dialog = new h(), this.dialog.content = a3, this.dialog.open = true, this.emit("dialog-create");
    let h2 = a3.on("cancel", n5), d3 = l3(() => this.dialog.open, n5), p5 = a3.on("submit", (e6) => {
      this.generateToken(t4, e6, { isAdmin: r4.isAdmin, signal: r4.signal }).then((i3) => {
        o();
        const n6 = new L2({ userId: e6.username, server: t4.server, token: i3.token, expires: null != i3.expires ? Number(i3.expires) : null, ssl: !!i3.ssl, isAdmin: r4.isAdmin, validity: i3.validity });
        s7.resolve(n6);
      }).catch((e7) => {
        a3.error = e7, a3.signingIn = false;
      });
    });
    return s7.promise;
  }
  oAuthSignIn(e5, t4, r4, s7) {
    this._oAuthDfd = D();
    const o = this._oAuthDfd;
    let n5;
    (s7 == null ? void 0 : s7.signal) && v(s7.signal, () => {
      const e6 = this._oAuthDfd && this._oAuthDfd.oAuthWin_;
      e6 && !e6.closed ? e6.close() : this.dialog && f();
    }), o.resUrl_ = e5, o.sinfo_ = t4, o.oinfo_ = r4;
    const a3 = r4._oAuthCred;
    if (a3.storage && ("authorization-code" === r4.flowType || "auto" === r4.flowType && !r4.popup && t4.currentVersion >= 8.4)) {
      let e6 = crypto.getRandomValues(new Uint8Array(32));
      n5 = et(e6), a3.codeVerifier = n5, e6 = crypto.getRandomValues(new Uint8Array(32)), a3.stateUID = et(e6), a3.save() || (a3.codeVerifier = n5 = null);
    } else a3.codeVerifier = null;
    let h2, d3, p5, _;
    this._getCodeChallenge(n5).then((i3) => {
      const o2 = !s7 || false !== s7.oAuthPopupConfirmation;
      r4.popup && o2 ? (h2 = new this.formConstructor(), h2.oAuthPrompt = true, h2.server = t4.server, this.dialog = new h(), this.dialog.content = h2, this.dialog.open = true, this.emit("dialog-create"), d3 = h2.on("cancel", f), p5 = l3(() => this.dialog.open, f), _ = h2.on("submit", () => {
        g(), this._doOAuthSignIn(e5, t4, r4, i3);
      })) : this._doOAuthSignIn(e5, t4, r4, i3);
    });
    const f = () => {
      g(), this._oAuthDfd = null, o.reject(new s2("identity-manager:user-aborted", "ABORTED"));
    }, g = () => {
      var _a;
      d3 == null ? void 0 : d3.remove(), p5 == null ? void 0 : p5.remove(), _ == null ? void 0 : _.remove(), h2 == null ? void 0 : h2.destroy(), (_a = this.dialog) == null ? void 0 : _a.destroy(), this.dialog = null;
    };
    return o.promise;
  }
  destroyCredentials() {
    if (this.credentials) {
      this.credentials.slice().forEach((e5) => {
        e5.destroy();
      });
    }
    this.emit("credentials-destroy");
  }
  enablePostMessageAuth(e5 = "https://www.arcgis.com/sharing/rest") {
    this._postMessageAuthHandle && this._postMessageAuthHandle.remove(), this._postMessageAuthHandle = r(window, "message", (t4) => {
      var _a;
      if ((t4.origin === this._appOrigin || t4.origin.endsWith(".arcgis.com")) && "arcgis:auth:requestCredential" === ((_a = t4.data) == null ? void 0 : _a.type)) {
        const r4 = t4.source;
        this.getCredential(e5).then((e6) => {
          r4.postMessage({ type: "arcgis:auth:credential", credential: { expires: e6.expires, server: e6.server, ssl: e6.ssl, token: e6.token, userId: e6.userId } }, t4.origin);
        }).catch((e6) => {
          r4.postMessage({ type: "arcgis:auth:error", error: { name: e6.name, message: e6.message } }, t4.origin);
        });
      }
    });
  }
  disablePostMessageAuth() {
    this._postMessageAuthHandle && (this._postMessageAuthHandle.remove(), this._postMessageAuthHandle = null);
  }
  _getOAuthLocationParams() {
    var _a, _b;
    let e5 = window.location.hash;
    if (e5) {
      "#" === e5.charAt(0) && (e5 = e5.substring(1));
      const t5 = v2(e5);
      let r4 = false;
      if (t5.access_token && t5.expires_in && t5.state && t5.hasOwnProperty("username")) try {
        t5.state = JSON.parse(t5.state), t5.state.portalUrl && (this._oAuthLocationParams = t5, r4 = true);
      } catch {
      }
      else if (t5.error && t5.error_description && (console.log("IdentityManager OAuth Error: ", t5.error, " - ", t5.error_description), "access_denied" === t5.error && (r4 = true, t5.state))) try {
        t5.state = JSON.parse(t5.state);
      } catch {
      }
      r4 && (window.location.hash = ((_a = t5.state) == null ? void 0 : _a.hash) || "");
    }
    let t4 = window.location.search;
    if (t4) {
      "?" === t4.charAt(0) && (t4 = t4.substring(1));
      const e6 = v2(t4);
      let r4 = false;
      if (e6.code && e6.state) try {
        e6.state = JSON.parse(e6.state), e6.state.portalUrl && e6.state.uid && (this._oAuthLocationParams = e6, r4 = true);
      } catch {
      }
      else if (e6.error && e6.error_description && (console.log("IdentityManager OAuth Error: ", e6.error, " - ", e6.error_description), "access_denied" === e6.error && (r4 = true, e6.state))) try {
        e6.state = JSON.parse(e6.state);
      } catch {
      }
      if (r4) {
        const t5 = { ...e6 };
        ["code", "error", "error_description", "message_code", "persist", "state"].forEach((e7) => {
          delete t5[e7];
        });
        const r5 = A(t5), s7 = window.location.pathname + (r5 ? `?${r5}` : "") + (((_b = e6.state) == null ? void 0 : _b.hash) || "");
        window.history.replaceState(window.history.state, "", s7);
      }
    }
  }
  _getOAuthToken(e5, t4, r4, i3, o) {
    return e5 = e5.replace(/^http:/i, "https:"), U(`${e5}/sharing/rest/oauth2/token`, { authMode: "anonymous", method: "post", query: i3 && o ? { grant_type: "authorization_code", code: t4, redirect_uri: i3, client_id: r4, code_verifier: o } : { grant_type: "refresh_token", refresh_token: t4, client_id: r4 } }).then((e6) => e6.data);
  }
  _getCodeChallenge(e5) {
    if (e5 && globalThis.isSecureContext) {
      const t4 = new TextEncoder().encode(e5);
      return crypto.subtle.digest("SHA-256", t4).then((e6) => et(new Uint8Array(e6)));
    }
    return Promise.resolve(null);
  }
  _pageShowHandler(e5) {
    if (e5.persisted && this.isBusy() && this._rejectOnPersistedPageShow) {
      const e6 = new s2("identity-manager:user-aborted", "ABORTED");
      this._errbackFunc(e6);
    }
  }
  _findCredential(e5, t4) {
    let r4, s7, i3, o, n5 = -1;
    const a3 = t4 && t4.token, h2 = t4 && t4.resource, l5 = this._isServerRsrc(e5) ? "server" : "portal", c2 = this.credentials.filter((t5) => this._hasSameServerInstance(t5.server, e5) && t5.scope === l5);
    if (e5 = h2 || e5, c2.length) if (1 === c2.length) {
      if (r4 = c2[0], i3 = this.findServerInfo(r4.server), s7 = i3 && i3.owningSystemUrl, o = s7 ? this.findCredential(s7, r4.userId) : void 0, n5 = this._getIdenticalSvcIdx(e5, r4), !a3) return -1 === n5 && r4.resources.push(e5), this._addResource(e5, o), r4;
      -1 !== n5 && (r4.resources.splice(n5, 1), this._removeResource(e5, o));
    } else {
      let t5, r5;
      if (c2.some((a4) => (r5 = this._getIdenticalSvcIdx(e5, a4), -1 !== r5 && (t5 = a4, i3 = this.findServerInfo(t5.server), s7 = i3 && i3.owningSystemUrl, o = s7 ? this.findCredential(s7, t5.userId) : void 0, n5 = r5, true))), a3) t5 && (t5.resources.splice(n5, 1), this._removeResource(e5, o));
      else if (t5) return this._addResource(e5, o), t5;
    }
  }
  _findOAuthInfo(e5) {
    let t4 = this.findOAuthInfo(e5);
    if (!t4) {
      for (const r4 of this.oAuthInfos) if (this._isIdProvider(r4.portalUrl, e5)) {
        t4 = r4;
        break;
      }
    }
    return t4;
  }
  _addResource(e5, t4) {
    t4 && -1 === this._getIdenticalSvcIdx(e5, t4) && t4.resources.push(e5);
  }
  _removeResource(e5, t4) {
    let r4 = -1;
    t4 && (r4 = this._getIdenticalSvcIdx(e5, t4), r4 > -1 && t4.resources.splice(r4, 1));
  }
  _useProxy(e5, t4) {
    return t4 && t4.isAdmin && !M(e5.adminTokenServiceUrl, this._appOrigin) || !this._isPortalDomain(e5.tokenServiceUrl) && "10.1" === String(e5.currentVersion) && !M(e5.tokenServiceUrl, this._appOrigin);
  }
  _getOrigin(e5) {
    const t4 = new $(e5);
    return t4.scheme + "://" + t4.host + (null != t4.port ? ":" + t4.port : "");
  }
  _getServerInstanceRoot(e5) {
    const t4 = e5.toLowerCase();
    let r4 = t4.indexOf(this._agsRest);
    return -1 === r4 && this._isAdminResource(e5) && (r4 = this._agsAdmin.test(e5) ? e5.replace(this._agsAdmin, "$1").length : e5.search(this._adminSvcs)), -1 !== r4 || t2(t4) || (r4 = t4.indexOf("/sharing")), -1 === r4 && "/" === t4.substr(-1) && (r4 = t4.length - 1), r4 > -1 ? e5.substring(0, r4) : e5;
  }
  _hasSameServerInstance(e5, t4) {
    return "/" === e5.substr(-1) && (e5 = e5.slice(0, -1)), e5 = e5.toLowerCase(), t4 = this._getServerInstanceRoot(t4).toLowerCase(), e5 = this._normalizeAGOLorgDomain(e5), t4 = this._normalizeAGOLorgDomain(t4), (e5 = e5.substr(e5.indexOf(":"))) === (t4 = t4.substr(t4.indexOf(":")));
  }
  _normalizeAGOLorgDomain(e5) {
    const t4 = /^https?:\/\/(?:cdn|[a-z\d-]+\.maps)\.arcgis\.com/i, r4 = /^https?:\/\/(?:cdndev|[a-z\d-]+\.mapsdevext)\.arcgis\.com/i, s7 = /^https?:\/\/(?:cdnqa|[a-z\d-]+\.mapsqa)\.arcgis\.com/i;
    return t4.test(e5) ? e5 = e5.replace(t4, "https://www.arcgis.com") : r4.test(e5) ? e5 = e5.replace(r4, "https://devext.arcgis.com") : s7.test(e5) && (e5 = e5.replace(s7, "https://qaext.arcgis.com")), e5;
  }
  _sanitizeUrl(e5) {
    const r4 = (s.request.proxyUrl || "").toLowerCase(), s7 = r4 ? e5.toLowerCase().indexOf(r4 + "?") : -1;
    return -1 !== s7 && (e5 = e5.substring(s7 + r4.length + 1)), e5 = K(e5), L(e5).path;
  }
  _isRESTService(e5) {
    return e5.includes(this._agsRest);
  }
  _isAdminResource(e5) {
    return this._agsAdmin.test(e5) || this._adminSvcs.test(e5);
  }
  _isServerRsrc(e5) {
    return this._isRESTService(e5) || this._isAdminResource(e5);
  }
  _isIdenticalService(e5, t4) {
    let r4 = false;
    if (this._isRESTService(e5) && this._isRESTService(t4)) {
      const s7 = this._getSuffix(e5).toLowerCase(), i3 = this._getSuffix(t4).toLowerCase();
      if (r4 = s7 === i3, !r4) {
        const e6 = /(.*)\/(MapServer|FeatureServer|UtilityNetworkServer).*/gi;
        r4 = s7.replace(e6, "$1") === i3.replace(e6, "$1");
      }
    } else this._isAdminResource(e5) && this._isAdminResource(t4) ? r4 = true : this._isServerRsrc(e5) || this._isServerRsrc(t4) || !this._isPortalDomain(e5) || (r4 = true);
    return r4;
  }
  _isPortalDomain(e5) {
    const r4 = new $(e5.toLowerCase()), s7 = this._portalConfig;
    let i3 = this._gwDomains.some((e6) => e6.regex.test(r4.uri));
    return !i3 && s7 && (i3 = this._hasSameServerInstance(this._getServerInstanceRoot(s7.restBaseUrl), r4.uri)), i3 || s.portalUrl && (i3 = M(r4, s.portalUrl, true)), i3 || (i3 = this._portals.some((e6) => this._hasSameServerInstance(e6, r4.uri))), i3 = i3 || this._agsPortal.test(r4.path), i3;
  }
  _isIdProvider(e5, t4) {
    let r4 = -1, s7 = -1;
    this._gwDomains.forEach((i4, o) => {
      -1 === r4 && i4.regex.test(e5) && (r4 = o), -1 === s7 && i4.regex.test(t4) && (s7 = o);
    });
    let i3 = false;
    if (r4 > -1 && s7 > -1 && (0 === r4 || 4 === r4 ? 0 !== s7 && 4 !== s7 || (i3 = true) : 1 === r4 ? 1 !== s7 && 2 !== s7 || (i3 = true) : 2 === r4 ? 2 === s7 && (i3 = true) : 3 === r4 && 3 === s7 && (i3 = true)), !i3) {
      const r5 = this.findServerInfo(t4), s8 = r5 && r5.owningSystemUrl;
      s8 && b(r5) && this._isPortalDomain(s8) && this._isIdProvider(e5, s8) && (i3 = true);
    }
    return i3;
  }
  _getIdenticalSvcIdx(e5, t4) {
    let r4 = -1;
    for (let s7 = 0; s7 < t4.resources.length; s7++) {
      const i3 = t4.resources[s7];
      if (this._isIdenticalService(e5, i3)) {
        r4 = s7;
        break;
      }
    }
    return r4;
  }
  _getSuffix(e5) {
    return e5.replace(this._regexSDirUrl, "").replace(this._regexServerType, "$1");
  }
  _getTokenSvcUrl(e5) {
    let t4, r4, i3;
    if (this._isRESTService(e5) || this._isAdminResource(e5)) {
      const i4 = this._getServerInstanceRoot(e5);
      return t4 = i4 + "/admin/generateToken", r4 = U(e5 = i4 + "/rest/info", { query: { f: "json" } }).then((e6) => e6.data), { adminUrl: t4, promise: r4 };
    }
    if (this._isPortalDomain(e5)) {
      let t5 = "";
      if (this._gwDomains.some((r5) => (r5.regex.test(e5) && (t5 = r5.tokenServiceUrl), !!t5)), t5 || this._portals.some((r5) => (this._hasSameServerInstance(r5, e5) && (t5 = r5 + this._gwTokenUrl), !!t5)), t5 || (i3 = e5.toLowerCase().indexOf("/sharing"), -1 !== i3 && (t5 = e5.substring(0, i3) + this._gwTokenUrl)), t5 || (t5 = this._getOrigin(e5) + this._gwTokenUrl), t5) {
        const r5 = new $(e5).port;
        /^http:\/\//i.test(e5) && "7080" === r5 && (t5 = t5.replace(/:7080/i, ":7443")), t5 = t5.replace(/http:/i, "https:");
      }
      return t5;
    }
    if (e5.toLowerCase().includes("premium.arcgisonline.com")) return "https://premium.arcgisonline.com/server/tokens";
  }
  _processOAuthResponseParams(e5, t4, r4) {
    const s7 = t4._oAuthCred;
    if (e5.code) {
      const i4 = s7.codeVerifier;
      return s7.codeVerifier = null, s7.stateUID = null, s7.save(), this._getOAuthToken(r4.server, e5.code, t4.appId, this._getRedirectURI(t4, true), i4).then((i5) => {
        const o = new L2({ userId: i5.username, server: r4.server, token: i5.access_token, expires: Date.now() + 1e3 * i5.expires_in, ssl: i5.ssl, oAuthState: e5.state, _oAuthCred: s7 });
        return t4.userId = o.userId, s7.storage = i5.persist ? q : j, s7.refreshToken = i5.refresh_token, s7.token = null, s7.expires = i5.refresh_token_expires_in ? Date.now() + 1e3 * i5.refresh_token_expires_in : null, s7.userId = o.userId, s7.ssl = o.ssl, s7.save(), o;
      });
    }
    const i3 = new L2({ userId: e5.username, server: r4.server, token: e5.access_token, expires: Date.now() + 1e3 * Number(e5.expires_in), ssl: "true" === e5.ssl, oAuthState: e5.state, _oAuthCred: s7 });
    return t4.userId = i3.userId, s7.storage = e5.persist ? q : j, s7.refreshToken = null, s7.token = i3.token, s7.expires = i3.expires, s7.userId = i3.userId, s7.ssl = i3.ssl, s7.save(), Promise.resolve(i3);
  }
  _processOAuthPopupParams(e5) {
    var _a;
    const t4 = this._oAuthDfd;
    if (this._oAuthDfd = null, t4) if (clearInterval(this._oAuthIntervalId), (_a = this._oAuthOnPopupHandle) == null ? void 0 : _a.remove(), e5.error) {
      const r4 = "access_denied" === e5.error, s7 = new s2(r4 ? "identity-manager:user-aborted" : "identity-manager:authentication-failed", r4 ? "ABORTED" : "OAuth: " + e5.error + " - " + e5.error_description);
      t4.reject(s7);
    } else this._processOAuthResponseParams(e5, t4.oinfo_, t4.sinfo_).then((e6) => {
      t4.resolve(e6);
    }).catch((e6) => {
      t4.reject(e6);
    });
  }
  _setOAuthResponseQueryString(e5) {
    e5 && ("?" === e5.charAt(0) && (e5 = e5.substring(1)), this._processOAuthPopupParams(v2(e5)));
  }
  _exchangeToken(e5, t4, r4) {
    return U(`${e5}/sharing/rest/oauth2/exchangeToken`, { authMode: "anonymous", method: "post", query: { f: "json", client_id: t4, token: r4 } }).then((e6) => e6.data.token);
  }
  _getPlatformSelf(e5, t4) {
    return e5 = e5.replace(/^http:/i, "https:"), U(`${e5}/sharing/rest/oauth2/platformSelf`, { authMode: "anonymous", headers: { "X-Esri-Auth-Client-Id": t4, "X-Esri-Auth-Redirect-Uri": window.location.href.replace(/#.*$/, "") }, method: "post", query: { f: "json", expiration: 30 }, withCredentials: true }).then((e6) => e6.data);
  }
  _getPortalSelf(e5, t4) {
    let r4;
    if (this._gwDomains.some((t5) => (t5.regex.test(e5) && (r4 = t5.customBaseUrl), !!r4)), r4) return Promise.resolve({ allSSL: true, currentVersion: "8.4", customBaseUrl: r4, portalMode: "multitenant", supportsOAuth: true });
    this._appOrigin.startsWith("https:") ? e5 = e5.replace(/^http:/i, "https:").replace(/:7080/i, ":7443") : /^http:/i.test(t4) && (e5 = e5.replace(/^https:/i, "http:").replace(/:7443/i, ":7080"));
    return U(e5, { query: { f: "json" }, authMode: "anonymous", withCredentials: true }).then((e6) => e6.data);
  }
  _doPortalSignIn(e5) {
    const t4 = this._portalConfig, r4 = window.location.href, s7 = this.findServerInfo(e5);
    return !(!t4 && !this._isPortalDomain(r4) || !(s7 ? s7.hasPortal || s7.owningSystemUrl && this._isPortalDomain(s7.owningSystemUrl) : this._isPortalDomain(e5)) || !(this._isIdProvider(r4, e5) || t4 && (this._hasSameServerInstance(this._getServerInstanceRoot(t4.restBaseUrl), e5) || this._isIdProvider(t4.restBaseUrl, e5)) || M(r4, e5, true)));
  }
  _checkProtocol(e5, t4, r4, s7) {
    let o = true;
    const n5 = s7 ? t4.adminTokenServiceUrl : t4.tokenServiceUrl;
    if (n5.trim().toLowerCase().startsWith("https:") && !this._appOrigin.startsWith("https:") && J(n5) && (o = !!this._protocolFunc && !!this._protocolFunc({ resourceUrl: e5, serverInfo: t4 }), !o)) {
      r4(new s2("identity-manager:aborted", "Aborted the Sign-In process to avoid sending password over insecure connection."));
    }
    return o;
  }
  _enqueue(e5, t4, r4, s7, i3, o) {
    return s7 || (s7 = D()), s7.resUrl_ = e5, s7.sinfo_ = t4, s7.options_ = r4, s7.admin_ = i3, s7.refresh_ = o, this._busy ? this._hasSameServerInstance(this._getServerInstanceRoot(e5), this._busy.resUrl_) ? (this._oAuthDfd && this._oAuthDfd.oAuthWin_ && this._oAuthDfd.oAuthWin_.focus(), this._soReqs.push(s7)) : this._xoReqs.push(s7) : this._doSignIn(s7), s7.promise;
  }
  _doSignIn(e5) {
    this._busy = e5, this._rejectOnPersistedPageShow = false;
    const t4 = (t5) => {
      const r5 = e5.options_ && e5.options_.resource, s8 = e5.resUrl_, i3 = e5.refresh_;
      let o2 = false;
      this.credentials.includes(t5) || (i3 && this.credentials.includes(i3) ? (i3.userId = t5.userId, i3.token = t5.token, i3.expires = t5.expires, i3.validity = t5.validity, i3.ssl = t5.ssl, i3.creationTime = t5.creationTime, o2 = true, t5 = i3) : this.credentials.push(t5)), t5.resources || (t5.resources = []), t5.resources.includes(r5 || s8) || t5.resources.push(r5 || s8), t5.scope = this._isServerRsrc(s8) ? "server" : "portal", t5.emitTokenChange();
      const n5 = this._soReqs, a4 = {};
      this._soReqs = [], n5.forEach((e6) => {
        if (!this._isIdenticalService(s8, e6.resUrl_)) {
          const r6 = this._getSuffix(e6.resUrl_);
          a4[r6] || (a4[r6] = true, t5.resources.push(e6.resUrl_));
        }
      }), e5.resolve(t5), n5.forEach((e6) => {
        this._hasSameServerInstance(this._getServerInstanceRoot(s8), e6.resUrl_) ? e6.resolve(t5) : this._soReqs.push(e6);
      }), this._busy = e5.resUrl_ = e5.sinfo_ = e5.refresh_ = null, o2 || this.emit("credential-create", { credential: t5 }), this._soReqs.length ? this._doSignIn(this._soReqs.shift()) : this._xoReqs.length && this._doSignIn(this._xoReqs.shift());
    }, r4 = (t5) => {
      e5.reject(t5), this._busy = e5.resUrl_ = e5.sinfo_ = e5.refresh_ = null, this._soReqs.length ? this._doSignIn(this._soReqs.shift()) : this._xoReqs.length && this._doSignIn(this._xoReqs.shift());
    }, s7 = (o2, a4, h2, l6) => {
      var _a, _b, _c;
      const d4 = e5.sinfo_, u2 = !e5.options_ || false !== e5.options_.prompt, p5 = d4.hasPortal && this._findOAuthInfo(e5.resUrl_);
      let f, g;
      if (o2) t4(new L2({ userId: o2, server: d4.server, token: h2 || null, expires: null != l6 ? Number(l6) : null, ssl: !!a4 }));
      else if (window !== window.parent && ((_a = this._appUrlObj.query) == null ? void 0 : _a["arcgis-auth-origin"]) && ((_b = this._appUrlObj.query) == null ? void 0 : _b["arcgis-auth-portal"]) && this._hasSameServerInstance(this._getServerInstanceRoot(this._appUrlObj.query["arcgis-auth-portal"]), e5.resUrl_)) {
        window.parent.postMessage({ type: "arcgis:auth:requestCredential" }, this._appUrlObj.query["arcgis-auth-origin"]);
        const s8 = r(window, "message", (e6) => {
          e6.source === window.parent && e6.data && ("arcgis:auth:credential" === e6.data.type ? (s8.remove(), e6.data.credential.expires < Date.now() ? r4(new s2("identity-manager:credential-request-failed", "Parent application's token has expired.")) : t4(new L2(e6.data.credential))) : "arcgis:auth:error" === e6.data.type && (s8.remove(), "tokenExpiredError" === e6.data.error.name ? r4(new s2("identity-manager:credential-request-failed", "Parent application's token has expired.")) : r4(s2.fromJSON(e6.data.error))));
        });
        v((_c = e5.options_) == null ? void 0 : _c.signal, () => {
          s8.remove();
        });
      } else if (p5) {
        let o3 = p5._oAuthCred;
        if (!o3) {
          const e6 = new e4(p5, q), t5 = new e4(p5, j);
          e6.isValid() && t5.isValid() ? e6.expires > t5.expires ? (o3 = e6, t5.destroy()) : (o3 = t5, e6.destroy()) : o3 = e6.isValid() ? e6 : t5, p5._oAuthCred = o3;
        }
        if (o3.isValid()) {
          f = new L2({ userId: o3.userId, server: d4.server, token: o3.token, expires: o3.expires, ssl: o3.ssl, _oAuthCred: o3 });
          const r5 = p5.appId !== o3.appId && this._doPortalSignIn(e5.resUrl_);
          r5 || o3.refreshToken ? (e5._pendingDfd = o3.refreshToken ? this._getOAuthToken(d4.server, o3.refreshToken, o3.appId).then((e6) => (f.expires = Date.now() + 1e3 * e6.expires_in, f.token = e6.access_token, f)) : Promise.resolve(f), e5._pendingDfd.then((e6) => r5 ? this._exchangeToken(e6.server, p5.appId, e6.token).then((t5) => (e6.token = t5, e6)).catch(() => e6) : e6).then((e6) => {
            t4(e6);
          }).catch(() => {
            o3 == null ? void 0 : o3.destroy(), s7();
          })) : t4(f);
        } else if (this._oAuthLocationParams && this._hasSameServerInstance(p5.portalUrl, this._oAuthLocationParams.state.portalUrl) && (this._oAuthLocationParams.access_token || this._oAuthLocationParams.code && this._oAuthLocationParams.state.uid === o3.stateUID && o3.codeVerifier)) {
          const s8 = this._oAuthLocationParams;
          this._oAuthLocationParams = null, e5._pendingDfd = this._processOAuthResponseParams(s8, p5, d4).then((e6) => {
            t4(e6);
          }).catch(r4);
        } else {
          const s8 = () => {
            u2 ? e5._pendingDfd = this.oAuthSignIn(e5.resUrl_, d4, p5, e5.options_).then(t4, r4) : (g = new s2("identity-manager:not-authenticated", "User is not signed in."), r4(g));
          };
          this._doPortalSignIn(e5.resUrl_) ? e5._pendingDfd = this._getPlatformSelf(d4.server, p5.appId).then((e6) => {
            M(e6.portalUrl, this._appOrigin, true) ? (f = new L2({ userId: e6.username, server: d4.server, expires: Date.now() + 1e3 * e6.expires_in, token: e6.token }), t4(f)) : s8();
          }).catch(s8) : s8();
        }
      } else if (u2) {
        if (this._checkProtocol(e5.resUrl_, d4, r4, e5.admin_)) {
          let s8 = e5.options_;
          e5.admin_ && (s8 = s8 || {}, s8.isAdmin = true), e5._pendingDfd = this.signIn(e5.resUrl_, d4, s8).then(t4, r4);
        }
      } else g = new s2("identity-manager:not-authenticated", "User is not signed in."), r4(g);
    }, o = () => {
      const s8 = e5.sinfo_, i3 = s8.owningSystemUrl, o2 = e5.options_;
      let n5, a4, h2, l6;
      if (o2 && (n5 = o2.token, a4 = o2.error, h2 = o2.prompt), l6 = this._findCredential(i3, { token: n5, resource: e5.resUrl_ }), !l6) {
        for (const e6 of this.credentials) if (this._isIdProvider(i3, e6.server)) {
          l6 = e6;
          break;
        }
      }
      if (l6) {
        const i4 = this.findCredential(e5.resUrl_, l6.userId);
        if (i4) t4(i4);
        else if (D2(s8, this._legacyFed)) {
          const e6 = l6.toJSON();
          e6.server = s8.server, e6.resources = null, t4(new L2(e6));
        } else {
          (e5._pendingDfd = this.generateToken(this.findServerInfo(l6.server), null, { serverUrl: e5.resUrl_, token: l6.token, signal: e5.options_.signal, ssl: l6.ssl })).then((r5) => {
            t4(new L2({ userId: l6 == null ? void 0 : l6.userId, server: s8.server, token: r5.token, expires: null != r5.expires ? Number(r5.expires) : null, ssl: !!r5.ssl, isAdmin: e5.admin_, validity: r5.validity }));
          }, r4);
        }
      } else {
        this._busy = null, n5 && (e5.options_.token = null);
        (e5._pendingDfd = this.getCredential(i3.replace(/\/?$/, "/sharing"), { resource: e5.resUrl_, owningTenant: s8.owningTenant, signal: e5.options_.signal, token: n5, error: a4, prompt: h2 })).then(() => {
          this._enqueue(e5.resUrl_, e5.sinfo_, e5.options_, e5, e5.admin_);
        }, (t5) => {
          e5.resUrl_ = e5.sinfo_ = e5.refresh_ = null, e5.reject(t5);
        });
      }
    };
    this._errbackFunc = r4;
    const a3 = e5.sinfo_.owningSystemUrl, l5 = this._isServerRsrc(e5.resUrl_), d3 = e5.sinfo_._restInfoPms;
    d3 ? d3.promise.then((t5) => {
      const r5 = e5.sinfo_;
      if (r5._restInfoPms) {
        r5.adminTokenServiceUrl = r5._restInfoPms.adminUrl, r5._restInfoPms = null, r5.tokenServiceUrl = (t("authInfo.tokenServicesUrl", t5) || t("authInfo.tokenServiceUrl", t5) || t("tokenServiceUrl", t5)) ?? null, r5.shortLivedTokenValidity = t("authInfo.shortLivedTokenValidity", t5) ?? null, r5.currentVersion = t5.currentVersion, r5.owningTenant = t5.owningTenant;
        const e6 = r5.owningSystemUrl = t5.owningSystemUrl;
        e6 && this._portals.push(e6);
      }
      l5 && r5.owningSystemUrl ? o() : s7();
    }, () => {
      e5.sinfo_._restInfoPms = null;
      const t5 = new s2("identity-manager:server-identification-failed", "Unknown resource - could not find token service endpoint.");
      r4(t5);
    }) : l5 && a3 ? o() : e5.sinfo_._selfReq ? e5.sinfo_._selfReq.selfDfd.then((t5) => {
      const r5 = {};
      let s8, i3, o2, n5;
      return t5 && (s8 = t5.user && t5.user.username, r5.username = s8, r5.allSSL = t5.allSSL, i3 = t5.supportsOAuth, n5 = parseFloat(t5.currentVersion), "multitenant" === t5.portalMode && (o2 = t5.customBaseUrl), e5.sinfo_.currentVersion = n5), e5.sinfo_.webTierAuth = !!s8, s8 && this.normalizeWebTierAuth ? this.generateToken(e5.sinfo_, null, { ssl: r5.allSSL }).catch(() => null).then((e6) => (r5.portalToken = e6 && e6.token, r5.tokenExpiration = e6 && e6.expires, r5)) : !s8 && i3 && n5 >= 4.4 && !this._findOAuthInfo(e5.resUrl_) ? this._generateOAuthInfo({ portalUrl: e5.sinfo_.server, customBaseUrl: o2, owningTenant: e5.sinfo_._selfReq.owningTenant }).catch(() => null).then(() => r5) : r5;
    }).catch(() => null).then((t5) => {
      e5.sinfo_._selfReq = null, t5 ? s7(t5.username, t5.allSSL, t5.portalToken, t5.tokenExpiration) : s7();
    }) : s7();
  }
  _generateOAuthInfo(e5) {
    let t4, r4 = null, i3 = e5.portalUrl;
    const o = e5.customBaseUrl, n5 = e5.owningTenant, a3 = !this._defaultOAuthInfo && this._createDefaultOAuthInfo && !this._hasTestedIfAppIsOnPortal;
    if (a3) {
      r4 = window.location.href;
      let e6 = r4.indexOf("?");
      e6 > -1 && (r4 = r4.slice(0, e6)), e6 = r4.search(/\/(apps|home)\//), r4 = e6 > -1 ? r4.slice(0, e6) : null;
    }
    return a3 && r4 ? (this._hasTestedIfAppIsOnPortal = true, t4 = U(r4 + "/sharing/rest", { query: { f: "json" } }).then(() => {
      this._defaultOAuthInfo = new i({ appId: "arcgisonline", popupCallbackUrl: r4 + "/home/<USER>" });
    })) : t4 = Promise.resolve(), t4.then(() => {
      if (this._defaultOAuthInfo) return i3 = i3.replace(/^http:/i, "https:"), U(i3 + "/sharing/rest/oauth2/validateRedirectUri", { query: { accountId: n5, client_id: this._defaultOAuthInfo.appId, redirect_uri: F(this._defaultOAuthInfo.popupCallbackUrl), f: "json" } }).then((e6) => {
        if (e6.data.valid) {
          const t5 = this._defaultOAuthInfo.clone();
          e6.data.urlKey && o ? t5.portalUrl = "https://" + e6.data.urlKey.toLowerCase() + "." + o : t5.portalUrl = i3, t5.popup = window !== window.top || !(M(i3, this._appOrigin) || this._gwDomains.some((e7) => e7.regex.test(i3) && e7.regex.test(this._appOrigin))), this.oAuthInfos.push(t5);
        }
      });
    });
  }
  _doOAuthSignIn(e5, t4, r4, s7) {
    const o = r4._oAuthCred, a3 = { portalUrl: r4.portalUrl };
    !r4.popup && r4.preserveUrlHash && window.location.hash && (a3.hash = window.location.hash), o.stateUID && (a3.uid = o.stateUID);
    const h2 = { client_id: r4.appId, response_type: o.codeVerifier ? "code" : "token", state: JSON.stringify(a3), expiration: r4.expiration, locale: r4.locale, redirect_uri: this._getRedirectURI(r4, !!o.codeVerifier) };
    r4.forceLogin && (h2.force_login = true), r4.forceUserId && r4.userId && (h2.prepopulatedusername = r4.userId), !r4.popup && this._doPortalSignIn(e5) && (h2.redirectToUserOrgUrl = true), o.codeVerifier && (h2.code_challenge = s7 || o.codeVerifier, h2.code_challenge_method = s7 ? "S256" : "plain");
    const l5 = r4.portalUrl.replace(/^http:/i, "https:") + "/sharing/oauth2/authorize", c2 = l5 + "?" + A(h2);
    if (r4.popup) {
      const e6 = window.open(c2, "esriJSAPIOAuth", r4.popupWindowFeatures);
      if (e6) e6.focus(), this._oAuthDfd.oAuthWin_ = e6, this._oAuthIntervalId = setInterval(() => {
        if (e6.closed) {
          clearInterval(this._oAuthIntervalId), this._oAuthOnPopupHandle.remove();
          const e7 = this._oAuthDfd;
          if (e7) {
            const t5 = new s2("identity-manager:user-aborted", "ABORTED");
            e7.reject(t5);
          }
        }
      }, 500), this._oAuthOnPopupHandle = r(window, ["arcgis:auth:hash", "arcgis:auth:location:search"], (e7) => {
        "arcgis:auth:hash" === e7.type ? this.setOAuthResponseHash(e7.detail) : this._setOAuthResponseQueryString(e7.detail);
      });
      else {
        const e7 = new s2("identity-manager:popup-blocked", "ABORTED");
        this._oAuthDfd.reject(e7);
      }
    } else this._rejectOnPersistedPageShow = true, this._oAuthRedirectFunc ? this._oAuthRedirectFunc({ authorizeParams: h2, authorizeUrl: l5, resourceUrl: e5, serverInfo: t4, oAuthInfo: r4 }) : window.location.href = c2;
  }
  _getRedirectURI(e5, t4) {
    const r4 = window.location.href.replace(/#.*$/, "");
    if (e5.popup) return F(e5.popupCallbackUrl);
    if (t4) {
      const e6 = L(r4);
      return e6.query && ["code", "error", "error_description", "message_code", "persist", "state"].forEach((t5) => {
        delete e6.query[t5];
      }), Et(e6.path, e6.query);
    }
    return r4;
  }
};
E.prototype.declaredClass = "esri.identity.IdentityManagerBase";
var L2 = class extends n2.EventedAccessor {
  constructor(e5) {
    super(e5), this._oAuthCred = null, this.tokenRefreshBuffer = 2, e5 && e5._oAuthCred && (this._oAuthCred = e5._oAuthCred);
  }
  initialize() {
    this.resources = this.resources || [], null == this.creationTime && (this.creationTime = Date.now());
  }
  refreshToken() {
    const e5 = r2.findServerInfo(this.server), t4 = e5 && e5.owningSystemUrl, s7 = !!t4 && "server" === this.scope, i3 = s7 && D2(e5, r2._legacyFed), o = e5.webTierAuth, n5 = o && r2.normalizeWebTierAuth, a3 = C[this.server], h2 = a3 && a3[this.userId];
    let l5, c2 = this.resources && this.resources[0], d3 = s7 ? r2.findServerInfo(t4) : null, u2 = { username: this.userId, password: h2 };
    if (o && !n5) return;
    s7 && !d3 && r2.serverInfos.some((e6) => (r2._isIdProvider(t4, e6.server) && (d3 = e6), !!d3));
    const p5 = d3 ? r2.findCredential(d3.server, this.userId) : null;
    if (!s7 || p5) {
      if (!i3) {
        if (s7) l5 = { serverUrl: c2, token: p5 && p5.token, ssl: p5 && p5.ssl };
        else if (n5) u2 = null, l5 = { ssl: this.ssl };
        else {
          if (!h2) {
            let t5;
            return c2 && (c2 = r2._sanitizeUrl(c2), this._enqueued = 1, t5 = r2._enqueue(c2, e5, null, null, this.isAdmin, this), t5.then(() => {
              this._enqueued = 0, this.refreshServerTokens();
            }).catch(() => {
              this._enqueued = 0;
            })), t5;
          }
          this.isAdmin && (l5 = { isAdmin: true });
        }
        return r2.generateToken(s7 ? d3 : e5, s7 ? null : u2, l5).then((e6) => {
          this.token = e6.token, this.expires = null != e6.expires ? Number(e6.expires) : null, this.creationTime = Date.now(), this.validity = e6.validity, this.emitTokenChange(), this.refreshServerTokens();
        }).catch(() => {
        });
      }
      p5 == null ? void 0 : p5.refreshToken();
    }
  }
  refreshServerTokens() {
    "portal" === this.scope && r2.credentials.forEach((e5) => {
      const t4 = r2.findServerInfo(e5.server), s7 = t4 && t4.owningSystemUrl;
      e5 !== this && e5.userId === this.userId && s7 && "server" === e5.scope && (r2._hasSameServerInstance(this.server, s7) || r2._isIdProvider(s7, this.server)) && (D2(t4, r2._legacyFed) ? (e5.token = this.token, e5.expires = this.expires, e5.creationTime = this.creationTime, e5.validity = this.validity, e5.emitTokenChange()) : e5.refreshToken());
    });
  }
  emitTokenChange(e5) {
    clearTimeout(this._refreshTimer);
    const t4 = this.server && r2.findServerInfo(this.server), s7 = t4 && t4.owningSystemUrl, i3 = s7 && r2.findServerInfo(s7);
    false === e5 || s7 && "portal" !== this.scope && (!i3 || !i3.webTierAuth || r2.normalizeWebTierAuth) || null == this.expires && null == this.validity || this._startRefreshTimer(), this.emit("token-change");
  }
  destroy() {
    this.userId = this.server = this.token = this.expires = this.validity = this.resources = this.creationTime = null, this._oAuthCred && (this._oAuthCred.destroy(), this._oAuthCred = null);
    const e5 = r2.credentials.indexOf(this);
    e5 > -1 && r2.credentials.splice(e5, 1), this.emitTokenChange(), this.emit("destroy");
  }
  toJSON() {
    const e5 = l({ userId: this.userId, server: this.server, token: this.token, expires: this.expires, validity: this.validity, ssl: this.ssl, isAdmin: this.isAdmin, creationTime: this.creationTime, scope: this.scope }), t4 = this.resources;
    return t4 && t4.length > 0 && (e5.resources = t4.slice()), e5;
  }
  _startRefreshTimer() {
    clearTimeout(this._refreshTimer);
    const e5 = 6e4 * this.tokenRefreshBuffer, t4 = 2 ** 31 - 1;
    let r4 = (this.validity ? this.creationTime + 6e4 * this.validity : this.expires) - Date.now();
    r4 < 0 ? r4 = 0 : r4 > t4 && (r4 = t4), this._refreshTimer = setTimeout(this.refreshToken.bind(this), r4 > e5 ? r4 - e5 : r4);
  }
};
e([y()], L2.prototype, "creationTime", void 0), e([y()], L2.prototype, "expires", void 0), e([y()], L2.prototype, "isAdmin", void 0), e([y()], L2.prototype, "oAuthState", void 0), e([y()], L2.prototype, "resources", void 0), e([y()], L2.prototype, "scope", void 0), e([y()], L2.prototype, "server", void 0), e([y()], L2.prototype, "ssl", void 0), e([y()], L2.prototype, "token", void 0), e([y()], L2.prototype, "tokenRefreshBuffer", void 0), e([y()], L2.prototype, "userId", void 0), e([y()], L2.prototype, "validity", void 0), L2 = e([a("esri.identity.Credential")], L2);

// node_modules/@arcgis/core/identity/IdentityManager.js
var r3 = class extends E {
};
r3.prototype.declaredClass = "esri.identity.IdentityManager";
var s6 = new r3();
n4(s6);
export {
  s6 as default
};
/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 7.2.0
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)
*/
//# sourceMappingURL=IdentityManager-IT5CKHA6.js.map
