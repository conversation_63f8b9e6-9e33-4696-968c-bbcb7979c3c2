{"version": 3, "sources": ["../../@arcgis/core/renderers/support/LegendOptions.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";var s;let p=s=class extends t{constructor(){super(...arguments),this.title=null}clone(){return new s({title:this.title})}};r([o({type:String,json:{write:!0}})],p.prototype,\"title\",void 0),p=s=r([e(\"esri.renderers.support.LegendOptions\")],p);export{p as LegendOptions};\n"], "mappings": ";;;;;;;;;;;;AAIkV,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;", "names": []}