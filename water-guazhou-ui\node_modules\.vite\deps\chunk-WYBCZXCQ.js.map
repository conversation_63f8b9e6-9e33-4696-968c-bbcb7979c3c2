{"version": 3, "sources": ["../../@arcgis/core/layers/support/I3SLayerDefinitions.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{enumeration as r}from\"../../core/accessorSupport/decorators/enumeration.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";let s=class extends t{constructor(){super(...arguments),this.nodesPerPage=null,this.rootIndex=0,this.lodSelectionMetricType=null}};e([o({type:Number})],s.prototype,\"nodesPerPage\",void 0),e([o({type:Number})],s.prototype,\"rootIndex\",void 0),e([o({type:String})],s.prototype,\"lodSelectionMetricType\",void 0),s=e([p(\"esri.layer.support.I3SNodePageDefinition\")],s);let i=class extends t{constructor(){super(...arguments),this.factor=1}};e([o({type:Number,json:{read:{source:\"textureSetDefinitionId\"}}})],i.prototype,\"id\",void 0),e([o({type:Number})],i.prototype,\"factor\",void 0),i=e([p(\"esri.layer.support.I3SMaterialTexture\")],i);let a=class extends t{constructor(){super(...arguments),this.baseColorFactor=[1,1,1,1],this.baseColorTexture=null,this.metallicRoughnessTexture=null,this.metallicFactor=1,this.roughnessFactor=1}};e([o({type:[Number]})],a.prototype,\"baseColorFactor\",void 0),e([o({type:i})],a.prototype,\"baseColorTexture\",void 0),e([o({type:i})],a.prototype,\"metallicRoughnessTexture\",void 0),e([o({type:Number})],a.prototype,\"metallicFactor\",void 0),e([o({type:Number})],a.prototype,\"roughnessFactor\",void 0),a=e([p(\"esri.layer.support.I3SMaterialPBRMetallicRoughness\")],a);let l=class extends t{constructor(){super(...arguments),this.alphaMode=\"opaque\",this.alphaCutoff=.25,this.doubleSided=!1,this.cullFace=\"none\",this.normalTexture=null,this.occlusionTexture=null,this.emissiveTexture=null,this.emissiveFactor=null,this.pbrMetallicRoughness=null}};e([r({opaque:\"opaque\",mask:\"mask\",blend:\"blend\"})],l.prototype,\"alphaMode\",void 0),e([o({type:Number})],l.prototype,\"alphaCutoff\",void 0),e([o({type:Boolean})],l.prototype,\"doubleSided\",void 0),e([r({none:\"none\",back:\"back\",front:\"front\"})],l.prototype,\"cullFace\",void 0),e([o({type:i})],l.prototype,\"normalTexture\",void 0),e([o({type:i})],l.prototype,\"occlusionTexture\",void 0),e([o({type:i})],l.prototype,\"emissiveTexture\",void 0),e([o({type:[Number]})],l.prototype,\"emissiveFactor\",void 0),e([o({type:a})],l.prototype,\"pbrMetallicRoughness\",void 0),l=e([p(\"esri.layer.support.I3SMaterialDefinition\")],l);let n=class extends t{};e([o({type:String,json:{read:{source:[\"name\",\"index\"],reader:(e,t)=>null!=e?e:`${t.index}`}}})],n.prototype,\"name\",void 0),e([r({jpg:\"jpg\",png:\"png\",dds:\"dds\",\"ktx-etc2\":\"ktx-etc2\",ktx2:\"ktx2\",basis:\"basis\"})],n.prototype,\"format\",void 0),n=e([p(\"esri.layer.support.I3STextureFormat\")],n);let u=class extends t{constructor(){super(...arguments),this.atlas=!1}};e([o({type:[n]})],u.prototype,\"formats\",void 0),e([o({type:Boolean})],u.prototype,\"atlas\",void 0),u=e([p(\"esri.layer.support.I3STextureSetDefinition\")],u);let y=class extends t{};e([r({Float32:\"Float32\",UInt64:\"UInt64\",UInt32:\"UInt32\",UInt16:\"UInt16\",UInt8:\"UInt8\"})],y.prototype,\"type\",void 0),e([o({type:Number})],y.prototype,\"component\",void 0),y=e([p(\"esri.layer.support.I3SGeometryAttribute\")],y);let d=class extends t{};e([r({draco:\"draco\"})],d.prototype,\"encoding\",void 0),e([o({type:[String]})],d.prototype,\"attributes\",void 0),d=e([p(\"esri.layer.support.I3SGeometryCompressedAttributes\")],d);let c=class extends t{constructor(){super(...arguments),this.offset=0}};e([o({type:Number})],c.prototype,\"offset\",void 0),e([o({type:y})],c.prototype,\"position\",void 0),e([o({type:y})],c.prototype,\"normal\",void 0),e([o({type:y})],c.prototype,\"uv0\",void 0),e([o({type:y})],c.prototype,\"color\",void 0),e([o({type:y})],c.prototype,\"uvRegion\",void 0),e([o({type:y})],c.prototype,\"featureId\",void 0),e([o({type:y})],c.prototype,\"faceRange\",void 0),e([o({type:d})],c.prototype,\"compressedAttributes\",void 0),c=e([p(\"esri.layer.support.I3SGeometryBuffer\")],c);let m=class extends t{};e([r({triangle:\"triangle\"})],m.prototype,\"topology\",void 0),e([o()],m.prototype,\"geometryBuffers\",void 0),m=e([p(\"esri.layer.support.I3SGeometryDefinition\")],m);export{y as I3SGeometryAttribute,c as I3SGeometryBuffer,d as I3SGeometryCompressedAttributes,m as I3SGeometryDefinition,l as I3SMaterialDefinition,a as I3SMaterialPBRMetallicRoughness,i as I3SMaterialTexture,s as I3SNodePageDefinition,n as I3STextureFormat,u as I3STextureSetDefinition};\n"], "mappings": ";;;;;;;;;;;;;;;AAIqa,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,MAAK,KAAK,YAAU,GAAE,KAAK,yBAAuB;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,0BAAyB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,yBAAwB,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAE,CAAC;AAAE,IAAIA,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,kBAAgB,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,KAAK,mBAAiB,MAAK,KAAK,2BAAyB,MAAK,KAAK,iBAAe,GAAE,KAAK,kBAAgB;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,4BAA2B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,oDAAoD,CAAC,GAAEA,EAAC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU,UAAS,KAAK,cAAY,MAAI,KAAK,cAAY,OAAG,KAAK,WAAS,QAAO,KAAK,gBAAc,MAAK,KAAK,mBAAiB,MAAK,KAAK,kBAAgB,MAAK,KAAK,iBAAe,MAAK,KAAK,uBAAqB;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,QAAO,UAAS,MAAK,QAAO,OAAM,QAAO,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,QAAO,OAAM,QAAO,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,GAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,wBAAuB,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAEA,EAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,CAAC,QAAO,OAAO,GAAE,QAAO,CAACC,IAAE,MAAI,QAAMA,KAAEA,KAAE,GAAG,EAAE,KAAK,GAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,YAAW,YAAW,MAAK,QAAO,OAAM,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,QAAM;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,4CAA4C,CAAC,GAAE,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,SAAQ,WAAU,QAAO,UAAS,QAAO,UAAS,QAAO,UAAS,OAAM,QAAO,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,yCAAyC,CAAC,GAAEA,EAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,OAAM,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,MAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,oDAAoD,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAE,CAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,WAAU,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,0CAA0C,CAAC,GAAE,CAAC;", "names": ["a", "l", "e", "y"]}