import {
  o
} from "./chunk-VYWZHTOQ.js";
import {
  G
} from "./chunk-DXGQBULN.js";
import {
  D
} from "./chunk-4M3AMTD4.js";

// node_modules/@arcgis/core/views/3d/support/engineContent/sdfPrimitives.js
var e = 128;
var a = 0.5;
function o2(t, o3 = e, c2 = o3 * a, s2 = 0) {
  const i2 = u(t, o3, c2, s2);
  return new G(i2, { mipmap: false, wrap: { s: D.CLAMP_TO_EDGE, t: D.CLAMP_TO_EDGE }, width: o3, height: o3, components: 4, noUnpackFlip: true });
}
function u(t, n = e, r = n * a, o3 = 0) {
  switch (t) {
    case "circle":
    default:
      return c(n, r);
    case "square":
      return s(n, r);
    case "cross":
      return h(n, r, o3);
    case "x":
      return f(n, r, o3);
    case "kite":
      return i(n, r);
    case "triangle":
      return M(n, r);
    case "arrow":
      return m(n, r);
  }
}
function c(t, n) {
  const r = t / 2 - 0.5;
  return w(t, T(r, r, n / 2));
}
function s(t, n) {
  return l(t, n, false);
}
function i(t, n) {
  return l(t, n, true);
}
function h(t, n, r = 0) {
  return p(t, n, false, r);
}
function f(t, n, r = 0) {
  return p(t, n, true, r);
}
function M(t, n) {
  return w(t, b(t / 2, n, n / 2));
}
function m(t, n) {
  const r = n, e2 = n / 2, a2 = t / 2, o3 = 0.8 * r, u2 = T(a2, (t - n) / 2 - o3, Math.sqrt(o3 * o3 + e2 * e2)), c2 = b(a2, r, e2);
  return w(t, (t2, n2) => Math.max(c2(t2, n2), -u2(t2, n2)));
}
function l(t, n, r) {
  return r && (n /= Math.SQRT2), w(t, (e2, a2) => {
    let o3 = e2 - 0.5 * t + 0.25, u2 = 0.5 * t - a2 - 0.75;
    if (r) {
      const t2 = (o3 + u2) / Math.SQRT2;
      u2 = (u2 - o3) / Math.SQRT2, o3 = t2;
    }
    return Math.max(Math.abs(o3), Math.abs(u2)) - 0.5 * n;
  });
}
function p(t, n, r, e2 = 0) {
  n -= e2, r && (n *= Math.SQRT2);
  const a2 = 0.5 * n;
  return w(t, (n2, o3) => {
    let u2, c2 = n2 - 0.5 * t, s2 = 0.5 * t - o3 - 1;
    if (r) {
      const t2 = (c2 + s2) / Math.SQRT2;
      s2 = (s2 - c2) / Math.SQRT2, c2 = t2;
    }
    return c2 = Math.abs(c2), s2 = Math.abs(s2), u2 = c2 > s2 ? c2 > a2 ? Math.sqrt((c2 - a2) * (c2 - a2) + s2 * s2) : s2 : s2 > a2 ? Math.sqrt(c2 * c2 + (s2 - a2) * (s2 - a2)) : c2, u2 -= e2 / 2, u2;
  });
}
function T(t, n, r) {
  return (e2, a2) => {
    const o3 = e2 - t, u2 = a2 - n;
    return Math.sqrt(o3 * o3 + u2 * u2) - r;
  };
}
function b(t, n, r) {
  const e2 = Math.sqrt(n * n + r * r);
  return (a2, o3) => {
    const u2 = Math.abs(a2 - t) - r, c2 = o3 - t + n / 2 + 0.75, s2 = (n * u2 + r * c2) / e2, i2 = -c2;
    return Math.max(s2, i2);
  };
}
function w(n, r) {
  const e2 = new Uint8Array(4 * n * n);
  for (let a2 = 0; a2 < n; a2++) for (let o3 = 0; o3 < n; o3++) {
    const u2 = o3 + n * a2;
    let c2 = r(o3, a2);
    c2 = c2 / n + 0.5, o(c2, e2, 4 * u2);
  }
  return e2;
}

export {
  e,
  a,
  o2 as o
};
//# sourceMappingURL=chunk-M3F5NDOQ.js.map
