{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/tileRenderers/BaseTileRenderer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../../core/HandleOwner.js\";import{property as s}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as r}from\"../../../../../core/accessorSupport/decorators/subclass.js\";let i=class extends t{constructor(e){super(e),this.tiles=new Map}destroy(){this.tiles.clear(),this.layer=this.layerView=this.tileInfoView=this.tiles=null}get updating(){return this.isUpdating()}acquireTile(e){const t=this.createTile(e);return t.once(\"isReady\",(()=>this.notifyChange(\"updating\"))),this.tiles.set(e.id,t),t}forceAttributeTextureUpload(){}forEachTile(e){this.tiles.forEach(e)}releaseTile(e){this.tiles.delete(e.key.id),this.disposeTile(e)}isUpdating(){let e=!0;return this.tiles.forEach((t=>{e=e&&t.isReady})),!e}setHighlight(){}invalidateLabels(){}requestUpdate(){this.layerView.requestUpdate()}};e([s()],i.prototype,\"layer\",void 0),e([s()],i.prototype,\"layerView\",void 0),e([s()],i.prototype,\"tileInfoView\",void 0),e([s()],i.prototype,\"updating\",null),i=e([r(\"esri.views.2d.layers.features.tileRenderers.BaseTileRenderer\")],i);const o=i;export{o as default};\n"], "mappings": ";;;;;;;;;;;;AAIwY,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,MAAM,GAAE,KAAK,QAAM,KAAK,YAAU,KAAK,eAAa,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAM,IAAE,KAAK,WAAWA,EAAC;AAAE,WAAO,EAAE,KAAK,WAAW,MAAI,KAAK,aAAa,UAAU,CAAE,GAAE,KAAK,MAAM,IAAIA,GAAE,IAAG,CAAC,GAAE;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,MAAM,OAAOA,GAAE,IAAI,EAAE,GAAE,KAAK,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,QAAIA,KAAE;AAAG,WAAO,KAAK,MAAM,QAAS,OAAG;AAAC,MAAAA,KAAEA,MAAG,EAAE;AAAA,IAAO,CAAE,GAAE,CAACA;AAAA,EAAC;AAAA,EAAC,eAAc;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,SAAK,UAAU,cAAc;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,8DAA8D,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["e"]}