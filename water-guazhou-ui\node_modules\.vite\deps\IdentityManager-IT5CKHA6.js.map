{"version": 3, "sources": ["../../@arcgis/core/identity/IdentityForm.js", "../../tabbable/src/index.js", "../../focus-trap/index.js", "../../@arcgis/core/identity/IdentityModal.js", "../../@arcgis/core/identity/OAuthCredential.js", "../../@arcgis/core/identity/OAuthInfo.js", "../../@arcgis/core/identity/ServerInfo.js", "../../@arcgis/core/identity/IdentityManagerBase.js", "../../@arcgis/core/identity/IdentityManager.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as s}from\"../chunks/tslib.es6.js\";import{property as e}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";import{substitute as r}from\"../intl/substitute.js\";import o from\"../widgets/Widget.js\";import{storeNode as i}from\"../widgets/support/widgetUtils.js\";import{messageBundle as n}from\"../widgets/support/decorators/messageBundle.js\";import{tsx as u}from\"../widgets/support/jsxFactory.js\";const a=\"esri-identity-form\",l={base:a,group:`${a}__group`,label:`${a}__label`,footer:`${a}__footer`,esriInput:\"esri-input\",esriButton:\"esri-button\",esriButtonSecondary:\"esri-button--secondary\"},p=\"ArcGIS Online\";let d=class extends o{constructor(s,e){super(s,e),this._usernameInputNode=null,this._passwordInputNode=null,this.signingIn=!1,this.server=null,this.resource=null,this.error=null,this.oAuthPrompt=!1}render(){const{error:s,server:e,resource:t,signingIn:o,oAuthPrompt:n,messages:a}=this,d=u(\"div\",{class:l.group},r(n?a.oAuthInfo:a.info,{server:e&&/\\.arcgis\\.com/i.test(e)?p:e,resource:`(${t||a.lblItem})`})),c=n?null:u(\"div\",{class:l.group,key:\"username\"},u(\"label\",{class:l.label},a.lblUser,u(\"input\",{value:\"\",required:!0,autocomplete:\"off\",spellcheck:!1,type:\"text\",bind:this,afterCreate:i,\"data-node-ref\":\"_usernameInputNode\",class:l.esriInput}))),m=n?null:u(\"div\",{class:l.group,key:\"password\"},u(\"label\",{class:l.label},a.lblPwd,u(\"input\",{value:\"\",required:!0,type:\"password\",bind:this,afterCreate:i,\"data-node-ref\":\"_passwordInputNode\",class:l.esriInput}))),h=u(\"div\",{class:this.classes(l.group,l.footer)},u(\"input\",{type:\"submit\",disabled:!!o,value:o?a.lblSigning:a.lblOk,class:l.esriButton}),u(\"input\",{type:\"button\",value:a.lblCancel,bind:this,onclick:this._cancel,class:this.classes(l.esriButton,l.esriButtonSecondary)})),b=s?u(\"div\",null,s.details&&s.details.httpStatus?a.invalidUser:a.noAuthService):null;return u(\"form\",{class:l.base,bind:this,onsubmit:this._submit},d,b,c,m,h)}_cancel(){this._set(\"signingIn\",!1),this._usernameInputNode&&(this._usernameInputNode.value=\"\"),this._passwordInputNode&&(this._passwordInputNode.value=\"\"),this.emit(\"cancel\")}_submit(s){s.preventDefault(),this._set(\"signingIn\",!0);const e=this.oAuthPrompt?{}:{username:this._usernameInputNode&&this._usernameInputNode.value,password:this._passwordInputNode&&this._passwordInputNode.value};this.emit(\"submit\",e)}};s([e(),n(\"esri/identity/t9n/identity\")],d.prototype,\"messages\",void 0),s([e()],d.prototype,\"signingIn\",void 0),s([e()],d.prototype,\"server\",void 0),s([e()],d.prototype,\"resource\",void 0),s([e()],d.prototype,\"error\",void 0),s([e()],d.prototype,\"oAuthPrompt\",void 0),d=s([t(\"esri.identity.IdentityForm\")],d);const c=d;export{c as default};\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n", "import { tabbable, focusable, isFocusable, isTabbable } from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e.key === 'Tab' || e.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element) {\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined) {\n      // option not specified: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode:\n          tabbableNodes.length > 0\n            ? tabbableNodes[tabbableNodes.length - 1]\n            : null,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          // NOTE: If tabindex is positive (in order to manipulate the tab order separate\n          //  from the DOM order), this __will not work__ because the list of focusableNodes,\n          //  while it contains tabbable nodes, does not sort its nodes in any order other\n          //  than DOM order, because it can't: Where would you place focusable (but not\n          //  tabbable) nodes in that order? They have no order, because they aren't tabbale...\n          // Support for positive tabindex is already broken and hard to manage (possibly\n          //  not supportable, TBD), so this isn't going to make things worse than they\n          //  already are, and at least makes things better for the majority of cases where\n          //  tabindex is either 0/unset or negative.\n          // FYI, positive tabindex issue: https://github.com/focus-trap/focus-trap/issues/375\n          const nodeIdx = focusableNodes.findIndex((n) => n === node);\n          if (nodeIdx < 0) {\n            return undefined;\n          }\n\n          if (forward) {\n            return focusableNodes\n              .slice(nodeIdx + 1)\n              .find((n) => isTabbable(n, config.tabbableOptions));\n          }\n\n          return focusableNodes\n            .slice(0, nodeIdx)\n            .reverse()\n            .find((n) => isTabbable(n, config.tabbableOptions));\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === doc.activeElement) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus:\n          config.returnFocusOnDeactivate &&\n          !isFocusable(target, config.tabbableOptions),\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  const checkFocusIn = function (e) {\n    const target = getActualTarget(e);\n    const targetContained = findContainerIndex(target) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    const target = getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.firstTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      if (onActivate) {\n        onActivate();\n      }\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      if (onDeactivate) {\n        onDeactivate();\n      }\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = true;\n      removeListeners();\n\n      return this;\n    },\n\n    unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{makeHandle as e}from\"../core/handleUtils.js\";import{watch as o}from\"../core/reactiveUtils.js\";import{property as s}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as i}from\"../core/accessorSupport/decorators/subclass.js\";import r from\"../widgets/Widget.js\";import{isWidget as n}from\"../widgets/support/widget.js\";import{createFocusTrap as a}from\"focus-trap\";import{tsx as c}from\"../widgets/support/jsxFactory.js\";import{messageBundle as l}from\"../widgets/support/decorators/messageBundle.js\";const d=\"esri-identity-modal\",p={base:d,open:`${d}--open`,closed:`${d}--closed`,title:`${d}__title`,dialog:`${d}__dialog`,content:`${d}__content`,closeButton:`${d}__close-button`,iconClose:\"esri-icon-close\"};let u=class extends r{constructor(t,e){super(t,e),this.container=document.createElement(\"div\"),this.content=null,this.open=!1,this._focusTrap=null,this._close=()=>{this.open=!1},document.body.appendChild(this.container),this.addHandles(o((()=>this.open),(()=>this._toggleFocusTrap())))}destroy(){this._destroyFocusTrap()}get title(){return this.messages?.auth.signIn}render(){const t=this.id,{open:e,content:o,title:s,messages:i}=this,r=e&&!!o,n={[p.open]:r,[p.closed]:!r},a=c(\"button\",{class:p.closeButton,\"aria-label\":i.close,title:i.close,bind:this,onclick:this._close,type:\"button\"},c(\"span\",{\"aria-hidden\":\"true\",class:p.iconClose})),l=`${t}_title`,d=`${t}_content`,u=s?c(\"h1\",{id:l,class:p.title},s):null,h=r?c(\"div\",{bind:this,class:p.dialog,role:\"dialog\",\"aria-labelledby\":l,\"aria-describedby\":d,afterCreate:this._createFocusTrap},a,u,this._renderContent(d)):null;return c(\"div\",{tabIndex:-1,class:this.classes(p.base,n)},h)}_destroyFocusTrap(){this._focusTrap?.deactivate({onDeactivate:()=>{}}),this._focusTrap=null}_toggleFocusTrap(){const{_focusTrap:t,open:e}=this;t&&(e?t.activate():t.deactivate())}_createFocusTrap(t){this._destroyFocusTrap();const o=requestAnimationFrame((()=>{this._focusTrap=a(t,{initialFocus:\"input\",onDeactivate:this._close}),this._toggleFocusTrap()}));this.addHandles(e((()=>cancelAnimationFrame(o))))}_renderContent(t){const e=this.content;return\"string\"==typeof e?c(\"div\",{class:p.content,id:t,innerHTML:e}):n(e)?c(\"div\",{class:p.content,id:t},e.render()):e instanceof HTMLElement?c(\"div\",{class:p.content,id:t,bind:e,afterCreate:this._attachToNode}):null}_attachToNode(t){const e=this;t.appendChild(e)}};t([s({readOnly:!0})],u.prototype,\"container\",void 0),t([s()],u.prototype,\"content\",void 0),t([s()],u.prototype,\"open\",void 0),t([s(),l(\"esri/t9n/common\")],u.prototype,\"messages\",void 0),t([s()],u.prototype,\"title\",null),u=t([i(\"esri.identity.IdentityModal\")],u);const h=u;export{h as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst t=\"esriJSAPIOAuth\";class e{constructor(t,e){this.oAuthInfo=null,this.storage=null,this.appId=null,this.codeVerifier=null,this.expires=null,this.refreshToken=null,this.ssl=null,this.stateUID=null,this.token=null,this.userId=null,this.oAuthInfo=t,this.storage=e,this._init()}isValid(){let t=!1;if(this.oAuthInfo&&this.userId&&(this.refreshToken||this.token))if(null==this.expires&&this.refreshToken)t=!0;else if(this.expires){const e=Date.now();if(this.expires>e){(this.expires-e)/1e3>60*this.oAuthInfo.minTimeUntilExpiration&&(t=!0)}}return t}save(){if(!this.storage)return!1;const e=this._load(),s=this.oAuthInfo;if(s&&s.authNamespace&&s.portalUrl){let r=e[s.authNamespace];r||(r=e[s.authNamespace]={}),this.appId||(this.appId=s.appId),r[s.portalUrl]={appId:this.appId,codeVerifier:this.codeVerifier,expires:this.expires,refreshToken:this.refreshToken,ssl:this.ssl,stateUID:this.stateUID,token:this.token,userId:this.userId};try{this.storage.setItem(t,JSON.stringify(e))}catch(i){return console.warn(i),!1}return!0}return!1}destroy(){const e=this._load(),s=this.oAuthInfo;if(s&&s.appId&&s.portalUrl&&(null==this.expires||this.expires>Date.now())&&(this.refreshToken||this.token)){const t=s.portalUrl.replace(/^http:/i,\"https:\")+\"/sharing/rest/oauth2/revokeToken\",e=new FormData;if(e.append(\"f\",\"json\"),e.append(\"auth_token\",this.refreshToken||this.token),e.append(\"client_id\",s.appId),e.append(\"token_type_hint\",this.refreshToken?\"refresh_token\":\"access_token\"),\"function\"==typeof navigator.sendBeacon)navigator.sendBeacon(t,e);else{const s=new XMLHttpRequest;s.open(\"POST\",t),s.send(e)}}if(s&&s.authNamespace&&s.portalUrl&&this.storage){const r=e[s.authNamespace];if(r){delete r[s.portalUrl];try{this.storage.setItem(t,JSON.stringify(e))}catch(i){console.log(i)}}}s&&(s._oAuthCred=null,this.oAuthInfo=null)}_init(){const t=this._load(),e=this.oAuthInfo;if(e&&e.authNamespace&&e.portalUrl){let s=t[e.authNamespace];s&&(s=s[e.portalUrl],s&&(this.appId=s.appId,this.codeVerifier=s.codeVerifier,this.expires=s.expires,this.refreshToken=s.refreshToken,this.ssl=s.ssl,this.stateUID=s.stateUID,this.token=s.token,this.userId=s.userId))}}_load(){let e={};if(this.storage){const i=this.storage.getItem(t);if(i)try{e=JSON.parse(i)}catch(s){console.warn(s)}}return e}}e.prototype.declaredClass=\"esri.identity.OAuthCredential\";export{e as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../core/JSONSupport.js\";import{property as r}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as e}from\"../core/accessorSupport/decorators/subclass.js\";var p;let s=p=class extends t{constructor(o){super(o),this._oAuthCred=null,this.appId=null,this.authNamespace=\"/\",this.expiration=20160,this.flowType=\"auto\",this.forceLogin=!1,this.forceUserId=!1,this.locale=null,this.minTimeUntilExpiration=30,this.popup=!1,this.popupCallbackUrl=\"oauth-callback.html\",this.popupWindowFeatures=\"height=490,width=800,resizable,scrollbars,status\",this.portalUrl=\"https://www.arcgis.com\",this.preserveUrlHash=!1,this.userId=null}clone(){return p.fromJSON(this.toJSON())}};o([r({json:{write:!0}})],s.prototype,\"appId\",void 0),o([r({json:{write:!0}})],s.prototype,\"authNamespace\",void 0),o([r({json:{write:!0}})],s.prototype,\"expiration\",void 0),o([r({json:{write:!0}})],s.prototype,\"flowType\",void 0),o([r({json:{write:!0}})],s.prototype,\"forceLogin\",void 0),o([r({json:{write:!0}})],s.prototype,\"forceUserId\",void 0),o([r({json:{write:!0}})],s.prototype,\"locale\",void 0),o([r({json:{write:!0}})],s.prototype,\"minTimeUntilExpiration\",void 0),o([r({json:{write:!0}})],s.prototype,\"popup\",void 0),o([r({json:{write:!0}})],s.prototype,\"popupCallbackUrl\",void 0),o([r({json:{write:!0}})],s.prototype,\"popupWindowFeatures\",void 0),o([r({json:{write:!0}})],s.prototype,\"portalUrl\",void 0),o([r({json:{write:!0}})],s.prototype,\"preserveUrlHash\",void 0),o([r({json:{write:!0}})],s.prototype,\"userId\",void 0),s=p=o([e(\"esri.identity.OAuthInfo\")],s);const i=s;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as o}from\"../chunks/tslib.es6.js\";import{JSONSupport as r}from\"../core/JSONSupport.js\";import{property as e}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as t}from\"../core/accessorSupport/decorators/subclass.js\";let s=class extends r{constructor(o){super(o),this.adminTokenServiceUrl=null,this.currentVersion=null,this.hasPortal=null,this.hasServer=null,this.owningSystemUrl=null,this.owningTenant=null,this.server=null,this.shortLivedTokenValidity=null,this.tokenServiceUrl=null,this.webTierAuth=null}};o([e({json:{write:!0}})],s.prototype,\"adminTokenServiceUrl\",void 0),o([e({json:{write:!0}})],s.prototype,\"currentVersion\",void 0),o([e({json:{write:!0}})],s.prototype,\"hasPortal\",void 0),o([e({json:{write:!0}})],s.prototype,\"hasServer\",void 0),o([e({json:{write:!0}})],s.prototype,\"owningSystemUrl\",void 0),o([e({json:{write:!0}})],s.prototype,\"owningTenant\",void 0),o([e({json:{write:!0}})],s.prototype,\"server\",void 0),o([e({json:{write:!0}})],s.prototype,\"shortLivedTokenValidity\",void 0),o([e({json:{write:!0}})],s.prototype,\"tokenServiceUrl\",void 0),o([e({json:{write:!0}})],s.prototype,\"webTierAuth\",void 0),s=o([t(\"esri.identity.ServerInfo\")],s);const i=s;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../config.js\";import{id as r}from\"../kernel.js\";import s from\"../request.js\";import i from\"../core/Error.js\";import o from\"../core/Evented.js\";import{on as n}from\"../core/events.js\";import{fixJson as a}from\"../core/lang.js\";import{getDeepValue as h}from\"../core/object.js\";import{createResolver as l,onAbort as c,isAborted as d}from\"../core/promiseUtils.js\";import{watch as u}from\"../core/reactiveUtils.js\";import{urlToObject as p,hasSameOrigin as _,Url as f,queryToObject as g,base64UrlEncode as m,objectToQuery as v,normalize as S,getProxyRule as w,makeAbsolute as I,addQueryParameters as A}from\"../core/urlUtils.js\";import{property as k}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import{subclass as y}from\"../core/accessorSupport/decorators/subclass.js\";import U from\"./IdentityForm.js\";import T from\"./IdentityModal.js\";import x from\"./OAuthCredential.js\";import O from\"./OAuthInfo.js\";import P from\"./ServerInfo.js\";import{isSecureProxyService as R}from\"../portal/support/urlUtils.js\";const C={},b=e=>{const t=new f(e.owningSystemUrl).host,r=new f(e.server).host,s=/.+\\.arcgis\\.com$/i;return s.test(t)&&s.test(r)},D=(e,t)=>!!(b(e)&&t&&t.some((t=>t.test(e.server))));let q=null,j=null;try{q=window.localStorage,j=window.sessionStorage}catch{}class E extends o{constructor(){super(),this._portalConfig=globalThis.esriGeowConfig,this.serverInfos=[],this.oAuthInfos=[],this.credentials=[],this._soReqs=[],this._xoReqs=[],this._portals=[],this._defaultOAuthInfo=null,this._defaultTokenValidity=60,this.dialog=null,this.formConstructor=U,this.tokenValidity=null,this.normalizeWebTierAuth=!1,this._appOrigin=\"null\"!==window.origin?window.origin:window.location.origin,this._appUrlObj=p(window.location.href),this._busy=null,this._rejectOnPersistedPageShow=!1,this._oAuthLocationParams=null,this._gwTokenUrl=\"/sharing/rest/generateToken\",this._agsRest=\"/rest/services\",this._agsPortal=/\\/sharing(\\/|$)/i,this._agsAdmin=/(https?:\\/\\/[^\\/]+\\/[^\\/]+)\\/admin\\/?(\\/.*)?$/i,this._adminSvcs=/\\/rest\\/admin\\/services(\\/|$)/i,this._gwDomains=[{regex:/^https?:\\/\\/www\\.arcgis\\.com/i,customBaseUrl:\"maps.arcgis.com\",tokenServiceUrl:\"https://www.arcgis.com/sharing/rest/generateToken\"},{regex:/^https?:\\/\\/(?:dev|[a-z\\d-]+\\.mapsdev)\\.arcgis\\.com/i,customBaseUrl:\"mapsdev.arcgis.com\",tokenServiceUrl:\"https://dev.arcgis.com/sharing/rest/generateToken\"},{regex:/^https?:\\/\\/(?:devext|[a-z\\d-]+\\.mapsdevext)\\.arcgis\\.com/i,customBaseUrl:\"mapsdevext.arcgis.com\",tokenServiceUrl:\"https://devext.arcgis.com/sharing/rest/generateToken\"},{regex:/^https?:\\/\\/(?:qaext|[a-z\\d-]+\\.mapsqa)\\.arcgis\\.com/i,customBaseUrl:\"mapsqa.arcgis.com\",tokenServiceUrl:\"https://qaext.arcgis.com/sharing/rest/generateToken\"},{regex:/^https?:\\/\\/[a-z\\d-]+\\.maps\\.arcgis\\.com/i,customBaseUrl:\"maps.arcgis.com\",tokenServiceUrl:\"https://www.arcgis.com/sharing/rest/generateToken\"}],this._legacyFed=[],this._regexSDirUrl=/http.+\\/rest\\/services\\/?/gi,this._regexServerType=/(\\/(FeatureServer|GPServer|GeoDataServer|GeocodeServer|GeoenrichmentServer|GeometryServer|GlobeServer|ImageServer|KnowledgeGraphServer|MapServer|MissionServer|MobileServer|NAServer|NetworkDiagramServer|OGCFeatureServer|ParcelFabricServer|RelationalCatalogServer|SceneServer|StreamServer|UtilityNetworkServer|ValidationServer|VectorTileServer|VersionManagementServer|VideoServer)).*/gi,this._gwUser=/http.+\\/users\\/([^\\/]+)\\/?.*/i,this._gwItem=/http.+\\/items\\/([^\\/]+)\\/?.*/i,this._gwGroup=/http.+\\/groups\\/([^\\/]+)\\/?.*/i,this._rePortalTokenSvc=/\\/sharing(\\/rest)?\\/generatetoken/i,this._createDefaultOAuthInfo=!0,this._hasTestedIfAppIsOnPortal=!1,this._getOAuthLocationParams(),window.addEventListener(\"pageshow\",(e=>{this._pageShowHandler(e)}))}registerServers(e){const t=this.serverInfos;t?(e=e.filter((e=>!this.findServerInfo(e.server))),this.serverInfos=t.concat(e)):this.serverInfos=e,e.forEach((e=>{e.owningSystemUrl&&this._portals.push(e.owningSystemUrl),e.hasPortal&&this._portals.push(e.server)}))}registerOAuthInfos(e){const t=this.oAuthInfos;if(t){for(const r of e){const e=this.findOAuthInfo(r.portalUrl);e&&t.splice(t.indexOf(e),1)}this.oAuthInfos=t.concat(e)}else this.oAuthInfos=e}registerToken(e){e={...e};const t=this._sanitizeUrl(e.server),r=this._isServerRsrc(t);let s,i=this.findServerInfo(t),o=!0;i||(i=new P,i.server=this._getServerInstanceRoot(t),r?i.hasServer=!0:(i.tokenServiceUrl=this._getTokenSvcUrl(t),i.hasPortal=!0),this.registerServers([i])),s=this._findCredential(t),s?(delete e.server,Object.assign(s,e),o=!1):(s=new L({userId:e.userId,server:i.server,token:e.token,expires:e.expires,ssl:e.ssl,scope:r?\"server\":\"portal\"}),s.resources=[t],this.credentials.push(s)),s.emitTokenChange(!1),o||s.refreshServerTokens()}toJSON(){return a({serverInfos:this.serverInfos.map((e=>e.toJSON())),oAuthInfos:this.oAuthInfos.map((e=>e.toJSON())),credentials:this.credentials.map((e=>e.toJSON()))})}initialize(e){if(!e)return;\"string\"==typeof e&&(e=JSON.parse(e));const t=e.serverInfos,r=e.oAuthInfos,s=e.credentials;if(t){const e=[];t.forEach((t=>{t.server&&t.tokenServiceUrl&&e.push(t.declaredClass?t:new P(t))})),e.length&&this.registerServers(e)}if(r){const e=[];r.forEach((t=>{t.appId&&e.push(t.declaredClass?t:new O(t))})),e.length&&this.registerOAuthInfos(e)}s&&s.forEach((e=>{e.server&&e.token&&e.expires&&e.expires>Date.now()&&((e=e.declaredClass?e:new L(e)).emitTokenChange(),this.credentials.push(e))}))}findServerInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.serverInfos)if(this._hasSameServerInstance(r.server,e)){t=r;break}return t}findOAuthInfo(e){let t;e=this._sanitizeUrl(e);for(const r of this.oAuthInfos)if(this._hasSameServerInstance(r.portalUrl,e)){t=r;break}return t}findCredential(e,t){if(!e)return;let r;e=this._sanitizeUrl(e);const s=this._isServerRsrc(e)?\"server\":\"portal\";if(t){for(const i of this.credentials)if(this._hasSameServerInstance(i.server,e)&&t===i.userId&&i.scope===s){r=i;break}}else for(const i of this.credentials)if(this._hasSameServerInstance(i.server,e)&&-1!==this._getIdenticalSvcIdx(e,i)&&i.scope===s){r=i;break}return r}getCredential(e,t){let r,s,o=!0;t&&(r=!!t.token,s=t.error,o=!1!==t.prompt),t={...t},e=this._sanitizeUrl(e);const n=new AbortController,a=l();if(t.signal&&c(t.signal,(()=>{n.abort()})),c(n,(()=>{a.reject(new i(\"identity-manager:user-aborted\",\"ABORTED\"))})),d(n))return a.promise;t.signal=n.signal;const h=this._isAdminResource(e),u=r?this.findCredential(e):null;let p;if(u&&s&&s.details&&498===s.details.httpStatus)u.destroy();else if(u)return p=new i(\"identity-manager:not-authorized\",\"You are currently signed in as: '\"+u.userId+\"'. You do not have access to this resource: \"+e,{error:s}),a.reject(p),a.promise;const f=this._findCredential(e,t);if(f)return a.resolve(f),a.promise;let g=this.findServerInfo(e);if(g)!g.hasServer&&this._isServerRsrc(e)&&(g._restInfoPms=this._getTokenSvcUrl(e),g.hasServer=!0);else{const t=this._getTokenSvcUrl(e);if(!t)return p=new i(\"identity-manager:unknown-resource\",\"Unknown resource - could not find token service endpoint.\"),a.reject(p),a.promise;g=new P,g.server=this._getServerInstanceRoot(e),\"string\"==typeof t?(g.tokenServiceUrl=t,g.hasPortal=!0):(g._restInfoPms=t,g.hasServer=!0),this.registerServers([g])}return g.hasPortal&&void 0===g._selfReq&&(o||_(g.tokenServiceUrl,this._appOrigin)||this._gwDomains.some((e=>e.tokenServiceUrl===g.tokenServiceUrl)))&&(g._selfReq={owningTenant:t&&t.owningTenant,selfDfd:this._getPortalSelf(g.tokenServiceUrl.replace(this._rePortalTokenSvc,\"/sharing/rest/portals/self\"),e)}),this._enqueue(e,g,t,a,h)}getResourceName(e){return this._isRESTService(e)?e.replace(this._regexSDirUrl,\"\").replace(this._regexServerType,\"\")||\"\":this._gwUser.test(e)&&e.replace(this._gwUser,\"$1\")||this._gwItem.test(e)&&e.replace(this._gwItem,\"$1\")||this._gwGroup.test(e)&&e.replace(this._gwGroup,\"$1\")||\"\"}generateToken(e,t,r){const o=this._rePortalTokenSvc.test(e.tokenServiceUrl),n=new f(this._appOrigin),a=e.shortLivedTokenValidity;let h,l,c,d,u,p,g,m;t&&(m=this.tokenValidity||a||this._defaultTokenValidity,m>a&&a>0&&(m=a)),r&&(h=r.isAdmin,l=r.serverUrl,c=r.token,p=r.signal,g=r.ssl,e.customParameters=r.customParameters),h?d=e.adminTokenServiceUrl:(d=e.tokenServiceUrl,u=new f(d.toLowerCase()),e.webTierAuth&&r?.serverUrl&&!g&&\"http\"===n.scheme&&(_(n.uri,d,!0)||\"https\"===u.scheme&&n.host===u.host&&\"7080\"===n.port&&\"7443\"===u.port)&&(d=d.replace(/^https:/i,\"http:\").replace(/:7443/i,\":7080\")));const v={query:{request:\"getToken\",username:t?.username,password:t?.password,serverUrl:l,token:c,expiration:m,referer:h||o?this._appOrigin:null,client:h?\"referer\":null,f:\"json\",...e.customParameters},method:\"post\",authMode:\"anonymous\",useProxy:this._useProxy(e,r),signal:p,...r?.ioArgs};o||(v.withCredentials=!1);return s(d,v).then((r=>{const s=r.data;if(!s||!s.token)return new i(\"identity-manager:authentication-failed\",\"Unable to generate token\");const o=e.server;return C[o]||(C[o]={}),t&&(C[o][t.username]=t.password),s.validity=m,s}))}isBusy(){return!!this._busy}checkSignInStatus(e){return this.checkAppAccess(e,\"\").then((e=>e.credential))}checkAppAccess(e,t,r){let o=!1;return this.getCredential(e,{prompt:!1}).then((n=>{let a;const h={f:\"json\"};if(\"portal\"===n.scope)if(t&&(this._doPortalSignIn(e)||r&&r.force))a=n.server+\"/sharing/rest/oauth2/validateAppAccess\",h.client_id=t;else{if(!n.token)return{credential:n};a=n.server+\"/sharing/rest\"}else{if(!n.token)return{credential:n};a=n.server+\"/rest/services\"}return n.token&&(h.token=n.token),s(a,{query:h,authMode:\"anonymous\"}).then((e=>{if(!1===e.data.valid)throw new i(\"identity-manager:not-authorized\",`You are currently signed in as: '${n.userId}'.`,e.data);return o=!!e.data.viewOnlyUserTypeApp,{credential:n}})).catch((e=>{if(\"identity-manager:not-authorized\"===e.name)throw e;const t=e.details&&e.details.httpStatus;if(498===t)throw n.destroy(),new i(\"identity-manager:not-authenticated\",\"User is not signed in.\");if(400===t)throw new i(\"identity-manager:invalid-request\");return{credential:n}}))})).then((e=>({credential:e.credential,viewOnly:o})))}setOAuthResponseHash(e){e&&(\"#\"===e.charAt(0)&&(e=e.substring(1)),this._processOAuthPopupParams(g(e)))}setOAuthRedirectionHandler(e){this._oAuthRedirectFunc=e}setProtocolErrorHandler(e){this._protocolFunc=e}signIn(e,t,r={}){const s=l(),o=()=>{h?.remove(),d?.remove(),p?.remove(),a?.destroy(),this.dialog?.destroy(),this.dialog=a=h=d=p=null},n=()=>{o(),this._oAuthDfd=null,s.reject(new i(\"identity-manager:user-aborted\",\"ABORTED\"))};r.signal&&c(r.signal,(()=>{n()}));let a=new this.formConstructor;a.resource=this.getResourceName(e),a.server=t.server,this.dialog=new T,this.dialog.content=a,this.dialog.open=!0,this.emit(\"dialog-create\");let h=a.on(\"cancel\",n),d=u((()=>this.dialog.open),n),p=a.on(\"submit\",(e=>{this.generateToken(t,e,{isAdmin:r.isAdmin,signal:r.signal}).then((i=>{o();const n=new L({userId:e.username,server:t.server,token:i.token,expires:null!=i.expires?Number(i.expires):null,ssl:!!i.ssl,isAdmin:r.isAdmin,validity:i.validity});s.resolve(n)})).catch((e=>{a.error=e,a.signingIn=!1}))}));return s.promise}oAuthSignIn(e,t,r,s){this._oAuthDfd=l();const o=this._oAuthDfd;let n;s?.signal&&c(s.signal,(()=>{const e=this._oAuthDfd&&this._oAuthDfd.oAuthWin_;e&&!e.closed?e.close():this.dialog&&f()})),o.resUrl_=e,o.sinfo_=t,o.oinfo_=r;const a=r._oAuthCred;if(a.storage&&(\"authorization-code\"===r.flowType||\"auto\"===r.flowType&&!r.popup&&t.currentVersion>=8.4)){let e=crypto.getRandomValues(new Uint8Array(32));n=m(e),a.codeVerifier=n,e=crypto.getRandomValues(new Uint8Array(32)),a.stateUID=m(e),a.save()||(a.codeVerifier=n=null)}else a.codeVerifier=null;let h,d,p,_;this._getCodeChallenge(n).then((i=>{const o=!s||!1!==s.oAuthPopupConfirmation;r.popup&&o?(h=new this.formConstructor,h.oAuthPrompt=!0,h.server=t.server,this.dialog=new T,this.dialog.content=h,this.dialog.open=!0,this.emit(\"dialog-create\"),d=h.on(\"cancel\",f),p=u((()=>this.dialog.open),f),_=h.on(\"submit\",(()=>{g(),this._doOAuthSignIn(e,t,r,i)}))):this._doOAuthSignIn(e,t,r,i)}));const f=()=>{g(),this._oAuthDfd=null,o.reject(new i(\"identity-manager:user-aborted\",\"ABORTED\"))},g=()=>{d?.remove(),p?.remove(),_?.remove(),h?.destroy(),this.dialog?.destroy(),this.dialog=null};return o.promise}destroyCredentials(){if(this.credentials){this.credentials.slice().forEach((e=>{e.destroy()}))}this.emit(\"credentials-destroy\")}enablePostMessageAuth(e=\"https://www.arcgis.com/sharing/rest\"){this._postMessageAuthHandle&&this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=n(window,\"message\",(t=>{if((t.origin===this._appOrigin||t.origin.endsWith(\".arcgis.com\"))&&\"arcgis:auth:requestCredential\"===t.data?.type){const r=t.source;this.getCredential(e).then((e=>{r.postMessage({type:\"arcgis:auth:credential\",credential:{expires:e.expires,server:e.server,ssl:e.ssl,token:e.token,userId:e.userId}},t.origin)})).catch((e=>{r.postMessage({type:\"arcgis:auth:error\",error:{name:e.name,message:e.message}},t.origin)}))}}))}disablePostMessageAuth(){this._postMessageAuthHandle&&(this._postMessageAuthHandle.remove(),this._postMessageAuthHandle=null)}_getOAuthLocationParams(){let e=window.location.hash;if(e){\"#\"===e.charAt(0)&&(e=e.substring(1));const t=g(e);let r=!1;if(t.access_token&&t.expires_in&&t.state&&t.hasOwnProperty(\"username\"))try{t.state=JSON.parse(t.state),t.state.portalUrl&&(this._oAuthLocationParams=t,r=!0)}catch{}else if(t.error&&t.error_description&&(console.log(\"IdentityManager OAuth Error: \",t.error,\" - \",t.error_description),\"access_denied\"===t.error&&(r=!0,t.state)))try{t.state=JSON.parse(t.state)}catch{}r&&(window.location.hash=t.state?.hash||\"\")}let t=window.location.search;if(t){\"?\"===t.charAt(0)&&(t=t.substring(1));const e=g(t);let r=!1;if(e.code&&e.state)try{e.state=JSON.parse(e.state),e.state.portalUrl&&e.state.uid&&(this._oAuthLocationParams=e,r=!0)}catch{}else if(e.error&&e.error_description&&(console.log(\"IdentityManager OAuth Error: \",e.error,\" - \",e.error_description),\"access_denied\"===e.error&&(r=!0,e.state)))try{e.state=JSON.parse(e.state)}catch{}if(r){const t={...e};[\"code\",\"error\",\"error_description\",\"message_code\",\"persist\",\"state\"].forEach((e=>{delete t[e]}));const r=v(t),s=window.location.pathname+(r?`?${r}`:\"\")+(e.state?.hash||\"\");window.history.replaceState(window.history.state,\"\",s)}}}_getOAuthToken(e,t,r,i,o){return e=e.replace(/^http:/i,\"https:\"),s(`${e}/sharing/rest/oauth2/token`,{authMode:\"anonymous\",method:\"post\",query:i&&o?{grant_type:\"authorization_code\",code:t,redirect_uri:i,client_id:r,code_verifier:o}:{grant_type:\"refresh_token\",refresh_token:t,client_id:r}}).then((e=>e.data))}_getCodeChallenge(e){if(e&&globalThis.isSecureContext){const t=(new TextEncoder).encode(e);return crypto.subtle.digest(\"SHA-256\",t).then((e=>m(new Uint8Array(e))))}return Promise.resolve(null)}_pageShowHandler(e){if(e.persisted&&this.isBusy()&&this._rejectOnPersistedPageShow){const e=new i(\"identity-manager:user-aborted\",\"ABORTED\");this._errbackFunc(e)}}_findCredential(e,t){let r,s,i,o,n=-1;const a=t&&t.token,h=t&&t.resource,l=this._isServerRsrc(e)?\"server\":\"portal\",c=this.credentials.filter((t=>this._hasSameServerInstance(t.server,e)&&t.scope===l));if(e=h||e,c.length)if(1===c.length){if(r=c[0],i=this.findServerInfo(r.server),s=i&&i.owningSystemUrl,o=s?this.findCredential(s,r.userId):void 0,n=this._getIdenticalSvcIdx(e,r),!a)return-1===n&&r.resources.push(e),this._addResource(e,o),r;-1!==n&&(r.resources.splice(n,1),this._removeResource(e,o))}else{let t,r;if(c.some((a=>(r=this._getIdenticalSvcIdx(e,a),-1!==r&&(t=a,i=this.findServerInfo(t.server),s=i&&i.owningSystemUrl,o=s?this.findCredential(s,t.userId):void 0,n=r,!0)))),a)t&&(t.resources.splice(n,1),this._removeResource(e,o));else if(t)return this._addResource(e,o),t}}_findOAuthInfo(e){let t=this.findOAuthInfo(e);if(!t)for(const r of this.oAuthInfos)if(this._isIdProvider(r.portalUrl,e)){t=r;break}return t}_addResource(e,t){t&&-1===this._getIdenticalSvcIdx(e,t)&&t.resources.push(e)}_removeResource(e,t){let r=-1;t&&(r=this._getIdenticalSvcIdx(e,t),r>-1&&t.resources.splice(r,1))}_useProxy(e,t){return t&&t.isAdmin&&!_(e.adminTokenServiceUrl,this._appOrigin)||!this._isPortalDomain(e.tokenServiceUrl)&&\"10.1\"===String(e.currentVersion)&&!_(e.tokenServiceUrl,this._appOrigin)}_getOrigin(e){const t=new f(e);return t.scheme+\"://\"+t.host+(null!=t.port?\":\"+t.port:\"\")}_getServerInstanceRoot(e){const t=e.toLowerCase();let r=t.indexOf(this._agsRest);return-1===r&&this._isAdminResource(e)&&(r=this._agsAdmin.test(e)?e.replace(this._agsAdmin,\"$1\").length:e.search(this._adminSvcs)),-1!==r||R(t)||(r=t.indexOf(\"/sharing\")),-1===r&&\"/\"===t.substr(-1)&&(r=t.length-1),r>-1?e.substring(0,r):e}_hasSameServerInstance(e,t){return\"/\"===e.substr(-1)&&(e=e.slice(0,-1)),e=e.toLowerCase(),t=this._getServerInstanceRoot(t).toLowerCase(),e=this._normalizeAGOLorgDomain(e),t=this._normalizeAGOLorgDomain(t),(e=e.substr(e.indexOf(\":\")))===(t=t.substr(t.indexOf(\":\")))}_normalizeAGOLorgDomain(e){const t=/^https?:\\/\\/(?:cdn|[a-z\\d-]+\\.maps)\\.arcgis\\.com/i,r=/^https?:\\/\\/(?:cdndev|[a-z\\d-]+\\.mapsdevext)\\.arcgis\\.com/i,s=/^https?:\\/\\/(?:cdnqa|[a-z\\d-]+\\.mapsqa)\\.arcgis\\.com/i;return t.test(e)?e=e.replace(t,\"https://www.arcgis.com\"):r.test(e)?e=e.replace(r,\"https://devext.arcgis.com\"):s.test(e)&&(e=e.replace(s,\"https://qaext.arcgis.com\")),e}_sanitizeUrl(e){const r=(t.request.proxyUrl||\"\").toLowerCase(),s=r?e.toLowerCase().indexOf(r+\"?\"):-1;return-1!==s&&(e=e.substring(s+r.length+1)),e=S(e),p(e).path}_isRESTService(e){return e.includes(this._agsRest)}_isAdminResource(e){return this._agsAdmin.test(e)||this._adminSvcs.test(e)}_isServerRsrc(e){return this._isRESTService(e)||this._isAdminResource(e)}_isIdenticalService(e,t){let r=!1;if(this._isRESTService(e)&&this._isRESTService(t)){const s=this._getSuffix(e).toLowerCase(),i=this._getSuffix(t).toLowerCase();if(r=s===i,!r){const e=/(.*)\\/(MapServer|FeatureServer|UtilityNetworkServer).*/gi;r=s.replace(e,\"$1\")===i.replace(e,\"$1\")}}else this._isAdminResource(e)&&this._isAdminResource(t)?r=!0:this._isServerRsrc(e)||this._isServerRsrc(t)||!this._isPortalDomain(e)||(r=!0);return r}_isPortalDomain(e){const r=new f(e.toLowerCase()),s=this._portalConfig;let i=this._gwDomains.some((e=>e.regex.test(r.uri)));return!i&&s&&(i=this._hasSameServerInstance(this._getServerInstanceRoot(s.restBaseUrl),r.uri)),i||t.portalUrl&&(i=_(r,t.portalUrl,!0)),i||(i=this._portals.some((e=>this._hasSameServerInstance(e,r.uri)))),i=i||this._agsPortal.test(r.path),i}_isIdProvider(e,t){let r=-1,s=-1;this._gwDomains.forEach(((i,o)=>{-1===r&&i.regex.test(e)&&(r=o),-1===s&&i.regex.test(t)&&(s=o)}));let i=!1;if(r>-1&&s>-1&&(0===r||4===r?0!==s&&4!==s||(i=!0):1===r?1!==s&&2!==s||(i=!0):2===r?2===s&&(i=!0):3===r&&3===s&&(i=!0)),!i){const r=this.findServerInfo(t),s=r&&r.owningSystemUrl;s&&b(r)&&this._isPortalDomain(s)&&this._isIdProvider(e,s)&&(i=!0)}return i}_getIdenticalSvcIdx(e,t){let r=-1;for(let s=0;s<t.resources.length;s++){const i=t.resources[s];if(this._isIdenticalService(e,i)){r=s;break}}return r}_getSuffix(e){return e.replace(this._regexSDirUrl,\"\").replace(this._regexServerType,\"$1\")}_getTokenSvcUrl(e){let t,r,i;if(this._isRESTService(e)||this._isAdminResource(e)){const i=this._getServerInstanceRoot(e);return t=i+\"/admin/generateToken\",r=s(e=i+\"/rest/info\",{query:{f:\"json\"}}).then((e=>e.data)),{adminUrl:t,promise:r}}if(this._isPortalDomain(e)){let t=\"\";if(this._gwDomains.some((r=>(r.regex.test(e)&&(t=r.tokenServiceUrl),!!t))),t||this._portals.some((r=>(this._hasSameServerInstance(r,e)&&(t=r+this._gwTokenUrl),!!t))),t||(i=e.toLowerCase().indexOf(\"/sharing\"),-1!==i&&(t=e.substring(0,i)+this._gwTokenUrl)),t||(t=this._getOrigin(e)+this._gwTokenUrl),t){const r=new f(e).port;/^http:\\/\\//i.test(e)&&\"7080\"===r&&(t=t.replace(/:7080/i,\":7443\")),t=t.replace(/http:/i,\"https:\")}return t}if(e.toLowerCase().includes(\"premium.arcgisonline.com\"))return\"https://premium.arcgisonline.com/server/tokens\"}_processOAuthResponseParams(e,t,r){const s=t._oAuthCred;if(e.code){const i=s.codeVerifier;return s.codeVerifier=null,s.stateUID=null,s.save(),this._getOAuthToken(r.server,e.code,t.appId,this._getRedirectURI(t,!0),i).then((i=>{const o=new L({userId:i.username,server:r.server,token:i.access_token,expires:Date.now()+1e3*i.expires_in,ssl:i.ssl,oAuthState:e.state,_oAuthCred:s});return t.userId=o.userId,s.storage=i.persist?q:j,s.refreshToken=i.refresh_token,s.token=null,s.expires=i.refresh_token_expires_in?Date.now()+1e3*i.refresh_token_expires_in:null,s.userId=o.userId,s.ssl=o.ssl,s.save(),o}))}const i=new L({userId:e.username,server:r.server,token:e.access_token,expires:Date.now()+1e3*Number(e.expires_in),ssl:\"true\"===e.ssl,oAuthState:e.state,_oAuthCred:s});return t.userId=i.userId,s.storage=e.persist?q:j,s.refreshToken=null,s.token=i.token,s.expires=i.expires,s.userId=i.userId,s.ssl=i.ssl,s.save(),Promise.resolve(i)}_processOAuthPopupParams(e){const t=this._oAuthDfd;if(this._oAuthDfd=null,t)if(clearInterval(this._oAuthIntervalId),this._oAuthOnPopupHandle?.remove(),e.error){const r=\"access_denied\"===e.error,s=new i(r?\"identity-manager:user-aborted\":\"identity-manager:authentication-failed\",r?\"ABORTED\":\"OAuth: \"+e.error+\" - \"+e.error_description);t.reject(s)}else this._processOAuthResponseParams(e,t.oinfo_,t.sinfo_).then((e=>{t.resolve(e)})).catch((e=>{t.reject(e)}))}_setOAuthResponseQueryString(e){e&&(\"?\"===e.charAt(0)&&(e=e.substring(1)),this._processOAuthPopupParams(g(e)))}_exchangeToken(e,t,r){return s(`${e}/sharing/rest/oauth2/exchangeToken`,{authMode:\"anonymous\",method:\"post\",query:{f:\"json\",client_id:t,token:r}}).then((e=>e.data.token))}_getPlatformSelf(e,t){return e=e.replace(/^http:/i,\"https:\"),s(`${e}/sharing/rest/oauth2/platformSelf`,{authMode:\"anonymous\",headers:{\"X-Esri-Auth-Client-Id\":t,\"X-Esri-Auth-Redirect-Uri\":window.location.href.replace(/#.*$/,\"\")},method:\"post\",query:{f:\"json\",expiration:30},withCredentials:!0}).then((e=>e.data))}_getPortalSelf(e,t){let r;if(this._gwDomains.some((t=>(t.regex.test(e)&&(r=t.customBaseUrl),!!r))),r)return Promise.resolve({allSSL:!0,currentVersion:\"8.4\",customBaseUrl:r,portalMode:\"multitenant\",supportsOAuth:!0});this._appOrigin.startsWith(\"https:\")?e=e.replace(/^http:/i,\"https:\").replace(/:7080/i,\":7443\"):/^http:/i.test(t)&&(e=e.replace(/^https:/i,\"http:\").replace(/:7443/i,\":7080\"));return s(e,{query:{f:\"json\"},authMode:\"anonymous\",withCredentials:!0}).then((e=>e.data))}_doPortalSignIn(e){const t=this._portalConfig,r=window.location.href,s=this.findServerInfo(e);return!(!t&&!this._isPortalDomain(r)||!(s?s.hasPortal||s.owningSystemUrl&&this._isPortalDomain(s.owningSystemUrl):this._isPortalDomain(e))||!(this._isIdProvider(r,e)||t&&(this._hasSameServerInstance(this._getServerInstanceRoot(t.restBaseUrl),e)||this._isIdProvider(t.restBaseUrl,e))||_(r,e,!0)))}_checkProtocol(e,t,r,s){let o=!0;const n=s?t.adminTokenServiceUrl:t.tokenServiceUrl;if(n.trim().toLowerCase().startsWith(\"https:\")&&!this._appOrigin.startsWith(\"https:\")&&w(n)&&(o=!!this._protocolFunc&&!!this._protocolFunc({resourceUrl:e,serverInfo:t}),!o)){r(new i(\"identity-manager:aborted\",\"Aborted the Sign-In process to avoid sending password over insecure connection.\"))}return o}_enqueue(e,t,r,s,i,o){return s||(s=l()),s.resUrl_=e,s.sinfo_=t,s.options_=r,s.admin_=i,s.refresh_=o,this._busy?this._hasSameServerInstance(this._getServerInstanceRoot(e),this._busy.resUrl_)?(this._oAuthDfd&&this._oAuthDfd.oAuthWin_&&this._oAuthDfd.oAuthWin_.focus(),this._soReqs.push(s)):this._xoReqs.push(s):this._doSignIn(s),s.promise}_doSignIn(e){this._busy=e,this._rejectOnPersistedPageShow=!1;const t=t=>{const r=e.options_&&e.options_.resource,s=e.resUrl_,i=e.refresh_;let o=!1;this.credentials.includes(t)||(i&&this.credentials.includes(i)?(i.userId=t.userId,i.token=t.token,i.expires=t.expires,i.validity=t.validity,i.ssl=t.ssl,i.creationTime=t.creationTime,o=!0,t=i):this.credentials.push(t)),t.resources||(t.resources=[]),t.resources.includes(r||s)||t.resources.push(r||s),t.scope=this._isServerRsrc(s)?\"server\":\"portal\",t.emitTokenChange();const n=this._soReqs,a={};this._soReqs=[],n.forEach((e=>{if(!this._isIdenticalService(s,e.resUrl_)){const r=this._getSuffix(e.resUrl_);a[r]||(a[r]=!0,t.resources.push(e.resUrl_))}})),e.resolve(t),n.forEach((e=>{this._hasSameServerInstance(this._getServerInstanceRoot(s),e.resUrl_)?e.resolve(t):this._soReqs.push(e)})),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,o||this.emit(\"credential-create\",{credential:t}),this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},r=t=>{e.reject(t),this._busy=e.resUrl_=e.sinfo_=e.refresh_=null,this._soReqs.length?this._doSignIn(this._soReqs.shift()):this._xoReqs.length&&this._doSignIn(this._xoReqs.shift())},s=(o,a,h,l)=>{const d=e.sinfo_,u=!e.options_||!1!==e.options_.prompt,p=d.hasPortal&&this._findOAuthInfo(e.resUrl_);let f,g;if(o)t(new L({userId:o,server:d.server,token:h||null,expires:null!=l?Number(l):null,ssl:!!a}));else if(window!==window.parent&&this._appUrlObj.query?.[\"arcgis-auth-origin\"]&&this._appUrlObj.query?.[\"arcgis-auth-portal\"]&&this._hasSameServerInstance(this._getServerInstanceRoot(this._appUrlObj.query[\"arcgis-auth-portal\"]),e.resUrl_)){window.parent.postMessage({type:\"arcgis:auth:requestCredential\"},this._appUrlObj.query[\"arcgis-auth-origin\"]);const s=n(window,\"message\",(e=>{e.source===window.parent&&e.data&&(\"arcgis:auth:credential\"===e.data.type?(s.remove(),e.data.credential.expires<Date.now()?r(new i(\"identity-manager:credential-request-failed\",\"Parent application's token has expired.\")):t(new L(e.data.credential))):\"arcgis:auth:error\"===e.data.type&&(s.remove(),\"tokenExpiredError\"===e.data.error.name?r(new i(\"identity-manager:credential-request-failed\",\"Parent application's token has expired.\")):r(i.fromJSON(e.data.error))))}));c(e.options_?.signal,(()=>{s.remove()}))}else if(p){let o=p._oAuthCred;if(!o){const e=new x(p,q),t=new x(p,j);e.isValid()&&t.isValid()?e.expires>t.expires?(o=e,t.destroy()):(o=t,e.destroy()):o=e.isValid()?e:t,p._oAuthCred=o}if(o.isValid()){f=new L({userId:o.userId,server:d.server,token:o.token,expires:o.expires,ssl:o.ssl,_oAuthCred:o});const r=p.appId!==o.appId&&this._doPortalSignIn(e.resUrl_);r||o.refreshToken?(e._pendingDfd=o.refreshToken?this._getOAuthToken(d.server,o.refreshToken,o.appId).then((e=>(f.expires=Date.now()+1e3*e.expires_in,f.token=e.access_token,f))):Promise.resolve(f),e._pendingDfd.then((e=>r?this._exchangeToken(e.server,p.appId,e.token).then((t=>(e.token=t,e))).catch((()=>e)):e)).then((e=>{t(e)})).catch((()=>{o?.destroy(),s()}))):t(f)}else if(this._oAuthLocationParams&&this._hasSameServerInstance(p.portalUrl,this._oAuthLocationParams.state.portalUrl)&&(this._oAuthLocationParams.access_token||this._oAuthLocationParams.code&&this._oAuthLocationParams.state.uid===o.stateUID&&o.codeVerifier)){const s=this._oAuthLocationParams;this._oAuthLocationParams=null,e._pendingDfd=this._processOAuthResponseParams(s,p,d).then((e=>{t(e)})).catch(r)}else{const s=()=>{u?e._pendingDfd=this.oAuthSignIn(e.resUrl_,d,p,e.options_).then(t,r):(g=new i(\"identity-manager:not-authenticated\",\"User is not signed in.\"),r(g))};this._doPortalSignIn(e.resUrl_)?e._pendingDfd=this._getPlatformSelf(d.server,p.appId).then((e=>{_(e.portalUrl,this._appOrigin,!0)?(f=new L({userId:e.username,server:d.server,expires:Date.now()+1e3*e.expires_in,token:e.token}),t(f)):s()})).catch(s):s()}}else if(u){if(this._checkProtocol(e.resUrl_,d,r,e.admin_)){let s=e.options_;e.admin_&&(s=s||{},s.isAdmin=!0),e._pendingDfd=this.signIn(e.resUrl_,d,s).then(t,r)}}else g=new i(\"identity-manager:not-authenticated\",\"User is not signed in.\"),r(g)},o=()=>{const s=e.sinfo_,i=s.owningSystemUrl,o=e.options_;let n,a,h,l;if(o&&(n=o.token,a=o.error,h=o.prompt),l=this._findCredential(i,{token:n,resource:e.resUrl_}),!l)for(const e of this.credentials)if(this._isIdProvider(i,e.server)){l=e;break}if(l){const i=this.findCredential(e.resUrl_,l.userId);if(i)t(i);else if(D(s,this._legacyFed)){const e=l.toJSON();e.server=s.server,e.resources=null,t(new L(e))}else{(e._pendingDfd=this.generateToken(this.findServerInfo(l.server),null,{serverUrl:e.resUrl_,token:l.token,signal:e.options_.signal,ssl:l.ssl})).then((r=>{t(new L({userId:l?.userId,server:s.server,token:r.token,expires:null!=r.expires?Number(r.expires):null,ssl:!!r.ssl,isAdmin:e.admin_,validity:r.validity}))}),r)}}else{this._busy=null,n&&(e.options_.token=null);(e._pendingDfd=this.getCredential(i.replace(/\\/?$/,\"/sharing\"),{resource:e.resUrl_,owningTenant:s.owningTenant,signal:e.options_.signal,token:n,error:a,prompt:h})).then((()=>{this._enqueue(e.resUrl_,e.sinfo_,e.options_,e,e.admin_)}),(t=>{e.resUrl_=e.sinfo_=e.refresh_=null,e.reject(t)}))}};this._errbackFunc=r;const a=e.sinfo_.owningSystemUrl,l=this._isServerRsrc(e.resUrl_),d=e.sinfo_._restInfoPms;d?d.promise.then((t=>{const r=e.sinfo_;if(r._restInfoPms){r.adminTokenServiceUrl=r._restInfoPms.adminUrl,r._restInfoPms=null,r.tokenServiceUrl=(h(\"authInfo.tokenServicesUrl\",t)||h(\"authInfo.tokenServiceUrl\",t)||h(\"tokenServiceUrl\",t))??null,r.shortLivedTokenValidity=h(\"authInfo.shortLivedTokenValidity\",t)??null,r.currentVersion=t.currentVersion,r.owningTenant=t.owningTenant;const e=r.owningSystemUrl=t.owningSystemUrl;e&&this._portals.push(e)}l&&r.owningSystemUrl?o():s()}),(()=>{e.sinfo_._restInfoPms=null;const t=new i(\"identity-manager:server-identification-failed\",\"Unknown resource - could not find token service endpoint.\");r(t)})):l&&a?o():e.sinfo_._selfReq?e.sinfo_._selfReq.selfDfd.then((t=>{const r={};let s,i,o,n;return t&&(s=t.user&&t.user.username,r.username=s,r.allSSL=t.allSSL,i=t.supportsOAuth,n=parseFloat(t.currentVersion),\"multitenant\"===t.portalMode&&(o=t.customBaseUrl),e.sinfo_.currentVersion=n),e.sinfo_.webTierAuth=!!s,s&&this.normalizeWebTierAuth?this.generateToken(e.sinfo_,null,{ssl:r.allSSL}).catch((()=>null)).then((e=>(r.portalToken=e&&e.token,r.tokenExpiration=e&&e.expires,r))):!s&&i&&n>=4.4&&!this._findOAuthInfo(e.resUrl_)?this._generateOAuthInfo({portalUrl:e.sinfo_.server,customBaseUrl:o,owningTenant:e.sinfo_._selfReq.owningTenant}).catch((()=>null)).then((()=>r)):r})).catch((()=>null)).then((t=>{e.sinfo_._selfReq=null,t?s(t.username,t.allSSL,t.portalToken,t.tokenExpiration):s()})):s()}_generateOAuthInfo(e){let t,r=null,i=e.portalUrl;const o=e.customBaseUrl,n=e.owningTenant,a=!this._defaultOAuthInfo&&this._createDefaultOAuthInfo&&!this._hasTestedIfAppIsOnPortal;if(a){r=window.location.href;let e=r.indexOf(\"?\");e>-1&&(r=r.slice(0,e)),e=r.search(/\\/(apps|home)\\//),r=e>-1?r.slice(0,e):null}return a&&r?(this._hasTestedIfAppIsOnPortal=!0,t=s(r+\"/sharing/rest\",{query:{f:\"json\"}}).then((()=>{this._defaultOAuthInfo=new O({appId:\"arcgisonline\",popupCallbackUrl:r+\"/home/<USER>\"})}))):t=Promise.resolve(),t.then((()=>{if(this._defaultOAuthInfo)return i=i.replace(/^http:/i,\"https:\"),s(i+\"/sharing/rest/oauth2/validateRedirectUri\",{query:{accountId:n,client_id:this._defaultOAuthInfo.appId,redirect_uri:I(this._defaultOAuthInfo.popupCallbackUrl),f:\"json\"}}).then((e=>{if(e.data.valid){const t=this._defaultOAuthInfo.clone();e.data.urlKey&&o?t.portalUrl=\"https://\"+e.data.urlKey.toLowerCase()+\".\"+o:t.portalUrl=i,t.popup=window!==window.top||!(_(i,this._appOrigin)||this._gwDomains.some((e=>e.regex.test(i)&&e.regex.test(this._appOrigin)))),this.oAuthInfos.push(t)}}))}))}_doOAuthSignIn(e,t,r,s){const o=r._oAuthCred,a={portalUrl:r.portalUrl};!r.popup&&r.preserveUrlHash&&window.location.hash&&(a.hash=window.location.hash),o.stateUID&&(a.uid=o.stateUID);const h={client_id:r.appId,response_type:o.codeVerifier?\"code\":\"token\",state:JSON.stringify(a),expiration:r.expiration,locale:r.locale,redirect_uri:this._getRedirectURI(r,!!o.codeVerifier)};r.forceLogin&&(h.force_login=!0),r.forceUserId&&r.userId&&(h.prepopulatedusername=r.userId),!r.popup&&this._doPortalSignIn(e)&&(h.redirectToUserOrgUrl=!0),o.codeVerifier&&(h.code_challenge=s||o.codeVerifier,h.code_challenge_method=s?\"S256\":\"plain\");const l=r.portalUrl.replace(/^http:/i,\"https:\")+\"/sharing/oauth2/authorize\",c=l+\"?\"+v(h);if(r.popup){const e=window.open(c,\"esriJSAPIOAuth\",r.popupWindowFeatures);if(e)e.focus(),this._oAuthDfd.oAuthWin_=e,this._oAuthIntervalId=setInterval((()=>{if(e.closed){clearInterval(this._oAuthIntervalId),this._oAuthOnPopupHandle.remove();const e=this._oAuthDfd;if(e){const t=new i(\"identity-manager:user-aborted\",\"ABORTED\");e.reject(t)}}}),500),this._oAuthOnPopupHandle=n(window,[\"arcgis:auth:hash\",\"arcgis:auth:location:search\"],(e=>{\"arcgis:auth:hash\"===e.type?this.setOAuthResponseHash(e.detail):this._setOAuthResponseQueryString(e.detail)}));else{const e=new i(\"identity-manager:popup-blocked\",\"ABORTED\");this._oAuthDfd.reject(e)}}else this._rejectOnPersistedPageShow=!0,this._oAuthRedirectFunc?this._oAuthRedirectFunc({authorizeParams:h,authorizeUrl:l,resourceUrl:e,serverInfo:t,oAuthInfo:r}):window.location.href=c}_getRedirectURI(e,t){const r=window.location.href.replace(/#.*$/,\"\");if(e.popup)return I(e.popupCallbackUrl);if(t){const e=p(r);return e.query&&[\"code\",\"error\",\"error_description\",\"message_code\",\"persist\",\"state\"].forEach((t=>{delete e.query[t]})),A(e.path,e.query)}return r}}E.prototype.declaredClass=\"esri.identity.IdentityManagerBase\";let L=class extends o.EventedAccessor{constructor(e){super(e),this._oAuthCred=null,this.tokenRefreshBuffer=2,e&&e._oAuthCred&&(this._oAuthCred=e._oAuthCred)}initialize(){this.resources=this.resources||[],null==this.creationTime&&(this.creationTime=Date.now())}refreshToken(){const e=r.findServerInfo(this.server),t=e&&e.owningSystemUrl,s=!!t&&\"server\"===this.scope,i=s&&D(e,r._legacyFed),o=e.webTierAuth,n=o&&r.normalizeWebTierAuth,a=C[this.server],h=a&&a[this.userId];let l,c=this.resources&&this.resources[0],d=s?r.findServerInfo(t):null,u={username:this.userId,password:h};if(o&&!n)return;s&&!d&&r.serverInfos.some((e=>(r._isIdProvider(t,e.server)&&(d=e),!!d)));const p=d?r.findCredential(d.server,this.userId):null;if(!s||p){if(!i){if(s)l={serverUrl:c,token:p&&p.token,ssl:p&&p.ssl};else if(n)u=null,l={ssl:this.ssl};else{if(!h){let t;return c&&(c=r._sanitizeUrl(c),this._enqueued=1,t=r._enqueue(c,e,null,null,this.isAdmin,this),t.then((()=>{this._enqueued=0,this.refreshServerTokens()})).catch((()=>{this._enqueued=0}))),t}this.isAdmin&&(l={isAdmin:!0})}return r.generateToken(s?d:e,s?null:u,l).then((e=>{this.token=e.token,this.expires=null!=e.expires?Number(e.expires):null,this.creationTime=Date.now(),this.validity=e.validity,this.emitTokenChange(),this.refreshServerTokens()})).catch((()=>{}))}p?.refreshToken()}}refreshServerTokens(){\"portal\"===this.scope&&r.credentials.forEach((e=>{const t=r.findServerInfo(e.server),s=t&&t.owningSystemUrl;e!==this&&e.userId===this.userId&&s&&\"server\"===e.scope&&(r._hasSameServerInstance(this.server,s)||r._isIdProvider(s,this.server))&&(D(t,r._legacyFed)?(e.token=this.token,e.expires=this.expires,e.creationTime=this.creationTime,e.validity=this.validity,e.emitTokenChange()):e.refreshToken())}))}emitTokenChange(e){clearTimeout(this._refreshTimer);const t=this.server&&r.findServerInfo(this.server),s=t&&t.owningSystemUrl,i=s&&r.findServerInfo(s);!1===e||s&&\"portal\"!==this.scope&&(!i||!i.webTierAuth||r.normalizeWebTierAuth)||null==this.expires&&null==this.validity||this._startRefreshTimer(),this.emit(\"token-change\")}destroy(){this.userId=this.server=this.token=this.expires=this.validity=this.resources=this.creationTime=null,this._oAuthCred&&(this._oAuthCred.destroy(),this._oAuthCred=null);const e=r.credentials.indexOf(this);e>-1&&r.credentials.splice(e,1),this.emitTokenChange(),this.emit(\"destroy\")}toJSON(){const e=a({userId:this.userId,server:this.server,token:this.token,expires:this.expires,validity:this.validity,ssl:this.ssl,isAdmin:this.isAdmin,creationTime:this.creationTime,scope:this.scope}),t=this.resources;return t&&t.length>0&&(e.resources=t.slice()),e}_startRefreshTimer(){clearTimeout(this._refreshTimer);const e=6e4*this.tokenRefreshBuffer,t=2**31-1;let r=(this.validity?this.creationTime+6e4*this.validity:this.expires)-Date.now();r<0?r=0:r>t&&(r=t),this._refreshTimer=setTimeout(this.refreshToken.bind(this),r>e?r-e:r)}};e([k()],L.prototype,\"creationTime\",void 0),e([k()],L.prototype,\"expires\",void 0),e([k()],L.prototype,\"isAdmin\",void 0),e([k()],L.prototype,\"oAuthState\",void 0),e([k()],L.prototype,\"resources\",void 0),e([k()],L.prototype,\"scope\",void 0),e([k()],L.prototype,\"server\",void 0),e([k()],L.prototype,\"ssl\",void 0),e([k()],L.prototype,\"token\",void 0),e([k()],L.prototype,\"tokenRefreshBuffer\",void 0),e([k()],L.prototype,\"userId\",void 0),e([k()],L.prototype,\"validity\",void 0),L=e([y(\"esri.identity.Credential\")],L);export{L as Credential,E as IdentityManagerBase};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{setId as e}from\"../kernel.js\";import{IdentityManagerBase as t}from\"./IdentityManagerBase.js\";class r extends t{}r.prototype.declaredClass=\"esri.identity.IdentityManager\";const s=new r;e(s);export{s as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsiB,IAAMA,KAAE;AAAR,IAA6BC,KAAE,EAAC,MAAKD,IAAE,OAAM,GAAGA,EAAC,WAAU,OAAM,GAAGA,EAAC,WAAU,QAAO,GAAGA,EAAC,YAAW,WAAU,cAAa,YAAW,eAAc,qBAAoB,yBAAwB;AAAjM,IAAmME,KAAE;AAAgB,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,qBAAmB,MAAK,KAAK,qBAAmB,MAAK,KAAK,YAAU,OAAG,KAAK,SAAO,MAAK,KAAK,WAAS,MAAK,KAAK,QAAM,MAAK,KAAK,cAAY;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,OAAMD,IAAE,QAAOC,IAAE,UAASC,IAAE,WAAU,GAAE,aAAYC,IAAE,UAASN,GAAC,IAAE,MAAKO,KAAED,GAAE,OAAM,EAAC,OAAML,GAAE,MAAK,GAAEE,GAAEG,KAAEN,GAAE,YAAUA,GAAE,MAAK,EAAC,QAAOI,MAAG,iBAAiB,KAAKA,EAAC,IAAEF,KAAEE,IAAE,UAAS,IAAIC,MAAGL,GAAE,OAAO,IAAG,CAAC,CAAC,GAAEQ,KAAEF,KAAE,OAAKA,GAAE,OAAM,EAAC,OAAML,GAAE,OAAM,KAAI,WAAU,GAAEK,GAAE,SAAQ,EAAC,OAAML,GAAE,MAAK,GAAED,GAAE,SAAQM,GAAE,SAAQ,EAAC,OAAM,IAAG,UAAS,MAAG,cAAa,OAAM,YAAW,OAAG,MAAK,QAAO,MAAK,MAAK,aAAYG,IAAE,iBAAgB,sBAAqB,OAAMR,GAAE,UAAS,CAAC,CAAC,CAAC,GAAE,IAAEK,KAAE,OAAKA,GAAE,OAAM,EAAC,OAAML,GAAE,OAAM,KAAI,WAAU,GAAEK,GAAE,SAAQ,EAAC,OAAML,GAAE,MAAK,GAAED,GAAE,QAAOM,GAAE,SAAQ,EAAC,OAAM,IAAG,UAAS,MAAG,MAAK,YAAW,MAAK,MAAK,aAAYG,IAAE,iBAAgB,sBAAqB,OAAMR,GAAE,UAAS,CAAC,CAAC,CAAC,GAAES,KAAEJ,GAAE,OAAM,EAAC,OAAM,KAAK,QAAQL,GAAE,OAAMA,GAAE,MAAM,EAAC,GAAEK,GAAE,SAAQ,EAAC,MAAK,UAAS,UAAS,CAAC,CAAC,GAAE,OAAM,IAAEN,GAAE,aAAWA,GAAE,OAAM,OAAMC,GAAE,WAAU,CAAC,GAAEK,GAAE,SAAQ,EAAC,MAAK,UAAS,OAAMN,GAAE,WAAU,MAAK,MAAK,SAAQ,KAAK,SAAQ,OAAM,KAAK,QAAQC,GAAE,YAAWA,GAAE,mBAAmB,EAAC,CAAC,CAAC,GAAEU,KAAER,KAAEG,GAAE,OAAM,MAAKH,GAAE,WAASA,GAAE,QAAQ,aAAWH,GAAE,cAAYA,GAAE,aAAa,IAAE;AAAK,WAAOM,GAAE,QAAO,EAAC,OAAML,GAAE,MAAK,MAAK,MAAK,UAAS,KAAK,QAAO,GAAEM,IAAEI,IAAEH,IAAE,GAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,aAAY,KAAE,GAAE,KAAK,uBAAqB,KAAK,mBAAmB,QAAM,KAAI,KAAK,uBAAqB,KAAK,mBAAmB,QAAM,KAAI,KAAK,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,QAAQP,IAAE;AAAC,IAAAA,GAAE,eAAe,GAAE,KAAK,KAAK,aAAY,IAAE;AAAE,UAAMC,KAAE,KAAK,cAAY,CAAC,IAAE,EAAC,UAAS,KAAK,sBAAoB,KAAK,mBAAmB,OAAM,UAAS,KAAK,sBAAoB,KAAK,mBAAmB,MAAK;AAAE,SAAK,KAAK,UAASA,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,GAAEA,GAAE,4BAA4B,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,4BAA4B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACC9tF,IAAMQ,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAUzC,IAAMC,UAAU,SAAVA,SAAoBC,MAAMC,QAAe;AAAA,MAAAC;AAAA,MAAfD,WAAM,QAAA;AAANA,aAAS;EAAI;AAI3C,MAAME,WAAWH,SAAI,QAAJA,SAAIE,SAAAA,UAAAA,qBAAJF,KAAMI,kBAAYF,QAAAA,uBAAA,SAAA,SAAlBA,mBAAAL,KAAAG,MAAqB,OAAO;AAC7C,MAAMK,QAAQF,aAAa,MAAMA,aAAa;AAO9C,MAAMG,SAASD,SAAUJ,UAAUD,QAAQD,SAAQC,KAAKO,UAAU;AAElE,SAAOD;AACT;AAOA,IAAME,oBAAoB,SAApBA,mBAA8BR,MAAM;AAAA,MAAAS;AAIxC,MAAMC,WAAWV,SAAI,QAAJA,SAAIS,SAAAA,UAAAA,sBAAJT,KAAMI,kBAAYK,QAAAA,wBAAA,SAAA,SAAlBA,oBAAAZ,KAAAG,MAAqB,iBAAiB;AACvD,SAAOU,aAAa,MAAMA,aAAa;AACzC;AAQA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAIC,kBAAkBC,QAAQ;AAG5D,MAAIf,QAAQa,EAAE,GAAG;AACf,WAAO,CAAA;EACT;AAEA,MAAIG,aAAaC,MAAMzB,UAAU0B,MAAMC,MACrCN,GAAGO,iBAAiBjC,iBAAiB,CACvC;AACA,MAAI2B,oBAAoBvB,QAAQO,KAAKe,IAAI1B,iBAAiB,GAAG;AAC3D6B,eAAWK,QAAQR,EAAE;EACvB;AACAG,eAAaA,WAAWD,OAAOA,MAAM;AACrC,SAAOC;AACT;AAoCA,IAAMM,2BAA2B,SAA3BA,0BACJC,UACAT,kBACAU,SACA;AACA,MAAMR,aAAa,CAAA;AACnB,MAAMS,kBAAkBR,MAAMS,KAAKH,QAAQ;AAC3C,SAAOE,gBAAgBE,QAAQ;AAC7B,QAAM/B,UAAU6B,gBAAgBG,MAAK;AACrC,QAAI5B,QAAQJ,SAAS,KAAK,GAAG;AAG3B;IACF;AAEA,QAAIA,QAAQiC,YAAY,QAAQ;AAE9B,UAAMC,WAAWlC,QAAQmC,iBAAgB;AACzC,UAAMC,UAAUF,SAASH,SAASG,WAAWlC,QAAQqC;AACrD,UAAMC,mBAAmBZ,0BAAyBU,SAAS,MAAMR,OAAO;AACxE,UAAIA,QAAQW,SAAS;AACnBnB,mBAAWoB,KAAIjB,MAAfH,YAAmBkB,gBAAgB;MACrC,OAAO;AACLlB,mBAAWoB,KAAK;UACdC,aAAazC;UACboB,YAAYkB;QACd,CAAC;MACH;IACF,OAAO;AAEL,UAAMI,iBAAiB/C,QAAQO,KAAKF,SAAST,iBAAiB;AAC9D,UACEmD,kBACAd,QAAQT,OAAOnB,OAAO,MACrBkB,oBAAoB,CAACS,SAASgB,SAAS3C,OAAO,IAC/C;AACAoB,mBAAWoB,KAAKxC,OAAO;MACzB;AAGA,UAAM4C,aACJ5C,QAAQ4C;MAEP,OAAOhB,QAAQiB,kBAAkB,cAChCjB,QAAQiB,cAAc7C,OAAO;AAKjC,UAAM8C,kBACJ,CAAC1C,QAAQwC,YAAY,KAAK,MACzB,CAAChB,QAAQmB,oBAAoBnB,QAAQmB,iBAAiB/C,OAAO;AAEhE,UAAI4C,cAAcE,iBAAiB;AAOjC,YAAMR,oBAAmBZ,0BACvBkB,eAAe,OAAO5C,QAAQqC,WAAWO,WAAWP,UACpD,MACAT,OACF;AAEA,YAAIA,QAAQW,SAAS;AACnBnB,qBAAWoB,KAAIjB,MAAfH,YAAmBkB,iBAAgB;QACrC,OAAO;AACLlB,qBAAWoB,KAAK;YACdC,aAAazC;YACboB,YAAYkB;UACd,CAAC;QACH;MACF,OAAO;AAGLT,wBAAgBJ,QAAOF,MAAvBM,iBAA2B7B,QAAQqC,QAAQ;MAC7C;IACF;EACF;AACA,SAAOjB;AACT;AAQA,IAAM4B,cAAc,SAAdA,aAAwB3C,MAAM;AAClC,SAAO,CAAC4C,MAAMC,SAAS7C,KAAKI,aAAa,UAAU,GAAG,EAAE,CAAC;AAC3D;AAQA,IAAM0C,cAAc,SAAdA,aAAwB9C,MAAM;AAClC,MAAI,CAACA,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AAEA,MAAI/C,KAAKgD,WAAW,GAAG;AAQrB,SACG,0BAA0BC,KAAKjD,KAAK4B,OAAO,KAC1CpB,kBAAkBR,IAAI,MACxB,CAAC2C,YAAY3C,IAAI,GACjB;AACA,aAAO;IACT;EACF;AAEA,SAAOA,KAAKgD;AACd;AAUA,IAAME,uBAAuB,SAAvBA,sBAAiClD,MAAMmD,SAAS;AACpD,MAAMH,WAAWF,YAAY9C,IAAI;AAEjC,MAAIgD,WAAW,KAAKG,WAAW,CAACR,YAAY3C,IAAI,GAAG;AACjD,WAAO;EACT;AAEA,SAAOgD;AACT;AAEA,IAAMI,uBAAuB,SAAvBA,sBAAiCC,IAAGC,IAAG;AAC3C,SAAOD,GAAEL,aAAaM,GAAEN,WACpBK,GAAEE,gBAAgBD,GAAEC,gBACpBF,GAAEL,WAAWM,GAAEN;AACrB;AAEA,IAAMQ,UAAU,SAAVA,SAAoBxD,MAAM;AAC9B,SAAOA,KAAK4B,YAAY;AAC1B;AAEA,IAAM6B,gBAAgB,SAAhBA,eAA0BzD,MAAM;AACpC,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMC,uBAAuB,SAAvBA,sBAAiC3D,MAAM;AAC3C,MAAM4D,KACJ5D,KAAK4B,YAAY,aACjBZ,MAAMzB,UAAU0B,MACbC,MAAMlB,KAAKgC,QAAQ,EACnB6B,KAAK,SAACC,OAAK;AAAA,WAAKA,MAAMlC,YAAY;GAAU;AACjD,SAAOgC;AACT;AAEA,IAAMG,kBAAkB,SAAlBA,iBAA4BC,OAAOC,MAAM;AAC7C,WAASC,KAAI,GAAGA,KAAIF,MAAMtC,QAAQwC,MAAK;AACrC,QAAIF,MAAME,EAAC,EAAEC,WAAWH,MAAME,EAAC,EAAED,SAASA,MAAM;AAC9C,aAAOD,MAAME,EAAC;IAChB;EACF;AACF;AAEA,IAAME,kBAAkB,SAAlBA,iBAA4BpE,MAAM;AACtC,MAAI,CAACA,KAAKqE,MAAM;AACd,WAAO;EACT;AACA,MAAMC,aAAatE,KAAKiE,QAAQvE,YAAYM,IAAI;AAChD,MAAMuE,cAAc,SAAdA,aAAwBF,MAAM;AAClC,WAAOC,WAAWnD,iBAChB,+BAA+BkD,OAAO,IACxC;;AAGF,MAAIG;AACJ,MACE,OAAOC,WAAW,eAClB,OAAOA,OAAOC,QAAQ,eACtB,OAAOD,OAAOC,IAAIC,WAAW,YAC7B;AACAH,eAAWD,YAAYE,OAAOC,IAAIC,OAAO3E,KAAKqE,IAAI,CAAC;EACrD,OAAO;AACL,QAAI;AACFG,iBAAWD,YAAYvE,KAAKqE,IAAI;aACzBO,KAAK;AAEZC,cAAQC,MACN,4IACAF,IAAIG,OACN;AACA,aAAO;IACT;EACF;AAEA,MAAMZ,UAAUJ,gBAAgBS,UAAUxE,KAAKiE,IAAI;AACnD,SAAO,CAACE,WAAWA,YAAYnE;AACjC;AAEA,IAAMgF,UAAU,SAAVA,SAAoBhF,MAAM;AAC9B,SAAOwD,QAAQxD,IAAI,KAAKA,KAAK0D,SAAS;AACxC;AAEA,IAAMuB,qBAAqB,SAArBA,oBAA+BjF,MAAM;AACzC,SAAOgF,QAAQhF,IAAI,KAAK,CAACoE,gBAAgBpE,IAAI;AAC/C;AAGA,IAAMkF,iBAAiB,SAAjBA,gBAA2BlF,MAAM;AAAA,MAAAmF;AAwBrC,MAAIC,WAAWpF,QAAQN,YAAYM,IAAI;AACvC,MAAIqF,gBAAYF,YAAGC,cAAQ,QAAAD,cAAA,SAAA,SAARA,UAAUG;AAI7B,MAAIC,WAAW;AACf,MAAIH,YAAYA,aAAapF,MAAM;AAAA,QAAAwF,eAAAC,uBAAAC;AACjCH,eAAW,CAAC,GACVC,gBAAAH,kBAAYG,QAAAA,kBAAA,WAAAC,wBAAZD,cAAc1F,mBAAa,QAAA2F,0BAAA,UAA3BA,sBAA6BE,SAASN,YAAY,KAClDrF,SAAI,QAAJA,SAAI0F,WAAAA,sBAAJ1F,KAAMF,mBAAa4F,QAAAA,wBAAA,UAAnBA,oBAAqBC,SAAS3F,IAAI;AAGpC,WAAO,CAACuF,YAAYF,cAAc;AAAA,UAAAO,YAAAC,gBAAAC;AAIhCV,iBAAW1F,YAAY2F,YAAY;AACnCA,sBAAYO,aAAGR,cAAQ,QAAAQ,eAAA,SAAA,SAARA,WAAUN;AACzBC,iBAAW,CAAC,GAAAM,iBAACR,kBAAY,QAAAQ,mBAAA,WAAAC,wBAAZD,eAAc/F,mBAAa,QAAAgG,0BAAA,UAA3BA,sBAA6BH,SAASN,YAAY;IACjE;EACF;AAEA,SAAOE;AACT;AAEA,IAAMQ,aAAa,SAAbA,YAAuB/F,MAAM;AACjC,MAAAgG,wBAA0BhG,KAAKiG,sBAAqB,GAA5CC,QAAKF,sBAALE,OAAOC,SAAMH,sBAANG;AACf,SAAOD,UAAU,KAAKC,WAAW;AACnC;AACA,IAAMC,WAAW,SAAXA,UAAqBpG,MAAIqG,MAAmC;AAAA,MAA/BC,eAAYD,KAAZC,cAAc9D,gBAAa6D,KAAb7D;AAM/C,MAAI+D,iBAAiBvG,IAAI,EAAEwG,eAAe,UAAU;AAClD,WAAO;EACT;AAEA,MAAMC,kBAAkBnH,QAAQO,KAAKG,MAAM,+BAA+B;AAC1E,MAAM0G,mBAAmBD,kBAAkBzG,KAAK2G,gBAAgB3G;AAChE,MAAIV,QAAQO,KAAK6G,kBAAkB,uBAAuB,GAAG;AAC3D,WAAO;EACT;AAEA,MACE,CAACJ,gBACDA,iBAAiB,UACjBA,iBAAiB,eACjB;AACA,QAAI,OAAO9D,kBAAkB,YAAY;AAGvC,UAAMoE,eAAe5G;AACrB,aAAOA,MAAM;AACX,YAAM2G,gBAAgB3G,KAAK2G;AAC3B,YAAME,WAAWnH,YAAYM,IAAI;AACjC,YACE2G,iBACA,CAACA,cAAcpE,cACfC,cAAcmE,aAAa,MAAM,MACjC;AAGA,iBAAOZ,WAAW/F,IAAI;QACxB,WAAWA,KAAK8G,cAAc;AAE5B9G,iBAAOA,KAAK8G;mBACH,CAACH,iBAAiBE,aAAa7G,KAAKF,eAAe;AAE5DE,iBAAO6G,SAASvB;QAClB,OAAO;AAELtF,iBAAO2G;QACT;MACF;AAEA3G,aAAO4G;IACT;AAWA,QAAI1B,eAAelF,IAAI,GAAG;AAKxB,aAAO,CAACA,KAAK+G,eAAc,EAAGrF;IAChC;AAkBA,QAAI4E,iBAAiB,eAAe;AAClC,aAAO;IACT;EAEF,WAAWA,iBAAiB,iBAAiB;AAM3C,WAAOP,WAAW/F,IAAI;EACxB;AAIA,SAAO;AACT;AAKA,IAAMgH,yBAAyB,SAAzBA,wBAAmChH,MAAM;AAC7C,MAAI,mCAAmCiD,KAAKjD,KAAK4B,OAAO,GAAG;AACzD,QAAIrB,aAAaP,KAAK2G;AAEtB,WAAOpG,YAAY;AACjB,UAAIA,WAAWqB,YAAY,cAAcrB,WAAW0G,UAAU;AAE5D,iBAAS/C,KAAI,GAAGA,KAAI3D,WAAWyB,SAASN,QAAQwC,MAAK;AACnD,cAAMJ,QAAQvD,WAAWyB,SAASkF,KAAKhD,EAAC;AAExC,cAAIJ,MAAMlC,YAAY,UAAU;AAG9B,mBAAOtC,QAAQO,KAAKU,YAAY,sBAAsB,IAClD,OACA,CAACuD,MAAM6B,SAAS3F,IAAI;UAC1B;QACF;AAEA,eAAO;MACT;AACAO,mBAAaA,WAAWoG;IAC1B;EACF;AAIA,SAAO;AACT;AAEA,IAAMQ,kCAAkC,SAAlCA,iCAA4C5F,SAASvB,MAAM;AAC/D,MACEA,KAAKiH;;;EAILlH,QAAQC,IAAI,KACZyD,cAAczD,IAAI,KAClBoG,SAASpG,MAAMuB,OAAO;EAEtBoC,qBAAqB3D,IAAI,KACzBgH,uBAAuBhH,IAAI,GAC3B;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMoH,iCAAiC,SAAjCA,gCAA2C7F,SAASvB,MAAM;AAC9D,MACEiF,mBAAmBjF,IAAI,KACvB8C,YAAY9C,IAAI,IAAI,KACpB,CAACmH,gCAAgC5F,SAASvB,IAAI,GAC9C;AACA,WAAO;EACT;AACA,SAAO;AACT;AAEA,IAAMqH,4BAA4B,SAA5BA,2BAAsCC,gBAAgB;AAC1D,MAAMtE,WAAWH,SAASyE,eAAelH,aAAa,UAAU,GAAG,EAAE;AACrE,MAAIwC,MAAMI,QAAQ,KAAKA,YAAY,GAAG;AACpC,WAAO;EACT;AAGA,SAAO;AACT;AAMA,IAAMuE,cAAc,SAAdA,aAAwBxG,YAAY;AACxC,MAAMyG,mBAAmB,CAAA;AACzB,MAAMC,mBAAmB,CAAA;AACzB1G,aAAW2G,QAAQ,SAAUR,MAAMhD,IAAG;AACpC,QAAMf,UAAU,CAAC,CAAC+D,KAAK9E;AACvB,QAAMzC,UAAUwD,UAAU+D,KAAK9E,cAAc8E;AAC7C,QAAMS,oBAAoBzE,qBAAqBvD,SAASwD,OAAO;AAC/D,QAAM7B,WAAW6B,UAAUoE,aAAYL,KAAKnG,UAAU,IAAIpB;AAC1D,QAAIgI,sBAAsB,GAAG;AAC3BxE,gBACIqE,iBAAiBrF,KAAIjB,MAArBsG,kBAAyBlG,QAAQ,IACjCkG,iBAAiBrF,KAAKxC,OAAO;IACnC,OAAO;AACL8H,uBAAiBtF,KAAK;QACpBoB,eAAeW;QACflB,UAAU2E;QACVT;QACA/D;QACApB,SAAST;MACX,CAAC;IACH;EACF,CAAC;AAED,SAAOmG,iBACJG,KAAKxE,oBAAoB,EACzByE,OAAO,SAACC,KAAKC,UAAa;AACzBA,aAAS5E,UACL2E,IAAI3F,KAAIjB,MAAR4G,KAAYC,SAAShG,OAAO,IAC5B+F,IAAI3F,KAAK4F,SAAShG,OAAO;AAC7B,WAAO+F;EACT,GAAG,CAAA,CAAE,EACJE,OAAOR,gBAAgB;AAC5B;AAEMS,IAAAA,WAAW,SAAXA,UAAqBC,WAAW3G,SAAS;AAC7CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQsG,+BAA+Be,KAAK,MAAM5G,OAAO;MACzDW,SAAS;MACTM,eAAejB,QAAQiB;MACvBE,kBAAkB2E;IACpB,CACF;EACF,OAAO;AACLtG,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRuG,+BAA+Be,KAAK,MAAM5G,OAAO,CACnD;EACF;AACA,SAAOgG,YAAYxG,UAAU;AAC/B;AAEMqH,IAAAA,YAAY,SAAZA,WAAsBF,WAAW3G,SAAS;AAC9CA,YAAUA,WAAW,CAAA;AAErB,MAAIR;AACJ,MAAIQ,QAAQiB,eAAe;AACzBzB,iBAAaM,yBACX,CAAC6G,SAAS,GACV3G,QAAQV,kBACR;MACEC,QAAQqG,gCAAgCgB,KAAK,MAAM5G,OAAO;MAC1DW,SAAS;MACTM,eAAejB,QAAQiB;IACzB,CACF;EACF,OAAO;AACLzB,iBAAaJ,cACXuH,WACA3G,QAAQV,kBACRsG,gCAAgCgB,KAAK,MAAM5G,OAAO,CACpD;EACF;AAEA,SAAOR;AACT;AAEMsH,IAAAA,aAAa,SAAbA,YAAuBrI,MAAMuB,SAAS;AAC1CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMd,iBAAiB,MAAM,OAAO;AACnD,WAAO;EACT;AACA,SAAOkI,+BAA+B7F,SAASvB,IAAI;AACrD;AAEA,IAAMsI,6BAA6CrJ,mBAChD+I,OAAO,QAAQ,EACf7I,KAAK,GAAG;AAELoJ,IAAAA,cAAc,SAAdA,aAAwBvI,MAAMuB,SAAS;AAC3CA,YAAUA,WAAW,CAAA;AACrB,MAAI,CAACvB,MAAM;AACT,UAAM,IAAI+C,MAAM,kBAAkB;EACpC;AACA,MAAIzD,QAAQO,KAAKG,MAAMsI,0BAA0B,MAAM,OAAO;AAC5D,WAAO;EACT;AACA,SAAOnB,gCAAgC5F,SAASvB,IAAI;AACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3qBA,IAAMwI,mBAAmB;EACvBC,cAAaC,SAAAA,aAAAA,WAAWC,MAAM;AAC5B,QAAID,UAAUE,SAAS,GAAG;AACxB,UAAMC,aAAaH,UAAUA,UAAUE,SAAS,CAAC;AACjD,UAAIC,eAAeF,MAAM;AACvBE,mBAAWC,MAAK;MAClB;IACF;AAEA,QAAMC,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUO,KAAKN,IAAI;IACrB,OAAO;AAELD,gBAAUQ,OAAOH,WAAW,CAAC;AAC7BL,gBAAUO,KAAKN,IAAI;IACrB;;EAGFQ,gBAAeT,SAAAA,eAAAA,WAAWC,MAAM;AAC9B,QAAMI,YAAYL,UAAUM,QAAQL,IAAI;AACxC,QAAII,cAAc,IAAI;AACpBL,gBAAUQ,OAAOH,WAAW,CAAC;IAC/B;AAEA,QAAIL,UAAUE,SAAS,GAAG;AACxBF,gBAAUA,UAAUE,SAAS,CAAC,EAAEQ,QAAO;IACzC;EACF;AACF;AAEA,IAAMC,oBAAoB,SAApBA,mBAA8BC,MAAM;AACxC,SACEA,KAAKC,WACLD,KAAKC,QAAQC,YAAW,MAAO,WAC/B,OAAOF,KAAKG,WAAW;AAE3B;AAEA,IAAMC,gBAAgB,SAAhBA,eAA0BC,IAAG;AACjC,SAAOA,GAAEC,QAAQ,YAAYD,GAAEC,QAAQ,SAASD,GAAEE,YAAY;AAChE;AAEA,IAAMC,aAAa,SAAbA,YAAuBH,IAAG;AAC9B,SAAOA,GAAEC,QAAQ,SAASD,GAAEE,YAAY;AAC1C;AAGA,IAAME,eAAe,SAAfA,cAAyBJ,IAAG;AAChC,SAAOG,WAAWH,EAAC,KAAK,CAACA,GAAEK;AAC7B;AAGA,IAAMC,gBAAgB,SAAhBA,eAA0BN,IAAG;AACjC,SAAOG,WAAWH,EAAC,KAAKA,GAAEK;AAC5B;AAEA,IAAME,QAAQ,SAARA,OAAkBC,IAAI;AAC1B,SAAOC,WAAWD,IAAI,CAAC;AACzB;AAIA,IAAME,YAAY,SAAZA,WAAsBC,KAAKH,IAAI;AACnC,MAAII,MAAM;AAEVD,MAAIE,MAAM,SAAUC,OAAOC,IAAG;AAC5B,QAAIP,GAAGM,KAAK,GAAG;AACbF,YAAMG;AACN,aAAO;IACT;AAEA,WAAO;EACT,CAAC;AAED,SAAOH;AACT;AASA,IAAMI,iBAAiB,SAAjBA,gBAA2BF,OAAkB;AAAA,WAAA,OAAA,UAAA,QAARG,SAAM,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,CAAA,GAAA,OAAA,GAAA,OAAA,MAAA,QAAA;AAANA,WAAM,OAAA,CAAA,IAAA,UAAA,IAAA;EAAA;AAC/C,SAAO,OAAOH,UAAU,aAAaA,MAASG,MAAAA,QAAAA,MAAM,IAAIH;AAC1D;AAEA,IAAMI,kBAAkB,SAAlBA,iBAA4BC,OAAO;AAQvC,SAAOA,MAAMC,OAAOC,cAAc,OAAOF,MAAMG,iBAAiB,aAC5DH,MAAMG,aAAY,EAAG,CAAC,IACtBH,MAAMC;AACZ;AAIA,IAAMG,oBAAoB,CAAA;AAEpBC,IAAAA,kBAAkB,SAAlBA,iBAA4BC,UAAUC,aAAa;AAGvD,MAAMC,OAAMD,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAaE,aAAYA;AAErC,MAAM7C,aAAY2C,gBAAW,QAAXA,gBAAW,SAAA,SAAXA,YAAa3C,cAAawC;AAE5C,MAAMM,SAAMC,eAAA;IACVC,yBAAyB;IACzBC,mBAAmB;IACnBC,mBAAmB;IACnB7B;IACAE;EAAa,GACVoB,WAAW;AAGhB,MAAMQ,QAAQ;;;IAGZC,YAAY,CAAA;;;;;;;;;;;;;;IAeZC,iBAAiB,CAAA;;;;;;IAMjBC,gBAAgB,CAAA;IAEhBC,6BAA6B;IAC7BC,yBAAyB;IACzBC,QAAQ;IACRC,QAAQ;;;IAIRC,wBAAwBC;;AAG1B,MAAI3D;AAUJ,MAAM4D,YAAY,SAAZA,WAAaC,uBAAuBC,YAAYC,kBAAqB;AACzE,WAAOF,yBACLA,sBAAsBC,UAAU,MAAMH,SACpCE,sBAAsBC,UAAU,IAChCjB,OAAOkB,oBAAoBD,UAAU;;AAU3C,MAAME,qBAAqB,SAArBA,oBAA+BC,SAAS;AAI5C,WAAOf,MAAME,gBAAgB1B,UAC3B,SAAA,MAAA;AAAA,UAAGwC,YAAS,KAATA,WAAWC,gBAAa,KAAbA;AAAa,aACzBD,UAAUE,SAASH,OAAO;;;;MAK1BE,cAAcE,KAAK,SAAC1D,MAAI;AAAA,eAAKA,SAASsD;OAAQ;KACjD;;AAgBH,MAAMK,mBAAmB,SAAnBA,kBAA6BR,YAAuB;AACxD,QAAIS,cAAc1B,OAAOiB,UAAU;AAEnC,QAAI,OAAOS,gBAAgB,YAAY;AAAA,eAAA,QAAA,UAAA,QAHStC,SAAM,IAAA,MAAA,QAAA,IAAA,QAAA,IAAA,CAAA,GAAA,QAAA,GAAA,QAAA,OAAA,SAAA;AAANA,eAAM,QAAA,CAAA,IAAA,UAAA,KAAA;MAAA;AAIpDsC,oBAAcA,YAAetC,MAAAA,QAAAA,MAAM;IACrC;AAEA,QAAIsC,gBAAgB,MAAM;AACxBA,oBAAcZ;IAChB;AAEA,QAAI,CAACY,aAAa;AAChB,UAAIA,gBAAgBZ,UAAaY,gBAAgB,OAAO;AACtD,eAAOA;MACT;AAGA,YAAM,IAAIC,MACHV,IAAAA,OAAAA,YACN,8DAAA,CAAA;IACH;AAEA,QAAInD,OAAO4D;AAEX,QAAI,OAAOA,gBAAgB,UAAU;AACnC5D,aAAOgC,IAAI8B,cAAcF,WAAW;AACpC,UAAI,CAAC5D,MAAM;AACT,cAAM,IAAI6D,MACHV,IAAAA,OAAAA,YACN,uCAAA,CAAA;MACH;IACF;AAEA,WAAOnD;;AAGT,MAAM+D,sBAAsB,SAAtBA,uBAAkC;AACtC,QAAI/D,OAAO2D,iBAAiB,cAAc;AAG1C,QAAI3D,SAAS,OAAO;AAClB,aAAO;IACT;AAEA,QAAIA,SAASgD,QAAW;AAEtB,UAAIK,mBAAmBrB,IAAIgC,aAAa,KAAK,GAAG;AAC9ChE,eAAOgC,IAAIgC;MACb,OAAO;AACL,YAAMC,qBAAqB1B,MAAMG,eAAe,CAAC;AACjD,YAAMwB,oBACJD,sBAAsBA,mBAAmBC;AAG3ClE,eAAOkE,qBAAqBP,iBAAiB,eAAe;MAC9D;IACF;AAEA,QAAI,CAAC3D,MAAM;AACT,YAAM,IAAI6D,MACR,8DAA8D;IAElE;AAEA,WAAO7D;;AAGT,MAAMmE,sBAAsB,SAAtBA,uBAAkC;AACtC5B,UAAME,kBAAkBF,MAAMC,WAAW4B,IAAI,SAACb,WAAc;AAC1D,UAAMC,gBAAgBa,SAASd,WAAWrB,OAAOoC,eAAe;AAIhE,UAAMC,iBAAiBC,UAAUjB,WAAWrB,OAAOoC,eAAe;AAElE,aAAO;QACLf;QACAC;QACAe;QACAL,mBAAmBV,cAAclE,SAAS,IAAIkE,cAAc,CAAC,IAAI;QACjEiB,kBACEjB,cAAclE,SAAS,IACnBkE,cAAcA,cAAclE,SAAS,CAAC,IACtC;;;;;;;;;QAUNoF,kBAAgB,SAAA,iBAAC1E,MAAsB;AAAA,cAAhB2E,UAAO,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAG;AAW/B,cAAMC,UAAUL,eAAexD,UAAU,SAAC8D,IAAC;AAAA,mBAAKA,OAAM7E;WAAK;AAC3D,cAAI4E,UAAU,GAAG;AACf,mBAAO5B;UACT;AAEA,cAAI2B,SAAS;AACX,mBAAOJ,eACJO,MAAMF,UAAU,CAAC,EACjBlB,KAAK,SAACmB,IAAC;AAAA,qBAAKE,WAAWF,IAAG3C,OAAOoC,eAAe;aAAE;UACvD;AAEA,iBAAOC,eACJO,MAAM,GAAGF,OAAO,EAChBI,QAAO,EACPtB,KAAK,SAACmB,IAAC;AAAA,mBAAKE,WAAWF,IAAG3C,OAAOoC,eAAe;WAAE;QACvD;;IAEJ,CAAC;AAED/B,UAAMG,iBAAiBH,MAAME,gBAAgBwC,OAC3C,SAACC,OAAK;AAAA,aAAKA,MAAM1B,cAAclE,SAAS;KACzC;AAGD,QACEiD,MAAMG,eAAepD,UAAU,KAC/B,CAACqE,iBAAiB,eAAe,GACjC;AACA,YAAM,IAAIE,MACR,qGAAqG;IAEzG;;AAGF,MAAMsB,WAAW,SAAXA,UAAqBnF,MAAM;AAC/B,QAAIA,SAAS,OAAO;AAClB;IACF;AAEA,QAAIA,SAASgC,IAAIgC,eAAe;AAC9B;IACF;AAEA,QAAI,CAAChE,QAAQ,CAACA,KAAKoF,OAAO;AACxBD,MAAAA,UAASpB,oBAAmB,CAAE;AAC9B;IACF;AAEA/D,SAAKoF,MAAM;MAAEC,eAAe,CAAC,CAACnD,OAAOmD;IAAc,CAAC;AACpD9C,UAAMK,0BAA0B5C;AAEhC,QAAID,kBAAkBC,IAAI,GAAG;AAC3BA,WAAKG,OAAM;IACb;;AAGF,MAAMmF,qBAAqB,SAArBA,oBAA+BC,uBAAuB;AAC1D,QAAMvF,OAAO2D,iBAAiB,kBAAkB4B,qBAAqB;AACrE,WAAOvF,OAAOA,OAAOA,SAAS,QAAQ,QAAQuF;;AAKhD,MAAMC,mBAAmB,SAAnBA,kBAA6BnF,IAAG;AACpC,QAAMoB,SAASF,gBAAgBlB,EAAC;AAEhC,QAAIgD,mBAAmB5B,MAAM,KAAK,GAAG;AAEnC;IACF;AAEA,QAAIJ,eAAea,OAAOuD,yBAAyBpF,EAAC,GAAG;AAErDhB,WAAKqG,WAAW;;;;;;;;;;;;QAYdC,aACEzD,OAAOE,2BACP,CAACwD,YAAYnE,QAAQS,OAAOoC,eAAe;MAC/C,CAAC;AACD;IACF;AAKA,QAAIjD,eAAea,OAAO2D,mBAAmBxF,EAAC,GAAG;AAE/C;IACF;AAGAA,IAAAA,GAAEyF,eAAc;;AAIlB,MAAMC,eAAe,SAAfA,cAAyB1F,IAAG;AAChC,QAAMoB,SAASF,gBAAgBlB,EAAC;AAChC,QAAM2F,kBAAkB3C,mBAAmB5B,MAAM,KAAK;AAGtD,QAAIuE,mBAAmBvE,kBAAkBwE,UAAU;AACjD,UAAID,iBAAiB;AACnBzD,cAAMK,0BAA0BnB;MAClC;IACF,OAAO;AAELpB,MAAAA,GAAE6F,yBAAwB;AAC1Bf,eAAS5C,MAAMK,2BAA2BmB,oBAAmB,CAAE;IACjE;;AAOF,MAAMoC,cAAc,SAAdA,aAAwB3E,OAA2B;AAAA,QAApB4E,aAAU,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAG;AAChD,QAAM3E,SAASF,gBAAgBC,KAAK;AACpC2C,wBAAmB;AAEnB,QAAIkC,kBAAkB;AAEtB,QAAI9D,MAAMG,eAAepD,SAAS,GAAG;AAInC,UAAMgH,iBAAiBjD,mBAAmB5B,MAAM;AAChD,UAAM8E,iBACJD,kBAAkB,IAAI/D,MAAME,gBAAgB6D,cAAc,IAAItD;AAEhE,UAAIsD,iBAAiB,GAAG;AAGtB,YAAIF,YAAY;AAEdC,4BACE9D,MAAMG,eAAeH,MAAMG,eAAepD,SAAS,CAAC,EACjDmF;QACP,OAAO;AAEL4B,4BAAkB9D,MAAMG,eAAe,CAAC,EAAEwB;QAC5C;iBACSkC,YAAY;AAIrB,YAAII,oBAAoBzF,UACtBwB,MAAMG,gBACN,SAAA,OAAA;AAAA,cAAGwB,oBAAiB,MAAjBA;AAAiB,iBAAOzC,WAAWyC;SACvC;AAED,YACEsC,oBAAoB,MACnBD,eAAehD,cAAc9B,UAC3BmE,YAAYnE,QAAQS,OAAOoC,eAAe,KACzC,CAACS,WAAWtD,QAAQS,OAAOoC,eAAe,KAC1C,CAACiC,eAAe7B,iBAAiBjD,QAAQ,KAAK,IAClD;AAOA+E,8BAAoBF;QACtB;AAEA,YAAIE,qBAAqB,GAAG;AAI1B,cAAMC,wBACJD,sBAAsB,IAClBjE,MAAMG,eAAepD,SAAS,IAC9BkH,oBAAoB;AAE1B,cAAME,mBAAmBnE,MAAMG,eAAe+D,qBAAqB;AACnEJ,4BAAkBK,iBAAiBjC;QACrC,WAAW,CAACjE,WAAWgB,KAAK,GAAG;AAG7B6E,4BAAkBE,eAAe7B,iBAAiBjD,QAAQ,KAAK;QACjE;MACF,OAAO;AAIL,YAAIkF,mBAAmB5F,UACrBwB,MAAMG,gBACN,SAAA,OAAA;AAAA,cAAG+B,mBAAgB,MAAhBA;AAAgB,iBAAOhD,WAAWgD;SACtC;AAED,YACEkC,mBAAmB,MAClBJ,eAAehD,cAAc9B,UAC3BmE,YAAYnE,QAAQS,OAAOoC,eAAe,KACzC,CAACS,WAAWtD,QAAQS,OAAOoC,eAAe,KAC1C,CAACiC,eAAe7B,iBAAiBjD,MAAM,IAC3C;AAOAkF,6BAAmBL;QACrB;AAEA,YAAIK,oBAAoB,GAAG;AAIzB,cAAMF,yBACJE,qBAAqBpE,MAAMG,eAAepD,SAAS,IAC/C,IACAqH,mBAAmB;AAEzB,cAAMD,oBAAmBnE,MAAMG,eAAe+D,sBAAqB;AACnEJ,4BAAkBK,kBAAiBxC;QACrC,WAAW,CAAC1D,WAAWgB,KAAK,GAAG;AAG7B6E,4BAAkBE,eAAe7B,iBAAiBjD,MAAM;QAC1D;MACF;IACF,OAAO;AAGL4E,wBAAkB1C,iBAAiB,eAAe;IACpD;AAEA,QAAI0C,iBAAiB;AACnB,UAAI7F,WAAWgB,KAAK,GAAG;AAKrBA,cAAMsE,eAAc;MACtB;AACAX,eAASkB,eAAe;IAC1B;;AAIF,MAAMO,WAAW,SAAXA,UAAqBpF,OAAO;AAChC,QACEpB,cAAcoB,KAAK,KACnBH,eAAea,OAAOG,mBAAmBb,KAAK,MAAM,OACpD;AACAA,YAAMsE,eAAc;AACpBzG,WAAKqG,WAAU;AACf;IACF;AAEA,QAAIxD,OAAOzB,aAAae,KAAK,KAAKU,OAAOvB,cAAca,KAAK,GAAG;AAC7D2E,kBAAY3E,OAAOU,OAAOvB,cAAca,KAAK,CAAC;IAChD;;AAGF,MAAMqF,aAAa,SAAbA,YAAuBxG,IAAG;AAC9B,QAAMoB,SAASF,gBAAgBlB,EAAC;AAEhC,QAAIgD,mBAAmB5B,MAAM,KAAK,GAAG;AACnC;IACF;AAEA,QAAIJ,eAAea,OAAOuD,yBAAyBpF,EAAC,GAAG;AACrD;IACF;AAEA,QAAIgB,eAAea,OAAO2D,mBAAmBxF,EAAC,GAAG;AAC/C;IACF;AAEAA,IAAAA,GAAEyF,eAAc;AAChBzF,IAAAA,GAAE6F,yBAAwB;;AAO5B,MAAMY,eAAe,SAAfA,gBAA2B;AAC/B,QAAI,CAACvE,MAAMM,QAAQ;AACjB;IACF;AAGA3D,qBAAiBC,aAAaC,WAAWC,IAAI;AAI7CkD,UAAMQ,yBAAyBb,OAAOI,oBAClC1B,MAAM,WAAY;AAChBuE,eAASpB,oBAAmB,CAAE;IAChC,CAAC,IACDoB,SAASpB,oBAAmB,CAAE;AAElC/B,QAAI+E,iBAAiB,WAAWhB,cAAc,IAAI;AAClD/D,QAAI+E,iBAAiB,aAAavB,kBAAkB;MAClDwB,SAAS;MACTC,SAAS;IACX,CAAC;AACDjF,QAAI+E,iBAAiB,cAAcvB,kBAAkB;MACnDwB,SAAS;MACTC,SAAS;IACX,CAAC;AACDjF,QAAI+E,iBAAiB,SAASF,YAAY;MACxCG,SAAS;MACTC,SAAS;IACX,CAAC;AACDjF,QAAI+E,iBAAiB,WAAWH,UAAU;MACxCI,SAAS;MACTC,SAAS;IACX,CAAC;AAED,WAAO5H;;AAGT,MAAM6H,kBAAkB,SAAlBA,mBAA8B;AAClC,QAAI,CAAC3E,MAAMM,QAAQ;AACjB;IACF;AAEAb,QAAImF,oBAAoB,WAAWpB,cAAc,IAAI;AACrD/D,QAAImF,oBAAoB,aAAa3B,kBAAkB,IAAI;AAC3DxD,QAAImF,oBAAoB,cAAc3B,kBAAkB,IAAI;AAC5DxD,QAAImF,oBAAoB,SAASN,YAAY,IAAI;AACjD7E,QAAImF,oBAAoB,WAAWP,UAAU,IAAI;AAEjD,WAAOvH;;AAOTA,SAAO;IACL,IAAIwD,SAAS;AACX,aAAON,MAAMM;;IAGf,IAAIC,SAAS;AACX,aAAOP,MAAMO;;IAGfsE,UAAQ,SAAA,SAACC,iBAAiB;AACxB,UAAI9E,MAAMM,QAAQ;AAChB,eAAO;MACT;AAEA,UAAMyE,aAAarE,UAAUoE,iBAAiB,YAAY;AAC1D,UAAME,iBAAiBtE,UAAUoE,iBAAiB,gBAAgB;AAClE,UAAMG,oBAAoBvE,UAAUoE,iBAAiB,mBAAmB;AAExE,UAAI,CAACG,mBAAmB;AACtBrD,4BAAmB;MACrB;AAEA5B,YAAMM,SAAS;AACfN,YAAMO,SAAS;AACfP,YAAMI,8BAA8BX,IAAIgC;AAExC,UAAIsD,YAAY;AACdA,mBAAU;MACZ;AAEA,UAAMG,mBAAmB,SAAnBA,oBAAyB;AAC7B,YAAID,mBAAmB;AACrBrD,8BAAmB;QACrB;AACA2C,qBAAY;AACZ,YAAIS,gBAAgB;AAClBA,yBAAc;QAChB;;AAGF,UAAIC,mBAAmB;AACrBA,0BAAkBjF,MAAMC,WAAWkF,OAAM,CAAE,EAAEC,KAC3CF,kBACAA,gBAAgB;AAElB,eAAO;MACT;AAEAA,uBAAgB;AAChB,aAAO;;IAGT/B,YAAU,SAAA,WAACkC,mBAAmB;AAC5B,UAAI,CAACrF,MAAMM,QAAQ;AACjB,eAAO;MACT;AAEA,UAAMgF,UAAO1F,eAAA;QACX2F,cAAc5F,OAAO4F;QACrBC,kBAAkB7F,OAAO6F;QACzBC,qBAAqB9F,OAAO8F;MAAmB,GAC5CJ,iBAAiB;AAGtBK,mBAAa1F,MAAMQ,sBAAsB;AACzCR,YAAMQ,yBAAyBC;AAE/BkE,sBAAe;AACf3E,YAAMM,SAAS;AACfN,YAAMO,SAAS;AAEf5D,uBAAiBW,eAAeT,WAAWC,IAAI;AAE/C,UAAMyI,eAAe7E,UAAU4E,SAAS,cAAc;AACtD,UAAME,mBAAmB9E,UAAU4E,SAAS,kBAAkB;AAC9D,UAAMG,sBAAsB/E,UAAU4E,SAAS,qBAAqB;AACpE,UAAMlC,cAAc1C,UAClB4E,SACA,eACA,yBAAyB;AAG3B,UAAIC,cAAc;AAChBA,qBAAY;MACd;AAEA,UAAMI,qBAAqB,SAArBA,sBAA2B;AAC/BtH,cAAM,WAAM;AACV,cAAI+E,aAAa;AACfR,qBAASG,mBAAmB/C,MAAMI,2BAA2B,CAAC;UAChE;AACA,cAAIoF,kBAAkB;AACpBA,6BAAgB;UAClB;QACF,CAAC;;AAGH,UAAIpC,eAAeqC,qBAAqB;AACtCA,4BACE1C,mBAAmB/C,MAAMI,2BAA2B,CAAC,EACrDgF,KAAKO,oBAAoBA,kBAAkB;AAC7C,eAAO;MACT;AAEAA,yBAAkB;AAClB,aAAO;;IAGT1I,OAAQ,SAAA,QAAA;AACN,UAAI+C,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AACjC,eAAO;MACT;AAEAN,YAAMO,SAAS;AACfoE,sBAAe;AAEf,aAAO;;IAGTpH,SAAU,SAAA,UAAA;AACR,UAAI,CAACyC,MAAMO,UAAU,CAACP,MAAMM,QAAQ;AAClC,eAAO;MACT;AAEAN,YAAMO,SAAS;AACfqB,0BAAmB;AACnB2C,mBAAY;AAEZ,aAAO;;IAGTqB,yBAAuB,SAAA,wBAACC,mBAAmB;AACzC,UAAMC,kBAAkB,CAAA,EAAGX,OAAOU,iBAAiB,EAAEnD,OAAOqD,OAAO;AAEnE/F,YAAMC,aAAa6F,gBAAgBjE,IAAI,SAACd,SAAO;AAAA,eAC7C,OAAOA,YAAY,WAAWtB,IAAI8B,cAAcR,OAAO,IAAIA;OAC5D;AAED,UAAIf,MAAMM,QAAQ;AAChBsB,4BAAmB;MACrB;AAEA,aAAO;IACT;;AAIF9E,OAAK8I,wBAAwBrG,QAAQ;AAErC,SAAOzC;AACT;;;ACxyB+nB,IAAMkJ,KAAE;AAAR,IAA8BC,KAAE,EAAC,MAAKD,IAAE,MAAK,GAAGA,EAAC,UAAS,QAAO,GAAGA,EAAC,YAAW,OAAM,GAAGA,EAAC,WAAU,QAAO,GAAGA,EAAC,YAAW,SAAQ,GAAGA,EAAC,aAAY,aAAY,GAAGA,EAAC,kBAAiB,WAAU,kBAAiB;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,YAAU,SAAS,cAAc,KAAK,GAAE,KAAK,UAAQ,MAAK,KAAK,OAAK,OAAG,KAAK,aAAW,MAAK,KAAK,SAAO,MAAI;AAAC,WAAK,OAAK;AAAA,IAAE,GAAE,SAAS,KAAK,YAAY,KAAK,SAAS,GAAE,KAAK,WAAWC,GAAG,MAAI,KAAK,MAAO,MAAI,KAAK,iBAAiB,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAJ3pC;AAI4pC,YAAO,UAAK,aAAL,mBAAe,KAAK;AAAA,EAAM;AAAA,EAAC,SAAQ;AAAC,UAAMF,KAAE,KAAK,IAAG,EAAC,MAAKC,IAAE,SAAQ,GAAE,OAAME,IAAE,UAASC,GAAC,IAAE,MAAKC,KAAEJ,MAAG,CAAC,CAAC,GAAEK,KAAE,EAAC,CAACP,GAAE,IAAI,GAAEM,IAAE,CAACN,GAAE,MAAM,GAAE,CAACM,GAAC,GAAEE,KAAED,GAAE,UAAS,EAAC,OAAMP,GAAE,aAAY,cAAaK,GAAE,OAAM,OAAMA,GAAE,OAAM,MAAK,MAAK,SAAQ,KAAK,QAAO,MAAK,SAAQ,GAAEE,GAAE,QAAO,EAAC,eAAc,QAAO,OAAMP,GAAE,UAAS,CAAC,CAAC,GAAEG,KAAE,GAAGF,EAAC,UAASF,KAAE,GAAGE,EAAC,YAAWQ,KAAEL,KAAEG,GAAE,MAAK,EAAC,IAAGJ,IAAE,OAAMH,GAAE,MAAK,GAAEI,EAAC,IAAE,MAAKM,KAAEJ,KAAEC,GAAE,OAAM,EAAC,MAAK,MAAK,OAAMP,GAAE,QAAO,MAAK,UAAS,mBAAkBG,IAAE,oBAAmBJ,IAAE,aAAY,KAAK,iBAAgB,GAAES,IAAEC,IAAE,KAAK,eAAeV,EAAC,CAAC,IAAE;AAAK,WAAOQ,GAAE,OAAM,EAAC,UAAS,IAAG,OAAM,KAAK,QAAQP,GAAE,MAAKO,EAAC,EAAC,GAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAJvwD;AAIwwD,eAAK,eAAL,mBAAiB,WAAW,EAAC,cAAa,MAAI;AAAA,IAAC,EAAC,IAAG,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,mBAAkB;AAAC,UAAK,EAAC,YAAWT,IAAE,MAAKC,GAAC,IAAE;AAAK,IAAAD,OAAIC,KAAED,GAAE,SAAS,IAAEA,GAAE,WAAW;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,SAAK,kBAAkB;AAAE,UAAM,IAAE,sBAAuB,MAAI;AAAC,WAAK,aAAW,gBAAEA,IAAE,EAAC,cAAa,SAAQ,cAAa,KAAK,OAAM,CAAC,GAAE,KAAK,iBAAiB;AAAA,IAAC,CAAE;AAAE,SAAK,WAAW,EAAG,MAAI,qBAAqB,CAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAQ,WAAM,YAAU,OAAOA,KAAEK,GAAE,OAAM,EAAC,OAAMP,GAAE,SAAQ,IAAGC,IAAE,WAAUC,GAAC,CAAC,IAAEA,GAAEA,EAAC,IAAEK,GAAE,OAAM,EAAC,OAAMP,GAAE,SAAQ,IAAGC,GAAC,GAAEC,GAAE,OAAO,CAAC,IAAEA,cAAa,cAAYK,GAAE,OAAM,EAAC,OAAMP,GAAE,SAAQ,IAAGC,IAAE,MAAKC,IAAE,aAAY,KAAK,cAAa,CAAC,IAAE;AAAA,EAAI;AAAA,EAAC,cAAcD,IAAE;AAAC,UAAMC,KAAE;AAAK,IAAAD,GAAE,YAAYC,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,GAAEA,GAAE,iBAAiB,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAxsF,IAAMS,KAAE;AAAiB,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,SAAK,YAAU,MAAK,KAAK,UAAQ,MAAK,KAAK,QAAM,MAAK,KAAK,eAAa,MAAK,KAAK,UAAQ,MAAK,KAAK,eAAa,MAAK,KAAK,MAAI,MAAK,KAAK,WAAS,MAAK,KAAK,QAAM,MAAK,KAAK,SAAO,MAAK,KAAK,YAAUD,IAAE,KAAK,UAAQC,IAAE,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAID,KAAE;AAAG,QAAG,KAAK,aAAW,KAAK,WAAS,KAAK,gBAAc,KAAK;AAAO,UAAG,QAAM,KAAK,WAAS,KAAK,aAAa,CAAAA,KAAE;AAAA,eAAW,KAAK,SAAQ;AAAC,cAAMC,KAAE,KAAK,IAAI;AAAE,YAAG,KAAK,UAAQA,IAAE;AAAC,WAAC,KAAK,UAAQA,MAAG,MAAI,KAAG,KAAK,UAAU,2BAAyBD,KAAE;AAAA,QAAG;AAAA,MAAC;AAAA;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAG,CAAC,KAAK,QAAQ,QAAM;AAAG,UAAMC,KAAE,KAAK,MAAM,GAAEC,KAAE,KAAK;AAAU,QAAGA,MAAGA,GAAE,iBAAeA,GAAE,WAAU;AAAC,UAAIC,KAAEF,GAAEC,GAAE,aAAa;AAAE,MAAAC,OAAIA,KAAEF,GAAEC,GAAE,aAAa,IAAE,CAAC,IAAG,KAAK,UAAQ,KAAK,QAAMA,GAAE,QAAOC,GAAED,GAAE,SAAS,IAAE,EAAC,OAAM,KAAK,OAAM,cAAa,KAAK,cAAa,SAAQ,KAAK,SAAQ,cAAa,KAAK,cAAa,KAAI,KAAK,KAAI,UAAS,KAAK,UAAS,OAAM,KAAK,OAAM,QAAO,KAAK,OAAM;AAAE,UAAG;AAAC,aAAK,QAAQ,QAAQF,IAAE,KAAK,UAAUC,EAAC,CAAC;AAAA,MAAC,SAAOG,IAAE;AAAC,eAAO,QAAQ,KAAKA,EAAC,GAAE;AAAA,MAAE;AAAC,aAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,UAAMH,KAAE,KAAK,MAAM,GAAEC,KAAE,KAAK;AAAU,QAAGA,MAAGA,GAAE,SAAOA,GAAE,cAAY,QAAM,KAAK,WAAS,KAAK,UAAQ,KAAK,IAAI,OAAK,KAAK,gBAAc,KAAK,QAAO;AAAC,YAAMF,KAAEE,GAAE,UAAU,QAAQ,WAAU,QAAQ,IAAE,oCAAmCD,KAAE,IAAI;AAAS,UAAGA,GAAE,OAAO,KAAI,MAAM,GAAEA,GAAE,OAAO,cAAa,KAAK,gBAAc,KAAK,KAAK,GAAEA,GAAE,OAAO,aAAYC,GAAE,KAAK,GAAED,GAAE,OAAO,mBAAkB,KAAK,eAAa,kBAAgB,cAAc,GAAE,cAAY,OAAO,UAAU,WAAW,WAAU,WAAWD,IAAEC,EAAC;AAAA,WAAM;AAAC,cAAMC,KAAE,IAAI;AAAe,QAAAA,GAAE,KAAK,QAAOF,EAAC,GAAEE,GAAE,KAAKD,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,QAAGC,MAAGA,GAAE,iBAAeA,GAAE,aAAW,KAAK,SAAQ;AAAC,YAAMC,KAAEF,GAAEC,GAAE,aAAa;AAAE,UAAGC,IAAE;AAAC,eAAOA,GAAED,GAAE,SAAS;AAAE,YAAG;AAAC,eAAK,QAAQ,QAAQF,IAAE,KAAK,UAAUC,EAAC,CAAC;AAAA,QAAC,SAAOG,IAAE;AAAC,kBAAQ,IAAIA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAF,OAAIA,GAAE,aAAW,MAAK,KAAK,YAAU;AAAA,EAAK;AAAA,EAAC,QAAO;AAAC,UAAMF,KAAE,KAAK,MAAM,GAAEC,KAAE,KAAK;AAAU,QAAGA,MAAGA,GAAE,iBAAeA,GAAE,WAAU;AAAC,UAAIC,KAAEF,GAAEC,GAAE,aAAa;AAAE,MAAAC,OAAIA,KAAEA,GAAED,GAAE,SAAS,GAAEC,OAAI,KAAK,QAAMA,GAAE,OAAM,KAAK,eAAaA,GAAE,cAAa,KAAK,UAAQA,GAAE,SAAQ,KAAK,eAAaA,GAAE,cAAa,KAAK,MAAIA,GAAE,KAAI,KAAK,WAASA,GAAE,UAAS,KAAK,QAAMA,GAAE,OAAM,KAAK,SAAOA,GAAE;AAAA,IAAQ;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAID,KAAE,CAAC;AAAE,QAAG,KAAK,SAAQ;AAAC,YAAMG,KAAE,KAAK,QAAQ,QAAQJ,EAAC;AAAE,UAAGI,GAAE,KAAG;AAAC,QAAAH,KAAE,KAAK,MAAMG,EAAC;AAAA,MAAC,SAAOF,IAAE;AAAC,gBAAQ,KAAKA,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC;AAACA,GAAE,UAAU,gBAAc;;;ACAl7D,IAAII;AAAE,IAAIC,KAAED,KAAE,cAAcE,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,QAAM,MAAK,KAAK,gBAAc,KAAI,KAAK,aAAW,OAAM,KAAK,WAAS,QAAO,KAAK,aAAW,OAAG,KAAK,cAAY,OAAG,KAAK,SAAO,MAAK,KAAK,yBAAuB,IAAG,KAAK,QAAM,OAAG,KAAK,mBAAiB,uBAAsB,KAAK,sBAAoB,oDAAmD,KAAK,YAAU,0BAAyB,KAAK,kBAAgB,OAAG,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAOF,GAAE,SAAS,KAAK,OAAO,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEC,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,GAAEC,EAAC;AAAE,IAAM,IAAEA;;;ACAj2C,IAAIE,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,uBAAqB,MAAK,KAAK,iBAAe,MAAK,KAAK,YAAU,MAAK,KAAK,YAAU,MAAK,KAAK,kBAAgB,MAAK,KAAK,eAAa,MAAK,KAAK,SAAO,MAAK,KAAK,0BAAwB,MAAK,KAAK,kBAAgB,MAAK,KAAK,cAAY;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAED,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,2BAA0B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,GAAEA,EAAC;AAAE,IAAME,KAAEF;;;ACA1K,IAAM,IAAE,CAAC;AAAT,IAAW,IAAE,CAAAG,OAAG;AAAC,QAAMC,KAAE,IAAI,EAAED,GAAE,eAAe,EAAE,MAAKE,KAAE,IAAI,EAAEF,GAAE,MAAM,EAAE,MAAKG,KAAE;AAAoB,SAAOA,GAAE,KAAKF,EAAC,KAAGE,GAAE,KAAKD,EAAC;AAAC;AAA/H,IAAiIE,KAAE,CAACJ,IAAEC,OAAI,CAAC,EAAE,EAAED,EAAC,KAAGC,MAAGA,GAAE,KAAM,CAAAA,OAAGA,GAAE,KAAKD,GAAE,MAAM,CAAE;AAAG,IAAI,IAAE;AAAN,IAAW,IAAE;AAAK,IAAG;AAAC,MAAE,OAAO,cAAa,IAAE,OAAO;AAAc,QAAM;AAAC;AAAC,IAAM,IAAN,cAAgBK,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAE,KAAK,gBAAc,WAAW,gBAAe,KAAK,cAAY,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,oBAAkB,MAAK,KAAK,wBAAsB,IAAG,KAAK,SAAO,MAAK,KAAK,kBAAgB,GAAE,KAAK,gBAAc,MAAK,KAAK,uBAAqB,OAAG,KAAK,aAAW,WAAS,OAAO,SAAO,OAAO,SAAO,OAAO,SAAS,QAAO,KAAK,aAAW,EAAE,OAAO,SAAS,IAAI,GAAE,KAAK,QAAM,MAAK,KAAK,6BAA2B,OAAG,KAAK,uBAAqB,MAAK,KAAK,cAAY,+BAA8B,KAAK,WAAS,kBAAiB,KAAK,aAAW,oBAAmB,KAAK,YAAU,kDAAiD,KAAK,aAAW,kCAAiC,KAAK,aAAW,CAAC,EAAC,OAAM,iCAAgC,eAAc,mBAAkB,iBAAgB,oDAAmD,GAAE,EAAC,OAAM,wDAAuD,eAAc,sBAAqB,iBAAgB,oDAAmD,GAAE,EAAC,OAAM,8DAA6D,eAAc,yBAAwB,iBAAgB,uDAAsD,GAAE,EAAC,OAAM,yDAAwD,eAAc,qBAAoB,iBAAgB,sDAAqD,GAAE,EAAC,OAAM,6CAA4C,eAAc,mBAAkB,iBAAgB,oDAAmD,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,gBAAc,+BAA8B,KAAK,mBAAiB,mYAAkY,KAAK,UAAQ,iCAAgC,KAAK,UAAQ,iCAAgC,KAAK,WAAS,kCAAiC,KAAK,oBAAkB,sCAAqC,KAAK,0BAAwB,MAAG,KAAK,4BAA0B,OAAG,KAAK,wBAAwB,GAAE,OAAO,iBAAiB,YAAY,CAAAL,OAAG;AAAC,WAAK,iBAAiBA,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAY,IAAAA,MAAGD,KAAEA,GAAE,OAAQ,CAAAA,OAAG,CAAC,KAAK,eAAeA,GAAE,MAAM,CAAE,GAAE,KAAK,cAAYC,GAAE,OAAOD,EAAC,KAAG,KAAK,cAAYA,IAAEA,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,mBAAiB,KAAK,SAAS,KAAKA,GAAE,eAAe,GAAEA,GAAE,aAAW,KAAK,SAAS,KAAKA,GAAE,MAAM;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAW,QAAGA,IAAE;AAAC,iBAAUC,MAAKF,IAAE;AAAC,cAAMA,KAAE,KAAK,cAAcE,GAAE,SAAS;AAAE,QAAAF,MAAGC,GAAE,OAAOA,GAAE,QAAQD,EAAC,GAAE,CAAC;AAAA,MAAC;AAAC,WAAK,aAAWC,GAAE,OAAOD,EAAC;AAAA,IAAC,MAAM,MAAK,aAAWA;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,IAAAA,KAAE,EAAC,GAAGA,GAAC;AAAE,UAAMC,KAAE,KAAK,aAAaD,GAAE,MAAM,GAAEE,KAAE,KAAK,cAAcD,EAAC;AAAE,QAAIE,IAAEG,KAAE,KAAK,eAAeL,EAAC,GAAE,IAAE;AAAG,IAAAK,OAAIA,KAAE,IAAIA,MAAEA,GAAE,SAAO,KAAK,uBAAuBL,EAAC,GAAEC,KAAEI,GAAE,YAAU,QAAIA,GAAE,kBAAgB,KAAK,gBAAgBL,EAAC,GAAEK,GAAE,YAAU,OAAI,KAAK,gBAAgB,CAACA,EAAC,CAAC,IAAGH,KAAE,KAAK,gBAAgBF,EAAC,GAAEE,MAAG,OAAOH,GAAE,QAAO,OAAO,OAAOG,IAAEH,EAAC,GAAE,IAAE,UAAKG,KAAE,IAAII,GAAE,EAAC,QAAOP,GAAE,QAAO,QAAOM,GAAE,QAAO,OAAMN,GAAE,OAAM,SAAQA,GAAE,SAAQ,KAAIA,GAAE,KAAI,OAAME,KAAE,WAAS,SAAQ,CAAC,GAAEC,GAAE,YAAU,CAACF,EAAC,GAAE,KAAK,YAAY,KAAKE,EAAC,IAAGA,GAAE,gBAAgB,KAAE,GAAE,KAAGA,GAAE,oBAAoB;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,EAAE,EAAC,aAAY,KAAK,YAAY,IAAK,CAAAH,OAAGA,GAAE,OAAO,CAAE,GAAE,YAAW,KAAK,WAAW,IAAK,CAAAA,OAAGA,GAAE,OAAO,CAAE,GAAE,aAAY,KAAK,YAAY,IAAK,CAAAA,OAAGA,GAAE,OAAO,CAAE,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,gBAAU,OAAOA,OAAIA,KAAE,KAAK,MAAMA,EAAC;AAAG,UAAMC,KAAED,GAAE,aAAYE,KAAEF,GAAE,YAAWG,KAAEH,GAAE;AAAY,QAAGC,IAAE;AAAC,YAAMD,KAAE,CAAC;AAAE,MAAAC,GAAE,QAAS,CAAAA,OAAG;AAAC,QAAAA,GAAE,UAAQA,GAAE,mBAAiBD,GAAE,KAAKC,GAAE,gBAAcA,KAAE,IAAIK,GAAEL,EAAC,CAAC;AAAA,MAAC,CAAE,GAAED,GAAE,UAAQ,KAAK,gBAAgBA,EAAC;AAAA,IAAC;AAAC,QAAGE,IAAE;AAAC,YAAMF,KAAE,CAAC;AAAE,MAAAE,GAAE,QAAS,CAAAD,OAAG;AAAC,QAAAA,GAAE,SAAOD,GAAE,KAAKC,GAAE,gBAAcA,KAAE,IAAI,EAAEA,EAAC,CAAC;AAAA,MAAC,CAAE,GAAED,GAAE,UAAQ,KAAK,mBAAmBA,EAAC;AAAA,IAAC;AAAC,IAAAG,MAAGA,GAAE,QAAS,CAAAH,OAAG;AAAC,MAAAA,GAAE,UAAQA,GAAE,SAAOA,GAAE,WAASA,GAAE,UAAQ,KAAK,IAAI,OAAKA,KAAEA,GAAE,gBAAcA,KAAE,IAAIO,GAAEP,EAAC,GAAG,gBAAgB,GAAE,KAAK,YAAY,KAAKA,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,QAAIC;AAAE,IAAAD,KAAE,KAAK,aAAaA,EAAC;AAAE,eAAUE,MAAK,KAAK,YAAY,KAAG,KAAK,uBAAuBA,GAAE,QAAOF,EAAC,GAAE;AAAC,MAAAC,KAAEC;AAAE;AAAA,IAAK;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAAC,QAAIC;AAAE,IAAAD,KAAE,KAAK,aAAaA,EAAC;AAAE,eAAUE,MAAK,KAAK,WAAW,KAAG,KAAK,uBAAuBA,GAAE,WAAUF,EAAC,GAAE;AAAC,MAAAC,KAAEC;AAAE;AAAA,IAAK;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAE;AAAC,QAAG,CAACD,GAAE;AAAO,QAAIE;AAAE,IAAAF,KAAE,KAAK,aAAaA,EAAC;AAAE,UAAMG,KAAE,KAAK,cAAcH,EAAC,IAAE,WAAS;AAAS,QAAGC,IAAE;AAAC,iBAAUK,MAAK,KAAK,YAAY,KAAG,KAAK,uBAAuBA,GAAE,QAAON,EAAC,KAAGC,OAAIK,GAAE,UAAQA,GAAE,UAAQH,IAAE;AAAC,QAAAD,KAAEI;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,YAAUA,MAAK,KAAK,YAAY,KAAG,KAAK,uBAAuBA,GAAE,QAAON,EAAC,KAAG,OAAK,KAAK,oBAAoBA,IAAEM,EAAC,KAAGA,GAAE,UAAQH,IAAE;AAAC,MAAAD,KAAEI;AAAE;AAAA,IAAK;AAAC,WAAOJ;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAE,IAAE;AAAG,IAAAF,OAAIC,KAAE,CAAC,CAACD,GAAE,OAAME,KAAEF,GAAE,OAAM,IAAE,UAAKA,GAAE,SAAQA,KAAE,EAAC,GAAGA,GAAC,GAAED,KAAE,KAAK,aAAaA,EAAC;AAAE,UAAMK,KAAE,IAAI,mBAAgBG,KAAE,EAAE;AAAE,QAAGP,GAAE,UAAQ,EAAEA,GAAE,QAAQ,MAAI;AAAC,MAAAI,GAAE,MAAM;AAAA,IAAC,CAAE,GAAE,EAAEA,IAAG,MAAI;AAAC,MAAAG,GAAE,OAAO,IAAIL,GAAE,iCAAgC,SAAS,CAAC;AAAA,IAAC,CAAE,GAAE,EAAEE,EAAC,EAAE,QAAOG,GAAE;AAAQ,IAAAP,GAAE,SAAOI,GAAE;AAAO,UAAMI,KAAE,KAAK,iBAAiBT,EAAC,GAAEU,KAAER,KAAE,KAAK,eAAeF,EAAC,IAAE;AAAK,QAAIW;AAAE,QAAGD,MAAGP,MAAGA,GAAE,WAAS,QAAMA,GAAE,QAAQ,WAAW,CAAAO,GAAE,QAAQ;AAAA,aAAUA,GAAE,QAAOC,KAAE,IAAIR,GAAE,mCAAkC,sCAAoCO,GAAE,SAAO,iDAA+CV,IAAE,EAAC,OAAMG,GAAC,CAAC,GAAEK,GAAE,OAAOG,EAAC,GAAEH,GAAE;AAAQ,UAAM,IAAE,KAAK,gBAAgBR,IAAEC,EAAC;AAAE,QAAG,EAAE,QAAOO,GAAE,QAAQ,CAAC,GAAEA,GAAE;AAAQ,QAAI,IAAE,KAAK,eAAeR,EAAC;AAAE,QAAG,EAAE,EAAC,EAAE,aAAW,KAAK,cAAcA,EAAC,MAAI,EAAE,eAAa,KAAK,gBAAgBA,EAAC,GAAE,EAAE,YAAU;AAAA,SAAQ;AAAC,YAAMC,KAAE,KAAK,gBAAgBD,EAAC;AAAE,UAAG,CAACC,GAAE,QAAOU,KAAE,IAAIR,GAAE,qCAAoC,2DAA2D,GAAEK,GAAE,OAAOG,EAAC,GAAEH,GAAE;AAAQ,UAAE,IAAIF,MAAE,EAAE,SAAO,KAAK,uBAAuBN,EAAC,GAAE,YAAU,OAAOC,MAAG,EAAE,kBAAgBA,IAAE,EAAE,YAAU,SAAK,EAAE,eAAaA,IAAE,EAAE,YAAU,OAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAE,aAAW,WAAS,EAAE,aAAW,KAAG,EAAE,EAAE,iBAAgB,KAAK,UAAU,KAAG,KAAK,WAAW,KAAM,CAAAD,OAAGA,GAAE,oBAAkB,EAAE,eAAgB,OAAK,EAAE,WAAS,EAAC,cAAaC,MAAGA,GAAE,cAAa,SAAQ,KAAK,eAAe,EAAE,gBAAgB,QAAQ,KAAK,mBAAkB,4BAA4B,GAAED,EAAC,EAAC,IAAG,KAAK,SAASA,IAAE,GAAEC,IAAEO,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBT,IAAE;AAAC,WAAO,KAAK,eAAeA,EAAC,IAAEA,GAAE,QAAQ,KAAK,eAAc,EAAE,EAAE,QAAQ,KAAK,kBAAiB,EAAE,KAAG,KAAG,KAAK,QAAQ,KAAKA,EAAC,KAAGA,GAAE,QAAQ,KAAK,SAAQ,IAAI,KAAG,KAAK,QAAQ,KAAKA,EAAC,KAAGA,GAAE,QAAQ,KAAK,SAAQ,IAAI,KAAG,KAAK,SAAS,KAAKA,EAAC,KAAGA,GAAE,QAAQ,KAAK,UAAS,IAAI,KAAG;AAAA,EAAE;AAAA,EAAC,cAAcA,IAAEC,IAAEC,IAAE;AAAC,UAAM,IAAE,KAAK,kBAAkB,KAAKF,GAAE,eAAe,GAAEK,KAAE,IAAI,EAAE,KAAK,UAAU,GAAEG,KAAER,GAAE;AAAwB,QAAIS,IAAEG,IAAEC,IAAEC,IAAEJ,IAAEC,IAAE,GAAE;AAAE,IAAAV,OAAI,IAAE,KAAK,iBAAeO,MAAG,KAAK,uBAAsB,IAAEA,MAAGA,KAAE,MAAI,IAAEA,MAAIN,OAAIO,KAAEP,GAAE,SAAQU,KAAEV,GAAE,WAAUW,KAAEX,GAAE,OAAMS,KAAET,GAAE,QAAO,IAAEA,GAAE,KAAIF,GAAE,mBAAiBE,GAAE,mBAAkBO,KAAEK,KAAEd,GAAE,wBAAsBc,KAAEd,GAAE,iBAAgBU,KAAE,IAAI,EAAEI,GAAE,YAAY,CAAC,GAAEd,GAAE,gBAAaE,MAAA,gBAAAA,GAAG,cAAW,CAAC,KAAG,WAASG,GAAE,WAAS,EAAEA,GAAE,KAAIS,IAAE,IAAE,KAAG,YAAUJ,GAAE,UAAQL,GAAE,SAAOK,GAAE,QAAM,WAASL,GAAE,QAAM,WAASK,GAAE,UAAQI,KAAEA,GAAE,QAAQ,YAAW,OAAO,EAAE,QAAQ,UAAS,OAAO;AAAI,UAAMC,KAAE,EAAC,OAAM,EAAC,SAAQ,YAAW,UAASd,MAAA,gBAAAA,GAAG,UAAS,UAASA,MAAA,gBAAAA,GAAG,UAAS,WAAUW,IAAE,OAAMC,IAAE,YAAW,GAAE,SAAQJ,MAAG,IAAE,KAAK,aAAW,MAAK,QAAOA,KAAE,YAAU,MAAK,GAAE,QAAO,GAAGT,GAAE,iBAAgB,GAAE,QAAO,QAAO,UAAS,aAAY,UAAS,KAAK,UAAUA,IAAEE,EAAC,GAAE,QAAOS,IAAE,GAAGT,MAAA,gBAAAA,GAAG,OAAM;AAAE,UAAIa,GAAE,kBAAgB;AAAI,WAAO,EAAED,IAAEC,EAAC,EAAE,KAAM,CAAAb,OAAG;AAAC,YAAMC,KAAED,GAAE;AAAK,UAAG,CAACC,MAAG,CAACA,GAAE,MAAM,QAAO,IAAIA,GAAE,0CAAyC,0BAA0B;AAAE,YAAMa,KAAEhB,GAAE;AAAO,aAAO,EAAEgB,EAAC,MAAI,EAAEA,EAAC,IAAE,CAAC,IAAGf,OAAI,EAAEe,EAAC,EAAEf,GAAE,QAAQ,IAAEA,GAAE,WAAUE,GAAE,WAAS,GAAEA;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,CAAC,CAAC,KAAK;AAAA,EAAK;AAAA,EAAC,kBAAkBH,IAAE;AAAC,WAAO,KAAK,eAAeA,IAAE,EAAE,EAAE,KAAM,CAAAA,OAAGA,GAAE,UAAW;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAE;AAAC,QAAI,IAAE;AAAG,WAAO,KAAK,cAAcF,IAAE,EAAC,QAAO,MAAE,CAAC,EAAE,KAAM,CAAAK,OAAG;AAAC,UAAIG;AAAE,YAAMC,KAAE,EAAC,GAAE,OAAM;AAAE,UAAG,aAAWJ,GAAE,MAAM,KAAGJ,OAAI,KAAK,gBAAgBD,EAAC,KAAGE,MAAGA,GAAE,OAAO,CAAAM,KAAEH,GAAE,SAAO,0CAAyCI,GAAE,YAAUR;AAAA,WAAM;AAAC,YAAG,CAACI,GAAE,MAAM,QAAM,EAAC,YAAWA,GAAC;AAAE,QAAAG,KAAEH,GAAE,SAAO;AAAA,MAAe;AAAA,WAAK;AAAC,YAAG,CAACA,GAAE,MAAM,QAAM,EAAC,YAAWA,GAAC;AAAE,QAAAG,KAAEH,GAAE,SAAO;AAAA,MAAgB;AAAC,aAAOA,GAAE,UAAQI,GAAE,QAAMJ,GAAE,QAAO,EAAEG,IAAE,EAAC,OAAMC,IAAE,UAAS,YAAW,CAAC,EAAE,KAAM,CAAAT,OAAG;AAAC,YAAG,UAAKA,GAAE,KAAK,MAAM,OAAM,IAAIG,GAAE,mCAAkC,oCAAoCE,GAAE,MAAM,MAAKL,GAAE,IAAI;AAAE,eAAO,IAAE,CAAC,CAACA,GAAE,KAAK,qBAAoB,EAAC,YAAWK,GAAC;AAAA,MAAC,CAAE,EAAE,MAAO,CAAAL,OAAG;AAAC,YAAG,sCAAoCA,GAAE,KAAK,OAAMA;AAAE,cAAMC,KAAED,GAAE,WAASA,GAAE,QAAQ;AAAW,YAAG,QAAMC,GAAE,OAAMI,GAAE,QAAQ,GAAE,IAAIF,GAAE,sCAAqC,wBAAwB;AAAE,YAAG,QAAMF,GAAE,OAAM,IAAIE,GAAE,kCAAkC;AAAE,eAAM,EAAC,YAAWE,GAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,EAAE,KAAM,CAAAL,QAAI,EAAC,YAAWA,GAAE,YAAW,UAAS,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,IAAAA,OAAI,QAAMA,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,UAAU,CAAC,IAAG,KAAK,yBAAyBe,GAAEf,EAAC,CAAC;AAAA,EAAE;AAAA,EAAC,2BAA2BA,IAAE;AAAC,SAAK,qBAAmBA;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,SAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,EAAE,GAAE,IAAE,MAAI;AAJ7mU;AAI8mU,MAAAM,MAAA,gBAAAA,GAAG,UAASK,MAAA,gBAAAA,GAAG,UAASH,MAAA,gBAAAA,GAAG,UAASH,MAAA,gBAAAA,GAAG,YAAU,UAAK,WAAL,mBAAa,WAAU,KAAK,SAAOA,KAAEC,KAAEK,KAAEH,KAAE;AAAA,IAAI,GAAEN,KAAE,MAAI;AAAC,QAAE,GAAE,KAAK,YAAU,MAAKF,GAAE,OAAO,IAAIA,GAAE,iCAAgC,SAAS,CAAC;AAAA,IAAC;AAAE,IAAAD,GAAE,UAAQ,EAAEA,GAAE,QAAQ,MAAI;AAAC,MAAAG,GAAE;AAAA,IAAC,CAAE;AAAE,QAAIG,KAAE,IAAI,KAAK;AAAgB,IAAAA,GAAE,WAAS,KAAK,gBAAgBR,EAAC,GAAEQ,GAAE,SAAOP,GAAE,QAAO,KAAK,SAAO,IAAI,KAAE,KAAK,OAAO,UAAQO,IAAE,KAAK,OAAO,OAAK,MAAG,KAAK,KAAK,eAAe;AAAE,QAAIC,KAAED,GAAE,GAAG,UAASH,EAAC,GAAES,KAAEF,GAAG,MAAI,KAAK,OAAO,MAAMP,EAAC,GAAEM,KAAEH,GAAE,GAAG,UAAU,CAAAR,OAAG;AAAC,WAAK,cAAcC,IAAED,IAAE,EAAC,SAAQE,GAAE,SAAQ,QAAOA,GAAE,OAAM,CAAC,EAAE,KAAM,CAAAI,OAAG;AAAC,UAAE;AAAE,cAAMD,KAAE,IAAIE,GAAE,EAAC,QAAOP,GAAE,UAAS,QAAOC,GAAE,QAAO,OAAMK,GAAE,OAAM,SAAQ,QAAMA,GAAE,UAAQ,OAAOA,GAAE,OAAO,IAAE,MAAK,KAAI,CAAC,CAACA,GAAE,KAAI,SAAQJ,GAAE,SAAQ,UAASI,GAAE,SAAQ,CAAC;AAAE,QAAAH,GAAE,QAAQE,EAAC;AAAA,MAAC,CAAE,EAAE,MAAO,CAAAL,OAAG;AAAC,QAAAQ,GAAE,QAAMR,IAAEQ,GAAE,YAAU;AAAA,MAAE,CAAE;AAAA,IAAC,CAAE;AAAE,WAAOL,GAAE;AAAA,EAAO;AAAA,EAAC,YAAYH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAU,EAAE;AAAE,UAAM,IAAE,KAAK;AAAU,QAAIE;AAAE,KAAAF,MAAA,gBAAAA,GAAG,WAAQ,EAAEA,GAAE,QAAQ,MAAI;AAAC,YAAMH,KAAE,KAAK,aAAW,KAAK,UAAU;AAAU,MAAAA,MAAG,CAACA,GAAE,SAAOA,GAAE,MAAM,IAAE,KAAK,UAAQ,EAAE;AAAA,IAAC,CAAE,GAAE,EAAE,UAAQA,IAAE,EAAE,SAAOC,IAAE,EAAE,SAAOC;AAAE,UAAMM,KAAEN,GAAE;AAAW,QAAGM,GAAE,YAAU,yBAAuBN,GAAE,YAAU,WAASA,GAAE,YAAU,CAACA,GAAE,SAAOD,GAAE,kBAAgB,MAAK;AAAC,UAAID,KAAE,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC;AAAE,MAAAK,KAAE,GAAEL,EAAC,GAAEQ,GAAE,eAAaH,IAAEL,KAAE,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,GAAEQ,GAAE,WAAS,GAAER,EAAC,GAAEQ,GAAE,KAAK,MAAIA,GAAE,eAAaH,KAAE;AAAA,IAAK,MAAM,CAAAG,GAAE,eAAa;AAAK,QAAIC,IAAEK,IAAEH,IAAE;AAAE,SAAK,kBAAkBN,EAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,YAAMU,KAAE,CAACb,MAAG,UAAKA,GAAE;AAAuB,MAAAD,GAAE,SAAOc,MAAGP,KAAE,IAAI,KAAK,mBAAgBA,GAAE,cAAY,MAAGA,GAAE,SAAOR,GAAE,QAAO,KAAK,SAAO,IAAI,KAAE,KAAK,OAAO,UAAQQ,IAAE,KAAK,OAAO,OAAK,MAAG,KAAK,KAAK,eAAe,GAAEK,KAAEL,GAAE,GAAG,UAAS,CAAC,GAAEE,KAAEC,GAAG,MAAI,KAAK,OAAO,MAAM,CAAC,GAAE,IAAEH,GAAE,GAAG,UAAU,MAAI;AAAC,UAAE,GAAE,KAAK,eAAeT,IAAEC,IAAEC,IAAEI,EAAC;AAAA,MAAC,CAAE,KAAG,KAAK,eAAeN,IAAEC,IAAEC,IAAEI,EAAC;AAAA,IAAC,CAAE;AAAE,UAAM,IAAE,MAAI;AAAC,QAAE,GAAE,KAAK,YAAU,MAAK,EAAE,OAAO,IAAIH,GAAE,iCAAgC,SAAS,CAAC;AAAA,IAAC,GAAE,IAAE,MAAI;AAJr4X;AAIs4X,MAAAW,MAAA,gBAAAA,GAAG,UAASH,MAAA,gBAAAA,GAAG,UAAS,uBAAG,UAASF,MAAA,gBAAAA,GAAG,YAAU,UAAK,WAAL,mBAAa,WAAU,KAAK,SAAO;AAAA,IAAI;AAAE,WAAO,EAAE;AAAA,EAAO;AAAA,EAAC,qBAAoB;AAAC,QAAG,KAAK,aAAY;AAAC,WAAK,YAAY,MAAM,EAAE,QAAS,CAAAT,OAAG;AAAC,QAAAA,GAAE,QAAQ;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,SAAK,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,sBAAsBA,KAAE,uCAAsC;AAAC,SAAK,0BAAwB,KAAK,uBAAuB,OAAO,GAAE,KAAK,yBAAuB,EAAE,QAAO,WAAW,CAAAC,OAAG;AAJryY;AAIsyY,WAAIA,GAAE,WAAS,KAAK,cAAYA,GAAE,OAAO,SAAS,aAAa,MAAI,sCAAkC,KAAAA,GAAE,SAAF,mBAAQ,OAAK;AAAC,cAAMC,KAAED,GAAE;AAAO,aAAK,cAAcD,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,UAAAE,GAAE,YAAY,EAAC,MAAK,0BAAyB,YAAW,EAAC,SAAQF,GAAE,SAAQ,QAAOA,GAAE,QAAO,KAAIA,GAAE,KAAI,OAAMA,GAAE,OAAM,QAAOA,GAAE,OAAM,EAAC,GAAEC,GAAE,MAAM;AAAA,QAAC,CAAE,EAAE,MAAO,CAAAD,OAAG;AAAC,UAAAE,GAAE,YAAY,EAAC,MAAK,qBAAoB,OAAM,EAAC,MAAKF,GAAE,MAAK,SAAQA,GAAE,QAAO,EAAC,GAAEC,GAAE,MAAM;AAAA,QAAC,CAAE;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,SAAK,2BAAyB,KAAK,uBAAuB,OAAO,GAAE,KAAK,yBAAuB;AAAA,EAAK;AAAA,EAAC,0BAAyB;AAJ91Z;AAI+1Z,QAAID,KAAE,OAAO,SAAS;AAAK,QAAGA,IAAE;AAAC,cAAMA,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,UAAU,CAAC;AAAG,YAAMC,KAAEc,GAAEf,EAAC;AAAE,UAAIE,KAAE;AAAG,UAAGD,GAAE,gBAAcA,GAAE,cAAYA,GAAE,SAAOA,GAAE,eAAe,UAAU,EAAE,KAAG;AAAC,QAAAA,GAAE,QAAM,KAAK,MAAMA,GAAE,KAAK,GAAEA,GAAE,MAAM,cAAY,KAAK,uBAAqBA,IAAEC,KAAE;AAAA,MAAG,QAAM;AAAA,MAAC;AAAA,eAASD,GAAE,SAAOA,GAAE,sBAAoB,QAAQ,IAAI,iCAAgCA,GAAE,OAAM,OAAMA,GAAE,iBAAiB,GAAE,oBAAkBA,GAAE,UAAQC,KAAE,MAAGD,GAAE,QAAQ,KAAG;AAAC,QAAAA,GAAE,QAAM,KAAK,MAAMA,GAAE,KAAK;AAAA,MAAC,QAAM;AAAA,MAAC;AAAC,MAAAC,OAAI,OAAO,SAAS,SAAK,KAAAD,GAAE,UAAF,mBAAS,SAAM;AAAA,IAAG;AAAC,QAAIA,KAAE,OAAO,SAAS;AAAO,QAAGA,IAAE;AAAC,cAAMA,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,UAAU,CAAC;AAAG,YAAMD,KAAEe,GAAEd,EAAC;AAAE,UAAIC,KAAE;AAAG,UAAGF,GAAE,QAAMA,GAAE,MAAM,KAAG;AAAC,QAAAA,GAAE,QAAM,KAAK,MAAMA,GAAE,KAAK,GAAEA,GAAE,MAAM,aAAWA,GAAE,MAAM,QAAM,KAAK,uBAAqBA,IAAEE,KAAE;AAAA,MAAG,QAAM;AAAA,MAAC;AAAA,eAASF,GAAE,SAAOA,GAAE,sBAAoB,QAAQ,IAAI,iCAAgCA,GAAE,OAAM,OAAMA,GAAE,iBAAiB,GAAE,oBAAkBA,GAAE,UAAQE,KAAE,MAAGF,GAAE,QAAQ,KAAG;AAAC,QAAAA,GAAE,QAAM,KAAK,MAAMA,GAAE,KAAK;AAAA,MAAC,QAAM;AAAA,MAAC;AAAC,UAAGE,IAAE;AAAC,cAAMD,KAAE,EAAC,GAAGD,GAAC;AAAE,SAAC,QAAO,SAAQ,qBAAoB,gBAAe,WAAU,OAAO,EAAE,QAAS,CAAAA,OAAG;AAAC,iBAAOC,GAAED,EAAC;AAAA,QAAC,CAAE;AAAE,cAAME,KAAE,EAAED,EAAC,GAAEE,KAAE,OAAO,SAAS,YAAUD,KAAE,IAAIA,EAAC,KAAG,SAAK,KAAAF,GAAE,UAAF,mBAAS,SAAM;AAAI,eAAO,QAAQ,aAAa,OAAO,QAAQ,OAAM,IAAGG,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAEC,IAAEC,IAAEI,IAAE,GAAE;AAAC,WAAON,KAAEA,GAAE,QAAQ,WAAU,QAAQ,GAAE,EAAE,GAAGA,EAAC,8BAA6B,EAAC,UAAS,aAAY,QAAO,QAAO,OAAMM,MAAG,IAAE,EAAC,YAAW,sBAAqB,MAAKL,IAAE,cAAaK,IAAE,WAAUJ,IAAE,eAAc,EAAC,IAAE,EAAC,YAAW,iBAAgB,eAAcD,IAAE,WAAUC,GAAC,EAAC,CAAC,EAAE,KAAM,CAAAF,OAAGA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,QAAGA,MAAG,WAAW,iBAAgB;AAAC,YAAMC,KAAG,IAAI,cAAa,OAAOD,EAAC;AAAE,aAAO,OAAO,OAAO,OAAO,WAAUC,EAAC,EAAE,KAAM,CAAAD,OAAG,GAAE,IAAI,WAAWA,EAAC,CAAC,CAAE;AAAA,IAAC;AAAC,WAAO,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAGA,GAAE,aAAW,KAAK,OAAO,KAAG,KAAK,4BAA2B;AAAC,YAAMA,KAAE,IAAIG,GAAE,iCAAgC,SAAS;AAAE,WAAK,aAAaH,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,QAAIC,IAAEC,IAAEG,IAAE,GAAED,KAAE;AAAG,UAAMG,KAAEP,MAAGA,GAAE,OAAMQ,KAAER,MAAGA,GAAE,UAASW,KAAE,KAAK,cAAcZ,EAAC,IAAE,WAAS,UAASa,KAAE,KAAK,YAAY,OAAQ,CAAAZ,OAAG,KAAK,uBAAuBA,GAAE,QAAOD,EAAC,KAAGC,GAAE,UAAQW,EAAE;AAAE,QAAGZ,KAAES,MAAGT,IAAEa,GAAE,OAAO,KAAG,MAAIA,GAAE,QAAO;AAAC,UAAGX,KAAEW,GAAE,CAAC,GAAEP,KAAE,KAAK,eAAeJ,GAAE,MAAM,GAAEC,KAAEG,MAAGA,GAAE,iBAAgB,IAAEH,KAAE,KAAK,eAAeA,IAAED,GAAE,MAAM,IAAE,QAAOG,KAAE,KAAK,oBAAoBL,IAAEE,EAAC,GAAE,CAACM,GAAE,QAAM,OAAKH,MAAGH,GAAE,UAAU,KAAKF,EAAC,GAAE,KAAK,aAAaA,IAAE,CAAC,GAAEE;AAAE,aAAKG,OAAIH,GAAE,UAAU,OAAOG,IAAE,CAAC,GAAE,KAAK,gBAAgBL,IAAE,CAAC;AAAA,IAAE,OAAK;AAAC,UAAIC,IAAEC;AAAE,UAAGW,GAAE,KAAM,CAAAL,QAAIN,KAAE,KAAK,oBAAoBF,IAAEQ,EAAC,GAAE,OAAKN,OAAID,KAAEO,IAAEF,KAAE,KAAK,eAAeL,GAAE,MAAM,GAAEE,KAAEG,MAAGA,GAAE,iBAAgB,IAAEH,KAAE,KAAK,eAAeA,IAAEF,GAAE,MAAM,IAAE,QAAOI,KAAEH,IAAE,MAAK,GAAEM,GAAE,CAAAP,OAAIA,GAAE,UAAU,OAAOI,IAAE,CAAC,GAAE,KAAK,gBAAgBL,IAAE,CAAC;AAAA,eAAWC,GAAE,QAAO,KAAK,aAAaD,IAAE,CAAC,GAAEC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAAC,QAAIC,KAAE,KAAK,cAAcD,EAAC;AAAE,QAAG,CAACC;AAAE,iBAAUC,MAAK,KAAK,WAAW,KAAG,KAAK,cAAcA,GAAE,WAAUF,EAAC,GAAE;AAAC,QAAAC,KAAEC;AAAE;AAAA,MAAK;AAAA;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAEC,IAAE;AAAC,IAAAA,MAAG,OAAK,KAAK,oBAAoBD,IAAEC,EAAC,KAAGA,GAAE,UAAU,KAAKD,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAG,IAAAD,OAAIC,KAAE,KAAK,oBAAoBF,IAAEC,EAAC,GAAEC,KAAE,MAAID,GAAE,UAAU,OAAOC,IAAE,CAAC;AAAA,EAAE;AAAA,EAAC,UAAUF,IAAEC,IAAE;AAAC,WAAOA,MAAGA,GAAE,WAAS,CAAC,EAAED,GAAE,sBAAqB,KAAK,UAAU,KAAG,CAAC,KAAK,gBAAgBA,GAAE,eAAe,KAAG,WAAS,OAAOA,GAAE,cAAc,KAAG,CAAC,EAAEA,GAAE,iBAAgB,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAED,EAAC;AAAE,WAAOC,GAAE,SAAO,QAAMA,GAAE,QAAM,QAAMA,GAAE,OAAK,MAAIA,GAAE,OAAK;AAAA,EAAG;AAAA,EAAC,uBAAuBD,IAAE;AAAC,UAAMC,KAAED,GAAE,YAAY;AAAE,QAAIE,KAAED,GAAE,QAAQ,KAAK,QAAQ;AAAE,WAAM,OAAKC,MAAG,KAAK,iBAAiBF,EAAC,MAAIE,KAAE,KAAK,UAAU,KAAKF,EAAC,IAAEA,GAAE,QAAQ,KAAK,WAAU,IAAI,EAAE,SAAOA,GAAE,OAAO,KAAK,UAAU,IAAG,OAAKE,MAAGD,GAAEA,EAAC,MAAIC,KAAED,GAAE,QAAQ,UAAU,IAAG,OAAKC,MAAG,QAAMD,GAAE,OAAO,EAAE,MAAIC,KAAED,GAAE,SAAO,IAAGC,KAAE,KAAGF,GAAE,UAAU,GAAEE,EAAC,IAAEF;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEC,IAAE;AAAC,WAAM,QAAMD,GAAE,OAAO,EAAE,MAAIA,KAAEA,GAAE,MAAM,GAAE,EAAE,IAAGA,KAAEA,GAAE,YAAY,GAAEC,KAAE,KAAK,uBAAuBA,EAAC,EAAE,YAAY,GAAED,KAAE,KAAK,wBAAwBA,EAAC,GAAEC,KAAE,KAAK,wBAAwBA,EAAC,IAAGD,KAAEA,GAAE,OAAOA,GAAE,QAAQ,GAAG,CAAC,QAAMC,KAAEA,GAAE,OAAOA,GAAE,QAAQ,GAAG,CAAC;AAAA,EAAE;AAAA,EAAC,wBAAwBD,IAAE;AAAC,UAAMC,KAAE,qDAAoDC,KAAE,8DAA6DC,KAAE;AAAwD,WAAOF,GAAE,KAAKD,EAAC,IAAEA,KAAEA,GAAE,QAAQC,IAAE,wBAAwB,IAAEC,GAAE,KAAKF,EAAC,IAAEA,KAAEA,GAAE,QAAQE,IAAE,2BAA2B,IAAEC,GAAE,KAAKH,EAAC,MAAIA,KAAEA,GAAE,QAAQG,IAAE,0BAA0B,IAAGH;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAME,MAAG,EAAE,QAAQ,YAAU,IAAI,YAAY,GAAEC,KAAED,KAAEF,GAAE,YAAY,EAAE,QAAQE,KAAE,GAAG,IAAE;AAAG,WAAM,OAAKC,OAAIH,KAAEA,GAAE,UAAUG,KAAED,GAAE,SAAO,CAAC,IAAGF,KAAE,EAAEA,EAAC,GAAE,EAAEA,EAAC,EAAE;AAAA,EAAI;AAAA,EAAC,eAAeA,IAAE;AAAC,WAAOA,GAAE,SAAS,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,WAAO,KAAK,UAAU,KAAKA,EAAC,KAAG,KAAK,WAAW,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,KAAK,eAAeA,EAAC,KAAG,KAAK,iBAAiBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAG,QAAG,KAAK,eAAeF,EAAC,KAAG,KAAK,eAAeC,EAAC,GAAE;AAAC,YAAME,KAAE,KAAK,WAAWH,EAAC,EAAE,YAAY,GAAEM,KAAE,KAAK,WAAWL,EAAC,EAAE,YAAY;AAAE,UAAGC,KAAEC,OAAIG,IAAE,CAACJ,IAAE;AAAC,cAAMF,KAAE;AAA2D,QAAAE,KAAEC,GAAE,QAAQH,IAAE,IAAI,MAAIM,GAAE,QAAQN,IAAE,IAAI;AAAA,MAAC;AAAA,IAAC,MAAM,MAAK,iBAAiBA,EAAC,KAAG,KAAK,iBAAiBC,EAAC,IAAEC,KAAE,OAAG,KAAK,cAAcF,EAAC,KAAG,KAAK,cAAcC,EAAC,KAAG,CAAC,KAAK,gBAAgBD,EAAC,MAAIE,KAAE;AAAI,WAAOA;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAE;AAAC,UAAME,KAAE,IAAI,EAAEF,GAAE,YAAY,CAAC,GAAEG,KAAE,KAAK;AAAc,QAAIG,KAAE,KAAK,WAAW,KAAM,CAAAN,OAAGA,GAAE,MAAM,KAAKE,GAAE,GAAG,CAAE;AAAE,WAAM,CAACI,MAAGH,OAAIG,KAAE,KAAK,uBAAuB,KAAK,uBAAuBH,GAAE,WAAW,GAAED,GAAE,GAAG,IAAGI,MAAG,EAAE,cAAYA,KAAE,EAAEJ,IAAE,EAAE,WAAU,IAAE,IAAGI,OAAIA,KAAE,KAAK,SAAS,KAAM,CAAAN,OAAG,KAAK,uBAAuBA,IAAEE,GAAE,GAAG,CAAE,IAAGI,KAAEA,MAAG,KAAK,WAAW,KAAKJ,GAAE,IAAI,GAAEI;AAAA,EAAC;AAAA,EAAC,cAAcN,IAAEC,IAAE;AAAC,QAAIC,KAAE,IAAGC,KAAE;AAAG,SAAK,WAAW,QAAS,CAACG,IAAE,MAAI;AAAC,aAAKJ,MAAGI,GAAE,MAAM,KAAKN,EAAC,MAAIE,KAAE,IAAG,OAAKC,MAAGG,GAAE,MAAM,KAAKL,EAAC,MAAIE,KAAE;AAAA,IAAE,CAAE;AAAE,QAAIG,KAAE;AAAG,QAAGJ,KAAE,MAAIC,KAAE,OAAK,MAAID,MAAG,MAAIA,KAAE,MAAIC,MAAG,MAAIA,OAAIG,KAAE,QAAI,MAAIJ,KAAE,MAAIC,MAAG,MAAIA,OAAIG,KAAE,QAAI,MAAIJ,KAAE,MAAIC,OAAIG,KAAE,QAAI,MAAIJ,MAAG,MAAIC,OAAIG,KAAE,QAAK,CAACA,IAAE;AAAC,YAAMJ,KAAE,KAAK,eAAeD,EAAC,GAAEE,KAAED,MAAGA,GAAE;AAAgB,MAAAC,MAAG,EAAED,EAAC,KAAG,KAAK,gBAAgBC,EAAC,KAAG,KAAK,cAAcH,IAAEG,EAAC,MAAIG,KAAE;AAAA,IAAG;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,oBAAoBN,IAAEC,IAAE;AAAC,QAAIC,KAAE;AAAG,aAAQC,KAAE,GAAEA,KAAEF,GAAE,UAAU,QAAOE,MAAI;AAAC,YAAMG,KAAEL,GAAE,UAAUE,EAAC;AAAE,UAAG,KAAK,oBAAoBH,IAAEM,EAAC,GAAE;AAAC,QAAAJ,KAAEC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAE;AAAC,WAAOA,GAAE,QAAQ,KAAK,eAAc,EAAE,EAAE,QAAQ,KAAK,kBAAiB,IAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,QAAIC,IAAEC,IAAEI;AAAE,QAAG,KAAK,eAAeN,EAAC,KAAG,KAAK,iBAAiBA,EAAC,GAAE;AAAC,YAAMM,KAAE,KAAK,uBAAuBN,EAAC;AAAE,aAAOC,KAAEK,KAAE,wBAAuBJ,KAAE,EAAEF,KAAEM,KAAE,cAAa,EAAC,OAAM,EAAC,GAAE,OAAM,EAAC,CAAC,EAAE,KAAM,CAAAN,OAAGA,GAAE,IAAK,GAAE,EAAC,UAASC,IAAE,SAAQC,GAAC;AAAA,IAAC;AAAC,QAAG,KAAK,gBAAgBF,EAAC,GAAE;AAAC,UAAIC,KAAE;AAAG,UAAG,KAAK,WAAW,KAAM,CAAAC,QAAIA,GAAE,MAAM,KAAKF,EAAC,MAAIC,KAAEC,GAAE,kBAAiB,CAAC,CAACD,GAAG,GAAEA,MAAG,KAAK,SAAS,KAAM,CAAAC,QAAI,KAAK,uBAAuBA,IAAEF,EAAC,MAAIC,KAAEC,KAAE,KAAK,cAAa,CAAC,CAACD,GAAG,GAAEA,OAAIK,KAAEN,GAAE,YAAY,EAAE,QAAQ,UAAU,GAAE,OAAKM,OAAIL,KAAED,GAAE,UAAU,GAAEM,EAAC,IAAE,KAAK,eAAcL,OAAIA,KAAE,KAAK,WAAWD,EAAC,IAAE,KAAK,cAAaC,IAAE;AAAC,cAAMC,KAAE,IAAI,EAAEF,EAAC,EAAE;AAAK,sBAAc,KAAKA,EAAC,KAAG,WAASE,OAAID,KAAEA,GAAE,QAAQ,UAAS,OAAO,IAAGA,KAAEA,GAAE,QAAQ,UAAS,QAAQ;AAAA,MAAC;AAAC,aAAOA;AAAA,IAAC;AAAC,QAAGD,GAAE,YAAY,EAAE,SAAS,0BAA0B,EAAE,QAAM;AAAA,EAAgD;AAAA,EAAC,4BAA4BA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE;AAAW,QAAGD,GAAE,MAAK;AAAC,YAAMM,KAAEH,GAAE;AAAa,aAAOA,GAAE,eAAa,MAAKA,GAAE,WAAS,MAAKA,GAAE,KAAK,GAAE,KAAK,eAAeD,GAAE,QAAOF,GAAE,MAAKC,GAAE,OAAM,KAAK,gBAAgBA,IAAE,IAAE,GAAEK,EAAC,EAAE,KAAM,CAAAA,OAAG;AAAC,cAAM,IAAE,IAAIC,GAAE,EAAC,QAAOD,GAAE,UAAS,QAAOJ,GAAE,QAAO,OAAMI,GAAE,cAAa,SAAQ,KAAK,IAAI,IAAE,MAAIA,GAAE,YAAW,KAAIA,GAAE,KAAI,YAAWN,GAAE,OAAM,YAAWG,GAAC,CAAC;AAAE,eAAOF,GAAE,SAAO,EAAE,QAAOE,GAAE,UAAQG,GAAE,UAAQ,IAAE,GAAEH,GAAE,eAAaG,GAAE,eAAcH,GAAE,QAAM,MAAKA,GAAE,UAAQG,GAAE,2BAAyB,KAAK,IAAI,IAAE,MAAIA,GAAE,2BAAyB,MAAKH,GAAE,SAAO,EAAE,QAAOA,GAAE,MAAI,EAAE,KAAIA,GAAE,KAAK,GAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,UAAMG,KAAE,IAAIC,GAAE,EAAC,QAAOP,GAAE,UAAS,QAAOE,GAAE,QAAO,OAAMF,GAAE,cAAa,SAAQ,KAAK,IAAI,IAAE,MAAI,OAAOA,GAAE,UAAU,GAAE,KAAI,WAASA,GAAE,KAAI,YAAWA,GAAE,OAAM,YAAWG,GAAC,CAAC;AAAE,WAAOF,GAAE,SAAOK,GAAE,QAAOH,GAAE,UAAQH,GAAE,UAAQ,IAAE,GAAEG,GAAE,eAAa,MAAKA,GAAE,QAAMG,GAAE,OAAMH,GAAE,UAAQG,GAAE,SAAQH,GAAE,SAAOG,GAAE,QAAOH,GAAE,MAAIG,GAAE,KAAIH,GAAE,KAAK,GAAE,QAAQ,QAAQG,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBN,IAAE;AAJl5oB;AAIm5oB,UAAMC,KAAE,KAAK;AAAU,QAAG,KAAK,YAAU,MAAKA,GAAE,KAAG,cAAc,KAAK,gBAAgB,IAAE,UAAK,wBAAL,mBAA0B,UAASD,GAAE,OAAM;AAAC,YAAME,KAAE,oBAAkBF,GAAE,OAAMG,KAAE,IAAIA,GAAED,KAAE,kCAAgC,0CAAyCA,KAAE,YAAU,YAAUF,GAAE,QAAM,QAAMA,GAAE,iBAAiB;AAAE,MAAAC,GAAE,OAAOE,EAAC;AAAA,IAAC,MAAM,MAAK,4BAA4BH,IAAEC,GAAE,QAAOA,GAAE,MAAM,EAAE,KAAM,CAAAD,OAAG;AAAC,MAAAC,GAAE,QAAQD,EAAC;AAAA,IAAC,CAAE,EAAE,MAAO,CAAAA,OAAG;AAAC,MAAAC,GAAE,OAAOD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,6BAA6BA,IAAE;AAAC,IAAAA,OAAI,QAAMA,GAAE,OAAO,CAAC,MAAIA,KAAEA,GAAE,UAAU,CAAC,IAAG,KAAK,yBAAyBe,GAAEf,EAAC,CAAC;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,GAAGF,EAAC,sCAAqC,EAAC,UAAS,aAAY,QAAO,QAAO,OAAM,EAAC,GAAE,QAAO,WAAUC,IAAE,OAAMC,GAAC,EAAC,CAAC,EAAE,KAAM,CAAAF,OAAGA,GAAE,KAAK,KAAM;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEC,IAAE;AAAC,WAAOD,KAAEA,GAAE,QAAQ,WAAU,QAAQ,GAAE,EAAE,GAAGA,EAAC,qCAAoC,EAAC,UAAS,aAAY,SAAQ,EAAC,yBAAwBC,IAAE,4BAA2B,OAAO,SAAS,KAAK,QAAQ,QAAO,EAAE,EAAC,GAAE,QAAO,QAAO,OAAM,EAAC,GAAE,QAAO,YAAW,GAAE,GAAE,iBAAgB,KAAE,CAAC,EAAE,KAAM,CAAAD,OAAGA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEC,IAAE;AAAC,QAAIC;AAAE,QAAG,KAAK,WAAW,KAAM,CAAAD,QAAIA,GAAE,MAAM,KAAKD,EAAC,MAAIE,KAAED,GAAE,gBAAe,CAAC,CAACC,GAAG,GAAEA,GAAE,QAAO,QAAQ,QAAQ,EAAC,QAAO,MAAG,gBAAe,OAAM,eAAcA,IAAE,YAAW,eAAc,eAAc,KAAE,CAAC;AAAE,SAAK,WAAW,WAAW,QAAQ,IAAEF,KAAEA,GAAE,QAAQ,WAAU,QAAQ,EAAE,QAAQ,UAAS,OAAO,IAAE,UAAU,KAAKC,EAAC,MAAID,KAAEA,GAAE,QAAQ,YAAW,OAAO,EAAE,QAAQ,UAAS,OAAO;AAAG,WAAO,EAAEA,IAAE,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,UAAS,aAAY,iBAAgB,KAAE,CAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAcC,KAAE,OAAO,SAAS,MAAKC,KAAE,KAAK,eAAeH,EAAC;AAAE,WAAM,EAAE,CAACC,MAAG,CAAC,KAAK,gBAAgBC,EAAC,KAAG,EAAEC,KAAEA,GAAE,aAAWA,GAAE,mBAAiB,KAAK,gBAAgBA,GAAE,eAAe,IAAE,KAAK,gBAAgBH,EAAC,MAAI,EAAE,KAAK,cAAcE,IAAEF,EAAC,KAAGC,OAAI,KAAK,uBAAuB,KAAK,uBAAuBA,GAAE,WAAW,GAAED,EAAC,KAAG,KAAK,cAAcC,GAAE,aAAYD,EAAC,MAAI,EAAEE,IAAEF,IAAE,IAAE;AAAA,EAAG;AAAA,EAAC,eAAeA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAI,IAAE;AAAG,UAAME,KAAEF,KAAEF,GAAE,uBAAqBA,GAAE;AAAgB,QAAGI,GAAE,KAAK,EAAE,YAAY,EAAE,WAAW,QAAQ,KAAG,CAAC,KAAK,WAAW,WAAW,QAAQ,KAAG,EAAEA,EAAC,MAAI,IAAE,CAAC,CAAC,KAAK,iBAAe,CAAC,CAAC,KAAK,cAAc,EAAC,aAAYL,IAAE,YAAWC,GAAC,CAAC,GAAE,CAAC,IAAG;AAAC,MAAAC,GAAE,IAAIC,GAAE,4BAA2B,iFAAiF,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,SAASH,IAAEC,IAAEC,IAAEC,IAAEG,IAAE,GAAE;AAAC,WAAOH,OAAIA,KAAE,EAAE,IAAGA,GAAE,UAAQH,IAAEG,GAAE,SAAOF,IAAEE,GAAE,WAASD,IAAEC,GAAE,SAAOG,IAAEH,GAAE,WAAS,GAAE,KAAK,QAAM,KAAK,uBAAuB,KAAK,uBAAuBH,EAAC,GAAE,KAAK,MAAM,OAAO,KAAG,KAAK,aAAW,KAAK,UAAU,aAAW,KAAK,UAAU,UAAU,MAAM,GAAE,KAAK,QAAQ,KAAKG,EAAC,KAAG,KAAK,QAAQ,KAAKA,EAAC,IAAE,KAAK,UAAUA,EAAC,GAAEA,GAAE;AAAA,EAAO;AAAA,EAAC,UAAUH,IAAE;AAAC,SAAK,QAAMA,IAAE,KAAK,6BAA2B;AAAG,UAAMC,KAAE,CAAAA,OAAG;AAAC,YAAMC,KAAEF,GAAE,YAAUA,GAAE,SAAS,UAASG,KAAEH,GAAE,SAAQM,KAAEN,GAAE;AAAS,UAAIgB,KAAE;AAAG,WAAK,YAAY,SAASf,EAAC,MAAIK,MAAG,KAAK,YAAY,SAASA,EAAC,KAAGA,GAAE,SAAOL,GAAE,QAAOK,GAAE,QAAML,GAAE,OAAMK,GAAE,UAAQL,GAAE,SAAQK,GAAE,WAASL,GAAE,UAASK,GAAE,MAAIL,GAAE,KAAIK,GAAE,eAAaL,GAAE,cAAae,KAAE,MAAGf,KAAEK,MAAG,KAAK,YAAY,KAAKL,EAAC,IAAGA,GAAE,cAAYA,GAAE,YAAU,CAAC,IAAGA,GAAE,UAAU,SAASC,MAAGC,EAAC,KAAGF,GAAE,UAAU,KAAKC,MAAGC,EAAC,GAAEF,GAAE,QAAM,KAAK,cAAcE,EAAC,IAAE,WAAS,UAASF,GAAE,gBAAgB;AAAE,YAAMI,KAAE,KAAK,SAAQG,KAAE,CAAC;AAAE,WAAK,UAAQ,CAAC,GAAEH,GAAE,QAAS,CAAAL,OAAG;AAAC,YAAG,CAAC,KAAK,oBAAoBG,IAAEH,GAAE,OAAO,GAAE;AAAC,gBAAME,KAAE,KAAK,WAAWF,GAAE,OAAO;AAAE,UAAAQ,GAAEN,EAAC,MAAIM,GAAEN,EAAC,IAAE,MAAGD,GAAE,UAAU,KAAKD,GAAE,OAAO;AAAA,QAAE;AAAA,MAAC,CAAE,GAAEA,GAAE,QAAQC,EAAC,GAAEI,GAAE,QAAS,CAAAL,OAAG;AAAC,aAAK,uBAAuB,KAAK,uBAAuBG,EAAC,GAAEH,GAAE,OAAO,IAAEA,GAAE,QAAQC,EAAC,IAAE,KAAK,QAAQ,KAAKD,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,QAAMA,GAAE,UAAQA,GAAE,SAAOA,GAAE,WAAS,MAAKgB,MAAG,KAAK,KAAK,qBAAoB,EAAC,YAAWf,GAAC,CAAC,GAAE,KAAK,QAAQ,SAAO,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC,IAAE,KAAK,QAAQ,UAAQ,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC;AAAA,IAAC,GAAEC,KAAE,CAAAD,OAAG;AAAC,MAAAD,GAAE,OAAOC,EAAC,GAAE,KAAK,QAAMD,GAAE,UAAQA,GAAE,SAAOA,GAAE,WAAS,MAAK,KAAK,QAAQ,SAAO,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC,IAAE,KAAK,QAAQ,UAAQ,KAAK,UAAU,KAAK,QAAQ,MAAM,CAAC;AAAA,IAAC,GAAEG,KAAE,CAACa,IAAER,IAAEC,IAAEG,OAAI;AAJ9pwB;AAI+pwB,YAAME,KAAEd,GAAE,QAAOU,KAAE,CAACV,GAAE,YAAU,UAAKA,GAAE,SAAS,QAAOW,KAAEG,GAAE,aAAW,KAAK,eAAed,GAAE,OAAO;AAAE,UAAI,GAAE;AAAE,UAAGgB,GAAE,CAAAf,GAAE,IAAIM,GAAE,EAAC,QAAOS,IAAE,QAAOF,GAAE,QAAO,OAAML,MAAG,MAAK,SAAQ,QAAMG,KAAE,OAAOA,EAAC,IAAE,MAAK,KAAI,CAAC,CAACJ,GAAC,CAAC,CAAC;AAAA,eAAU,WAAS,OAAO,YAAQ,UAAK,WAAW,UAAhB,mBAAwB,4BAAuB,UAAK,WAAW,UAAhB,mBAAwB,0BAAuB,KAAK,uBAAuB,KAAK,uBAAuB,KAAK,WAAW,MAAM,oBAAoB,CAAC,GAAER,GAAE,OAAO,GAAE;AAAC,eAAO,OAAO,YAAY,EAAC,MAAK,gCAA+B,GAAE,KAAK,WAAW,MAAM,oBAAoB,CAAC;AAAE,cAAMG,KAAE,EAAE,QAAO,WAAW,CAAAH,OAAG;AAAC,UAAAA,GAAE,WAAS,OAAO,UAAQA,GAAE,SAAO,6BAA2BA,GAAE,KAAK,QAAMG,GAAE,OAAO,GAAEH,GAAE,KAAK,WAAW,UAAQ,KAAK,IAAI,IAAEE,GAAE,IAAIC,GAAE,8CAA6C,yCAAyC,CAAC,IAAEF,GAAE,IAAIM,GAAEP,GAAE,KAAK,UAAU,CAAC,KAAG,wBAAsBA,GAAE,KAAK,SAAOG,GAAE,OAAO,GAAE,wBAAsBH,GAAE,KAAK,MAAM,OAAKE,GAAE,IAAIC,GAAE,8CAA6C,yCAAyC,CAAC,IAAED,GAAEC,GAAE,SAASH,GAAE,KAAK,KAAK,CAAC;AAAA,QAAG,CAAE;AAAE,WAAE,KAAAA,GAAE,aAAF,mBAAY,QAAQ,MAAI;AAAC,UAAAG,GAAE,OAAO;AAAA,QAAC,CAAE;AAAA,MAAC,WAASQ,IAAE;AAAC,YAAIK,KAAEL,GAAE;AAAW,YAAG,CAACK,IAAE;AAAC,gBAAMhB,KAAE,IAAIA,GAAEW,IAAE,CAAC,GAAEV,KAAE,IAAID,GAAEW,IAAE,CAAC;AAAE,UAAAX,GAAE,QAAQ,KAAGC,GAAE,QAAQ,IAAED,GAAE,UAAQC,GAAE,WAASe,KAAEhB,IAAEC,GAAE,QAAQ,MAAIe,KAAEf,IAAED,GAAE,QAAQ,KAAGgB,KAAEhB,GAAE,QAAQ,IAAEA,KAAEC,IAAEU,GAAE,aAAWK;AAAA,QAAC;AAAC,YAAGA,GAAE,QAAQ,GAAE;AAAC,cAAE,IAAIT,GAAE,EAAC,QAAOS,GAAE,QAAO,QAAOF,GAAE,QAAO,OAAME,GAAE,OAAM,SAAQA,GAAE,SAAQ,KAAIA,GAAE,KAAI,YAAWA,GAAC,CAAC;AAAE,gBAAMd,KAAES,GAAE,UAAQK,GAAE,SAAO,KAAK,gBAAgBhB,GAAE,OAAO;AAAE,UAAAE,MAAGc,GAAE,gBAAchB,GAAE,cAAYgB,GAAE,eAAa,KAAK,eAAeF,GAAE,QAAOE,GAAE,cAAaA,GAAE,KAAK,EAAE,KAAM,CAAAhB,QAAI,EAAE,UAAQ,KAAK,IAAI,IAAE,MAAIA,GAAE,YAAW,EAAE,QAAMA,GAAE,cAAa,EAAG,IAAE,QAAQ,QAAQ,CAAC,GAAEA,GAAE,YAAY,KAAM,CAAAA,OAAGE,KAAE,KAAK,eAAeF,GAAE,QAAOW,GAAE,OAAMX,GAAE,KAAK,EAAE,KAAM,CAAAC,QAAID,GAAE,QAAMC,IAAED,GAAG,EAAE,MAAO,MAAIA,EAAE,IAAEA,EAAE,EAAE,KAAM,CAAAA,OAAG;AAAC,YAAAC,GAAED,EAAC;AAAA,UAAC,CAAE,EAAE,MAAO,MAAI;AAAC,YAAAgB,MAAA,gBAAAA,GAAG,WAAUb,GAAE;AAAA,UAAC,CAAE,KAAGF,GAAE,CAAC;AAAA,QAAC,WAAS,KAAK,wBAAsB,KAAK,uBAAuBU,GAAE,WAAU,KAAK,qBAAqB,MAAM,SAAS,MAAI,KAAK,qBAAqB,gBAAc,KAAK,qBAAqB,QAAM,KAAK,qBAAqB,MAAM,QAAMK,GAAE,YAAUA,GAAE,eAAc;AAAC,gBAAMb,KAAE,KAAK;AAAqB,eAAK,uBAAqB,MAAKH,GAAE,cAAY,KAAK,4BAA4BG,IAAEQ,IAAEG,EAAC,EAAE,KAAM,CAAAd,OAAG;AAAC,YAAAC,GAAED,EAAC;AAAA,UAAC,CAAE,EAAE,MAAME,EAAC;AAAA,QAAC,OAAK;AAAC,gBAAMC,KAAE,MAAI;AAAC,YAAAO,KAAEV,GAAE,cAAY,KAAK,YAAYA,GAAE,SAAQc,IAAEH,IAAEX,GAAE,QAAQ,EAAE,KAAKC,IAAEC,EAAC,KAAG,IAAE,IAAIC,GAAE,sCAAqC,wBAAwB,GAAED,GAAE,CAAC;AAAA,UAAE;AAAE,eAAK,gBAAgBF,GAAE,OAAO,IAAEA,GAAE,cAAY,KAAK,iBAAiBc,GAAE,QAAOH,GAAE,KAAK,EAAE,KAAM,CAAAX,OAAG;AAAC,cAAEA,GAAE,WAAU,KAAK,YAAW,IAAE,KAAG,IAAE,IAAIO,GAAE,EAAC,QAAOP,GAAE,UAAS,QAAOc,GAAE,QAAO,SAAQ,KAAK,IAAI,IAAE,MAAId,GAAE,YAAW,OAAMA,GAAE,MAAK,CAAC,GAAEC,GAAE,CAAC,KAAGE,GAAE;AAAA,UAAC,CAAE,EAAE,MAAMA,EAAC,IAAEA,GAAE;AAAA,QAAC;AAAA,MAAC,WAASO,IAAE;AAAC,YAAG,KAAK,eAAeV,GAAE,SAAQc,IAAEZ,IAAEF,GAAE,MAAM,GAAE;AAAC,cAAIG,KAAEH,GAAE;AAAS,UAAAA,GAAE,WAASG,KAAEA,MAAG,CAAC,GAAEA,GAAE,UAAQ,OAAIH,GAAE,cAAY,KAAK,OAAOA,GAAE,SAAQc,IAAEX,EAAC,EAAE,KAAKF,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC,MAAM,KAAE,IAAIC,GAAE,sCAAqC,wBAAwB,GAAED,GAAE,CAAC;AAAA,IAAC,GAAE,IAAE,MAAI;AAAC,YAAMC,KAAEH,GAAE,QAAOM,KAAEH,GAAE,iBAAgBa,KAAEhB,GAAE;AAAS,UAAIK,IAAEG,IAAEC,IAAEG;AAAE,UAAGI,OAAIX,KAAEW,GAAE,OAAMR,KAAEQ,GAAE,OAAMP,KAAEO,GAAE,SAAQJ,KAAE,KAAK,gBAAgBN,IAAE,EAAC,OAAMD,IAAE,UAASL,GAAE,QAAO,CAAC,GAAE,CAACY;AAAE,mBAAUZ,MAAK,KAAK,YAAY,KAAG,KAAK,cAAcM,IAAEN,GAAE,MAAM,GAAE;AAAC,UAAAY,KAAEZ;AAAE;AAAA,QAAK;AAAA;AAAC,UAAGY,IAAE;AAAC,cAAMN,KAAE,KAAK,eAAeN,GAAE,SAAQY,GAAE,MAAM;AAAE,YAAGN,GAAE,CAAAL,GAAEK,EAAC;AAAA,iBAAUF,GAAED,IAAE,KAAK,UAAU,GAAE;AAAC,gBAAMH,KAAEY,GAAE,OAAO;AAAE,UAAAZ,GAAE,SAAOG,GAAE,QAAOH,GAAE,YAAU,MAAKC,GAAE,IAAIM,GAAEP,EAAC,CAAC;AAAA,QAAC,OAAK;AAAC,WAACA,GAAE,cAAY,KAAK,cAAc,KAAK,eAAeY,GAAE,MAAM,GAAE,MAAK,EAAC,WAAUZ,GAAE,SAAQ,OAAMY,GAAE,OAAM,QAAOZ,GAAE,SAAS,QAAO,KAAIY,GAAE,IAAG,CAAC,GAAG,KAAM,CAAAV,OAAG;AAAC,YAAAD,GAAE,IAAIM,GAAE,EAAC,QAAOK,MAAA,gBAAAA,GAAG,QAAO,QAAOT,GAAE,QAAO,OAAMD,GAAE,OAAM,SAAQ,QAAMA,GAAE,UAAQ,OAAOA,GAAE,OAAO,IAAE,MAAK,KAAI,CAAC,CAACA,GAAE,KAAI,SAAQF,GAAE,QAAO,UAASE,GAAE,SAAQ,CAAC,CAAC;AAAA,UAAC,GAAGA,EAAC;AAAA,QAAC;AAAA,MAAC,OAAK;AAAC,aAAK,QAAM,MAAKG,OAAIL,GAAE,SAAS,QAAM;AAAM,SAACA,GAAE,cAAY,KAAK,cAAcM,GAAE,QAAQ,QAAO,UAAU,GAAE,EAAC,UAASN,GAAE,SAAQ,cAAaG,GAAE,cAAa,QAAOH,GAAE,SAAS,QAAO,OAAMK,IAAE,OAAMG,IAAE,QAAOC,GAAC,CAAC,GAAG,KAAM,MAAI;AAAC,eAAK,SAAST,GAAE,SAAQA,GAAE,QAAOA,GAAE,UAASA,IAAEA,GAAE,MAAM;AAAA,QAAC,GAAI,CAAAC,OAAG;AAAC,UAAAD,GAAE,UAAQA,GAAE,SAAOA,GAAE,WAAS,MAAKA,GAAE,OAAOC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAA,IAAC;AAAE,SAAK,eAAaC;AAAE,UAAMM,KAAER,GAAE,OAAO,iBAAgBY,KAAE,KAAK,cAAcZ,GAAE,OAAO,GAAEc,KAAEd,GAAE,OAAO;AAAa,IAAAc,KAAEA,GAAE,QAAQ,KAAM,CAAAb,OAAG;AAAC,YAAMC,KAAEF,GAAE;AAAO,UAAGE,GAAE,cAAa;AAAC,QAAAA,GAAE,uBAAqBA,GAAE,aAAa,UAASA,GAAE,eAAa,MAAKA,GAAE,mBAAiB,EAAE,6BAA4BD,EAAC,KAAG,EAAE,4BAA2BA,EAAC,KAAG,EAAE,mBAAkBA,EAAC,MAAI,MAAKC,GAAE,0BAAwB,EAAE,oCAAmCD,EAAC,KAAG,MAAKC,GAAE,iBAAeD,GAAE,gBAAeC,GAAE,eAAaD,GAAE;AAAa,cAAMD,KAAEE,GAAE,kBAAgBD,GAAE;AAAgB,QAAAD,MAAG,KAAK,SAAS,KAAKA,EAAC;AAAA,MAAC;AAAC,MAAAY,MAAGV,GAAE,kBAAgB,EAAE,IAAEC,GAAE;AAAA,IAAC,GAAI,MAAI;AAAC,MAAAH,GAAE,OAAO,eAAa;AAAK,YAAMC,KAAE,IAAIE,GAAE,iDAAgD,2DAA2D;AAAE,MAAAD,GAAED,EAAC;AAAA,IAAC,CAAE,IAAEW,MAAGJ,KAAE,EAAE,IAAER,GAAE,OAAO,WAASA,GAAE,OAAO,SAAS,QAAQ,KAAM,CAAAC,OAAG;AAAC,YAAMC,KAAE,CAAC;AAAE,UAAIC,IAAEG,IAAEU,IAAEX;AAAE,aAAOJ,OAAIE,KAAEF,GAAE,QAAMA,GAAE,KAAK,UAASC,GAAE,WAASC,IAAED,GAAE,SAAOD,GAAE,QAAOK,KAAEL,GAAE,eAAcI,KAAE,WAAWJ,GAAE,cAAc,GAAE,kBAAgBA,GAAE,eAAae,KAAEf,GAAE,gBAAeD,GAAE,OAAO,iBAAeK,KAAGL,GAAE,OAAO,cAAY,CAAC,CAACG,IAAEA,MAAG,KAAK,uBAAqB,KAAK,cAAcH,GAAE,QAAO,MAAK,EAAC,KAAIE,GAAE,OAAM,CAAC,EAAE,MAAO,MAAI,IAAK,EAAE,KAAM,CAAAF,QAAIE,GAAE,cAAYF,MAAGA,GAAE,OAAME,GAAE,kBAAgBF,MAAGA,GAAE,SAAQE,GAAG,IAAE,CAACC,MAAGG,MAAGD,MAAG,OAAK,CAAC,KAAK,eAAeL,GAAE,OAAO,IAAE,KAAK,mBAAmB,EAAC,WAAUA,GAAE,OAAO,QAAO,eAAcgB,IAAE,cAAahB,GAAE,OAAO,SAAS,aAAY,CAAC,EAAE,MAAO,MAAI,IAAK,EAAE,KAAM,MAAIE,EAAE,IAAEA;AAAA,IAAC,CAAE,EAAE,MAAO,MAAI,IAAK,EAAE,KAAM,CAAAD,OAAG;AAAC,MAAAD,GAAE,OAAO,WAAS,MAAKC,KAAEE,GAAEF,GAAE,UAASA,GAAE,QAAOA,GAAE,aAAYA,GAAE,eAAe,IAAEE,GAAE;AAAA,IAAC,CAAE,IAAEA,GAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBH,IAAE;AAAC,QAAIC,IAAEC,KAAE,MAAKI,KAAEN,GAAE;AAAU,UAAM,IAAEA,GAAE,eAAcK,KAAEL,GAAE,cAAaQ,KAAE,CAAC,KAAK,qBAAmB,KAAK,2BAAyB,CAAC,KAAK;AAA0B,QAAGA,IAAE;AAAC,MAAAN,KAAE,OAAO,SAAS;AAAK,UAAIF,KAAEE,GAAE,QAAQ,GAAG;AAAE,MAAAF,KAAE,OAAKE,KAAEA,GAAE,MAAM,GAAEF,EAAC,IAAGA,KAAEE,GAAE,OAAO,iBAAiB,GAAEA,KAAEF,KAAE,KAAGE,GAAE,MAAM,GAAEF,EAAC,IAAE;AAAA,IAAI;AAAC,WAAOQ,MAAGN,MAAG,KAAK,4BAA0B,MAAGD,KAAE,EAAEC,KAAE,iBAAgB,EAAC,OAAM,EAAC,GAAE,OAAM,EAAC,CAAC,EAAE,KAAM,MAAI;AAAC,WAAK,oBAAkB,IAAI,EAAE,EAAC,OAAM,gBAAe,kBAAiBA,KAAE,4BAA2B,CAAC;AAAA,IAAC,CAAE,KAAGD,KAAE,QAAQ,QAAQ,GAAEA,GAAE,KAAM,MAAI;AAAC,UAAG,KAAK,kBAAkB,QAAOK,KAAEA,GAAE,QAAQ,WAAU,QAAQ,GAAE,EAAEA,KAAE,4CAA2C,EAAC,OAAM,EAAC,WAAUD,IAAE,WAAU,KAAK,kBAAkB,OAAM,cAAa,EAAE,KAAK,kBAAkB,gBAAgB,GAAE,GAAE,OAAM,EAAC,CAAC,EAAE,KAAM,CAAAL,OAAG;AAAC,YAAGA,GAAE,KAAK,OAAM;AAAC,gBAAMC,KAAE,KAAK,kBAAkB,MAAM;AAAE,UAAAD,GAAE,KAAK,UAAQ,IAAEC,GAAE,YAAU,aAAWD,GAAE,KAAK,OAAO,YAAY,IAAE,MAAI,IAAEC,GAAE,YAAUK,IAAEL,GAAE,QAAM,WAAS,OAAO,OAAK,EAAE,EAAEK,IAAE,KAAK,UAAU,KAAG,KAAK,WAAW,KAAM,CAAAN,OAAGA,GAAE,MAAM,KAAKM,EAAC,KAAGN,GAAE,MAAM,KAAK,KAAK,UAAU,CAAE,IAAG,KAAK,WAAW,KAAKC,EAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAM,IAAED,GAAE,YAAWM,KAAE,EAAC,WAAUN,GAAE,UAAS;AAAE,KAACA,GAAE,SAAOA,GAAE,mBAAiB,OAAO,SAAS,SAAOM,GAAE,OAAK,OAAO,SAAS,OAAM,EAAE,aAAWA,GAAE,MAAI,EAAE;AAAU,UAAMC,KAAE,EAAC,WAAUP,GAAE,OAAM,eAAc,EAAE,eAAa,SAAO,SAAQ,OAAM,KAAK,UAAUM,EAAC,GAAE,YAAWN,GAAE,YAAW,QAAOA,GAAE,QAAO,cAAa,KAAK,gBAAgBA,IAAE,CAAC,CAAC,EAAE,YAAY,EAAC;AAAE,IAAAA,GAAE,eAAaO,GAAE,cAAY,OAAIP,GAAE,eAAaA,GAAE,WAASO,GAAE,uBAAqBP,GAAE,SAAQ,CAACA,GAAE,SAAO,KAAK,gBAAgBF,EAAC,MAAIS,GAAE,uBAAqB,OAAI,EAAE,iBAAeA,GAAE,iBAAeN,MAAG,EAAE,cAAaM,GAAE,wBAAsBN,KAAE,SAAO;AAAS,UAAMS,KAAEV,GAAE,UAAU,QAAQ,WAAU,QAAQ,IAAE,6BAA4BW,KAAED,KAAE,MAAI,EAAEH,EAAC;AAAE,QAAGP,GAAE,OAAM;AAAC,YAAMF,KAAE,OAAO,KAAKa,IAAE,kBAAiBX,GAAE,mBAAmB;AAAE,UAAGF,GAAE,CAAAA,GAAE,MAAM,GAAE,KAAK,UAAU,YAAUA,IAAE,KAAK,mBAAiB,YAAa,MAAI;AAAC,YAAGA,GAAE,QAAO;AAAC,wBAAc,KAAK,gBAAgB,GAAE,KAAK,oBAAoB,OAAO;AAAE,gBAAMA,KAAE,KAAK;AAAU,cAAGA,IAAE;AAAC,kBAAMC,KAAE,IAAIE,GAAE,iCAAgC,SAAS;AAAE,YAAAH,GAAE,OAAOC,EAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAG,GAAG,GAAE,KAAK,sBAAoB,EAAE,QAAO,CAAC,oBAAmB,6BAA6B,GAAG,CAAAD,OAAG;AAAC,+BAAqBA,GAAE,OAAK,KAAK,qBAAqBA,GAAE,MAAM,IAAE,KAAK,6BAA6BA,GAAE,MAAM;AAAA,MAAC,CAAE;AAAA,WAAM;AAAC,cAAMA,KAAE,IAAIG,GAAE,kCAAiC,SAAS;AAAE,aAAK,UAAU,OAAOH,EAAC;AAAA,MAAC;AAAA,IAAC,MAAM,MAAK,6BAA2B,MAAG,KAAK,qBAAmB,KAAK,mBAAmB,EAAC,iBAAgBS,IAAE,cAAaG,IAAE,aAAYZ,IAAE,YAAWC,IAAE,WAAUC,GAAC,CAAC,IAAE,OAAO,SAAS,OAAKW;AAAA,EAAC;AAAA,EAAC,gBAAgBb,IAAEC,IAAE;AAAC,UAAMC,KAAE,OAAO,SAAS,KAAK,QAAQ,QAAO,EAAE;AAAE,QAAGF,GAAE,MAAM,QAAO,EAAEA,GAAE,gBAAgB;AAAE,QAAGC,IAAE;AAAC,YAAMD,KAAE,EAAEE,EAAC;AAAE,aAAOF,GAAE,SAAO,CAAC,QAAO,SAAQ,qBAAoB,gBAAe,WAAU,OAAO,EAAE,QAAS,CAAAC,OAAG;AAAC,eAAOD,GAAE,MAAMC,EAAC;AAAA,MAAC,CAAE,GAAE,GAAED,GAAE,MAAKA,GAAE,KAAK;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAC;AAAC,EAAE,UAAU,gBAAc;AAAoC,IAAIK,KAAE,cAAcF,GAAE,gBAAe;AAAA,EAAC,YAAYL,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,qBAAmB,GAAEA,MAAGA,GAAE,eAAa,KAAK,aAAWA,GAAE;AAAA,EAAW;AAAA,EAAC,aAAY;AAAC,SAAK,YAAU,KAAK,aAAW,CAAC,GAAE,QAAM,KAAK,iBAAe,KAAK,eAAa,KAAK,IAAI;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,UAAMA,KAAEE,GAAE,eAAe,KAAK,MAAM,GAAED,KAAED,MAAGA,GAAE,iBAAgBG,KAAE,CAAC,CAACF,MAAG,aAAW,KAAK,OAAMK,KAAEH,MAAGC,GAAEJ,IAAEE,GAAE,UAAU,GAAE,IAAEF,GAAE,aAAYK,KAAE,KAAGH,GAAE,sBAAqBM,KAAE,EAAE,KAAK,MAAM,GAAEC,KAAED,MAAGA,GAAE,KAAK,MAAM;AAAE,QAAII,IAAEC,KAAE,KAAK,aAAW,KAAK,UAAU,CAAC,GAAEC,KAAEX,KAAED,GAAE,eAAeD,EAAC,IAAE,MAAKS,KAAE,EAAC,UAAS,KAAK,QAAO,UAASD,GAAC;AAAE,QAAG,KAAG,CAACJ,GAAE;AAAO,IAAAF,MAAG,CAACW,MAAGZ,GAAE,YAAY,KAAM,CAAAF,QAAIE,GAAE,cAAcD,IAAED,GAAE,MAAM,MAAIc,KAAEd,KAAG,CAAC,CAACc,GAAG;AAAE,UAAMH,KAAEG,KAAEZ,GAAE,eAAeY,GAAE,QAAO,KAAK,MAAM,IAAE;AAAK,QAAG,CAACX,MAAGQ,IAAE;AAAC,UAAG,CAACL,IAAE;AAAC,YAAGH,GAAE,CAAAS,KAAE,EAAC,WAAUC,IAAE,OAAMF,MAAGA,GAAE,OAAM,KAAIA,MAAGA,GAAE,IAAG;AAAA,iBAAUN,GAAE,CAAAK,KAAE,MAAKE,KAAE,EAAC,KAAI,KAAK,IAAG;AAAA,aAAM;AAAC,cAAG,CAACH,IAAE;AAAC,gBAAIR;AAAE,mBAAOY,OAAIA,KAAEX,GAAE,aAAaW,EAAC,GAAE,KAAK,YAAU,GAAEZ,KAAEC,GAAE,SAASW,IAAEb,IAAE,MAAK,MAAK,KAAK,SAAQ,IAAI,GAAEC,GAAE,KAAM,MAAI;AAAC,mBAAK,YAAU,GAAE,KAAK,oBAAoB;AAAA,YAAC,CAAE,EAAE,MAAO,MAAI;AAAC,mBAAK,YAAU;AAAA,YAAC,CAAE,IAAGA;AAAA,UAAC;AAAC,eAAK,YAAUW,KAAE,EAAC,SAAQ,KAAE;AAAA,QAAE;AAAC,eAAOV,GAAE,cAAcC,KAAEW,KAAEd,IAAEG,KAAE,OAAKO,IAAEE,EAAC,EAAE,KAAM,CAAAZ,OAAG;AAAC,eAAK,QAAMA,GAAE,OAAM,KAAK,UAAQ,QAAMA,GAAE,UAAQ,OAAOA,GAAE,OAAO,IAAE,MAAK,KAAK,eAAa,KAAK,IAAI,GAAE,KAAK,WAASA,GAAE,UAAS,KAAK,gBAAgB,GAAE,KAAK,oBAAoB;AAAA,QAAC,CAAE,EAAE,MAAO,MAAI;AAAA,QAAC,CAAE;AAAA,MAAC;AAAC,MAAAW,MAAA,gBAAAA,GAAG;AAAA,IAAc;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,iBAAW,KAAK,SAAOT,GAAE,YAAY,QAAS,CAAAF,OAAG;AAAC,YAAMC,KAAEC,GAAE,eAAeF,GAAE,MAAM,GAAEG,KAAEF,MAAGA,GAAE;AAAgB,MAAAD,OAAI,QAAMA,GAAE,WAAS,KAAK,UAAQG,MAAG,aAAWH,GAAE,UAAQE,GAAE,uBAAuB,KAAK,QAAOC,EAAC,KAAGD,GAAE,cAAcC,IAAE,KAAK,MAAM,OAAKC,GAAEH,IAAEC,GAAE,UAAU,KAAGF,GAAE,QAAM,KAAK,OAAMA,GAAE,UAAQ,KAAK,SAAQA,GAAE,eAAa,KAAK,cAAaA,GAAE,WAAS,KAAK,UAASA,GAAE,gBAAgB,KAAGA,GAAE,aAAa;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,iBAAa,KAAK,aAAa;AAAE,UAAMC,KAAE,KAAK,UAAQC,GAAE,eAAe,KAAK,MAAM,GAAEC,KAAEF,MAAGA,GAAE,iBAAgBK,KAAEH,MAAGD,GAAE,eAAeC,EAAC;AAAE,cAAKH,MAAGG,MAAG,aAAW,KAAK,UAAQ,CAACG,MAAG,CAACA,GAAE,eAAaJ,GAAE,yBAAuB,QAAM,KAAK,WAAS,QAAM,KAAK,YAAU,KAAK,mBAAmB,GAAE,KAAK,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,SAAO,KAAK,QAAM,KAAK,UAAQ,KAAK,WAAS,KAAK,YAAU,KAAK,eAAa,MAAK,KAAK,eAAa,KAAK,WAAW,QAAQ,GAAE,KAAK,aAAW;AAAM,UAAMF,KAAEE,GAAE,YAAY,QAAQ,IAAI;AAAE,IAAAF,KAAE,MAAIE,GAAE,YAAY,OAAOF,IAAE,CAAC,GAAE,KAAK,gBAAgB,GAAE,KAAK,KAAK,SAAS;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAMA,KAAE,EAAE,EAAC,QAAO,KAAK,QAAO,QAAO,KAAK,QAAO,OAAM,KAAK,OAAM,SAAQ,KAAK,SAAQ,UAAS,KAAK,UAAS,KAAI,KAAK,KAAI,SAAQ,KAAK,SAAQ,cAAa,KAAK,cAAa,OAAM,KAAK,MAAK,CAAC,GAAEC,KAAE,KAAK;AAAU,WAAOA,MAAGA,GAAE,SAAO,MAAID,GAAE,YAAUC,GAAE,MAAM,IAAGD;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,iBAAa,KAAK,aAAa;AAAE,UAAMA,KAAE,MAAI,KAAK,oBAAmBC,KAAE,KAAG,KAAG;AAAE,QAAIC,MAAG,KAAK,WAAS,KAAK,eAAa,MAAI,KAAK,WAAS,KAAK,WAAS,KAAK,IAAI;AAAE,IAAAA,KAAE,IAAEA,KAAE,IAAEA,KAAED,OAAIC,KAAED,KAAG,KAAK,gBAAc,WAAW,KAAK,aAAa,KAAK,IAAI,GAAEC,KAAEF,KAAEE,KAAEF,KAAEE,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAEK,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,OAAM,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,sBAAqB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,GAAEA,EAAC;;;ACAvonC,IAAMU,KAAN,cAAgB,EAAC;AAAC;AAACA,GAAE,UAAU,gBAAc;AAAgC,IAAMC,KAAE,IAAID;AAAEE,GAAED,EAAC;", "names": ["a", "l", "p", "s", "e", "t", "n", "d", "c", "v", "h", "b", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "isInert", "node", "lookUp", "_node$getAttribute", "inertAtt", "getAttribute", "inert", "result", "parentNode", "isContentEditable", "_node$getAttribute2", "attValue", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scopeParent", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasTabIndex", "isNaN", "parseInt", "getTabIndex", "Error", "tabIndex", "test", "getSortOrderTabIndex", "isScope", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isNodeAttached", "_nodeRoot", "nodeRoot", "nodeRootHost", "host", "attached", "_nodeRootHost", "_nodeRootHost$ownerDo", "_node$ownerDocument", "contains", "_nodeRoot2", "_nodeRootHost2", "_nodeRootHost2$ownerD", "isZeroArea", "_node$getBoundingClie", "getBoundingClientRect", "width", "height", "isHidden", "_ref", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "container", "bind", "focusable", "isTabbable", "focusableCandidateSelector", "isFocusable", "activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "params", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "container", "tabbableNodes", "contains", "find", "getNodeForOption", "optionValue", "Error", "querySelector", "getInitialFocusNode", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "tabbableOptions", "focusableNodes", "focusable", "lastTabbableNode", "nextTabbableNode", "forward", "nodeIdx", "n", "slice", "isTabbable", "reverse", "filter", "group", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "isFocusable", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "checkKeyNav", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "concat", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean", "d", "p", "t", "e", "l", "s", "i", "r", "n", "a", "u", "h", "t", "e", "s", "r", "i", "p", "s", "l", "s", "l", "i", "e", "t", "r", "s", "D", "n", "i", "L", "a", "h", "u", "p", "l", "c", "d", "v", "o", "r", "s", "n"]}