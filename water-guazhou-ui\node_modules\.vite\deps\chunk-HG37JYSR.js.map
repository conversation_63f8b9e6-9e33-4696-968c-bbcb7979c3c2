{"version": 3, "sources": ["../../@arcgis/core/layers/support/I3SIndexInfo.js", "../../@arcgis/core/layers/support/schemaValidatorLoader.js", "../../@arcgis/core/webdoc/support/saveUtils.js", "../../@arcgis/core/layers/mixins/SceneService.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../request.js\";import o from\"../../core/Error.js\";import{isSome as r}from\"../../core/maybe.js\";async function n(n,t,s,a,i,d){let l=null;if(r(s)){const o=`${n}/nodepages/`,t=o+Math.floor(s.rootIndex/s.nodesPerPage);try{return{type:\"page\",rootPage:(await e(t,{query:{f:\"json\",token:a},responseType:\"json\",signal:d})).data,rootIndex:s.rootIndex,pageSize:s.nodesPerPage,lodMetric:s.lodSelectionMetricType,urlPrefix:o}}catch(f){r(i)&&i.warn(\"#fetchIndexInfo()\",\"Failed to load root node page. Falling back to node documents.\",t,f),l=f}}if(!t)return null;const p=`${n}/nodes/`,c=p+(t&&t.split(\"/\").pop());try{return{type:\"node\",rootNode:(await e(c,{query:{f:\"json\",token:a},responseType:\"json\",signal:d})).data,urlPrefix:p}}catch(f){throw new o(\"sceneservice:root-node-missing\",\"Root node missing.\",{pageError:l,nodeError:f,url:c})}}export{n as fetchIndexInfo};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nlet n=null;function t(t){n=t}function u(){return n}export{u as getLoader,t as registerLoader};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{result as r}from\"../../core/asyncUtils.js\";import e from\"../../core/Error.js\";import{isSome as o}from\"../../core/maybe.js\";import{eachAlways as t,throwIfAborted as s}from\"../../core/promiseUtils.js\";import{generateUUID as c}from\"../../core/uuid.js\";import{getSiblingOfSameTypeI as p}from\"../../portal/support/resourceUtils.js\";async function a(r,o,a){if(!o||!o.resources)return;const h=o.portalItem===r.portalItem?new Set(r.paths):new Set;r.paths.length=0,r.portalItem=o.portalItem;const i=new Set(o.resources.toKeep.map((r=>r.resource.path))),m=new Set,f=[];i.forEach((e=>{h.delete(e),r.paths.push(e)}));for(const e of o.resources.toUpdate)if(h.delete(e.resource.path),i.has(e.resource.path)||m.has(e.resource.path)){const{resource:o,content:t,finish:s,error:u}=e,h=p(o,c());r.paths.push(h.path),f.push(n({resource:h,content:t,compress:e.compress,finish:s,error:u},a))}else r.paths.push(e.resource.path),f.push(u(e,a)),m.add(e.resource.path);for(const e of o.resources.toAdd)f.push(n(e,a)),r.paths.push(e.resource.path);if(h.forEach((r=>{if(o.portalItem){const e=o.portalItem.resourceFromPath(r);f.push(e.portalItem.removeResource(e).catch((()=>{})))}})),0===f.length)return;const l=await t(f);s(a);const d=l.filter((r=>\"error\"in r)).map((r=>r.error));if(d.length>0)throw new e(\"save:resources\",\"Failed to save one or more resources\",{errors:d})}async function n(e,t){const s={...o(t)?t:{},compress:e.compress},c=await r(e.resource.portalItem.addResource(e.resource,e.content,s));if(!0!==c.ok)throw e.error?.(c.error),c.error;e.finish?.(e.resource)}async function u(e,o){const t=await r(e.resource.update(e.content,o));if(!0!==t.ok)throw e.error?.(t.error),t.error;e.finish?.(e.resource)}export{a as saveResources};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../request.js\";import r from\"../../core/Error.js\";import o from\"../../core/Logger.js\";import{isSome as i}from\"../../core/maybe.js\";import{debounce as s}from\"../../core/promiseUtils.js\";import{urlToObject as a}from\"../../core/urlUtils.js\";import{property as n}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as l}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as c}from\"../../core/accessorSupport/decorators/writer.js\";import{updateOrigins as d}from\"../../core/accessorSupport/originUtils.js\";import u from\"../../geometry/Extent.js\";import m from\"../../geometry/HeightModelInfo.js\";import y from\"../../geometry/SpatialReference.js\";import{titleFromUrlAndName as h,parse as f,cleanTitle as v,sanitizeUrlWithLayerId as S,writeUrlWithLayerId as g}from\"../support/arcgisLayerUrl.js\";import{id as w,url as I}from\"../support/commonProperties.js\";import{fetchIndexInfo as x}from\"../support/I3SIndexInfo.js\";import{getLoader as b}from\"../support/schemaValidatorLoader.js\";import _ from\"../../portal/Portal.js\";import j from\"../../portal/PortalItem.js\";import{saveResources as R}from\"../../webdoc/support/saveUtils.js\";const A=\"esri.layers.mixins.SceneService\",N=o.getLogger(A),E=o=>{let E=class extends o{constructor(){super(...arguments),this.spatialReference=null,this.fullExtent=null,this.heightModelInfo=null,this.minScale=0,this.maxScale=0,this.version={major:Number.NaN,minor:Number.NaN,versionString:\"\"},this.copyright=null,this.sublayerTitleMode=\"item-title\",this.title=null,this.layerId=null,this.indexInfo=null,this._debouncedSaveOperations=s((async(e,t,r)=>{switch(e){case L.SAVE:return this._save(t);case L.SAVE_AS:return this._saveAs(r,t)}}))}readSpatialReference(e,t){return this._readSpatialReference(t)}_readSpatialReference(e){if(null!=e.spatialReference)return y.fromJSON(e.spatialReference);{const t=e.store,r=t.indexCRS||t.geographicCRS,o=r&&parseInt(r.substring(r.lastIndexOf(\"/\")+1,r.length),10);return null!=o?new y(o):null}}readFullExtent(e,t,r){if(null!=e&&\"object\"==typeof e){const o=null==e.spatialReference?{...e,spatialReference:this._readSpatialReference(t)}:e;return u.fromJSON(o,r)}const o=t.store,i=this._readSpatialReference(t);return null==i||null==o||null==o.extent||!Array.isArray(o.extent)||o.extent.some((e=>e<U))?null:new u({xmin:o.extent[0],ymin:o.extent[1],xmax:o.extent[2],ymax:o.extent[3],spatialReference:i})}parseVersionString(e){const t={major:Number.NaN,minor:Number.NaN,versionString:e},r=e.split(\".\");return r.length>=2&&(t.major=parseInt(r[0],10),t.minor=parseInt(r[1],10)),t}readVersion(e,t){const r=t.store,o=null!=r.version?r.version.toString():\"\";return this.parseVersionString(o)}readTitlePortalItem(e){return\"item-title\"!==this.sublayerTitleMode?void 0:e}readTitleService(e,t){const r=this.portalItem&&this.portalItem.title;if(\"item-title\"===this.sublayerTitleMode)return h(this.url,t.name);let o=t.name;if(!o&&this.url){const e=f(this.url);i(e)&&(o=e.title)}return\"item-title-and-service-name\"===this.sublayerTitleMode&&r&&(o=r+\" - \"+o),v(o)}set url(e){const t=S({layer:this,url:e,nonStandardUrlAllowed:!1,logger:N});this._set(\"url\",t.url),null!=t.layerId&&this._set(\"layerId\",t.layerId)}writeUrl(e,t,r,o){g(this,e,\"layers\",t,o)}get parsedUrl(){const e=this._get(\"url\"),t=a(e);return null!=this.layerId&&(t.path=`${t.path}/layers/${this.layerId}`),t}async _fetchIndexAndUpdateExtent(e,t){this.indexInfo=x(this.parsedUrl.path,this.rootNode,e,this.apiKey,N,t),null==this.fullExtent||this.fullExtent.hasZ||this._updateExtent(await this.indexInfo)}_updateExtent(e){if(\"page\"===e?.type){const t=e.rootIndex%e.pageSize,o=e.rootPage?.nodes?.[t];if(null==o||null==o.obb||null==o.obb.center||null==o.obb.halfSize)throw new r(\"sceneservice:invalid-node-page\",\"Invalid node page.\");if(o.obb.center[0]<U||null==this.fullExtent||this.fullExtent.hasZ)return;const i=o.obb.halfSize,s=o.obb.center[2],a=Math.sqrt(i[0]*i[0]+i[1]*i[1]+i[2]*i[2]);this.fullExtent.zmin=s-a,this.fullExtent.zmax=s+a}else if(\"node\"===e?.type){const t=e.rootNode?.mbs;if(!Array.isArray(t)||4!==t.length||t[0]<U)return;const r=t[2],o=t[3],{fullExtent:i}=this;i&&(i.zmin=r-o,i.zmax=r+o)}}async _fetchService(e){if(null==this.url)throw new r(\"sceneservice:url-not-set\",\"Scene service can not be loaded without valid portal item or url\");if(null==this.layerId&&/SceneServer\\/*$/i.test(this.url)){const t=await this._fetchFirstLayerId(e);null!=t&&(this.layerId=t)}return this._fetchServiceLayer(e)}async _fetchFirstLayerId(e){const r=await t(this.url,{query:{f:\"json\",token:this.apiKey},responseType:\"json\",signal:e});if(r.data&&Array.isArray(r.data.layers)&&r.data.layers.length>0)return r.data.layers[0].id}async _fetchServiceLayer(e){const r=await t(this.parsedUrl?.path??\"\",{query:{f:\"json\",token:this.apiKey},responseType:\"json\",signal:e});r.ssl&&(this.url=this.url.replace(/^http:/i,\"https:\"));let o=!1;if(r.data.layerType&&\"Voxel\"===r.data.layerType&&(o=!0),o)return this._fetchVoxelServiceLayer();const i=r.data;this.read(i,this._getServiceContext()),this.validateLayer(i)}async _fetchVoxelServiceLayer(e){const r=(await t(this.parsedUrl?.path+\"/layer\",{query:{f:\"json\",token:this.apiKey},responseType:\"json\",signal:e})).data;this.read(r,this._getServiceContext()),this.validateLayer(r)}_getServiceContext(){return{origin:\"service\",portalItem:this.portalItem,portal:this.portalItem?.portal,url:this.parsedUrl}}async _ensureLoadBeforeSave(){await this.load(),\"beforeSave\"in this&&\"function\"==typeof this.beforeSave&&await this.beforeSave()}validateLayer(e){}_updateTypeKeywords(e,t,r){e.typeKeywords||(e.typeKeywords=[]);const o=t.getTypeKeywords();for(const i of o)e.typeKeywords.push(i);e.typeKeywords&&(e.typeKeywords=e.typeKeywords.filter(((e,t,r)=>r.indexOf(e)===t)),r===T.newItem&&(e.typeKeywords=e.typeKeywords.filter((e=>\"Hosted Service\"!==e))))}async _saveAs(e,t){const o={...K,...t};let i=j.from(e);i||(N.error(\"_saveAs(): requires a portal item parameter\"),await Promise.reject(new r(\"sceneservice:portal-item-required\",\"_saveAs() requires a portal item to save to\"))),i.id&&(i=i.clone(),i.id=null);const s=i.portal||_.getDefault();await this._ensureLoadBeforeSave(),i.type=O,i.portal=s;const a={origin:\"portal-item\",url:null,messages:[],portal:s,portalItem:i,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},n={layers:[this.write({},a)]};return await Promise.all(a.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(n,a,o),i.url=this.url,i.title||(i.title=this.title),this._updateTypeKeywords(i,o,T.newItem),await s.signIn(),await(s.user?.addItem({item:i,folder:o&&o.folder,data:n})),await R(this.resourceReferences,a,null),this.portalItem=i,d(a),a.portalItem=i,i}async _save(e){const t={...K,...e};if(!this.portalItem)throw N.error(\"_save(): requires the .portalItem property to be set\"),new r(\"sceneservice:portal-item-not-set\",\"Portal item to save to has not been set on this SceneService\");if(this.portalItem.type!==O)throw N.error(\"_save(): Non-matching portal item type. Got \"+this.portalItem.type+\", expected \"+O),new r(\"sceneservice:portal-item-wrong-type\",`Portal item needs to have type \"${O}\"`);await this._ensureLoadBeforeSave();const o={origin:\"portal-item\",url:this.portalItem.itemUrl&&a(this.portalItem.itemUrl),messages:[],portal:this.portalItem.portal||_.getDefault(),portalItem:this.portalItem,writtenProperties:[],blockedRelativeUrls:[],resources:{toAdd:[],toUpdate:[],toKeep:[],pendingOperations:[]}},i={layers:[this.write({},o)]};return await Promise.all(o.resources.pendingOperations??[]),await this._validateAgainstJSONSchema(i,o,t),this.portalItem.url=this.url,this.portalItem.title||(this.portalItem.title=this.title),this._updateTypeKeywords(this.portalItem,t,T.existingItem),await this.portalItem.update({data:i}),await R(this.resourceReferences,o,null),d(o),this.portalItem}async _validateAgainstJSONSchema(e,t,o){let i=t.messages?.filter((e=>\"error\"===e.type)).map((e=>new r(e.name,e.message,e.details)))??[];o?.validationOptions?.ignoreUnsupported&&(i=i.filter((e=>\"layer:unsupported\"!==e.name&&\"symbol:unsupported\"!==e.name&&\"symbol-layer:unsupported\"!==e.name&&\"property:unsupported\"!==e.name&&\"url:unsupported\"!==e.name&&\"scenemodification:unsupported\"!==e.name)));const s=o?.validationOptions,a=s?.enabled,n=b();if(a&&n){const t=(await n()).validate(e,o.portalItemLayerType);if(t.length>0){const e=`Layer item did not validate:\\n${t.join(\"\\n\")}`;if(N.error(`_validateAgainstJSONSchema(): ${e}`),\"throw\"===s.failPolicy){const e=t.map((e=>new r(\"sceneservice:schema-validation\",e))).concat(i);throw new r(\"sceneservice-validate:error\",\"Failed to save layer item due to schema validation, see `details.errors`.\",{combined:e})}}}if(i.length>0)throw new r(\"sceneservice:save\",\"Failed to save SceneService due to unsupported or invalid content. See 'details.errors' for more detailed information\",{errors:i})}};return e([n(w)],E.prototype,\"id\",void 0),e([n({type:y})],E.prototype,\"spatialReference\",void 0),e([l(\"spatialReference\",[\"spatialReference\",\"store.indexCRS\",\"store.geographicCRS\"])],E.prototype,\"readSpatialReference\",null),e([n({type:u})],E.prototype,\"fullExtent\",void 0),e([l(\"fullExtent\",[\"fullExtent\",\"store.extent\",\"spatialReference\",\"store.indexCRS\",\"store.geographicCRS\"])],E.prototype,\"readFullExtent\",null),e([n({readOnly:!0,type:m})],E.prototype,\"heightModelInfo\",void 0),e([n({type:Number,json:{name:\"layerDefinition.minScale\",write:!0,origins:{service:{read:{source:\"minScale\"},write:!1}}}})],E.prototype,\"minScale\",void 0),e([n({type:Number,json:{name:\"layerDefinition.maxScale\",write:!0,origins:{service:{read:{source:\"maxScale\"},write:!1}}}})],E.prototype,\"maxScale\",void 0),e([n({readOnly:!0})],E.prototype,\"version\",void 0),e([l(\"version\",[\"store.version\"])],E.prototype,\"readVersion\",null),e([n({type:String,json:{read:{source:\"copyrightText\"}}})],E.prototype,\"copyright\",void 0),e([n({type:String,json:{read:!1}})],E.prototype,\"sublayerTitleMode\",void 0),e([n({type:String})],E.prototype,\"title\",void 0),e([l(\"portal-item\",\"title\")],E.prototype,\"readTitlePortalItem\",null),e([l(\"service\",\"title\",[\"name\"])],E.prototype,\"readTitleService\",null),e([n({type:Number,json:{origins:{service:{read:{source:\"id\"}},\"portal-item\":{write:{target:\"id\",isRequired:!0,ignoreOrigin:!0},read:!1}}}})],E.prototype,\"layerId\",void 0),e([n(I)],E.prototype,\"url\",null),e([c(\"url\")],E.prototype,\"writeUrl\",null),e([n()],E.prototype,\"parsedUrl\",null),e([n({readOnly:!0})],E.prototype,\"store\",void 0),e([n({type:String,readOnly:!0,json:{read:{source:\"store.rootNode\"}}})],E.prototype,\"rootNode\",void 0),E=e([p(A)],E),E},U=-1e38;var T;!function(e){e[e.existingItem=0]=\"existingItem\",e[e.newItem=1]=\"newItem\"}(T||(T={}));const O=\"Scene Service\",K={getTypeKeywords:()=>[],portalItemLayerType:\"unknown\",validationOptions:{enabled:!0,ignoreUnsupported:!1,failPolicy:\"throw\"}};var L;!function(e){e[e.SAVE=0]=\"SAVE\",e[e.SAVE_AS=1]=\"SAVE_AS\"}(L||(L={}));export{O as SCENE_SERVICE_ITEM_TYPE,L as SaveOperationType,E as SceneService};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgH,eAAeA,GAAEA,IAAE,GAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAI,IAAE;AAAK,MAAG,EAAEH,EAAC,GAAE;AAAC,UAAMI,KAAE,GAAGL,EAAC,eAAcM,KAAED,KAAE,KAAK,MAAMJ,GAAE,YAAUA,GAAE,YAAY;AAAE,QAAG;AAAC,aAAM,EAAC,MAAK,QAAO,WAAU,MAAM,EAAEK,IAAE,EAAC,OAAM,EAAC,GAAE,QAAO,OAAMJ,GAAC,GAAE,cAAa,QAAO,QAAOE,GAAC,CAAC,GAAG,MAAK,WAAUH,GAAE,WAAU,UAASA,GAAE,cAAa,WAAUA,GAAE,wBAAuB,WAAUI,GAAC;AAAA,IAAC,SAAOE,IAAE;AAAC,QAAEJ,EAAC,KAAGA,GAAE,KAAK,qBAAoB,kEAAiEG,IAAEC,EAAC,GAAE,IAAEA;AAAA,IAAC;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,QAAM,IAAE,GAAGP,EAAC,WAAU,IAAE,KAAG,KAAG,EAAE,MAAM,GAAG,EAAE,IAAI;AAAG,MAAG;AAAC,WAAM,EAAC,MAAK,QAAO,WAAU,MAAM,EAAE,GAAE,EAAC,OAAM,EAAC,GAAE,QAAO,OAAME,GAAC,GAAE,cAAa,QAAO,QAAOE,GAAC,CAAC,GAAG,MAAK,WAAU,EAAC;AAAA,EAAC,SAAOG,IAAE;AAAC,UAAM,IAAIN,GAAE,kCAAiC,sBAAqB,EAAC,WAAU,GAAE,WAAUM,IAAE,KAAI,EAAC,CAAC;AAAA,EAAC;AAAC;;;ACA30B,IAAIC,KAAE;AAAuB,SAAS,IAAG;AAAC,SAAOC;AAAC;;;ACA4R,eAAeC,GAAEC,IAAEC,IAAEF,IAAE;AAAC,MAAG,CAACE,MAAG,CAACA,GAAE,UAAU;AAAO,QAAM,IAAEA,GAAE,eAAaD,GAAE,aAAW,IAAI,IAAIA,GAAE,KAAK,IAAE,oBAAI;AAAI,EAAAA,GAAE,MAAM,SAAO,GAAEA,GAAE,aAAWC,GAAE;AAAW,QAAMC,KAAE,IAAI,IAAID,GAAE,UAAU,OAAO,IAAK,CAAAD,OAAGA,GAAE,SAAS,IAAK,CAAC,GAAE,IAAE,oBAAI,OAAIG,KAAE,CAAC;AAAE,EAAAD,GAAE,QAAS,CAAAE,OAAG;AAAC,MAAE,OAAOA,EAAC,GAAEJ,GAAE,MAAM,KAAKI,EAAC;AAAA,EAAC,CAAE;AAAE,aAAUA,MAAKH,GAAE,UAAU,SAAS,KAAG,EAAE,OAAOG,GAAE,SAAS,IAAI,GAAEF,GAAE,IAAIE,GAAE,SAAS,IAAI,KAAG,EAAE,IAAIA,GAAE,SAAS,IAAI,GAAE;AAAC,UAAK,EAAC,UAASH,IAAE,SAAQ,GAAE,QAAOI,IAAE,OAAMC,GAAC,IAAEF,IAAEG,KAAEC,GAAEP,IAAE,EAAE,CAAC;AAAE,IAAAD,GAAE,MAAM,KAAKO,GAAE,IAAI,GAAEJ,GAAE,KAAKM,GAAE,EAAC,UAASF,IAAE,SAAQ,GAAE,UAASH,GAAE,UAAS,QAAOC,IAAE,OAAMC,GAAC,GAAEP,EAAC,CAAC;AAAA,EAAC,MAAM,CAAAC,GAAE,MAAM,KAAKI,GAAE,SAAS,IAAI,GAAED,GAAE,KAAKG,GAAEF,IAAEL,EAAC,CAAC,GAAE,EAAE,IAAIK,GAAE,SAAS,IAAI;AAAE,aAAUA,MAAKH,GAAE,UAAU,MAAM,CAAAE,GAAE,KAAKM,GAAEL,IAAEL,EAAC,CAAC,GAAEC,GAAE,MAAM,KAAKI,GAAE,SAAS,IAAI;AAAE,MAAG,EAAE,QAAS,CAAAJ,OAAG;AAAC,QAAGC,GAAE,YAAW;AAAC,YAAMG,KAAEH,GAAE,WAAW,iBAAiBD,EAAC;AAAE,MAAAG,GAAE,KAAKC,GAAE,WAAW,eAAeA,EAAC,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE,CAAC;AAAA,IAAC;AAAA,EAAC,CAAE,GAAE,MAAID,GAAE,OAAO;AAAO,QAAM,IAAE,MAAM,EAAEA,EAAC;AAAE,IAAEJ,EAAC;AAAE,QAAMW,KAAE,EAAE,OAAQ,CAAAV,OAAG,WAAUA,EAAE,EAAE,IAAK,CAAAA,OAAGA,GAAE,KAAM;AAAE,MAAGU,GAAE,SAAO,EAAE,OAAM,IAAIL,GAAE,kBAAiB,wCAAuC,EAAC,QAAOK,GAAC,CAAC;AAAC;AAAC,eAAeD,GAAEL,IAAE,GAAE;AAJ/1C;AAIg2C,QAAMC,KAAE,EAAC,GAAG,EAAE,CAAC,IAAE,IAAE,CAAC,GAAE,UAASD,GAAE,SAAQ,GAAE,IAAE,MAAM,EAAEA,GAAE,SAAS,WAAW,YAAYA,GAAE,UAASA,GAAE,SAAQC,EAAC,CAAC;AAAE,MAAG,SAAK,EAAE,GAAG,QAAM,KAAAD,GAAE,UAAF,wBAAAA,IAAU,EAAE,QAAO,EAAE;AAAM,QAAAA,GAAE,WAAF,wBAAAA,IAAWA,GAAE;AAAS;AAAC,eAAeE,GAAEF,IAAEH,IAAE;AAJ1iD;AAI2iD,QAAM,IAAE,MAAM,EAAEG,GAAE,SAAS,OAAOA,GAAE,SAAQH,EAAC,CAAC;AAAE,MAAG,SAAK,EAAE,GAAG,QAAM,KAAAG,GAAE,UAAF,wBAAAA,IAAU,EAAE,QAAO,EAAE;AAAM,QAAAA,GAAE,WAAF,wBAAAA,IAAWA,GAAE;AAAS;;;ACAjU,IAAM,IAAE;AAAR,IAA0C,IAAE,EAAE,UAAU,CAAC;AAAzD,IAA2DO,KAAE,CAAAC,OAAG;AAAC,MAAID,KAAE,cAAcC,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB,MAAK,KAAK,aAAW,MAAK,KAAK,kBAAgB,MAAK,KAAK,WAAS,GAAE,KAAK,WAAS,GAAE,KAAK,UAAQ,EAAC,OAAM,OAAO,KAAI,OAAM,OAAO,KAAI,eAAc,GAAE,GAAE,KAAK,YAAU,MAAK,KAAK,oBAAkB,cAAa,KAAK,QAAM,MAAK,KAAK,UAAQ,MAAK,KAAK,YAAU,MAAK,KAAK,2BAAyB,EAAG,OAAMC,IAAE,GAAEC,OAAI;AAAC,gBAAOD,IAAE;AAAA,UAAC,KAAKE,GAAE;AAAK,mBAAO,KAAK,MAAM,CAAC;AAAA,UAAE,KAAKA,GAAE;AAAQ,mBAAO,KAAK,QAAQD,IAAE,CAAC;AAAA,QAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,IAAC,qBAAqBD,IAAE,GAAE;AAAC,aAAO,KAAK,sBAAsB,CAAC;AAAA,IAAC;AAAA,IAAC,sBAAsBA,IAAE;AAAC,UAAG,QAAMA,GAAE,iBAAiB,QAAOG,GAAE,SAASH,GAAE,gBAAgB;AAAE;AAAC,cAAM,IAAEA,GAAE,OAAMC,KAAE,EAAE,YAAU,EAAE,eAAcF,KAAEE,MAAG,SAASA,GAAE,UAAUA,GAAE,YAAY,GAAG,IAAE,GAAEA,GAAE,MAAM,GAAE,EAAE;AAAE,eAAO,QAAMF,KAAE,IAAII,GAAEJ,EAAC,IAAE;AAAA,MAAI;AAAA,IAAC;AAAA,IAAC,eAAeC,IAAE,GAAEC,IAAE;AAAC,UAAG,QAAMD,MAAG,YAAU,OAAOA,IAAE;AAAC,cAAMD,KAAE,QAAMC,GAAE,mBAAiB,EAAC,GAAGA,IAAE,kBAAiB,KAAK,sBAAsB,CAAC,EAAC,IAAEA;AAAE,eAAO,EAAE,SAASD,IAAEE,EAAC;AAAA,MAAC;AAAC,YAAMF,KAAE,EAAE,OAAMK,KAAE,KAAK,sBAAsB,CAAC;AAAE,aAAO,QAAMA,MAAG,QAAML,MAAG,QAAMA,GAAE,UAAQ,CAAC,MAAM,QAAQA,GAAE,MAAM,KAAGA,GAAE,OAAO,KAAM,CAAAC,OAAGA,KAAEK,EAAE,IAAE,OAAK,IAAI,EAAE,EAAC,MAAKN,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,kBAAiBK,GAAC,CAAC;AAAA,IAAC;AAAA,IAAC,mBAAmBJ,IAAE;AAAC,YAAM,IAAE,EAAC,OAAM,OAAO,KAAI,OAAM,OAAO,KAAI,eAAcA,GAAC,GAAEC,KAAED,GAAE,MAAM,GAAG;AAAE,aAAOC,GAAE,UAAQ,MAAI,EAAE,QAAM,SAASA,GAAE,CAAC,GAAE,EAAE,GAAE,EAAE,QAAM,SAASA,GAAE,CAAC,GAAE,EAAE,IAAG;AAAA,IAAC;AAAA,IAAC,YAAYD,IAAE,GAAE;AAAC,YAAMC,KAAE,EAAE,OAAMF,KAAE,QAAME,GAAE,UAAQA,GAAE,QAAQ,SAAS,IAAE;AAAG,aAAO,KAAK,mBAAmBF,EAAC;AAAA,IAAC;AAAA,IAAC,oBAAoBC,IAAE;AAAC,aAAM,iBAAe,KAAK,oBAAkB,SAAOA;AAAA,IAAC;AAAA,IAAC,iBAAiBA,IAAE,GAAE;AAAC,YAAMC,KAAE,KAAK,cAAY,KAAK,WAAW;AAAM,UAAG,iBAAe,KAAK,kBAAkB,QAAOK,GAAE,KAAK,KAAI,EAAE,IAAI;AAAE,UAAIP,KAAE,EAAE;AAAK,UAAG,CAACA,MAAG,KAAK,KAAI;AAAC,cAAMC,KAAE,EAAE,KAAK,GAAG;AAAE,UAAEA,EAAC,MAAID,KAAEC,GAAE;AAAA,MAAM;AAAC,aAAM,kCAAgC,KAAK,qBAAmBC,OAAIF,KAAEE,KAAE,QAAMF,KAAGQ,GAAER,EAAC;AAAA,IAAC;AAAA,IAAC,IAAI,IAAIC,IAAE;AAAC,YAAM,IAAEQ,GAAE,EAAC,OAAM,MAAK,KAAIR,IAAE,uBAAsB,OAAG,QAAO,EAAC,CAAC;AAAE,WAAK,KAAK,OAAM,EAAE,GAAG,GAAE,QAAM,EAAE,WAAS,KAAK,KAAK,WAAU,EAAE,OAAO;AAAA,IAAC;AAAA,IAAC,SAASA,IAAE,GAAEC,IAAEF,IAAE;AAAC,QAAE,MAAKC,IAAE,UAAS,GAAED,EAAC;AAAA,IAAC;AAAA,IAAC,IAAI,YAAW;AAAC,YAAMC,KAAE,KAAK,KAAK,KAAK,GAAE,IAAE,EAAEA,EAAC;AAAE,aAAO,QAAM,KAAK,YAAU,EAAE,OAAK,GAAG,EAAE,IAAI,WAAW,KAAK,OAAO,KAAI;AAAA,IAAC;AAAA,IAAC,MAAM,2BAA2BA,IAAE,GAAE;AAAC,WAAK,YAAUS,GAAE,KAAK,UAAU,MAAK,KAAK,UAAST,IAAE,KAAK,QAAO,GAAE,CAAC,GAAE,QAAM,KAAK,cAAY,KAAK,WAAW,QAAM,KAAK,cAAc,MAAM,KAAK,SAAS;AAAA,IAAC;AAAA,IAAC,cAAcA,IAAE;AAJ9rH;AAI+rH,UAAG,YAASA,MAAA,gBAAAA,GAAG,OAAK;AAAC,cAAM,IAAEA,GAAE,YAAUA,GAAE,UAASD,MAAE,WAAAC,GAAE,aAAF,mBAAY,UAAZ,mBAAoB;AAAG,YAAG,QAAMD,MAAG,QAAMA,GAAE,OAAK,QAAMA,GAAE,IAAI,UAAQ,QAAMA,GAAE,IAAI,SAAS,OAAM,IAAIW,GAAE,kCAAiC,oBAAoB;AAAE,YAAGX,GAAE,IAAI,OAAO,CAAC,IAAEM,MAAG,QAAM,KAAK,cAAY,KAAK,WAAW,KAAK;AAAO,cAAMD,KAAEL,GAAE,IAAI,UAASW,KAAEX,GAAE,IAAI,OAAO,CAAC,GAAEY,KAAE,KAAK,KAAKP,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAC;AAAE,aAAK,WAAW,OAAKM,KAAEC,IAAE,KAAK,WAAW,OAAKD,KAAEC;AAAA,MAAC,WAAS,YAASX,MAAA,gBAAAA,GAAG,OAAK;AAAC,cAAM,KAAE,KAAAA,GAAE,aAAF,mBAAY;AAAI,YAAG,CAAC,MAAM,QAAQ,CAAC,KAAG,MAAI,EAAE,UAAQ,EAAE,CAAC,IAAEK,GAAE;AAAO,cAAMJ,KAAE,EAAE,CAAC,GAAEF,KAAE,EAAE,CAAC,GAAE,EAAC,YAAWK,GAAC,IAAE;AAAK,QAAAA,OAAIA,GAAE,OAAKH,KAAEF,IAAEK,GAAE,OAAKH,KAAEF;AAAA,MAAE;AAAA,IAAC;AAAA,IAAC,MAAM,cAAcC,IAAE;AAAC,UAAG,QAAM,KAAK,IAAI,OAAM,IAAIU,GAAE,4BAA2B,kEAAkE;AAAE,UAAG,QAAM,KAAK,WAAS,mBAAmB,KAAK,KAAK,GAAG,GAAE;AAAC,cAAM,IAAE,MAAM,KAAK,mBAAmBV,EAAC;AAAE,gBAAM,MAAI,KAAK,UAAQ;AAAA,MAAE;AAAC,aAAO,KAAK,mBAAmBA,EAAC;AAAA,IAAC;AAAA,IAAC,MAAM,mBAAmBA,IAAE;AAAC,YAAMC,KAAE,MAAM,EAAE,KAAK,KAAI,EAAC,OAAM,EAAC,GAAE,QAAO,OAAM,KAAK,OAAM,GAAE,cAAa,QAAO,QAAOD,GAAC,CAAC;AAAE,UAAGC,GAAE,QAAM,MAAM,QAAQA,GAAE,KAAK,MAAM,KAAGA,GAAE,KAAK,OAAO,SAAO,EAAE,QAAOA,GAAE,KAAK,OAAO,CAAC,EAAE;AAAA,IAAE;AAAA,IAAC,MAAM,mBAAmBD,IAAE;AAJzyJ;AAI0yJ,YAAMC,KAAE,MAAM,IAAE,UAAK,cAAL,mBAAgB,SAAM,IAAG,EAAC,OAAM,EAAC,GAAE,QAAO,OAAM,KAAK,OAAM,GAAE,cAAa,QAAO,QAAOD,GAAC,CAAC;AAAE,MAAAC,GAAE,QAAM,KAAK,MAAI,KAAK,IAAI,QAAQ,WAAU,QAAQ;AAAG,UAAIF,KAAE;AAAG,UAAGE,GAAE,KAAK,aAAW,YAAUA,GAAE,KAAK,cAAYF,KAAE,OAAIA,GAAE,QAAO,KAAK,wBAAwB;AAAE,YAAMK,KAAEH,GAAE;AAAK,WAAK,KAAKG,IAAE,KAAK,mBAAmB,CAAC,GAAE,KAAK,cAAcA,EAAC;AAAA,IAAC;AAAA,IAAC,MAAM,wBAAwBJ,IAAE;AAJlqK;AAImqK,YAAMC,MAAG,MAAM,IAAE,UAAK,cAAL,mBAAgB,QAAK,UAAS,EAAC,OAAM,EAAC,GAAE,QAAO,OAAM,KAAK,OAAM,GAAE,cAAa,QAAO,QAAOD,GAAC,CAAC,GAAG;AAAK,WAAK,KAAKC,IAAE,KAAK,mBAAmB,CAAC,GAAE,KAAK,cAAcA,EAAC;AAAA,IAAC;AAAA,IAAC,qBAAoB;AAJ52K;AAI62K,aAAM,EAAC,QAAO,WAAU,YAAW,KAAK,YAAW,SAAO,UAAK,eAAL,mBAAiB,QAAO,KAAI,KAAK,UAAS;AAAA,IAAC;AAAA,IAAC,MAAM,wBAAuB;AAAC,YAAM,KAAK,KAAK,GAAE,gBAAe,QAAM,cAAY,OAAO,KAAK,cAAY,MAAM,KAAK,WAAW;AAAA,IAAC;AAAA,IAAC,cAAcD,IAAE;AAAA,IAAC;AAAA,IAAC,oBAAoBA,IAAE,GAAEC,IAAE;AAAC,MAAAD,GAAE,iBAAeA,GAAE,eAAa,CAAC;AAAG,YAAMD,KAAE,EAAE,gBAAgB;AAAE,iBAAUK,MAAKL,GAAE,CAAAC,GAAE,aAAa,KAAKI,EAAC;AAAE,MAAAJ,GAAE,iBAAeA,GAAE,eAAaA,GAAE,aAAa,OAAQ,CAACA,IAAEY,IAAEX,OAAIA,GAAE,QAAQD,EAAC,MAAIY,EAAE,GAAEX,OAAI,EAAE,YAAUD,GAAE,eAAaA,GAAE,aAAa,OAAQ,CAAAA,OAAG,qBAAmBA,EAAE;AAAA,IAAG;AAAA,IAAC,MAAM,QAAQA,IAAE,GAAE;AAJh6L;AAIi6L,YAAMD,KAAE,EAAC,GAAG,GAAE,GAAG,EAAC;AAAE,UAAIK,KAAES,GAAE,KAAKb,EAAC;AAAE,MAAAI,OAAI,EAAE,MAAM,6CAA6C,GAAE,MAAM,QAAQ,OAAO,IAAIM,GAAE,qCAAoC,6CAA6C,CAAC,IAAGN,GAAE,OAAKA,KAAEA,GAAE,MAAM,GAAEA,GAAE,KAAG;AAAM,YAAMM,KAAEN,GAAE,UAAQI,GAAE,WAAW;AAAE,YAAM,KAAK,sBAAsB,GAAEJ,GAAE,OAAK,GAAEA,GAAE,SAAOM;AAAE,YAAMC,KAAE,EAAC,QAAO,eAAc,KAAI,MAAK,UAAS,CAAC,GAAE,QAAOD,IAAE,YAAWN,IAAE,mBAAkB,CAAC,GAAE,qBAAoB,CAAC,GAAE,WAAU,EAAC,OAAM,CAAC,GAAE,UAAS,CAAC,GAAE,QAAO,CAAC,GAAE,mBAAkB,CAAC,EAAC,EAAC,GAAEK,KAAE,EAAC,QAAO,CAAC,KAAK,MAAM,CAAC,GAAEE,EAAC,CAAC,EAAC;AAAE,aAAO,MAAM,QAAQ,IAAIA,GAAE,UAAU,qBAAmB,CAAC,CAAC,GAAE,MAAM,KAAK,2BAA2BF,IAAEE,IAAEZ,EAAC,GAAEK,GAAE,MAAI,KAAK,KAAIA,GAAE,UAAQA,GAAE,QAAM,KAAK,QAAO,KAAK,oBAAoBA,IAAEL,IAAE,EAAE,OAAO,GAAE,MAAMW,GAAE,OAAO,GAAE,QAAM,KAAAA,GAAE,SAAF,mBAAQ,QAAQ,EAAC,MAAKN,IAAE,QAAOL,MAAGA,GAAE,QAAO,MAAKU,GAAC,KAAI,MAAME,GAAE,KAAK,oBAAmBA,IAAE,IAAI,GAAE,KAAK,aAAWP,IAAE,EAAEO,EAAC,GAAEA,GAAE,aAAWP,IAAEA;AAAA,IAAC;AAAA,IAAC,MAAM,MAAMJ,IAAE;AAAC,YAAM,IAAE,EAAC,GAAG,GAAE,GAAGA,GAAC;AAAE,UAAG,CAAC,KAAK,WAAW,OAAM,EAAE,MAAM,sDAAsD,GAAE,IAAIU,GAAE,oCAAmC,8DAA8D;AAAE,UAAG,KAAK,WAAW,SAAO,EAAE,OAAM,EAAE,MAAM,iDAA+C,KAAK,WAAW,OAAK,gBAAc,CAAC,GAAE,IAAIA,GAAE,uCAAsC,mCAAmC,CAAC,GAAG;AAAE,YAAM,KAAK,sBAAsB;AAAE,YAAMX,KAAE,EAAC,QAAO,eAAc,KAAI,KAAK,WAAW,WAAS,EAAE,KAAK,WAAW,OAAO,GAAE,UAAS,CAAC,GAAE,QAAO,KAAK,WAAW,UAAQS,GAAE,WAAW,GAAE,YAAW,KAAK,YAAW,mBAAkB,CAAC,GAAE,qBAAoB,CAAC,GAAE,WAAU,EAAC,OAAM,CAAC,GAAE,UAAS,CAAC,GAAE,QAAO,CAAC,GAAE,mBAAkB,CAAC,EAAC,EAAC,GAAEJ,KAAE,EAAC,QAAO,CAAC,KAAK,MAAM,CAAC,GAAEL,EAAC,CAAC,EAAC;AAAE,aAAO,MAAM,QAAQ,IAAIA,GAAE,UAAU,qBAAmB,CAAC,CAAC,GAAE,MAAM,KAAK,2BAA2BK,IAAEL,IAAE,CAAC,GAAE,KAAK,WAAW,MAAI,KAAK,KAAI,KAAK,WAAW,UAAQ,KAAK,WAAW,QAAM,KAAK,QAAO,KAAK,oBAAoB,KAAK,YAAW,GAAE,EAAE,YAAY,GAAE,MAAM,KAAK,WAAW,OAAO,EAAC,MAAKK,GAAC,CAAC,GAAE,MAAMO,GAAE,KAAK,oBAAmBZ,IAAE,IAAI,GAAE,EAAEA,EAAC,GAAE,KAAK;AAAA,IAAU;AAAA,IAAC,MAAM,2BAA2BC,IAAE,GAAED,IAAE;AAJ76P;AAI86P,UAAIK,OAAE,OAAE,aAAF,mBAAY,OAAQ,CAAAJ,OAAG,YAAUA,GAAE,MAAO,IAAK,CAAAA,OAAG,IAAIU,GAAEV,GAAE,MAAKA,GAAE,SAAQA,GAAE,OAAO,OAAK,CAAC;AAAE,aAAAD,MAAA,gBAAAA,GAAG,sBAAH,mBAAsB,uBAAoBK,KAAEA,GAAE,OAAQ,CAAAJ,OAAG,wBAAsBA,GAAE,QAAM,yBAAuBA,GAAE,QAAM,+BAA6BA,GAAE,QAAM,2BAAyBA,GAAE,QAAM,sBAAoBA,GAAE,QAAM,oCAAkCA,GAAE,IAAK;AAAG,YAAMU,KAAEX,MAAA,gBAAAA,GAAG,mBAAkBY,KAAED,MAAA,gBAAAA,GAAG,SAAQD,KAAE,EAAE;AAAE,UAAGE,MAAGF,IAAE;AAAC,cAAMG,MAAG,MAAMH,GAAE,GAAG,SAAST,IAAED,GAAE,mBAAmB;AAAE,YAAGa,GAAE,SAAO,GAAE;AAAC,gBAAMZ,KAAE;AAAA,EAAiCY,GAAE,KAAK,IAAI,CAAC;AAAG,cAAG,EAAE,MAAM,iCAAiCZ,EAAC,EAAE,GAAE,YAAUU,GAAE,YAAW;AAAC,kBAAMV,KAAEY,GAAE,IAAK,CAAAZ,OAAG,IAAIU,GAAE,kCAAiCV,EAAC,CAAE,EAAE,OAAOI,EAAC;AAAE,kBAAM,IAAIM,GAAE,+BAA8B,6EAA4E,EAAC,UAASV,GAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGI,GAAE,SAAO,EAAE,OAAM,IAAIM,GAAE,qBAAoB,yHAAwH,EAAC,QAAON,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEN,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,GAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,oBAAmB,CAAC,oBAAmB,kBAAiB,qBAAqB,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,cAAa,CAAC,cAAa,gBAAe,oBAAmB,kBAAiB,qBAAqB,CAAC,CAAC,GAAEA,GAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAKgB,GAAC,CAAC,CAAC,GAAEhB,GAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,4BAA2B,OAAM,MAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,WAAU,GAAE,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,4BAA2B,OAAM,MAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,WAAU,GAAE,OAAM,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,WAAU,CAAC,eAAe,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,eAAc,OAAO,CAAC,GAAEA,GAAE,WAAU,uBAAsB,IAAI,GAAE,EAAE,CAAC,EAAE,WAAU,SAAQ,CAAC,MAAM,CAAC,CAAC,GAAEA,GAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,KAAI,EAAC,GAAE,eAAc,EAAC,OAAM,EAAC,QAAO,MAAK,YAAW,MAAG,cAAa,KAAE,GAAE,MAAK,MAAE,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAEK,EAAC,CAAC,GAAEL,GAAE,WAAU,OAAM,IAAI,GAAE,EAAE,CAACG,GAAE,KAAK,CAAC,GAAEH,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAEA,GAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,MAAG,MAAK,EAAC,MAAK,EAAC,QAAO,iBAAgB,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,EAAC,GAAEA;AAAC;AAAruS,IAAuuSO,KAAE;AAAM,IAAI;AAAE,CAAC,SAASL,IAAE;AAAC,EAAAA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,UAAQ,CAAC,IAAE;AAAS,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAM,IAAE;AAAR,IAAwB,IAAE,EAAC,iBAAgB,MAAI,CAAC,GAAE,qBAAoB,WAAU,mBAAkB,EAAC,SAAQ,MAAG,mBAAkB,OAAG,YAAW,QAAO,EAAC;AAAE,IAAIE;AAAE,CAAC,SAASF,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,UAAQ,CAAC,IAAE;AAAS,EAAEE,OAAIA,KAAE,CAAC,EAAE;", "names": ["n", "s", "a", "i", "d", "o", "t", "f", "n", "n", "a", "r", "o", "i", "f", "e", "s", "u", "h", "w", "n", "d", "E", "o", "e", "r", "L", "f", "i", "U", "y", "w", "b", "n", "s", "a", "t", "x", "v"]}