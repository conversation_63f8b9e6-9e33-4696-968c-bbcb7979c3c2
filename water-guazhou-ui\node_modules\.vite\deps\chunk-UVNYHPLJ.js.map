{"version": 3, "sources": ["../../@arcgis/core/core/accessorSupport/tracking/SimpleObservable.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ObservableBase as o}from\"../ObservableBase.js\";class s extends o{notify(){const o=this._observers;if(o&&o.length>0){const s=o.slice();for(const o of s)o.onInvalidated(),o.onCommitted()}}}export{s as SimpleObservable};\n"], "mappings": ";;;;;AAIsD,IAAMA,KAAN,cAAgB,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAM,IAAE,KAAK;AAAW,QAAG,KAAG,EAAE,SAAO,GAAE;AAAC,YAAMA,KAAE,EAAE,MAAM;AAAE,iBAAUC,MAAKD,GAAE,CAAAC,GAAE,cAAc,GAAEA,GAAE,YAAY;AAAA,IAAC;AAAA,EAAC;AAAC;", "names": ["s", "o"]}