import {
  b
} from "./chunk-P37TUI4J.js";
import {
  t2 as t
} from "./chunk-JN4FSB7Y.js";
import {
  f
} from "./chunk-EKX3LLYN.js";

// node_modules/@arcgis/core/renderers/support/styleUtils.js
async function t2(t3, i, n) {
  const s = t3 && t3.getAtOrigin && t3.getAtOrigin("renderer", i.origin);
  if (s && "unique-value" === s.type && s.styleOrigin) {
    const a = await b(s.populateFromStyle());
    if (f(n), false === a.ok) {
      const e = a.error;
      i && i.messages && i.messages.push(new t("renderer:style-reference", `Failed to create unique value renderer from style reference: ${e.message}`, { error: e, context: i })), t3.clear("renderer", i == null ? void 0 : i.origin);
    }
  }
}

export {
  t2 as t
};
//# sourceMappingURL=chunk-NNKS4NNY.js.map
