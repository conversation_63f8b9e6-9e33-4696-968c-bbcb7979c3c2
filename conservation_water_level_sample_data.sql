-- 涵养水位测试数据
-- 根据实际表结构 tb_conservation_water_level 和 tb_conservation_analysis 生成
-- 注意：tenantId 应该使用实际的租户UUID，这里使用示例UUID
-- 在实际使用时，请替换为正确的租户ID

-- 清空现有数据（可选）
-- TRUNCATE TABLE tb_conservation_water_level;
-- TRUNCATE TABLE tb_conservation_analysis;

-- 插入涵养水位数据
-- 注意：请将 '13814000-1dd2-11b2-8080-************' 替换为实际的租户ID
INSERT INTO tb_conservation_water_level (
    id, tenant_id, station_id,
    raw_water_level, groundwater_level, level_change,
    rainfall_amount, evaporation_amount, surface_runoff, extraction_amount,
    soil_moisture, permeability_coefficient, record_time,
    create_time, update_time, remark, data_source, creator
) VALUES
-- 测点1数据 (近30天) - station_001
('cwl_001', '13814000-1dd2-11b2-8080-************', 'station_001',
 12.450, 8.320, -0.150, 15.2, 8.5, 1200.0, 450.0,
 32.5, 0.0025, '2024-01-01 08:00:00',
 '2024-01-01 08:00:00', '2024-01-01 08:00:00', '正常监测', 1, 'admin'),

('cwl_002', '13814000-1dd2-11b2-8080-************', 'station_001',
 12.380, 8.280, -0.070, 12.8, 9.2, 1150.0, 480.0,
 31.8, 0.0025, '2024-01-02 08:00:00',
 '2024-01-02 08:00:00', '2024-01-02 08:00:00', '正常监测', 1, 'admin'),

('cwl_003', '13814000-1dd2-11b2-8080-************', 'station_001',
 12.420, 8.310, 0.040, 18.5, 7.8, 1300.0, 420.0,
 33.2, 0.0025, '2024-01-03 08:00:00',
 '2024-01-03 08:00:00', '2024-01-03 08:00:00', '降雨后水位回升', 1, 'admin'),

('cwl_004', '13814000-1dd2-11b2-8080-************', 'station_001',
 12.350, 8.250, -0.070, 8.2, 11.5, 1100.0, 520.0,
 30.5, 0.0025, '2024-01-04 08:00:00',
 '2024-01-04 08:00:00', '2024-01-04 08:00:00', '蒸发量较大', 1, 'admin'),

('cwl_005', '13814000-1dd2-11b2-8080-************', 'station_001',
 12.280, 8.180, -0.100, 5.5, 12.8, 980.0, 580.0,
 29.8, 0.0025, '2024-01-05 08:00:00',
 '2024-01-05 08:00:00', '2024-01-05 08:00:00', '开采量增加', 1, 'admin'),

-- 测点2数据 - station_002
('cwl_006', 'default', 'station_002',
 15.680, 11.420, 0.080, 22.3, 6.8, 1800.0, 350.0,
 38.5, 0.0032, '2024-01-01 08:00:00',
 '2024-01-01 08:00:00', '2024-01-01 08:00:00', '水位稳定', 1, 'admin'),

('cwl_007', 'default', 'station_002',
 15.720, 11.460, 0.040, 19.8, 7.5, 1750.0, 380.0,
 37.8, 0.0032, '2024-01-02 08:00:00',
 '2024-01-02 08:00:00', '2024-01-02 08:00:00', '水位上升', 1, 'admin'),

('cwl_008', 'default', 'station_002',
 15.650, 11.380, -0.070, 14.2, 9.8, 1650.0, 420.0,
 36.5, 0.0032, '2024-01-03 08:00:00',
 '2024-01-03 08:00:00', '2024-01-03 08:00:00', '轻微下降', 1, 'admin'),

-- 测点3数据 - station_003
('cwl_009', 'default', 'station_003',
 9.850, 6.520, -0.180, 8.5, 15.2, 850.0, 680.0,
 25.8, 0.0018, '2024-01-01 08:00:00',
 '2024-01-01 08:00:00', '2024-01-01 08:00:00', '水位偏低', 1, 'admin'),

('cwl_010', 'default', 'station_003',
 9.780, 6.450, -0.070, 6.2, 16.8, 800.0, 720.0,
 24.5, 0.0018, '2024-01-02 08:00:00',
 '2024-01-02 08:00:00', '2024-01-02 08:00:00', '持续下降', 1, 'admin'),

-- 更多测点1的历史数据
('cwl_011', 'default', 'station_001',
 12.320, 8.220, 0.040, 25.8, 5.2, 1450.0, 380.0,
 34.8, 0.0025, '2024-01-06 08:00:00',
 '2024-01-06 08:00:00', '2024-01-06 08:00:00', '大雨后回升', 1, 'admin'),

('cwl_012', 'default', 'station_001',
 12.480, 8.380, 0.160, 32.5, 4.8, 1650.0, 320.0,
 36.2, 0.0025, '2024-01-07 08:00:00',
 '2024-01-07 08:00:00', '2024-01-07 08:00:00', '强降雨补给', 1, 'admin'),

('cwl_013', 'default', 'station_001',
 12.520, 8.420, 0.040, 28.2, 6.5, 1580.0, 350.0,
 35.5, 0.0025, '2024-01-08 08:00:00',
 '2024-01-08 08:00:00', '2024-01-08 08:00:00', '水位稳定上升', 1, 'admin'),

('cwl_014', 'default', 'station_001',
 12.450, 8.350, -0.070, 15.8, 10.2, 1380.0, 450.0,
 33.8, 0.0025, '2024-01-09 08:00:00',
 '2024-01-09 08:00:00', '2024-01-09 08:00:00', '正常波动', 1, 'admin'),

('cwl_015', 'default', 'station_001',
 12.380, 8.280, -0.070, 12.5, 11.8, 1250.0, 480.0,
 32.2, 0.0025, '2024-01-10 08:00:00',
 '2024-01-10 08:00:00', '2024-01-10 08:00:00', '轻微下降', 1, 'admin'),

-- 设备采集数据示例
('cwl_016', 'default', 'station_002',
 15.780, 11.520, 0.060, 20.5, 7.2, 1820.0, 340.0,
 39.2, 0.0032, '2024-01-04 08:00:00',
 '2024-01-04 08:00:00', '2024-01-04 08:00:00', '自动监测', 2, 'system'),

('cwl_017', 'default', 'station_002',
 15.820, 11.580, 0.060, 18.8, 8.5, 1780.0, 360.0,
 38.8, 0.0032, '2024-01-05 08:00:00',
 '2024-01-05 08:00:00', '2024-01-05 08:00:00', '自动监测', 2, 'system'),

('cwl_018', 'default', 'station_003',
 9.720, 6.380, -0.070, 4.8, 18.5, 750.0, 750.0,
 23.2, 0.0018, '2024-01-03 08:00:00',
 '2024-01-03 08:00:00', '2024-01-03 08:00:00', '水位告警', 1, 'admin'),

('cwl_019', 'default', 'station_003',
 9.680, 6.320, -0.040, 3.2, 19.8, 720.0, 780.0,
 22.5, 0.0018, '2024-01-04 08:00:00',
 '2024-01-04 08:00:00', '2024-01-04 08:00:00', '需要关注', 1, 'admin'),

('cwl_020', 'default', 'station_003',
 9.750, 6.420, 0.070, 12.5, 14.2, 920.0, 650.0,
 26.8, 0.0018, '2024-01-05 08:00:00',
 '2024-01-05 08:00:00', '2024-01-05 08:00:00', '降雨补给', 1, 'admin');

-- 插入分析结果数据
INSERT INTO tb_conservation_analysis (
    id, tenant_id, station_id, start_time, end_time,
    initial_level, final_level, level_change, avg_rainfall, avg_evaporation, total_extraction,
    conservation_coefficient, conservation_potential, suggested_conservation_amount,
    conservation_suggestion, risk_level, risk_description, algorithm_version,
    analysis_details, create_time, update_time, status, creator
) VALUES
('ca_001', 'default', 'station_001',
 '2024-01-01 00:00:00', '2024-01-05 23:59:59',
 8.320, 8.180, -0.140, 12.04, 10.36, 2450.0,
 0.0856, 72.5, 1200.0,
 '建议增加人工补给，控制开采量在500m³/日以内。加强雨水收集利用，提高地下水涵养效率。',
 2, '中等风险：水位呈下降趋势，需要采取措施控制开采量', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-01T08:00:00","rawWaterLevel":12.45,"groundwaterLevel":8.32,"levelChange":-0.15},{"time":"2024-01-02T08:00:00","rawWaterLevel":12.38,"groundwaterLevel":8.28,"levelChange":-0.07}],"environmentFactors":{"avgTemperature":15.2,"avgHumidity":45.8,"soilType":"砂质壤土"}}',
 '2024-01-06 09:00:00', '2024-01-06 09:00:00', 2, 'admin'),

('ca_002', 'default', 'station_002',
 '2024-01-01 00:00:00', '2024-01-05 23:59:59',
 11.420, 11.580, 0.160, 18.92, 7.66, 1710.0,
 0.1245, 85.2, 800.0,
 '水位保持良好上升趋势，建议维持现有管理措施。可适当增加开采量，但需持续监测。',
 1, '低风险：水位稳定上升，涵养状况良好', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-01T08:00:00","rawWaterLevel":15.68,"groundwaterLevel":11.42,"levelChange":0.08},{"time":"2024-01-02T08:00:00","rawWaterLevel":15.72,"groundwaterLevel":11.46,"levelChange":0.04}],"environmentFactors":{"avgTemperature":12.8,"avgHumidity":52.3,"soilType":"粘质壤土"}}',
 '2024-01-06 10:00:00', '2024-01-06 10:00:00', 2, 'admin'),

('ca_003', 'default', 'station_003',
 '2024-01-01 00:00:00', '2024-01-05 23:59:59',
 6.520, 6.420, -0.100, 6.84, 16.9, 3380.0,
 0.0405, 35.8, 2500.0,
 '紧急建议：立即减少开采量至400m³/日以下，增加人工回灌。考虑从其他水源地调水补给。',
 3, '高风险：水位持续下降，开采量过大，急需干预', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-01T08:00:00","rawWaterLevel":9.85,"groundwaterLevel":6.52,"levelChange":-0.18},{"time":"2024-01-02T08:00:00","rawWaterLevel":9.78,"groundwaterLevel":6.45,"levelChange":-0.07}],"environmentFactors":{"avgTemperature":18.5,"avgHumidity":35.2,"soilType":"砂土"}}',
 '2024-01-06 11:00:00', '2024-01-06 11:00:00', 2, 'admin'),

-- 正在分析的任务
('ca_004', 'default', 'station_001',
 '2024-01-06 00:00:00', '2024-01-10 23:59:59',
 8.220, 8.280, 0.060, 20.76, 7.7, 1980.0,
 NULL, NULL, NULL, NULL, NULL, NULL, 'v1.2.0', NULL,
 '2024-01-11 08:30:00', '2024-01-11 08:30:00', 1, 'admin'),

-- 分析失败的任务
('ca_005', 'default', 'station_002',
 '2024-01-08 00:00:00', '2024-01-10 23:59:59',
 NULL, NULL, NULL, NULL, NULL, NULL,
 NULL, NULL, NULL, NULL, NULL, NULL, 'v1.2.0', NULL,
 '2024-01-11 09:00:00', '2024-01-11 09:00:00', 3, 'admin');

-- 提交事务
COMMIT;
