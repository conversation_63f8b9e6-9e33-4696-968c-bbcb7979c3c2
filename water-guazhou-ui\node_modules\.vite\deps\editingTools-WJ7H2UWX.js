import {
  A as A2,
  B,
  H,
  S as S2
} from "./chunk-4LJHCRF5.js";
import "./chunk-UWLG66KQ.js";
import "./chunk-VCDD3IVD.js";
import {
  j,
  t as t3
} from "./chunk-WJFUUMLN.js";
import {
  W,
  Z
} from "./chunk-U7VYWSSV.js";
import "./chunk-4W7HU754.js";
import {
  u as u3
} from "./chunk-GCMPBEAK.js";
import "./chunk-7GPM2ZU5.js";
import "./chunk-FQMXSCOG.js";
import "./chunk-IKOX2HGY.js";
import "./chunk-PWCXATLS.js";
import "./chunk-XXXEFHEN.js";
import "./chunk-I2B245QS.js";
import "./chunk-MN2LGVDI.js";
import "./chunk-OY3C7FMJ.js";
import {
  I as I2
} from "./chunk-ANH6666P.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-5ZZCQR67.js";
import "./chunk-NQQSL2QK.js";
import "./chunk-IZLLLMFE.js";
import "./chunk-K4QGLA2K.js";
import "./chunk-5XZZKPPL.js";
import "./chunk-PCLDCFRI.js";
import {
  u as u4
} from "./chunk-P6WIKP33.js";
import "./chunk-TBHJZ2TU.js";
import {
  U,
  a as a4,
  k,
  p,
  v as v4,
  x
} from "./chunk-S3MOIHQ7.js";
import "./chunk-TL5Y53I4.js";
import {
  E as E2,
  V,
  e as e5,
  g as g3
} from "./chunk-PTI7U6FU.js";
import {
  a as a3
} from "./chunk-ZOIBK6WV.js";
import "./chunk-FAMLZKHJ.js";
import "./chunk-6IU6DQRF.js";
import "./chunk-YELYN22P.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import "./chunk-A7PY25IH.js";
import "./chunk-RURSJOSG.js";
import "./chunk-EM6CPBT6.js";
import {
  l as l5
} from "./chunk-JJ3NE6DY.js";
import "./chunk-4VO6N7OL.js";
import "./chunk-7VXHHPI3.js";
import "./chunk-OYGWWPGZ.js";
import "./chunk-77E52HT5.js";
import {
  i
} from "./chunk-WPZSZ6CK.js";
import "./chunk-B4YFVQZH.js";
import "./chunk-4RJYWSAT.js";
import "./chunk-T6GIT4YI.js";
import "./chunk-PHEIXDVR.js";
import "./chunk-WX7B7OKM.js";
import "./chunk-UQWZJZ2S.js";
import "./chunk-5S4W3ME5.js";
import "./chunk-CDZ24ELJ.js";
import "./chunk-VHLK35TF.js";
import "./chunk-KXA6I5TQ.js";
import {
  n as n4
} from "./chunk-HAEVWZ5B.js";
import {
  e as e4
} from "./chunk-HURTVQSL.js";
import "./chunk-TNGCGN7L.js";
import "./chunk-ONE6GLG5.js";
import {
  o,
  r as r3,
  v as v3
} from "./chunk-SROTSYJS.js";
import {
  l as l4,
  n as n3
} from "./chunk-FOE4ICAJ.js";
import "./chunk-P2G4OGHI.js";
import {
  h
} from "./chunk-4YSFMXMT.js";
import "./chunk-56K7OMWB.js";
import "./chunk-IU22XAFH.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-SY6DBVDS.js";
import {
  _n,
  rn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-MIA6BJ32.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FBVKALLT.js";
import "./chunk-FIVMDF4P.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  l as l2
} from "./chunk-QUHG7NMD.js";
import "./chunk-3WEGNHPY.js";
import {
  g as g2
} from "./chunk-TLKX5XIJ.js";
import "./chunk-MQ2IOGEF.js";
import "./chunk-24NZLSKM.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-ETY52UBV.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import {
  d
} from "./chunk-ADTC77YB.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import {
  S
} from "./chunk-R3VLALN5.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import {
  u
} from "./chunk-3WCHZJQK.js";
import {
  v as v2
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  y as y2
} from "./chunk-VX6YUKFM.js";
import "./chunk-6ILWLF72.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  $,
  E,
  I,
  R
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-YD3YIZNH.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-73VUEZR7.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import {
  l as l3
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  b,
  m
} from "./chunk-EIGTETCG.js";
import {
  A,
  P,
  e as e3,
  g,
  q,
  r as r2,
  s as s2,
  u as u2,
  z
} from "./chunk-MQAXMQFG.js";
import {
  n as n2
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  n
} from "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  a,
  e as e2,
  l,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/interactive/editingTools/draw/symbols.js
var t4 = new d({ data: { type: "CIMSymbolReference", symbol: { type: "CIMLineSymbol", symbolLayers: [{ type: "CIMSolidStroke", effects: [{ type: "CIMGeometricEffectDashes", dashTemplate: [3.75, 3.75], lineDashEnding: "HalfPattern", controlPointEnding: "NoConstraint" }], enable: true, capStyle: "Butt", joinStyle: "Round", miterLimit: 10, width: 1.6, color: [255, 255, 255, 255] }, { type: "CIMSolidStroke", enable: true, capStyle: "Butt", joinStyle: "Round", miterLimit: 10, width: 2, color: [0, 0, 0, 255] }] } } });
var l6 = new y2({ style: "circle", size: 6, color: [127, 127, 127, 1], outline: { color: [50, 50, 50], width: 1 } });
var i2 = new y2({ style: "circle", size: 6, color: [255, 127, 0, 1], outline: { color: [50, 50, 50], width: 1 } });

// node_modules/@arcgis/core/views/2d/interactive/editingTools/draw/DrawGraphicTool2D.js
var E3 = class extends H {
  constructor(e7) {
    super(e7), this._visualElementGraphics = { outline: null, regularVertices: null, activeVertex: null }, this.activeFillSymbol = null, this.type = "draw-2d", this._visualElementSymbols = { outline: l(e7.activeLineSymbol, t4), regularVertices: l(e7.regularVerticesSymbol, l6), activeVertex: l(e7.activeVertexSymbol, i2), fill: e2(e7.activeFillSymbol) };
  }
  normalizeCtorArgs(e7) {
    const t7 = { ...e7 };
    return delete t7.activeFillSymbol, delete t7.activeVertexSymbol, delete t7.regularVerticesSymbol, delete t7.activeLineSymbol, t7;
  }
  initializeGraphic(e7) {
    return r(this._visualElementSymbols.fill) && (e7.symbol = this._visualElementSymbols.fill), null;
  }
  makeDrawOperation() {
    const { defaultZ: e7, hasZ: t7, view: i5 } = this;
    return new k({ view: i5, manipulators: this.manipulators, geometryType: B(this.geometryType), drawingMode: this.mode, hasZ: t7, defaultZ: e7, snapToSceneEnabled: this.snapToScene, drawSurface: new a4(i5, t7, e7), hasM: false, snappingManager: this.snappingManager, snappingVisualizer: new u4(this.internalGraphicsLayer), tooltipOptions: this.tooltipOptions });
  }
  onActiveVertexChanged(e7) {
    if ("point" === this.geometryType) return null;
    const [r6, s4] = e7, n5 = new w({ x: r6, y: s4, spatialReference: this.view.spatialReference });
    return r(this._visualElementGraphics.activeVertex) ? (this._visualElementGraphics.activeVertex.geometry = n5, null) : (this._visualElementGraphics.activeVertex = new g2({ geometry: n5, symbol: this._visualElementSymbols.activeVertex, attributes: { displayOrder: 2 } }), this.internalGraphicsLayer.add(this._visualElementGraphics.activeVertex), this.internalGraphicsLayer.graphics.sort(_), n(() => {
      r(this._visualElementGraphics.activeVertex) && (this.internalGraphicsLayer.remove(this._visualElementGraphics.activeVertex), this._visualElementGraphics.activeVertex = a(this._visualElementGraphics.activeVertex));
    }));
  }
  onOutlineChanged(e7) {
    const r6 = e7.clone();
    if ("polyline" === r6.type) {
      const e8 = r6.paths[r6.paths.length - 1];
      e8.splice(0, e8.length - 2);
    }
    return r(this._visualElementGraphics.outline) ? (this._visualElementGraphics.outline.geometry = r6, null) : (this._visualElementGraphics.outline = new g2({ geometry: r6, symbol: this._visualElementSymbols.outline, attributes: { displayOrder: 0 } }), this.internalGraphicsLayer.add(this._visualElementGraphics.outline), this.internalGraphicsLayer.graphics.sort(_), n(() => {
      r(this._visualElementGraphics.outline) && (this.internalGraphicsLayer.remove(this._visualElementGraphics.outline), this._visualElementGraphics.outline = a(this._visualElementGraphics.outline));
    }));
  }
  onRegularVerticesChanged(e7) {
    const r6 = new u({ points: e7, spatialReference: this.view.spatialReference });
    return r(this._visualElementGraphics.regularVertices) ? (this._visualElementGraphics.regularVertices.geometry = r6, null) : (this._visualElementGraphics.regularVertices = new g2({ geometry: r6, symbol: this._visualElementSymbols.regularVertices, attributes: { displayOrder: 1 } }), this.internalGraphicsLayer.add(this._visualElementGraphics.regularVertices), this.internalGraphicsLayer.graphics.sort(_), n(() => {
      r(this._visualElementGraphics.regularVertices) && (this.internalGraphicsLayer.remove(this._visualElementGraphics.regularVertices), this._visualElementGraphics.regularVertices = a(this._visualElementGraphics.regularVertices));
    }));
  }
};
function _(e7, t7) {
  var _a, _b;
  return (((_a = e7.attributes) == null ? void 0 : _a.displayOrder) ?? -1 / 0) - (((_b = t7.attributes) == null ? void 0 : _b.displayOrder) ?? -1 / 0);
}
e([y()], E3.prototype, "activeFillSymbol", void 0), e([y({ readOnly: true })], E3.prototype, "type", void 0), e([y({ constructOnly: true, nonNullable: true })], E3.prototype, "view", void 0), E3 = e([a2("esri.views.2d.interactive.draw.DrawGraphicTool2D")], E3);

// node_modules/@arcgis/core/core/analysisThemeUtils.js
function t5(o2, t7) {
  const e7 = o2.a * t7;
  return u3(o2) > 225 ? new l3([0, 0, 0, e7]) : new l3([255, 255, 255, e7]);
}
function e6(o2, r6) {
  const t7 = new l3(o2);
  return t7.a *= r6, t7;
}
function i3(n5 = 1) {
  return e6(s.analysisTheme.accentColor, n5);
}
function u5(n5 = 1) {
  return t5(i3(), n5);
}

// node_modules/@arcgis/core/views/2d/interactive/editingTools/manipulations/Manipulation.js
var r4 = class {
  get hovering() {
    return this.someManipulator((r6) => r6.hovering);
  }
  get grabbing() {
    return this.someManipulator((r6) => r6.grabbing);
  }
  get dragging() {
    return this.someManipulator((r6) => r6.dragging);
  }
  hasManipulator(r6) {
    return this.someManipulator((t7) => t7 === r6);
  }
  someManipulator(r6) {
    let t7 = false;
    return this.forEachManipulator((a5) => {
      !t7 && r6(a5) && (t7 = true);
    }), t7;
  }
};
var t6;
!function(r6) {
  r6[r6.TRANSLATE_XY = 0] = "TRANSLATE_XY", r6[r6.SCALE = 1] = "SCALE", r6[r6.ROTATE = 2] = "ROTATE";
}(t6 || (t6 = {}));

// node_modules/@arcgis/core/views/2d/interactive/editingTools/manipulations/DragManipulation.js
var c = class extends r4 {
  constructor(t7) {
    super(), this._view = t7.view, this._tool = t7.tool, this._graphic = t7.graphic, this._manipulator = this._createManipulator(), this.forEachManipulator((t8) => this._tool.manipulators.add(t8));
  }
  destroy() {
    this.forEachManipulator((t7) => {
      this._tool.manipulators.remove(t7), t7.destroy();
    }), this._tool = null, this._view = null, this._manipulator = null, this._graphic = null;
  }
  forEachManipulator(t7) {
    t7(this._manipulator, t6.TRANSLATE_XY);
  }
  createDragPipeline(i5, r6) {
    let e7 = null, c2 = null, p2 = 0, l8 = 0, m3 = 0;
    const { offsetX: h2, offsetY: u6, size: _2 } = t3(e2(this._graphic.symbol));
    return x(this._manipulator, (t7, o2) => {
      o2.next((t8) => {
        if ("start" === t8.action) {
          const t9 = i5();
          e7 = t9.editGeometryOperations, c2 = t9.constraints;
        }
        return t8;
      }).next(v4(this._view)).next((t8) => {
        const { x: i6, y: o3, z: a5 } = t8.mapEnd;
        if (c2 && (i6 + h2 < c2.xmin || o3 + u6 - _2 < c2.ymin || i6 + h2 > c2.xmax || o3 + u6 - _2 > c2.ymax)) return t8;
        "start" === t8.action && (p2 = t8.mapStart.x, l8 = t8.mapStart.y, m3 = t8.mapStart.z);
        const n5 = i6 - p2, f = o3 - l8, v5 = a5 - m3;
        p2 = i6, l8 = o3, m3 = a5;
        const x2 = [];
        for (const r7 of e7.data.components) x2.push(...r7.vertices);
        const d3 = "start" === t8.action ? E2.NEW_STEP : E2.ACCUMULATE_STEPS, y4 = e7.moveVertices(x2, n5, f, v5, d3);
        return r6(t8, y4), t8;
      });
    });
  }
  _createManipulator() {
    const t7 = this._view, i5 = this._graphic;
    return new j({ view: t7, graphic: i5, selectable: true, cursor: "move" });
  }
};

// node_modules/@arcgis/core/views/2d/interactive/editingTools/ControlPointsTransformTool.js
var k2 = { up: "ArrowUp", down: "ArrowDown", left: "ArrowLeft", right: "ArrowRight", toggleOpacity: "t", shift: "Shift", primaryKey: e4 };
var b2 = 1;
var G = 10;
var M = new l3("#009AF2");
var w2 = class extends p {
  constructor(t7) {
    super(t7), this._isOpacityToggled = false, this._isModifierActive = false, this._factor = 1, this._initialControlPoints = null, this._graphicsLayer = new h({ internal: true, listMode: "hide", visible: false, effect: "drop-shadow(0px, 0px, 3px)" }), this._undoStack = [], this._redoStack = [], this._sharedUndoStack = [], this._sharedRedoStack = [], this._highlightHandle = null, this.activeHandle = 0;
  }
  initialize() {
    this._initialize();
  }
  destroy() {
    const { map: t7 } = this.view;
    this._controlPointManipulations.forEach((t8) => t8.destroy()), this._controlPointEditGeometryOperations.forEach((t8) => t8.destroy()), t7.removeMany([this._graphicsLayer]), this._graphicsLayer.removeAll(), this._graphicsLayer = a(this._graphicsLayer), this._georeference = null, this._controlPointGraphics = null, this._controlPointManipulations = null, this._graphicsLayer = null, this._controlPointEditGeometryOperations = null, this._undoStack = null, this._redoStack = null, this._initialControlPoints = null, this._sharedUndoStack = null, this._sharedRedoStack = null;
  }
  get _hasValidSpatialReference() {
    return I(this.view.spatialReference);
  }
  onActivate() {
    this.visible = true;
  }
  onDeactivate() {
    this.visible = false;
  }
  onShow() {
    this._graphicsLayer.visible = true;
  }
  onHide() {
    this._graphicsLayer.visible = false;
  }
  canUndo() {
    const t7 = this._undoStack[this._undoStack.length - 1];
    return null != t7 && this._controlPointEditGeometryOperations[t7].canUndo;
  }
  canRedo() {
    const t7 = this._redoStack[this._redoStack.length - 1];
    return null != t7 && this._controlPointEditGeometryOperations[t7].canRedo;
  }
  undo() {
    if (this._undoStack.length > 0) {
      const t7 = this._undoStack.pop();
      this._controlPointEditGeometryOperations[t7].undo(), this.updateGraphics(), this._redoStack.push(t7);
    }
  }
  redo() {
    if (this._redoStack.length > 0) {
      const t7 = this._redoStack.pop();
      this._controlPointEditGeometryOperations[t7].redo(), this.updateGraphics(), this._undoStack.push(t7);
    }
  }
  refresh() {
    const { mediaElement: t7 } = this;
    if (t(t7.georeference)) return;
    const e7 = t7.georeference;
    "control-points" !== e7.type || t(e7.coords) || (this._georeference = e7, e2(this._georeference.controlPoints).forEach(({ mapPoint: t8 }, e8) => {
      const i5 = this._controlPointEditGeometryOperations[e8], o2 = i5.data.components[0].vertices[0];
      i5.setVertexPosition(o2, i5.data.coordinateHelper.pointToVector(t8));
    }), this.updateGraphics());
  }
  reset() {
    this._georeference.controlPoints = this._initialControlPoints, this.refresh(), this._sharedUndoStack.length = 0, this._sharedRedoStack.length = 0;
  }
  updateGraphics() {
    const t7 = this._georeference, e7 = e2(t7.controlPoints), i5 = e2(e7[0].mapPoint).spatialReference, o2 = this._hasValidSpatialReference;
    this._georeference.controlPoints = this._controlPointEditGeometryOperations.map((r6, s4) => {
      const n5 = r6.data.geometry;
      return this._controlPointGraphics[s4].geometry = n5, { mapPoint: rn(n5, i5), sourcePoint: o2 ? e2(e7[s4]).sourcePoint : t7.toSource(n5) };
    });
  }
  updateActiveHandle(t7) {
    if (this.activeHandle === t7) return;
    const e7 = e2(this._controlPointGraphics[this.activeHandle].symbol).clone();
    I2(e7, i3()), this._controlPointGraphics[this.activeHandle].symbol = e7;
    const i5 = e2(this._controlPointGraphics[t7].symbol).clone();
    I2(i5, M), this._controlPointGraphics[t7].symbol = i5, this.activeHandle = t7, this.view.surface === document.activeElement && this.highlightActiveHandle();
  }
  async highlightActiveHandle() {
    this.removeHighlightActiveHandle();
    const t7 = await this.view.whenLayerView(this._graphicsLayer);
    this._highlightHandle = t7.highlight(this._controlPointGraphics[this.activeHandle]);
  }
  removeHighlightActiveHandle() {
    this._highlightHandle && this._highlightHandle.remove();
  }
  setSharedUndoStack(t7) {
    this._sharedUndoStack = t7;
  }
  setSharedRedoStack(t7) {
    this._sharedRedoStack = t7;
  }
  async _initialize() {
    const { view: t7, mediaElement: e7 } = this;
    if (t(e7.georeference)) return;
    const r6 = e7.georeference;
    "control-points" !== r6.type || t(r6.coords) || (this._georeference = r6, this._initialControlPoints = e2(this._georeference.controlPoints), t7.map.addMany([this._graphicsLayer]), t7.focus(), this.visible = false, this.finishToolCreation(), await this._loadProjectionEngine(), this._controlPointEditGeometryOperations = e2(this._georeference.controlPoints).map(({ mapPoint: e8 }) => V.fromGeometry(rn(e8, t7.spatialReference), l5.Local)), this._controlPointGraphics = this._controlPointEditGeometryOperations.map((t8, e8) => new g2({ symbol: new d({ data: { type: "CIMSymbolReference", symbol: { type: "CIMPointSymbol", symbolLayers: [{ type: "CIMVectorMarker", enable: true, colorLocked: true, anchorPoint: { x: 0, y: -15.75 }, anchorPointUnits: "Absolute", dominantSizeAxis3D: "Y", size: 9, billboardMode3D: "FaceNearPlane", frame: { xmin: 0, ymin: 0, xmax: 84.3, ymax: 84.3 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { rings: [[[83.2, 32.5], [84.3, 40.7], [83.8, 48.9], [81.7, 56.9], [78.1, 64.3], [73, 70.9], [66.9, 76.4], [59.7, 80.5], [51.9, 83.2], [43.7, 84.3], [35.4, 83.8], [27.4, 81.7], [20, 78], [13.4, 73], [7.9, 66.8], [3.8, 59.7], [1.1, 51.9], [0, 43.7], [0.5, 35.4], [2.6, 27.4], [6.3, 20], [11.3, 13.4], [17.5, 7.9], [24.7, 3.8], [32.5, 1.1], [39.8, 0.1], [47.1, 0.3], [54.3, 1.8], [61.1, 4.5], [67.4, 8.4], [72.9, 13.3], [77.4, 19.1], [80.9, 25.5], [83.2, 32.5]]] }, symbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: [255, 255, 255, 255] }] } }], scaleSymbolsProportionally: true, respectFrame: true, clippingPath: { type: "CIMClippingPath", clippingType: "Intersect", path: { rings: [[[0, 0], [84.3, 0], [84.3, 84.3], [0, 84.3], [0, 0]]] } }, rotation: 0 }, { type: "CIMVectorMarker", enable: true, anchorPoint: { x: 0, y: -11.25 }, anchorPointUnits: "Absolute", dominantSizeAxis3D: "Y", size: 22.5, billboardMode3D: "FaceNearPlane", frame: { xmin: 0, ymin: 0, xmax: 197.7, ymax: 294.7 }, markerGraphics: [{ type: "CIMMarkerGraphic", geometry: { rings: [[[98.9, 0], [119.4, 23.2], [139.4, 49.3], [156.8, 75.2], [171.2, 100.8], [182.4, 125.3], [190.6, 148.8], [195.7, 171.4], [197.7, 192.9], [197.7, 195.8], [197.7, 200.3], [197.6, 202.5], [197.5, 204.8], [197.3, 207.1], [197, 209.4], [196.7, 211.7], [196.4, 214.1], [196, 216.4], [195.5, 218.7], [195, 221.1], [194.4, 223.4], [193.7, 225.8], [193, 228.1], [192.2, 230.5], [191.4, 232.8], [190.5, 235.1], [189.5, 237.5], [188.5, 239.7], [187.4, 242], [186.2, 244.3], [185, 246.5], [183.7, 248.7], [182.4, 250.9], [181, 253.1], [179.5, 255.2], [178, 257.3], [176.4, 259.4], [174.7, 261.4], [173.1, 263.3], [171.3, 265.3], [169.5, 267.2], [167.7, 269], [165.8, 270.8], [163.9, 272.5], [161.9, 274.2], [159.9, 275.8], [157.8, 277.4], [155.7, 278.9], [153.6, 280.4], [151.4, 281.7], [149.2, 283.1], [147, 284.4], [144.8, 285.6], [142.5, 286.7], [140.3, 287.8], [138, 288.8], [135.7, 289.8], [133.4, 290.7], [131, 291.5], [128.7, 292.3], [126.4, 293], [124, 293.6], [121.7, 294.2], [119.4, 294.7], [117, 295.2], [114.7, 295.6], [112.4, 296], [110.1, 296.3], [107.8, 296.5], [105.5, 296.7], [103.3, 296.8], [101.1, 296.9], [98.8, 296.9], [83.1, 295.7], [67.8, 292], [53.3, 285.9], [39.9, 277.5], [28.1, 267.2], [18, 255.1], [10, 241.5], [4.2, 226.9], [0.9, 211.5], [0, 195.8], [0.1, 192.9], [2.1, 171.4], [7.2, 148.8], [15.4, 125.3], [26.6, 100.8], [41, 75.2], [58.4, 49.3], [78.4, 23.2], [98.9, 0]]] }, symbol: { type: "CIMPolygonSymbol", symbolLayers: [{ type: "CIMSolidFill", enable: true, color: e8 === this.activeHandle ? M.toArray() : i3().toArray() }] } }], scaleSymbolsProportionally: true, respectFrame: true, clippingPath: { type: "CIMClippingPath", clippingType: "Intersect", path: { rings: [[[0, 0], [197.7, 0], [197.7, 294.7], [0, 294.7], [0, 0]]] } }, rotation: 0 }], haloSize: 1, scaleX: 1, angleAlignment: "Display", angle: 0 } } }), geometry: t8.data.geometry })), this._graphicsLayer.graphics.addMany([...this._controlPointGraphics]), this._controlPointManipulations = this._controlPointGraphics.map((e8) => new c({ tool: this, view: t7, graphic: e8 })), this.addHandles([...this._controlPointManipulations.map((t8, e8) => t8.createDragPipeline(this._getInfo.bind(this, e8), (t9, i5) => {
      "start" === t9.action && (this._undoStack.push(e8), this._redoStack = [], this._sharedUndoStack.push({ tool: this, operation: i5 }), this._sharedRedoStack.length = 0), this.updateGraphics();
    })), l2(() => this.view.scale, () => this.active ? this.updateGraphics() : null)]), this._controlPointManipulations.forEach((t8, e8) => {
      const i5 = (t9) => {
        this.addHandles([t9.events.on(["click", "grab-changed"], (t10) => this.updateActiveHandle(e8))]);
      };
      t8.forEachManipulator(i5);
    }), this.addHandles([t7.on("key-down", (i5) => {
      t7.activeTool === this && (i5.key !== k2.shift || i5.repeat || (this._isModifierActive = true, i5.stopPropagation()), i5.key !== k2.toggleOpacity || i5.repeat || (e7.opacity *= this._isOpacityToggled ? 2 : 0.5, this._isOpacityToggled = !this._isOpacityToggled, i5.stopPropagation()), i5.key !== k2.primaryKey || i5.repeat || (this._factor = G, i5.stopPropagation()), this._isModifierActive && (i5.key === k2.up && (this._move(0, this._factor), i5.stopPropagation()), i5.key === k2.down && (this._move(0, -this._factor), i5.stopPropagation()), i5.key === k2.left && (this._move(-this._factor, 0), i5.stopPropagation()), i5.key === k2.right && (this._move(this._factor, 0), i5.stopPropagation())));
    }), t7.on("key-up", (e8) => {
      t7.activeTool === this && (e8.key === k2.shift && (this._isModifierActive = false, e8.stopPropagation()), e8.key === k2.primaryKey && (this._factor = b2, e8.stopPropagation()));
    })]));
  }
  async _loadProjectionEngine() {
    const t7 = e2(e2(this._georeference.controlPoints)[0].mapPoint);
    return _n(t7.spatialReference, this.view.spatialReference);
  }
  _getInfo(t7) {
    return { editGeometryOperations: this._controlPointEditGeometryOperations[t7], constraints: this._hasValidSpatialReference ? null : { xmin: 0, ymin: 0, xmax: this._georeference.width, ymax: this._georeference.height } };
  }
  _move(t7, e7) {
    const i5 = this._controlPointEditGeometryOperations[this.activeHandle], o2 = [];
    for (const s4 of i5.data.components) o2.push(...s4.vertices);
    const r6 = i5.moveVertices(o2, t7 * this.view.resolution, e7 * this.view.resolution, 0, E2.NEW_STEP);
    this._sharedUndoStack.push({ tool: this, operation: r6 }), this._sharedRedoStack.length = 0, this.updateGraphics();
  }
};
e([y()], w2.prototype, "_hasValidSpatialReference", null), e([y()], w2.prototype, "activeHandle", void 0), e([y({ constructOnly: true, nonNullable: true })], w2.prototype, "mediaElement", void 0), e([y({ constructOnly: true })], w2.prototype, "view", void 0), w2 = e([a2("esri.views.2d.interactive.editingTools.ControlPointsTransformTool")], w2);

// node_modules/@arcgis/core/views/2d/interactive/editingTools/manipulations/utils.js
function s3(t7, e7) {
  "start" === t7.action ? e7.cursor = "grabbing" : e7.cursor = "grab";
}
var r5 = class {
  constructor() {
    this._lastDragEvent = null, this.next = new U(), this._enabled = false;
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(t7) {
    if (this._enabled !== t7 && r(this._lastDragEvent)) {
      const e7 = { ...this._lastDragEvent, action: "update" };
      t7 && this._adjustScaleFactors(e7), this.next.execute(e7);
    }
    this._enabled = t7;
  }
  createDragEventPipelineStep() {
    return this._lastDragEvent = null, (t7) => (this._lastDragEvent = "end" !== t7.action ? { ...t7 } : null, this._enabled && this._adjustScaleFactors(t7), t7);
  }
  _adjustScaleFactors(t7) {
    const e7 = 0 !== t7.direction[0] && 0 !== t7.direction[1] ? Math.max(Math.abs(t7.factor1), Math.abs(t7.factor2)) : 0 === t7.direction[0] ? Math.abs(t7.factor2) : Math.abs(t7.factor1);
    t7.factor1 = t7.factor1 < 0 ? -e7 : e7, t7.factor2 = t7.factor2 < 0 ? -e7 : e7;
  }
};
var i4 = class {
  constructor() {
    this._lastDragEvent = null, this.next = new U(), this._enabled = false;
  }
  get enabled() {
    return this._enabled;
  }
  set enabled(t7) {
    if (this._enabled !== t7 && r(this._lastDragEvent)) {
      const e7 = { ...this._lastDragEvent, action: "update" };
      t7 && this._adjustRotateAngle(e7), this.next.execute(e7);
    }
    this._enabled = t7;
  }
  createDragEventPipelineStep() {
    return this._lastDragEvent = null, (t7) => (this._lastDragEvent = "end" !== t7.action ? { ...t7 } : null, this._enabled && this._adjustRotateAngle(t7), t7);
  }
  _adjustRotateAngle(a5) {
    const n5 = b(a5.rotateAngle);
    a5.rotateAngle = m(5 * Math.round(n5 / 5));
  }
};

// node_modules/@arcgis/core/views/2d/interactive/editingTools/manipulations/RotateManipulation.js
var m2 = class extends r4 {
  constructor(i5) {
    super(), this._handles = new t2(), this._originCache = n2(), this._view = i5.view, this._tool = i5.tool, this._graphic = i5.graphic, this._snapRotation = i5.snapRotation, this._manipulator = this._createManipulator(), this._handles.add([this._manipulator.events.on("grab-changed", (t7) => s3(t7, this._manipulator))]), this.forEachManipulator((t7) => this._tool.manipulators.add(t7));
  }
  destroy() {
    this._handles.destroy(), this.forEachManipulator((t7) => {
      this._tool.manipulators.remove(t7), t7.destroy();
    }), this._tool = null, this._view = null, this._manipulator = null, this._snapRotation = null, this._graphic = null, this._handles = null, this._originCache = null;
  }
  forEachManipulator(t7) {
    t7(this._manipulator, t6.ROTATE);
  }
  createDragPipeline(t7, r6) {
    let e7 = null, o2 = null;
    return x(this._manipulator, (a5, s4) => {
      s4.next((i5) => {
        if ("start" === i5.action) {
          a5.cursor = "grabbing";
          const i6 = t7();
          e7 = i6.plane, o2 = i6.editGeometryOperations;
        }
        return i5;
      }).next(v4(this._view)).next((t8) => ({ ...t8, rotateAngle: a3(t8.mapStart, t8.mapEnd, { x: e7.origin[0], y: e7.origin[1] }, true) })).next(this._snapRotation.createDragEventPipelineStep(), this._snapRotation.next).next((t8) => {
        const a6 = r2(this._originCache, e7.origin), n5 = [];
        for (const i5 of o2.data.components) n5.push(...i5.vertices);
        const s5 = "start" === t8.action ? E2.NEW_STEP : E2.ACCUMULATE_STEPS, l8 = o2.rotateVertices(n5, a6, t8.rotateAngle, s5, e5.REPLACE);
        return S2(l8, e7), r6(t8, l8), t8;
      }).next((t8) => ("end" === t8.action && (a5.cursor = "grab"), t8));
    });
  }
  _createManipulator() {
    const t7 = this._view, i5 = this._graphic;
    return new j({ view: t7, graphic: i5, selectable: true, cursor: "grab" });
  }
};

// node_modules/@arcgis/core/views/2d/interactive/editingTools/manipulations/ScaleManipulation.js
var E4 = 10;
var S3 = 1e-6;
var y3 = 0.3;
function j2(t7) {
  const i5 = s2(t7.basis1), e7 = s2(t7.basis2);
  return y3 * Math.min(i5, e7);
}
var w3 = class extends r4 {
  constructor(i5) {
    super(), this._handles = new t2(), this._planeStart = W(), this._displayPlaneStart = W(), this._originCache = n2(), this._axisCache = n3(), this._renderStartCache = n2(), this._renderEndCache = n2(), this._resizeOriginCache = n2(), this._view = i5.view, this._tool = i5.tool, this._graphic = i5.graphic, this._direction = i5.direction, this._preserveAspectRatio = i5.preserveAspectRatio, this._manipulator = this._createManipulator(), this._handles.add([this._manipulator.events.on("grab-changed", (t7) => s3(t7, this._manipulator))]), this.forEachManipulator((t7) => this._tool.manipulators.add(t7));
  }
  destroy() {
    this._handles.destroy(), this.forEachManipulator((t7) => {
      this._tool.manipulators.remove(t7), t7.destroy();
    }), this._tool = null, this._view = null, this._graphic = null, this._manipulator = null, this._direction = null, this._handles = null, this._planeStart = null, this._displayPlaneStart = null, this._originCache = null, this._axisCache = null, this._renderStartCache = null, this._renderEndCache = null, this._resizeOriginCache = null, this._preserveAspectRatio = null;
  }
  forEachManipulator(t7) {
    t7(this._manipulator, t6.SCALE);
  }
  createDragPipeline(t7, s4) {
    let c2 = null, h2 = null, _2 = null, d3 = 0, m3 = null, g4 = null;
    const y4 = this._planeStart, w4 = this._displayPlaneStart, P2 = this._direction;
    return x(this._manipulator, (f, M2) => {
      M2.next((i5) => {
        if ("start" === i5.action) {
          f.cursor = "grabbing";
          const i6 = t7();
          c2 = i6.plane, h2 = i6.displayPlane, _2 = i6.editGeometryOperations, d3 = E4 * this._view.resolution, Z(c2, y4), Z(h2, w4);
          const e7 = R(_2.data.spatialReference);
          m3 = e7 ? e7.valid[1] - e7.valid[0] - 3 * E4 * this._view.resolution : null;
        }
        return i5;
      }).next(v4(this._view)).next((t8) => {
        const i5 = r2(this._renderStartCache, [t8.mapStart.x, t8.mapStart.y, 0]), e7 = r2(this._renderEndCache, [t8.mapEnd.x, t8.mapEnd.y, 0]), s5 = r2(this._resizeOriginCache, w4.origin);
        q(s5, s5, w4.basis1, -P2[0]), q(s5, s5, w4.basis2, -P2[1]), e3(e7, e7, s5), e3(i5, i5, s5);
        const c3 = 0 !== P2[0] && 0 !== P2[1], p2 = j2(w4), u6 = j2(h2) / p2, _3 = (t9, s6) => {
          if (0 === t9) return 1;
          let a5 = s2(s6), n5 = 0.5 * t9 * P(s6, e7) / a5;
          const o2 = n5 < 0 ? -1 : 1;
          if (c3) {
            n5 += (a5 - 0.5 * t9 * P(s6, i5) / a5) * o2 * u6;
          }
          const h3 = a5 < 1.5 * d3 ? 1 : S3;
          return a5 = Math.max(a5 - d3, S3), o2 > 0 && (n5 -= E4 * this._view.resolution), o2 * Math.max(o2 * (n5 / a5), h3);
        }, m4 = _3(P2[0], w4.basis1), f2 = _3(P2[1], w4.basis2);
        return { ...t8, direction: P2, factor1: m4, factor2: f2 };
      }).next(this._preserveAspectRatio.createDragEventPipelineStep(), this._preserveAspectRatio.next).next((t8) => {
        const r6 = r2(this._originCache, y4.origin);
        q(r6, r6, y4.basis1, -P2[0]), q(r6, r6, y4.basis2, -P2[1]);
        const o2 = r3(this._axisCache, y4.basis1[0], y4.basis1[1]);
        v3(o2, o2);
        const l8 = [];
        for (const i5 of _2.data.components) l8.push(...i5.vertices);
        const h3 = "start" === t8.action ? E2.NEW_STEP : E2.ACCUMULATE_STEPS, u6 = _2.scaleVertices(l8, r6, o2, t8.factor1, t8.factor2, h3, e5.REPLACE);
        return m3 && m3 < _2.data.geometry.extent.width && g4 ? _2.updateVertices(l8, g4) : (Z(y4, c2), S2(u6, c2), g4 = u6.operation, s4(t8, u6)), t8;
      }).next((t8) => ("end" === t8.action && (f.cursor = "grab"), t8));
    });
  }
  _createManipulator() {
    return new j({ view: this._view, graphic: this._graphic, selectable: true, cursor: "grab" });
  }
};

// node_modules/@arcgis/core/views/2d/interactive/editingTools/TransformTool.js
var F = { up: "ArrowUp", down: "ArrowDown", left: "ArrowLeft", right: "ArrowRight", plus: "+", minus: "-", toggleOpacity: "t", shift: "Shift", primaryKey: e4 };
var J = 80;
var Q = 10;
var X = 30;
var Y = [[1, 1], [1, -1], [-1, -1], [-1, 1], [1, 0], [0, -1], [-1, 0], [0, 1]];
var Z2 = 1;
var $2 = 10;
var tt = class extends p {
  constructor(t7) {
    super(t7), this._initialControlPoints = null, this._initialGeometry = null, this._graphic = null, this._planeCache = W(), this._displayPlaneCache = W(), this._mainAxisCache = n3(), this._rotationHandleCache = n2(), this._cornerA = n2(), this._cornerB = n2(), this._cornerC = n2(), this._cornerD = n2(), this._avgAB = n2(), this._avgBC = n2(), this._avgCD = n2(), this._avgDA = n2(), this._preserveAspectRatio = new r5(), this._snapRotation = new i4(), this._graphicsLayer = new h({ internal: true, listMode: "hide", visible: false }), this._sharedUndoStack = [], this._sharedRedoStack = [], this._isOpacityToggled = false, this._isModifierActive = false, this._factor = 1, this.preserveAspectRatio = null, this.snapRotation = null;
  }
  initialize() {
    this._initialize();
  }
  destroy() {
    const { map: t7 } = this.view;
    this._dragManipulation.destroy(), this._rotateManipulation.destroy(), this._scaleManipulations.forEach((t8) => t8.destroy()), this._editGeometryOperations.destroy(), t7.removeMany([this._graphicsLayer]), this._graphicsLayer.removeAll(), this._graphicsLayer = a(this._graphicsLayer), this._initialControlPoints = null, this._initialGeometry = null, this._graphic = null, this._preserveAspectRatio = null, this._snapRotation = null, this._planeCache = null, this._displayPlaneCache = null, this._rotationHandleCache = null, this._mainAxisCache = null, this._cornerA = null, this._cornerB = null, this._cornerC = null, this._cornerD = null, this._avgAB = null, this._avgBC = null, this._avgCD = null, this._avgDA = null, this._sharedUndoStack = null, this._sharedRedoStack = null;
  }
  get _plane() {
    const t7 = this._graphic.geometry;
    if (!r(t7)) return null;
    const e7 = this._editGeometryOperations.data, i5 = e7.components[0].edges[0], s4 = o(this._mainAxisCache, i5.leftVertex.pos, i5.rightVertex.pos);
    v3(s4, s4);
    let o2 = J * this.view.resolution;
    const a5 = this.view.spatialReference;
    return E(a5, t7.spatialReference) && (o2 *= $(a5) / $(t7.spatialReference)), A2(s4, e7, o2, this._planeCache);
  }
  get _displayPlane() {
    const t7 = this._plane;
    if (!t7) return null;
    const e7 = this._displayPlaneCache;
    Z(t7, e7);
    const i5 = Q * this.view.resolution;
    return g(e7.basis1, e7.basis1, 1 + i5 / s2(e7.basis1)), g(e7.basis2, e7.basis2, 1 + i5 / s2(e7.basis2)), e7;
  }
  get _backgroundGraphicGeometry() {
    const t7 = this._displayPlane;
    if (!t7) return null;
    const e7 = this.view.spatialReference;
    return this._updateDisplayPlaneConrers(t7), new v2({ spatialReference: e7, rings: [[this._cornerA, this._cornerB, this._cornerC, this._cornerD, this._cornerA]] });
  }
  get _rotateGraphicGeometry() {
    const t7 = this._plane;
    if (!t7) return null;
    const e7 = this._rotationHandleCache;
    return z(e7, t7.basis1), g(e7, e7, X * this.view.resolution), u2(e7, e7, t7.origin), u2(e7, e7, t7.basis1), new w({ x: e7[0], y: e7[1], spatialReference: this.view.spatialReference });
  }
  get _scaleGraphicGeometries() {
    const t7 = this._displayPlane;
    if (!t7) return [];
    const e7 = this.view.spatialReference;
    this._updateDisplayPlaneConrers(t7);
    const { _cornerA: i5, _cornerB: s4, _cornerC: o2, _cornerD: r6 } = this, a5 = A(this._avgAB, i5, s4, 0.5), n5 = A(this._avgBC, s4, o2, 0.5), h2 = A(this._avgCD, o2, r6, 0.5), c2 = A(this._avgDA, r6, i5, 0.5);
    return [new w({ x: i5[0], y: i5[1], spatialReference: e7 }), new w({ x: s4[0], y: s4[1], spatialReference: e7 }), new w({ x: o2[0], y: o2[1], spatialReference: e7 }), new w({ x: r6[0], y: r6[1], spatialReference: e7 }), new w({ x: a5[0], y: a5[1], spatialReference: e7 }), new w({ x: n5[0], y: n5[1], spatialReference: e7 }), new w({ x: h2[0], y: h2[1], spatialReference: e7 }), new w({ x: c2[0], y: c2[1], spatialReference: e7 })];
  }
  onActivate() {
    this.visible = true;
  }
  onDeactivate() {
    this.visible = false;
  }
  onShow() {
    this._graphicsLayer.visible = true;
  }
  onHide() {
    this._graphicsLayer.visible = false;
  }
  canUndo() {
    return this._editGeometryOperations.canUndo;
  }
  canRedo() {
    return this._editGeometryOperations.canRedo;
  }
  undo() {
    this._editGeometryOperations.undo(), this.updateGraphics();
  }
  redo() {
    this._editGeometryOperations.redo(), this.updateGraphics();
  }
  refresh() {
    const { view: t7, target: e7 } = this, i5 = "georeference" in e7 ? e2(e2(e7.georeference).coords) : e7.geometry, s4 = this._editGeometryOperations, o2 = s4.data.components[0].vertices, r6 = g3.fromGeometry(rn(i5, t7.spatialReference), l5.Local).components[0].vertices;
    o2.forEach((t8, e8) => {
      s4.setVertexPosition(t8, r6[e8].pos);
    }), this.updateGraphics();
  }
  reset() {
    const { target: t7 } = this;
    if ("georeference" in t7) {
      const e7 = e2(t7.georeference);
      "control-points" === e7.type && (e7.controlPoints = this._initialControlPoints);
    } else t7.geometry = this._initialGeometry;
    this.refresh(), this._sharedUndoStack.length = 0, this._sharedRedoStack.length = 0;
  }
  updateGraphics() {
    const t7 = this._editGeometryOperations.data.geometry;
    if ("georeference" in this.target) {
      e2(this.target.georeference).coords = t7;
    }
    this._graphic.geometry = t7, this._backgroundGraphic.geometry = this._backgroundGraphicGeometry, this._rotateGraphic.geometry = this._rotateGraphicGeometry, this._scaleGraphicGeometries.forEach((t8, e7) => {
      this._scaleGraphics[e7].geometry = t8;
    });
  }
  setSharedUndoStack(t7) {
    this._sharedUndoStack = t7;
  }
  setSharedRedoStack(t7) {
    this._sharedRedoStack = t7;
  }
  async _initialize() {
    const { view: t7, target: o2 } = this;
    if ("georeference" in o2) {
      const t8 = e2(o2.georeference);
      this._graphic = new g2({ geometry: e2(t8.coords) }), this._initialControlPoints = "control-points" === t8.type ? t8.controlPoints : null;
    } else this._graphic = o2, this._initialGeometry = e2(o2.geometry);
    t7.map.addMany([this._graphicsLayer]), t7.focus(), this.visible = false, this.finishToolCreation(), await this._loadProjectionEngine(), this._editGeometryOperations = V.fromGeometry(rn(this._graphic.geometry, t7.spatialReference), l5.Local), this._backgroundGraphic = new g2({ symbol: new S({ color: "transparent", outline: { type: "simple-line", color: i3(), width: 2 } }), geometry: this._backgroundGraphicGeometry }), this._rotateGraphic = new g2({ symbol: new y2({ color: u5(), outline: { type: "simple-line", color: i3(), width: 1 } }), geometry: this._rotateGraphicGeometry }), this._scaleGraphics = this._scaleGraphicGeometries.map((t8) => new g2({ symbol: new y2({ size: 6, style: "square", color: u5(), outline: { type: "simple-line", color: i3(), width: 1 } }), geometry: t8 })), this._graphicsLayer.graphics.addMany([this._backgroundGraphic, this._rotateGraphic, ...this._scaleGraphics]), this._dragManipulation = new c({ tool: this, view: t7, graphic: this._graphic }), this._rotateManipulation = new m2({ tool: this, view: t7, graphic: this._rotateGraphic, snapRotation: this._snapRotation }), this._scaleManipulations = this._scaleGraphics.map((e7, i5) => new w3({ tool: this, view: t7, graphic: e7, direction: Y[i5], preserveAspectRatio: this._preserveAspectRatio })), this.addHandles([this._dragManipulation.createDragPipeline(this._getInfo.bind(this), this._updateGraphics.bind(this)), this._rotateManipulation.createDragPipeline(this._getInfo.bind(this), this._updateGraphics.bind(this)), ...this._scaleManipulations.map((t8) => t8.createDragPipeline(this._getInfo.bind(this), this._updateGraphics.bind(this))), l2(() => this.view.scale, () => this.active ? this.updateGraphics() : null), t7.on("click", async (e7) => {
      if (null != t7.activeTool && t7.activeTool !== this) return;
      const i5 = n4(e7), s4 = [];
      t7.map.allLayers.forEach((t8) => {
        "vector-tile" !== t8.type && "imagery" !== t8.type || s4.push(t8);
      });
      const a5 = await this.view.hitTest(i5, { exclude: s4 }), n5 = a5.results;
      if (0 === n5.length) t7.activeTool = null;
      else {
        const e8 = i(a5.results), i6 = "georeference" in o2, s5 = n5.map((t8) => "media" === t8.type ? t8.element : null).filter(Boolean), h3 = [...this._graphicsLayer.graphics, i6 ? null : o2].filter(Boolean);
        i6 && s5.includes(o2) || r(e8) && h3.includes(e8.graphic) ? null == t7.activeTool && (t7.activeTool = this) : t7.activeTool = null;
      }
    })]);
    const h2 = (t8) => {
      this.addHandles(t8.events.on("grab-changed", (t9) => {
        "georeference" in o2 && ("start" === t9.action ? o2.opacity *= 0.5 : "end" === t9.action && (o2.opacity *= 2));
      }));
    };
    this._dragManipulation.forEachManipulator(h2), this._rotateManipulation.forEachManipulator(h2), this._scaleManipulations.forEach((t8) => t8.forEachManipulator(h2)), this.addHandles([t7.on("key-down", (e7) => {
      t7.activeTool === this && (e7.key !== F.shift || e7.repeat || (null == this.preserveAspectRatio && (this._preserveAspectRatio.enabled = !this._preserveAspectRatio.enabled), null == this.snapRotation && (this._snapRotation.enabled = !this._snapRotation.enabled), this._isModifierActive = true, e7.stopPropagation()), e7.key !== F.toggleOpacity || e7.repeat || ("georeference" in o2 && (o2.opacity *= this._isOpacityToggled ? 2 : 0.5, this._isOpacityToggled = !this._isOpacityToggled), e7.stopPropagation()), e7.key !== F.primaryKey || e7.repeat || (this._factor = $2, e7.stopPropagation()), this._isModifierActive && (e7.key === F.plus && (this._scale(this._factor), e7.stopPropagation()), e7.key === F.minus && (this._scale(-this._factor), e7.stopPropagation()), e7.key === F.up && (this._move(0, this._factor), e7.stopPropagation()), e7.key === F.down && (this._move(0, -this._factor), e7.stopPropagation()), e7.key === F.left && (this._move(-this._factor, 0), e7.stopPropagation()), e7.key === F.right && (this._move(this._factor, 0), e7.stopPropagation())));
    }), t7.on("key-up", (e7) => {
      t7.activeTool === this && (e7.key === F.shift && (null == this.preserveAspectRatio && (this._preserveAspectRatio.enabled = !this._preserveAspectRatio.enabled), null == this.snapRotation && (this._snapRotation.enabled = !this._snapRotation.enabled), this._isModifierActive = false, e7.stopPropagation()), e7.key === F.primaryKey && (this._factor = Z2, e7.stopPropagation()));
    })]);
  }
  async _loadProjectionEngine() {
    const t7 = e2(this._graphic.geometry);
    return _n(t7.spatialReference, this.view.spatialReference);
  }
  _updateDisplayPlaneConrers(t7) {
    const { basis1: e7, basis2: i5, origin: s4 } = t7, o2 = this._cornerA;
    u2(o2, s4, e7), u2(o2, o2, i5);
    const r6 = this._cornerB;
    u2(r6, s4, e7), e3(r6, r6, i5);
    const a5 = this._cornerC;
    e3(a5, s4, e7), e3(a5, a5, i5);
    const n5 = this._cornerD;
    e3(n5, s4, e7), u2(n5, n5, i5);
  }
  _getInfo() {
    return { editGeometryOperations: this._editGeometryOperations, plane: this._plane, displayPlane: this._displayPlane };
  }
  _updateGraphics(t7, e7) {
    "start" === t7.action && (this._sharedUndoStack.push({ tool: this, operation: e7 }), this._sharedRedoStack.length = 0), this.updateGraphics();
  }
  _scale(t7) {
    var _a;
    const e7 = this._editGeometryOperations, i5 = [];
    for (const a5 of e7.data.components) i5.push(...a5.vertices);
    const s4 = (_a = e7.data.geometry.extent) == null ? void 0 : _a.width, o2 = (s4 + t7 * this.view.resolution) / s4, r6 = e7.scaleVertices(i5, this._plane.origin, l4, o2, o2, E2.NEW_STEP, e5.REPLACE);
    this._sharedUndoStack.push({ tool: this, operation: r6 }), this._sharedRedoStack.length = 0, this.updateGraphics();
  }
  _move(t7, e7) {
    const i5 = this._editGeometryOperations, s4 = [];
    for (const r6 of i5.data.components) s4.push(...r6.vertices);
    const o2 = i5.moveVertices(s4, t7 * this.view.resolution, e7 * this.view.resolution, 0, E2.NEW_STEP);
    this._sharedUndoStack.push({ tool: this, operation: o2 }), this._sharedRedoStack.length = 0, this.updateGraphics();
  }
};
e([y()], tt.prototype, "_plane", null), e([y()], tt.prototype, "_backgroundGraphicGeometry", null), e([y()], tt.prototype, "_rotateGraphicGeometry", null), e([y()], tt.prototype, "_scaleGraphicGeometries", null), e([y()], tt.prototype, "preserveAspectRatio", void 0), e([y()], tt.prototype, "snapRotation", void 0), e([y({ constructOnly: true, nonNullable: true })], tt.prototype, "target", void 0), e([y({ constructOnly: true })], tt.prototype, "view", void 0), tt = e([a2("esri.views.2d.interactive.editingTools.TransformTool")], tt);

// node_modules/@arcgis/core/views/2d/interactive/editingTools/MediaTransformToolsWrapper.js
var d2 = { redo: "r", undo: "z" };
var l7 = class extends v {
  constructor(o2) {
    super(o2), this._transformTool = null, this._controlPointsTransformTool = null, this._advancedModeTransformTool = null, this._activeTool = null, this._sharedUndoStack = [], this._sharedRedoStack = [], this._originalOpacity = null, this.activeHandle = 0;
  }
  initialize() {
    const { view: o2, mediaElement: t7, preserveAspectRatio: i5, snapRotation: a5, advancedMode: l8 } = this;
    this._originalOpacity = t7.opacity, this._transformTool = new tt({ target: t7, view: o2, preserveAspectRatio: i5, snapRotation: a5 }), this._controlPointsTransformTool = new w2({ mediaElement: t7, view: o2 }), this._advancedModeTransformTool = new w2({ mediaElement: l8.mediaElement, view: l8.view }), this._transformTool.setSharedUndoStack(this._sharedUndoStack), this._transformTool.setSharedRedoStack(this._sharedRedoStack), this._controlPointsTransformTool.setSharedUndoStack(this._sharedUndoStack), this._controlPointsTransformTool.setSharedRedoStack(this._sharedRedoStack), this._advancedModeTransformTool.setSharedUndoStack(this._sharedUndoStack), this._advancedModeTransformTool.setSharedRedoStack(this._sharedRedoStack);
    const c2 = e2(t7.georeference), h2 = e2(l8.mediaElement.georeference);
    l8.view.tools.addMany([this._advancedModeTransformTool]), "controlPoints" in h2 && "controlPoints" in c2 && this.addHandles([l8.view.on("key-down", (o3) => {
      o3.key === d2.undo && this.canUndo() && (this.undo(), o3.stopPropagation()), o3.key === d2.redo && this.canRedo() && (this.redo(), o3.stopPropagation());
    }), l8.view.on("focus", async (o3) => {
      this._controlPointsTransformTool.removeHighlightActiveHandle(), this._advancedModeTransformTool.highlightActiveHandle();
    }), l2(() => h2.controlPoints, (o3) => {
      var _a;
      c2.controlPoints = e2(o3).map(({ sourcePoint: o4 }, t8) => ({ sourcePoint: o4, mapPoint: e2(c2.controlPoints)[t8].mapPoint })), (_a = this._activeTool) == null ? void 0 : _a.refresh();
    }), l2(() => this._controlPointsTransformTool.activeHandle, (o3) => {
      this._advancedModeTransformTool.updateActiveHandle(o3), this.activeHandle = o3;
    }), l2(() => this._advancedModeTransformTool.activeHandle, (o3) => {
      this._controlPointsTransformTool.updateActiveHandle(o3), this.activeHandle = o3;
    })]), this.addHandles([o2.on("key-down", (o3) => {
      o3.key === d2.undo && this.canUndo() && (this.undo(), o3.stopPropagation()), o3.key === d2.redo && this.canRedo() && (this.redo(), o3.stopPropagation());
    }), o2.on("focus", async (o3) => {
      this._advancedModeTransformTool.removeHighlightActiveHandle(), this._controlPointsTransformTool.highlightActiveHandle();
    })]), o2.tools.addMany([this._transformTool, this._controlPointsTransformTool]), o2.activeTool = this._transformTool, this._activeTool = this._transformTool, o2.focus();
  }
  destroy() {
    var _a, _b;
    (_a = this._transformTool) == null ? void 0 : _a.destroy(), (_b = this._controlPointsTransformTool) == null ? void 0 : _b.destroy(), this._transformTool = null, this._controlPointsTransformTool = null, this._advancedModeTransformTool = null, this._activeTool = null, this._sharedUndoStack = null, this._sharedRedoStack = null;
  }
  canUndo() {
    return this._sharedUndoStack.length > 0;
  }
  canRedo() {
    return this._sharedRedoStack.length > 0;
  }
  undo() {
    var _a;
    if (this._sharedUndoStack.length > 0) {
      const { tool: o2, operation: t7 } = this._sharedUndoStack.pop();
      o2 !== this._activeTool && o2.refresh(), t7.undo(), o2.updateGraphics(), this._sharedRedoStack.push({ tool: o2, operation: t7 }), this._activeTool !== o2 && ((_a = this._activeTool) == null ? void 0 : _a.refresh());
    }
  }
  redo() {
    var _a;
    if (this._sharedRedoStack.length > 0) {
      const { tool: o2, operation: t7 } = this._sharedRedoStack.pop();
      o2 !== this._activeTool && o2.refresh(), t7.apply(), o2.updateGraphics(), this._sharedUndoStack.push({ tool: o2, operation: t7 }), this._activeTool !== o2 && ((_a = this._activeTool) == null ? void 0 : _a.refresh());
    }
  }
  refresh() {
    this._activeTool.refresh();
  }
  reset() {
    this._activeTool.reset(), this._advancedModeTransformTool.reset();
  }
  async enableAdvancedMode() {
    this.view.activeTool = this._controlPointsTransformTool, this._activeTool = this._controlPointsTransformTool, this._activeTool.refresh(), await this.advancedMode.view.when(), this.advancedMode.view.activeTool = this._advancedModeTransformTool, this._originalOpacity = this._controlPointsTransformTool.mediaElement.opacity, this._controlPointsTransformTool.mediaElement.opacity = 0.25 * this._originalOpacity;
  }
  disableAdvancedMode() {
    this.view.activeTool = this._transformTool, this._activeTool = this._transformTool, this._activeTool.refresh(), this.advancedMode.view.activeTool = null, this._controlPointsTransformTool.mediaElement.opacity = this._originalOpacity;
  }
};
e([y()], l7.prototype, "activeHandle", void 0), e([y({ constructOnly: true })], l7.prototype, "advancedMode", void 0), e([y()], l7.prototype, "preserveAspectRatio", void 0), e([y()], l7.prototype, "snapRotation", void 0), e([y({ constructOnly: true, nonNullable: true })], l7.prototype, "mediaElement", void 0), e([y({ constructOnly: true })], l7.prototype, "view", void 0), l7 = e([a2("esri.views.2d.interactive.editingTools.MediaTransformToolsWrapper")], l7);
export {
  w2 as ControlPointsTransformTool,
  E3 as DrawGraphicTool2D,
  l7 as MediaTransformToolsWrapper,
  tt as TransformTool
};
//# sourceMappingURL=editingTools-WJ7H2UWX.js.map
