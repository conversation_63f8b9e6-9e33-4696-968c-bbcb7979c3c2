import {
  G2 as G,
  W
} from "./chunk-JXLVNWKF.js";
import {
  r
} from "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  t2 as t,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  v
} from "./chunk-GZGAQUSK.js";

// node_modules/@arcgis/core/geometry/HeightModelInfo.js
var d;
var p = o2()({ orthometric: "gravity-related-height", gravity_related_height: "gravity-related-height", ellipsoidal: "ellipsoidal" });
var u = p.jsonValues.slice();
v(u, "orthometric");
var g = o2()({ meter: "meters", foot: "feet", "us-foot": "us-feet", "clarke-foot": "clarke-feet", "clarke-yard": "clarke-yards", "clarke-link": "clarke-links", "sears-yard": "sears-yards", "sears-foot": "sears-feet", "sears-chain": "sears-chains", "benoit-1895-b-chain": "benoit-1895-b-chains", "indian-yard": "indian-yards", "indian-1937-yard": "indian-1937-yards", "gold-coast-foot": "gold-coast-feet", "sears-1922-truncated-chain": "sears-1922-truncated-chains", "50-kilometers": "50-kilometers", "150-kilometers": "150-kilometers" });
var m = d = class extends l {
  constructor(e2) {
    super(e2), this.heightModel = "gravity-related-height", this.heightUnit = "meters", this.vertCRS = null;
  }
  writeHeightModel(e2, t2, r2) {
    return p.write(e2, t2, r2);
  }
  readHeightModel(e2, t2, r2) {
    const o3 = p.read(e2);
    return o3 || (r2 && r2.messages && r2.messages.push(f(e2, { context: r2 })), null);
  }
  readHeightUnit(e2, t2, r2) {
    const o3 = g.read(e2);
    return o3 || (r2 && r2.messages && r2.messages.push(y2(e2, { context: r2 })), null);
  }
  readHeightUnitService(e2, t2, r2) {
    const o3 = G(e2) || g.read(e2);
    return o3 || (r2 && r2.messages && r2.messages.push(y2(e2, { context: r2 })), null);
  }
  readVertCRS(e2, t2) {
    return t2.vertCRS || t2.ellipsoid || t2.geoid;
  }
  clone() {
    return new d({ heightModel: this.heightModel, heightUnit: this.heightUnit, vertCRS: this.vertCRS });
  }
  equals(e2) {
    return !!e2 && (this === e2 || this.heightModel === e2.heightModel && this.heightUnit === e2.heightUnit && this.vertCRS === e2.vertCRS);
  }
  static deriveUnitFromSR(e2, t2) {
    const r2 = W(t2);
    return new d({ heightModel: e2.heightModel, heightUnit: r2, vertCRS: e2.vertCRS });
  }
  write(e2, t2) {
    return t2 = { origin: "web-scene", ...t2 }, super.write(e2, t2);
  }
  static fromJSON(e2) {
    if (!e2) return null;
    const t2 = new d();
    return t2.read(e2, { origin: "web-scene" }), t2;
  }
};
function y2(e2, t2) {
  return new t("height-unit:unsupported", `Height unit of value '${e2}' is not supported`, t2);
}
function f(e2, t2) {
  return new t("height-model:unsupported", `Height model of value '${e2}' is not supported`, t2);
}
e([y({ type: p.apiValues, constructOnly: true, json: { origins: { "web-scene": { type: u, default: "ellipsoidal" } } } })], m.prototype, "heightModel", void 0), e([r("web-scene", "heightModel")], m.prototype, "writeHeightModel", null), e([o(["web-scene", "service"], "heightModel")], m.prototype, "readHeightModel", null), e([y({ type: g.apiValues, constructOnly: true, json: { origins: { "web-scene": { type: g.jsonValues, write: g.write } } } })], m.prototype, "heightUnit", void 0), e([o("web-scene", "heightUnit")], m.prototype, "readHeightUnit", null), e([o("service", "heightUnit")], m.prototype, "readHeightUnitService", null), e([y({ type: String, constructOnly: true, json: { origins: { "web-scene": { write: true } } } })], m.prototype, "vertCRS", void 0), e([o("service", "vertCRS", ["vertCRS", "ellipsoid", "geoid"])], m.prototype, "readVertCRS", null), m = d = e([a("esri.geometry.HeightModelInfo")], m);
var v2 = m;

export {
  v2 as v
};
//# sourceMappingURL=chunk-ZJC3GHA7.js.map
