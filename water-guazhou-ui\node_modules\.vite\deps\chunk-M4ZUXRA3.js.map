{"version": 3, "sources": ["../../@arcgis/core/statistics/utils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport n from\"../rest/support/ClassBreaksDefinition.js\";import{createGenerateRendererClassBreaks as e}from\"../rest/support/generateRendererUtils.js\";const t=\"<Null>\",l=\"equal-interval\",i=1,a=5,r=10,o=/\\s*(\\+|-)?((\\d+(\\.\\d+)?)|(\\.\\d+))\\s*/gi,u=new Set([\"esriFieldTypeInteger\",\"esriFieldTypeSmallInteger\",\"esriFieldTypeSingle\",\"esriFieldTypeDouble\"]),s=[\"min\",\"max\",\"avg\",\"stddev\",\"count\",\"sum\",\"variance\",\"nullcount\",\"median\"];function c(n){return null==n||\"string\"==typeof n&&!n?t:n}function m(n){const e=null!=n.normalizationField||null!=n.normalizationType,t=null!=n.minValue||null!=n.maxValue,l=!!n.sqlExpression&&n.supportsSQLExpression;return!e&&!t&&!l}function f(n){const e=n.returnDistinct?[...new Set(n.values)]:n.values,t=e.filter((n=>null!=n)).length,l={count:t};return n.supportsNullCount&&(l.nullcount=e.length-t),n.percentileParams&&(l.median=p(e,n.percentileParams)),l}function d(n){const{values:e,useSampleStdDev:t,supportsNullCount:l}=n;let i=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY,r=null,o=null,u=null,s=null,c=0;const m=null==n.minValue?-1/0:n.minValue,f=null==n.maxValue?1/0:n.maxValue;for(const p of e)Number.isFinite(p)?p>=m&&p<=f&&(r=null===r?p:r+p,i=Math.min(i,p),a=Math.max(a,p),c++):\"string\"==typeof p&&c++;if(c&&null!=r){o=r/c;let n=0;for(const t of e)Number.isFinite(t)&&t>=m&&t<=f&&(n+=(t-o)**2);s=t?c>1?n/(c-1):0:c>0?n/c:0,u=Math.sqrt(s)}else i=null,a=null;const d={avg:o,count:c,max:a,min:i,stddev:u,sum:r,variance:s};return l&&(d.nullcount=e.length-c),n.percentileParams&&(d.median=p(e,n.percentileParams)),d}function p(n,e){const{fieldType:t,value:l,orderBy:i,isDiscrete:a}=e,r=v(t,\"desc\"===i);if(0===(n=[...n].filter((n=>null!=n)).sort(((n,e)=>r(n,e)))).length)return null;if(l<=0)return n[0];if(l>=1)return n[n.length-1];const o=(n.length-1)*l,u=Math.floor(o),s=u+1,c=o%1,m=n[u],f=n[s];return s>=n.length||a||\"string\"==typeof m||\"string\"==typeof f?m:m*(1-c)+f*c}function v(n,e){const t=e?1:-1,l=g(e),i=h(e);if(!(!!n&&[\"esriFieldTypeDate\",\"esriFieldTypeString\",\"esriFieldTypeGUID\",\"esriFieldTypeGlobalID\",...u].includes(n)))return(n,e)=>\"number\"==typeof n&&\"number\"==typeof e?l(n,e):\"string\"==typeof n&&\"string\"==typeof e?i(n,e):t;if(\"esriFieldTypeDate\"===n)return(n,e)=>{const i=new Date(n).getTime(),a=new Date(e).getTime();return isNaN(i)||isNaN(a)?t:l(i,a)};if(u.has(n))return(n,e)=>l(n,e);if(\"esriFieldTypeString\"===n)return(n,e)=>i(n,e);if(\"esriFieldTypeGUID\"===n||\"esriFieldTypeGlobalID\"===n){const n=h(e);return(e,t)=>n(V(e),V(t))}return e?(n,e)=>1:(n,e)=>-1}function b(n,e,t){if(t){if(null==n)return null==e?0:1;if(null==e)return-1}else{if(null==n)return null==e?0:-1;if(null==e)return 1}return null}function h(n){return n?(n,e)=>{const t=b(n,e,!0);return null!=t?t:(n=n.toUpperCase())>(e=e.toUpperCase())?-1:n<e?1:0}:(n,e)=>{const t=b(n,e,!1);return null!=t?t:(n=n.toUpperCase())<(e=e.toUpperCase())?-1:n>e?1:0}}function g(n){return n?(n,e)=>{const t=b(n,e,!0);return null!=t?t:e-n}:(n,e)=>{const t=b(n,e,!1);return null!=t?t:n-e}}function V(n){return n.substr(24,12)+n.substr(19,4)+n.substr(16,2)+n.substr(14,2)+n.substr(11,2)+n.substr(9,2)+n.substr(6,2)+n.substr(4,2)+n.substr(2,2)+n.substr(0,2)}function T(n,e){let t;for(t in n)s.includes(t)&&(Number.isFinite(n[t])||(n[t]=null));return e?([\"avg\",\"stddev\",\"variance\"].forEach((e=>{null!=n[e]&&(n[e]=Math.ceil(n[e]))})),n):n}function y(n){const e={};for(let t of n)(null==t||\"string\"==typeof t&&\"\"===t.trim())&&(t=null),null==e[t]?e[t]={count:1,data:t}:e[t].count++;return{count:e}}function F(n){return\"coded-value\"!==n?.type?[]:n.codedValues.map((n=>n.code))}function x(n,e,t,l){const i=n.count,a=[];if(t&&e){const n=[],t=F(e[0]);for(const i of t)if(e[1]){const t=F(e[1]);for(const a of t)if(e[2]){const t=F(e[2]);for(const e of t)n.push(`${c(i)}${l}${c(a)}${l}${c(e)}`)}else n.push(`${c(i)}${l}${c(a)}`)}else n.push(i);for(const e of n)i.hasOwnProperty(e)||(i[e]={data:e,count:0})}for(const r in i){const n=i[r];a.push({value:n.data,count:n.count,label:n.label})}return{uniqueValueInfos:a}}function D(n,e,t,l){let i=null;switch(e){case\"log\":0!==n&&(i=Math.log(n)*Math.LOG10E);break;case\"percent-of-total\":Number.isFinite(l)&&0!==l&&(i=n/l*100);break;case\"field\":Number.isFinite(t)&&0!==t&&(i=n/t);break;case\"natural-log\":n>0&&(i=Math.log(n));break;case\"square-root\":n>0&&(i=n**.5)}return i}function z(n,t){const l=N({field:t.field,normalizationType:t.normalizationType,normalizationField:t.normalizationField,classificationMethod:t.classificationMethod,standardDeviationInterval:t.standardDeviationInterval,breakCount:t.numClasses||a});return n=I(n,t.minValue,t.maxValue),e({definition:l,values:n,normalizationTotal:t.normalizationTotal})}function I(n,e,t){const l=e??-1/0,i=t??1/0;return n.filter((n=>Number.isFinite(n)&&n>=l&&n<=i))}function N(e){const{breakCount:t,field:a,normalizationField:r,normalizationType:o}=e,u=e.classificationMethod||l,s=\"standard-deviation\"===u?e.standardDeviationInterval||i:void 0;return new n({breakCount:t,classificationField:a,classificationMethod:u,normalizationField:\"field\"===o?r:void 0,normalizationType:o,standardDeviationInterval:s})}function S(n,e){let t=n.classBreaks;const l=t.length,i=t[0].minValue,a=t[l-1].maxValue,r=\"standard-deviation\"===e,u=o;return t=t.map((n=>{const e=n.label,t={minValue:n.minValue,maxValue:n.maxValue,label:e};if(r&&e){const n=e.match(u)?.map((n=>+n.trim()))??[];2===n.length?(t.minStdDev=n[0],t.maxStdDev=n[1],n[0]<0&&n[1]>0&&(t.hasAvg=!0)):1===n.length&&(e.includes(\"<\")?(t.minStdDev=null,t.maxStdDev=n[0]):e.includes(\">\")&&(t.minStdDev=n[0],t.maxStdDev=null))}return t})),{minValue:i,maxValue:a,classBreakInfos:t,normalizationTotal:n.normalizationTotal}}function M(n,e){const t=C(n,e),l=t.intervals,i=t.min??0,a=t.max??0,r=l.map(((n,e)=>({minValue:l[e][0],maxValue:l[e][1],count:0})));for(const o of n)if(null!=o&&o>=i&&o<=a){const n=k(l,o);n>-1&&r[n].count++}return{bins:r,minValue:i,maxValue:a,normalizationTotal:e.normalizationTotal}}function C(n,e){const{field:t,classificationMethod:l,standardDeviationInterval:i,normalizationType:a,normalizationField:o,normalizationTotal:u,minValue:s,maxValue:c}=e,f=e.numBins||r;let p=null,v=null,b=null;if((!l||\"equal-interval\"===l)&&!a){if(null!=s&&null!=c)p=s,v=c;else{const e=d({values:n,minValue:s,maxValue:c,useSampleStdDev:!a,supportsNullCount:m({normalizationType:a,normalizationField:o,minValue:s,maxValue:c})});p=e.min??null,v=e.max??null}b=$(p??0,v??0,f)}else{const{classBreaks:e}=z(n,{field:t,normalizationType:a,normalizationField:o,normalizationTotal:u,classificationMethod:l,standardDeviationInterval:i,minValue:s,maxValue:c,numClasses:f});p=e[0].minValue,v=e[e.length-1].maxValue,b=e.map((n=>[n.minValue,n.maxValue]))}return{min:p,max:v,intervals:b}}function k(n,e){let t=-1;for(let l=n.length-1;l>=0;l--){if(e>=n[l][0]){t=l;break}}return t}function $(n,e,t){const l=(e-n)/t,i=[];let a,r=n;for(let o=1;o<=t;o++)a=r+l,a=Number(a.toFixed(16)),i.push([r,o===t?e:a]),r=a;return i}export{z as calculateClassBreaks,M as calculateHistogram,p as calculatePercentile,d as calculateStatistics,f as calculateStringStatistics,y as calculateUniqueValuesCount,N as createClassBreaksDefinition,x as createUVResult,v as getAttributeComparator,$ as getEqualIntervalBins,D as getNormalizedValue,m as isNullCountSupported,c as processNullValue,T as processSummaryStatisticsResult,S as resolveCBResult,s as statisticTypes};\n"], "mappings": ";;;;;;AAIqJ,IAAM,IAAE;AAAR,IAAiB,IAAE;AAAnB,IAAoC,IAAE;AAAtC,IAAwCA,KAAE;AAA1C,IAA4C,IAAE;AAA9C,IAAiD,IAAE;AAAnD,IAA4F,IAAE,oBAAI,IAAI,CAAC,wBAAuB,6BAA4B,uBAAsB,qBAAqB,CAAC;AAAtM,IAAwM,IAAE,CAAC,OAAM,OAAM,OAAM,UAAS,SAAQ,OAAM,YAAW,aAAY,QAAQ;AAAE,SAAS,EAAE,GAAE;AAAC,SAAO,QAAM,KAAG,YAAU,OAAO,KAAG,CAAC,IAAE,IAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,QAAM,EAAE,sBAAoB,QAAM,EAAE,mBAAkBC,KAAE,QAAM,EAAE,YAAU,QAAM,EAAE,UAASC,KAAE,CAAC,CAAC,EAAE,iBAAe,EAAE;AAAsB,SAAM,CAAC,KAAG,CAACD,MAAG,CAACC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,EAAE,iBAAe,CAAC,GAAG,IAAI,IAAI,EAAE,MAAM,CAAC,IAAE,EAAE,QAAOD,KAAE,EAAE,OAAQ,CAAAE,OAAG,QAAMA,EAAE,EAAE,QAAOD,KAAE,EAAC,OAAMD,GAAC;AAAE,SAAO,EAAE,sBAAoBC,GAAE,YAAU,EAAE,SAAOD,KAAG,EAAE,qBAAmBC,GAAE,SAAO,EAAE,GAAE,EAAE,gBAAgB,IAAGA;AAAC;AAAC,SAASE,GAAE,GAAE;AAAC,QAAK,EAAC,QAAO,GAAE,iBAAgBH,IAAE,mBAAkBC,GAAC,IAAE;AAAE,MAAIG,KAAE,OAAO,mBAAkBL,KAAE,OAAO,mBAAkBM,KAAE,MAAKC,KAAE,MAAKC,KAAE,MAAKC,KAAE,MAAKC,KAAE;AAAE,QAAMC,KAAE,QAAM,EAAE,WAAS,KAAG,IAAE,EAAE,UAASC,KAAE,QAAM,EAAE,WAAS,IAAE,IAAE,EAAE;AAAS,aAAUC,MAAK,EAAE,QAAO,SAASA,EAAC,IAAEA,MAAGF,MAAGE,MAAGD,OAAIN,KAAE,SAAOA,KAAEO,KAAEP,KAAEO,IAAER,KAAE,KAAK,IAAIA,IAAEQ,EAAC,GAAEb,KAAE,KAAK,IAAIA,IAAEa,EAAC,GAAEH,QAAK,YAAU,OAAOG,MAAGH;AAAI,MAAGA,MAAG,QAAMJ,IAAE;AAAC,IAAAC,KAAED,KAAEI;AAAE,QAAIP,KAAE;AAAE,eAAUF,MAAK,EAAE,QAAO,SAASA,EAAC,KAAGA,MAAGU,MAAGV,MAAGW,OAAIT,OAAIF,KAAEM,OAAI;AAAG,IAAAE,KAAER,KAAES,KAAE,IAAEP,MAAGO,KAAE,KAAG,IAAEA,KAAE,IAAEP,KAAEO,KAAE,GAAEF,KAAE,KAAK,KAAKC,EAAC;AAAA,EAAC,MAAM,CAAAJ,KAAE,MAAKL,KAAE;AAAK,QAAMI,KAAE,EAAC,KAAIG,IAAE,OAAMG,IAAE,KAAIV,IAAE,KAAIK,IAAE,QAAOG,IAAE,KAAIF,IAAE,UAASG,GAAC;AAAE,SAAOP,OAAIE,GAAE,YAAU,EAAE,SAAOM,KAAG,EAAE,qBAAmBN,GAAE,SAAO,EAAE,GAAE,EAAE,gBAAgB,IAAGA;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAK,EAAC,WAAUH,IAAE,OAAMC,IAAE,SAAQG,IAAE,YAAWL,GAAC,IAAE,GAAEM,KAAE,EAAEL,IAAE,WAASI,EAAC;AAAE,MAAG,OAAK,IAAE,CAAC,GAAG,CAAC,EAAE,OAAQ,CAAAF,OAAG,QAAMA,EAAE,EAAE,KAAM,CAACA,IAAEW,OAAIR,GAAEH,IAAEW,EAAC,CAAE,GAAG,OAAO,QAAO;AAAK,MAAGZ,MAAG,EAAE,QAAO,EAAE,CAAC;AAAE,MAAGA,MAAG,EAAE,QAAO,EAAE,EAAE,SAAO,CAAC;AAAE,QAAMK,MAAG,EAAE,SAAO,KAAGL,IAAEM,KAAE,KAAK,MAAMD,EAAC,GAAEE,KAAED,KAAE,GAAEE,KAAEH,KAAE,GAAEI,KAAE,EAAEH,EAAC,GAAEI,KAAE,EAAEH,EAAC;AAAE,SAAOA,MAAG,EAAE,UAAQT,MAAG,YAAU,OAAOW,MAAG,YAAU,OAAOC,KAAED,KAAEA,MAAG,IAAED,MAAGE,KAAEF;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAMT,KAAE,IAAE,IAAE,IAAGC,KAAE,EAAE,CAAC,GAAEG,KAAE,EAAE,CAAC;AAAE,MAAG,EAAE,CAAC,CAAC,KAAG,CAAC,qBAAoB,uBAAsB,qBAAoB,yBAAwB,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,QAAM,CAACF,IAAEW,OAAI,YAAU,OAAOX,MAAG,YAAU,OAAOW,KAAEZ,GAAEC,IAAEW,EAAC,IAAE,YAAU,OAAOX,MAAG,YAAU,OAAOW,KAAET,GAAEF,IAAEW,EAAC,IAAEb;AAAE,MAAG,wBAAsB,EAAE,QAAM,CAACE,IAAEW,OAAI;AAAC,UAAMT,KAAE,IAAI,KAAKF,EAAC,EAAE,QAAQ,GAAEH,KAAE,IAAI,KAAKc,EAAC,EAAE,QAAQ;AAAE,WAAO,MAAMT,EAAC,KAAG,MAAML,EAAC,IAAEC,KAAEC,GAAEG,IAAEL,EAAC;AAAA,EAAC;AAAE,MAAG,EAAE,IAAI,CAAC,EAAE,QAAM,CAACG,IAAEW,OAAIZ,GAAEC,IAAEW,EAAC;AAAE,MAAG,0BAAwB,EAAE,QAAM,CAACX,IAAEW,OAAIT,GAAEF,IAAEW,EAAC;AAAE,MAAG,wBAAsB,KAAG,4BAA0B,GAAE;AAAC,UAAMX,KAAE,EAAE,CAAC;AAAE,WAAM,CAACW,IAAEb,OAAIE,GAAE,EAAEW,EAAC,GAAE,EAAEb,EAAC,CAAC;AAAA,EAAC;AAAC,SAAO,IAAE,CAACE,IAAEW,OAAI,IAAE,CAACX,IAAEW,OAAI;AAAE;AAAC,SAAS,EAAE,GAAE,GAAEb,IAAE;AAAC,MAAGA,IAAE;AAAC,QAAG,QAAM,EAAE,QAAO,QAAM,IAAE,IAAE;AAAE,QAAG,QAAM,EAAE,QAAM;AAAA,EAAE,OAAK;AAAC,QAAG,QAAM,EAAE,QAAO,QAAM,IAAE,IAAE;AAAG,QAAG,QAAM,EAAE,QAAO;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,CAACE,IAAE,MAAI;AAAC,UAAMF,KAAE,EAAEE,IAAE,GAAE,IAAE;AAAE,WAAO,QAAMF,KAAEA,MAAGE,KAAEA,GAAE,YAAY,MAAI,IAAE,EAAE,YAAY,KAAG,KAAGA,KAAE,IAAE,IAAE;AAAA,EAAC,IAAE,CAACA,IAAE,MAAI;AAAC,UAAMF,KAAE,EAAEE,IAAE,GAAE,KAAE;AAAE,WAAO,QAAMF,KAAEA,MAAGE,KAAEA,GAAE,YAAY,MAAI,IAAE,EAAE,YAAY,KAAG,KAAGA,KAAE,IAAE,IAAE;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,IAAE,CAACA,IAAE,MAAI;AAAC,UAAMF,KAAE,EAAEE,IAAE,GAAE,IAAE;AAAE,WAAO,QAAMF,KAAEA,KAAE,IAAEE;AAAA,EAAC,IAAE,CAACA,IAAE,MAAI;AAAC,UAAMF,KAAE,EAAEE,IAAE,GAAE,KAAE;AAAE,WAAO,QAAMF,KAAEA,KAAEE,KAAE;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,OAAO,IAAG,EAAE,IAAE,EAAE,OAAO,IAAG,CAAC,IAAE,EAAE,OAAO,IAAG,CAAC,IAAE,EAAE,OAAO,IAAG,CAAC,IAAE,EAAE,OAAO,IAAG,CAAC,IAAE,EAAE,OAAO,GAAE,CAAC,IAAE,EAAE,OAAO,GAAE,CAAC,IAAE,EAAE,OAAO,GAAE,CAAC,IAAE,EAAE,OAAO,GAAE,CAAC,IAAE,EAAE,OAAO,GAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAIF;AAAE,OAAIA,MAAK,EAAE,GAAE,SAASA,EAAC,MAAI,OAAO,SAAS,EAAEA,EAAC,CAAC,MAAI,EAAEA,EAAC,IAAE;AAAO,SAAO,KAAG,CAAC,OAAM,UAAS,UAAU,EAAE,QAAS,CAAAa,OAAG;AAAC,YAAM,EAAEA,EAAC,MAAI,EAAEA,EAAC,IAAE,KAAK,KAAK,EAAEA,EAAC,CAAC;AAAA,EAAE,CAAE,GAAE,KAAG;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAM,IAAE,CAAC;AAAE,WAAQb,MAAK,EAAE,EAAC,QAAMA,MAAG,YAAU,OAAOA,MAAG,OAAKA,GAAE,KAAK,OAAKA,KAAE,OAAM,QAAM,EAAEA,EAAC,IAAE,EAAEA,EAAC,IAAE,EAAC,OAAM,GAAE,MAAKA,GAAC,IAAE,EAAEA,EAAC,EAAE;AAAQ,SAAM,EAAC,OAAM,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,mBAAgB,uBAAG,QAAK,CAAC,IAAE,EAAE,YAAY,IAAK,CAAAE,OAAGA,GAAE,IAAK;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEF,IAAEC,IAAE;AAAC,QAAMG,KAAE,EAAE,OAAML,KAAE,CAAC;AAAE,MAAGC,MAAG,GAAE;AAAC,UAAME,KAAE,CAAC,GAAEF,KAAE,EAAE,EAAE,CAAC,CAAC;AAAE,eAAUI,MAAKJ,GAAE,KAAG,EAAE,CAAC,GAAE;AAAC,YAAMA,KAAE,EAAE,EAAE,CAAC,CAAC;AAAE,iBAAUD,MAAKC,GAAE,KAAG,EAAE,CAAC,GAAE;AAAC,cAAMA,KAAE,EAAE,EAAE,CAAC,CAAC;AAAE,mBAAUa,MAAKb,GAAE,CAAAE,GAAE,KAAK,GAAG,EAAEE,EAAC,CAAC,GAAGH,EAAC,GAAG,EAAEF,EAAC,CAAC,GAAGE,EAAC,GAAG,EAAEY,EAAC,CAAC,EAAE;AAAA,MAAC,MAAM,CAAAX,GAAE,KAAK,GAAG,EAAEE,EAAC,CAAC,GAAGH,EAAC,GAAG,EAAEF,EAAC,CAAC,EAAE;AAAA,IAAC,MAAM,CAAAG,GAAE,KAAKE,EAAC;AAAE,eAAUS,MAAKX,GAAE,CAAAE,GAAE,eAAeS,EAAC,MAAIT,GAAES,EAAC,IAAE,EAAC,MAAKA,IAAE,OAAM,EAAC;AAAA,EAAE;AAAC,aAAUR,MAAKD,IAAE;AAAC,UAAMF,KAAEE,GAAEC,EAAC;AAAE,IAAAN,GAAE,KAAK,EAAC,OAAMG,GAAE,MAAK,OAAMA,GAAE,OAAM,OAAMA,GAAE,MAAK,CAAC;AAAA,EAAC;AAAC,SAAM,EAAC,kBAAiBH,GAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEC,IAAEC,IAAE;AAAC,MAAIG,KAAE;AAAK,UAAO,GAAE;AAAA,IAAC,KAAI;AAAM,YAAI,MAAIA,KAAE,KAAK,IAAI,CAAC,IAAE,KAAK;AAAQ;AAAA,IAAM,KAAI;AAAmB,aAAO,SAASH,EAAC,KAAG,MAAIA,OAAIG,KAAE,IAAEH,KAAE;AAAK;AAAA,IAAM,KAAI;AAAQ,aAAO,SAASD,EAAC,KAAG,MAAIA,OAAII,KAAE,IAAEJ;AAAG;AAAA,IAAM,KAAI;AAAc,UAAE,MAAII,KAAE,KAAK,IAAI,CAAC;AAAG;AAAA,IAAM,KAAI;AAAc,UAAE,MAAIA,KAAE,KAAG;AAAA,EAAG;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAE,GAAEJ,IAAE;AAAC,QAAMC,KAAE,EAAE,EAAC,OAAMD,GAAE,OAAM,mBAAkBA,GAAE,mBAAkB,oBAAmBA,GAAE,oBAAmB,sBAAqBA,GAAE,sBAAqB,2BAA0BA,GAAE,2BAA0B,YAAWA,GAAE,cAAYD,GAAC,CAAC;AAAE,SAAO,IAAE,EAAE,GAAEC,GAAE,UAASA,GAAE,QAAQ,GAAE,EAAE,EAAC,YAAWC,IAAE,QAAO,GAAE,oBAAmBD,GAAE,mBAAkB,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,KAAG,KAAG,GAAEG,KAAEJ,MAAG,IAAE;AAAE,SAAO,EAAE,OAAQ,CAAAE,OAAG,OAAO,SAASA,EAAC,KAAGA,MAAGD,MAAGC,MAAGE,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,YAAWJ,IAAE,OAAMD,IAAE,oBAAmBM,IAAE,mBAAkBC,GAAC,IAAE,GAAEC,KAAE,EAAE,wBAAsB,GAAEC,KAAE,yBAAuBD,KAAE,EAAE,6BAA2B,IAAE;AAAO,SAAO,IAAI,EAAE,EAAC,YAAWP,IAAE,qBAAoBD,IAAE,sBAAqBQ,IAAE,oBAAmB,YAAUD,KAAED,KAAE,QAAO,mBAAkBC,IAAE,2BAA0BE,GAAC,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAIR,KAAE,EAAE;AAAY,QAAMC,KAAED,GAAE,QAAOI,KAAEJ,GAAE,CAAC,EAAE,UAASD,KAAEC,GAAEC,KAAE,CAAC,EAAE,UAASI,KAAE,yBAAuB,GAAEE,KAAE;AAAE,SAAOP,KAAEA,GAAE,IAAK,CAAAE,OAAG;AAJtmK;AAIumK,UAAMW,KAAEX,GAAE,OAAMF,KAAE,EAAC,UAASE,GAAE,UAAS,UAASA,GAAE,UAAS,OAAMW,GAAC;AAAE,QAAGR,MAAGQ,IAAE;AAAC,YAAMX,OAAE,KAAAW,GAAE,MAAMN,EAAC,MAAT,mBAAY,IAAK,CAAAL,OAAG,CAACA,GAAE,KAAK,OAAK,CAAC;AAAE,YAAIA,GAAE,UAAQF,GAAE,YAAUE,GAAE,CAAC,GAAEF,GAAE,YAAUE,GAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAGA,GAAE,CAAC,IAAE,MAAIF,GAAE,SAAO,SAAK,MAAIE,GAAE,WAASW,GAAE,SAAS,GAAG,KAAGb,GAAE,YAAU,MAAKA,GAAE,YAAUE,GAAE,CAAC,KAAGW,GAAE,SAAS,GAAG,MAAIb,GAAE,YAAUE,GAAE,CAAC,GAAEF,GAAE,YAAU;AAAA,IAAM;AAAC,WAAOA;AAAA,EAAC,CAAE,GAAE,EAAC,UAASI,IAAE,UAASL,IAAE,iBAAgBC,IAAE,oBAAmB,EAAE,mBAAkB;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAMA,KAAE,EAAE,GAAE,CAAC,GAAEC,KAAED,GAAE,WAAUI,KAAEJ,GAAE,OAAK,GAAED,KAAEC,GAAE,OAAK,GAAEK,KAAEJ,GAAE,IAAK,CAACC,IAAEW,QAAK,EAAC,UAASZ,GAAEY,EAAC,EAAE,CAAC,GAAE,UAASZ,GAAEY,EAAC,EAAE,CAAC,GAAE,OAAM,EAAC,EAAG;AAAE,aAAUP,MAAK,EAAE,KAAG,QAAMA,MAAGA,MAAGF,MAAGE,MAAGP,IAAE;AAAC,UAAMG,KAAE,EAAED,IAAEK,EAAC;AAAE,IAAAJ,KAAE,MAAIG,GAAEH,EAAC,EAAE;AAAA,EAAO;AAAC,SAAM,EAAC,MAAKG,IAAE,UAASD,IAAE,UAASL,IAAE,oBAAmB,EAAE,mBAAkB;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,QAAK,EAAC,OAAMC,IAAE,sBAAqBC,IAAE,2BAA0BG,IAAE,mBAAkBL,IAAE,oBAAmBO,IAAE,oBAAmBC,IAAE,UAASC,IAAE,UAASC,GAAC,IAAE,GAAEE,KAAE,EAAE,WAAS;AAAE,MAAIC,KAAE,MAAKE,KAAE,MAAKC,KAAE;AAAK,OAAI,CAACd,MAAG,qBAAmBA,OAAI,CAACF,IAAE;AAAC,QAAG,QAAMS,MAAG,QAAMC,GAAE,CAAAG,KAAEJ,IAAEM,KAAEL;AAAA,SAAM;AAAC,YAAMI,KAAEV,GAAE,EAAC,QAAO,GAAE,UAASK,IAAE,UAASC,IAAE,iBAAgB,CAACV,IAAE,mBAAkB,EAAE,EAAC,mBAAkBA,IAAE,oBAAmBO,IAAE,UAASE,IAAE,UAASC,GAAC,CAAC,EAAC,CAAC;AAAE,MAAAG,KAAEC,GAAE,OAAK,MAAKC,KAAED,GAAE,OAAK;AAAA,IAAI;AAAC,IAAAE,KAAE,EAAEH,MAAG,GAAEE,MAAG,GAAEH,EAAC;AAAA,EAAC,OAAK;AAAC,UAAK,EAAC,aAAYE,GAAC,IAAE,EAAE,GAAE,EAAC,OAAMb,IAAE,mBAAkBD,IAAE,oBAAmBO,IAAE,oBAAmBC,IAAE,sBAAqBN,IAAE,2BAA0BG,IAAE,UAASI,IAAE,UAASC,IAAE,YAAWE,GAAC,CAAC;AAAE,IAAAC,KAAEC,GAAE,CAAC,EAAE,UAASC,KAAED,GAAEA,GAAE,SAAO,CAAC,EAAE,UAASE,KAAEF,GAAE,IAAK,CAAAX,OAAG,CAACA,GAAE,UAASA,GAAE,QAAQ,CAAE;AAAA,EAAC;AAAC,SAAM,EAAC,KAAIU,IAAE,KAAIE,IAAE,WAAUC,GAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE;AAAC,MAAIf,KAAE;AAAG,WAAQC,KAAE,EAAE,SAAO,GAAEA,MAAG,GAAEA,MAAI;AAAC,QAAG,KAAG,EAAEA,EAAC,EAAE,CAAC,GAAE;AAAC,MAAAD,KAAEC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,QAAMC,MAAG,IAAE,KAAGD,IAAEI,KAAE,CAAC;AAAE,MAAIL,IAAEM,KAAE;AAAE,WAAQC,KAAE,GAAEA,MAAGN,IAAEM,KAAI,CAAAP,KAAEM,KAAEJ,IAAEF,KAAE,OAAOA,GAAE,QAAQ,EAAE,CAAC,GAAEK,GAAE,KAAK,CAACC,IAAEC,OAAIN,KAAE,IAAED,EAAC,CAAC,GAAEM,KAAEN;AAAE,SAAOK;AAAC;", "names": ["a", "t", "l", "n", "d", "i", "r", "o", "u", "s", "c", "m", "f", "p", "e", "v", "b"]}