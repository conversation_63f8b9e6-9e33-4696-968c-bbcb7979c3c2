{"version": 3, "sources": ["../../@arcgis/core/geometry/support/MeshVertexAttributes.js", "../../@arcgis/core/geometry/support/MeshComponent.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{clone as t}from\"../../core/lang.js\";import n from\"../../core/Logger.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import{cast as a}from\"../../core/accessorSupport/decorators/cast.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";var i;const l=\"esri.geometry.support.MeshVertexAttributes\",c=n.getLogger(l);let p=i=class extends o{constructor(r){super(r),this.color=null,this.position=new Float64Array(0),this.uv=null,this.normal=null,this.tangent=null}castColor(r){return g(r,Uint8Array,[Uint8ClampedArray],{loggerTag:\".color=\",stride:4},c)}castPosition(r){r&&r instanceof Float32Array&&c.warn(\".position=\",\"Setting position attribute from a Float32Array may cause precision problems. Consider storing data in a Float64Array or a regular number array\");return g(r,Float64Array,[Float32Array],{loggerTag:\".position=\",stride:3},c)}castUv(r){return g(r,Float32Array,[Float64Array],{loggerTag:\".uv=\",stride:2},c)}castNormal(r){return g(r,Float32Array,[Float64Array],{loggerTag:\".normal=\",stride:3},c)}castTangent(r){return g(r,Float32Array,[Float64Array],{loggerTag:\".tangent=\",stride:4},c)}clone(){const r={position:t(this.position),uv:t(this.uv),normal:t(this.normal),tangent:t(this.tangent),color:t(this.color)};return new i(r)}clonePositional(){const r={position:t(this.position),normal:t(this.normal),tangent:t(this.tangent),uv:this.uv,color:this.color};return new i(r)}};function u(r,o,t,n){const{loggerTag:e,stride:a}=o;return r.length%a!=0?(n.error(e,`Invalid array length, expected a multiple of ${a}`),new t([])):r}function g(r,o,t,n,e){if(!r)return r;if(r instanceof o)return u(r,n,o,e);for(const a of t)if(r instanceof a)return u(new o(r),n,o,e);if(Array.isArray(r))return u(new o(r),n,o,e);{const n=t.map((r=>`'${r.name}'`));return e.error(`Failed to set property, expected one of ${n}, but got ${r.constructor.name}`),new o([])}}function m(r,o,t){o[t]=y(r)}function y(r){const o=new Array(r.length);for(let t=0;t<r.length;t++)o[t]=r[t];return o}r([e({json:{write:m}})],p.prototype,\"color\",void 0),r([a(\"color\")],p.prototype,\"castColor\",null),r([e({nonNullable:!0,json:{write:m}})],p.prototype,\"position\",void 0),r([a(\"position\")],p.prototype,\"castPosition\",null),r([e({json:{write:m}})],p.prototype,\"uv\",void 0),r([a(\"uv\")],p.prototype,\"castUv\",null),r([e({json:{write:m}})],p.prototype,\"normal\",void 0),r([a(\"normal\")],p.prototype,\"castNormal\",null),r([e({json:{write:m}})],p.prototype,\"tangent\",void 0),r([a(\"tangent\")],p.prototype,\"castTangent\",null),p=i=r([s(l)],p);export{p as MeshVertexAttributes,g as castArray};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{clone as e}from\"../../core/lang.js\";import s from\"../../core/Logger.js\";import{property as o}from\"../../core/accessorSupport/decorators/property.js\";import{cast as a}from\"../../core/accessorSupport/decorators/cast.js\";import{subclass as i}from\"../../core/accessorSupport/decorators/subclass.js\";import{ensureClass as c}from\"../../core/accessorSupport/ensureType.js\";import l from\"./MeshMaterial.js\";import p from\"./MeshMaterialMetallicRoughness.js\";import{castArray as n}from\"./MeshVertexAttributes.js\";var u;const m=\"esri.geometry.support.MeshComponent\",h=s.getLogger(m);let f=u=class extends t{static from(r){return c(u,r)}constructor(r){super(r),this.faces=null,this.material=null,this.shading=\"source\",this.trustSourceNormals=!1}castFaces(r){return n(r,Uint32Array,[Uint16Array],{loggerTag:\".faces=\",stride:3},h)}castMaterial(r){return c(r&&\"object\"==typeof r&&(\"metallic\"in r||\"roughness\"in r||\"metallicRoughnessTexture\"in r)?p:l,r)}clone(){return new u({faces:e(this.faces),shading:this.shading,material:e(this.material),trustSourceNormals:this.trustSourceNormals})}cloneWithDeduplication(r,t){const s={faces:e(this.faces),shading:this.shading,material:this.material?this.material.cloneWithDeduplication(r,t):null,trustSourceNormals:this.trustSourceNormals};return new u(s)}};r([o({json:{write:!0}})],f.prototype,\"faces\",void 0),r([a(\"faces\")],f.prototype,\"castFaces\",null),r([o({type:l,json:{write:!0}})],f.prototype,\"material\",void 0),r([a(\"material\")],f.prototype,\"castMaterial\",null),r([o({type:String,json:{write:!0}})],f.prototype,\"shading\",void 0),r([o({type:Boolean})],f.prototype,\"trustSourceNormals\",void 0),f=u=r([i(m)],f);const g=f;export{g as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIoZ,IAAI;AAAE,IAAMA,KAAE;AAAR,IAAqDC,KAAE,EAAE,UAAUD,EAAC;AAAE,IAAIE,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,WAAS,IAAI,aAAa,CAAC,GAAE,KAAK,KAAG,MAAK,KAAK,SAAO,MAAK,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,UAAU,GAAE;AAAC,WAAO,EAAE,GAAE,YAAW,CAAC,iBAAiB,GAAE,EAAC,WAAU,WAAU,QAAO,EAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,aAAa,GAAE;AAAC,SAAG,aAAa,gBAAcA,GAAE,KAAK,cAAa,gJAAgJ;AAAE,WAAO,EAAE,GAAE,cAAa,CAAC,YAAY,GAAE,EAAC,WAAU,cAAa,QAAO,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,GAAE;AAAC,WAAO,EAAE,GAAE,cAAa,CAAC,YAAY,GAAE,EAAC,WAAU,QAAO,QAAO,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,WAAW,GAAE;AAAC,WAAO,EAAE,GAAE,cAAa,CAAC,YAAY,GAAE,EAAC,WAAU,YAAW,QAAO,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,WAAO,EAAE,GAAE,cAAa,CAAC,YAAY,GAAE,EAAC,WAAU,aAAY,QAAO,EAAC,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAM,IAAE,EAAC,UAAS,EAAE,KAAK,QAAQ,GAAE,IAAG,EAAE,KAAK,EAAE,GAAE,QAAO,EAAE,KAAK,MAAM,GAAE,SAAQ,EAAE,KAAK,OAAO,GAAE,OAAM,EAAE,KAAK,KAAK,EAAC;AAAE,WAAO,IAAI,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAM,IAAE,EAAC,UAAS,EAAE,KAAK,QAAQ,GAAE,QAAO,EAAE,KAAK,MAAM,GAAE,SAAQ,EAAE,KAAK,OAAO,GAAE,IAAG,KAAK,IAAG,OAAM,KAAK,MAAK;AAAE,WAAO,IAAI,EAAE,CAAC;AAAA,EAAC;AAAC;AAAE,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE;AAAC,QAAK,EAAC,WAAUE,IAAE,QAAOC,GAAC,IAAE;AAAE,SAAO,EAAE,SAAOA,MAAG,KAAG,EAAE,MAAMD,IAAE,gDAAgDC,EAAC,EAAE,GAAE,IAAI,EAAE,CAAC,CAAC,KAAG;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE,GAAED,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAE,MAAG,aAAa,EAAE,QAAO,EAAE,GAAE,GAAE,GAAEA,EAAC;AAAE,aAAUC,MAAK,EAAE,KAAG,aAAaA,GAAE,QAAO,EAAE,IAAI,EAAE,CAAC,GAAE,GAAE,GAAED,EAAC;AAAE,MAAG,MAAM,QAAQ,CAAC,EAAE,QAAO,EAAE,IAAI,EAAE,CAAC,GAAE,GAAE,GAAEA,EAAC;AAAE;AAAC,UAAME,KAAE,EAAE,IAAK,CAAAC,OAAG,IAAIA,GAAE,IAAI,GAAI;AAAE,WAAOH,GAAE,MAAM,2CAA2CE,EAAC,aAAa,EAAE,YAAY,IAAI,EAAE,GAAE,IAAI,EAAE,CAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,IAAE,CAAC,IAAEE,GAAE,CAAC;AAAC;AAAC,SAASA,GAAE,GAAE;AAAC,QAAM,IAAE,IAAI,MAAM,EAAE,MAAM;AAAE,WAAQ,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,IAAE,EAAE,CAAC;AAAE,SAAO;AAAC;AAAC,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAEL,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAACM,GAAE,OAAO,CAAC,GAAEN,GAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,aAAY,MAAG,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACM,GAAE,UAAU,CAAC,GAAEN,GAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAACM,GAAE,IAAI,CAAC,GAAEN,GAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAACM,GAAE,QAAQ,CAAC,GAAEN,GAAE,WAAU,cAAa,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAACM,GAAE,SAAS,CAAC,GAAEN,GAAE,WAAU,eAAc,IAAI,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAEF,EAAC,CAAC,GAAEE,EAAC;;;ACAx/D,IAAIO;AAAE,IAAMC,KAAE;AAAR,IAA8C,IAAE,EAAE,UAAUA,EAAC;AAAE,IAAI,IAAED,KAAE,cAAc,EAAC;AAAA,EAAC,OAAO,KAAK,GAAE;AAAC,WAAO,EAAEA,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,WAAS,MAAK,KAAK,UAAQ,UAAS,KAAK,qBAAmB;AAAA,EAAE;AAAA,EAAC,UAAU,GAAE;AAAC,WAAO,EAAE,GAAE,aAAY,CAAC,WAAW,GAAE,EAAC,WAAU,WAAU,QAAO,EAAC,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,aAAa,GAAE;AAAC,WAAO,EAAE,KAAG,YAAU,OAAO,MAAI,cAAa,KAAG,eAAc,KAAG,8BAA6B,KAAG,IAAEE,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAC,OAAM,EAAE,KAAK,KAAK,GAAE,SAAQ,KAAK,SAAQ,UAAS,EAAE,KAAK,QAAQ,GAAE,oBAAmB,KAAK,mBAAkB,CAAC;AAAA,EAAC;AAAA,EAAC,uBAAuB,GAAE,GAAE;AAAC,UAAMG,KAAE,EAAC,OAAM,EAAE,KAAK,KAAK,GAAE,SAAQ,KAAK,SAAQ,UAAS,KAAK,WAAS,KAAK,SAAS,uBAAuB,GAAE,CAAC,IAAE,MAAK,oBAAmB,KAAK,mBAAkB;AAAE,WAAO,IAAIH,GAAEG,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAACA,GAAE,OAAO,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACC,GAAE,UAAU,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,MAAM,GAAE,IAAEH,KAAE,EAAE,CAAC,EAAEC,EAAC,CAAC,GAAE,CAAC;AAAE,IAAMG,KAAE;", "names": ["l", "c", "p", "e", "a", "n", "r", "y", "s", "u", "m", "a", "s", "g"]}