{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/sceneLayerSnappingUtils.js", "../../@arcgis/core/views/interactive/snapping/featureSources/sceneLayerSource/SceneLayerSnappingSourceWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{z as s,i as o,a as r}from\"../../../../../chunks/vec3.js\";import{c,g as n}from\"../../../../../chunks/sphere.js\";const t=1e3;function a(t,a,e){const i=c(),m=n(i);return s(m,m,t,.5),s(m,m,a,.5),i[3]=o(m,t),r(m,m,e),i}export{t as MAX_CANDIDATE_COUNT,a as boundsFromEdge};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import{isNone as t,unwrapOr as s}from\"../../../../../core/maybe.js\";import{throwIfAborted as o}from\"../../../../../core/promiseUtils.js\";import\"../../../../../core/Logger.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import\"../../../../../core/Error.js\";import\"../../../../../core/has.js\";import{subclass as i}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{a as n,i as r}from\"../../../../../chunks/vec3.js\";import{c as d,a as c}from\"../../../../../chunks/vec3f64.js\";import{create as a,fromPoints as p,projectPoint as m}from\"../../../../../geometry/support/lineSegment.js\";import{g as h,m as u}from\"../../../../../chunks/sphere.js\";import{SnappingTypes as g}from\"../../../../../layers/graphics/data/QueryEngineResult.js\";import l from\"../../../../3d/webgl-engine/lib/Octree.js\";import{extractComponentsEdgeLocationsLayout as _}from\"../../../../3d/webgl-engine/lib/edgeRendering/edgeProcessing.js\";import{MAX_CANDIDATE_COUNT as f,boundsFromEdge as b}from\"./sceneLayerSnappingUtils.js\";let j=class{constructor(){this._idToComponent=new Map,this._components=new l((e=>e.bounds)),this._edges=new l((e=>e.bounds)),this._tmpLineSegment=a(),this._tmpP1=d(),this._tmpP2=d(),this._tmpP3=d(),this.remoteClient=null}async fetchCandidates(e,t){await Promise.resolve(),o(t),await this._ensureEdgeLocations(e,t);const s=[];return this._edges.forEachNeighbor((t=>(this._addCandidates(e,t,s),s.length<f)),e.bounds),{result:{candidates:s}}}async _ensureEdgeLocations(e,o){const i=[];if(this._components.forEachNeighbor((e=>{if(t(e.info)){const{id:t,uid:s}=e;i.push({id:t,uid:s})}return!0}),e.bounds),!i.length)return;const n={components:i},r=await this.remoteClient.invoke(\"fetchAllEdgeLocations\",n,s(o,{}));for(const t of r.components)this._setFetchEdgeLocations(t)}async add(e){const t=new E(e.id,e.bounds);return this._idToComponent.set(t.id,t),this._components.add([t]),{result:{}}}async remove(e){const t=this._idToComponent.get(e.id);if(t){const e=[];this._edges.forEachNeighbor((s=>(s.component===t&&e.push(s),!0)),t.bounds),this._edges.remove(e),this._components.remove([t]),this._idToComponent.delete(t.id)}return{result:{}}}_setFetchEdgeLocations(e){const s=this._idToComponent.get(e.id);if(t(s)||e.uid!==s.uid)return;const o=_.createView(e.locations),i=new Array(o.count),n=d(),r=d();for(let t=0;t<o.count;t++){o.position0.getVec(t,n),o.position1.getVec(t,r);const d=b(n,r,e.origin),c=new C(s,t,d);i[t]=c}this._edges.add(i);const{objectIds:c,origin:a}=e;s.info={locations:o,objectIds:c,origin:a}}_addCandidates(e,t,s){const{info:o}=t.component,{origin:i,objectIds:r}=o,d=o.locations,c=d.position0.getVec(t.index,this._tmpP1),a=d.position1.getVec(t.index,this._tmpP2);n(c,c,i),n(a,a,i);const p=r[d.componentIndex.get(t.index)];this._addEdgeCandidate(e,p,c,a,s),this._addVertexCandidate(e,p,c,s),this._addVertexCandidate(e,p,a,s)}_addEdgeCandidate(e,t,s,o,i){if(!(e.types&g.EDGE))return;const n=h(e.bounds),d=p(s,o,this._tmpLineSegment),a=m(d,n,this._tmpP3);u(e.bounds,a)&&i.push({type:\"edge\",objectId:t,target:c(a),distance:r(n,a),start:c(s),end:c(o)})}_addVertexCandidate(e,t,s,o){if(!(e.types&g.VERTEX))return;const i=h(e.bounds);u(e.bounds,s)&&o.push({type:\"vertex\",objectId:t,target:c(s),distance:r(i,s)})}};j=e([i(\"esri.views.interactive.snapping.featureSources.sceneLayerSource.SceneLayerSnappingSourceWorker\")],j);const y=j;class E{constructor(e,t){this.id=e,this.bounds=t,this.info=null,this.uid=++E.uid}}E.uid=0;class C{constructor(e,t,s){this.component=e,this.index=t,this.bounds=s}}export{y as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsH,IAAMA,KAAE;AAAI,SAASC,GAAED,IAAEC,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,GAAEC,KAAE,EAAE,CAAC;AAAE,SAAO,EAAEA,IAAEA,IAAEH,IAAE,GAAE,GAAE,EAAEG,IAAEA,IAAEF,IAAE,GAAE,GAAE,EAAE,CAAC,IAAE,EAAEE,IAAEH,EAAC,GAAE,EAAEG,IAAEA,IAAED,EAAC,GAAE;AAAC;;;ACAs4B,IAAIE,KAAE,MAAK;AAAA,EAAC,cAAa;AAAC,SAAK,iBAAe,oBAAI,OAAI,KAAK,cAAY,IAAI,EAAG,CAAAC,OAAGA,GAAE,MAAO,GAAE,KAAK,SAAO,IAAI,EAAG,CAAAA,OAAGA,GAAE,MAAO,GAAE,KAAK,kBAAgB,EAAE,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,SAAO,EAAE,GAAE,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,MAAM,gBAAgBA,IAAEC,IAAE;AAAC,UAAM,QAAQ,QAAQ,GAAE,EAAEA,EAAC,GAAE,MAAM,KAAK,qBAAqBD,IAAEC,EAAC;AAAE,UAAM,IAAE,CAAC;AAAE,WAAO,KAAK,OAAO,gBAAiB,CAAAA,QAAI,KAAK,eAAeD,IAAEC,IAAE,CAAC,GAAE,EAAE,SAAOA,KAAID,GAAE,MAAM,GAAE,EAAC,QAAO,EAAC,YAAW,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBA,IAAE,GAAE;AAAC,UAAM,IAAE,CAAC;AAAE,QAAG,KAAK,YAAY,gBAAiB,CAAAA,OAAG;AAAC,UAAG,EAAEA,GAAE,IAAI,GAAE;AAAC,cAAK,EAAC,IAAGC,IAAE,KAAI,EAAC,IAAED;AAAE,UAAE,KAAK,EAAC,IAAGC,IAAE,KAAI,EAAC,CAAC;AAAA,MAAC;AAAC,aAAM;AAAA,IAAE,GAAGD,GAAE,MAAM,GAAE,CAAC,EAAE,OAAO;AAAO,UAAME,KAAE,EAAC,YAAW,EAAC,GAAE,IAAE,MAAM,KAAK,aAAa,OAAO,yBAAwBA,IAAE,EAAE,GAAE,CAAC,CAAC,CAAC;AAAE,eAAUD,MAAK,EAAE,WAAW,MAAK,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,IAAID,IAAE;AAAC,UAAMC,KAAE,IAAI,EAAED,GAAE,IAAGA,GAAE,MAAM;AAAE,WAAO,KAAK,eAAe,IAAIC,GAAE,IAAGA,EAAC,GAAE,KAAK,YAAY,IAAI,CAACA,EAAC,CAAC,GAAE,EAAC,QAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOD,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAe,IAAID,GAAE,EAAE;AAAE,QAAGC,IAAE;AAAC,YAAMD,KAAE,CAAC;AAAE,WAAK,OAAO,gBAAiB,QAAI,EAAE,cAAYC,MAAGD,GAAE,KAAK,CAAC,GAAE,OAAKC,GAAE,MAAM,GAAE,KAAK,OAAO,OAAOD,EAAC,GAAE,KAAK,YAAY,OAAO,CAACC,EAAC,CAAC,GAAE,KAAK,eAAe,OAAOA,GAAE,EAAE;AAAA,IAAC;AAAC,WAAM,EAAC,QAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAE;AAAC,UAAM,IAAE,KAAK,eAAe,IAAIA,GAAE,EAAE;AAAE,QAAG,EAAE,CAAC,KAAGA,GAAE,QAAM,EAAE,IAAI;AAAO,UAAM,IAAE,EAAE,WAAWA,GAAE,SAAS,GAAE,IAAE,IAAI,MAAM,EAAE,KAAK,GAAEE,KAAE,EAAE,GAAE,IAAE,EAAE;AAAE,aAAQD,KAAE,GAAEA,KAAE,EAAE,OAAMA,MAAI;AAAC,QAAE,UAAU,OAAOA,IAAEC,EAAC,GAAE,EAAE,UAAU,OAAOD,IAAE,CAAC;AAAE,YAAM,IAAEE,GAAED,IAAE,GAAEF,GAAE,MAAM,GAAEI,KAAE,IAAI,EAAE,GAAEH,IAAE,CAAC;AAAE,QAAEA,EAAC,IAAEG;AAAA,IAAC;AAAC,SAAK,OAAO,IAAI,CAAC;AAAE,UAAK,EAAC,WAAU,GAAE,QAAOD,GAAC,IAAEH;AAAE,MAAE,OAAK,EAAC,WAAU,GAAE,WAAU,GAAE,QAAOG,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAEC,IAAE,GAAE;AAAC,UAAK,EAAC,MAAK,EAAC,IAAEA,GAAE,WAAU,EAAC,QAAO,GAAE,WAAU,EAAC,IAAE,GAAE,IAAE,EAAE,WAAU,IAAE,EAAE,UAAU,OAAOA,GAAE,OAAM,KAAK,MAAM,GAAEE,KAAE,EAAE,UAAU,OAAOF,GAAE,OAAM,KAAK,MAAM;AAAE,MAAE,GAAE,GAAE,CAAC,GAAE,EAAEE,IAAEA,IAAE,CAAC;AAAE,UAAM,IAAE,EAAE,EAAE,eAAe,IAAIF,GAAE,KAAK,CAAC;AAAE,SAAK,kBAAkBD,IAAE,GAAE,GAAEG,IAAE,CAAC,GAAE,KAAK,oBAAoBH,IAAE,GAAE,GAAE,CAAC,GAAE,KAAK,oBAAoBA,IAAE,GAAEG,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAE,GAAE,GAAE,GAAE;AAAC,QAAG,EAAED,GAAE,QAAM,EAAE,MAAM;AAAO,UAAME,KAAE,EAAEF,GAAE,MAAM,GAAE,IAAE,EAAE,GAAE,GAAE,KAAK,eAAe,GAAEG,KAAE,EAAE,GAAED,IAAE,KAAK,MAAM;AAAE,MAAEF,GAAE,QAAOG,EAAC,KAAG,EAAE,KAAK,EAAC,MAAK,QAAO,UAASF,IAAE,QAAOA,GAAEE,EAAC,GAAE,UAAS,EAAED,IAAEC,EAAC,GAAE,OAAMF,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAEC,IAAE,GAAE,GAAE;AAAC,QAAG,EAAED,GAAE,QAAM,EAAE,QAAQ;AAAO,UAAM,IAAE,EAAEA,GAAE,MAAM;AAAE,MAAEA,GAAE,QAAO,CAAC,KAAG,EAAE,KAAK,EAAC,MAAK,UAAS,UAASC,IAAE,QAAOA,GAAE,CAAC,GAAE,UAAS,EAAE,GAAE,CAAC,EAAC,CAAC;AAAA,EAAC;AAAC;AAAEF,KAAE,EAAE,CAAC,EAAE,gGAAgG,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;AAAE,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,KAAGD,IAAE,KAAK,SAAOC,IAAE,KAAK,OAAK,MAAK,KAAK,MAAI,EAAE,GAAE;AAAA,EAAG;AAAC;AAAC,EAAE,MAAI;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE,GAAE;AAAC,SAAK,YAAUD,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAO;AAAA,EAAC;AAAC;", "names": ["t", "a", "e", "m", "j", "e", "t", "n", "a", "c"]}