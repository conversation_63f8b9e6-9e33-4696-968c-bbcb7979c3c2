import {
  j
} from "./chunk-JOV46W3N.js";
import {
  v
} from "./chunk-NDCSRZLO.js";
import {
  e
} from "./chunk-2CM7MIII.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/accessorSupport/diffUtils.js
var f = ["esri.Color", "esri.portal.Portal", "esri.symbols.support.Symbol3DAnchorPosition2D", "esri.symbols.support.Symbol3DAnchorPosition3D"];
function i(t2) {
  return t2 instanceof v;
}
function c(e2) {
  return e2 instanceof j ? Object.keys(e2.items) : i(e2) ? e(e2).keys() : e2 ? Object.keys(e2) : [];
}
function l(e2, n) {
  return e2 instanceof j ? e2.items[n] : e2[n];
}
function u(e2, t2) {
  return !(!Array.isArray(e2) || !Array.isArray(t2)) && e2.length !== t2.length;
}
function s(e2) {
  return e2 ? e2.declaredClass : null;
}
function p(e2, t2) {
  const n = e2.diff;
  if (n && "function" == typeof n) return n(e2, t2);
  const r2 = c(e2), a2 = c(t2);
  if (0 === r2.length && 0 === a2.length) return;
  if (!r2.length || !a2.length || u(e2, t2)) return { type: "complete", oldValue: e2, newValue: t2 };
  const y2 = a2.filter((e3) => !r2.includes(e3)), m2 = r2.filter((e3) => !a2.includes(e3)), d2 = r2.filter((n2) => a2.includes(n2) && l(e2, n2) !== l(t2, n2)).concat(y2, m2).sort(), b = s(e2);
  if (b && f.includes(b) && d2.length) return { type: "complete", oldValue: e2, newValue: t2 };
  let h;
  const g = i(e2) && i(t2);
  for (const f2 of d2) {
    const r3 = l(e2, f2), i2 = l(t2, f2);
    let c2;
    if ((g || "function" != typeof r3 && "function" != typeof i2) && (r3 !== i2 && (null != r3 || null != i2))) {
      if (n && n[f2] && "function" == typeof n[f2]) c2 = n[f2](r3, i2);
      else if (r3 instanceof Date && i2 instanceof Date) {
        if (r3.getTime() === i2.getTime()) continue;
        c2 = { type: "complete", oldValue: r3, newValue: i2 };
      } else c2 = "object" == typeof r3 && "object" == typeof i2 && s(r3) === s(i2) ? p(r3, i2) : { type: "complete", oldValue: r3, newValue: i2 };
      r(c2) && (r(h) ? h.diff[f2] = c2 : h = { type: "partial", diff: { [f2]: c2 } });
    }
  }
  return h;
}
function a(e2, t2) {
  if (t(e2)) return false;
  const o = t2.split(".");
  let r2 = e2;
  for (const n of o) {
    if ("complete" === r2.type) return true;
    if ("partial" !== r2.type) return false;
    {
      const e3 = r2.diff[n];
      if (!e3) return false;
      r2 = e3;
    }
  }
  return true;
}
function y(e2, t2) {
  for (const n of t2) if (a(e2, n)) return true;
  return false;
}
function m(e2, t2) {
  if (!("function" == typeof e2 || "function" == typeof t2 || t(e2) && t(t2))) return t(e2) || t(t2) || "object" == typeof e2 && "object" == typeof t2 && s(e2) !== s(t2) ? { type: "complete", oldValue: e2, newValue: t2 } : p(e2, t2);
}
function d(e2) {
  if (t(e2)) return true;
  switch (e2.type) {
    case "complete":
      return false;
    case "collection": {
      const t2 = e2;
      for (const e3 of t2.added) if (!d(e3)) return false;
      for (const e3 of t2.removed) if (!d(e3)) return false;
      for (const e3 of t2.changed) if (!d(e3)) return false;
      return true;
    }
    case "partial":
      for (const t2 in e2.diff) {
        if (!d(e2.diff[t2])) return false;
      }
      return true;
  }
}

export {
  a,
  y,
  m,
  d
};
//# sourceMappingURL=chunk-GE5PSQPZ.js.map
