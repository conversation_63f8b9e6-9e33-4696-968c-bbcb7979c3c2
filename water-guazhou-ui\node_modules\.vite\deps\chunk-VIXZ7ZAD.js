import {
  a as a2
} from "./chunk-QMG7GZIF.js";
import {
  o
} from "./chunk-G5KX4JSG.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/symbols/MarkerSymbol.js
var p = class extends a2 {
  constructor(t) {
    super(t), this.angle = 0, this.type = null, this.xoffset = 0, this.yoffset = 0, this.size = 9;
  }
  hash() {
    return `${this.type}.${this.angle}.${this.size}.${this.xoffset}.${this.yoffset}`;
  }
};
e([y({ type: Number, json: { read: (t) => t && -1 * t, write: (t, e2) => e2.angle = t && -1 * t } })], p.prototype, "angle", void 0), e([y({ type: ["simple-marker", "picture-marker"], readOnly: true })], p.prototype, "type", void 0), e([y({ type: Number, cast: o, json: { write: true } })], p.prototype, "xoffset", void 0), e([y({ type: Number, cast: o, json: { write: true } })], p.prototype, "yoffset", void 0), e([y({ type: Number, cast: (t) => "auto" === t ? t : o(t), json: { write: true } })], p.prototype, "size", void 0), p = e([a("esri.symbols.MarkerSymbol")], p);
var i = p;

export {
  i
};
//# sourceMappingURL=chunk-VIXZ7ZAD.js.map
