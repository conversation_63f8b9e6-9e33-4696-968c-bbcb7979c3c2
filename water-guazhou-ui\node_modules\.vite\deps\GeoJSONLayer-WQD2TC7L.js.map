{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/sources/GeoJSONSource.js", "../../@arcgis/core/layers/GeoJSONLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../core/Error.js\";import has from\"../../../core/has.js\";import r from\"../../../core/Loadable.js\";import o from\"../../../core/Logger.js\";import{isSome as s,isNone as i}from\"../../../core/maybe.js\";import{debounce as n}from\"../../../core/promiseUtils.js\";import{open as a}from\"../../../core/workers/workers.js\";import{property as u}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as l}from\"../../../core/accessorSupport/decorators/subclass.js\";import{createCapabilities as c}from\"./support/clientSideDefaults.js\";import d from\"../../../rest/support/FeatureSet.js\";import p from\"../../../geometry/Extent.js\";import h from\"../../../geometry/Polygon.js\";import{featureGeometryTypeKebabDictionary as m}from\"../../../geometry/support/typeUtils.js\";const y=\"esri.layers.graphics.sources.GeoJSONSource\",f=o.getLogger(y);let g=class extends r{constructor(){super(...arguments),this.type=\"geojson\",this.refresh=n((async e=>{await this.load();const{extent:t,timeExtent:r}=await this._connection.invoke(\"refresh\",e);return this.sourceJSON.extent=t,r&&(this.sourceJSON.timeInfo.timeExtent=[r.start,r.end]),{dataChanged:!0,updates:{extent:this.sourceJSON.extent,timeInfo:this.sourceJSON.timeInfo}}}))}load(e){const t=s(e)?e.signal:null;return this.addResolvingPromise(this._startWorker(t)),Promise.resolve(this)}destroy(){this._connection?.close(),this._connection=null}applyEdits(e){return this.load().then((()=>this._applyEdits(e)))}openPorts(){return this.load().then((()=>this._connection.openPorts()))}queryFeatures(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"queryFeatures\",e?e.toJSON():null,t))).then((e=>d.fromJSON(e)))}queryFeaturesJSON(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"queryFeatures\",e?e.toJSON():null,t)))}queryFeatureCount(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"queryFeatureCount\",e?e.toJSON():null,t)))}queryObjectIds(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"queryObjectIds\",e?e.toJSON():null,t)))}queryExtent(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"queryExtent\",e?e.toJSON():null,t))).then((e=>({count:e.count,extent:p.fromJSON(e.extent)})))}querySnapping(e,t={}){return this.load(t).then((()=>this._connection.invoke(\"querySnapping\",e,t)))}_applyEdits(e){if(!this._connection)throw new t(\"geojson-layer-source:edit-failure\",\"Memory source not loaded\");const r=this.layer.objectIdField,o=[],s=[],i=[];if(e.addFeatures)for(const t of e.addFeatures)o.push(this._serializeFeature(t));if(e.deleteFeatures)for(const t of e.deleteFeatures)\"objectId\"in t&&null!=t.objectId?s.push(t.objectId):\"attributes\"in t&&null!=t.attributes[r]&&s.push(t.attributes[r]);if(e.updateFeatures)for(const t of e.updateFeatures)i.push(this._serializeFeature(t));return this._connection.invoke(\"applyEdits\",{adds:o,updates:i,deletes:s}).then((({extent:e,timeExtent:t,featureEditResults:r})=>(this.sourceJSON.extent=e,t&&(this.sourceJSON.timeInfo.timeExtent=[t.start,t.end]),this._createEditsResult(r))))}_createEditsResult(e){return{addFeatureResults:e.addResults?e.addResults.map(this._createFeatureEditResult,this):[],updateFeatureResults:e.updateResults?e.updateResults.map(this._createFeatureEditResult,this):[],deleteFeatureResults:e.deleteResults?e.deleteResults.map(this._createFeatureEditResult,this):[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]}}_createFeatureEditResult(e){const r=!0===e.success?null:e.error||{code:void 0,description:void 0};return{objectId:e.objectId,globalId:e.globalId,error:r?new t(\"geojson-layer-source:edit-failure\",r.description,{code:r.code}):null}}_serializeFeature(e){const{attributes:t}=e,r=this._geometryForSerialization(e);return r?{geometry:r.toJSON(),attributes:t}:{attributes:t}}_geometryForSerialization(e){const{geometry:t}=e;return i(t)?null:\"mesh\"===t.type||\"extent\"===t.type?h.fromExtent(t.extent):t}async _startWorker(e){this._connection=await a(\"GeoJSONSourceWorker\",{strategy:has(\"feature-layers-workers\")?\"dedicated\":\"local\",signal:e});const{fields:t,spatialReference:r,hasZ:o,geometryType:s,objectIdField:i,url:n,timeInfo:u,customParameters:l}=this.layer,d=\"defaults\"===this.layer.originOf(\"spatialReference\"),p={url:n,customParameters:l,fields:t&&t.map((e=>e.toJSON())),geometryType:m.toJSON(s),hasZ:o,objectIdField:i,timeInfo:u?u.toJSON():null,spatialReference:d?null:r&&r.toJSON()},h=await this._connection.invoke(\"load\",p,{signal:e});for(const a of h.warnings)f.warn(a.message,{layer:this.layer,warning:a});h.featureErrors.length&&f.warn(`Encountered ${h.featureErrors.length} validation errors while loading features`,h.featureErrors),this.sourceJSON=h.layerDefinition,this.capabilities=c(this.sourceJSON.hasZ,!0)}};e([u()],g.prototype,\"capabilities\",void 0),e([u()],g.prototype,\"type\",void 0),e([u({constructOnly:!0})],g.prototype,\"layer\",void 0),e([u()],g.prototype,\"sourceJSON\",void 0),g=e([l(y)],g);export{g as GeoJSONSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import\"../geometry.js\";import t from\"../PopupTemplate.js\";import\"../renderers/ClassBreaksRenderer.js\";import\"../renderers/DictionaryRenderer.js\";import\"../renderers/DotDensityRenderer.js\";import\"../renderers/HeatmapRenderer.js\";import\"../renderers/PieChartRenderer.js\";import\"../renderers/Renderer.js\";import\"../renderers/SimpleRenderer.js\";import\"../renderers/UniqueValueRenderer.js\";import\"../renderers/support/jsonUtils.js\";import{rendererTypes as r,webSceneRendererTypes as i}from\"../renderers/support/types.js\";import{isSome as o}from\"../core/maybe.js\";import{MultiOriginJSONMixin as s}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as p}from\"../core/promiseUtils.js\";import{urlToObject as n}from\"../core/urlUtils.js\";import{property as l}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as a}from\"../core/accessorSupport/decorators/subclass.js\";import{featureGeometryTypeKebabDictionary as d}from\"../geometry/support/typeUtils.js\";import u from\"./Layer.js\";import{GeoJSONSource as m}from\"./graphics/sources/GeoJSONSource.js\";import{BlendLayer as y}from\"./mixins/BlendLayer.js\";import{CustomParametersMixin as h}from\"./mixins/CustomParametersMixin.js\";import{FeatureEffectLayer as c}from\"./mixins/FeatureEffectLayer.js\";import{FeatureReductionLayer as f}from\"./mixins/FeatureReductionLayer.js\";import{OperationalLayer as j}from\"./mixins/OperationalLayer.js\";import{OrderedLayer as g}from\"./mixins/OrderedLayer.js\";import{PortalLayer as v}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as b}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as x}from\"./mixins/ScaleRangeLayer.js\";import{TemporalLayer as I}from\"./mixins/TemporalLayer.js\";import{elevationInfo as O,id as S,labelsVisible as E,legendEnabled as F,opacity as P,popupEnabled as w,screenSizePerspectiveEnabled as T,url as L}from\"./support/commonProperties.js\";import R from\"./support/FeatureTemplate.js\";import D from\"./support/FeatureType.js\";import q from\"./support/Field.js\";import{defineFieldProperties as U}from\"./support/fieldProperties.js\";import{fixRendererFields as J,fixTimeInfoFields as N}from\"./support/fieldUtils.js\";import Q from\"./support/LabelClass.js\";import{reader as C}from\"./support/labelingInfo.js\";import G from\"../rest/support/Query.js\";import{createPopupTemplate as _}from\"../support/popupUtils.js\";import V from\"../geometry/SpatialReference.js\";import B from\"../geometry/Extent.js\";const Z=U();let M=class extends(g(h(f(c(y(I(x(b(j(v(s(u)))))))))))){constructor(e){super(e),this.copyright=null,this.definitionExpression=null,this.displayField=null,this.editingEnabled=!1,this.elevationInfo=null,this.fields=null,this.fieldsIndex=null,this.fullExtent=null,this.geometryType=null,this.hasZ=void 0,this.labelsVisible=!0,this.labelingInfo=null,this.legendEnabled=!0,this.objectIdField=null,this.operationalLayerType=\"GeoJSON\",this.popupEnabled=!0,this.popupTemplate=null,this.screenSizePerspectiveEnabled=!0,this.source=new m({layer:this}),this.spatialReference=V.WGS84,this.templates=null,this.title=\"GeoJSON\",this.type=\"geojson\",this.typeIdField=null,this.types=null}destroy(){this.source?.destroy()}load(e){const t=this.loadFromPortal({supportedTypes:[\"GeoJson\"],supportsData:!1},e).catch(p).then((()=>this.source.load(e))).then((()=>{this.read(this.source.sourceJSON,{origin:\"service\",url:this.parsedUrl}),this.revert([\"objectIdField\",\"fields\",\"timeInfo\"],\"service\"),J(this.renderer,this.fieldsIndex),N(this.timeInfo,this.fieldsIndex)}));return this.addResolvingPromise(t),Promise.resolve(this)}get capabilities(){return this.source?this.source.capabilities:null}get createQueryVersion(){return this.commitProperty(\"definitionExpression\"),this.commitProperty(\"timeExtent\"),this.commitProperty(\"timeOffset\"),this.commitProperty(\"geometryType\"),this.commitProperty(\"capabilities\"),(this._get(\"createQueryVersion\")||0)+1}get defaultPopupTemplate(){return this.createPopupTemplate()}get isTable(){return this.loaded&&null==this.geometryType}get parsedUrl(){return this.url?n(this.url):null}set renderer(e){J(e,this.fieldsIndex),this._set(\"renderer\",e)}set url(e){if(!e)return void this._set(\"url\",e);const t=n(e);this._set(\"url\",t.path),t.query&&(this.customParameters={...this.customParameters,...t.query})}async applyEdits(e,t){const r=await import(\"./graphics/editingSupport.js\");await this.load();const i=await r.applyEdits(this,this.source,e,t);return this.read({extent:this.source.sourceJSON.extent,timeInfo:this.source.sourceJSON.timeInfo},{origin:\"service\",ignoreDefaults:!0}),i}on(e,t){return super.on(e,t)}createPopupTemplate(e){return _(this,e)}createQuery(){const e=new G,t=this.get(\"capabilities.data\");e.returnGeometry=!0,t&&t.supportsZ&&(e.returnZ=!0),e.outFields=[\"*\"],e.where=this.definitionExpression||\"1=1\";const{timeOffset:r,timeExtent:i}=this;return e.timeExtent=null!=r&&null!=i?i.offset(-r.value,r.unit):i||null,e}getFieldDomain(e,t){let r,i=!1;const o=t&&t.feature,s=o&&o.attributes,p=this.typeIdField&&s&&s[this.typeIdField];return null!=p&&this.types&&(i=this.types.some((t=>t.id==p&&(r=t.domains&&t.domains[e],r&&\"inherited\"===r.type&&(r=this._getLayerDomain(e)),!0)))),i||r||(r=this._getLayerDomain(e)),r}getField(e){return this.fieldsIndex.get(e)}queryFeatures(e,t){return this.load().then((()=>this.source.queryFeatures(G.from(e)||this.createQuery(),t))).then((e=>{if(e?.features)for(const t of e.features)t.layer=t.sourceLayer=this;return e}))}queryObjectIds(e,t){return this.load().then((()=>this.source.queryObjectIds(G.from(e)||this.createQuery(),t)))}queryFeatureCount(e,t){return this.load().then((()=>this.source.queryFeatureCount(G.from(e)||this.createQuery(),t)))}queryExtent(e,t){return this.load().then((()=>this.source.queryExtent(G.from(e)||this.createQuery(),t)))}async hasDataChanged(){try{const{dataChanged:e,updates:t}=await this.source.refresh(this.customParameters);return o(t)&&this.read(t,{origin:\"service\",url:this.parsedUrl,ignoreDefaults:!0}),e}catch{}return!1}_getLayerDomain(e){if(!this.fields)return null;let t=null;return this.fields.some((r=>(r.name===e&&(t=r.domain),!!t))),t}};e([l({readOnly:!0,json:{read:!1,write:!1}})],M.prototype,\"capabilities\",null),e([l({type:String})],M.prototype,\"copyright\",void 0),e([l({readOnly:!0})],M.prototype,\"createQueryVersion\",null),e([l({readOnly:!0})],M.prototype,\"defaultPopupTemplate\",null),e([l({type:String,json:{name:\"layerDefinition.definitionExpression\",write:{enabled:!0,allowNull:!0}}})],M.prototype,\"definitionExpression\",void 0),e([l({type:String})],M.prototype,\"displayField\",void 0),e([l({type:Boolean})],M.prototype,\"editingEnabled\",void 0),e([l(O)],M.prototype,\"elevationInfo\",void 0),e([l({type:[q],json:{name:\"layerDefinition.fields\",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:\"fields\"}}}})],M.prototype,\"fields\",void 0),e([l(Z.fieldsIndex)],M.prototype,\"fieldsIndex\",void 0),e([l({type:B,json:{name:\"extent\"}})],M.prototype,\"fullExtent\",void 0),e([l({type:[\"point\",\"polygon\",\"polyline\",\"multipoint\"],json:{read:{reader:d.read}}})],M.prototype,\"geometryType\",void 0),e([l({type:Boolean})],M.prototype,\"hasZ\",void 0),e([l(S)],M.prototype,\"id\",void 0),e([l({type:Boolean,readOnly:!0})],M.prototype,\"isTable\",null),e([l(E)],M.prototype,\"labelsVisible\",void 0),e([l({type:[Q],json:{name:\"layerDefinition.drawingInfo.labelingInfo\",read:{reader:C},write:!0}})],M.prototype,\"labelingInfo\",void 0),e([l(F)],M.prototype,\"legendEnabled\",void 0),e([l({type:[\"show\",\"hide\"]})],M.prototype,\"listMode\",void 0),e([l({type:String,json:{name:\"layerDefinition.objectIdField\",write:{ignoreOrigin:!0,isRequired:!0},origins:{service:{name:\"objectIdField\"}}}})],M.prototype,\"objectIdField\",void 0),e([l(P)],M.prototype,\"opacity\",void 0),e([l({type:[\"GeoJSON\"]})],M.prototype,\"operationalLayerType\",void 0),e([l({readOnly:!0})],M.prototype,\"parsedUrl\",null),e([l(w)],M.prototype,\"popupEnabled\",void 0),e([l({type:t,json:{name:\"popupInfo\",write:!0}})],M.prototype,\"popupTemplate\",void 0),e([l({types:r,json:{name:\"layerDefinition.drawingInfo.renderer\",write:!0,origins:{service:{name:\"drawingInfo.renderer\"},\"web-scene\":{types:i}}}})],M.prototype,\"renderer\",null),e([l(T)],M.prototype,\"screenSizePerspectiveEnabled\",void 0),e([l({readOnly:!0})],M.prototype,\"source\",void 0),e([l({type:V})],M.prototype,\"spatialReference\",void 0),e([l({type:[R]})],M.prototype,\"templates\",void 0),e([l()],M.prototype,\"title\",void 0),e([l({json:{read:!1},readOnly:!0})],M.prototype,\"type\",void 0),e([l({type:String,readOnly:!0})],M.prototype,\"typeIdField\",void 0),e([l({type:[D]})],M.prototype,\"types\",void 0),e([l(L)],M.prototype,\"url\",null),M=e([a(\"esri.layers.GeoJSONLayer\")],M);const k=M;export{k as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI07B,IAAMA,KAAE;AAAR,IAAqDC,KAAE,EAAE,UAAUD,EAAC;AAAE,IAAI,IAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,WAAU,KAAK,UAAQ,EAAG,OAAME,OAAG;AAAC,YAAM,KAAK,KAAK;AAAE,YAAK,EAAC,QAAOC,IAAE,YAAWC,GAAC,IAAE,MAAM,KAAK,YAAY,OAAO,WAAUF,EAAC;AAAE,aAAO,KAAK,WAAW,SAAOC,IAAEC,OAAI,KAAK,WAAW,SAAS,aAAW,CAACA,GAAE,OAAMA,GAAE,GAAG,IAAG,EAAC,aAAY,MAAG,SAAQ,EAAC,QAAO,KAAK,WAAW,QAAO,UAAS,KAAK,WAAW,SAAQ,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO;AAAK,WAAO,KAAK,oBAAoB,KAAK,aAAaC,EAAC,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,UAAS;AAJ/+C;AAIg/C,eAAK,gBAAL,mBAAkB,SAAQ,KAAK,cAAY;AAAA,EAAI;AAAA,EAAC,WAAWD,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,YAAYA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,YAAY,UAAU,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,iBAAgBD,KAAEA,GAAE,OAAO,IAAE,MAAKC,EAAC,CAAE,EAAE,KAAM,CAAAD,OAAGG,GAAE,SAASH,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,iBAAgBD,KAAEA,GAAE,OAAO,IAAE,MAAKC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,qBAAoBD,KAAEA,GAAE,OAAO,IAAE,MAAKC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,kBAAiBD,KAAEA,GAAE,OAAO,IAAE,MAAKC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,eAAcD,KAAEA,GAAE,OAAO,IAAE,MAAKC,EAAC,CAAE,EAAE,KAAM,CAAAD,QAAI,EAAC,OAAMA,GAAE,OAAM,QAAOI,GAAE,SAASJ,GAAE,MAAM,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,KAAKA,EAAC,EAAE,KAAM,MAAI,KAAK,YAAY,OAAO,iBAAgBD,IAAEC,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,QAAG,CAAC,KAAK,YAAY,OAAM,IAAIK,GAAE,qCAAoC,0BAA0B;AAAE,UAAMH,KAAE,KAAK,MAAM,eAAcI,KAAE,CAAC,GAAED,KAAE,CAAC,GAAEE,KAAE,CAAC;AAAE,QAAGP,GAAE,YAAY,YAAUC,MAAKD,GAAE,YAAY,CAAAM,GAAE,KAAK,KAAK,kBAAkBL,EAAC,CAAC;AAAE,QAAGD,GAAE,eAAe,YAAUC,MAAKD,GAAE,eAAe,eAAaC,MAAG,QAAMA,GAAE,WAASI,GAAE,KAAKJ,GAAE,QAAQ,IAAE,gBAAeA,MAAG,QAAMA,GAAE,WAAWC,EAAC,KAAGG,GAAE,KAAKJ,GAAE,WAAWC,EAAC,CAAC;AAAE,QAAGF,GAAE,eAAe,YAAUC,MAAKD,GAAE,eAAe,CAAAO,GAAE,KAAK,KAAK,kBAAkBN,EAAC,CAAC;AAAE,WAAO,KAAK,YAAY,OAAO,cAAa,EAAC,MAAKK,IAAE,SAAQC,IAAE,SAAQF,GAAC,CAAC,EAAE,KAAM,CAAC,EAAC,QAAOL,IAAE,YAAWC,IAAE,oBAAmBC,GAAC,OAAK,KAAK,WAAW,SAAOF,IAAEC,OAAI,KAAK,WAAW,SAAS,aAAW,CAACA,GAAE,OAAMA,GAAE,GAAG,IAAG,KAAK,mBAAmBC,EAAC,EAAG;AAAA,EAAC;AAAA,EAAC,mBAAmBF,IAAE;AAAC,WAAM,EAAC,mBAAkBA,GAAE,aAAWA,GAAE,WAAW,IAAI,KAAK,0BAAyB,IAAI,IAAE,CAAC,GAAE,sBAAqBA,GAAE,gBAAcA,GAAE,cAAc,IAAI,KAAK,0BAAyB,IAAI,IAAE,CAAC,GAAE,sBAAqBA,GAAE,gBAAcA,GAAE,cAAc,IAAI,KAAK,0BAAyB,IAAI,IAAE,CAAC,GAAE,sBAAqB,CAAC,GAAE,yBAAwB,CAAC,GAAE,yBAAwB,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,UAAME,KAAE,SAAKF,GAAE,UAAQ,OAAKA,GAAE,SAAO,EAAC,MAAK,QAAO,aAAY,OAAM;AAAE,WAAM,EAAC,UAASA,GAAE,UAAS,UAASA,GAAE,UAAS,OAAME,KAAE,IAAIG,GAAE,qCAAoCH,GAAE,aAAY,EAAC,MAAKA,GAAE,KAAI,CAAC,IAAE,KAAI;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAE;AAAC,UAAK,EAAC,YAAWC,GAAC,IAAED,IAAEE,KAAE,KAAK,0BAA0BF,EAAC;AAAE,WAAOE,KAAE,EAAC,UAASA,GAAE,OAAO,GAAE,YAAWD,GAAC,IAAE,EAAC,YAAWA,GAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAED;AAAE,WAAO,EAAEC,EAAC,IAAE,OAAK,WAASA,GAAE,QAAM,aAAWA,GAAE,OAAK,EAAE,WAAWA,GAAE,MAAM,IAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaD,IAAE;AAAC,SAAK,cAAY,MAAMQ,GAAE,uBAAsB,EAAC,UAAS,IAAI,wBAAwB,IAAE,cAAY,SAAQ,QAAOR,GAAC,CAAC;AAAE,UAAK,EAAC,QAAOC,IAAE,kBAAiBC,IAAE,MAAKI,IAAE,cAAaD,IAAE,eAAcE,IAAE,KAAIE,IAAE,UAASD,IAAE,kBAAiBE,GAAC,IAAE,KAAK,OAAMC,KAAE,eAAa,KAAK,MAAM,SAAS,kBAAkB,GAAEC,KAAE,EAAC,KAAIH,IAAE,kBAAiBC,IAAE,QAAOT,MAAGA,GAAE,IAAK,CAAAD,OAAGA,GAAE,OAAO,CAAE,GAAE,cAAa,EAAE,OAAOK,EAAC,GAAE,MAAKC,IAAE,eAAcC,IAAE,UAASC,KAAEA,GAAE,OAAO,IAAE,MAAK,kBAAiBG,KAAE,OAAKT,MAAGA,GAAE,OAAO,EAAC,GAAE,IAAE,MAAM,KAAK,YAAY,OAAO,QAAOU,IAAE,EAAC,QAAOZ,GAAC,CAAC;AAAE,eAAUa,MAAK,EAAE,SAAS,CAAAd,GAAE,KAAKc,GAAE,SAAQ,EAAC,OAAM,KAAK,OAAM,SAAQA,GAAC,CAAC;AAAE,MAAE,cAAc,UAAQd,GAAE,KAAK,eAAe,EAAE,cAAc,MAAM,6CAA4C,EAAE,aAAa,GAAE,KAAK,aAAW,EAAE,iBAAgB,KAAK,eAAa,EAAE,KAAK,WAAW,MAAK,IAAE;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAE,EAAE,CAAC,EAAED,EAAC,CAAC,GAAE,CAAC;;;ACA5/E,IAAM,IAAEgB,GAAE;AAAE,IAAI,IAAE,cAAcC,GAAEC,GAAEC,GAAEC,GAAE,EAAEC,GAAEC,GAAEF,GAAEH,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYM,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,YAAU,MAAK,KAAK,uBAAqB,MAAK,KAAK,eAAa,MAAK,KAAK,iBAAe,OAAG,KAAK,gBAAc,MAAK,KAAK,SAAO,MAAK,KAAK,cAAY,MAAK,KAAK,aAAW,MAAK,KAAK,eAAa,MAAK,KAAK,OAAK,QAAO,KAAK,gBAAc,MAAG,KAAK,eAAa,MAAK,KAAK,gBAAc,MAAG,KAAK,gBAAc,MAAK,KAAK,uBAAqB,WAAU,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,+BAA6B,MAAG,KAAK,SAAO,IAAI,EAAE,EAAC,OAAM,KAAI,CAAC,GAAE,KAAK,mBAAiB,EAAE,OAAM,KAAK,YAAU,MAAK,KAAK,QAAM,WAAU,KAAK,OAAK,WAAU,KAAK,cAAY,MAAK,KAAK,QAAM;AAAA,EAAI;AAAA,EAAC,UAAS;AAJnqG;AAIoqG,eAAK,WAAL,mBAAa;AAAA,EAAS;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAMD,KAAE,KAAK,eAAe,EAAC,gBAAe,CAAC,SAAS,GAAE,cAAa,MAAE,GAAEC,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,OAAO,KAAKA,EAAC,CAAE,EAAE,KAAM,MAAI;AAAC,WAAK,KAAK,KAAK,OAAO,YAAW,EAAC,QAAO,WAAU,KAAI,KAAK,UAAS,CAAC,GAAE,KAAK,OAAO,CAAC,iBAAgB,UAAS,UAAU,GAAE,SAAS,GAAE,EAAE,KAAK,UAAS,KAAK,WAAW,GAAEC,GAAE,KAAK,UAAS,KAAK,WAAW;AAAA,IAAC,CAAE;AAAE,WAAO,KAAK,oBAAoBF,EAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,SAAO,KAAK,OAAO,eAAa;AAAA,EAAI;AAAA,EAAC,IAAI,qBAAoB;AAAC,WAAO,KAAK,eAAe,sBAAsB,GAAE,KAAK,eAAe,YAAY,GAAE,KAAK,eAAe,YAAY,GAAE,KAAK,eAAe,cAAc,GAAE,KAAK,eAAe,cAAc,IAAG,KAAK,KAAK,oBAAoB,KAAG,KAAG;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,UAAQ,QAAM,KAAK;AAAA,EAAY;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK,MAAI,EAAE,KAAK,GAAG,IAAE;AAAA,EAAI;AAAA,EAAC,IAAI,SAASC,IAAE;AAAC,MAAEA,IAAE,KAAK,WAAW,GAAE,KAAK,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAIA,IAAE;AAAC,QAAG,CAACA,GAAE,QAAO,KAAK,KAAK,KAAK,OAAMA,EAAC;AAAE,UAAMD,KAAE,EAAEC,EAAC;AAAE,SAAK,KAAK,OAAMD,GAAE,IAAI,GAAEA,GAAE,UAAQ,KAAK,mBAAiB,EAAC,GAAG,KAAK,kBAAiB,GAAGA,GAAE,MAAK;AAAA,EAAE;AAAA,EAAC,MAAM,WAAWC,IAAED,IAAE;AAAC,UAAMG,KAAE,MAAM,OAAO,8BAA8B;AAAE,UAAM,KAAK,KAAK;AAAE,UAAMC,KAAE,MAAMD,GAAE,WAAW,MAAK,KAAK,QAAOF,IAAED,EAAC;AAAE,WAAO,KAAK,KAAK,EAAC,QAAO,KAAK,OAAO,WAAW,QAAO,UAAS,KAAK,OAAO,WAAW,SAAQ,GAAE,EAAC,QAAO,WAAU,gBAAe,KAAE,CAAC,GAAEI;AAAA,EAAC;AAAA,EAAC,GAAGH,IAAED,IAAE;AAAC,WAAO,MAAM,GAAGC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBC,IAAE;AAAC,WAAOH,GAAE,MAAKG,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAMA,KAAE,IAAIC,MAAEF,KAAE,KAAK,IAAI,mBAAmB;AAAE,IAAAC,GAAE,iBAAe,MAAGD,MAAGA,GAAE,cAAYC,GAAE,UAAQ,OAAIA,GAAE,YAAU,CAAC,GAAG,GAAEA,GAAE,QAAM,KAAK,wBAAsB;AAAM,UAAK,EAAC,YAAWE,IAAE,YAAWC,GAAC,IAAE;AAAK,WAAOH,GAAE,aAAW,QAAME,MAAG,QAAMC,KAAEA,GAAE,OAAO,CAACD,GAAE,OAAMA,GAAE,IAAI,IAAEC,MAAG,MAAKH;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAED,IAAE;AAAC,QAAIG,IAAEC,KAAE;AAAG,UAAMR,KAAEI,MAAGA,GAAE,SAAQN,KAAEE,MAAGA,GAAE,YAAWE,KAAE,KAAK,eAAaJ,MAAGA,GAAE,KAAK,WAAW;AAAE,WAAO,QAAMI,MAAG,KAAK,UAAQM,KAAE,KAAK,MAAM,KAAM,CAAAJ,OAAGA,GAAE,MAAIF,OAAIK,KAAEH,GAAE,WAASA,GAAE,QAAQC,EAAC,GAAEE,MAAG,gBAAcA,GAAE,SAAOA,KAAE,KAAK,gBAAgBF,EAAC,IAAG,KAAI,IAAGG,MAAGD,OAAIA,KAAE,KAAK,gBAAgBF,EAAC,IAAGE;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,WAAO,KAAK,YAAY,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAED,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,OAAO,cAAcE,GAAE,KAAKD,EAAC,KAAG,KAAK,YAAY,GAAED,EAAC,CAAE,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAGA,MAAA,gBAAAA,GAAG,SAAS,YAAUD,MAAKC,GAAE,SAAS,CAAAD,GAAE,QAAMA,GAAE,cAAY;AAAK,aAAOC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAED,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,OAAO,eAAeE,GAAE,KAAKD,EAAC,KAAG,KAAK,YAAY,GAAED,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBC,IAAED,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,OAAO,kBAAkBE,GAAE,KAAKD,EAAC,KAAG,KAAK,YAAY,GAAED,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAED,IAAE;AAAC,WAAO,KAAK,KAAK,EAAE,KAAM,MAAI,KAAK,OAAO,YAAYE,GAAE,KAAKD,EAAC,KAAG,KAAK,YAAY,GAAED,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAgB;AAAC,QAAG;AAAC,YAAK,EAAC,aAAYC,IAAE,SAAQD,GAAC,IAAE,MAAM,KAAK,OAAO,QAAQ,KAAK,gBAAgB;AAAE,aAAO,EAAEA,EAAC,KAAG,KAAK,KAAKA,IAAE,EAAC,QAAO,WAAU,KAAI,KAAK,WAAU,gBAAe,KAAE,CAAC,GAAEC;AAAA,IAAC,QAAM;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAE;AAAC,QAAG,CAAC,KAAK,OAAO,QAAO;AAAK,QAAID,KAAE;AAAK,WAAO,KAAK,OAAO,KAAM,CAAAG,QAAIA,GAAE,SAAOF,OAAID,KAAEG,GAAE,SAAQ,CAAC,CAACH,GAAG,GAAEA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,sBAAqB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,wCAAuC,OAAM,EAAC,SAAQ,MAAG,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACK,EAAC,GAAE,MAAK,EAAC,MAAK,0BAAyB,OAAM,EAAC,cAAa,MAAG,YAAW,KAAE,GAAE,SAAQ,EAAC,SAAQ,EAAC,MAAK,SAAQ,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,MAAK,SAAQ,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,SAAQ,WAAU,YAAW,YAAY,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,EAAE,KAAI,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAEC,EAAC,CAAC,GAAE,EAAE,WAAU,MAAK,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAEC,EAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,MAAK,4CAA2C,MAAK,EAAC,QAAO,EAAC,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,iCAAgC,OAAM,EAAC,cAAa,MAAG,YAAW,KAAE,GAAE,SAAQ,EAAC,SAAQ,EAAC,MAAK,gBAAe,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,SAAS,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,aAAY,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMV,IAAE,MAAK,EAAC,MAAK,wCAAuC,OAAM,MAAG,SAAQ,EAAC,SAAQ,EAAC,MAAK,uBAAsB,GAAE,aAAY,EAAC,OAAMD,GAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAEY,EAAC,CAAC,GAAE,EAAE,WAAU,gCAA+B,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACX,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACD,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAEa,EAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;", "names": ["y", "f", "e", "t", "r", "x", "w", "s", "o", "i", "u", "n", "l", "d", "p", "a", "s", "c", "o", "n", "p", "a", "t", "e", "x", "r", "i", "y", "w", "v", "m", "l", "f", "k"]}