{"version": 3, "sources": ["../../@arcgis/core/views/interactive/snapping/featureSources/snappingCandidateElevationAlignment.js", "../../@arcgis/core/views/interactive/snapping/featureSources/snappingCandidateElevationFilter.js", "../../@arcgis/core/views/interactive/snapping/featureSources/symbologySnappingCandidates.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import e from\"../../../../core/LRUCache.js\";import{getOrCreateMapValue as t}from\"../../../../core/MapUtils.js\";import{isSome as s,isNone as n}from\"../../../../core/maybe.js\";import{throwIfAborted as o}from\"../../../../core/promiseUtils.js\";import{getMetersPerVerticalUnitForSR as i}from\"../../../../core/unitUtils.js\";import{getMetersPerUnit as a}from\"../../../../symbols/support/unitConversionUtils.js\";function r(e=!1,t){if(e){const{elevationInfo:e,alignPointsInFeatures:s,spatialReference:n}=t;return new l(e,s,n)}return new c}class c{async alignCandidates(e,t){return e}notifyElevationSourceChange(){}}const h=1024;class l{constructor(t,s,n){this._elevationInfo=t,this._alignPointsInFeatures=s,this.spatialReference=n,this._alignmentsCache=new e(h),this._cacheVersion=0,this._metersPerVerticalUnit=i(n)}async alignCandidates(e,t){const n=this._elevationInfo;return s(n)&&\"absolute-height\"===n.mode&&!n.featureExpressionInfo?(this._alignAbsoluteElevationCandidates(e,n),e):this._alignComputedElevationCandidates(e,t)}notifyElevationSourceChange(){this._alignmentsCache.clear(),this._cacheVersion++}_alignAbsoluteElevationCandidates(e,t){const{offset:s,unit:o}=t;if(n(s))return;const i=s*(a(o??\"meters\")/this._metersPerVerticalUnit);for(const n of e)switch(n.type){case\"edge\":n.start.z+=i,n.end.z+=i;continue;case\"vertex\":n.target.z+=i;continue}}async _alignComputedElevationCandidates(e,s){const n=new Map;for(const o of e)t(n,o.objectId,p).push(o);const[i,a,r]=this._prepareQuery(n),c=await this._alignPointsInFeatures(i,s);o(s);if(r!==this._cacheVersion)return this._alignComputedElevationCandidates(e,s);this._applyCacheAndResponse(i,c,a);const{drapedObjectIds:h,failedObjectIds:l}=c,d=[];for(const t of e){const{objectId:e}=t;h.has(e)&&\"edge\"===t.type&&(t.draped=!0),l.has(e)||d.push(t)}return d}_prepareQuery(e){const t=[],s=[];for(const[n,o]of e){const e=[];for(const t of o)this._addToQueriesOrCachedResult(n,t.target,e,s),\"edge\"===t.type&&(this._addToQueriesOrCachedResult(n,t.start,e,s),this._addToQueriesOrCachedResult(n,t.end,e,s));0!==e.length&&t.push({objectId:n,points:e})}return[t,s,this._cacheVersion]}_addToQueriesOrCachedResult(e,t,n,o){const i=u(e,t),a=this._alignmentsCache.get(i);s(a)?o.push(new d(t,a)):n.push(t)}_applyCacheAndResponse(e,{elevations:t,drapedObjectIds:s,failedObjectIds:n},o){for(const r of o)r.apply();let i=0;const a=this._alignmentsCache;for(const{objectId:r,points:c}of e){if(n.has(r)){i+=c.length;continue}const e=!s.has(r);for(const s of c){const n=u(r,s),o=t[i++];s.z=o,e&&a.put(n,o,1)}}}}class d{constructor(e,t){this.point=e,this.z=t}apply(){this.point.z=this.z}}function u(e,{x:t,y:s,z:n}){return`${e}-${t}-${s}-${n??0}}`}function p(){return[]}export{r as getSnappingCandidateElevationAligner};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";class t{filter(t,n){return n}notifyElevationSourceChange(){}}class n{filter(t,n){const{point:r,distance:c}=t,{z:i}=r;if(!(null!=i))return n;if(0===n.length)return n;const o=s(c),u=this._updateCandidatesTo3D(n,r,o).filter(e);return u.sort(a),u}_updateCandidatesTo3D(t,n,e){for(const r of t)switch(r.type){case\"edge\":c(r,n,e);continue;case\"vertex\":o(r,n,e);continue}return t}}function e(t){return t.distance<=1}function r(e=!1){return e?new n:new t}function c(t,n,{x:e,y:r,z:c}){const{start:o,end:s,target:a}=t;t.draped||i(a,n,o,s);const u=(n.x-a.x)/e,d=(n.y-a.y)/r,f=(n.z-a.z)/c;t.distance=Math.sqrt(u*u+d*d+f*f)}function i(t,n,e,r){const c=r.x-e.x,i=r.y-e.y,o=r.z-e.z,s=c*c+i*i+o*o,a=(n.x-e.x)*c+(n.y-e.y)*i+o*(n.z-e.z),u=Math.min(1,Math.max(0,a/s)),d=e.x+c*u,f=e.y+i*u,x=e.z+o*u;t.x=d,t.y=f,t.z=x}function o(t,n,{x:e,y:r,z:c}){const{target:i}=t,o=(n.x-i.x)/e,s=(n.y-i.y)/r,a=(n.z-i.z)/c,u=Math.sqrt(o*o+s*s+a*a);t.distance=u}function s(t){return\"number\"==typeof t?{x:t,y:t,z:t}:t}function a(t,n){return t.distance-n.distance}export{r as getSnappingCandidateElevationFilter};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../core/has.js\";import{clone as t}from\"../../../../core/lang.js\";import e from\"../../../../core/LRUCache.js\";import{throwIfAborted as s}from\"../../../../core/promiseUtils.js\";import{numericHash as o}from\"../../../../core/string.js\";function n(t=!1,e){return t?new i(e):new c}class c{async fetch(){return[]}notifySymbologyChange(){}}const r=1024;class i{constructor(t){this._getSymbologyCandidates=t,this._candidatesCache=new e(r),this._cacheVersion=0}async fetch(e,o){if(0===e.length)return[];const n=[],c=[],r=this._candidatesCache;for(const s of e){const e=a(s),o=r.get(e);if(o)for(const s of o)c.push(t(s));else n.push(s),r.put(e,[],1)}if(0===n.length)return c;const i=this._cacheVersion,{candidates:h,sourceCandidateIndices:d}=await this._getSymbologyCandidates(n,o);s(o);if(i!==this._cacheVersion)return this.fetch(e,o);const f=[],{length:g}=h;for(let s=0;s<g;++s){const e=h[s],o=a(n[d[s]]),c=r.get(o);c.push(e),r.put(o,c,c.length),f.push(t(e))}return c.concat(f)}notifySymbologyChange(){this._candidatesCache.clear(),this._cacheVersion++}}function a(t){switch(t.type){case\"vertex\":{const{objectId:e,target:s}=t,n=`${e}-vertex-${s.x}-${s.y}-${s.z??0}`;return o(n).toString()}case\"edge\":{const{objectId:e,start:s,end:n}=t,c=`${e}-edge-${s.x}-${s.y}-${s.z??0}-to-${n.x}-${n.y}-${n.z??0}`;return o(c).toString()}default:return\"\"}}export{n as getSymbologySnappingCandidatesFetcher};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAIob,SAASA,GAAEC,KAAE,OAAGC,IAAE;AAAC,MAAGD,IAAE;AAAC,UAAK,EAAC,eAAcA,IAAE,uBAAsBE,IAAE,kBAAiBC,GAAC,IAAEF;AAAE,WAAO,IAAI,EAAED,IAAEE,IAAEC,EAAC;AAAA,EAAC;AAAC,SAAO,IAAIC;AAAC;AAAC,IAAMA,KAAN,MAAO;AAAA,EAAC,MAAM,gBAAgBJ,IAAEC,IAAE;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE;AAAK,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,iBAAeF,IAAE,KAAK,yBAAuBC,IAAE,KAAK,mBAAiBC,IAAE,KAAK,mBAAiB,IAAI,EAAE,CAAC,GAAE,KAAK,gBAAc,GAAE,KAAK,yBAAuB,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBH,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK;AAAe,WAAO,EAAEA,EAAC,KAAG,sBAAoBA,GAAE,QAAM,CAACA,GAAE,yBAAuB,KAAK,kCAAkCH,IAAEG,EAAC,GAAEH,MAAG,KAAK,kCAAkCA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAC,SAAK,iBAAiB,MAAM,GAAE,KAAK;AAAA,EAAe;AAAA,EAAC,kCAAkCD,IAAEC,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,MAAKG,GAAC,IAAEJ;AAAE,QAAG,EAAEC,EAAC,EAAE;AAAO,UAAMI,KAAEJ,MAAGH,GAAEM,MAAG,QAAQ,IAAE,KAAK;AAAwB,eAAUF,MAAKH,GAAE,SAAOG,GAAE,MAAK;AAAA,MAAC,KAAI;AAAO,QAAAA,GAAE,MAAM,KAAGG,IAAEH,GAAE,IAAI,KAAGG;AAAE;AAAA,MAAS,KAAI;AAAS,QAAAH,GAAE,OAAO,KAAGG;AAAE;AAAA,IAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,kCAAkCN,IAAEE,IAAE;AAAC,UAAMC,KAAE,oBAAI;AAAI,eAAUE,MAAKL,GAAE,CAAAD,GAAEI,IAAEE,GAAE,UAASE,EAAC,EAAE,KAAKF,EAAC;AAAE,UAAK,CAACC,IAAEE,IAAET,EAAC,IAAE,KAAK,cAAcI,EAAC,GAAEC,KAAE,MAAM,KAAK,uBAAuBE,IAAEJ,EAAC;AAAE,MAAEA,EAAC;AAAE,QAAGH,OAAI,KAAK,cAAc,QAAO,KAAK,kCAAkCC,IAAEE,EAAC;AAAE,SAAK,uBAAuBI,IAAEF,IAAEI,EAAC;AAAE,UAAK,EAAC,iBAAgBC,IAAE,iBAAgBC,GAAC,IAAEN,IAAEO,KAAE,CAAC;AAAE,eAAUV,MAAKD,IAAE;AAAC,YAAK,EAAC,UAASA,GAAC,IAAEC;AAAE,MAAAQ,GAAE,IAAIT,EAAC,KAAG,WAASC,GAAE,SAAOA,GAAE,SAAO,OAAIS,GAAE,IAAIV,EAAC,KAAGW,GAAE,KAAKV,EAAC;AAAA,IAAC;AAAC,WAAOU;AAAA,EAAC;AAAA,EAAC,cAAcX,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,eAAS,CAACC,IAAEE,EAAC,KAAIL,IAAE;AAAC,YAAMA,KAAE,CAAC;AAAE,iBAAUC,MAAKI,GAAE,MAAK,4BAA4BF,IAAEF,GAAE,QAAOD,IAAEE,EAAC,GAAE,WAASD,GAAE,SAAO,KAAK,4BAA4BE,IAAEF,GAAE,OAAMD,IAAEE,EAAC,GAAE,KAAK,4BAA4BC,IAAEF,GAAE,KAAID,IAAEE,EAAC;AAAG,YAAIF,GAAE,UAAQC,GAAE,KAAK,EAAC,UAASE,IAAE,QAAOH,GAAC,CAAC;AAAA,IAAC;AAAC,WAAM,CAACC,IAAEC,IAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,4BAA4BF,IAAEC,IAAEE,IAAEE,IAAE;AAAC,UAAMC,KAAE,EAAEN,IAAEC,EAAC,GAAEO,KAAE,KAAK,iBAAiB,IAAIF,EAAC;AAAE,MAAEE,EAAC,IAAEH,GAAE,KAAK,IAAI,EAAEJ,IAAEO,EAAC,CAAC,IAAEL,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBD,IAAE,EAAC,YAAWC,IAAE,iBAAgBC,IAAE,iBAAgBC,GAAC,GAAEE,IAAE;AAAC,eAAUN,MAAKM,GAAE,CAAAN,GAAE,MAAM;AAAE,QAAIO,KAAE;AAAE,UAAME,KAAE,KAAK;AAAiB,eAAS,EAAC,UAAST,IAAE,QAAOK,GAAC,KAAIJ,IAAE;AAAC,UAAGG,GAAE,IAAIJ,EAAC,GAAE;AAAC,QAAAO,MAAGF,GAAE;AAAO;AAAA,MAAQ;AAAC,YAAMJ,KAAE,CAACE,GAAE,IAAIH,EAAC;AAAE,iBAAUG,MAAKE,IAAE;AAAC,cAAMD,KAAE,EAAEJ,IAAEG,EAAC,GAAEG,KAAEJ,GAAEK,IAAG;AAAE,QAAAJ,GAAE,IAAEG,IAAEL,MAAGQ,GAAE,IAAIL,IAAEE,IAAE,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYL,IAAEC,IAAE;AAAC,SAAK,QAAMD,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,IAAE,KAAK;AAAA,EAAC;AAAC;AAAC,SAAS,EAAED,IAAE,EAAC,GAAEC,IAAE,GAAEC,IAAE,GAAEC,GAAC,GAAE;AAAC,SAAM,GAAGH,EAAC,IAAIC,EAAC,IAAIC,EAAC,IAAIC,MAAG,CAAC;AAAG;AAAC,SAASI,KAAG;AAAC,SAAM,CAAC;AAAC;;;ACAtoF,IAAMK,KAAN,MAAO;AAAA,EAAC,OAAOA,IAAEC,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,8BAA6B;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,OAAOD,IAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,UAASC,GAAC,IAAEH,IAAE,EAAC,GAAEI,GAAC,IAAEF;AAAE,QAAG,EAAE,QAAME,IAAG,QAAOH;AAAE,QAAG,MAAIA,GAAE,OAAO,QAAOA;AAAE,UAAMI,KAAE,EAAEF,EAAC,GAAEG,KAAE,KAAK,sBAAsBL,IAAEC,IAAEG,EAAC,EAAE,OAAOE,EAAC;AAAE,WAAOD,GAAE,KAAK,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBN,IAAEC,IAAEM,IAAE;AAAC,eAAUL,MAAKF,GAAE,SAAOE,GAAE,MAAK;AAAA,MAAC,KAAI;AAAO,QAAAC,GAAED,IAAED,IAAEM,EAAC;AAAE;AAAA,MAAS,KAAI;AAAS,UAAEL,IAAED,IAAEM,EAAC;AAAE;AAAA,IAAQ;AAAC,WAAOP;AAAA,EAAC;AAAC;AAAC,SAASO,GAAEP,IAAE;AAAC,SAAOA,GAAE,YAAU;AAAC;AAAC,SAASE,GAAEK,KAAE,OAAG;AAAC,SAAOA,KAAE,IAAI,MAAE,IAAIP;AAAC;AAAC,SAASG,GAAEH,IAAEC,IAAE,EAAC,GAAEM,IAAE,GAAEL,IAAE,GAAEC,GAAC,GAAE;AAAC,QAAK,EAAC,OAAME,IAAE,KAAIG,IAAE,QAAOC,GAAC,IAAET;AAAE,EAAAA,GAAE,UAAQ,EAAES,IAAER,IAAEI,IAAEG,EAAC;AAAE,QAAMF,MAAGL,GAAE,IAAEQ,GAAE,KAAGF,IAAEG,MAAGT,GAAE,IAAEQ,GAAE,KAAGP,IAAES,MAAGV,GAAE,IAAEQ,GAAE,KAAGN;AAAE,EAAAH,GAAE,WAAS,KAAK,KAAKM,KAAEA,KAAEI,KAAEA,KAAEC,KAAEA,EAAC;AAAC;AAAC,SAAS,EAAEX,IAAEC,IAAEM,IAAEL,IAAE;AAAC,QAAMC,KAAED,GAAE,IAAEK,GAAE,GAAEH,KAAEF,GAAE,IAAEK,GAAE,GAAEF,KAAEH,GAAE,IAAEK,GAAE,GAAEC,KAAEL,KAAEA,KAAEC,KAAEA,KAAEC,KAAEA,IAAEI,MAAGR,GAAE,IAAEM,GAAE,KAAGJ,MAAGF,GAAE,IAAEM,GAAE,KAAGH,KAAEC,MAAGJ,GAAE,IAAEM,GAAE,IAAGD,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAEG,KAAED,EAAC,CAAC,GAAEE,KAAEH,GAAE,IAAEJ,KAAEG,IAAEK,KAAEJ,GAAE,IAAEH,KAAEE,IAAE,IAAEC,GAAE,IAAEF,KAAEC;AAAE,EAAAN,GAAE,IAAEU,IAAEV,GAAE,IAAEW,IAAEX,GAAE,IAAE;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE,EAAC,GAAEM,IAAE,GAAEL,IAAE,GAAEC,GAAC,GAAE;AAAC,QAAK,EAAC,QAAOC,GAAC,IAAEJ,IAAEK,MAAGJ,GAAE,IAAEG,GAAE,KAAGG,IAAEC,MAAGP,GAAE,IAAEG,GAAE,KAAGF,IAAEO,MAAGR,GAAE,IAAEG,GAAE,KAAGD,IAAEG,KAAE,KAAK,KAAKD,KAAEA,KAAEG,KAAEA,KAAEC,KAAEA,EAAC;AAAE,EAAAT,GAAE,WAASM;AAAC;AAAC,SAAS,EAAEN,IAAE;AAAC,SAAM,YAAU,OAAOA,KAAE,EAAC,GAAEA,IAAE,GAAEA,IAAE,GAAEA,GAAC,IAAEA;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOD,GAAE,WAASC,GAAE;AAAQ;;;ACAzyB,SAASW,GAAEC,KAAE,OAAGC,IAAE;AAAC,SAAOD,KAAE,IAAIE,GAAED,EAAC,IAAE,IAAIE;AAAC;AAAC,IAAMA,KAAN,MAAO;AAAA,EAAC,MAAM,QAAO;AAAC,WAAM,CAAC;AAAA,EAAC;AAAA,EAAC,wBAAuB;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE;AAAK,IAAMF,KAAN,MAAO;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,0BAAwBA,IAAE,KAAK,mBAAiB,IAAI,EAAEI,EAAC,GAAE,KAAK,gBAAc;AAAA,EAAC;AAAA,EAAC,MAAM,MAAMH,IAAEI,IAAE;AAAC,QAAG,MAAIJ,GAAE,OAAO,QAAM,CAAC;AAAE,UAAMF,KAAE,CAAC,GAAEI,KAAE,CAAC,GAAEC,KAAE,KAAK;AAAiB,eAAUE,MAAKL,IAAE;AAAC,YAAMA,KAAEM,GAAED,EAAC,GAAED,KAAED,GAAE,IAAIH,EAAC;AAAE,UAAGI,GAAE,YAAUC,MAAKD,GAAE,CAAAF,GAAE,KAAK,EAAEG,EAAC,CAAC;AAAA,UAAO,CAAAP,GAAE,KAAKO,EAAC,GAAEF,GAAE,IAAIH,IAAE,CAAC,GAAE,CAAC;AAAA,IAAC;AAAC,QAAG,MAAIF,GAAE,OAAO,QAAOI;AAAE,UAAMD,KAAE,KAAK,eAAc,EAAC,YAAWM,IAAE,wBAAuBC,GAAC,IAAE,MAAM,KAAK,wBAAwBV,IAAEM,EAAC;AAAE,MAAEA,EAAC;AAAE,QAAGH,OAAI,KAAK,cAAc,QAAO,KAAK,MAAMD,IAAEI,EAAC;AAAE,UAAMK,KAAE,CAAC,GAAE,EAAC,QAAO,EAAC,IAAEF;AAAE,aAAQF,KAAE,GAAEA,KAAE,GAAE,EAAEA,IAAE;AAAC,YAAML,KAAEO,GAAEF,EAAC,GAAED,KAAEE,GAAER,GAAEU,GAAEH,EAAC,CAAC,CAAC,GAAEH,KAAEC,GAAE,IAAIC,EAAC;AAAE,MAAAF,GAAE,KAAKF,EAAC,GAAEG,GAAE,IAAIC,IAAEF,IAAEA,GAAE,MAAM,GAAEO,GAAE,KAAK,EAAET,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOE,GAAE,OAAOO,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAuB;AAAC,SAAK,iBAAiB,MAAM,GAAE,KAAK;AAAA,EAAe;AAAC;AAAC,SAASH,GAAEP,IAAE;AAAC,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI,UAAS;AAAC,YAAK,EAAC,UAASC,IAAE,QAAOK,GAAC,IAAEN,IAAED,KAAE,GAAGE,EAAC,WAAWK,GAAE,CAAC,IAAIA,GAAE,CAAC,IAAIA,GAAE,KAAG,CAAC;AAAG,aAAO,EAAEP,EAAC,EAAE,SAAS;AAAA,IAAC;AAAA,IAAC,KAAI,QAAO;AAAC,YAAK,EAAC,UAASE,IAAE,OAAMK,IAAE,KAAIP,GAAC,IAAEC,IAAEG,KAAE,GAAGF,EAAC,SAASK,GAAE,CAAC,IAAIA,GAAE,CAAC,IAAIA,GAAE,KAAG,CAAC,OAAOP,GAAE,CAAC,IAAIA,GAAE,CAAC,IAAIA,GAAE,KAAG,CAAC;AAAG,aAAO,EAAEI,EAAC,EAAE,SAAS;AAAA,IAAC;AAAA,IAAC;AAAQ,aAAM;AAAA,EAAE;AAAC;", "names": ["r", "e", "t", "s", "n", "c", "o", "i", "p", "a", "h", "l", "d", "t", "n", "r", "c", "i", "o", "u", "e", "s", "a", "d", "f", "n", "t", "e", "i", "c", "r", "o", "s", "a", "h", "d", "f"]}