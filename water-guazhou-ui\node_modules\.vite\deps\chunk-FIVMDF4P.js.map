{"version": 3, "sources": ["../../@arcgis/core/symbols/support/defaults.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import{isNone as r}from\"../../core/maybe.js\";import o from\"../SimpleFillSymbol.js\";import m from\"../SimpleLineSymbol.js\";import e from\"../SimpleMarkerSymbol.js\";import t from\"../TextSymbol.js\";import{defaultPointSymbolJSON as l,defaultPolylineSymbolJSON as i,defaultPolygonSymbolJSON as n,defaultTextSymbolJSON as s,errorPointSymbolJSON as f,errorPolylineSymbolJSON as p,errorPolygonSymbolJSON as S}from\"./defaultsJSON.js\";const c=e.fromJSON(l),u=m.fromJSON(i),a=o.fromJSON(n),y=t.fromJSON(s);function J(o){if(r(o))return null;switch(o.type){case\"mesh\":return null;case\"point\":case\"multipoint\":return c;case\"polyline\":return u;case\"polygon\":case\"extent\":return a}return null}const N=e.fromJSON(f),O=m.fromJSON(p),j=o.fromJSON(S);export{c as defaultPointSymbol2D,a as defaultPolygonSymbol2D,u as defaultPolylineSymbol2D,y as defaultTextSymbol2D,N as errorPointSymbol2D,j as errorPolygonSymbol2D,O as errorPolylineSymbol2D,J as getDefaultSymbol2D};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIic,IAAM,IAAEA,GAAE,SAAS,CAAC;AAApB,IAAsB,IAAE,EAAE,SAAS,CAAC;AAApC,IAAsC,IAAEC,GAAE,SAAS,CAAC;AAApD,IAAsDD,KAAEE,GAAE,SAASC,EAAC;AAAE,SAAS,EAAEC,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAO,aAAO;AAAA,IAAK,KAAI;AAAA,IAAQ,KAAI;AAAa,aAAO;AAAA,IAAE,KAAI;AAAW,aAAO;AAAA,IAAE,KAAI;AAAA,IAAU,KAAI;AAAS,aAAO;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,IAAM,IAAEJ,GAAE,SAAS,CAAC;AAApB,IAAsB,IAAE,EAAE,SAAS,CAAC;AAApC,IAAsC,IAAEC,GAAE,SAAS,CAAC;", "names": ["y", "S", "m", "t", "o"]}