import {
  g
} from "./chunk-VD4TYNIV.js";
import "./chunk-XX6IKIRW.js";
import {
  l as l2,
  m,
  s as s2,
  u as u2
} from "./chunk-WYBCZXCQ.js";
import {
  E,
  L as L2
} from "./chunk-HG37JYSR.js";
import "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import "./chunk-SJMI5Q5M.js";
import "./chunk-PCLDCFRI.js";
import "./chunk-ZJC3GHA7.js";
import {
  i
} from "./chunk-RR74IWZB.js";
import {
  p as p2
} from "./chunk-KTB2COPC.js";
import "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-77ZF73NA.js";
import "./chunk-55WN4LCX.js";
import {
  c as c2
} from "./chunk-VSFGOST3.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-P37TUI4J.js";
import {
  An,
  mn
} from "./chunk-UYAKJRPP.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-6NE6A2GD.js";
import "./chunk-FZ7BG3VX.js";
import {
  t as t4
} from "./chunk-NGPCXWDX.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-FBVKALLT.js";
import {
  d
} from "./chunk-VJW7RCN7.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-FSNYK4TH.js";
import "./chunk-3WUI7ZKG.js";
import {
  U,
  a as a2
} from "./chunk-QUHG7NMD.js";
import "./chunk-D7S3BWBP.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-VNYCO3JG.js";
import "./chunk-57XIOVP5.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-QMNV7QQK.js";
import "./chunk-TNGL5OFU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import {
  U as U2
} from "./chunk-AW4AS2UW.js";
import "./chunk-WXFAAYJL.js";
import "./chunk-7THWOTCY.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-ZACBBT3Y.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-3WCHZJQK.js";
import {
  v
} from "./chunk-X7FOCGBC.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  c
} from "./chunk-XVA5SA7P.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l,
  u2 as u
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e,
  t2 as t3
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  t2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t,
  x
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/SceneModification.js
var m2;
var y2 = m2 = class extends l {
  constructor(e2) {
    super(e2), this.geometry = null, this.type = "clip";
  }
  writeGeometry(e2, r3, o2, s3) {
    if (s3.layer && s3.layer.spatialReference && !s3.layer.spatialReference.equals(this.geometry.spatialReference)) {
      if (!An(e2.spatialReference, s3.layer.spatialReference)) return void (s3 && s3.messages && s3.messages.push(new t2("scenemodification:unsupported", "Scene modifications with incompatible spatial references are not supported", { modification: this, spatialReference: s3.layer.spatialReference, context: s3 })));
      const p3 = new v();
      mn(e2, p3, s3.layer.spatialReference), r3[o2] = p3.toJSON(s3);
    } else r3[o2] = e2.toJSON(s3);
    delete r3[o2].spatialReference;
  }
  clone() {
    return new m2({ geometry: p(this.geometry), type: this.type });
  }
};
e([y({ type: v }), g()], y2.prototype, "geometry", void 0), e([r2(["web-scene", "portal-item"], "geometry")], y2.prototype, "writeGeometry", null), e([y({ type: ["clip", "mask", "replace"], nonNullable: true }), g()], y2.prototype, "type", void 0), y2 = m2 = e([a("esri.layers.support.SceneModification")], y2);
var f = y2;

// node_modules/@arcgis/core/layers/support/SceneModifications.js
var a3;
var n = a3 = class extends u(j.ofType(f)) {
  constructor(r3) {
    super(r3), this.url = null;
  }
  clone() {
    return new a3({ url: this.url, items: this.items.map((r3) => r3.clone()) });
  }
  toJSON(r3) {
    return this.toArray().map((o2) => o2.toJSON(r3)).filter((r4) => !!r4.geometry);
  }
  static fromJSON(r3, o2) {
    const t5 = new a3();
    for (const e2 of r3) t5.add(f.fromJSON(e2, o2));
    return t5;
  }
  static async fromUrl(r3, t5, e2) {
    const c3 = { url: L(r3), origin: "service" }, p3 = await U2(r3, { responseType: "json", signal: x(e2, "signal") }), n2 = t5.toJSON(), l4 = [];
    for (const o2 of p3.data) l4.push(f.fromJSON({ ...o2, geometry: { ...o2.geometry, spatialReference: n2 } }, c3));
    return new a3({ url: r3, items: l4 });
  }
};
e([y({ type: String })], n.prototype, "url", void 0), n = a3 = e([a("esri.layers.support.SceneModifications")], n);
var l3 = n;

// node_modules/@arcgis/core/layers/IntegratedMeshLayer.js
var b2 = class extends E(p2(c2(_(t4(O(i(b))))))) {
  constructor(...e2) {
    super(...e2), this._handles = new t3(), this.geometryType = "mesh", this.operationalLayerType = "IntegratedMeshLayer", this.type = "integrated-mesh", this.nodePages = null, this.materialDefinitions = null, this.textureSetDefinitions = null, this.geometryDefinitions = null, this.serviceUpdateTimeStamp = null, this.profile = "mesh-pyramids", this.modifications = null, this._modificationsSource = null, this.elevationInfo = null, this.path = null;
  }
  destroy() {
    this._handles.destroy();
  }
  initialize() {
    this._handles.add(a2(() => this.modifications, "after-changes", () => this.modifications = this.modifications, U));
  }
  normalizeCtorArgs(e2, t5) {
    return "string" == typeof e2 ? { url: e2, ...t5 } : e2;
  }
  readModifications(e2, t5, r3) {
    this._modificationsSource = { url: c(e2, r3), context: r3 };
  }
  async load(e2) {
    return this.addResolvingPromise(this._doLoad(e2)), this;
  }
  async _doLoad(e2) {
    const t5 = x(e2, "signal");
    try {
      await this.loadFromPortal({ supportedTypes: ["Scene Service"] }, e2);
    } catch (r3) {
      w(r3);
    }
    if (await this._fetchService(t5), r(this._modificationsSource)) {
      const t6 = await l3.fromUrl(this._modificationsSource.url, this.spatialReference, e2);
      this.setAtOrigin("modifications", t6, this._modificationsSource.context.origin), this._modificationsSource = null;
    }
    await this._fetchIndexAndUpdateExtent(this.nodePages, t5);
  }
  beforeSave() {
    if (!t(this._modificationsSource)) return this.load().then(() => {
    }, () => {
    });
  }
  async saveAs(e2, t5) {
    return this._debouncedSaveOperations(L2.SAVE_AS, { ...t5, getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "integrated-mesh" }, e2);
  }
  async save() {
    const e2 = { getTypeKeywords: () => this._getTypeKeywords(), portalItemLayerType: "integrated-mesh" };
    return this._debouncedSaveOperations(L2.SAVE, e2);
  }
  validateLayer(e2) {
    if (e2.layerType && "IntegratedMesh" !== e2.layerType) throw new s("integrated-mesh-layer:layer-type-not-supported", "IntegratedMeshLayer does not support this layer type", { layerType: e2.layerType });
    if (isNaN(this.version.major) || isNaN(this.version.minor)) throw new s("layer:service-version-not-supported", "Service version is not supported.", { serviceVersion: this.version.versionString, supportedVersions: "1.x" });
    if (this.version.major > 1) throw new s("layer:service-version-too-new", "Service version is too new.", { serviceVersion: this.version.versionString, supportedVersions: "1.x" });
  }
  _getTypeKeywords() {
    return ["IntegratedMeshLayer"];
  }
};
e([y({ type: String, readOnly: true })], b2.prototype, "geometryType", void 0), e([y({ type: ["show", "hide"] })], b2.prototype, "listMode", void 0), e([y({ type: ["IntegratedMeshLayer"] })], b2.prototype, "operationalLayerType", void 0), e([y({ json: { read: false }, readOnly: true })], b2.prototype, "type", void 0), e([y({ type: s2, readOnly: true })], b2.prototype, "nodePages", void 0), e([y({ type: [l2], readOnly: true })], b2.prototype, "materialDefinitions", void 0), e([y({ type: [u2], readOnly: true })], b2.prototype, "textureSetDefinitions", void 0), e([y({ type: [m], readOnly: true })], b2.prototype, "geometryDefinitions", void 0), e([y({ readOnly: true })], b2.prototype, "serviceUpdateTimeStamp", void 0), e([y({ type: l3 }), g({ origins: ["web-scene", "portal-item"], type: "resource", prefix: "modifications" })], b2.prototype, "modifications", void 0), e([o(["web-scene", "portal-item"], "modifications")], b2.prototype, "readModifications", null), e([y(d)], b2.prototype, "elevationInfo", void 0), e([y({ type: String, json: { origins: { "web-scene": { read: true, write: true }, "portal-item": { read: true, write: true } }, read: false } })], b2.prototype, "path", void 0), b2 = e([a("esri.layers.IntegratedMeshLayer")], b2);
var A = b2;
export {
  A as default
};
//# sourceMappingURL=IntegratedMeshLayer-7W7JHHLT.js.map
