import {
  k
} from "./chunk-MQ2IOGEF.js";
import {
  j3 as j
} from "./chunk-ETY52UBV.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  n
} from "./chunk-7THWOTCY.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  e2
} from "./chunk-C5VMWMBD.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/Graphic.js
function y2(t) {
  if (!t) return null;
  const e3 = {};
  for (const r2 in t) {
    const o = v(t[r2]);
    o && (e3[r2] = o);
  }
  return 0 !== Object.keys(e3).length ? e3 : null;
}
function m(t) {
  if (!r(t)) return null;
  const e3 = {};
  for (const r2 in t) {
    const o = t[r2];
    o && (e3[r2] = o.toJSON());
  }
  return 0 !== Object.keys(e3).length ? e3 : null;
}
var h = class extends i(l) {
  constructor(...t) {
    super(...t), this.isAggregate = false, this.layer = null, this.popupTemplate = null, this.sourceLayer = null, Object.defineProperty(this, "uid", { value: e2(), configurable: true });
  }
  normalizeCtorArgs(t, e3, r2, o) {
    return t && !t.declaredClass ? t : { geometry: t, symbol: e3, attributes: r2, popupTemplate: o };
  }
  set aggregateGeometries(t) {
    const e3 = this._get("aggregateGeometries");
    JSON.stringify(e3) !== JSON.stringify(t) && this._set("aggregateGeometries", t);
  }
  set attributes(t) {
    const e3 = this._get("attributes");
    e3 !== t && (this._set("attributes", t), this._notifyLayer("attributes", e3, t));
  }
  set geometry(t) {
    const e3 = this._get("geometry");
    e3 !== t && (this._set("geometry", t), this._notifyLayer("geometry", e3, t));
  }
  set symbol(t) {
    const e3 = this._get("symbol");
    e3 !== t && (this._set("symbol", t), this._notifyLayer("symbol", e3, t));
  }
  set visible(t) {
    const e3 = this._get("visible");
    e3 !== t && (this._set("visible", t), this._notifyLayer("visible", e3, t));
  }
  getEffectivePopupTemplate(t = false) {
    if (this.popupTemplate) return this.popupTemplate;
    for (const e3 of [this.sourceLayer, this.layer]) if (e3) {
      if ("popupTemplate" in e3 && e3.popupTemplate) return e3.popupTemplate;
      if (t && "defaultPopupTemplate" in e3 && r(e3.defaultPopupTemplate)) return e3.defaultPopupTemplate;
    }
    return null;
  }
  getAttribute(t) {
    var _a;
    return (_a = this.attributes) == null ? void 0 : _a[t];
  }
  setAttribute(t, e3) {
    if (this.attributes) {
      const r2 = this.getAttribute(t);
      this.attributes[t] = e3, this._notifyLayer("attributes", r2, e3, t);
    } else this.attributes = { [t]: e3 }, this._notifyLayer("attributes", void 0, e3, t);
  }
  getObjectId() {
    return this.sourceLayer && "objectIdField" in this.sourceLayer && this.sourceLayer.objectIdField ? this.getAttribute(this.sourceLayer.objectIdField) : null;
  }
  toJSON() {
    return { aggregateGeometries: m(this.aggregateGeometries), geometry: r(this.geometry) ? this.geometry.toJSON() : null, symbol: r(this.symbol) ? this.symbol.toJSON() : null, attributes: { ...this.attributes }, popupTemplate: this.popupTemplate && this.popupTemplate.toJSON() };
  }
  notifyGeometryChanged() {
    this._notifyLayer("geometry", this.geometry, this.geometry);
  }
  notifyMeshTransformChanged() {
    r(this.geometry) && "mesh" === this.geometry.type && this._notifyLayer("transform", this.geometry.transform, this.geometry.transform);
  }
  _notifyLayer(t, e3, r2, o) {
    if (!this.layer || !("graphicChanged" in this.layer)) return;
    const s = { graphic: this, property: t, oldValue: e3, newValue: r2 };
    "attributes" === t && (s.attributeName = o), this.layer.graphicChanged(s);
  }
};
e([y({ value: null, json: { read: y2 } })], h.prototype, "aggregateGeometries", null), e([y({ value: null })], h.prototype, "attributes", null), e([y({ value: null, types: n, json: { read: v } })], h.prototype, "geometry", null), e([y({ type: Boolean })], h.prototype, "isAggregate", void 0), e([y({ clonable: "reference" })], h.prototype, "layer", void 0), e([y({ type: k })], h.prototype, "popupTemplate", void 0), e([y({ clonable: "reference" })], h.prototype, "sourceLayer", void 0), e([y({ value: null, types: j })], h.prototype, "symbol", null), e([y({ type: Boolean, value: true })], h.prototype, "visible", null), h = e([a("esri.Graphic")], h), function(t) {
  t.generateUID = e2;
}(h || (h = {}));
var g = h;

export {
  g
};
//# sourceMappingURL=chunk-TLKX5XIJ.js.map
