import {
  c,
  d,
  f,
  h,
  i,
  l,
  u,
  w
} from "./chunk-SQH4JCBU.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";
export {
  u as addOrUpdateResource,
  h as contentToBlob,
  c as fetchResources,
  f as getSiblingOfSameType,
  w as getSiblingOfSameTypeI,
  l as removeAllResources,
  i as removeResource,
  d as splitPrefixFileNameAndExtension
};
//# sourceMappingURL=resourceUtils-S7AWUQFB.js.map
