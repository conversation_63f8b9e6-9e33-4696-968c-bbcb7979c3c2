{"version": 3, "sources": ["../../@arcgis/core/views/support/euclideanAreaMeasurementUtils.js", "../../@arcgis/core/views/support/geodesicAreaMeasurementUtils.js", "../../@arcgis/core/views/support/automaticAreaMeasurementUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../core/maybe.js\";import{createArea as o}from\"../../core/quantityUtils.js\";import{areaUnitFromSpatialReference as r}from\"../../core/unitUtils.js\";import{e as n}from\"../../chunks/earcut.js\";import{s as e,z as s,p as i}from\"../../chunks/vec3.js\";import{c}from\"../../chunks/vec3f64.js\";import{projectVectorToVector as f}from\"../../geometry/projection.js\";import{fromPoints as u,wrap as l,normal as m,projectPointLocal as p}from\"../../geometry/support/plane.js\";import{areaPoints2d as a}from\"../../geometry/support/triangle.js\";import{computeEuclideanMeasurementSR as g}from\"./measurementUtils.js\";function h(t,o=U()){return y(t,o)}function j(t,o=U()){return y(t,o,!1)}function y(n,h,j=n.hasZ){const y=g(n.spatialReference),U=r(y);if(t(U))return null;const C=(t,o)=>!(o.length<2)&&(e(t,o[0],o[1],j&&o[2]||0),!0);let R=0;for(const t of n.rings){const o=t.length;if(o<3)continue;const{positionsWorldCoords:r}=h;for(;r.length<o;)r.push(c());const g=d,j=e(k,0,0,0),U=1/o;for(let e=0;e<o;e++){if(!C(g,t[e]))return null;if(!f(g,n.spatialReference,r[e],y))return null;s(j,j,r[e],U)}const W=u(r[0],r[1],j,l());if(0===i(m(W)))continue;for(let t=0;t<o;t++)p(W,j,r[t],r[t]);const b=v(r);for(let t=0;t<b.length;t+=3)R+=a(r[b[t]],r[b[t+1]],r[b[t+2]])}return o(R,U)}const d=c(),k=c();function U(){return{positionsWorldCoords:[]}}function v(t){return n(C(t),[],2)}function C(t){const o=new Float64Array(2*t.length);for(let r=0;r<t.length;++r){const n=t[r],e=2*r;o[e+0]=n[0],o[e+1]=n[1]}return o}export{y as computeEuclideanPlanarArea,U as createEuclideanPlanarAreaCache,j as euclideanHorizontalPlanarArea,h as euclideanPlanarArea,v as triangulate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../geometry.js\";import{createArea as e}from\"../../core/quantityUtils.js\";import{geodesicArea as r}from\"../../geometry/geometryEngine.js\";import{projectPolygonToWGS84ComparableLonLat as t}from\"../../geometry/projection.js\";import{geodesicAreas as s}from\"../../geometry/support/geodesicUtils.js\";import{geodesicMeasure as o}from\"./geodesicMeasurementUtils.js\";import n from\"../../geometry/SpatialReference.js\";function m(e){const{spatialReference:r}=e;return o(r,i,a,u,e)}function i(r){return e(Math.abs(s([r],\"square-meters\")[0]),\"square-meters\")}function a(t){try{return e(Math.abs(r(t,\"square-meters\")),\"square-meters\")}catch(s){return null}}function u(r){const o=[];return t(r,o)?e(Math.abs(s([{type:\"polygon\",rings:o,spatialReference:n.WGS84}],\"square-meters\")[0]),\"square-meters\"):null}export{m as geodesicArea};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../core/maybe.js\";import{euclideanHorizontalPlanarArea as r,euclideanPlanarArea as o,createEuclideanPlanarAreaCache as t}from\"./euclideanAreaMeasurementUtils.js\";import{geodesicArea as n}from\"./geodesicAreaMeasurementUtils.js\";function i(i,u,m=t()){if(\"on-the-ground\"===u){const o=n(i);return e(o)?o:r(i,m)}return o(i,m)}function u(e,r=t()){return i(e,\"on-the-ground\",r)}export{u as autoArea2D,i as autoAreaByElevationMode};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6mB,SAAS,EAAEA,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAO,EAAED,IAAEC,EAAC;AAAC;AAAC,SAASC,GAAEF,IAAEC,KAAE,EAAE,GAAE;AAAC,SAAO,EAAED,IAAEC,IAAE,KAAE;AAAC;AAAC,SAAS,EAAEE,IAAEC,IAAEF,KAAEC,GAAE,MAAK;AAAC,QAAME,KAAE,EAAEF,GAAE,gBAAgB,GAAEG,KAAE,GAAED,EAAC;AAAE,MAAG,EAAEC,EAAC,EAAE,QAAO;AAAK,QAAMC,KAAE,CAACP,IAAEC,OAAI,EAAEA,GAAE,SAAO,OAAK,EAAED,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,MAAGD,GAAE,CAAC,KAAG,CAAC,GAAE;AAAI,MAAI,IAAE;AAAE,aAAUD,MAAKG,GAAE,OAAM;AAAC,UAAMF,KAAED,GAAE;AAAO,QAAGC,KAAE,EAAE;AAAS,UAAK,EAAC,sBAAqBO,GAAC,IAAEJ;AAAE,WAAKI,GAAE,SAAOP,KAAG,CAAAO,GAAE,KAAK,EAAE,CAAC;AAAE,UAAM,IAAE,GAAEN,KAAE,EAAEO,IAAE,GAAE,GAAE,CAAC,GAAEH,KAAE,IAAEL;AAAE,aAAQ,IAAE,GAAE,IAAEA,IAAE,KAAI;AAAC,UAAG,CAACM,GAAE,GAAEP,GAAE,CAAC,CAAC,EAAE,QAAO;AAAK,UAAG,CAAC,GAAE,GAAEG,GAAE,kBAAiBK,GAAE,CAAC,GAAEH,EAAC,EAAE,QAAO;AAAK,QAAEH,IAAEA,IAAEM,GAAE,CAAC,GAAEF,EAAC;AAAA,IAAC;AAAC,UAAM,IAAE,EAAEE,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEN,IAAE,EAAE,CAAC;AAAE,QAAG,MAAI,EAAE,EAAE,CAAC,CAAC,EAAE;AAAS,aAAQF,KAAE,GAAEA,KAAEC,IAAED,KAAI,GAAE,GAAEE,IAAEM,GAAER,EAAC,GAAEQ,GAAER,EAAC,CAAC;AAAE,UAAM,IAAEU,GAAEF,EAAC;AAAE,aAAQR,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAG,EAAE,MAAG,EAAEQ,GAAE,EAAER,EAAC,CAAC,GAAEQ,GAAE,EAAER,KAAE,CAAC,CAAC,GAAEQ,GAAE,EAAER,KAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOC,GAAE,GAAEK,EAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAYG,KAAE,EAAE;AAAE,SAAS,IAAG;AAAC,SAAM,EAAC,sBAAqB,CAAC,EAAC;AAAC;AAAC,SAASC,GAAEV,IAAE;AAAC,SAAOQ,GAAE,EAAER,EAAC,GAAE,CAAC,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAE,IAAI,aAAa,IAAED,GAAE,MAAM;AAAE,WAAQQ,KAAE,GAAEA,KAAER,GAAE,QAAO,EAAEQ,IAAE;AAAC,UAAML,KAAEH,GAAEQ,EAAC,GAAE,IAAE,IAAEA;AAAE,IAAAP,GAAE,IAAE,CAAC,IAAEE,GAAE,CAAC,GAAEF,GAAE,IAAE,CAAC,IAAEE,GAAE,CAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;;;ACA5jC,SAAS,EAAE,GAAE;AAAC,QAAK,EAAC,kBAAiBU,GAAC,IAAE;AAAE,SAAOA,GAAEA,IAAEC,IAAE,GAAE,GAAE,CAAC;AAAC;AAAC,SAASA,GAAED,IAAE;AAAC,SAAOE,GAAE,KAAK,IAAI,EAAE,CAACF,EAAC,GAAE,eAAe,EAAE,CAAC,CAAC,GAAE,eAAe;AAAC;AAAC,SAAS,EAAEG,IAAE;AAAC,MAAG;AAAC,WAAOD,GAAE,KAAK,IAAI,EAAEC,IAAE,eAAe,CAAC,GAAE,eAAe;AAAA,EAAC,SAAO,GAAE;AAAC,WAAO;AAAA,EAAI;AAAC;AAAC,SAAS,EAAEH,IAAE;AAAC,QAAME,KAAE,CAAC;AAAE,SAAO,GAAEF,IAAEE,EAAC,IAAEA,GAAE,KAAK,IAAI,EAAE,CAAC,EAAC,MAAK,WAAU,OAAMA,IAAE,kBAAiB,EAAE,MAAK,CAAC,GAAE,eAAe,EAAE,CAAC,CAAC,GAAE,eAAe,IAAE;AAAI;;;ACAjiB,SAASE,GAAEA,IAAEC,IAAEC,KAAE,EAAE,GAAE;AAAC,MAAG,oBAAkBD,IAAE;AAAC,UAAME,KAAE,EAAEH,EAAC;AAAE,WAAO,EAAEG,EAAC,IAAEA,KAAEC,GAAEJ,IAAEE,EAAC;AAAA,EAAC;AAAC,SAAO,EAAEF,IAAEE,EAAC;AAAC;AAAC,SAASD,GAAE,GAAEI,KAAE,EAAE,GAAE;AAAC,SAAOL,GAAE,GAAE,iBAAgBK,EAAC;AAAC;", "names": ["t", "o", "j", "n", "h", "y", "U", "C", "r", "k", "v", "r", "i", "o", "t", "i", "u", "m", "o", "j", "r"]}