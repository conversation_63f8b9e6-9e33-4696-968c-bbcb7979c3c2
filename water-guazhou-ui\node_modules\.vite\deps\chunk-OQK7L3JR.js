import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/mixins/CustomParametersMixin.js
var o = (o2) => {
  let t = class extends o2 {
    constructor() {
      super(...arguments), this.customParameters = null;
    }
  };
  return e([y({ type: Object, json: { write: { overridePolicy: (r) => ({ enabled: !!(r && Object.keys(r).length > 0) }) } } })], t.prototype, "customParameters", void 0), t = e([a("esri.layers.mixins.CustomParametersMixin")], t), t;
};

export {
  o
};
//# sourceMappingURL=chunk-OQK7L3JR.js.map
