import {
  i,
  r,
  s as s2
} from "./chunk-KUBJOT5K.js";
import {
  u as u2
} from "./chunk-I7WHRVHF.js";
import {
  f,
  l,
  s,
  u,
  y
} from "./chunk-ZACBBT3Y.js";
import {
  p
} from "./chunk-X7FOCGBC.js";
import {
  c
} from "./chunk-SRBBUKOI.js";
import {
  R,
  k
} from "./chunk-JXLVNWKF.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/geometry/support/normalizeUtilsSync.js
function a(t2) {
  return f2(t2, true);
}
function p2(t2) {
  return f2(t2, false);
}
function f2(i2, n) {
  if (t(i2)) return null;
  const a2 = i2.spatialReference, p3 = R(a2), f3 = "toJSON" in i2 ? i2.toJSON() : i2;
  if (!p3) return f3;
  const I2 = k(a2) ? 102100 : 4326, g2 = r[I2].maxX, v2 = r[I2].minX;
  if (s(f3)) return y2(f3, g2, v2);
  if (l(f3)) return f3.points = f3.points.map((t2) => y2(t2, g2, v2)), f3;
  if (u(f3)) return d(f3, p3);
  if (y(f3) || f(f3)) {
    const t2 = c(S, f3), i3 = { xmin: t2[0], ymin: t2[1], xmax: t2[2], ymax: t2[3] }, e = i(i3.xmin, v2) * (2 * g2), h = 0 === e ? f3 : s2(f3, e);
    return i3.xmin += e, i3.xmax += e, i3.xmax > g2 ? P(h, g2, n) : i3.xmin < v2 ? P(h, v2, n) : h;
  }
  return f3;
}
function d(t2, i2) {
  if (!i2) return t2;
  const s3 = I(t2, i2).map((t3) => t3.extent);
  return s3.length < 2 ? s3[0] || t2 : s3.length > 2 ? (t2.xmin = i2.valid[0], t2.xmax = i2.valid[1], t2) : { rings: s3.map((t3) => [[t3.xmin, t3.ymin], [t3.xmin, t3.ymax], [t3.xmax, t3.ymax], [t3.xmax, t3.ymin], [t3.xmin, t3.ymin]]) };
}
function y2(t2, i2, s3) {
  if (Array.isArray(t2)) {
    const n = t2[0];
    if (n > i2) {
      const s4 = i(n, i2);
      t2[0] = n + s4 * (-2 * i2);
    } else if (n < s3) {
      const i3 = i(n, s3);
      t2[0] = n + i3 * (-2 * s3);
    }
  } else {
    const n = t2.x;
    if (n > i2) {
      const s4 = i(n, i2);
      t2.x += s4 * (-2 * i2);
    } else if (n < s3) {
      const i3 = i(n, s3);
      t2.x += i3 * (-2 * s3);
    }
  }
  return t2;
}
function I(t2, i2) {
  const s3 = [], { ymin: n, ymax: e, xmin: h, xmax: o } = t2, r2 = t2.xmax - t2.xmin, [u3, m] = i2.valid, { x, frameId: _ } = g(t2.xmin, i2), { x: l2, frameId: c2 } = g(t2.xmax, i2), a2 = x === l2 && r2 > 0;
  if (r2 > 2 * m) {
    const t3 = { xmin: h < o ? x : l2, ymin: n, xmax: m, ymax: e }, i3 = { xmin: u3, ymin: n, xmax: h < o ? l2 : x, ymax: e }, r3 = { xmin: 0, ymin: n, xmax: m, ymax: e }, a3 = { xmin: u3, ymin: n, xmax: 0, ymax: e }, p3 = [], f3 = [];
    v(t3, r3) && p3.push(_), v(t3, a3) && f3.push(_), v(i3, r3) && p3.push(c2), v(i3, a3) && f3.push(c2);
    for (let s4 = _ + 1; s4 < c2; s4++) p3.push(s4), f3.push(s4);
    s3.push(new C(t3, [_]), new C(i3, [c2]), new C(r3, p3), new C(a3, f3));
  } else x > l2 || a2 ? s3.push(new C({ xmin: x, ymin: n, xmax: m, ymax: e }, [_]), new C({ xmin: u3, ymin: n, xmax: l2, ymax: e }, [c2])) : s3.push(new C({ xmin: x, ymin: n, xmax: l2, ymax: e }, [_]));
  return s3;
}
function g(t2, i2) {
  const [s3, n] = i2.valid, e = 2 * n;
  let h, o = 0;
  return t2 > n ? (h = Math.ceil(Math.abs(t2 - n) / e), t2 -= h * e, o = h) : t2 < s3 && (h = Math.ceil(Math.abs(t2 - s3) / e), t2 += h * e, o = -h), { x: t2, frameId: o };
}
function v(t2, i2) {
  const { xmin: s3, ymin: n, xmax: e, ymax: h } = i2;
  return O(t2, s3, n) && O(t2, s3, h) && O(t2, e, h) && O(t2, e, n);
}
function O(t2, i2, s3) {
  return i2 >= t2.xmin && i2 <= t2.xmax && s3 >= t2.ymin && s3 <= t2.ymax;
}
function P(t2, i2, s3 = true) {
  const e = !f(t2);
  if (e && p(t2), s3) {
    return new T().cut(t2, i2);
  }
  const h = e ? t2.rings : t2.paths, o = e ? 4 : 2, r2 = h.length, m = -2 * i2;
  for (let n = 0; n < r2; n++) {
    const t3 = h[n];
    if (t3 && t3.length >= o) {
      const i3 = [];
      for (const s4 of t3) i3.push([s4[0] + m, s4[1]]);
      h.push(i3);
    }
  }
  return e ? t2.rings = h : t2.paths = h, t2;
}
var C = class {
  constructor(t2, i2) {
    this.extent = t2, this.frameIds = i2;
  }
};
var S = u2();
var T = class {
  constructor() {
    this._linesIn = [], this._linesOut = [];
  }
  cut(t2, i2) {
    let s3;
    if (this._xCut = i2, t2.rings) this._closed = true, s3 = t2.rings, this._minPts = 4;
    else {
      if (!t2.paths) return null;
      this._closed = false, s3 = t2.paths, this._minPts = 2;
    }
    for (const e of s3) {
      if (!e || e.length < this._minPts) continue;
      let t3 = true;
      for (const i3 of e) t3 ? (this.moveTo(i3), t3 = false) : this.lineTo(i3);
      this._closed && this.close();
    }
    this._pushLineIn(), this._pushLineOut(), s3 = [];
    for (const e of this._linesIn) e && e.length >= this._minPts && s3.push(e);
    const n = -2 * this._xCut;
    for (const e of this._linesOut) if (e && e.length >= this._minPts) {
      for (const t3 of e) t3[0] += n;
      s3.push(e);
    }
    return this._closed ? t2.rings = s3 : t2.paths = s3, t2;
  }
  moveTo(t2) {
    this._pushLineIn(), this._pushLineOut(), this._prevSide = this._side(t2[0]), this._moveTo(t2[0], t2[1], this._prevSide), this._prevPt = t2, this._firstPt = t2;
  }
  lineTo(t2) {
    const i2 = this._side(t2[0]);
    if (i2 * this._prevSide == -1) {
      const s3 = this._intersect(this._prevPt, t2);
      this._lineTo(this._xCut, s3, 0), this._prevSide = 0, this._lineTo(t2[0], t2[1], i2);
    } else this._lineTo(t2[0], t2[1], i2);
    this._prevSide = i2, this._prevPt = t2;
  }
  close() {
    const t2 = this._firstPt, i2 = this._prevPt;
    t2[0] === i2[0] && t2[1] === i2[1] || this.lineTo(t2), this._checkClosingPt(this._lineIn), this._checkClosingPt(this._lineOut);
  }
  _moveTo(t2, i2, s3) {
    this._closed ? (this._lineIn.push([s3 <= 0 ? t2 : this._xCut, i2]), this._lineOut.push([s3 >= 0 ? t2 : this._xCut, i2])) : (s3 <= 0 && this._lineIn.push([t2, i2]), s3 >= 0 && this._lineOut.push([t2, i2]));
  }
  _lineTo(t2, i2, s3) {
    this._closed ? (this._addPolyVertex(this._lineIn, s3 <= 0 ? t2 : this._xCut, i2), this._addPolyVertex(this._lineOut, s3 >= 0 ? t2 : this._xCut, i2)) : s3 < 0 ? (0 === this._prevSide && this._pushLineOut(), this._lineIn.push([t2, i2])) : s3 > 0 ? (0 === this._prevSide && this._pushLineIn(), this._lineOut.push([t2, i2])) : this._prevSide < 0 ? (this._lineIn.push([t2, i2]), this._lineOut.push([t2, i2])) : this._prevSide > 0 && (this._lineOut.push([t2, i2]), this._lineIn.push([t2, i2]));
  }
  _addPolyVertex(t2, i2, s3) {
    const n = t2.length;
    n > 1 && t2[n - 1][0] === i2 && t2[n - 2][0] === i2 ? t2[n - 1][1] = s3 : t2.push([i2, s3]);
  }
  _checkClosingPt(t2) {
    const i2 = t2.length;
    i2 > 3 && t2[0][0] === this._xCut && t2[i2 - 2][0] === this._xCut && t2[1][0] === this._xCut && (t2[0][1] = t2[i2 - 2][1], t2.pop());
  }
  _side(t2) {
    return t2 < this._xCut ? -1 : t2 > this._xCut ? 1 : 0;
  }
  _intersect(t2, i2) {
    const s3 = (this._xCut - t2[0]) / (i2[0] - t2[0]);
    return t2[1] + s3 * (i2[1] - t2[1]);
  }
  _pushLineIn() {
    this._lineIn && this._lineIn.length >= this._minPts && this._linesIn.push(this._lineIn), this._lineIn = [];
  }
  _pushLineOut() {
    this._lineOut && this._lineOut.length >= this._minPts && this._linesOut.push(this._lineOut), this._lineOut = [];
  }
};

export {
  a,
  p2 as p
};
//# sourceMappingURL=chunk-OHAM27JH.js.map
