import {
  c
} from "./chunk-XZ2UVSB4.js";
import {
  o
} from "./chunk-PTIRBOGQ.js";
import {
  x as x2
} from "./chunk-W3CLOCDX.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  U as U2
} from "./chunk-AW4AS2UW.js";
import {
  v
} from "./chunk-ZACBBT3Y.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  Et
} from "./chunk-U4SVMKOQ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  D,
  U
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  l,
  r,
  t,
  x
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/StreamConnection.js
var c2 = class extends n.EventedAccessor {
  destroy() {
    this.emit("destroy");
  }
  get connectionError() {
    return this.errorString ? new s2("stream-connection", this.errorString) : null;
  }
  onFeature(r2) {
    this.emit("data-received", r2);
  }
  onMessage(r2) {
    this.emit("message-received", r2);
  }
};
e([y({ readOnly: true })], c2.prototype, "connectionError", null), c2 = e([a("esri.layers.support.StreamConnection")], c2);
var n2 = c2;

// node_modules/@arcgis/core/layers/graphics/sources/connections/WebSocketConnection.js
var g;
!function(e2) {
  e2[e2.CONNECTING = 0] = "CONNECTING", e2[e2.OPEN = 1] = "OPEN", e2[e2.CLOSING = 2] = "CLOSING", e2[e2.CLOSED = 3] = "CLOSED";
}(g || (g = {}));
var u = class extends n2 {
  constructor(e2) {
    super(), this._outstandingMessages = [], this.errorString = null;
    const { geometryType: t2, spatialReference: o3, sourceSpatialReference: s3 } = e2;
    this._config = e2, this._featureZScaler = o(t2, s3, o3), this._open();
  }
  async _open() {
    await this._tryCreateWebSocket(), this.destroyed || await this._handshake();
  }
  destroy() {
    super.destroy(), r(this._websocket) && (this._websocket.onopen = null, this._websocket.onclose = null, this._websocket.onerror = null, this._websocket.onmessage = null, this._websocket.close()), this._websocket = null;
  }
  get connectionStatus() {
    if (t(this._websocket)) return "disconnected";
    switch (this._websocket.readyState) {
      case g.CONNECTING:
      case g.OPEN:
        return "connected";
      case g.CLOSING:
      case g.CLOSED:
        return "disconnected";
    }
  }
  sendMessageToSocket(e2) {
    t(this._websocket) ? this._outstandingMessages.push(e2) : this._websocket.send(JSON.stringify(e2));
  }
  sendMessageToClient(e2) {
    this._onMessage(e2);
  }
  updateCustomParameters(e2) {
    this._config.customParameters = e2, r(this._websocket) && this._websocket.close();
  }
  async _tryCreateWebSocket(e2 = this._config.source.path, s3 = 1e3, r2 = 0) {
    try {
      if (this.destroyed) return;
      const t2 = Et(e2, this._config.customParameters ?? {});
      this._websocket = await this._createWebSocket(t2), this.notifyChange("connectionStatus");
    } catch (c3) {
      const i = s3 / 1e3;
      return this._config.maxReconnectionAttempts && r2 >= this._config.maxReconnectionAttempts ? (s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Exceeded maxReconnectionAttempts attempts. No further attempts will be made")), void this.destroy()) : (s.getLogger(this.declaredClass).error(new s2("websocket-connection", `Failed to connect. Attempting to reconnect in ${i}s`, c3)), await U(s3), this._tryCreateWebSocket(e2, Math.min(1.5 * s3, 1e3 * this._config.maxReconnectionInterval), r2 + 1));
    }
  }
  _setWebSocketJSONParseHandler(e2) {
    e2.onmessage = (e3) => {
      try {
        const t2 = JSON.parse(e3.data);
        this._onMessage(t2);
      } catch (s3) {
        return void s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Failed to parse message, invalid JSON", { error: s3 }));
      }
    };
  }
  _createWebSocket(e2) {
    return new Promise((t2, o3) => {
      const s3 = new WebSocket(e2);
      s3.onopen = () => {
        if (s3.onopen = null, this.destroyed) return s3.onclose = null, void s3.close();
        s3.onclose = (e3) => this._onClose(e3), s3.onerror = (e3) => this._onError(e3), this._setWebSocketJSONParseHandler(s3), t2(s3);
      }, s3.onclose = (e3) => {
        s3.onopen = s3.onclose = null, o3(e3);
      };
    });
  }
  async _handshake(e2 = 1e4) {
    const s3 = this._websocket;
    if (t(s3)) return;
    const n3 = D(), i = s3.onmessage, { filter: a3, outFields: l2, spatialReference: d } = this._config;
    return n3.timeout(e2), s3.onmessage = (e3) => {
      var _a;
      let r2 = null;
      try {
        r2 = JSON.parse(e3.data);
      } catch (c3) {
      }
      r2 && "object" == typeof r2 || (s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Protocol violation. Handshake failed - malformed message", e3.data)), n3.reject(), this.destroy()), ((_a = r2.spatialReference) == null ? void 0 : _a.wkid) !== (d == null ? void 0 : d.wkid) && (s.getLogger(this.declaredClass).error(new s2("websocket-connection", `Protocol violation. Handshake failed - expected wkid of ${d.wkid}`, e3.data)), n3.reject(), this.destroy()), "json" !== r2.format && (s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Protocol violation. Handshake failed - format is not set", e3.data)), n3.reject(), this.destroy()), a3 && r2.filter !== a3 && s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Tried to set filter, but server doesn't support it")), l2 && r2.outFields !== l2 && s.getLogger(this.declaredClass).error(new s2("websocket-connection", "Tried to set outFields, but server doesn't support it")), s3.onmessage = i;
      for (const t2 of this._outstandingMessages) s3.send(JSON.stringify(t2));
      this._outstandingMessages = [], n3.resolve();
    }, s3.send(JSON.stringify({ filter: a3, outFields: l2, format: "json", spatialReference: { wkid: d.wkid } })), n3.promise;
  }
  _onMessage(e2) {
    if (this.onMessage(e2), "type" in e2) switch (e2.type) {
      case "features":
      case "featureResult":
        for (const t2 of e2.features) r(this._featureZScaler) && this._featureZScaler(t2.geometry), this.onFeature(t2);
    }
  }
  _onError(e2) {
    const t2 = "Encountered an error over WebSocket connection";
    this._set("errorString", t2), s.getLogger(this.declaredClass).error("websocket-connection", t2);
  }
  _onClose(e2) {
    this._websocket = null, this.notifyChange("connectionStatus"), 1e3 !== e2.code && s.getLogger(this.declaredClass).error("websocket-connection", `WebSocket closed unexpectedly with error code ${e2.code}`), this.destroyed || this._open();
  }
};
e([y()], u.prototype, "connectionStatus", null), e([y()], u.prototype, "errorString", void 0), u = e([a("esri.layers.graphics.sources.connections.WebSocketConnection")], u);

// node_modules/@arcgis/core/layers/graphics/sources/connections/GeoEventConnection.js
var m = 1e4;
var p = { maxQueryDepth: 5, maxRecordCountFactor: 3 };
var _ = class extends u {
  constructor(e2) {
    super({ ...p, ...e2 }), this._buddyServicesQuery = null, this._relatedFeatures = null;
  }
  async _open() {
    const e2 = await this._fetchServiceDefinition(this._config.source);
    e2.timeInfo.trackIdField || s.getLogger(this.declaredClass).warn("GeoEvent service was configured without a TrackIdField. This may result in certain functionality being disabled. The purgeOptions.maxObservations property will have no effect.");
    const t2 = this._fetchWebSocketUrl(e2.streamUrls, this._config.spatialReference);
    this._buddyServicesQuery || (this._buddyServicesQuery = this._queryBuddyServices()), await this._buddyServicesQuery, await this._tryCreateWebSocket(t2);
    const { filter: r2, outFields: o3 } = this._config;
    this.destroyed || this._setFilter(r2, o3);
  }
  _onMessage(e2) {
    if ("attributes" in e2) {
      let i;
      try {
        i = this._enrich(e2), r(this._featureZScaler) && this._featureZScaler(i.geometry);
      } catch (t2) {
        return void s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Failed to parse message", t2));
      }
      this.onFeature(i);
    } else this.onMessage(e2);
  }
  async _fetchServiceDefinition(e2) {
    const r2 = { f: "json", ...this._config.customParameters }, s3 = U2(e2.path, { query: r2, responseType: "json" }), o3 = (await s3).data;
    return this._serviceDefinition = o3, o3;
  }
  _fetchWebSocketUrl(e2, t2) {
    const r2 = e2[0], { urls: s3, token: o3 } = r2, i = this._inferWebSocketBaseUrl(s3);
    return Et(`${i}/subscribe`, { outSR: "" + t2.wkid, token: o3 });
  }
  _inferWebSocketBaseUrl(e2) {
    if (1 === e2.length) return e2[0];
    for (const t2 of e2) if (t2.includes("wss")) return t2;
    return s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Unable to infer WebSocket url", e2)), null;
  }
  async _setFilter(e2, t2) {
    const o3 = this._websocket;
    if (t(o3) || t(e2) && t(t2)) return;
    const n3 = JSON.stringify({ filter: this._serializeFilter(e2, t2) });
    let a3 = false;
    const u2 = D(), d = () => {
      a3 || (this.destroyed || this._websocket !== o3 || s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Server timed out when setting filter")), u2.reject());
    }, l2 = (e3) => {
      const t3 = JSON.parse(e3.data);
      t3.filter && (t3.error && (s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Failed to set service filter", t3.error)), this._set("errorString", `Could not set service filter - ${t3.error}`), u2.reject(t3.error)), this._setWebSocketJSONParseHandler(o3), a3 = true, u2.resolve());
    };
    return o3.onmessage = l2, o3.send(n3), setTimeout(d, m), u2.promise;
  }
  _serializeFilter(e2, t2) {
    const n3 = {};
    if (t(e2) && t(t2)) return n3;
    if (r(e2) && e2.geometry) try {
      const t3 = v(e2.geometry);
      if ("extent" !== t3.type) throw new s2(`Expected extent but found type ${t3.type}`);
      n3.geometry = JSON.stringify(t3.shiftCentralMeridian());
    } catch (a3) {
      s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Encountered an error when setting connection geometryDefinition", a3));
    }
    return r(e2) && e2.where && "1 = 1" !== e2.where && "1=1" !== e2.where && (n3.where = e2.where), r(t2) && (n3.outFields = t2.join(",")), n3;
  }
  _enrich(e2) {
    if (!this._relatedFeatures) return e2;
    const t2 = this._serviceDefinition.relatedFeatures.joinField, o3 = e2.attributes[t2], i = this._relatedFeatures.get(o3);
    if (!i) return s.getLogger(this.declaredClass).warn("geoevent-connection", "Feature join failed. Is the join field configured correctly?", e2), e2;
    const { attributes: n3, geometry: a3 } = i;
    for (const r2 in n3) e2.attributes[r2] = n3[r2];
    return a3 && (e2.geometry = a3), e2.geometry || e2.centroid || s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Found malformed feature - no geometry found", e2)), e2;
  }
  async _queryBuddyServices() {
    try {
      const { relatedFeatures: e2, keepLatestArchive: t2 } = this._serviceDefinition, r2 = this._queryRelatedFeatures(e2), s3 = this._queryArchive(t2);
      await r2;
      const o3 = await s3;
      if (!o3) return;
      for (const i of o3.features) this.onFeature(this._enrich(i));
    } catch (e2) {
      s.getLogger(this.declaredClass).error(new s2("geoevent-connection", "Encountered an error when querying buddy services", { error: e2 }));
    }
  }
  async _queryRelatedFeatures(e2) {
    if (!e2) return;
    const t2 = await this._queryBuddy(e2.featuresUrl);
    this._addRelatedFeatures(t2);
  }
  async _queryArchive(e2) {
    if (e2) return this._queryBuddy(e2.featuresUrl);
  }
  async _queryBuddy(e2) {
    const t2 = new (await import("./@arcgis_core_layers_FeatureLayer__js.js")).default({ url: e2 }), { capabilities: r2 } = await t2.load(), s3 = r2.query.supportsMaxRecordCountFactor, o3 = r2.query.supportsPagination, i = r2.query.supportsCentroid, c3 = this._config.maxRecordCountFactor, u2 = t2.capabilities.query.maxRecordCount, d = s3 ? u2 * c3 : u2, l2 = new x2();
    if (l2.outFields = l(this._config.outFields, ["*"]), l2.where = l(x(this._config.filter, "where"), "1=1"), l2.returnGeometry = true, l2.returnExceededLimitFeatures = true, l2.outSpatialReference = f.fromJSON(this._config.spatialReference), i && (l2.returnCentroid = true), s3 && (l2.maxRecordCountFactor = c3), o3) return l2.num = d, t2.destroy(), this._queryPages(e2, l2);
    const g2 = await c(e2, l2, this._config.sourceSpatialReference);
    return t2.destroy(), g2.data;
  }
  async _queryPages(e2, t2, r2 = [], s3 = 0) {
    t2.start = r(t2.num) ? s3 * t2.num : null;
    const { data: i } = await c(e2, t2, this._config.sourceSpatialReference);
    return i.exceededTransferLimit && s3 < (this._config.maxQueryDepth ?? 0) ? (i.features.forEach((e3) => r2.push(e3)), this._queryPages(e2, t2, r2, s3 + 1)) : (r2.forEach((e3) => i.features.push(e3)), i);
  }
  _addRelatedFeatures(e2) {
    const t2 = /* @__PURE__ */ new Map(), r2 = e2.features, s3 = this._serviceDefinition.relatedFeatures.joinField;
    for (const o3 of r2) {
      const e3 = o3.attributes[s3];
      t2.set(e3, o3);
    }
    this._relatedFeatures = t2;
  }
};
_ = e([a("esri.layers.graphics.sources.connections.GeoEventConnection")], _);
var w = _;

// node_modules/@arcgis/core/layers/support/ClientSideConnection.js
var a2 = class extends n2 {
  constructor(e2) {
    super(), this.connectionStatus = "connected", this.errorString = null;
    const { geometryType: r2, spatialReference: t2, sourceSpatialReference: o3 } = e2;
    this._featureZScaler = o(r2, o3, t2);
  }
  updateCustomParameters(e2) {
  }
  sendMessageToSocket(e2) {
  }
  sendMessageToClient(e2) {
    if ("type" in e2) switch (e2.type) {
      case "features":
      case "featureResult":
        for (const t2 of e2.features) r(this._featureZScaler) && this._featureZScaler(t2.geometry), this.onFeature(t2);
    }
    this.onMessage(e2);
  }
};
e([y()], a2.prototype, "connectionStatus", void 0), e([y()], a2.prototype, "errorString", void 0), a2 = e([a("esri.layers.support.ClientSideConnection")], a2);

// node_modules/@arcgis/core/layers/graphics/sources/connections/createConnection.js
function o2(o3, r2, s3, i, c3, a3, m2, p2) {
  const f2 = { source: o3, sourceSpatialReference: r2, spatialReference: s3, geometryType: i, filter: c3, maxReconnectionAttempts: a3, maxReconnectionInterval: m2, customParameters: p2 };
  if (!o3) return new a2(f2);
  return o3.path.startsWith("wss://") || o3.path.startsWith("ws://") ? new u(f2) : new w(f2);
}

export {
  o2 as o
};
//# sourceMappingURL=chunk-IJODK4QW.js.map
