{"version": 3, "sources": ["../../@arcgis/core/symbols/support/FeatureExpressionInfo.js", "../../@arcgis/core/symbols/support/ElevationInfo.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import{collectArcadeFieldNames as o}from\"../../layers/support/fieldUtils.js\";var i;let p=i=class extends e{constructor(r){super(r)}async collectRequiredFields(r,e){return o(r,e,this.expression)}clone(){return new i({expression:this.expression,title:this.title})}equals(r){return this.expression===r.expression&&this.title===r.title}};r([s({type:String,json:{write:!0}})],p.prototype,\"expression\",void 0),r([s({type:String,json:{write:!0}})],p.prototype,\"title\",void 0),p=i=r([t(\"esri.layers.support.FeatureExpressionInfo\")],p);const c=p;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{strict as r,JSONMap as o}from\"../../core/jsonMap.js\";import{JSONSupport as t}from\"../../core/JSONSupport.js\";import{isSome as s,equalsMaybe as i}from\"../../core/maybe.js\";import{property as n}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{reader as p}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as u}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as a}from\"../../core/accessorSupport/decorators/writer.js\";import f from\"./FeatureExpressionInfo.js\";import{supportedUnits as l}from\"./unitConversionUtils.js\";var m;const d=r()({onTheGround:\"on-the-ground\",relativeToGround:\"relative-to-ground\",relativeToScene:\"relative-to-scene\",absoluteHeight:\"absolute-height\"}),c=new o({foot:\"feet\",kilometer:\"kilometers\",meter:\"meters\",mile:\"miles\",\"us-foot\":\"us-feet\",yard:\"yards\"});let h=m=class extends t{constructor(e){super(e),this.offset=null}readFeatureExpressionInfo(e,r){return null!=e?e:r.featureExpression&&0===r.featureExpression.value?{expression:\"0\"}:void 0}writeFeatureExpressionInfo(e,r,o,t){r[o]=e.write({},t),\"0\"===e.expression&&(r.featureExpression={value:0})}get mode(){const{offset:e,featureExpressionInfo:r}=this;return this._isOverridden(\"mode\")?this._get(\"mode\"):s(e)||r?\"relative-to-ground\":\"on-the-ground\"}set mode(e){this._override(\"mode\",e)}set unit(e){this._set(\"unit\",e)}write(e,r){return this.offset||this.mode||this.featureExpressionInfo||this.unit?super.write(e,r):null}clone(){return new m({mode:this.mode,offset:this.offset,featureExpressionInfo:this.featureExpressionInfo?this.featureExpressionInfo.clone():void 0,unit:this.unit})}equals(e){return this.mode===e.mode&&this.offset===e.offset&&this.unit===e.unit&&i(this.featureExpressionInfo,e.featureExpressionInfo)}};e([n({type:f,json:{write:!0}})],h.prototype,\"featureExpressionInfo\",void 0),e([p(\"featureExpressionInfo\",[\"featureExpressionInfo\",\"featureExpression\"])],h.prototype,\"readFeatureExpressionInfo\",null),e([a(\"featureExpressionInfo\",{featureExpressionInfo:{type:f},\"featureExpression.value\":{type:[0]}})],h.prototype,\"writeFeatureExpressionInfo\",null),e([n({type:d.apiValues,nonNullable:!0,json:{type:d.jsonValues,read:d.read,write:{writer:d.write,isRequired:!0}}})],h.prototype,\"mode\",null),e([n({type:Number,json:{write:!0}})],h.prototype,\"offset\",void 0),e([n({type:l,json:{type:String,read:c.read,write:c.write}})],h.prototype,\"unit\",null),h=m=e([u(\"esri.layers.support.ElevationInfo\")],h);const x=h;export{x as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+Z,IAAI;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBA,IAAEC,IAAE;AAAC,WAAO,EAAED,IAAEC,IAAE,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,YAAW,KAAK,YAAW,OAAM,KAAK,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAO,KAAK,eAAaA,GAAE,cAAY,KAAK,UAAQA,GAAE;AAAA,EAAK;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,2CAA2C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;;;ACAvK,IAAI;AAAE,IAAME,KAAEC,GAAE,EAAE,EAAC,aAAY,iBAAgB,kBAAiB,sBAAqB,iBAAgB,qBAAoB,gBAAe,kBAAiB,CAAC;AAApJ,IAAsJC,KAAE,IAAI,EAAE,EAAC,MAAK,QAAO,WAAU,cAAa,OAAM,UAAS,MAAK,SAAQ,WAAU,WAAU,MAAK,QAAO,CAAC;AAAE,IAAI,IAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,0BAA0BA,IAAEC,IAAE;AAAC,WAAO,QAAMD,KAAEA,KAAEC,GAAE,qBAAmB,MAAIA,GAAE,kBAAkB,QAAM,EAAC,YAAW,IAAG,IAAE;AAAA,EAAM;AAAA,EAAC,2BAA2BD,IAAEC,IAAEH,IAAE,GAAE;AAAC,IAAAG,GAAEH,EAAC,IAAEE,GAAE,MAAM,CAAC,GAAE,CAAC,GAAE,QAAMA,GAAE,eAAaC,GAAE,oBAAkB,EAAC,OAAM,EAAC;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,UAAK,EAAC,QAAOD,IAAE,uBAAsBC,GAAC,IAAE;AAAK,WAAO,KAAK,cAAc,MAAM,IAAE,KAAK,KAAK,MAAM,IAAE,EAAED,EAAC,KAAGC,KAAE,uBAAqB;AAAA,EAAe;AAAA,EAAC,IAAI,KAAKD,IAAE;AAAC,SAAK,UAAU,QAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,KAAKA,IAAE;AAAC,SAAK,KAAK,QAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAQ,KAAK,QAAM,KAAK,yBAAuB,KAAK,OAAK,MAAM,MAAMD,IAAEC,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,MAAK,QAAO,KAAK,QAAO,uBAAsB,KAAK,wBAAsB,KAAK,sBAAsB,MAAM,IAAE,QAAO,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAO,KAAK,SAAOA,GAAE,QAAM,KAAK,WAASA,GAAE,UAAQ,KAAK,SAAOA,GAAE,QAAM,EAAE,KAAK,uBAAsBA,GAAE,qBAAqB;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,yBAAwB,CAAC,yBAAwB,mBAAmB,CAAC,CAAC,GAAE,EAAE,WAAU,6BAA4B,IAAI,GAAE,EAAE,CAACC,GAAE,yBAAwB,EAAC,uBAAsB,EAAC,MAAK,EAAC,GAAE,2BAA0B,EAAC,MAAK,CAAC,CAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,8BAA6B,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKJ,GAAE,WAAU,aAAY,MAAG,MAAK,EAAC,MAAKA,GAAE,YAAW,MAAKA,GAAE,MAAK,OAAM,EAAC,QAAOA,GAAE,OAAM,YAAW,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKC,IAAE,MAAK,EAAC,MAAK,QAAO,MAAKC,GAAE,MAAK,OAAMA,GAAE,MAAK,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,IAAI,GAAE,IAAE,IAAE,EAAE,CAAC,EAAE,mCAAmC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["r", "e", "d", "o", "c", "e", "r"]}