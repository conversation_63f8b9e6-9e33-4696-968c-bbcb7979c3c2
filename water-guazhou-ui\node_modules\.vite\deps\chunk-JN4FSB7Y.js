import {
  $,
  B,
  M,
  S,
  _,
  b,
  j,
  k,
  m,
  w
} from "./chunk-HP475EI3.js";
import {
  i2 as i,
  o2,
  o3
} from "./chunk-2CM7MIII.js";
import {
  r,
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  has,
  o,
  p
} from "./chunk-REW33H3I.js";
import {
  N
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/accessorSupport/metadata.js
function o4(r3) {
  let o8 = r3.constructor.__accessorMetadata__;
  const c6 = Object.prototype.hasOwnProperty.call(r3.constructor, "__accessorMetadata__");
  if (o8) {
    if (!c6) {
      o8 = Object.create(o8);
      for (const e4 in o8) o8[e4] = p(o8[e4]);
      Object.defineProperty(r3.constructor, "__accessorMetadata__", { value: o8, enumerable: false, configurable: true, writable: true });
    }
  } else o8 = {}, Object.defineProperty(r3.constructor, "__accessorMetadata__", { value: o8, enumerable: false, configurable: true, writable: true });
  return N(r3.constructor.__accessorMetadata__);
}
function c(t4, e4) {
  const r3 = o4(t4);
  let c6 = r3[e4];
  return c6 || (c6 = r3[e4] = {}), c6;
}
function n(t4, e4) {
  return o2(t4, e4, u);
}
var s3 = /^(?:[^.]+\.)?(?:value|type|(?:json\.type|json\.origins\.[^.]\.type))$/;
function u(t4) {
  return s3.test(t4) ? "replace" : "merge";
}

// node_modules/@arcgis/core/core/accessorSupport/set.js
function t(o8, e4, s8) {
  if (o8 && e4) if ("object" == typeof e4) for (const r3 of Object.getOwnPropertyNames(e4)) t(o8, r3, e4[r3]);
  else {
    if (e4.includes(".")) {
      const n6 = e4.split("."), i6 = n6.splice(n6.length - 1, 1)[0];
      return void t(o3(o8, n6), i6, s8);
    }
    const i5 = o8.__accessor__;
    null != i5 && n2(e4, i5), o8[e4] = s8;
  }
}
function n2(r3, t4) {
  if (has("esri-unknown-property-errors") && !e(r3, t4)) throw new s2("set:unknown-property", s4(r3, t4));
}
function e(o8, r3) {
  return null != r3.metadatas[o8];
}
function s4(o8, r3) {
  return "setting unknown property '" + o8 + "' on instance of " + r3.host.declaredClass;
}

// node_modules/@arcgis/core/core/accessorSupport/decorators/property.js
function y(n6 = {}) {
  return (o8, s8) => {
    if (o8 === Function.prototype) throw new Error(`Inappropriate use of @property() on a static field: ${o8.name}.${s8}. Accessor does not support static properties.`);
    const i5 = Object.getOwnPropertyDescriptor(o8, s8), a5 = c(o8, s8);
    i5 && (i5.get || i5.set ? (a5.get = i5.get || a5.get, a5.set = i5.set || a5.set) : "value" in i5 && ("value" in n6 && s.getLogger("esri.core.accessorSupport.decorators.property").warn(`@property() will redefine the value of "${s8}" on "${o8.constructor.name}" already defined in the metadata`, n6), a5.value = n6.value = i5.value)), null != n6.readOnly && (a5.readOnly = n6.readOnly);
    const p5 = n6.aliasOf;
    if (p5) {
      const t4 = "string" == typeof p5 ? p5 : p5.source, e4 = "string" == typeof p5 ? null : true === p5.overridable;
      let r3;
      a5.dependsOn = [t4], a5.get = function() {
        let e5 = o3(this, t4);
        if ("function" == typeof e5) {
          r3 || (r3 = t4.split(".").slice(0, -1).join("."));
          const n7 = o3(this, r3);
          n7 && (e5 = e5.bind(n7));
        }
        return e5;
      }, a5.readOnly || (a5.set = e4 ? function(t5) {
        this._override(s8, t5);
      } : function(e5) {
        t(this, t4, e5);
      });
    }
    const u5 = n6.type, c6 = n6.types;
    a5.cast || (u5 ? a5.cast = h(u5) : c6 && (Array.isArray(c6) ? a5.cast = m(S(c6[0])) : a5.cast = S(c6))), n(a5, n6), n6.range && (a5.cast = j2(a5.cast, n6.range));
  };
}
function d(t4, e4, r3) {
  const n6 = c(t4, r3);
  n6.json || (n6.json = {});
  let o8 = n6.json;
  return void 0 !== e4 && (o8.origins || (o8.origins = {}), o8.origins[e4] || (o8.origins[e4] = {}), o8 = o8.origins[e4]), o8;
}
function h(t4) {
  let e4 = 0, r3 = t4;
  if (_(t4)) return B(t4);
  for (; Array.isArray(r3) && 1 === r3.length && "string" != typeof r3[0] && "number" != typeof r3[0]; ) r3 = r3[0], e4++;
  const f4 = r3;
  if (k(f4)) return 0 === e4 ? M(f4) : $(M(f4), e4);
  if (1 === e4) return w(f4);
  if (e4 > 1) return j(f4, e4);
  const l4 = t4;
  return l4.from ? l4.from : b(l4);
}
function j2(t4, e4) {
  return (r3) => {
    let n6 = +t4(r3);
    return null != e4.step && (n6 = Math.round(n6 / e4.step) * e4.step), null != e4.min && (n6 = Math.max(e4.min, n6)), null != e4.max && (n6 = Math.min(e4.max, n6)), n6;
  };
}

// node_modules/@arcgis/core/core/Warning.js
var t2 = class _t extends r {
  constructor(e4, s8, r3) {
    if (super(e4, s8, r3), !(this instanceof _t)) return new _t(e4, s8, r3);
  }
};
t2.prototype.type = "warning";

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty/type.js
function e2(e4) {
  return !!e4 && e4.prototype && e4.prototype.declaredClass && 0 === e4.prototype.declaredClass.indexOf("esri.core.Collection");
}

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty/reader.js
var i2 = s.getLogger("esri.core.accessorSupport.extensions.serializableProperty.reader");
function p2(t4, r3, n6) {
  var _a, _b;
  t4 && (!n6 && !r3.read || ((_a = r3.read) == null ? void 0 : _a.reader) || false === ((_b = r3.read) == null ? void 0 : _b.enabled) || l(t4) && o("read.reader", u2(t4), r3));
}
function u2(t4) {
  var _a, _b;
  const e4 = t4.ndimArray ?? 0;
  if (e4 > 1) return c2(t4);
  if (1 === e4) return a(t4);
  if ("type" in t4 && d2(t4.type)) {
    const e5 = (_b = (_a = t4.type.prototype) == null ? void 0 : _a.itemType) == null ? void 0 : _b.Type, r3 = a("function" == typeof e5 ? { type: e5 } : { types: e5 });
    return (e6, n6, o8) => {
      const i5 = r3(e6, n6, o8);
      return i5 ? new t4.type(i5) : i5;
    };
  }
  return s5(t4);
}
function s5(t4) {
  return "type" in t4 ? y2(t4.type) : g(t4.types);
}
function y2(t4) {
  return t4.prototype.read ? (e4, r3, n6) => {
    if (null == e4) return e4;
    const o8 = typeof e4;
    if ("object" !== o8) return void i2.error(`Expected JSON value of type 'object' to deserialize type '${t4.prototype.declaredClass}', but got '${o8}'`);
    const p5 = new t4();
    return p5.read(e4, n6), p5;
  } : t4.fromJSON;
}
function f(t4, e4, r3, n6) {
  return 0 !== n6 && Array.isArray(e4) ? e4.map((e5) => f(t4, e5, r3, n6 - 1)) : t4(e4, void 0, r3);
}
function c2(t4) {
  const e4 = s5(t4), r3 = f.bind(null, e4), n6 = t4.ndimArray ?? 0;
  return (t5, e5, o8) => {
    if (null == t5) return t5;
    t5 = r3(t5, o8, n6);
    let i5 = n6, p5 = t5;
    for (; i5 > 0 && Array.isArray(p5); ) i5--, p5 = p5[0];
    if (void 0 !== p5) for (let r4 = 0; r4 < i5; r4++) t5 = [t5];
    return t5;
  };
}
function a(t4) {
  const e4 = s5(t4);
  return (t5, r3, n6) => {
    if (null == t5) return t5;
    if (Array.isArray(t5)) {
      const r4 = [];
      for (const o9 of t5) {
        const t6 = e4(o9, void 0, n6);
        void 0 !== t6 && r4.push(t6);
      }
      return r4;
    }
    const o8 = e4(t5, void 0, n6);
    return void 0 !== o8 ? [o8] : void 0;
  };
}
function d2(t4) {
  if (!e2(t4)) return false;
  const e4 = t4.prototype.itemType;
  return !(!e4 || !e4.Type) && ("function" == typeof e4.Type ? m2(e4.Type) : j3(e4.Type));
}
function l(t4) {
  return "types" in t4 ? j3(t4.types) : m2(t4.type);
}
function m2(t4) {
  return !Array.isArray(t4) && (!!t4 && t4.prototype && ("read" in t4.prototype || "fromJSON" in t4 || d2(t4)));
}
function j3(t4) {
  for (const e4 in t4.typeMap) {
    if (!m2(t4.typeMap[e4])) return false;
  }
  return true;
}
function g(t4) {
  let e4 = null;
  const n6 = t4.errorContext ?? "type";
  return (o8, p5, u5) => {
    if (null == o8) return o8;
    const s8 = typeof o8;
    if ("object" !== s8) return void i2.error(`Expected JSON value of type 'object' to deserialize, but got '${s8}'`);
    e4 || (e4 = v(t4));
    const y5 = t4.key;
    if ("string" != typeof y5) return;
    const f4 = o8[y5], c6 = f4 ? e4[f4] : t4.defaultKeyValue ? t4.typeMap[t4.defaultKeyValue] : void 0;
    if (!c6) {
      const t5 = `Type '${f4 || "unknown"}' is not supported`;
      return u5 && u5.messages && o8 && u5.messages.push(new t2(`${n6}:unsupported`, t5, { definition: o8, context: u5 })), void i2.error(t5);
    }
    const a5 = new c6();
    return a5.read(o8, u5), a5;
  };
}
function v(t4) {
  var _a, _b;
  const e4 = {};
  for (const r3 in t4.typeMap) {
    const o8 = t4.typeMap[r3], i5 = o4(o8.prototype);
    if ("function" == typeof t4.key) continue;
    const p5 = i5[t4.key];
    if (!p5) continue;
    ((_a = p5.json) == null ? void 0 : _a.type) && Array.isArray(p5.json.type) && 1 === p5.json.type.length && "string" == typeof p5.json.type[0] && (e4[p5.json.type[0]] = o8);
    const u5 = (_b = p5.json) == null ? void 0 : _b.write;
    if (!u5 || !u5.writer) {
      e4[r3] = o8;
      continue;
    }
    const s8 = u5.target, y5 = "string" == typeof s8 ? s8 : t4.key, f4 = {};
    u5.writer(r3, f4, y5), f4[y5] && (e4[f4[y5]] = o8);
  }
  return e4;
}

// node_modules/@arcgis/core/core/accessorSupport/beforeDestroy.js
var o5 = Symbol("Accessor-beforeDestroy");

// node_modules/@arcgis/core/core/accessorSupport/interfaces.js
var I;
!function(I2) {
  I2[I2.INITIALIZING = 0] = "INITIALIZING", I2[I2.CONSTRUCTING = 1] = "CONSTRUCTING", I2[I2.CONSTRUCTED = 2] = "CONSTRUCTED";
}(I || (I = {}));

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty/originAliases.js
function n3(n6) {
  if (n6.json && n6.json.origins) {
    const o8 = n6.json.origins, e4 = { "web-document": ["web-scene", "web-map"] };
    for (const n7 in e4) if (o8[n7]) {
      const s8 = o8[n7];
      e4[n7].forEach((n8) => {
        o8[n8] = s8;
      }), delete o8[n7];
    }
  }
}

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty/shorthands.js
function e3(e4) {
  if (e4.json || (e4.json = {}), o6(e4.json), n4(e4.json), r2(e4.json), e4.json.origins) for (const t4 in e4.json.origins) o6(e4.json.origins[t4]), n4(e4.json.origins[t4]), r2(e4.json.origins[t4]);
  return true;
}
function r2(e4) {
  e4.name && (e4.read && "object" == typeof e4.read ? void 0 === e4.read.source && (e4.read.source = e4.name) : e4.read = { source: e4.name }, e4.write && "object" == typeof e4.write ? void 0 === e4.write.target && (e4.write.target = e4.name) : e4.write = { target: e4.name });
}
function o6(e4) {
  "boolean" == typeof e4.read ? e4.read = { enabled: e4.read } : "function" == typeof e4.read ? e4.read = { enabled: true, reader: e4.read } : e4.read && "object" == typeof e4.read && void 0 === e4.read.enabled && (e4.read.enabled = true);
}
function n4(e4) {
  "boolean" == typeof e4.write ? e4.write = { enabled: e4.write } : "function" == typeof e4.write ? e4.write = { enabled: true, writer: e4.write } : e4.write && "object" == typeof e4.write && void 0 === e4.write.enabled && (e4.write.enabled = true);
}

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty/writer.js
function i3(r3, e4) {
  if (!e4.write || e4.write.writer || false === e4.write.enabled && !e4.write.overridePolicy) return;
  const t4 = (r3 == null ? void 0 : r3.ndimArray) ?? 0;
  r3 && (1 === t4 || "type" in r3 && e2(r3.type)) ? e4.write.writer = a2 : t4 > 1 ? e4.write.writer = l2(t4) : e4.types ? Array.isArray(e4.types) ? e4.write.writer = f2(e4.types[0]) : e4.write.writer = o7(e4.types) : e4.write.writer = s6;
}
function o7(r3) {
  return (e4, t4, n6, i5) => e4 ? u3(e4, r3, i5) ? s6(e4, t4, n6, i5) : void 0 : s6(e4, t4, n6, i5);
}
function u3(t4, n6, i5) {
  for (const r3 in n6.typeMap) if (t4 instanceof n6.typeMap[r3]) return true;
  if (i5 == null ? void 0 : i5.messages) {
    const o8 = n6.errorContext ?? "type", u5 = `Values of type '${("function" != typeof n6.key ? t4[n6.key] : t4.declaredClass) ?? "Unknown"}' cannot be written`;
    i5 && i5.messages && t4 && i5.messages.push(new s2(`${o8}:unsupported`, u5, { definition: t4, context: i5 })), s.getLogger("esri.core.accessorSupport.extensions.serializableProperty.writer").error(u5);
  }
  return false;
}
function f2(r3) {
  return (e4, t4, n6, i5) => {
    if (!e4 || !Array.isArray(e4)) return s6(e4, t4, n6, i5);
    return s6(e4.filter((e5) => u3(e5, r3, i5)), t4, n6, i5);
  };
}
function s6(r3, e4, n6, i5) {
  o(n6, p3(r3, i5), e4);
}
function p3(r3, e4) {
  return r3 && "function" == typeof r3.write ? r3.write({}, e4) : r3 && "function" == typeof r3.toJSON ? r3.toJSON() : "number" == typeof r3 ? y3(r3) : r3;
}
function y3(r3) {
  return r3 === -1 / 0 ? -Number.MAX_VALUE : r3 === 1 / 0 ? Number.MAX_VALUE : isNaN(r3) ? null : r3;
}
function a2(r3, e4, n6, i5) {
  let o8;
  null === r3 ? o8 = null : r3 && "function" == typeof r3.map ? (o8 = r3.map((r4) => p3(r4, i5)), "function" == typeof o8.toArray && (o8 = o8.toArray())) : o8 = [p3(r3, i5)], o(n6, o8, e4);
}
function c3(r3, e4, t4) {
  return 0 !== t4 && Array.isArray(r3) ? r3.map((r4) => c3(r4, e4, t4 - 1)) : p3(r3, e4);
}
function l2(r3) {
  return (e4, n6, i5, o8) => {
    let u5;
    if (null === e4) u5 = null;
    else {
      u5 = c3(e4, o8, r3);
      let t4 = r3, n7 = u5;
      for (; t4 > 0 && Array.isArray(n7); ) t4--, n7 = n7[0];
      if (void 0 !== n7) for (let r4 = 0; r4 < t4; r4++) u5 = [u5];
    }
    o(i5, u5, n6);
  };
}

// node_modules/@arcgis/core/core/accessorSupport/extensions/serializableProperty.js
function t3(r3, n6) {
  return a3(r3, "read", n6);
}
function s7(r3, n6) {
  return a3(r3, "write", n6);
}
function a3(r3, n6, i5) {
  let e4 = r3 && r3.json;
  if (r3 && r3.json && r3.json.origins && i5) {
    const o8 = i5.origin && r3.json.origins[i5.origin];
    o8 && ("any" === n6 || n6 in o8) && (e4 = o8);
  }
  return e4;
}
function p4(r3) {
  const n6 = y4(r3);
  if (r3.json.origins) for (const e4 in r3.json.origins) {
    const t4 = r3.json.origins[e4], s8 = t4.types ? f3(t4) : n6;
    p2(s8, t4, false), t4.types && !t4.write && r3.json.write && r3.json.write.enabled && (t4.write = { ...r3.json.write }), i3(s8, t4);
  }
  p2(n6, r3.json, true), i3(n6, r3.json);
}
function y4(r3) {
  return r3.json.types ? u4(r3.json) : r3.type ? j4(r3) : u4(r3);
}
function f3(r3) {
  return r3.type ? j4(r3) : u4(r3);
}
function j4(n6) {
  if (!n6.type) return;
  let i5 = 0, e4 = n6.type;
  for (; Array.isArray(e4) && !k(e4); ) e4 = e4[0], i5++;
  return { type: e4, ndimArray: i5 };
}
function u4(r3) {
  if (!r3.types) return;
  let n6 = 0, i5 = r3.types;
  for (; Array.isArray(i5); ) i5 = i5[0], n6++;
  return { types: i5, ndimArray: n6 };
}
function c4(r3) {
  e3(r3) && (n3(r3), p4(r3));
}

// node_modules/@arcgis/core/core/accessorSupport/decorators/subclass.js
var i4 = /* @__PURE__ */ new Set();
var n5 = /* @__PURE__ */ new Set();
function a4(t4) {
  return (o8) => {
    o8.prototype.declaredClass = t4, l3(o8);
    const s8 = [], a5 = [];
    let c6 = o8.prototype;
    for (; c6; ) c6.hasOwnProperty("initialize") && !i4.has(c6.initialize) && (i4.add(c6.initialize), s8.push(c6.initialize)), c6.hasOwnProperty("destroy") && !n5.has(c6.destroy) && (n5.add(c6.destroy), a5.push(c6.destroy)), c6 = Object.getPrototypeOf(c6);
    i4.clear(), n5.clear();
    class p5 extends o8 {
      constructor(...t5) {
        if (super(...t5), this.constructor === p5 && "function" == typeof this.postscript) {
          if (s8.length && Object.defineProperty(this, "initialize", { enumerable: false, configurable: true, value() {
            for (let e4 = s8.length - 1; e4 >= 0; e4--) s8[e4].call(this);
          } }), a5.length) {
            let t6 = false;
            const r3 = this[o5];
            Object.defineProperty(this, "destroy", { enumerable: false, configurable: true, value() {
              if (!t6) {
                t6 = true, r3.call(this);
                for (let e4 = 0; e4 < a5.length; e4++) a5[e4].call(this);
              }
            } });
          }
          this.postscript(...t5);
        }
      }
    }
    return p5.__accessorMetadata__ = o4(o8.prototype), p5.prototype.declaredClass = t4, p5;
  };
}
function c5(e4, t4) {
  return null == t4.get ? function() {
    const t5 = this.__accessor__.properties.get(e4);
    if (void 0 === t5) return;
    i(t5.observerObject);
    const r3 = this.__accessor__.store;
    return r3.has(e4) ? r3.get(e4) : t5.metadata.value;
  } : function() {
    const t5 = this.__accessor__.properties.get(e4);
    if (void 0 !== t5) return t5.getComputed();
  };
}
function l3(e4) {
  const o8 = e4.prototype, i5 = o4(o8), n6 = {};
  for (const r3 of Object.getOwnPropertyNames(i5)) {
    const e5 = i5[r3];
    c4(e5), n6[r3] = { enumerable: true, configurable: true, get: c5(r3, e5), set(o9) {
      const s8 = this.__accessor__;
      if (void 0 !== s8) {
        if (!Object.isFrozen(this)) {
          if (s8.initialized && e5.readOnly) throw new TypeError(`[accessor] cannot assign to read-only property '${r3}' of ${this.declaredClass}`);
          if (s8.lifecycle === I.CONSTRUCTED && e5.constructOnly) throw new TypeError(`[accessor] cannot assign to construct-only property '${r3}' of ${this.declaredClass}`);
          s8.set(r3, o9);
        }
      } else Object.defineProperty(this, r3, { enumerable: true, configurable: true, writable: true, value: o9 });
    } };
  }
  Object.defineProperties(e4.prototype, n6);
}

export {
  o4 as o,
  c,
  t,
  y,
  d,
  j2 as j,
  o5 as o2,
  I,
  t2,
  u2 as u,
  y3 as y2,
  t3,
  s7 as s,
  a3 as a,
  a4 as a2,
  l3 as l
};
//# sourceMappingURL=chunk-JN4FSB7Y.js.map
