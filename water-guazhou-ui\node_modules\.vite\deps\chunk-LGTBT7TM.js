import {
  e as e2
} from "./chunk-C2ZE76VJ.js";
import {
  o as o4
} from "./chunk-Q6BEUTMN.js";
import {
  d
} from "./chunk-BOT4BSSB.js";
import {
  a
} from "./chunk-QB6AUIQ2.js";
import {
  r
} from "./chunk-REGYRSW7.js";
import {
  n
} from "./chunk-Y424ZXTG.js";
import {
  e as e3
} from "./chunk-UB5FTTH5.js";
import {
  i,
  t as t2
} from "./chunk-6GW7M2AQ.js";
import {
  t
} from "./chunk-ND2RJTSZ.js";
import {
  h
} from "./chunk-L4Y6W6Y5.js";
import {
  u
} from "./chunk-IRHOIB3A.js";
import {
  v
} from "./chunk-N3S5O3YO.js";
import {
  o as o3
} from "./chunk-BPRRRPC3.js";
import {
  e
} from "./chunk-GXMOAZWH.js";
import {
  o as o2
} from "./chunk-LHO3WKNH.js";
import {
  o
} from "./chunk-RFTQI4ZD.js";
import {
  O
} from "./chunk-CPQSD22U.js";

// node_modules/@arcgis/core/chunks/ColorMaterial.glsl.js
function v2(v3) {
  const f2 = new o2(), { vertex: C, fragment: y } = f2, j = v3.output === h.Depth, L = v3.hasMultipassTerrain && (v3.output === h.Color || v3.output === h.Alpha);
  return v(C, v3), f2.include(r, v3), f2.include(e2, v3), f2.include(d, v3), f2.attributes.add(O.POSITION, "vec3"), f2.varyings.add("vpos", "vec3"), L && f2.varyings.add("depth", "float"), j && (f2.include(o4, v3), i(f2), t2(f2)), C.code.add(o`
    void main(void) {
      vpos = position;
      forwardNormalizedVertexColor();
      forwardObjectAndLayerIdColor();
      ${L ? "depth = (view * vec4(vpos, 1.0)).z;" : ""}
      gl_Position = ${j ? o`transformPositionWithDepth(proj, view, vpos, nearFar, linearDepth);` : o`transformPosition(proj, view, vpos);`}
    }
  `), f2.include(u, v3), L && f2.include(n, v3), y.include(e3), y.uniforms.add(new e("eColor", (e4) => e4.color)), v3.output === h.Highlight && f2.include(a, v3), y.code.add(o`
  void main() {
    discardBySlice(vpos);
    ${L ? "terrainDepthTest(gl_FragCoord, depth);" : ""}
    vec4 fColor = ${v3.hasVertexColors ? "vColor * eColor;" : "eColor;"}

    ${v3.output === h.ObjectAndLayerIdColor ? o`fColor.a = 1.0;` : ""}

    if (fColor.a < ${o.float(t)}) {
      discard;
    }

    ${v3.output === h.Alpha ? o`gl_FragColor = vec4(fColor.a);` : ""}

    ${v3.output === h.Color ? o`gl_FragColor = highlightSlice(fColor, vpos); ${v3.transparencyPassType === o3.Color ? "gl_FragColor = premultiplyAlpha(gl_FragColor);" : ""}` : ""}
    ${v3.output === h.Highlight ? o`outputHighlight();` : ""};
    ${v3.output === h.Depth ? o`outputDepth(linearDepth);` : ""};
    ${v3.output === h.ObjectAndLayerIdColor ? o`outputObjectAndLayerIdColor();` : ""}
  }
  `), f2;
}
var f = Object.freeze(Object.defineProperty({ __proto__: null, build: v2 }, Symbol.toStringTag, { value: "Module" }));

export {
  v2 as v,
  f
};
//# sourceMappingURL=chunk-LGTBT7TM.js.map
