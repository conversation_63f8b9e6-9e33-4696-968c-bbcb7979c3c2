import {
  a as a2
} from "./chunk-2WMCP27R.js";
import {
  T,
  g
} from "./chunk-N7ADFPOO.js";
import {
  x
} from "./chunk-VNYCO3JG.js";
import {
  i
} from "./chunk-FLHLIVG4.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o as o3
} from "./chunk-PEEUPDEG.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import {
  o as o2
} from "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/portal/timeUnitKebabDictionary.js
var e2 = o2()({ esriTimeUnitsMilliseconds: "milliseconds", esriTimeUnitsSeconds: "seconds", esriTimeUnitsMinutes: "minutes", esriTimeUnitsHours: "hours", esriTimeUnitsDays: "days", esriTimeUnitsWeeks: "weeks", esriTimeUnitsMonths: "months", esriTimeUnitsYears: "years", esriTimeUnitsDecades: "decades", esriTimeUnitsCenturies: "centuries", esriTimeUnitsUnknown: void 0 });

// node_modules/@arcgis/core/TimeInterval.js
var l2 = class extends i(l) {
  constructor(o4) {
    super(o4), this.unit = "milliseconds", this.value = 0;
  }
  toMilliseconds() {
    return g(this.value, this.unit, "milliseconds");
  }
};
e([o3(e2, { nonNullable: true })], l2.prototype, "unit", void 0), e([y({ type: Number, json: { write: true }, nonNullable: true })], l2.prototype, "value", void 0), l2 = e([a("esri.TimeInterval")], l2);
var a3 = l2;

// node_modules/@arcgis/core/layers/support/TimeInfo.js
function u(e3, t) {
  return a3.fromJSON({ value: e3, unit: t });
}
var d = class extends i(l) {
  constructor(e3) {
    super(e3), this.cumulative = false, this.endField = null, this.fullTimeExtent = null, this.hasLiveData = false, this.interval = null, this.startField = null, this.timeReference = null, this.trackIdField = null, this.useTime = true;
  }
  readFullTimeExtent(e3, r3) {
    if (!r3.timeExtent || !Array.isArray(r3.timeExtent) || 2 !== r3.timeExtent.length) return null;
    const l3 = new Date(r3.timeExtent[0]), i2 = new Date(r3.timeExtent[1]);
    return new T({ start: l3, end: i2 });
  }
  writeFullTimeExtent(e3, t) {
    e3 && r(e3.start) && r(e3.end) ? t.timeExtent = [e3.start.getTime(), e3.end.getTime()] : t.timeExtent = null;
  }
  readInterval(e3, t) {
    return t.timeInterval && t.timeIntervalUnits ? u(t.timeInterval, t.timeIntervalUnits) : t.defaultTimeInterval && t.defaultTimeIntervalUnits ? u(t.defaultTimeInterval, t.defaultTimeIntervalUnits) : null;
  }
  writeInterval(e3, t) {
    t.timeInterval = (e3 == null ? void 0 : e3.toJSON().value) ?? null, t.timeIntervalUnits = (e3 == null ? void 0 : e3.toJSON().unit) ?? null;
  }
};
e([y({ type: Boolean, json: { name: "exportOptions.timeDataCumulative", write: true } })], d.prototype, "cumulative", void 0), e([y({ type: String, json: { name: "endTimeField", write: { enabled: true, allowNull: true } } })], d.prototype, "endField", void 0), e([y({ type: T, json: { write: { enabled: true, allowNull: true } } })], d.prototype, "fullTimeExtent", void 0), e([o("fullTimeExtent", ["timeExtent"])], d.prototype, "readFullTimeExtent", null), e([r2("fullTimeExtent")], d.prototype, "writeFullTimeExtent", null), e([y({ type: Boolean, json: { write: true } })], d.prototype, "hasLiveData", void 0), e([y({ type: a3, json: { write: { enabled: true, allowNull: true } } })], d.prototype, "interval", void 0), e([o("interval", ["timeInterval", "timeIntervalUnits", "defaultTimeInterval", "defaultTimeIntervalUnits"])], d.prototype, "readInterval", null), e([r2("interval")], d.prototype, "writeInterval", null), e([y({ type: String, json: { name: "startTimeField", write: { enabled: true, allowNull: true } } })], d.prototype, "startField", void 0), e([y({ type: a2, json: { write: { enabled: true, allowNull: true } } })], d.prototype, "timeReference", void 0), e([y({ type: String, json: { write: { enabled: true, allowNull: true } } })], d.prototype, "trackIdField", void 0), e([y({ type: Boolean, json: { name: "exportOptions.useTime", write: true } })], d.prototype, "useTime", void 0), d = e([a("esri.layers.support.TimeInfo")], d);
var v = d;

// node_modules/@arcgis/core/layers/mixins/TemporalLayer.js
var a4 = (a5) => {
  let f = class extends a5 {
    constructor() {
      super(...arguments), this.timeExtent = null, this.timeOffset = null, this.useViewTime = true;
    }
    readOffset(e3, t) {
      const o4 = t.timeInfo.exportOptions;
      if (!o4) return null;
      const i2 = o4.timeOffset, s = e2.fromJSON(o4.timeOffsetUnits);
      return i2 && s ? new a3({ value: i2, unit: s }) : null;
    }
    set timeInfo(e3) {
      x(e3, this.fieldsIndex), this._set("timeInfo", e3);
    }
  };
  return e([y({ type: T, json: { write: false } })], f.prototype, "timeExtent", void 0), e([y({ type: a3 })], f.prototype, "timeOffset", void 0), e([o("service", "timeOffset", ["timeInfo.exportOptions"])], f.prototype, "readOffset", null), e([y({ value: null, type: v, json: { write: true, origins: { "web-document": { read: false, write: false }, "portal-item": { read: false, write: false } } } })], f.prototype, "timeInfo", null), e([y({ type: Boolean, json: { read: { source: "timeAnimation" }, write: { target: "timeAnimation" }, origins: { "web-scene": { read: false, write: false } } } })], f.prototype, "useViewTime", void 0), f = e([a("esri.layers.mixins.TemporalLayer")], f), f;
};

export {
  v,
  a4 as a
};
//# sourceMappingURL=chunk-RZCOX454.js.map
