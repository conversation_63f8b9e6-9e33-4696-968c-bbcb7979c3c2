{"version": 3, "sources": ["../../@arcgis/core/geometry/support/heightModelInfoUtils.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import e from\"../../core/Error.js\";import{isSome as t}from\"../../core/maybe.js\";import n from\"../HeightModelInfo.js\";import{parse as i}from\"../../layers/support/arcgisLayerUrl.js\";function r(t,i){if(!t)return null;if(!c(t))return new e(\"webscene:unsupported-height-model-info\",\"The vertical coordinate system of the scene is not supported\",{heightModelInfo:t});const r=t.heightUnit,o=n.deriveUnitFromSR(t,i).heightUnit;return r!==o?new e(\"webscene:incompatible-height-unit\",`The vertical units of the scene (${r}) must match the horizontal units of the scene (${o})`,{verticalUnit:r,horizontalUnit:o}):null}function o(t,i,r){const o=h(t),c=i,u=a(o,c,r);if(o){const i=n.deriveUnitFromSR(o,t.spatialReference).heightUnit;if(!r&&i!==o.heightUnit){const t=new e(\"layerview:unmatched-height-unit\",`The vertical units of the layer must match the horizontal units (${i})`,{horizontalUnit:i});return new e(\"layerview:unsupported-height-model-info\",\"The vertical coordinate system of the layer is not supported\",{heightModelInfo:o,error:t})}}if(!l(t)||u===s.Unsupported)return new e(\"layerview:unsupported-height-model-info\",\"The vertical coordinate system of the layer is not supported\",{heightModelInfo:o});switch(u){case s.Units:{const t=o?.heightUnit||\"unknown\",n=c?.heightUnit||\"unknown\",i=new e(\"layerview:incompatible-height-unit\",`The vertical units of the layer (${t}) must match the vertical units of the scene (${n})`,{layerUnit:t,sceneUnit:n});return new e(\"layerview:incompatible-height-model-info\",\"The vertical coordinate system of the layer is incompatible with the scene\",{layerHeightModelInfo:o,sceneHeightModelInfo:c,error:i})}case s.HeightModel:{const t=o?.heightModel||\"unknown\",n=c?.heightModel||\"unknown\",i=new e(\"layerview:incompatible-height-model\",`The height model of the layer (${t}) must match the height model of the scene (${n})`,{layerHeightModel:t,sceneHeightModel:n});return new e(\"layerview:incompatible-height-model-info\",\"The vertical coordinate system of the layer is incompatible with the scene\",{layerHeightModelInfo:o,sceneHeightModelInfo:c,error:i})}case s.CRS:{const t=o?.vertCRS||\"unknown\",n=c?.vertCRS||\"unknown\",i=new e(\"layerview:incompatible-vertical-datum\",`The vertical datum of the layer (${t}) must match the vertical datum of the scene (${n})`,{layerDatum:t,sceneDatum:n});return new e(\"layerview:incompatible-height-model-info\",\"The vertical coordinate system of the layer is incompatible with the scene\",{layerHeightModelInfo:o,sceneHeightModelInfo:c,error:i})}}return null}function a(e,t,n){if(!c(e)||!c(t))return s.Unsupported;if(null==e||null==t)return s.Ok;if(!n&&e.heightUnit!==t.heightUnit)return s.Units;if(e.heightModel!==t.heightModel)return s.HeightModel;switch(e.heightModel){case\"gravity-related-height\":return s.Ok;case\"ellipsoidal\":return e.vertCRS===t.vertCRS?s.Ok:s.CRS;default:return s.Unsupported}}var s;function c(e){return null==e||null!=e.heightModel&&null!=e.heightUnit}function l(e){return\"heightModelInfo\"in e&&null!=e.heightModelInfo||null!=e.spatialReference||!g(e)}function h(e){const r=e.url?i(e.url):void 0,o=e.spatialReference?.vcsWkid;return!(null==o&&t(r)&&\"ImageServer\"===r.serverType)&&u(e)&&e.heightModelInfo?e.heightModelInfo:g(e)?n.deriveUnitFromSR(p,e.spatialReference):null}function u(e){return\"heightModelInfo\"in e}function d(e){if(\"unknown\"===e.type||!(\"capabilities\"in e))return!1;switch(e.type){case\"csv\":case\"feature\":case\"geojson\":case\"subtype-group\":case\"ogc-feature\":case\"oriented-imagery\":case\"wfs\":case\"knowledge-graph-sublayer\":return!0;default:return!1}}function g(e){return d(e)?!!(e.capabilities&&e.capabilities.data&&e.capabilities.data.supportsZ):m(e)}function f(e){return null!=e.layers||m(e)||d(e)||u(e)}function m(e){switch(e.type){case\"building-scene\":case\"elevation\":case\"integrated-mesh\":case\"point-cloud\":case\"scene\":case\"voxel\":return!0;case\"base-dynamic\":case\"base-elevation\":case\"base-tile\":case\"bing-maps\":case\"csv\":case\"dimension\":case\"geojson\":case\"feature\":case\"subtype-group\":case\"geo-rss\":case\"graphics\":case\"group\":case\"imagery\":case\"imagery-tile\":case\"kml\":case\"knowledge-graph\":case\"link-chart\":case\"knowledge-graph-sublayer\":case\"line-of-sight\":case\"map-image\":case\"map-notes\":case\"media\":case\"ogc-feature\":case\"open-street-map\":case\"oriented-imagery\":case\"route\":case\"stream\":case\"tile\":case\"unknown\":case\"unsupported\":case\"vector-tile\":case\"wcs\":case\"web-tile\":case\"wfs\":case\"wms\":case\"wmts\":case null:return!1}return!1}!function(e){e[e.Ok=0]=\"Ok\",e[e.Units=1]=\"Units\",e[e.HeightModel=2]=\"HeightModel\",e[e.CRS=3]=\"CRS\",e[e.Unsupported=4]=\"Unsupported\"}(s||(s={}));const p=new n({heightModel:\"gravity-related-height\"});export{h as deriveHeightModelInfoFromLayer,o as rejectLayerError,f as supportsHeightModelInfo,r as validateWebSceneError};\n"], "mappings": ";;;;;;;;;;;;;;AAIynB,SAAS,EAAE,GAAE,GAAEA,IAAE;AAAC,QAAMC,KAAE,EAAE,CAAC,GAAEC,KAAE,GAAEC,KAAE,EAAEF,IAAEC,IAAEF,EAAC;AAAE,MAAGC,IAAE;AAAC,UAAMG,KAAE,EAAE,iBAAiBH,IAAE,EAAE,gBAAgB,EAAE;AAAW,QAAG,CAACD,MAAGI,OAAIH,GAAE,YAAW;AAAC,YAAMI,KAAE,IAAI,EAAE,mCAAkC,oEAAoED,EAAC,KAAI,EAAC,gBAAeA,GAAC,CAAC;AAAE,aAAO,IAAI,EAAE,2CAA0C,gEAA+D,EAAC,iBAAgBH,IAAE,OAAMI,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,CAAC,KAAGF,OAAIG,GAAE,YAAY,QAAO,IAAI,EAAE,2CAA0C,gEAA+D,EAAC,iBAAgBL,GAAC,CAAC;AAAE,UAAOE,IAAE;AAAA,IAAC,KAAKG,GAAE,OAAM;AAAC,YAAMD,MAAEJ,MAAA,gBAAAA,GAAG,eAAY,WAAU,KAAEC,MAAA,gBAAAA,GAAG,eAAY,WAAUE,KAAE,IAAI,EAAE,sCAAqC,oCAAoCC,EAAC,iDAAiD,CAAC,KAAI,EAAC,WAAUA,IAAE,WAAU,EAAC,CAAC;AAAE,aAAO,IAAI,EAAE,4CAA2C,8EAA6E,EAAC,sBAAqBJ,IAAE,sBAAqBC,IAAE,OAAME,GAAC,CAAC;AAAA,IAAC;AAAA,IAAC,KAAKE,GAAE,aAAY;AAAC,YAAMD,MAAEJ,MAAA,gBAAAA,GAAG,gBAAa,WAAU,KAAEC,MAAA,gBAAAA,GAAG,gBAAa,WAAUE,KAAE,IAAI,EAAE,uCAAsC,kCAAkCC,EAAC,+CAA+C,CAAC,KAAI,EAAC,kBAAiBA,IAAE,kBAAiB,EAAC,CAAC;AAAE,aAAO,IAAI,EAAE,4CAA2C,8EAA6E,EAAC,sBAAqBJ,IAAE,sBAAqBC,IAAE,OAAME,GAAC,CAAC;AAAA,IAAC;AAAA,IAAC,KAAKE,GAAE,KAAI;AAAC,YAAMD,MAAEJ,MAAA,gBAAAA,GAAG,YAAS,WAAU,KAAEC,MAAA,gBAAAA,GAAG,YAAS,WAAUE,KAAE,IAAI,EAAE,yCAAwC,oCAAoCC,EAAC,iDAAiD,CAAC,KAAI,EAAC,YAAWA,IAAE,YAAW,EAAC,CAAC;AAAE,aAAO,IAAI,EAAE,4CAA2C,8EAA6E,EAAC,sBAAqBJ,IAAE,sBAAqBC,IAAE,OAAME,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,CAAC,KAAG,CAAC,EAAE,CAAC,EAAE,QAAOE,GAAE;AAAY,MAAG,QAAM,KAAG,QAAM,EAAE,QAAOA,GAAE;AAAG,MAAG,CAAC,KAAG,EAAE,eAAa,EAAE,WAAW,QAAOA,GAAE;AAAM,MAAG,EAAE,gBAAc,EAAE,YAAY,QAAOA,GAAE;AAAY,UAAO,EAAE,aAAY;AAAA,IAAC,KAAI;AAAyB,aAAOA,GAAE;AAAA,IAAG,KAAI;AAAc,aAAO,EAAE,YAAU,EAAE,UAAQA,GAAE,KAAGA,GAAE;AAAA,IAAI;AAAQ,aAAOA,GAAE;AAAA,EAAW;AAAC;AAAC,IAAIA;AAAE,SAAS,EAAE,GAAE;AAAC,SAAO,QAAM,KAAG,QAAM,EAAE,eAAa,QAAM,EAAE;AAAU;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,qBAAoB,KAAG,QAAM,EAAE,mBAAiB,QAAM,EAAE,oBAAkB,CAAC,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAJrgG;AAIsgG,QAAMN,KAAE,EAAE,MAAI,EAAE,EAAE,GAAG,IAAE,QAAOC,MAAE,OAAE,qBAAF,mBAAoB;AAAQ,SAAM,EAAE,QAAMA,MAAG,EAAED,EAAC,KAAG,kBAAgBA,GAAE,eAAa,EAAE,CAAC,KAAG,EAAE,kBAAgB,EAAE,kBAAgB,EAAE,CAAC,IAAE,EAAE,iBAAiB,GAAE,EAAE,gBAAgB,IAAE;AAAI;AAAC,SAAS,EAAE,GAAE;AAAC,SAAM,qBAAoB;AAAC;AAAC,SAASO,GAAE,GAAE;AAAC,MAAG,cAAY,EAAE,QAAM,EAAE,kBAAiB,GAAG,QAAM;AAAG,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAA,IAAM,KAAI;AAAA,IAAU,KAAI;AAAA,IAAU,KAAI;AAAA,IAAgB,KAAI;AAAA,IAAc,KAAI;AAAA,IAAmB,KAAI;AAAA,IAAM,KAAI;AAA2B,aAAM;AAAA,IAAG;AAAQ,aAAM;AAAA,EAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAOA,GAAE,CAAC,IAAE,CAAC,EAAE,EAAE,gBAAc,EAAE,aAAa,QAAM,EAAE,aAAa,KAAK,aAAW,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,QAAM,EAAE,UAAQ,EAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,EAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAA,IAAiB,KAAI;AAAA,IAAY,KAAI;AAAA,IAAkB,KAAI;AAAA,IAAc,KAAI;AAAA,IAAQ,KAAI;AAAQ,aAAM;AAAA,IAAG,KAAI;AAAA,IAAe,KAAI;AAAA,IAAiB,KAAI;AAAA,IAAY,KAAI;AAAA,IAAY,KAAI;AAAA,IAAM,KAAI;AAAA,IAAY,KAAI;AAAA,IAAU,KAAI;AAAA,IAAU,KAAI;AAAA,IAAgB,KAAI;AAAA,IAAU,KAAI;AAAA,IAAW,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAU,KAAI;AAAA,IAAe,KAAI;AAAA,IAAM,KAAI;AAAA,IAAkB,KAAI;AAAA,IAAa,KAAI;AAAA,IAA2B,KAAI;AAAA,IAAgB,KAAI;AAAA,IAAY,KAAI;AAAA,IAAY,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAc,KAAI;AAAA,IAAkB,KAAI;AAAA,IAAmB,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAS,KAAI;AAAA,IAAO,KAAI;AAAA,IAAU,KAAI;AAAA,IAAc,KAAI;AAAA,IAAc,KAAI;AAAA,IAAM,KAAI;AAAA,IAAW,KAAI;AAAA,IAAM,KAAI;AAAA,IAAM,KAAI;AAAA,IAAO,KAAK;AAAK,aAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,CAAC,SAAS,GAAE;AAAC,IAAE,EAAE,KAAG,CAAC,IAAE,MAAK,EAAE,EAAE,QAAM,CAAC,IAAE,SAAQ,EAAE,EAAE,cAAY,CAAC,IAAE,eAAc,EAAE,EAAE,MAAI,CAAC,IAAE,OAAM,EAAE,EAAE,cAAY,CAAC,IAAE;AAAa,EAAED,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAM,IAAE,IAAI,EAAE,EAAC,aAAY,yBAAwB,CAAC;", "names": ["r", "o", "c", "u", "i", "t", "s", "d"]}