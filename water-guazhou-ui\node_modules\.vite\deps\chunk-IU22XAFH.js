import {
  h,
  l
} from "./chunk-DTQ34PEY.js";
import {
  a
} from "./chunk-FZ7BG3VX.js";
import {
  L,
  m
} from "./chunk-RFYOGM4H.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import {
  r as r2
} from "./chunk-LTKA6OKA.js";
import {
  t
} from "./chunk-REW33H3I.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/intl/substitute.js
var i = s.getLogger("esri.intl.substitute");
function s3(t2, r3, n = {}) {
  const { format: o2 = {} } = n;
  return r2(t2, (t3) => u(t3, r3, o2));
}
function u(t2, e, n) {
  let o2, i3;
  const s6 = t2.indexOf(":");
  if (-1 === s6 ? o2 = t2.trim() : (o2 = t2.slice(0, s6).trim(), i3 = t2.slice(s6 + 1).trim()), !o2) return "";
  const u4 = t(o2, e);
  if (null == u4) return "";
  const m2 = (i3 ? n == null ? void 0 : n[i3] : null) ?? (n == null ? void 0 : n[o2]);
  return m2 ? c(u4, m2) : i3 ? a2(u4, i3) : f(u4);
}
function c(t2, r3) {
  switch (r3.type) {
    case "date":
      return L(t2, r3.intlOptions);
    case "number":
      return m(t2, r3.intlOptions);
    default:
      return i.warn("missing format descriptor for key {key}"), f(t2);
  }
}
function a2(t2, r3) {
  switch (r3.toLowerCase()) {
    case "dateformat":
      return L(t2);
    case "numberformat":
      return m(t2);
    default:
      return i.warn(`inline format is unsupported since 4.12: ${r3}`), /^(dateformat|datestring)/i.test(r3) ? L(t2) : /^numberformat/i.test(r3) ? m(t2) : f(t2);
  }
}
function f(t2) {
  switch (typeof t2) {
    case "string":
      return t2;
    case "number":
      return m(t2);
    case "boolean":
      return "" + t2;
    default:
      return t2 instanceof Date ? L(t2) : "";
  }
}

// node_modules/@arcgis/core/intl/t9n.js
async function o(e, n, o2, i3) {
  const a5 = n.exec(o2);
  if (!a5) throw new s2("esri-intl:invalid-bundle", `Bundle id "${o2}" is not compatible with the pattern "${n}"`);
  const c4 = a5[1] ? `${a5[1]}/` : "", l3 = a5[2], w = h(i3), h2 = `${c4}${l3}.json`, u4 = w ? `${c4}${l3}_${w}.json` : h2;
  let f2;
  try {
    f2 = await s4(e(u4));
  } catch (d) {
    if (u4 === h2) throw new s2("intl:unknown-bundle", `Bundle "${o2}" cannot be loaded`, { error: d });
    try {
      f2 = await s4(e(h2));
    } catch (d2) {
      throw new s2("intl:unknown-bundle", `Bundle "${o2}" cannot be loaded`, { error: d2 });
    }
  }
  return f2;
}
async function s4(t2) {
  if (r(c2.fetchBundleAsset)) return c2.fetchBundleAsset(t2);
  const r3 = await U(t2, { responseType: "text" });
  return JSON.parse(r3.data);
}
var i2 = class {
  constructor({ base: e = "", pattern: t2, location: n = new URL(window.location.href) }) {
    let r3;
    r3 = "string" == typeof n ? (e2) => new URL(e2, new URL(n, window.location.href)).href : n instanceof URL ? (e2) => new URL(e2, n).href : n, this.pattern = "string" == typeof t2 ? new RegExp(`^${t2}`) : t2, this.getAssetUrl = r3, e = e ? e.endsWith("/") ? e : e + "/" : "", this.matcher = new RegExp(`^${e}(?:(.*)/)?(.*)$`);
  }
  fetchMessageBundle(e, t2) {
    return o(this.getAssetUrl, this.matcher, e, t2);
  }
};
function a3(e) {
  return new i2(e);
}
var c2 = {};

// node_modules/@arcgis/core/intl.js
l(a3({ pattern: "esri/", location: a }));

export {
  s3 as s
};
//# sourceMappingURL=chunk-IU22XAFH.js.map
