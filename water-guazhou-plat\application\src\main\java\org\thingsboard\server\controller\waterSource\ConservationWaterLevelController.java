package org.thingsboard.server.controller.waterSource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.waterSource.ConservationWaterLevelService;
import org.thingsboard.server.dao.waterSource.ConservationAnalysisService;
import org.thingsboard.server.dao.waterSource.entity.ConservationWaterLevel;
import org.thingsboard.server.dao.waterSource.entity.ConservationAnalysis;
import org.thingsboard.server.service.common.response.IstarResponse;
import org.thingsboard.server.common.data.UUIDConverter;

import java.util.List;
import java.util.Map;

/**
 * 涵养水位管理Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/conservation-water-level")
@Api(tags = "涵养水位管理")
public class ConservationWaterLevelController extends BaseController {

    @Autowired
    private ConservationWaterLevelService conservationWaterLevelService;
    
    @Autowired
    private ConservationAnalysisService conservationAnalysisService;

    // ==================== 涵养水位数据相关接口 ====================

    @ApiOperation(value = "保存涵养水位数据")
    @PostMapping("/water-level")
    public IstarResponse saveWaterLevel(@RequestBody ConservationWaterLevel waterLevel) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
            
            waterLevel.setTenantId(tenantId);
            waterLevel.setCreator(userId);
            
            // 计算液位变化（如果是更新操作）
            if (waterLevel.getId() != null) {
                ConservationWaterLevel existingData = conservationWaterLevelService.findById(waterLevel.getId());
                if (existingData != null) {
                    // 这里可以添加液位变化的计算逻辑
                    waterLevel.setLevelChange(waterLevel.getGroundwaterLevel() - existingData.getGroundwaterLevel());
                }
            }
            
            ConservationWaterLevel savedData = conservationWaterLevelService.save(waterLevel);
            return IstarResponse.ok(savedData);
        } catch (Exception e) {
            log.error("保存涵养水位数据失败", e);
            return IstarResponse.error("保存失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "更新涵养水位数据")
    @PutMapping("/water-level")
    public IstarResponse updateWaterLevel(@RequestBody ConservationWaterLevel waterLevel) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            waterLevel.setTenantId(tenantId);
            
            ConservationWaterLevel updatedData = conservationWaterLevelService.update(waterLevel);
            return IstarResponse.ok(updatedData);
        } catch (Exception e) {
            log.error("更新涵养水位数据失败", e);
            return IstarResponse.error("更新失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "删除涵养水位数据")
    @DeleteMapping("/water-level/{id}")
    public IstarResponse deleteWaterLevel(@PathVariable String id) throws ThingsboardException {
        try {
            conservationWaterLevelService.deleteById(id);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除涵养水位数据失败", e);
            return IstarResponse.error("删除失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取涵养水位数据详情")
    @GetMapping("/water-level/{id}")
    public IstarResponse getWaterLevelById(@PathVariable String id) throws ThingsboardException {
        try {
            ConservationWaterLevel data = conservationWaterLevelService.findById(id);
            return IstarResponse.ok(data);
        } catch (Exception e) {
            log.error("获取涵养水位数据详情失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询涵养水位数据")
    @GetMapping("/water-level/list")
    public IstarResponse getWaterLevelList(@RequestParam Map<String, Object> params) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationWaterLevelService.getList(params, tenantId));
        } catch (Exception e) {
            log.error("查询涵养水位数据列表失败", e);
            return IstarResponse.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取水位变化数据")
    @GetMapping("/water-level/change-data")
    public IstarResponse getWaterLevelChangeData(@RequestParam String stationId, 
                                                @RequestParam Long startTime, 
                                                @RequestParam Long endTime) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationWaterLevelService.getWaterLevelChangeData(stationId, startTime, endTime, tenantId));
        } catch (Exception e) {
            log.error("获取水位变化数据失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取测点最新水位数据")
    @GetMapping("/water-level/latest/{stationId}")
    public IstarResponse getLatestWaterLevel(@PathVariable String stationId) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationWaterLevelService.getLatestWaterLevel(stationId, tenantId));
        } catch (Exception e) {
            log.error("获取最新水位数据失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取统计数据")
    @GetMapping("/water-level/statistics")
    public IstarResponse getStatisticsData(@RequestParam String stationId, 
                                          @RequestParam Long startTime, 
                                          @RequestParam Long endTime) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationWaterLevelService.getStatisticsData(stationId, startTime, endTime, tenantId));
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "批量导入水位数据")
    @PostMapping("/water-level/batch-import")
    public IstarResponse batchImportWaterLevel(@RequestBody List<ConservationWaterLevel> waterLevelList) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
            
            // 设置租户ID和创建人
            for (ConservationWaterLevel waterLevel : waterLevelList) {
                waterLevel.setTenantId(tenantId);
                waterLevel.setCreator(userId);
                waterLevel.setDataSource(1); // 手动录入
            }
            
            int successCount = conservationWaterLevelService.batchImport(waterLevelList);
            return IstarResponse.ok("成功导入 " + successCount + " 条数据");
        } catch (Exception e) {
            log.error("批量导入水位数据失败", e);
            return IstarResponse.error("导入失败：" + e.getMessage());
        }
    }

    // ==================== 智能分析相关接口 ====================

    @ApiOperation(value = "执行智能分析")
    @PostMapping("/analysis/perform")
    public IstarResponse performIntelligentAnalysis(@RequestParam String stationId, 
                                                   @RequestParam Long startTime, 
                                                   @RequestParam Long endTime) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
            
            ConservationAnalysis analysis = conservationAnalysisService.performAnalysis(stationId, startTime, endTime, tenantId, userId);
            return IstarResponse.ok(analysis);
        } catch (Exception e) {
            log.error("执行智能分析失败", e);
            return IstarResponse.error("分析失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "分页查询分析结果")
    @GetMapping("/analysis/list")
    public IstarResponse getAnalysisList(@RequestParam Map<String, Object> params) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationAnalysisService.getList(params, tenantId));
        } catch (Exception e) {
            log.error("查询分析结果列表失败", e);
            return IstarResponse.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取分析结果详情")
    @GetMapping("/analysis/{id}")
    public IstarResponse getAnalysisById(@PathVariable String id) throws ThingsboardException {
        try {
            ConservationAnalysis analysis = conservationAnalysisService.findById(id);
            return IstarResponse.ok(analysis);
        } catch (Exception e) {
            log.error("获取分析结果详情失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取测点最新分析结果")
    @GetMapping("/analysis/latest/{stationId}")
    public IstarResponse getLatestAnalysis(@PathVariable String stationId) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationAnalysisService.getLatestAnalysis(stationId, tenantId));
        } catch (Exception e) {
            log.error("获取最新分析结果失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取风险等级统计")
    @GetMapping("/analysis/risk-statistics")
    public IstarResponse getRiskLevelStatistics() throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationAnalysisService.getRiskLevelStatistics(tenantId));
        } catch (Exception e) {
            log.error("获取风险等级统计失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取涵养潜力趋势")
    @GetMapping("/analysis/potential-trend")
    public IstarResponse getConservationPotentialTrend(@RequestParam String stationId, 
                                                      @RequestParam Long startTime, 
                                                      @RequestParam Long endTime) throws ThingsboardException {
        try {
            String tenantId = UUIDConverter.fromTimeUUID(getTenantId().getId());
            return IstarResponse.ok(conservationAnalysisService.getConservationPotentialTrend(stationId, startTime, endTime, tenantId));
        } catch (Exception e) {
            log.error("获取涵养潜力趋势失败", e);
            return IstarResponse.error("获取失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "重新分析")
    @PostMapping("/analysis/re-analysis/{analysisId}")
    public IstarResponse reAnalysis(@PathVariable String analysisId) throws ThingsboardException {
        try {
            String userId = UUIDConverter.fromTimeUUID(getCurrentUser().getUuidId());
            ConservationAnalysis analysis = conservationAnalysisService.reAnalysis(analysisId, userId);
            return IstarResponse.ok(analysis);
        } catch (Exception e) {
            log.error("重新分析失败", e);
            return IstarResponse.error("重新分析失败：" + e.getMessage());
        }
    }

    @ApiOperation(value = "删除分析结果")
    @DeleteMapping("/analysis/{id}")
    public IstarResponse deleteAnalysis(@PathVariable String id) throws ThingsboardException {
        try {
            conservationAnalysisService.deleteById(id);
            return IstarResponse.ok();
        } catch (Exception e) {
            log.error("删除分析结果失败", e);
            return IstarResponse.error("删除失败：" + e.getMessage());
        }
    }
}
