{"version": 3, "sources": ["../../@esri/calcite-components/dist/components/Heading.js", "../../@esri/calcite-components/dist/components/focusTrapComponent.js", "../../@esri/calcite-components/dist/components/popover.js", "../../@esri/calcite-components/dist/components/array.js", "../../@esri/calcite-components/dist/components/action-menu.js"], "sourcesContent": ["/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { h } from '@stencil/core/internal/client/index.js';\n\nfunction constrainHeadingLevel(level) {\n  return Math.min(Math.max(Math.ceil(level), 1), 6);\n}\nconst Heading = (props, children) => {\n  const HeadingTag = props.level ? `h${props.level}` : \"div\";\n  delete props.level;\n  return h(HeadingTag, { ...props }, children);\n};\n\nexport { Heading as H, constrainHeadingLevel as c };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { w as tabbable, x as focusable, y as isTabbable, z as isFocusable, h as focusElement, A as tabbableOptions } from './dom.js';\n\n/*!\n* focus-trap 7.2.0\n* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE\n*/\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar activeFocusTraps = {\n  activateTrap: function activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      var activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n    var trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n  deactivateTrap: function deactivateTrap(trapStack, trap) {\n    var trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  }\n};\nvar isSelectableInput = function isSelectableInput(node) {\n  return node.tagName && node.tagName.toLowerCase() === 'input' && typeof node.select === 'function';\n};\nvar isEscapeEvent = function isEscapeEvent(e) {\n  return e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27;\n};\nvar isTabEvent = function isTabEvent(e) {\n  return e.key === 'Tab' || e.keyCode === 9;\n};\n\n// checks for TAB by default\nvar isKeyForward = function isKeyForward(e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nvar isKeyBackward = function isKeyBackward(e) {\n  return isTabEvent(e) && e.shiftKey;\n};\nvar delay = function delay(fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nvar findIndex = function findIndex(arr, fn) {\n  var idx = -1;\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nvar valueOrHandler = function valueOrHandler(value) {\n  for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    params[_key - 1] = arguments[_key];\n  }\n  return typeof value === 'function' ? value.apply(void 0, params) : value;\n};\nvar getActualTarget = function getActualTarget(event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function' ? event.composedPath()[0] : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nvar internalTrapStack = [];\nvar createFocusTrap = function createFocusTrap(elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;\n  var trapStack = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.trapStack) || internalTrapStack;\n  var config = _objectSpread2({\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward: isKeyForward,\n    isKeyBackward: isKeyBackward\n  }, userOptions);\n  var state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [],\n    // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined\n  };\n  var trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  var getOption = function getOption(configOverrideOptions, optionName, configOptionName) {\n    return configOverrideOptions && configOverrideOptions[optionName] !== undefined ? configOverrideOptions[optionName] : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  var findContainerIndex = function findContainerIndex(element) {\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(function (_ref) {\n      var container = _ref.container,\n        tabbableNodes = _ref.tabbableNodes;\n      return container.contains(element) ||\n      // fall back to explicit tabbable search which will take into consideration any\n      //  web components if the `tabbableOptions.getShadowRoot` option was used for\n      //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n      //  look inside web components even if open)\n      tabbableNodes.find(function (node) {\n        return node === element;\n      });\n    });\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  var getNodeForOption = function getNodeForOption(optionName) {\n    var optionValue = config[optionName];\n    if (typeof optionValue === 'function') {\n      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        params[_key2 - 1] = arguments[_key2];\n      }\n      optionValue = optionValue.apply(void 0, params);\n    }\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\"`\".concat(optionName, \"` was specified but was not a node, or did not return a node\"));\n    }\n    var node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\"`\".concat(optionName, \"` as selector refers to no known node\"));\n      }\n    }\n    return node;\n  };\n  var getInitialFocusNode = function getInitialFocusNode() {\n    var node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n    if (node === undefined) {\n      // option not specified: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        var firstTabbableGroup = state.tabbableGroups[0];\n        var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n    if (!node) {\n      throw new Error('Your focus-trap needs to have at least one focusable element');\n    }\n    return node;\n  };\n  var updateTabbableNodes = function updateTabbableNodes() {\n    state.containerGroups = state.containers.map(function (container) {\n      var tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes\n      var focusableNodes = focusable(container, config.tabbableOptions);\n      return {\n        container: container,\n        tabbableNodes: tabbableNodes,\n        focusableNodes: focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[tabbableNodes.length - 1] : null,\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode: function nextTabbableNode(node) {\n          var forward = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n          // NOTE: If tabindex is positive (in order to manipulate the tab order separate\n          //  from the DOM order), this __will not work__ because the list of focusableNodes,\n          //  while it contains tabbable nodes, does not sort its nodes in any order other\n          //  than DOM order, because it can't: Where would you place focusable (but not\n          //  tabbable) nodes in that order? They have no order, because they aren't tabbale...\n          // Support for positive tabindex is already broken and hard to manage (possibly\n          //  not supportable, TBD), so this isn't going to make things worse than they\n          //  already are, and at least makes things better for the majority of cases where\n          //  tabindex is either 0/unset or negative.\n          // FYI, positive tabindex issue: https://github.com/focus-trap/focus-trap/issues/375\n          var nodeIdx = focusableNodes.findIndex(function (n) {\n            return n === node;\n          });\n          if (nodeIdx < 0) {\n            return undefined;\n          }\n          if (forward) {\n            return focusableNodes.slice(nodeIdx + 1).find(function (n) {\n              return isTabbable(n, config.tabbableOptions);\n            });\n          }\n          return focusableNodes.slice(0, nodeIdx).reverse().find(function (n) {\n            return isTabbable(n, config.tabbableOptions);\n          });\n        }\n      };\n    });\n    state.tabbableGroups = state.containerGroups.filter(function (group) {\n      return group.tabbableNodes.length > 0;\n    });\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (state.tabbableGroups.length <= 0 && !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error('Your focus-trap must have at least one container with at least one tabbable node in it at all times');\n    }\n  };\n  var tryFocus = function tryFocus(node) {\n    if (node === false) {\n      return;\n    }\n    if (node === doc.activeElement) {\n      return;\n    }\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n    node.focus({\n      preventScroll: !!config.preventScroll\n    });\n    state.mostRecentlyFocusedNode = node;\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n  var getReturnFocusNode = function getReturnFocusNode(previousActiveElement) {\n    var node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  var checkPointerDown = function checkPointerDown(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus: config.returnFocusOnDeactivate && !isFocusable(target, config.tabbableOptions)\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  var checkFocusIn = function checkFocusIn(e) {\n    var target = getActualTarget(e);\n    var targetContained = findContainerIndex(target) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  var checkKeyNav = function checkKeyNav(event) {\n    var isBackward = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var target = getActualTarget(event);\n    updateTabbableNodes();\n    var destinationNode = null;\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      var containerIndex = findContainerIndex(target);\n      var containerGroup = containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        var startOfGroupIndex = findIndex(state.tabbableGroups, function (_ref2) {\n          var firstTabbableNode = _ref2.firstTabbableNode;\n          return target === firstTabbableNode;\n        });\n        if (startOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target, false))) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;\n          var destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        var lastOfGroupIndex = findIndex(state.tabbableGroups, function (_ref3) {\n          var lastTabbableNode = _ref3.lastTabbableNode;\n          return target === lastTabbableNode;\n        });\n        if (lastOfGroupIndex < 0 && (containerGroup.container === target || isFocusable(target, config.tabbableOptions) && !isTabbable(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target))) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;\n          var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];\n          destinationNode = _destinationGroup.firstTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  var checkKey = function checkKey(event) {\n    if (isEscapeEvent(event) && valueOrHandler(config.escapeDeactivates, event) !== false) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n  var checkClick = function checkClick(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  var addListeners = function addListeners() {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function () {\n      tryFocus(getInitialFocusNode());\n    }) : tryFocus(getInitialFocusNode());\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false\n    });\n    return trap;\n  };\n  var removeListeners = function removeListeners() {\n    if (!state.active) {\n      return;\n    }\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n    return trap;\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n    get paused() {\n      return state.paused;\n    },\n    activate: function activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n      var onActivate = getOption(activateOptions, 'onActivate');\n      var onPostActivate = getOption(activateOptions, 'onPostActivate');\n      var checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n      if (onActivate) {\n        onActivate();\n      }\n      var finishActivation = function finishActivation() {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);\n        return this;\n      }\n      finishActivation();\n      return this;\n    },\n    deactivate: function deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n      var options = _objectSpread2({\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus\n      }, deactivateOptions);\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n      var onDeactivate = getOption(options, 'onDeactivate');\n      var onPostDeactivate = getOption(options, 'onPostDeactivate');\n      var checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      var returnFocus = getOption(options, 'returnFocus', 'returnFocusOnDeactivate');\n      if (onDeactivate) {\n        onDeactivate();\n      }\n      var finishDeactivation = function finishDeactivation() {\n        delay(function () {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n      finishDeactivation();\n      return this;\n    },\n    pause: function pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n      state.paused = true;\n      removeListeners();\n      return this;\n    },\n    unpause: function unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n      return this;\n    },\n    updateContainerElements: function updateContainerElements(containerElements) {\n      var elementsAsArray = [].concat(containerElements).filter(Boolean);\n      state.containers = elementsAsArray.map(function (element) {\n        return typeof element === 'string' ? doc.querySelector(element) : element;\n      });\n      if (state.active) {\n        updateTabbableNodes();\n      }\n      return this;\n    }\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n  return trap;\n};\n\nconst trapStack = [];\n/**\n * Helper to set up the FocusTrap component.\n *\n * @param {FocusTrapComponent} component The FocusTrap component.\n */\nfunction connectFocusTrap(component) {\n  const { focusTrapEl } = component;\n  if (!focusTrapEl) {\n    return;\n  }\n  if (focusTrapEl.tabIndex == null) {\n    focusTrapEl.tabIndex = -1;\n  }\n  const focusTrapOptions = {\n    clickOutsideDeactivates: true,\n    document: focusTrapEl.ownerDocument,\n    escapeDeactivates: false,\n    fallbackFocus: focusTrapEl,\n    setReturnFocus: (el) => {\n      focusElement(el);\n      return false;\n    },\n    tabbableOptions,\n    trapStack\n  };\n  component.focusTrap = createFocusTrap(focusTrapEl, focusTrapOptions);\n}\n/**\n * Helper to activate the FocusTrap component.\n *\n * @param {FocusTrapComponent} component The FocusTrap component.\n */\nfunction activateFocusTrap(component) {\n  if (!component.focusTrapDisabled) {\n    component.focusTrap?.activate();\n  }\n}\n/**\n * Helper to deactivate the FocusTrap component.\n *\n * @param {FocusTrapComponent} component The FocusTrap component.\n */\nfunction deactivateFocusTrap(component) {\n  component.focusTrap?.deactivate();\n}\n/**\n * Helper to update the element(s) that are used within the FocusTrap component.\n *\n * @param {FocusTrapComponent} component The FocusTrap component.\n * @example\n * const modal = document.querySelector(\"calcite-modal\");\n * const input = document.createElement(\"calcite-input\");\n * content.appendChild(input);\n * await input.componentOnReady();\n * await modal.updateFocusTrapElements();\n * requestAnimationFrame(() => input.setFocus());\n */\nfunction updateFocusTrapElements(component) {\n  component.focusTrap?.updateContainerElements(component.focusTrapEl);\n}\n\nexport { activateFocusTrap as a, connectFocusTrap as c, deactivateFocusTrap as d, updateFocusTrapElements as u };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { proxyCustomElement, HTMLElement, createEvent, forceUpdate, h, Host } from '@stencil/core/internal/client/index.js';\nimport { f as filterComputedPlacements, c as connectFloatingUI, b as defaultOffsetDistance, u as updateAfterClose, a as disconnectFloatingUI, r as reposition, F as FloatingCSS } from './floating-ui.js';\nimport { c as connectFocusTrap, d as deactivateFocusTrap, a as activateFocusTrap, u as updateFocusTrapElements } from './focusTrapComponent.js';\nimport { i as isPrimaryPointerButton, t as toAriaBoolean, j as focusFirstTabbable, u as queryElementRoots } from './dom.js';\nimport { g as guid } from './guid.js';\nimport { c as connectOpenCloseComponent, d as disconnectOpenCloseComponent } from './openCloseComponent.js';\nimport { H as Heading } from './Heading.js';\nimport { c as connectLocalized, d as disconnectLocalized } from './locale.js';\nimport { u as updateMessages, c as connectMessages, s as setUpMessages, d as disconnectMessages } from './t9n.js';\nimport { i as isActivationKey } from './key.js';\nimport { a as setUpLoadableComponent, s as setComponentLoaded, c as componentLoaded } from './loadable.js';\nimport { d as defineCustomElement$3 } from './action.js';\nimport { d as defineCustomElement$2 } from './icon.js';\nimport { d as defineCustomElement$1 } from './loader.js';\n\nconst CSS = {\n  container: \"container\",\n  arrow: \"arrow\",\n  imageContainer: \"image-container\",\n  closeButtonContainer: \"close-button-container\",\n  closeButton: \"close-button\",\n  content: \"content\",\n  hasHeader: \"has-header\",\n  header: \"header\",\n  headerContent: \"header-content\",\n  heading: \"heading\"\n};\nconst defaultPopoverPlacement = \"auto\";\nconst ARIA_CONTROLS = \"aria-controls\";\nconst ARIA_EXPANDED = \"aria-expanded\";\n\nclass PopoverManager {\n  constructor() {\n    // --------------------------------------------------------------------------\n    //\n    //  Private Properties\n    //\n    // --------------------------------------------------------------------------\n    this.registeredElements = new Map();\n    this.registeredElementCount = 0;\n    // --------------------------------------------------------------------------\n    //\n    //  Private Methods\n    //\n    // --------------------------------------------------------------------------\n    this.queryPopover = (composedPath) => {\n      const { registeredElements } = this;\n      const registeredElement = composedPath.find((pathEl) => registeredElements.has(pathEl));\n      return registeredElements.get(registeredElement);\n    };\n    this.togglePopovers = (event) => {\n      const composedPath = event.composedPath();\n      const togglePopover = this.queryPopover(composedPath);\n      if (togglePopover && !togglePopover.triggerDisabled) {\n        togglePopover.open = !togglePopover.open;\n      }\n      Array.from(this.registeredElements.values())\n        .filter((popover) => popover !== togglePopover && popover.autoClose && popover.open && !composedPath.includes(popover))\n        .forEach((popover) => (popover.open = false));\n    };\n    this.keyHandler = (event) => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.key === \"Escape\") {\n        this.closeAllPopovers();\n      }\n      else if (isActivationKey(event.key)) {\n        this.togglePopovers(event);\n      }\n    };\n    this.clickHandler = (event) => {\n      if (isPrimaryPointerButton(event)) {\n        this.togglePopovers(event);\n      }\n    };\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Public Methods\n  //\n  // --------------------------------------------------------------------------\n  registerElement(referenceEl, popover) {\n    this.registeredElementCount++;\n    this.registeredElements.set(referenceEl, popover);\n    if (this.registeredElementCount === 1) {\n      this.addListeners();\n    }\n  }\n  unregisterElement(referenceEl) {\n    if (this.registeredElements.delete(referenceEl)) {\n      this.registeredElementCount--;\n    }\n    if (this.registeredElementCount === 0) {\n      this.removeListeners();\n    }\n  }\n  closeAllPopovers() {\n    Array.from(this.registeredElements.values()).forEach((popover) => (popover.open = false));\n  }\n  addListeners() {\n    document.addEventListener(\"pointerdown\", this.clickHandler, { capture: true });\n    document.addEventListener(\"keydown\", this.keyHandler, { capture: true });\n  }\n  removeListeners() {\n    document.removeEventListener(\"pointerdown\", this.clickHandler, { capture: true });\n    document.removeEventListener(\"keydown\", this.keyHandler, { capture: true });\n  }\n}\n\nconst popoverCss = \"@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{--calcite-floating-ui-z-index:var(--calcite-popover-z-index, 900);display:block;position:absolute;z-index:var(--calcite-floating-ui-z-index)}.calcite-floating-ui-anim{position:relative;transition:var(--calcite-floating-ui-transition);transition-property:transform, visibility, opacity;opacity:0;box-shadow:0 0 16px 0 rgba(0, 0, 0, 0.16);z-index:1;border-radius:0.25rem}:host([data-placement^=bottom]) .calcite-floating-ui-anim{transform:translateY(-5px)}:host([data-placement^=top]) .calcite-floating-ui-anim{transform:translateY(5px)}:host([data-placement^=left]) .calcite-floating-ui-anim{transform:translateX(5px)}:host([data-placement^=right]) .calcite-floating-ui-anim{transform:translateX(-5px)}:host([data-placement]) .calcite-floating-ui-anim--active{opacity:1;transform:translate(0)}:host([calcite-hydrated-hidden]){visibility:hidden !important;pointer-events:none}.arrow,.arrow::before{position:absolute;inline-size:8px;block-size:8px;z-index:-1}.arrow::before{content:\\\"\\\";--tw-shadow:0 4px 8px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);--tw-shadow-colored:0 4px 8px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);transform:rotate(45deg);background:var(--calcite-ui-foreground-1)}:host([data-placement^=top]) .arrow{inset-block-end:-4px}:host([data-placement^=bottom]) .arrow{inset-block-start:-4px}:host([data-placement^=right]) .arrow,:host([data-placement^=left]) .arrow{direction:ltr;text-align:start}:host([data-placement^=left]) .arrow{inset-inline-end:-4px}:host([data-placement^=right]) .arrow{inset-inline-start:-4px}:host([scale=s]) .heading{padding-inline:0.75rem;padding-block:0.5rem;font-size:var(--calcite-font-size--1);line-height:1.375}:host([scale=m]) .heading{padding-inline:1rem;padding-block:0.75rem;font-size:var(--calcite-font-size-0);line-height:1.375}:host([scale=l]) .heading{padding-inline:1.25rem;padding-block:1rem;font-size:var(--calcite-font-size-1);line-height:1.375}:host{pointer-events:none}:host([open]){pointer-events:initial}.calcite-floating-ui-anim{border-radius:0.25rem;border-width:1px;border-style:solid;border-color:var(--calcite-ui-border-3);background-color:var(--calcite-ui-foreground-1)}.arrow::before{outline:1px solid var(--calcite-ui-border-3)}.header{display:flex;flex:1 1 auto;align-items:stretch;justify-content:flex-start;border-width:0px;border-block-end-width:1px;border-style:solid;background-color:var(--calcite-ui-foreground-1);border-block-end-color:var(--calcite-ui-border-3)}.heading{margin:0px;display:block;flex:1 1 auto;align-self:center;white-space:normal;font-weight:var(--calcite-font-weight-medium);color:var(--calcite-ui-text-1);word-wrap:break-word;word-break:break-word}.container{position:relative;display:flex;block-size:100%;flex-direction:row;flex-wrap:nowrap;border-radius:0.25rem;background-color:var(--calcite-ui-foreground-1);color:var(--calcite-ui-text-1)}.container.has-header{flex-direction:column}.content{display:flex;block-size:100%;inline-size:100%;flex-direction:column;flex-wrap:nowrap;align-self:center;word-wrap:break-word;word-break:break-word}.close-button-container{display:flex;overflow:hidden;flex:0 0 auto;border-start-end-radius:0.25rem;border-end-end-radius:0.25rem}::slotted(calcite-panel),::slotted(calcite-flow){block-size:100%}\";\n\nconst manager = new PopoverManager();\nconst Popover = /*@__PURE__*/ proxyCustomElement(class extends HTMLElement {\n  constructor() {\n    super();\n    this.__registerHost();\n    this.__attachShadow();\n    this.calcitePopoverBeforeClose = createEvent(this, \"calcitePopoverBeforeClose\", 6);\n    this.calcitePopoverClose = createEvent(this, \"calcitePopoverClose\", 6);\n    this.calcitePopoverBeforeOpen = createEvent(this, \"calcitePopoverBeforeOpen\", 6);\n    this.calcitePopoverOpen = createEvent(this, \"calcitePopoverOpen\", 6);\n    this.guid = `calcite-popover-${guid()}`;\n    this.openTransitionProp = \"opacity\";\n    this.hasLoaded = false;\n    // --------------------------------------------------------------------------\n    //\n    //  Private Methods\n    //\n    // --------------------------------------------------------------------------\n    this.setTransitionEl = (el) => {\n      this.transitionEl = el;\n      connectOpenCloseComponent(this);\n      this.focusTrapEl = el;\n      connectFocusTrap(this);\n    };\n    this.setFilteredPlacements = () => {\n      const { el, flipPlacements } = this;\n      this.filteredFlipPlacements = flipPlacements\n        ? filterComputedPlacements(flipPlacements, el)\n        : null;\n    };\n    this.setUpReferenceElement = (warn = true) => {\n      this.removeReferences();\n      this.effectiveReferenceElement = this.getReferenceElement();\n      connectFloatingUI(this, this.effectiveReferenceElement, this.el);\n      const { el, referenceElement, effectiveReferenceElement } = this;\n      if (warn && referenceElement && !effectiveReferenceElement) {\n        console.warn(`${el.tagName}: reference-element id \"${referenceElement}\" was not found.`, {\n          el\n        });\n      }\n      this.addReferences();\n    };\n    this.getId = () => {\n      return this.el.id || this.guid;\n    };\n    this.setExpandedAttr = () => {\n      const { effectiveReferenceElement, open } = this;\n      if (!effectiveReferenceElement) {\n        return;\n      }\n      if (\"setAttribute\" in effectiveReferenceElement) {\n        effectiveReferenceElement.setAttribute(ARIA_EXPANDED, toAriaBoolean(open));\n      }\n    };\n    this.addReferences = () => {\n      const { effectiveReferenceElement } = this;\n      if (!effectiveReferenceElement) {\n        return;\n      }\n      const id = this.getId();\n      if (\"setAttribute\" in effectiveReferenceElement) {\n        effectiveReferenceElement.setAttribute(ARIA_CONTROLS, id);\n      }\n      manager.registerElement(effectiveReferenceElement, this.el);\n      this.setExpandedAttr();\n    };\n    this.removeReferences = () => {\n      const { effectiveReferenceElement } = this;\n      if (!effectiveReferenceElement) {\n        return;\n      }\n      if (\"removeAttribute\" in effectiveReferenceElement) {\n        effectiveReferenceElement.removeAttribute(ARIA_CONTROLS);\n        effectiveReferenceElement.removeAttribute(ARIA_EXPANDED);\n      }\n      manager.unregisterElement(effectiveReferenceElement);\n    };\n    this.hide = () => {\n      this.open = false;\n    };\n    this.storeArrowEl = (el) => {\n      this.arrowEl = el;\n      this.reposition(true);\n    };\n    this.autoClose = false;\n    this.closable = false;\n    this.flipDisabled = false;\n    this.focusTrapDisabled = false;\n    this.pointerDisabled = false;\n    this.flipPlacements = undefined;\n    this.heading = undefined;\n    this.headingLevel = undefined;\n    this.label = undefined;\n    this.messageOverrides = undefined;\n    this.messages = undefined;\n    this.offsetDistance = defaultOffsetDistance;\n    this.offsetSkidding = 0;\n    this.open = false;\n    this.overlayPositioning = \"absolute\";\n    this.placement = defaultPopoverPlacement;\n    this.referenceElement = undefined;\n    this.scale = \"m\";\n    this.triggerDisabled = false;\n    this.effectiveLocale = \"\";\n    this.effectiveReferenceElement = undefined;\n    this.defaultMessages = undefined;\n  }\n  handlefocusTrapDisabled(focusTrapDisabled) {\n    if (!this.open) {\n      return;\n    }\n    focusTrapDisabled ? deactivateFocusTrap(this) : activateFocusTrap(this);\n  }\n  flipPlacementsHandler() {\n    this.setFilteredPlacements();\n    this.reposition(true);\n  }\n  onMessagesChange() {\n    /* wired up by t9n util */\n  }\n  offsetDistanceOffsetHandler() {\n    this.reposition(true);\n  }\n  offsetSkiddingHandler() {\n    this.reposition(true);\n  }\n  openHandler(value) {\n    if (value) {\n      this.reposition(true);\n    }\n    else {\n      updateAfterClose(this.el);\n    }\n    this.setExpandedAttr();\n  }\n  overlayPositioningHandler() {\n    this.reposition(true);\n  }\n  placementHandler() {\n    this.reposition(true);\n  }\n  referenceElementHandler() {\n    this.setUpReferenceElement();\n    this.reposition(true);\n  }\n  effectiveLocaleChange() {\n    updateMessages(this, this.effectiveLocale);\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Lifecycle\n  //\n  // --------------------------------------------------------------------------\n  connectedCallback() {\n    this.setFilteredPlacements();\n    connectLocalized(this);\n    connectMessages(this);\n    connectOpenCloseComponent(this);\n    this.setUpReferenceElement(this.hasLoaded);\n  }\n  async componentWillLoad() {\n    await setUpMessages(this);\n    setUpLoadableComponent(this);\n  }\n  componentDidLoad() {\n    setComponentLoaded(this);\n    if (this.referenceElement && !this.effectiveReferenceElement) {\n      this.setUpReferenceElement();\n    }\n    this.reposition();\n    this.hasLoaded = true;\n  }\n  disconnectedCallback() {\n    this.removeReferences();\n    disconnectLocalized(this);\n    disconnectMessages(this);\n    disconnectFloatingUI(this, this.effectiveReferenceElement, this.el);\n    disconnectOpenCloseComponent(this);\n    deactivateFocusTrap(this);\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Public Methods\n  //\n  // --------------------------------------------------------------------------\n  /**\n   * Updates the position of the component.\n   *\n   * @param delayed\n   */\n  async reposition(delayed = false) {\n    const { el, effectiveReferenceElement, placement, overlayPositioning, flipDisabled, filteredFlipPlacements, offsetDistance, offsetSkidding, arrowEl } = this;\n    return reposition(this, {\n      floatingEl: el,\n      referenceEl: effectiveReferenceElement,\n      overlayPositioning,\n      placement,\n      flipDisabled,\n      flipPlacements: filteredFlipPlacements,\n      offsetDistance,\n      offsetSkidding,\n      includeArrow: !this.pointerDisabled,\n      arrowEl,\n      type: \"popover\"\n    }, delayed);\n  }\n  /**\n   * Sets focus on the component's first focusable element.\n   */\n  async setFocus() {\n    await componentLoaded(this);\n    forceUpdate(this.el);\n    focusFirstTabbable(this.focusTrapEl);\n  }\n  /**\n   * Updates the element(s) that are used within the focus-trap of the component.\n   */\n  async updateFocusTrapElements() {\n    updateFocusTrapElements(this);\n  }\n  getReferenceElement() {\n    const { referenceElement, el } = this;\n    return ((typeof referenceElement === \"string\"\n      ? queryElementRoots(el, { id: referenceElement })\n      : referenceElement) || null);\n  }\n  onBeforeOpen() {\n    this.calcitePopoverBeforeOpen.emit();\n  }\n  onOpen() {\n    this.calcitePopoverOpen.emit();\n    activateFocusTrap(this);\n  }\n  onBeforeClose() {\n    this.calcitePopoverBeforeClose.emit();\n  }\n  onClose() {\n    this.calcitePopoverClose.emit();\n    deactivateFocusTrap(this);\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Render Methods\n  //\n  // --------------------------------------------------------------------------\n  renderCloseButton() {\n    const { messages, closable } = this;\n    return closable ? (h(\"div\", { class: CSS.closeButtonContainer, key: CSS.closeButtonContainer }, h(\"calcite-action\", { class: CSS.closeButton, onClick: this.hide, ref: (closeButtonEl) => (this.closeButtonEl = closeButtonEl), scale: this.scale, text: messages.close }, h(\"calcite-icon\", { icon: \"x\", scale: this.scale === \"l\" ? \"m\" : this.scale })))) : null;\n  }\n  renderHeader() {\n    const { heading, headingLevel } = this;\n    const headingNode = heading ? (h(Heading, { class: CSS.heading, level: headingLevel }, heading)) : null;\n    return headingNode ? (h(\"div\", { class: CSS.header, key: CSS.header }, headingNode, this.renderCloseButton())) : null;\n  }\n  render() {\n    const { effectiveReferenceElement, heading, label, open, pointerDisabled } = this;\n    const displayed = effectiveReferenceElement && open;\n    const hidden = !displayed;\n    const arrowNode = !pointerDisabled ? h(\"div\", { class: CSS.arrow, ref: this.storeArrowEl }) : null;\n    return (h(Host, { \"aria-hidden\": toAriaBoolean(hidden), \"aria-label\": label, \"aria-live\": \"polite\", \"calcite-hydrated-hidden\": hidden, id: this.getId(), role: \"dialog\" }, h(\"div\", { class: {\n        [FloatingCSS.animation]: true,\n        [FloatingCSS.animationActive]: displayed\n      }, ref: this.setTransitionEl }, arrowNode, h(\"div\", { class: {\n        [CSS.hasHeader]: !!heading,\n        [CSS.container]: true\n      } }, this.renderHeader(), h(\"div\", { class: CSS.content }, h(\"slot\", null)), !heading ? this.renderCloseButton() : null))));\n  }\n  static get assetsDirs() { return [\"assets\"]; }\n  get el() { return this; }\n  static get watchers() { return {\n    \"focusTrapDisabled\": [\"handlefocusTrapDisabled\"],\n    \"flipPlacements\": [\"flipPlacementsHandler\"],\n    \"messageOverrides\": [\"onMessagesChange\"],\n    \"offsetDistance\": [\"offsetDistanceOffsetHandler\"],\n    \"offsetSkidding\": [\"offsetSkiddingHandler\"],\n    \"open\": [\"openHandler\"],\n    \"overlayPositioning\": [\"overlayPositioningHandler\"],\n    \"placement\": [\"placementHandler\"],\n    \"referenceElement\": [\"referenceElementHandler\"],\n    \"effectiveLocale\": [\"effectiveLocaleChange\"]\n  }; }\n  static get style() { return popoverCss; }\n}, [1, \"calcite-popover\", {\n    \"autoClose\": [516, \"auto-close\"],\n    \"closable\": [1540],\n    \"flipDisabled\": [516, \"flip-disabled\"],\n    \"focusTrapDisabled\": [516, \"focus-trap-disabled\"],\n    \"pointerDisabled\": [516, \"pointer-disabled\"],\n    \"flipPlacements\": [16],\n    \"heading\": [1],\n    \"headingLevel\": [514, \"heading-level\"],\n    \"label\": [1],\n    \"messageOverrides\": [1040],\n    \"messages\": [1040],\n    \"offsetDistance\": [514, \"offset-distance\"],\n    \"offsetSkidding\": [514, \"offset-skidding\"],\n    \"open\": [1540],\n    \"overlayPositioning\": [513, \"overlay-positioning\"],\n    \"placement\": [513],\n    \"referenceElement\": [1, \"reference-element\"],\n    \"scale\": [513],\n    \"triggerDisabled\": [516, \"trigger-disabled\"],\n    \"effectiveLocale\": [32],\n    \"effectiveReferenceElement\": [32],\n    \"defaultMessages\": [32],\n    \"reposition\": [64],\n    \"setFocus\": [64],\n    \"updateFocusTrapElements\": [64]\n  }]);\nfunction defineCustomElement() {\n  if (typeof customElements === \"undefined\") {\n    return;\n  }\n  const components = [\"calcite-popover\", \"calcite-action\", \"calcite-icon\", \"calcite-loader\"];\n  components.forEach(tagName => { switch (tagName) {\n    case \"calcite-popover\":\n      if (!customElements.get(tagName)) {\n        customElements.define(tagName, Popover);\n      }\n      break;\n    case \"calcite-action\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$3();\n      }\n      break;\n    case \"calcite-icon\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$2();\n      }\n      break;\n    case \"calcite-loader\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$1();\n      }\n      break;\n  } });\n}\ndefineCustomElement();\n\nexport { Popover as P, defineCustomElement as d };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nfunction getRoundRobinIndex(index, total) {\n  return (index + total) % total;\n}\n\nexport { getRoundRobinIndex as g };\n", "/*!\n * All material copyright ESRI, All Rights Reserved, unless otherwise specified.\n * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.\n * v1.0.8-next.4\n */\nimport { proxyCustomElement, HTMLElement, createEvent, h, Fragment } from '@stencil/core/internal/client/index.js';\nimport { g as getRoundRobinIndex } from './array.js';\nimport { t as toAriaBoolean, i as isPrimaryPointerButton, h as focusElement } from './dom.js';\nimport { g as guid } from './guid.js';\nimport { i as isActivationKey } from './key.js';\nimport { a as setUpLoadableComponent, s as setComponentLoaded, c as componentLoaded } from './loadable.js';\nimport { d as defineCustomElement$4 } from './action.js';\nimport { d as defineCustomElement$3 } from './icon.js';\nimport { d as defineCustomElement$2 } from './loader.js';\nimport { d as defineCustomElement$1 } from './popover.js';\n\nconst CSS = {\n  menu: \"menu\",\n  defaultTrigger: \"default-trigger\"\n};\nconst SLOTS = {\n  tooltip: \"tooltip\",\n  trigger: \"trigger\"\n};\nconst ICONS = {\n  menu: \"ellipsis\"\n};\n\nconst actionMenuCss = \"@keyframes in{0%{opacity:0}100%{opacity:1}}@keyframes in-down{0%{opacity:0;transform:translate3D(0, -5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-up{0%{opacity:0;transform:translate3D(0, 5px, 0)}100%{opacity:1;transform:translate3D(0, 0, 0)}}@keyframes in-scale{0%{opacity:0;transform:scale3D(0.95, 0.95, 1)}100%{opacity:1;transform:scale3D(1, 1, 1)}}:root{--calcite-animation-timing:calc(150ms * var(--calcite-internal-duration-factor));--calcite-internal-duration-factor:var(--calcite-duration-factor, 1);--calcite-internal-animation-timing-fast:calc(100ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-medium:calc(200ms * var(--calcite-internal-duration-factor));--calcite-internal-animation-timing-slow:calc(300ms * var(--calcite-internal-duration-factor))}.calcite-animate{opacity:0;animation-fill-mode:both;animation-duration:var(--calcite-animation-timing)}.calcite-animate__in{animation-name:in}.calcite-animate__in-down{animation-name:in-down}.calcite-animate__in-up{animation-name:in-up}.calcite-animate__in-scale{animation-name:in-scale}@media (prefers-reduced-motion: reduce){:root{--calcite-internal-duration-factor:0}}:root{--calcite-floating-ui-transition:var(--calcite-animation-timing);--calcite-floating-ui-z-index:600}:host([hidden]){display:none}:host{box-sizing:border-box;display:flex;flex-direction:column;background-color:var(--calcite-ui-foreground-1);font-size:var(--calcite-font-size-1);color:var(--calcite-ui-text-2)}.menu ::slotted(calcite-action){margin:0.125rem;display:flex;outline-color:transparent}.menu ::slotted(calcite-action[active]){outline:2px solid var(--calcite-ui-brand);outline-offset:0px}.default-trigger{position:relative;block-size:100%;flex:0 1 auto;align-self:stretch}slot[name=trigger]::slotted(calcite-action),calcite-action::slotted([slot=trigger]){position:relative;block-size:100%;flex:0 1 auto;align-self:stretch}.menu{flex-direction:column;flex-wrap:nowrap;outline:2px solid transparent;outline-offset:2px}\";\n\nconst SUPPORTED_MENU_NAV_KEYS = [\"ArrowUp\", \"ArrowDown\", \"End\", \"Home\"];\nconst ActionMenu = /*@__PURE__*/ proxyCustomElement(class extends HTMLElement {\n  constructor() {\n    super();\n    this.__registerHost();\n    this.__attachShadow();\n    this.calciteActionMenuOpen = createEvent(this, \"calciteActionMenuOpen\", 6);\n    this.actionElements = [];\n    this.guid = `calcite-action-menu-${guid()}`;\n    this.menuId = `${this.guid}-menu`;\n    this.menuButtonId = `${this.guid}-menu-button`;\n    // --------------------------------------------------------------------------\n    //\n    //  Component Methods\n    //\n    // --------------------------------------------------------------------------\n    this.connectMenuButtonEl = () => {\n      const { menuButtonId, menuId, open, label } = this;\n      const menuButtonEl = this.slottedMenuButtonEl || this.defaultMenuButtonEl;\n      if (this.menuButtonEl === menuButtonEl) {\n        return;\n      }\n      this.disconnectMenuButtonEl();\n      this.menuButtonEl = menuButtonEl;\n      this.setTooltipReferenceElement();\n      if (!menuButtonEl) {\n        return;\n      }\n      menuButtonEl.active = open;\n      menuButtonEl.setAttribute(\"aria-controls\", menuId);\n      menuButtonEl.setAttribute(\"aria-expanded\", toAriaBoolean(open));\n      menuButtonEl.setAttribute(\"aria-haspopup\", \"true\");\n      if (!menuButtonEl.id) {\n        menuButtonEl.id = menuButtonId;\n      }\n      if (!menuButtonEl.label) {\n        menuButtonEl.label = label;\n      }\n      if (!menuButtonEl.text) {\n        menuButtonEl.text = label;\n      }\n      menuButtonEl.addEventListener(\"pointerdown\", this.menuButtonClick);\n      menuButtonEl.addEventListener(\"keydown\", this.menuButtonKeyDown);\n    };\n    this.disconnectMenuButtonEl = () => {\n      const { menuButtonEl } = this;\n      if (!menuButtonEl) {\n        return;\n      }\n      menuButtonEl.removeEventListener(\"pointerdown\", this.menuButtonClick);\n      menuButtonEl.removeEventListener(\"keydown\", this.menuButtonKeyDown);\n    };\n    this.setMenuButtonEl = (event) => {\n      const actions = event.target\n        .assignedElements({\n        flatten: true\n      })\n        .filter((el) => el?.matches(\"calcite-action\"));\n      this.slottedMenuButtonEl = actions[0];\n      this.connectMenuButtonEl();\n    };\n    this.setDefaultMenuButtonEl = (el) => {\n      this.defaultMenuButtonEl = el;\n      this.connectMenuButtonEl();\n    };\n    // --------------------------------------------------------------------------\n    //\n    //  Private Methods\n    //\n    // --------------------------------------------------------------------------\n    this.handleCalciteActionClick = () => {\n      this.open = false;\n      this.setFocus();\n    };\n    this.menuButtonClick = (event) => {\n      if (!isPrimaryPointerButton(event)) {\n        return;\n      }\n      this.toggleOpen();\n    };\n    this.updateTooltip = (event) => {\n      const tooltips = event.target\n        .assignedElements({\n        flatten: true\n      })\n        .filter((el) => el?.matches(\"calcite-tooltip\"));\n      this.tooltipEl = tooltips[0];\n      this.setTooltipReferenceElement();\n    };\n    this.setTooltipReferenceElement = () => {\n      const { tooltipEl, expanded, menuButtonEl, open } = this;\n      if (tooltipEl) {\n        tooltipEl.referenceElement = !expanded && !open ? menuButtonEl : null;\n      }\n    };\n    this.updateAction = (action, index) => {\n      const { guid, activeMenuItemIndex } = this;\n      const id = `${guid}-action-${index}`;\n      action.tabIndex = -1;\n      action.setAttribute(\"role\", \"menuitem\");\n      if (!action.id) {\n        action.id = id;\n      }\n      action.active = index === activeMenuItemIndex;\n    };\n    this.updateActions = (actions) => {\n      actions?.forEach(this.updateAction);\n    };\n    this.handleDefaultSlotChange = (event) => {\n      const actions = event.target\n        .assignedElements({\n        flatten: true\n      })\n        .filter((el) => el?.matches(\"calcite-action\"));\n      this.actionElements = actions;\n    };\n    this.menuButtonKeyDown = (event) => {\n      const { key } = event;\n      const { actionElements, activeMenuItemIndex, open } = this;\n      if (!actionElements.length) {\n        return;\n      }\n      if (isActivationKey(key)) {\n        event.preventDefault();\n        if (!open) {\n          this.toggleOpen();\n          return;\n        }\n        const action = actionElements[activeMenuItemIndex];\n        action ? action.click() : this.toggleOpen(false);\n      }\n      if (key === \"Tab\") {\n        this.open = false;\n        return;\n      }\n      if (key === \"Escape\") {\n        this.toggleOpen(false);\n        event.preventDefault();\n        return;\n      }\n      this.handleActionNavigation(event, key, actionElements);\n    };\n    this.handleActionNavigation = (event, key, actions) => {\n      if (!this.isValidKey(key, SUPPORTED_MENU_NAV_KEYS)) {\n        return;\n      }\n      event.preventDefault();\n      if (!this.open) {\n        this.toggleOpen();\n        if (key === \"Home\" || key === \"ArrowDown\") {\n          this.activeMenuItemIndex = 0;\n        }\n        if (key === \"End\" || key === \"ArrowUp\") {\n          this.activeMenuItemIndex = actions.length - 1;\n        }\n        return;\n      }\n      if (key === \"Home\") {\n        this.activeMenuItemIndex = 0;\n      }\n      if (key === \"End\") {\n        this.activeMenuItemIndex = actions.length - 1;\n      }\n      const currentIndex = this.activeMenuItemIndex;\n      if (key === \"ArrowUp\") {\n        this.activeMenuItemIndex = getRoundRobinIndex(Math.max(currentIndex - 1, -1), actions.length);\n      }\n      if (key === \"ArrowDown\") {\n        this.activeMenuItemIndex = getRoundRobinIndex(currentIndex + 1, actions.length);\n      }\n    };\n    this.toggleOpenEnd = () => {\n      this.setFocus();\n      this.el.removeEventListener(\"calcitePopoverOpen\", this.toggleOpenEnd);\n    };\n    this.toggleOpen = (value = !this.open) => {\n      this.el.addEventListener(\"calcitePopoverOpen\", this.toggleOpenEnd);\n      this.open = value;\n    };\n    this.expanded = false;\n    this.flipPlacements = undefined;\n    this.label = undefined;\n    this.open = false;\n    this.overlayPositioning = \"absolute\";\n    this.placement = \"auto\";\n    this.scale = undefined;\n    this.menuButtonEl = undefined;\n    this.activeMenuItemIndex = -1;\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Lifecycle\n  //\n  // --------------------------------------------------------------------------\n  componentWillLoad() {\n    setUpLoadableComponent(this);\n  }\n  componentDidLoad() {\n    setComponentLoaded(this);\n  }\n  disconnectedCallback() {\n    this.disconnectMenuButtonEl();\n  }\n  expandedHandler() {\n    this.open = false;\n    this.setTooltipReferenceElement();\n  }\n  openHandler(open) {\n    this.activeMenuItemIndex = this.open ? 0 : -1;\n    if (this.menuButtonEl) {\n      this.menuButtonEl.active = open;\n    }\n    this.calciteActionMenuOpen.emit();\n    this.setTooltipReferenceElement();\n  }\n  closeCalciteActionMenuOnClick(event) {\n    if (!isPrimaryPointerButton(event)) {\n      return;\n    }\n    const composedPath = event.composedPath();\n    if (composedPath.includes(this.el)) {\n      return;\n    }\n    this.open = false;\n  }\n  activeMenuItemIndexHandler() {\n    this.updateActions(this.actionElements);\n  }\n  // --------------------------------------------------------------------------\n  //\n  //  Methods\n  //\n  // --------------------------------------------------------------------------\n  /** Sets focus on the component. */\n  async setFocus() {\n    await componentLoaded(this);\n    focusElement(this.menuButtonEl);\n  }\n  renderMenuButton() {\n    const { label, scale, expanded } = this;\n    const menuButtonSlot = (h(\"slot\", { name: SLOTS.trigger, onSlotchange: this.setMenuButtonEl }, h(\"calcite-action\", { class: CSS.defaultTrigger, icon: ICONS.menu, ref: this.setDefaultMenuButtonEl, scale: scale, text: label, textEnabled: expanded })));\n    return menuButtonSlot;\n  }\n  renderMenuItems() {\n    const { actionElements, activeMenuItemIndex, open, menuId, menuButtonEl, label, placement, overlayPositioning, flipPlacements } = this;\n    const activeAction = actionElements[activeMenuItemIndex];\n    const activeDescendantId = activeAction?.id || null;\n    return (h(\"calcite-popover\", { flipPlacements: flipPlacements, focusTrapDisabled: true, label: label, offsetDistance: 0, open: open, overlayPositioning: overlayPositioning, placement: placement, pointerDisabled: true, referenceElement: menuButtonEl }, h(\"div\", { \"aria-activedescendant\": activeDescendantId, \"aria-labelledby\": menuButtonEl?.id, class: CSS.menu, id: menuId, onClick: this.handleCalciteActionClick, role: \"menu\", tabIndex: -1 }, h(\"slot\", { onSlotchange: this.handleDefaultSlotChange }))));\n  }\n  render() {\n    return (h(Fragment, null, this.renderMenuButton(), this.renderMenuItems(), h(\"slot\", { name: SLOTS.tooltip, onSlotchange: this.updateTooltip })));\n  }\n  isValidKey(key, supportedKeys) {\n    return !!supportedKeys.find((k) => k === key);\n  }\n  get el() { return this; }\n  static get watchers() { return {\n    \"expanded\": [\"expandedHandler\"],\n    \"open\": [\"openHandler\"],\n    \"activeMenuItemIndex\": [\"activeMenuItemIndexHandler\"]\n  }; }\n  static get style() { return actionMenuCss; }\n}, [1, \"calcite-action-menu\", {\n    \"expanded\": [516],\n    \"flipPlacements\": [16],\n    \"label\": [1],\n    \"open\": [1540],\n    \"overlayPositioning\": [513, \"overlay-positioning\"],\n    \"placement\": [513],\n    \"scale\": [513],\n    \"menuButtonEl\": [32],\n    \"activeMenuItemIndex\": [32],\n    \"setFocus\": [64]\n  }, [[9, \"pointerdown\", \"closeCalciteActionMenuOnClick\"]]]);\nfunction defineCustomElement() {\n  if (typeof customElements === \"undefined\") {\n    return;\n  }\n  const components = [\"calcite-action-menu\", \"calcite-action\", \"calcite-icon\", \"calcite-loader\", \"calcite-popover\"];\n  components.forEach(tagName => { switch (tagName) {\n    case \"calcite-action-menu\":\n      if (!customElements.get(tagName)) {\n        customElements.define(tagName, ActionMenu);\n      }\n      break;\n    case \"calcite-action\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$4();\n      }\n      break;\n    case \"calcite-icon\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$3();\n      }\n      break;\n    case \"calcite-loader\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$2();\n      }\n      break;\n    case \"calcite-popover\":\n      if (!customElements.get(tagName)) {\n        defineCustomElement$1();\n      }\n      break;\n  } });\n}\ndefineCustomElement();\n\nexport { ActionMenu as A, SLOTS as S, defineCustomElement as d };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,UAAU,CAAC,OAAO,aAAa;AACnC,QAAM,aAAa,MAAM,QAAQ,IAAI,MAAM,KAAK,KAAK;AACrD,SAAO,MAAM;AACb,SAAO,EAAE,YAAY,EAAE,GAAG,MAAM,GAAG,QAAQ;AAC7C;;;ACFA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AACjD,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AACzD,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IACtD,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AACpD,QAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AACzD,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAC1C,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjK,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IACjF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AACxD,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAW;AACtB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;AAEA,IAAI,mBAAmB;AAAA,EACrB,cAAc,SAAS,aAAaA,YAAW,MAAM;AACnD,QAAIA,WAAU,SAAS,GAAG;AACxB,UAAI,aAAaA,WAAUA,WAAU,SAAS,CAAC;AAC/C,UAAI,eAAe,MAAM;AACvB,mBAAW,MAAM;AAAA,MACnB;AAAA,IACF;AACA,QAAI,YAAYA,WAAU,QAAQ,IAAI;AACtC,QAAI,cAAc,IAAI;AACpB,MAAAA,WAAU,KAAK,IAAI;AAAA,IACrB,OAAO;AAEL,MAAAA,WAAU,OAAO,WAAW,CAAC;AAC7B,MAAAA,WAAU,KAAK,IAAI;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB,SAAS,eAAeA,YAAW,MAAM;AACvD,QAAI,YAAYA,WAAU,QAAQ,IAAI;AACtC,QAAI,cAAc,IAAI;AACpB,MAAAA,WAAU,OAAO,WAAW,CAAC;AAAA,IAC/B;AACA,QAAIA,WAAU,SAAS,GAAG;AACxB,MAAAA,WAAUA,WAAU,SAAS,CAAC,EAAE,QAAQ;AAAA,IAC1C;AAAA,EACF;AACF;AACA,IAAI,oBAAoB,SAASC,mBAAkB,MAAM;AACvD,SAAO,KAAK,WAAW,KAAK,QAAQ,YAAY,MAAM,WAAW,OAAO,KAAK,WAAW;AAC1F;AACA,IAAI,gBAAgB,SAASC,eAAc,GAAG;AAC5C,SAAO,EAAE,QAAQ,YAAY,EAAE,QAAQ,SAAS,EAAE,YAAY;AAChE;AACA,IAAI,aAAa,SAASC,YAAW,GAAG;AACtC,SAAO,EAAE,QAAQ,SAAS,EAAE,YAAY;AAC1C;AAGA,IAAI,eAAe,SAASC,cAAa,GAAG;AAC1C,SAAO,WAAW,CAAC,KAAK,CAAC,EAAE;AAC7B;AAGA,IAAI,gBAAgB,SAASC,eAAc,GAAG;AAC5C,SAAO,WAAW,CAAC,KAAK,EAAE;AAC5B;AACA,IAAI,QAAQ,SAASC,OAAM,IAAI;AAC7B,SAAO,WAAW,IAAI,CAAC;AACzB;AAIA,IAAI,YAAY,SAASC,WAAU,KAAK,IAAI;AAC1C,MAAI,MAAM;AACV,MAAI,MAAM,SAAU,OAAO,GAAG;AAC5B,QAAI,GAAG,KAAK,GAAG;AACb,YAAM;AACN,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,CAAC;AAED,SAAO;AACT;AASA,IAAI,iBAAiB,SAASC,gBAAe,OAAO;AAClD,WAAS,OAAO,UAAU,QAAQ,SAAS,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC5G,WAAO,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACnC;AACA,SAAO,OAAO,UAAU,aAAa,MAAM,MAAM,QAAQ,MAAM,IAAI;AACrE;AACA,IAAI,kBAAkB,SAASC,iBAAgB,OAAO;AAQpD,SAAO,MAAM,OAAO,cAAc,OAAO,MAAM,iBAAiB,aAAa,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC/G;AAIA,IAAI,oBAAoB,CAAC;AACzB,IAAI,kBAAkB,SAASC,iBAAgB,UAAU,aAAa;AAGpE,MAAI,OAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa;AAC9F,MAAIV,cAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,cAAc;AACrG,MAAI,SAAS,eAAe;AAAA,IAC1B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,EACF,GAAG,WAAW;AACd,MAAI,QAAQ;AAAA;AAAA;AAAA,IAGV,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcb,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOlB,gBAAgB,CAAC;AAAA,IACjB,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,QAAQ;AAAA,IACR,QAAQ;AAAA;AAAA;AAAA,IAGR,wBAAwB;AAAA,EAC1B;AACA,MAAI;AAUJ,MAAI,YAAY,SAASW,WAAU,uBAAuB,YAAY,kBAAkB;AACtF,WAAO,yBAAyB,sBAAsB,UAAU,MAAM,SAAY,sBAAsB,UAAU,IAAI,OAAO,oBAAoB,UAAU;AAAA,EAC7J;AASA,MAAI,qBAAqB,SAASC,oBAAmB,SAAS;AAI5D,WAAO,MAAM,gBAAgB,UAAU,SAAU,MAAM;AACrD,UAAI,YAAY,KAAK,WACnB,gBAAgB,KAAK;AACvB,aAAO,UAAU,SAAS,OAAO;AAAA;AAAA;AAAA;AAAA,MAKjC,cAAc,KAAK,SAAU,MAAM;AACjC,eAAO,SAAS;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAeA,MAAI,mBAAmB,SAASC,kBAAiB,YAAY;AAC3D,QAAI,cAAc,OAAO,UAAU;AACnC,QAAI,OAAO,gBAAgB,YAAY;AACrC,eAAS,QAAQ,UAAU,QAAQ,SAAS,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACnH,eAAO,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,MACrC;AACA,oBAAc,YAAY,MAAM,QAAQ,MAAM;AAAA,IAChD;AACA,QAAI,gBAAgB,MAAM;AACxB,oBAAc;AAAA,IAChB;AAEA,QAAI,CAAC,aAAa;AAChB,UAAI,gBAAgB,UAAa,gBAAgB,OAAO;AACtD,eAAO;AAAA,MACT;AAGA,YAAM,IAAI,MAAM,IAAI,OAAO,YAAY,8DAA8D,CAAC;AAAA,IACxG;AACA,QAAI,OAAO;AAEX,QAAI,OAAO,gBAAgB,UAAU;AACnC,aAAO,IAAI,cAAc,WAAW;AACpC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,IAAI,OAAO,YAAY,uCAAuC,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,sBAAsB,SAASC,uBAAsB;AACvD,QAAI,OAAO,iBAAiB,cAAc;AAG1C,QAAI,SAAS,OAAO;AAClB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,QAAW;AAEtB,UAAI,mBAAmB,IAAI,aAAa,KAAK,GAAG;AAC9C,eAAO,IAAI;AAAA,MACb,OAAO;AACL,YAAI,qBAAqB,MAAM,eAAe,CAAC;AAC/C,YAAI,oBAAoB,sBAAsB,mBAAmB;AAGjE,eAAO,qBAAqB,iBAAiB,eAAe;AAAA,MAC9D;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,YAAM,IAAI,MAAM,8DAA8D;AAAA,IAChF;AACA,WAAO;AAAA,EACT;AACA,MAAI,sBAAsB,SAASC,uBAAsB;AACvD,UAAM,kBAAkB,MAAM,WAAW,IAAI,SAAU,WAAW;AAChE,UAAI,gBAAgB,SAAS,WAAW,OAAO,eAAe;AAI9D,UAAI,iBAAiB,UAAU,WAAW,OAAO,eAAe;AAChE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,mBAAmB,cAAc,SAAS,IAAI,cAAc,CAAC,IAAI;AAAA,QACjE,kBAAkB,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,CAAC,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASvF,kBAAkB,SAAS,iBAAiB,MAAM;AAChD,cAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAWlF,cAAI,UAAU,eAAe,UAAU,SAAU,GAAG;AAClD,mBAAO,MAAM;AAAA,UACf,CAAC;AACD,cAAI,UAAU,GAAG;AACf,mBAAO;AAAA,UACT;AACA,cAAI,SAAS;AACX,mBAAO,eAAe,MAAM,UAAU,CAAC,EAAE,KAAK,SAAU,GAAG;AACzD,qBAAO,WAAW,GAAG,OAAO,eAAe;AAAA,YAC7C,CAAC;AAAA,UACH;AACA,iBAAO,eAAe,MAAM,GAAG,OAAO,EAAE,QAAQ,EAAE,KAAK,SAAU,GAAG;AAClE,mBAAO,WAAW,GAAG,OAAO,eAAe;AAAA,UAC7C,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,iBAAiB,MAAM,gBAAgB,OAAO,SAAU,OAAO;AACnE,aAAO,MAAM,cAAc,SAAS;AAAA,IACtC,CAAC;AAGD,QAAI,MAAM,eAAe,UAAU,KAAK,CAAC,iBAAiB,eAAe,GACvE;AACA,YAAM,IAAI,MAAM,qGAAqG;AAAA,IACvH;AAAA,EACF;AACA,MAAI,WAAW,SAASC,UAAS,MAAM;AACrC,QAAI,SAAS,OAAO;AAClB;AAAA,IACF;AACA,QAAI,SAAS,IAAI,eAAe;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AACxB,MAAAA,UAAS,oBAAoB,CAAC;AAC9B;AAAA,IACF;AACA,SAAK,MAAM;AAAA,MACT,eAAe,CAAC,CAAC,OAAO;AAAA,IAC1B,CAAC;AACD,UAAM,0BAA0B;AAChC,QAAI,kBAAkB,IAAI,GAAG;AAC3B,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AACA,MAAI,qBAAqB,SAASC,oBAAmB,uBAAuB;AAC1E,QAAI,OAAO,iBAAiB,kBAAkB,qBAAqB;AACnE,WAAO,OAAO,OAAO,SAAS,QAAQ,QAAQ;AAAA,EAChD;AAIA,MAAI,mBAAmB,SAASC,kBAAiB,GAAG;AAClD,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,mBAAmB,MAAM,KAAK,GAAG;AAEnC;AAAA,IACF;AACA,QAAI,eAAe,OAAO,yBAAyB,CAAC,GAAG;AAErD,WAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYd,aAAa,OAAO,2BAA2B,CAAC,YAAY,QAAQ,OAAO,eAAe;AAAA,MAC5F,CAAC;AACD;AAAA,IACF;AAKA,QAAI,eAAe,OAAO,mBAAmB,CAAC,GAAG;AAE/C;AAAA,IACF;AAGA,MAAE,eAAe;AAAA,EACnB;AAGA,MAAI,eAAe,SAASC,cAAa,GAAG;AAC1C,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,kBAAkB,mBAAmB,MAAM,KAAK;AAGpD,QAAI,mBAAmB,kBAAkB,UAAU;AACjD,UAAI,iBAAiB;AACnB,cAAM,0BAA0B;AAAA,MAClC;AAAA,IACF,OAAO;AAEL,QAAE,yBAAyB;AAC3B,eAAS,MAAM,2BAA2B,oBAAoB,CAAC;AAAA,IACjE;AAAA,EACF;AAMA,MAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,QAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACrF,QAAI,SAAS,gBAAgB,KAAK;AAClC,wBAAoB;AACpB,QAAI,kBAAkB;AACtB,QAAI,MAAM,eAAe,SAAS,GAAG;AAInC,UAAI,iBAAiB,mBAAmB,MAAM;AAC9C,UAAI,iBAAiB,kBAAkB,IAAI,MAAM,gBAAgB,cAAc,IAAI;AACnF,UAAI,iBAAiB,GAAG;AAGtB,YAAI,YAAY;AAEd,4BAAkB,MAAM,eAAe,MAAM,eAAe,SAAS,CAAC,EAAE;AAAA,QAC1E,OAAO;AAEL,4BAAkB,MAAM,eAAe,CAAC,EAAE;AAAA,QAC5C;AAAA,MACF,WAAW,YAAY;AAIrB,YAAI,oBAAoB,UAAU,MAAM,gBAAgB,SAAU,OAAO;AACvE,cAAI,oBAAoB,MAAM;AAC9B,iBAAO,WAAW;AAAA,QACpB,CAAC;AACD,YAAI,oBAAoB,MAAM,eAAe,cAAc,UAAU,YAAY,QAAQ,OAAO,eAAe,KAAK,CAAC,WAAW,QAAQ,OAAO,eAAe,KAAK,CAAC,eAAe,iBAAiB,QAAQ,KAAK,IAAI;AAOnN,8BAAoB;AAAA,QACtB;AACA,YAAI,qBAAqB,GAAG;AAI1B,cAAI,wBAAwB,sBAAsB,IAAI,MAAM,eAAe,SAAS,IAAI,oBAAoB;AAC5G,cAAI,mBAAmB,MAAM,eAAe,qBAAqB;AACjE,4BAAkB,iBAAiB;AAAA,QACrC,WAAW,CAAC,WAAW,KAAK,GAAG;AAG7B,4BAAkB,eAAe,iBAAiB,QAAQ,KAAK;AAAA,QACjE;AAAA,MACF,OAAO;AAIL,YAAI,mBAAmB,UAAU,MAAM,gBAAgB,SAAU,OAAO;AACtE,cAAI,mBAAmB,MAAM;AAC7B,iBAAO,WAAW;AAAA,QACpB,CAAC;AACD,YAAI,mBAAmB,MAAM,eAAe,cAAc,UAAU,YAAY,QAAQ,OAAO,eAAe,KAAK,CAAC,WAAW,QAAQ,OAAO,eAAe,KAAK,CAAC,eAAe,iBAAiB,MAAM,IAAI;AAO3M,6BAAmB;AAAA,QACrB;AACA,YAAI,oBAAoB,GAAG;AAIzB,cAAI,yBAAyB,qBAAqB,MAAM,eAAe,SAAS,IAAI,IAAI,mBAAmB;AAC3G,cAAI,oBAAoB,MAAM,eAAe,sBAAsB;AACnE,4BAAkB,kBAAkB;AAAA,QACtC,WAAW,CAAC,WAAW,KAAK,GAAG;AAG7B,4BAAkB,eAAe,iBAAiB,MAAM;AAAA,QAC1D;AAAA,MACF;AAAA,IACF,OAAO;AAGL,wBAAkB,iBAAiB,eAAe;AAAA,IACpD;AACA,QAAI,iBAAiB;AACnB,UAAI,WAAW,KAAK,GAAG;AAKrB,cAAM,eAAe;AAAA,MACvB;AACA,eAAS,eAAe;AAAA,IAC1B;AAAA,EAEF;AAEA,MAAI,WAAW,SAASC,UAAS,OAAO;AACtC,QAAI,cAAc,KAAK,KAAK,eAAe,OAAO,mBAAmB,KAAK,MAAM,OAAO;AACrF,YAAM,eAAe;AACrB,WAAK,WAAW;AAChB;AAAA,IACF;AACA,QAAI,OAAO,aAAa,KAAK,KAAK,OAAO,cAAc,KAAK,GAAG;AAC7D,kBAAY,OAAO,OAAO,cAAc,KAAK,CAAC;AAAA,IAChD;AAAA,EACF;AACA,MAAI,aAAa,SAASC,YAAW,GAAG;AACtC,QAAI,SAAS,gBAAgB,CAAC;AAC9B,QAAI,mBAAmB,MAAM,KAAK,GAAG;AACnC;AAAA,IACF;AACA,QAAI,eAAe,OAAO,yBAAyB,CAAC,GAAG;AACrD;AAAA,IACF;AACA,QAAI,eAAe,OAAO,mBAAmB,CAAC,GAAG;AAC/C;AAAA,IACF;AACA,MAAE,eAAe;AACjB,MAAE,yBAAyB;AAAA,EAC7B;AAMA,MAAI,eAAe,SAASC,gBAAe;AACzC,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AAGA,qBAAiB,aAAavB,YAAW,IAAI;AAI7C,UAAM,yBAAyB,OAAO,oBAAoB,MAAM,WAAY;AAC1E,eAAS,oBAAoB,CAAC;AAAA,IAChC,CAAC,IAAI,SAAS,oBAAoB,CAAC;AACnC,QAAI,iBAAiB,WAAW,cAAc,IAAI;AAClD,QAAI,iBAAiB,aAAa,kBAAkB;AAAA,MAClD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,cAAc,kBAAkB;AAAA,MACnD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,SAAS,YAAY;AAAA,MACxC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,QAAI,iBAAiB,WAAW,UAAU;AAAA,MACxC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC;AACD,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,SAASwB,mBAAkB;AAC/C,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AACA,QAAI,oBAAoB,WAAW,cAAc,IAAI;AACrD,QAAI,oBAAoB,aAAa,kBAAkB,IAAI;AAC3D,QAAI,oBAAoB,cAAc,kBAAkB,IAAI;AAC5D,QAAI,oBAAoB,SAAS,YAAY,IAAI;AACjD,QAAI,oBAAoB,WAAW,UAAU,IAAI;AACjD,WAAO;AAAA,EACT;AAMA,SAAO;AAAA,IACL,IAAI,SAAS;AACX,aAAO,MAAM;AAAA,IACf;AAAA,IACA,IAAI,SAAS;AACX,aAAO,MAAM;AAAA,IACf;AAAA,IACA,UAAU,SAAS,SAAS,iBAAiB;AAC3C,UAAI,MAAM,QAAQ;AAChB,eAAO;AAAA,MACT;AACA,UAAI,aAAa,UAAU,iBAAiB,YAAY;AACxD,UAAI,iBAAiB,UAAU,iBAAiB,gBAAgB;AAChE,UAAI,oBAAoB,UAAU,iBAAiB,mBAAmB;AACtE,UAAI,CAAC,mBAAmB;AACtB,4BAAoB;AAAA,MACtB;AACA,YAAM,SAAS;AACf,YAAM,SAAS;AACf,YAAM,8BAA8B,IAAI;AACxC,UAAI,YAAY;AACd,mBAAW;AAAA,MACb;AACA,UAAI,mBAAmB,SAASC,oBAAmB;AACjD,YAAI,mBAAmB;AACrB,8BAAoB;AAAA,QACtB;AACA,qBAAa;AACb,YAAI,gBAAgB;AAClB,yBAAe;AAAA,QACjB;AAAA,MACF;AACA,UAAI,mBAAmB;AACrB,0BAAkB,MAAM,WAAW,OAAO,CAAC,EAAE,KAAK,kBAAkB,gBAAgB;AACpF,eAAO;AAAA,MACT;AACA,uBAAiB;AACjB,aAAO;AAAA,IACT;AAAA,IACA,YAAY,SAAS,WAAW,mBAAmB;AACjD,UAAI,CAAC,MAAM,QAAQ;AACjB,eAAO;AAAA,MACT;AACA,UAAI,UAAU,eAAe;AAAA,QAC3B,cAAc,OAAO;AAAA,QACrB,kBAAkB,OAAO;AAAA,QACzB,qBAAqB,OAAO;AAAA,MAC9B,GAAG,iBAAiB;AACpB,mBAAa,MAAM,sBAAsB;AACzC,YAAM,yBAAyB;AAC/B,sBAAgB;AAChB,YAAM,SAAS;AACf,YAAM,SAAS;AACf,uBAAiB,eAAezB,YAAW,IAAI;AAC/C,UAAI,eAAe,UAAU,SAAS,cAAc;AACpD,UAAI,mBAAmB,UAAU,SAAS,kBAAkB;AAC5D,UAAI,sBAAsB,UAAU,SAAS,qBAAqB;AAClE,UAAI,cAAc,UAAU,SAAS,eAAe,yBAAyB;AAC7E,UAAI,cAAc;AAChB,qBAAa;AAAA,MACf;AACA,UAAI,qBAAqB,SAAS0B,sBAAqB;AACrD,cAAM,WAAY;AAChB,cAAI,aAAa;AACf,qBAAS,mBAAmB,MAAM,2BAA2B,CAAC;AAAA,UAChE;AACA,cAAI,kBAAkB;AACpB,6BAAiB;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,eAAe,qBAAqB;AACtC,4BAAoB,mBAAmB,MAAM,2BAA2B,CAAC,EAAE,KAAK,oBAAoB,kBAAkB;AACtH,eAAO;AAAA,MACT;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,UAAI,MAAM,UAAU,CAAC,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AACA,YAAM,SAAS;AACf,sBAAgB;AAChB,aAAO;AAAA,IACT;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,UAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ;AAClC,eAAO;AAAA,MACT;AACA,YAAM,SAAS;AACf,0BAAoB;AACpB,mBAAa;AACb,aAAO;AAAA,IACT;AAAA,IACA,yBAAyB,SAAS,wBAAwB,mBAAmB;AAC3E,UAAI,kBAAkB,CAAC,EAAE,OAAO,iBAAiB,EAAE,OAAO,OAAO;AACjE,YAAM,aAAa,gBAAgB,IAAI,SAAU,SAAS;AACxD,eAAO,OAAO,YAAY,WAAW,IAAI,cAAc,OAAO,IAAI;AAAA,MACpE,CAAC;AACD,UAAI,MAAM,QAAQ;AAChB,4BAAoB;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAGA,OAAK,wBAAwB,QAAQ;AACrC,SAAO;AACT;AAEA,IAAM,YAAY,CAAC;AAMnB,SAAS,iBAAiB,WAAW;AACnC,QAAM,EAAE,YAAY,IAAI;AACxB,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AACA,MAAI,YAAY,YAAY,MAAM;AAChC,gBAAY,WAAW;AAAA,EACzB;AACA,QAAM,mBAAmB;AAAA,IACvB,yBAAyB;AAAA,IACzB,UAAU,YAAY;AAAA,IACtB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,gBAAgB,CAAC,OAAO;AACtB,mBAAa,EAAE;AACf,aAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,YAAU,YAAY,gBAAgB,aAAa,gBAAgB;AACrE;AAMA,SAAS,kBAAkB,WAAW;AAlwBtC;AAmwBE,MAAI,CAAC,UAAU,mBAAmB;AAChC,oBAAU,cAAV,mBAAqB;AAAA,EACvB;AACF;AAMA,SAAS,oBAAoB,WAAW;AA5wBxC;AA6wBE,kBAAU,cAAV,mBAAqB;AACvB;AAaA,SAAS,wBAAwB,WAAW;AA3xB5C;AA4xBE,kBAAU,cAAV,mBAAqB,wBAAwB,UAAU;AACzD;;;ACzwBA,IAAM,MAAM;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,SAAS;AACX;AACA,IAAM,0BAA0B;AAChC,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAEtB,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AAMZ,SAAK,qBAAqB,oBAAI,IAAI;AAClC,SAAK,yBAAyB;AAM9B,SAAK,eAAe,CAAC,iBAAiB;AACpC,YAAM,EAAE,mBAAmB,IAAI;AAC/B,YAAM,oBAAoB,aAAa,KAAK,CAAC,WAAW,mBAAmB,IAAI,MAAM,CAAC;AACtF,aAAO,mBAAmB,IAAI,iBAAiB;AAAA,IACjD;AACA,SAAK,iBAAiB,CAAC,UAAU;AAC/B,YAAM,eAAe,MAAM,aAAa;AACxC,YAAM,gBAAgB,KAAK,aAAa,YAAY;AACpD,UAAI,iBAAiB,CAAC,cAAc,iBAAiB;AACnD,sBAAc,OAAO,CAAC,cAAc;AAAA,MACtC;AACA,YAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,EACxC,OAAO,CAAC,YAAY,YAAY,iBAAiB,QAAQ,aAAa,QAAQ,QAAQ,CAAC,aAAa,SAAS,OAAO,CAAC,EACrH,QAAQ,CAAC,YAAa,QAAQ,OAAO,KAAM;AAAA,IAChD;AACA,SAAK,aAAa,CAAC,UAAU;AAC3B,UAAI,MAAM,kBAAkB;AAC1B;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,UAAU;AAC1B,aAAK,iBAAiB;AAAA,MACxB,WACS,gBAAgB,MAAM,GAAG,GAAG;AACnC,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,eAAe,CAAC,UAAU;AAC7B,UAAI,uBAAuB,KAAK,GAAG;AACjC,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,aAAa,SAAS;AACpC,SAAK;AACL,SAAK,mBAAmB,IAAI,aAAa,OAAO;AAChD,QAAI,KAAK,2BAA2B,GAAG;AACrC,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa;AAC7B,QAAI,KAAK,mBAAmB,OAAO,WAAW,GAAG;AAC/C,WAAK;AAAA,IACP;AACA,QAAI,KAAK,2BAA2B,GAAG;AACrC,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,UAAM,KAAK,KAAK,mBAAmB,OAAO,CAAC,EAAE,QAAQ,CAAC,YAAa,QAAQ,OAAO,KAAM;AAAA,EAC1F;AAAA,EACA,eAAe;AACb,aAAS,iBAAiB,eAAe,KAAK,cAAc,EAAE,SAAS,KAAK,CAAC;AAC7E,aAAS,iBAAiB,WAAW,KAAK,YAAY,EAAE,SAAS,KAAK,CAAC;AAAA,EACzE;AAAA,EACA,kBAAkB;AAChB,aAAS,oBAAoB,eAAe,KAAK,cAAc,EAAE,SAAS,KAAK,CAAC;AAChF,aAAS,oBAAoB,WAAW,KAAK,YAAY,EAAE,SAAS,KAAK,CAAC;AAAA,EAC5E;AACF;AAEA,IAAM,aAAa;AAEnB,IAAM,UAAU,IAAI,eAAe;AACnC,IAAM,UAAwB,mBAAmB,cAAc,EAAY;AAAA,EACzE,cAAc;AACZ,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,4BAA4B,YAAY,MAAM,6BAA6B,CAAC;AACjF,SAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AACrE,SAAK,2BAA2B,YAAY,MAAM,4BAA4B,CAAC;AAC/E,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,SAAK,OAAO,mBAAmB,KAAK,CAAC;AACrC,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AAMjB,SAAK,kBAAkB,CAAC,OAAO;AAC7B,WAAK,eAAe;AACpB,gCAA0B,IAAI;AAC9B,WAAK,cAAc;AACnB,uBAAiB,IAAI;AAAA,IACvB;AACA,SAAK,wBAAwB,MAAM;AACjC,YAAM,EAAE,IAAI,eAAe,IAAI;AAC/B,WAAK,yBAAyB,iBAC1B,yBAAyB,gBAAgB,EAAE,IAC3C;AAAA,IACN;AACA,SAAK,wBAAwB,CAAC,OAAO,SAAS;AAC5C,WAAK,iBAAiB;AACtB,WAAK,4BAA4B,KAAK,oBAAoB;AAC1D,wBAAkB,MAAM,KAAK,2BAA2B,KAAK,EAAE;AAC/D,YAAM,EAAE,IAAI,kBAAkB,0BAA0B,IAAI;AAC5D,UAAI,QAAQ,oBAAoB,CAAC,2BAA2B;AAC1D,gBAAQ,KAAK,GAAG,GAAG,OAAO,2BAA2B,gBAAgB,oBAAoB;AAAA,UACvF;AAAA,QACF,CAAC;AAAA,MACH;AACA,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,QAAQ,MAAM;AACjB,aAAO,KAAK,GAAG,MAAM,KAAK;AAAA,IAC5B;AACA,SAAK,kBAAkB,MAAM;AAC3B,YAAM,EAAE,2BAA2B,KAAK,IAAI;AAC5C,UAAI,CAAC,2BAA2B;AAC9B;AAAA,MACF;AACA,UAAI,kBAAkB,2BAA2B;AAC/C,kCAA0B,aAAa,eAAe,cAAc,IAAI,CAAC;AAAA,MAC3E;AAAA,IACF;AACA,SAAK,gBAAgB,MAAM;AACzB,YAAM,EAAE,0BAA0B,IAAI;AACtC,UAAI,CAAC,2BAA2B;AAC9B;AAAA,MACF;AACA,YAAM,KAAK,KAAK,MAAM;AACtB,UAAI,kBAAkB,2BAA2B;AAC/C,kCAA0B,aAAa,eAAe,EAAE;AAAA,MAC1D;AACA,cAAQ,gBAAgB,2BAA2B,KAAK,EAAE;AAC1D,WAAK,gBAAgB;AAAA,IACvB;AACA,SAAK,mBAAmB,MAAM;AAC5B,YAAM,EAAE,0BAA0B,IAAI;AACtC,UAAI,CAAC,2BAA2B;AAC9B;AAAA,MACF;AACA,UAAI,qBAAqB,2BAA2B;AAClD,kCAA0B,gBAAgB,aAAa;AACvD,kCAA0B,gBAAgB,aAAa;AAAA,MACzD;AACA,cAAQ,kBAAkB,yBAAyB;AAAA,IACrD;AACA,SAAK,OAAO,MAAM;AAChB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,eAAe,CAAC,OAAO;AAC1B,WAAK,UAAU;AACf,WAAK,WAAW,IAAI;AAAA,IACtB;AACA,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,4BAA4B;AACjC,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,wBAAwB,mBAAmB;AACzC,QAAI,CAAC,KAAK,MAAM;AACd;AAAA,IACF;AACA,wBAAoB,oBAAoB,IAAI,IAAI,kBAAkB,IAAI;AAAA,EACxE;AAAA,EACA,wBAAwB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,mBAAmB;AAAA,EAEnB;AAAA,EACA,8BAA8B;AAC5B,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,wBAAwB;AACtB,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,OAAO;AACT,WAAK,WAAW,IAAI;AAAA,IACtB,OACK;AACH,uBAAiB,KAAK,EAAE;AAAA,IAC1B;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,4BAA4B;AAC1B,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,mBAAmB;AACjB,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,0BAA0B;AACxB,SAAK,sBAAsB;AAC3B,SAAK,WAAW,IAAI;AAAA,EACtB;AAAA,EACA,wBAAwB;AACtB,mBAAe,MAAM,KAAK,eAAe;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,SAAK,sBAAsB;AAC3B,qBAAiB,IAAI;AACrB,oBAAgB,IAAI;AACpB,8BAA0B,IAAI;AAC9B,SAAK,sBAAsB,KAAK,SAAS;AAAA,EAC3C;AAAA,EACA,MAAM,oBAAoB;AACxB,UAAM,cAAc,IAAI;AACxB,2BAAuB,IAAI;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,uBAAmB,IAAI;AACvB,QAAI,KAAK,oBAAoB,CAAC,KAAK,2BAA2B;AAC5D,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,uBAAuB;AACrB,SAAK,iBAAiB;AACtB,wBAAoB,IAAI;AACxB,uBAAmB,IAAI;AACvB,yBAAqB,MAAM,KAAK,2BAA2B,KAAK,EAAE;AAClE,iCAA6B,IAAI;AACjC,wBAAoB,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW,UAAU,OAAO;AAChC,UAAM,EAAE,IAAI,2BAA2B,WAAW,oBAAoB,cAAc,wBAAwB,gBAAgB,gBAAgB,QAAQ,IAAI;AACxJ,WAAO,WAAW,MAAM;AAAA,MACtB,YAAY;AAAA,MACZ,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,cAAc,CAAC,KAAK;AAAA,MACpB;AAAA,MACA,MAAM;AAAA,IACR,GAAG,OAAO;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,WAAW;AACf,UAAM,gBAAgB,IAAI;AAC1B,gBAAY,KAAK,EAAE;AACnB,uBAAmB,KAAK,WAAW;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,0BAA0B;AAC9B,4BAAwB,IAAI;AAAA,EAC9B;AAAA,EACA,sBAAsB;AACpB,UAAM,EAAE,kBAAkB,GAAG,IAAI;AACjC,YAAS,OAAO,qBAAqB,WACjC,kBAAkB,IAAI,EAAE,IAAI,iBAAiB,CAAC,IAC9C,qBAAqB;AAAA,EAC3B;AAAA,EACA,eAAe;AACb,SAAK,yBAAyB,KAAK;AAAA,EACrC;AAAA,EACA,SAAS;AACP,SAAK,mBAAmB,KAAK;AAC7B,sBAAkB,IAAI;AAAA,EACxB;AAAA,EACA,gBAAgB;AACd,SAAK,0BAA0B,KAAK;AAAA,EACtC;AAAA,EACA,UAAU;AACR,SAAK,oBAAoB,KAAK;AAC9B,wBAAoB,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,UAAM,EAAE,UAAU,SAAS,IAAI;AAC/B,WAAO,WAAY,EAAE,OAAO,EAAE,OAAO,IAAI,sBAAsB,KAAK,IAAI,qBAAqB,GAAG,EAAE,kBAAkB,EAAE,OAAO,IAAI,aAAa,SAAS,KAAK,MAAM,KAAK,CAAC,kBAAmB,KAAK,gBAAgB,eAAgB,OAAO,KAAK,OAAO,MAAM,SAAS,MAAM,GAAG,EAAE,gBAAgB,EAAE,MAAM,KAAK,OAAO,KAAK,UAAU,MAAM,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAK;AAAA,EACjW;AAAA,EACA,eAAe;AACb,UAAM,EAAE,SAAS,aAAa,IAAI;AAClC,UAAM,cAAc,UAAW,EAAE,SAAS,EAAE,OAAO,IAAI,SAAS,OAAO,aAAa,GAAG,OAAO,IAAK;AACnG,WAAO,cAAe,EAAE,OAAO,EAAE,OAAO,IAAI,QAAQ,KAAK,IAAI,OAAO,GAAG,aAAa,KAAK,kBAAkB,CAAC,IAAK;AAAA,EACnH;AAAA,EACA,SAAS;AACP,UAAM,EAAE,2BAA2B,SAAS,OAAO,MAAM,gBAAgB,IAAI;AAC7E,UAAM,YAAY,6BAA6B;AAC/C,UAAM,SAAS,CAAC;AAChB,UAAM,YAAY,CAAC,kBAAkB,EAAE,OAAO,EAAE,OAAO,IAAI,OAAO,KAAK,KAAK,aAAa,CAAC,IAAI;AAC9F,WAAQ,EAAE,MAAM,EAAE,eAAe,cAAc,MAAM,GAAG,cAAc,OAAO,aAAa,UAAU,2BAA2B,QAAQ,IAAI,KAAK,MAAM,GAAG,MAAM,SAAS,GAAG,EAAE,OAAO,EAAE,OAAO;AAAA,MACzL,CAAC,YAAY,SAAS,GAAG;AAAA,MACzB,CAAC,YAAY,eAAe,GAAG;AAAA,IACjC,GAAG,KAAK,KAAK,gBAAgB,GAAG,WAAW,EAAE,OAAO,EAAE,OAAO;AAAA,MAC3D,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC;AAAA,MACnB,CAAC,IAAI,SAAS,GAAG;AAAA,IACnB,EAAE,GAAG,KAAK,aAAa,GAAG,EAAE,OAAO,EAAE,OAAO,IAAI,QAAQ,GAAG,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC,UAAU,KAAK,kBAAkB,IAAI,IAAI,CAAC,CAAC;AAAA,EAC7H;AAAA,EACA,WAAW,aAAa;AAAE,WAAO,CAAC,QAAQ;AAAA,EAAG;AAAA,EAC7C,IAAI,KAAK;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,WAAW,WAAW;AAAE,WAAO;AAAA,MAC7B,qBAAqB,CAAC,yBAAyB;AAAA,MAC/C,kBAAkB,CAAC,uBAAuB;AAAA,MAC1C,oBAAoB,CAAC,kBAAkB;AAAA,MACvC,kBAAkB,CAAC,6BAA6B;AAAA,MAChD,kBAAkB,CAAC,uBAAuB;AAAA,MAC1C,QAAQ,CAAC,aAAa;AAAA,MACtB,sBAAsB,CAAC,2BAA2B;AAAA,MAClD,aAAa,CAAC,kBAAkB;AAAA,MAChC,oBAAoB,CAAC,yBAAyB;AAAA,MAC9C,mBAAmB,CAAC,uBAAuB;AAAA,IAC7C;AAAA,EAAG;AAAA,EACH,WAAW,QAAQ;AAAE,WAAO;AAAA,EAAY;AAC1C,GAAG,CAAC,GAAG,mBAAmB;AAAA,EACtB,aAAa,CAAC,KAAK,YAAY;AAAA,EAC/B,YAAY,CAAC,IAAI;AAAA,EACjB,gBAAgB,CAAC,KAAK,eAAe;AAAA,EACrC,qBAAqB,CAAC,KAAK,qBAAqB;AAAA,EAChD,mBAAmB,CAAC,KAAK,kBAAkB;AAAA,EAC3C,kBAAkB,CAAC,EAAE;AAAA,EACrB,WAAW,CAAC,CAAC;AAAA,EACb,gBAAgB,CAAC,KAAK,eAAe;AAAA,EACrC,SAAS,CAAC,CAAC;AAAA,EACX,oBAAoB,CAAC,IAAI;AAAA,EACzB,YAAY,CAAC,IAAI;AAAA,EACjB,kBAAkB,CAAC,KAAK,iBAAiB;AAAA,EACzC,kBAAkB,CAAC,KAAK,iBAAiB;AAAA,EACzC,QAAQ,CAAC,IAAI;AAAA,EACb,sBAAsB,CAAC,KAAK,qBAAqB;AAAA,EACjD,aAAa,CAAC,GAAG;AAAA,EACjB,oBAAoB,CAAC,GAAG,mBAAmB;AAAA,EAC3C,SAAS,CAAC,GAAG;AAAA,EACb,mBAAmB,CAAC,KAAK,kBAAkB;AAAA,EAC3C,mBAAmB,CAAC,EAAE;AAAA,EACtB,6BAA6B,CAAC,EAAE;AAAA,EAChC,mBAAmB,CAAC,EAAE;AAAA,EACtB,cAAc,CAAC,EAAE;AAAA,EACjB,YAAY,CAAC,EAAE;AAAA,EACf,2BAA2B,CAAC,EAAE;AAChC,CAAC,CAAC;AACJ,SAASC,uBAAsB;AAC7B,MAAI,OAAO,mBAAmB,aAAa;AACzC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,mBAAmB,kBAAkB,gBAAgB,gBAAgB;AACzF,aAAW,QAAQ,aAAW;AAAE,YAAQ,SAAS;AAAA,MAC/C,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,yBAAe,OAAO,SAAS,OAAO;AAAA,QACxC;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,8BAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,IACJ;AAAA,EAAE,CAAC;AACL;AACAA,qBAAoB;;;ACjcpB,SAAS,mBAAmB,OAAO,OAAO;AACxC,UAAQ,QAAQ,SAAS;AAC3B;;;ACSA,IAAMC,OAAM;AAAA,EACV,MAAM;AAAA,EACN,gBAAgB;AAClB;AACA,IAAM,QAAQ;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,QAAQ;AAAA,EACZ,MAAM;AACR;AAEA,IAAM,gBAAgB;AAEtB,IAAM,0BAA0B,CAAC,WAAW,aAAa,OAAO,MAAM;AACtE,IAAM,aAA2B,mBAAmB,cAAc,EAAY;AAAA,EAC5E,cAAc;AACZ,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,wBAAwB,YAAY,MAAM,yBAAyB,CAAC;AACzE,SAAK,iBAAiB,CAAC;AACvB,SAAK,OAAO,uBAAuB,KAAK,CAAC;AACzC,SAAK,SAAS,GAAG,KAAK,IAAI;AAC1B,SAAK,eAAe,GAAG,KAAK,IAAI;AAMhC,SAAK,sBAAsB,MAAM;AAC/B,YAAM,EAAE,cAAc,QAAQ,MAAM,MAAM,IAAI;AAC9C,YAAM,eAAe,KAAK,uBAAuB,KAAK;AACtD,UAAI,KAAK,iBAAiB,cAAc;AACtC;AAAA,MACF;AACA,WAAK,uBAAuB;AAC5B,WAAK,eAAe;AACpB,WAAK,2BAA2B;AAChC,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AACA,mBAAa,SAAS;AACtB,mBAAa,aAAa,iBAAiB,MAAM;AACjD,mBAAa,aAAa,iBAAiB,cAAc,IAAI,CAAC;AAC9D,mBAAa,aAAa,iBAAiB,MAAM;AACjD,UAAI,CAAC,aAAa,IAAI;AACpB,qBAAa,KAAK;AAAA,MACpB;AACA,UAAI,CAAC,aAAa,OAAO;AACvB,qBAAa,QAAQ;AAAA,MACvB;AACA,UAAI,CAAC,aAAa,MAAM;AACtB,qBAAa,OAAO;AAAA,MACtB;AACA,mBAAa,iBAAiB,eAAe,KAAK,eAAe;AACjE,mBAAa,iBAAiB,WAAW,KAAK,iBAAiB;AAAA,IACjE;AACA,SAAK,yBAAyB,MAAM;AAClC,YAAM,EAAE,aAAa,IAAI;AACzB,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AACA,mBAAa,oBAAoB,eAAe,KAAK,eAAe;AACpE,mBAAa,oBAAoB,WAAW,KAAK,iBAAiB;AAAA,IACpE;AACA,SAAK,kBAAkB,CAAC,UAAU;AAChC,YAAM,UAAU,MAAM,OACnB,iBAAiB;AAAA,QAClB,SAAS;AAAA,MACX,CAAC,EACE,OAAO,CAAC,OAAO,yBAAI,QAAQ,iBAAiB;AAC/C,WAAK,sBAAsB,QAAQ,CAAC;AACpC,WAAK,oBAAoB;AAAA,IAC3B;AACA,SAAK,yBAAyB,CAAC,OAAO;AACpC,WAAK,sBAAsB;AAC3B,WAAK,oBAAoB;AAAA,IAC3B;AAMA,SAAK,2BAA2B,MAAM;AACpC,WAAK,OAAO;AACZ,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,kBAAkB,CAAC,UAAU;AAChC,UAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC;AAAA,MACF;AACA,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,gBAAgB,CAAC,UAAU;AAC9B,YAAM,WAAW,MAAM,OACpB,iBAAiB;AAAA,QAClB,SAAS;AAAA,MACX,CAAC,EACE,OAAO,CAAC,OAAO,yBAAI,QAAQ,kBAAkB;AAChD,WAAK,YAAY,SAAS,CAAC;AAC3B,WAAK,2BAA2B;AAAA,IAClC;AACA,SAAK,6BAA6B,MAAM;AACtC,YAAM,EAAE,WAAW,UAAU,cAAc,KAAK,IAAI;AACpD,UAAI,WAAW;AACb,kBAAU,mBAAmB,CAAC,YAAY,CAAC,OAAO,eAAe;AAAA,MACnE;AAAA,IACF;AACA,SAAK,eAAe,CAAC,QAAQ,UAAU;AACrC,YAAM,EAAE,MAAAC,OAAM,oBAAoB,IAAI;AACtC,YAAM,KAAK,GAAGA,KAAI,WAAW,KAAK;AAClC,aAAO,WAAW;AAClB,aAAO,aAAa,QAAQ,UAAU;AACtC,UAAI,CAAC,OAAO,IAAI;AACd,eAAO,KAAK;AAAA,MACd;AACA,aAAO,SAAS,UAAU;AAAA,IAC5B;AACA,SAAK,gBAAgB,CAAC,YAAY;AAChC,yCAAS,QAAQ,KAAK;AAAA,IACxB;AACA,SAAK,0BAA0B,CAAC,UAAU;AACxC,YAAM,UAAU,MAAM,OACnB,iBAAiB;AAAA,QAClB,SAAS;AAAA,MACX,CAAC,EACE,OAAO,CAAC,OAAO,yBAAI,QAAQ,iBAAiB;AAC/C,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,oBAAoB,CAAC,UAAU;AAClC,YAAM,EAAE,IAAI,IAAI;AAChB,YAAM,EAAE,gBAAgB,qBAAqB,KAAK,IAAI;AACtD,UAAI,CAAC,eAAe,QAAQ;AAC1B;AAAA,MACF;AACA,UAAI,gBAAgB,GAAG,GAAG;AACxB,cAAM,eAAe;AACrB,YAAI,CAAC,MAAM;AACT,eAAK,WAAW;AAChB;AAAA,QACF;AACA,cAAM,SAAS,eAAe,mBAAmB;AACjD,iBAAS,OAAO,MAAM,IAAI,KAAK,WAAW,KAAK;AAAA,MACjD;AACA,UAAI,QAAQ,OAAO;AACjB,aAAK,OAAO;AACZ;AAAA,MACF;AACA,UAAI,QAAQ,UAAU;AACpB,aAAK,WAAW,KAAK;AACrB,cAAM,eAAe;AACrB;AAAA,MACF;AACA,WAAK,uBAAuB,OAAO,KAAK,cAAc;AAAA,IACxD;AACA,SAAK,yBAAyB,CAAC,OAAO,KAAK,YAAY;AACrD,UAAI,CAAC,KAAK,WAAW,KAAK,uBAAuB,GAAG;AAClD;AAAA,MACF;AACA,YAAM,eAAe;AACrB,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,WAAW;AAChB,YAAI,QAAQ,UAAU,QAAQ,aAAa;AACzC,eAAK,sBAAsB;AAAA,QAC7B;AACA,YAAI,QAAQ,SAAS,QAAQ,WAAW;AACtC,eAAK,sBAAsB,QAAQ,SAAS;AAAA,QAC9C;AACA;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ;AAClB,aAAK,sBAAsB;AAAA,MAC7B;AACA,UAAI,QAAQ,OAAO;AACjB,aAAK,sBAAsB,QAAQ,SAAS;AAAA,MAC9C;AACA,YAAM,eAAe,KAAK;AAC1B,UAAI,QAAQ,WAAW;AACrB,aAAK,sBAAsB,mBAAmB,KAAK,IAAI,eAAe,GAAG,EAAE,GAAG,QAAQ,MAAM;AAAA,MAC9F;AACA,UAAI,QAAQ,aAAa;AACvB,aAAK,sBAAsB,mBAAmB,eAAe,GAAG,QAAQ,MAAM;AAAA,MAChF;AAAA,IACF;AACA,SAAK,gBAAgB,MAAM;AACzB,WAAK,SAAS;AACd,WAAK,GAAG,oBAAoB,sBAAsB,KAAK,aAAa;AAAA,IACtE;AACA,SAAK,aAAa,CAAC,QAAQ,CAAC,KAAK,SAAS;AACxC,WAAK,GAAG,iBAAiB,sBAAsB,KAAK,aAAa;AACjE,WAAK,OAAO;AAAA,IACd;AACA,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,qBAAqB;AAC1B,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,2BAAuB,IAAI;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,uBAAmB,IAAI;AAAA,EACzB;AAAA,EACA,uBAAuB;AACrB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAChB,SAAK,OAAO;AACZ,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,sBAAsB,KAAK,OAAO,IAAI;AAC3C,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,SAAS;AAAA,IAC7B;AACA,SAAK,sBAAsB,KAAK;AAChC,SAAK,2BAA2B;AAAA,EAClC;AAAA,EACA,8BAA8B,OAAO;AACnC,QAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC;AAAA,IACF;AACA,UAAM,eAAe,MAAM,aAAa;AACxC,QAAI,aAAa,SAAS,KAAK,EAAE,GAAG;AAClC;AAAA,IACF;AACA,SAAK,OAAO;AAAA,EACd;AAAA,EACA,6BAA6B;AAC3B,SAAK,cAAc,KAAK,cAAc;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW;AACf,UAAM,gBAAgB,IAAI;AAC1B,iBAAa,KAAK,YAAY;AAAA,EAChC;AAAA,EACA,mBAAmB;AACjB,UAAM,EAAE,OAAO,OAAO,SAAS,IAAI;AACnC,UAAM,iBAAkB,EAAE,QAAQ,EAAE,MAAM,MAAM,SAAS,cAAc,KAAK,gBAAgB,GAAG,EAAE,kBAAkB,EAAE,OAAOD,KAAI,gBAAgB,MAAM,MAAM,MAAM,KAAK,KAAK,wBAAwB,OAAc,MAAM,OAAO,aAAa,SAAS,CAAC,CAAC;AACvP,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAChB,UAAM,EAAE,gBAAgB,qBAAqB,MAAM,QAAQ,cAAc,OAAO,WAAW,oBAAoB,eAAe,IAAI;AAClI,UAAM,eAAe,eAAe,mBAAmB;AACvD,UAAM,sBAAqB,6CAAc,OAAM;AAC/C,WAAQ,EAAE,mBAAmB,EAAE,gBAAgC,mBAAmB,MAAM,OAAc,gBAAgB,GAAG,MAAY,oBAAwC,WAAsB,iBAAiB,MAAM,kBAAkB,aAAa,GAAG,EAAE,OAAO,EAAE,yBAAyB,oBAAoB,mBAAmB,6CAAc,IAAI,OAAOA,KAAI,MAAM,IAAI,QAAQ,SAAS,KAAK,0BAA0B,MAAM,QAAQ,UAAU,GAAG,GAAG,EAAE,QAAQ,EAAE,cAAc,KAAK,wBAAwB,CAAC,CAAC,CAAC;AAAA,EACxf;AAAA,EACA,SAAS;AACP,WAAQ,EAAE,UAAU,MAAM,KAAK,iBAAiB,GAAG,KAAK,gBAAgB,GAAG,EAAE,QAAQ,EAAE,MAAM,MAAM,SAAS,cAAc,KAAK,cAAc,CAAC,CAAC;AAAA,EACjJ;AAAA,EACA,WAAW,KAAK,eAAe;AAC7B,WAAO,CAAC,CAAC,cAAc,KAAK,CAAC,MAAM,MAAM,GAAG;AAAA,EAC9C;AAAA,EACA,IAAI,KAAK;AAAE,WAAO;AAAA,EAAM;AAAA,EACxB,WAAW,WAAW;AAAE,WAAO;AAAA,MAC7B,YAAY,CAAC,iBAAiB;AAAA,MAC9B,QAAQ,CAAC,aAAa;AAAA,MACtB,uBAAuB,CAAC,4BAA4B;AAAA,IACtD;AAAA,EAAG;AAAA,EACH,WAAW,QAAQ;AAAE,WAAO;AAAA,EAAe;AAC7C,GAAG,CAAC,GAAG,uBAAuB;AAAA,EAC1B,YAAY,CAAC,GAAG;AAAA,EAChB,kBAAkB,CAAC,EAAE;AAAA,EACrB,SAAS,CAAC,CAAC;AAAA,EACX,QAAQ,CAAC,IAAI;AAAA,EACb,sBAAsB,CAAC,KAAK,qBAAqB;AAAA,EACjD,aAAa,CAAC,GAAG;AAAA,EACjB,SAAS,CAAC,GAAG;AAAA,EACb,gBAAgB,CAAC,EAAE;AAAA,EACnB,uBAAuB,CAAC,EAAE;AAAA,EAC1B,YAAY,CAAC,EAAE;AACjB,GAAG,CAAC,CAAC,GAAG,eAAe,+BAA+B,CAAC,CAAC,CAAC;AAC3D,SAASE,uBAAsB;AAC7B,MAAI,OAAO,mBAAmB,aAAa;AACzC;AAAA,EACF;AACA,QAAM,aAAa,CAAC,uBAAuB,kBAAkB,gBAAgB,kBAAkB,iBAAiB;AAChH,aAAW,QAAQ,aAAW;AAAE,YAAQ,SAAS;AAAA,MAC/C,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,yBAAe,OAAO,SAAS,UAAU;AAAA,QAC3C;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,8BAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,YAAI,CAAC,eAAe,IAAI,OAAO,GAAG;AAChC,UAAAA,qBAAsB;AAAA,QACxB;AACA;AAAA,IACJ;AAAA,EAAE,CAAC;AACL;AACAA,qBAAoB;", "names": ["trapStack", "isSelectableInput", "isEscapeEvent", "isTabEvent", "isKeyForward", "isKeyBackward", "delay", "findIndex", "valueOrHandler", "getActualTarget", "createFocusTrap", "getOption", "findContainerIndex", "getNodeForOption", "getInitialFocusNode", "updateTabbableNodes", "tryFocus", "getReturnFocusNode", "checkPointerDown", "checkFocusIn", "checkKeyNav", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "removeListeners", "finishActivation", "finishDeactivation", "defineCustomElement", "CSS", "guid", "defineCustomElement"]}