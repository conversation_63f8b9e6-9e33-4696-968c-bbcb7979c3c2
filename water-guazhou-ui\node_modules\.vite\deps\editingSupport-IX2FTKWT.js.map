{"version": 3, "sources": ["../../@arcgis/core/layers/support/infoFor3D.js", "../../@arcgis/core/layers/graphics/editingSupport.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nvar _;!function(_){_.GLTF_BINARY=\"3D_glb\",_.GLTF_JSON=\"3D_gltf\"}(_||(_={}));export{_ as AssetType};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../Graphic.js\";import t from\"../../core/Collection.js\";import a from\"../../core/Error.js\";import{clone as r}from\"../../core/lang.js\";import{isSome as s,isNone as i}from\"../../core/maybe.js\";import{dataComponents as o}from\"../../core/urlUtils.js\";import{generateUUID as n}from\"../../core/uuid.js\";import{normalizeCentralMeridian as d}from\"../../geometry/support/normalizeUtils.js\";import{AssetMapEditFlags as l}from\"./assetEditingSupport.js\";import{isEditBusLayer as u,editEventBus as p}from\"../mixins/EditBusLayer.js\";import{AssetType as c}from\"../support/infoFor3D.js\";import{getEffectiveEditingEnabled as h,getEffectiveLayerCapabilities as m}from\"../support/layerUtils.js\";function y(e){return e&&null!=e.applyEdits}async function f(e,t,a,s={}){let i,o;const n={edits:a,result:new Promise(((e,t)=>{i=e,o=t}))};e.emit(\"apply-edits\",n);try{const{results:o,edits:n}=await g(e,t,a,s),d=e=>e.filter((e=>!e.error)).map(r),l={edits:n,addedFeatures:d(o.addFeatureResults),updatedFeatures:d(o.updateFeatureResults),deletedFeatures:d(o.deleteFeatureResults),addedAttachments:d(o.addAttachmentResults),updatedAttachments:d(o.updateAttachmentResults),deletedAttachments:d(o.deleteAttachmentResults),exceededTransferLimit:!1};return o.editedFeatureResults?.length&&(l.editedFeatures=o.editedFeatureResults),(l.addedFeatures.length||l.updatedFeatures.length||l.deletedFeatures.length||l.addedAttachments.length||l.updatedAttachments.length||l.deletedAttachments.length)&&(e.emit(\"edits\",l),u(e)&&p.emit(\"edits\",{layer:e,event:l})),i(l),o}catch(d){throw o(d),d}}async function g(e,t,r,s){if(await e.load(),!y(t))throw new a(`${e.type}-layer:no-editing-support`,\"Layer source does not support applyEdits capability\",{layer:e});if(!h(e))throw new a(`${e.type}-layer:editing-disabled`,\"Editing is disabled for layer\",{layer:e});const{edits:i,options:o}=await b(e,r,s);return i.addFeatures?.length||i.updateFeatures?.length||i.deleteFeatures?.length||i.addAttachments?.length||i.updateAttachments?.length||i.deleteAttachments?.length?{edits:i,results:await t.applyEdits(i,o)}:{edits:i,results:{addFeatureResults:[],updateFeatureResults:[],deleteFeatureResults:[],addAttachmentResults:[],updateAttachmentResults:[],deleteAttachmentResults:[]}}}async function b(e,r,i){const o=r&&(r.addFeatures||r.updateFeatures||r.deleteFeatures),n=r&&(r.addAttachments||r.updateAttachments||r.deleteAttachments),d=s(e.infoFor3D);if(!r||!o&&!n)throw new a(`${e.type}-layer:missing-parameters`,\"'addFeatures', 'updateFeatures', 'deleteFeatures', 'addAttachments', 'updateAttachments' or 'deleteAttachments' parameter is required\");const l=m(e);if(!l.data.isVersioned&&i?.gdbVersion)throw new a(`${e.type}-layer:invalid-parameter`,\"'gdbVersion' is applicable only if the layer supports versioned data. See: 'capabilities.data.isVersioned'\");if(!l.editing.supportsRollbackOnFailure&&i?.rollbackOnFailureEnabled)throw new a(`${e.type}-layer:invalid-parameter`,\"This layer does not support 'rollbackOnFailureEnabled' parameter. See: 'capabilities.editing.supportsRollbackOnFailure'\");if(!l.editing.supportsGlobalId&&i?.globalIdUsed)throw new a(`${e.type}-layer:invalid-parameter`,\"This layer does not support 'globalIdUsed' parameter. See: 'capabilities.editing.supportsGlobalId'\");if(!l.editing.supportsGlobalId&&n)throw new a(`${e.type}-layer:invalid-parameter`,\"'addAttachments', 'updateAttachments' and 'deleteAttachments' are applicable only if the layer supports global ids. See: 'capabilities.editing.supportsGlobalId'\");if(!i?.globalIdUsed&&n)throw new a(`${e.type}-layer:invalid-parameter`,\"When 'addAttachments', 'updateAttachments' or 'deleteAttachments' is specified, globalIdUsed should be set to true\");const u={...i};if(null!=u.rollbackOnFailureEnabled||l.editing.supportsRollbackOnFailure||(u.rollbackOnFailureEnabled=!0),!1===u.rollbackOnFailureEnabled&&\"original-and-current-features\"===u.returnServiceEditsOption)throw new a(`${e.type}-layer:invalid-parameter`,\"'original-and-current-features' is valid for 'returnServiceEditsOption' only when 'rollBackOnFailure' is true.\");if(!l.editing.supportsReturnServiceEditsInSourceSpatialReference&&u.returnServiceEditsInSourceSR)throw new a(`${e.type}-layer:invalid-parameter`,\"This layer does not support 'returnServiceEditsInSourceSR' parameter. See: 'capabilities.editing.supportsReturnServiceEditsInSourceSpatialReference'\");if(u.returnServiceEditsInSourceSR&&\"original-and-current-features\"!==u.returnServiceEditsOption)throw new a(`${e.type}-layer:invalid-parameter`,\"'returnServiceEditsInSourceSR' is valid only when 'returnServiceEditsOption' is set to 'original-and-current-features'\");const p={...r};if(p.addFeatures=r&&t.isCollection(r.addFeatures)?r.addFeatures.toArray():p.addFeatures||[],p.updateFeatures=r&&t.isCollection(r.updateFeatures)?r.updateFeatures.toArray():p.updateFeatures||[],p.deleteFeatures=r&&t.isCollection(r.deleteFeatures)?r.deleteFeatures.toArray():p.deleteFeatures||[],p.addFeatures.length&&!l.operations.supportsAdd)throw new a(`${e.type}-layer:unsupported-operation`,\"Layer does not support adding features.\");if(p.updateFeatures.length&&!l.operations.supportsUpdate)throw new a(`${e.type}-layer:unsupported-operation`,\"Layer does not support updating features.\");if(p.deleteFeatures.length&&!l.operations.supportsDelete)throw new a(`${e.type}-layer:unsupported-operation`,\"Layer does not support deleting features.\");p.addAttachments=p.addAttachments||[],p.updateAttachments=p.updateAttachments||[],p.deleteAttachments=p.deleteAttachments||[],p.addFeatures=p.addFeatures.map(S),p.updateFeatures=p.updateFeatures.map(S),p.addAssets=[];const c=i?.globalIdUsed||d;p.addFeatures.forEach((t=>F(t,e,c))),p.updateFeatures.forEach((t=>I(t,e,c))),p.deleteFeatures.forEach((t=>A(t,e,c))),p.addAttachments.forEach((t=>v(t,e))),p.updateAttachments.forEach((t=>v(t,e))),d&&await R(p,e);return{edits:await E(p),options:u}}function w(e,t,r){if(r){if(\"attributes\"in e&&!e.attributes[t.globalIdField])throw new a(`${t.type}-layer:invalid-parameter`,\"Feature should have 'globalId' when 'globalIdUsed' is true\");if(!(\"attributes\"in e)&&!e.globalId)throw new a(`${t.type}-layer:invalid-parameter`,\"'globalId' of the feature should be passed when 'globalIdUsed' is true\")}if(\"geometry\"in e&&s(e.geometry)){if(e.geometry.hasZ&&!1===t.capabilities?.data.supportsZ)throw new a(`${t.type}-layer:z-unsupported`,\"Layer does not support z values while feature has z values.\");if(e.geometry.hasM&&!1===t.capabilities?.data.supportsM)throw new a(`${t.type}-layer:m-unsupported`,\"Layer does not support m values while feature has m values.\")}}function F(e,t,a){w(e,t,a)}function A(e,t,a){w(e,t,a)}function I(e,t,r){w(e,t,r);const i=m(t);if(\"geometry\"in e&&s(e.geometry)&&!i?.editing.supportsGeometryUpdate)throw new a(`${t.type}-layer:unsupported-operation`,\"Layer does not support geometry updates.\")}function v(e,t){const{feature:r,attachment:s}=e;if(!r||\"attributes\"in r&&!r.attributes[t.globalIdField])throw new a(`${t.type}-layer:invalid-parameter`,\"Attachment should have reference to a feature with 'globalId'\");if(!(\"attributes\"in r)&&!r.globalId)throw new a(`${t.type}-layer:invalid-parameter`,\"Attachment should have reference to 'globalId' of the parent feature\");if(!s.globalId)throw new a(`${t.type}-layer:invalid-parameter`,\"Attachment should have 'globalId'\");if(!s.data&&!s.uploadId)throw new a(`${t.type}-layer:invalid-parameter`,\"Attachment should have 'data' or 'uploadId'\");if(!(s.data instanceof File&&!!s.data.name)&&!s.name)throw new a(`${t.type}-layer:invalid-parameter`,\"'name' is required when attachment is specified as Base64 encoded string using 'data'\");if(!t.capabilities?.editing.supportsUploadWithItemId&&s.uploadId)throw new a(`${t.type}-layer:invalid-parameter`,\"This layer does not support 'uploadId' parameter. See: 'capabilities.editing.supportsUploadWithItemId'\");if(\"string\"==typeof s.data){const e=o(s.data);if(e&&!e.isBase64)throw new a(`${t.type}-layer:invalid-parameter`,\"Attachment 'data' should be a Blob, File or Base64 encoded string\")}}async function E(e){const t=e.addFeatures??[],a=e.updateFeatures??[],r=t.concat(a).map((e=>e.geometry)),s=await d(r),i=t.length,o=a.length;return s.slice(0,i).forEach(((e,a)=>t[a].geometry=e)),s.slice(i,i+o).forEach(((e,t)=>a[t].geometry=e)),e}function S(t){const a=new e;return t.attributes||(t.attributes={}),a.geometry=t.geometry,a.attributes=t.attributes,a}async function R(e,t){if(i(t.infoFor3D))return;const{infoFor3D:a}=t;let r=!1;for(const i of a.editFormats)if(i===c.GLTF_BINARY){r=!0;break}const s=[];for(const i of e.addFeatures??[])s.push($(i,e,t,r));for(const i of e.updateFeatures??[])s.push($(i,e,t,r));const o=await Promise.allSettled(s);for(const i of o)if(\"rejected\"===i.status)throw i.reason}async function $(e,t,r,o){if(i(e.geometry)||\"mesh\"!==e.geometry.type)return;const d=e.geometry,u=r.globalIdField;if(s(r.parsedUrl)&&s(d.external)&&Array.isArray(d.external.source)&&1===d.external.source.length&&\"source\"in d.external.source[0]&&\"string\"==typeof d.external.source[0].source&&d.external.source[0].source.startsWith(r.parsedUrl.path))return;if(!o)throw new a(`${r.type}-layer:binary-gltf-asset-not-supported`,\"3DObjectFeatureLayer requires binary glTF (.glb) support for updating mesh geometry.\");const p=await d.toBinaryGLTF({ignoreLocalTransform:!0}),h=await p.buffer(),m=`{${n()}}`,y=`${m}.glb`;t.addAssets?.push({featureGlobalId:e.getAttribute(u),assetMapGlobalId:m,assetName:y,flags:s(d.transform)&&d.transform.geographic?l.PROJECT_VERTICES:0,data:h.data,mimeType:h.type,assetType:c.GLTF_BINARY,feature:e})}export{f as applyEdits};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAE,cAAY,UAASA,GAAE,YAAU;AAAS,EAAE,MAAI,IAAE,CAAC,EAAE;;;ACA0mB,SAAS,EAAE,GAAE;AAAC,SAAO,KAAG,QAAM,EAAE;AAAU;AAAC,eAAe,EAAE,GAAEC,IAAE,GAAEC,KAAE,CAAC,GAAE;AAJ3vB;AAI4vB,MAAI,GAAEC;AAAE,QAAMC,KAAE,EAAC,OAAM,GAAE,QAAO,IAAI,QAAS,CAACC,IAAEJ,OAAI;AAAC,QAAEI,IAAEF,KAAEF;AAAA,EAAC,CAAE,EAAC;AAAE,IAAE,KAAK,eAAcG,EAAC;AAAE,MAAG;AAAC,UAAK,EAAC,SAAQD,IAAE,OAAMC,GAAC,IAAE,MAAME,GAAE,GAAEL,IAAE,GAAEC,EAAC,GAAEK,KAAE,CAAAF,OAAGA,GAAE,OAAQ,CAAAA,OAAG,CAACA,GAAE,KAAM,EAAE,IAAI,CAAC,GAAE,IAAE,EAAC,OAAMD,IAAE,eAAcG,GAAEJ,GAAE,iBAAiB,GAAE,iBAAgBI,GAAEJ,GAAE,oBAAoB,GAAE,iBAAgBI,GAAEJ,GAAE,oBAAoB,GAAE,kBAAiBI,GAAEJ,GAAE,oBAAoB,GAAE,oBAAmBI,GAAEJ,GAAE,uBAAuB,GAAE,oBAAmBI,GAAEJ,GAAE,uBAAuB,GAAE,uBAAsB,MAAE;AAAE,aAAO,KAAAA,GAAE,yBAAF,mBAAwB,YAAS,EAAE,iBAAeA,GAAE,wBAAuB,EAAE,cAAc,UAAQ,EAAE,gBAAgB,UAAQ,EAAE,gBAAgB,UAAQ,EAAE,iBAAiB,UAAQ,EAAE,mBAAmB,UAAQ,EAAE,mBAAmB,YAAU,EAAE,KAAK,SAAQ,CAAC,GAAE,EAAE,CAAC,KAAG,EAAE,KAAK,SAAQ,EAAC,OAAM,GAAE,OAAM,EAAC,CAAC,IAAG,EAAE,CAAC,GAAEA;AAAA,EAAC,SAAOI,IAAE;AAAC,UAAMJ,GAAEI,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,eAAeD,GAAE,GAAEL,IAAEO,IAAEN,IAAE;AAJvjD;AAIwjD,MAAG,MAAM,EAAE,KAAK,GAAE,CAAC,EAAED,EAAC,EAAE,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,6BAA4B,uDAAsD,EAAC,OAAM,EAAC,CAAC;AAAE,MAAG,CAAC,EAAE,CAAC,EAAE,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,2BAA0B,iCAAgC,EAAC,OAAM,EAAC,CAAC;AAAE,QAAK,EAAC,OAAM,GAAE,SAAQE,GAAC,IAAE,MAAM,EAAE,GAAEK,IAAEN,EAAC;AAAE,WAAO,OAAE,gBAAF,mBAAe,aAAQ,OAAE,mBAAF,mBAAkB,aAAQ,OAAE,mBAAF,mBAAkB,aAAQ,OAAE,mBAAF,mBAAkB,aAAQ,OAAE,sBAAF,mBAAqB,aAAQ,OAAE,sBAAF,mBAAqB,UAAO,EAAC,OAAM,GAAE,SAAQ,MAAMD,GAAE,WAAW,GAAEE,EAAC,EAAC,IAAE,EAAC,OAAM,GAAE,SAAQ,EAAC,mBAAkB,CAAC,GAAE,sBAAqB,CAAC,GAAE,sBAAqB,CAAC,GAAE,sBAAqB,CAAC,GAAE,yBAAwB,CAAC,GAAE,yBAAwB,CAAC,EAAC,EAAC;AAAC;AAAC,eAAe,EAAE,GAAEK,IAAE,GAAE;AAAC,QAAML,KAAEK,OAAIA,GAAE,eAAaA,GAAE,kBAAgBA,GAAE,iBAAgBJ,KAAEI,OAAIA,GAAE,kBAAgBA,GAAE,qBAAmBA,GAAE,oBAAmBD,KAAE,EAAE,EAAE,SAAS;AAAE,MAAG,CAACC,MAAG,CAACL,MAAG,CAACC,GAAE,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,6BAA4B,uIAAuI;AAAE,QAAM,IAAEK,GAAE,CAAC;AAAE,MAAG,CAAC,EAAE,KAAK,gBAAa,uBAAG,YAAW,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,4GAA4G;AAAE,MAAG,CAAC,EAAE,QAAQ,8BAA2B,uBAAG,0BAAyB,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,yHAAyH;AAAE,MAAG,CAAC,EAAE,QAAQ,qBAAkB,uBAAG,cAAa,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,oGAAoG;AAAE,MAAG,CAAC,EAAE,QAAQ,oBAAkBL,GAAE,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,kKAAkK;AAAE,MAAG,EAAC,uBAAG,iBAAcA,GAAE,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,oHAAoH;AAAE,QAAM,IAAE,EAAC,GAAG,EAAC;AAAE,MAAG,QAAM,EAAE,4BAA0B,EAAE,QAAQ,8BAA4B,EAAE,2BAAyB,OAAI,UAAK,EAAE,4BAA0B,oCAAkC,EAAE,yBAAyB,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,gHAAgH;AAAE,MAAG,CAAC,EAAE,QAAQ,sDAAoD,EAAE,6BAA6B,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,sJAAsJ;AAAE,MAAG,EAAE,gCAA8B,oCAAkC,EAAE,yBAAyB,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,4BAA2B,wHAAwH;AAAE,QAAMM,KAAE,EAAC,GAAGF,GAAC;AAAE,MAAGE,GAAE,cAAYF,MAAG,EAAE,aAAaA,GAAE,WAAW,IAAEA,GAAE,YAAY,QAAQ,IAAEE,GAAE,eAAa,CAAC,GAAEA,GAAE,iBAAeF,MAAG,EAAE,aAAaA,GAAE,cAAc,IAAEA,GAAE,eAAe,QAAQ,IAAEE,GAAE,kBAAgB,CAAC,GAAEA,GAAE,iBAAeF,MAAG,EAAE,aAAaA,GAAE,cAAc,IAAEA,GAAE,eAAe,QAAQ,IAAEE,GAAE,kBAAgB,CAAC,GAAEA,GAAE,YAAY,UAAQ,CAAC,EAAE,WAAW,YAAY,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,gCAA+B,yCAAyC;AAAE,MAAGA,GAAE,eAAe,UAAQ,CAAC,EAAE,WAAW,eAAe,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,gCAA+B,2CAA2C;AAAE,MAAGA,GAAE,eAAe,UAAQ,CAAC,EAAE,WAAW,eAAe,OAAM,IAAI,EAAE,GAAG,EAAE,IAAI,gCAA+B,2CAA2C;AAAE,EAAAA,GAAE,iBAAeA,GAAE,kBAAgB,CAAC,GAAEA,GAAE,oBAAkBA,GAAE,qBAAmB,CAAC,GAAEA,GAAE,oBAAkBA,GAAE,qBAAmB,CAAC,GAAEA,GAAE,cAAYA,GAAE,YAAY,IAAI,CAAC,GAAEA,GAAE,iBAAeA,GAAE,eAAe,IAAI,CAAC,GAAEA,GAAE,YAAU,CAAC;AAAE,QAAM,KAAE,uBAAG,iBAAcH;AAAE,EAAAG,GAAE,YAAY,QAAS,CAAAT,OAAG,EAAEA,IAAE,GAAE,CAAC,CAAE,GAAES,GAAE,eAAe,QAAS,CAAAT,OAAG,EAAEA,IAAE,GAAE,CAAC,CAAE,GAAES,GAAE,eAAe,QAAS,CAAAT,OAAG,EAAEA,IAAE,GAAE,CAAC,CAAE,GAAES,GAAE,eAAe,QAAS,CAAAT,OAAGQ,GAAER,IAAE,CAAC,CAAE,GAAES,GAAE,kBAAkB,QAAS,CAAAT,OAAGQ,GAAER,IAAE,CAAC,CAAE,GAAEM,MAAG,MAAM,EAAEG,IAAE,CAAC;AAAE,SAAM,EAAC,OAAM,MAAMC,GAAED,EAAC,GAAE,SAAQ,EAAC;AAAC;AAAC,SAASE,GAAE,GAAEX,IAAEO,IAAE;AAJ/wL;AAIgxL,MAAGA,IAAE;AAAC,QAAG,gBAAe,KAAG,CAAC,EAAE,WAAWP,GAAE,aAAa,EAAE,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,4BAA2B,4DAA4D;AAAE,QAAG,EAAE,gBAAe,MAAI,CAAC,EAAE,SAAS,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,4BAA2B,wEAAwE;AAAA,EAAC;AAAC,MAAG,cAAa,KAAG,EAAE,EAAE,QAAQ,GAAE;AAAC,QAAG,EAAE,SAAS,QAAM,YAAK,KAAAA,GAAE,iBAAF,mBAAgB,KAAK,WAAU,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,wBAAuB,6DAA6D;AAAE,QAAG,EAAE,SAAS,QAAM,YAAK,KAAAA,GAAE,iBAAF,mBAAgB,KAAK,WAAU,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,wBAAuB,6DAA6D;AAAA,EAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,EAAAW,GAAE,GAAEX,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE,GAAE;AAAC,EAAAW,GAAE,GAAEX,IAAE,CAAC;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAEO,IAAE;AAAC,EAAAI,GAAE,GAAEX,IAAEO,EAAC;AAAE,QAAM,IAAEC,GAAER,EAAC;AAAE,MAAG,cAAa,KAAG,EAAE,EAAE,QAAQ,KAAG,EAAC,uBAAG,QAAQ,wBAAuB,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,gCAA+B,0CAA0C;AAAC;AAAC,SAASQ,GAAE,GAAER,IAAE;AAJjtN;AAIktN,QAAK,EAAC,SAAQO,IAAE,YAAWN,GAAC,IAAE;AAAE,MAAG,CAACM,MAAG,gBAAeA,MAAG,CAACA,GAAE,WAAWP,GAAE,aAAa,EAAE,OAAM,IAAI,EAAE,GAAGA,GAAE,IAAI,4BAA2B,+DAA+D;AAAE,MAAG,EAAE,gBAAeO,OAAI,CAACA,GAAE,SAAS,OAAM,IAAI,EAAE,GAAGP,GAAE,IAAI,4BAA2B,sEAAsE;AAAE,MAAG,CAACC,GAAE,SAAS,OAAM,IAAI,EAAE,GAAGD,GAAE,IAAI,4BAA2B,mCAAmC;AAAE,MAAG,CAACC,GAAE,QAAM,CAACA,GAAE,SAAS,OAAM,IAAI,EAAE,GAAGD,GAAE,IAAI,4BAA2B,6CAA6C;AAAE,MAAG,EAAEC,GAAE,gBAAgB,QAAM,CAAC,CAACA,GAAE,KAAK,SAAO,CAACA,GAAE,KAAK,OAAM,IAAI,EAAE,GAAGD,GAAE,IAAI,4BAA2B,uFAAuF;AAAE,MAAG,GAAC,KAAAA,GAAE,iBAAF,mBAAgB,QAAQ,6BAA0BC,GAAE,SAAS,OAAM,IAAI,EAAE,GAAGD,GAAE,IAAI,4BAA2B,wGAAwG;AAAE,MAAG,YAAU,OAAOC,GAAE,MAAK;AAAC,UAAMG,KAAE,GAAEH,GAAE,IAAI;AAAE,QAAGG,MAAG,CAACA,GAAE,SAAS,OAAM,IAAI,EAAE,GAAGJ,GAAE,IAAI,4BAA2B,mEAAmE;AAAA,EAAC;AAAC;AAAC,eAAeU,GAAE,GAAE;AAAC,QAAMV,KAAE,EAAE,eAAa,CAAC,GAAE,IAAE,EAAE,kBAAgB,CAAC,GAAEO,KAAEP,GAAE,OAAO,CAAC,EAAE,IAAK,CAAAI,OAAGA,GAAE,QAAS,GAAEH,KAAE,MAAM,EAAEM,EAAC,GAAE,IAAEP,GAAE,QAAOE,KAAE,EAAE;AAAO,SAAOD,GAAE,MAAM,GAAE,CAAC,EAAE,QAAS,CAACG,IAAEQ,OAAIZ,GAAEY,EAAC,EAAE,WAASR,EAAE,GAAEH,GAAE,MAAM,GAAE,IAAEC,EAAC,EAAE,QAAS,CAACE,IAAEJ,OAAI,EAAEA,EAAC,EAAE,WAASI,EAAE,GAAE;AAAC;AAAC,SAAS,EAAEJ,IAAE;AAAC,QAAM,IAAE,IAAI;AAAE,SAAOA,GAAE,eAAaA,GAAE,aAAW,CAAC,IAAG,EAAE,WAASA,GAAE,UAAS,EAAE,aAAWA,GAAE,YAAW;AAAC;AAAC,eAAe,EAAE,GAAEA,IAAE;AAAC,MAAG,EAAEA,GAAE,SAAS,EAAE;AAAO,QAAK,EAAC,WAAU,EAAC,IAAEA;AAAE,MAAIO,KAAE;AAAG,aAAU,KAAK,EAAE,YAAY,KAAG,MAAI,EAAE,aAAY;AAAC,IAAAA,KAAE;AAAG;AAAA,EAAK;AAAC,QAAMN,KAAE,CAAC;AAAE,aAAU,KAAK,EAAE,eAAa,CAAC,EAAE,CAAAA,GAAE,KAAK,EAAE,GAAE,GAAED,IAAEO,EAAC,CAAC;AAAE,aAAU,KAAK,EAAE,kBAAgB,CAAC,EAAE,CAAAN,GAAE,KAAK,EAAE,GAAE,GAAED,IAAEO,EAAC,CAAC;AAAE,QAAML,KAAE,MAAM,QAAQ,WAAWD,EAAC;AAAE,aAAU,KAAKC,GAAE,KAAG,eAAa,EAAE,OAAO,OAAM,EAAE;AAAM;AAAC,eAAe,EAAE,GAAEF,IAAEO,IAAEL,IAAE;AAJjkR;AAIkkR,MAAG,EAAE,EAAE,QAAQ,KAAG,WAAS,EAAE,SAAS,KAAK;AAAO,QAAMI,KAAE,EAAE,UAAS,IAAEC,GAAE;AAAc,MAAG,EAAEA,GAAE,SAAS,KAAG,EAAED,GAAE,QAAQ,KAAG,MAAM,QAAQA,GAAE,SAAS,MAAM,KAAG,MAAIA,GAAE,SAAS,OAAO,UAAQ,YAAWA,GAAE,SAAS,OAAO,CAAC,KAAG,YAAU,OAAOA,GAAE,SAAS,OAAO,CAAC,EAAE,UAAQA,GAAE,SAAS,OAAO,CAAC,EAAE,OAAO,WAAWC,GAAE,UAAU,IAAI,EAAE;AAAO,MAAG,CAACL,GAAE,OAAM,IAAI,EAAE,GAAGK,GAAE,IAAI,0CAAyC,sFAAsF;AAAE,QAAME,KAAE,MAAMH,GAAE,aAAa,EAAC,sBAAqB,KAAE,CAAC,GAAE,IAAE,MAAMG,GAAE,OAAO,GAAE,IAAE,IAAI,EAAE,CAAC,KAAII,KAAE,GAAG,CAAC;AAAO,QAAAb,GAAE,cAAF,mBAAa,KAAK,EAAC,iBAAgB,EAAE,aAAa,CAAC,GAAE,kBAAiB,GAAE,WAAUa,IAAE,OAAM,EAAEP,GAAE,SAAS,KAAGA,GAAE,UAAU,aAAW,EAAE,mBAAiB,GAAE,MAAK,EAAE,MAAK,UAAS,EAAE,MAAK,WAAU,EAAE,aAAY,SAAQ,EAAC;AAAE;", "names": ["_", "t", "s", "o", "n", "e", "g", "d", "r", "v", "p", "E", "w", "a", "y"]}