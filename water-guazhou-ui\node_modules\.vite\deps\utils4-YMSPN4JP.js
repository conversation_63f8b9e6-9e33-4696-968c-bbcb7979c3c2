import "./chunk-H3AJBOWU.js";

// node_modules/@esri/calcite-components/dist/components/utils4.js
function offsetParent(element) {
  for (let ancestor = element; ancestor; ancestor = flatTreeParent(ancestor)) {
    if (!(ancestor instanceof Element)) {
      continue;
    }
    if (getComputedStyle(ancestor).display === "none") {
      return null;
    }
  }
  for (let ancestor = flatTreeParent(element); ancestor; ancestor = flatTreeParent(ancestor)) {
    if (!(ancestor instanceof Element)) {
      continue;
    }
    const style = getComputedStyle(ancestor);
    if (style.display === "contents") {
      continue;
    }
    if (style.position !== "static" || style.filter !== "none") {
      return ancestor;
    }
    if (ancestor.tagName === "BODY") {
      return ancestor;
    }
  }
  return null;
}
function flatTreeParent(element) {
  if (element.assignedSlot) {
    return element.assignedSlot;
  }
  if (element.parentNode instanceof ShadowRoot) {
    return element.parentNode.host;
  }
  return element.parentNode;
}
export {
  offsetParent
};
/*! Bundled license information:

@esri/calcite-components/dist/components/utils4.js:
  (*!
   * All material copyright ESRI, All Rights Reserved, unless otherwise specified.
   * See https://github.com/Esri/calcite-components/blob/master/LICENSE.md for details.
   * v1.0.8-next.4
   *)
*/
//# sourceMappingURL=utils4-YMSPN4JP.js.map
