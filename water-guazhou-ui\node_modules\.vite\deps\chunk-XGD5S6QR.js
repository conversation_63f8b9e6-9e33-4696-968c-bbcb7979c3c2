import {
  e as e3,
  g,
  u
} from "./chunk-UOKTNY52.js";
import {
  E,
  e,
  n2 as n,
  r,
  t3 as t,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a
} from "./chunk-JN4FSB7Y.js";
import {
  e as e2
} from "./chunk-2CM7MIII.js";
import {
  p
} from "./chunk-REW33H3I.js";
import {
  N
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/accessorSupport/MultiOriginStore.js
var i = class _i {
  constructor() {
    this._propertyOriginMap = /* @__PURE__ */ new Map(), this._originStores = new Array(E), this._values = /* @__PURE__ */ new Map(), this.multipleOriginsSupported = true;
  }
  clone(s) {
    const o = new _i(), n2 = this._originStores[r.DEFAULTS];
    n2 && n2.forEach((s2, e4) => {
      o.set(e4, p(s2), r.DEFAULTS);
    });
    for (let i2 = r.SERVICE; i2 < E; i2++) {
      const e4 = this._originStores[i2];
      e4 && e4.forEach((e5, r2) => {
        s && s.has(r2) || o.set(r2, p(e5), i2);
      });
    }
    return o;
  }
  get(t2, s) {
    const e4 = void 0 === s ? this._values : this._originStores[s];
    return e4 ? e4.get(t2) : void 0;
  }
  keys(t2) {
    const s = null == t2 ? this._values : this._originStores[t2];
    return s ? [...s.keys()] : [];
  }
  set(t2, e4, i2 = r.USER) {
    let o = this._originStores[i2];
    if (o || (o = /* @__PURE__ */ new Map(), this._originStores[i2] = o), o.set(t2, e4), !this._values.has(t2) || N(this._propertyOriginMap.get(t2)) <= i2) {
      const s = this._values.get(t2);
      return this._values.set(t2, e4), this._propertyOriginMap.set(t2, i2), s !== e4;
    }
    return false;
  }
  delete(t2, s = r.USER) {
    const e4 = this._originStores[s];
    if (!e4) return;
    const i2 = e4.get(t2);
    if (e4.delete(t2), this._values.has(t2) && this._propertyOriginMap.get(t2) === s) {
      this._values.delete(t2);
      for (let e5 = s - 1; e5 >= 0; e5--) {
        const s2 = this._originStores[e5];
        if (s2 && s2.has(t2)) {
          this._values.set(t2, s2.get(t2)), this._propertyOriginMap.set(t2, e5);
          break;
        }
      }
    }
    return i2;
  }
  has(t2, s) {
    const e4 = void 0 === s ? this._values : this._originStores[s];
    return !!e4 && e4.has(t2);
  }
  revert(t2, s) {
    for (; s > 0 && !this.has(t2, s); ) --s;
    const e4 = this._originStores[s], r2 = e4 && e4.get(t2), i2 = this._values.get(t2);
    return this._values.set(t2, r2), this._propertyOriginMap.set(t2, s), i2 !== r2;
  }
  originOf(t2) {
    return this._propertyOriginMap.get(t2) || r.DEFAULTS;
  }
  forEach(t2) {
    this._values.forEach(t2);
  }
};

// node_modules/@arcgis/core/core/ReadOnlyMultiOriginJSONSupport.js
var a2 = (t2) => {
  let a3 = class extends t2 {
    constructor(...r2) {
      super(...r2);
      const t3 = N(e2(this)), i2 = t3.store, c = new i();
      t3.store = c, e3(t3, i2, c);
    }
    read(r2, t3) {
      u(this, r2, t3);
    }
    getAtOrigin(r2, t3) {
      const s = m(this), o = t(t3);
      if ("string" == typeof r2) return s.get(r2, o);
      const e4 = {};
      return r2.forEach((r3) => {
        e4[r3] = s.get(r3, o);
      }), e4;
    }
    originOf(r2) {
      return n(this.originIdOf(r2));
    }
    originIdOf(r2) {
      return m(this).originOf(r2);
    }
    revert(r2, t3) {
      const s = m(this), o = t(t3), e4 = e2(this);
      let c;
      c = "string" == typeof r2 ? "*" === r2 ? s.keys(o) : [r2] : r2, c.forEach((r3) => {
        e4.invalidate(r3), s.revert(r3, o), e4.commit(r3);
      });
    }
  };
  return a3 = e([a("esri.core.ReadOnlyMultiOriginJSONSupport")], a3), a3;
};
function m(r2) {
  return e2(r2).store;
}
var f = class extends a2(v) {
};
f = e([a("esri.core.ReadOnlyMultiOriginJSONSupport")], f);

// node_modules/@arcgis/core/core/MultiOriginJSONSupport.js
var u2 = (t2) => {
  let s = class extends t2 {
    constructor(...r2) {
      super(...r2);
    }
    clear(r2, t3 = "user") {
      return l(this).delete(r2, t(t3));
    }
    write(r2 = {}, t3) {
      return g(this, r2 = r2 || {}, t3), r2;
    }
    setAtOrigin(r2, t3, s2) {
      e2(this).setAtOrigin(r2, t3, t(s2));
    }
    removeOrigin(r2) {
      const t3 = l(this), s2 = t(r2), i2 = t3.keys(s2);
      for (const e4 of i2) t3.originOf(e4) === s2 && t3.set(e4, t3.get(e4, s2), r.USER);
    }
    updateOrigin(r2, t3) {
      const s2 = l(this), o = t(t3), c = this.get(r2);
      for (let e4 = o + 1; e4 < E; ++e4) s2.delete(r2, e4);
      s2.set(r2, c, o);
    }
    toJSON(r2) {
      return this.write({}, r2);
    }
  };
  return s = e([a("esri.core.WriteableMultiOriginJSONSupport")], s), s.prototype.toJSON.isDefaultToJSON = true, s;
};
function l(r2) {
  return e2(r2).store;
}
var O = (t2) => {
  let e4 = class extends u2(a2(t2)) {
    constructor(...r2) {
      super(...r2);
    }
  };
  return e4 = e([a("esri.core.MultiOriginJSONSupport")], e4), e4;
};
var S = class extends O(v) {
};
S = e([a("esri.core.MultiOriginJSONSupport")], S);

export {
  O,
  S
};
//# sourceMappingURL=chunk-XGD5S6QR.js.map
