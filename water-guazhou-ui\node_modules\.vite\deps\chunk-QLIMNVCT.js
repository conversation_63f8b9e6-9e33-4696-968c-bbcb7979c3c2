import {
  m
} from "./chunk-CK22CKHH.js";
import {
  I,
  e as e4,
  r as r3
} from "./chunk-7QFVH5MN.js";
import {
  s
} from "./chunk-IUNR7SKI.js";
import {
  t as t3
} from "./chunk-UHA44FM7.js";
import {
  e as e3
} from "./chunk-XSQFM27N.js";
import {
  e as e2
} from "./chunk-QYOAH6AO.js";
import {
  e
} from "./chunk-A7PY25IH.js";
import {
  He
} from "./chunk-33Z6JDMT.js";
import {
  x
} from "./chunk-6NE6A2GD.js";
import {
  n as n2
} from "./chunk-NOZFLZZL.js";
import {
  u as u2
} from "./chunk-I7WHRVHF.js";
import {
  b
} from "./chunk-QMNV7QQK.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  r as r2
} from "./chunk-WXFAAYJL.js";
import {
  n,
  t as t2
} from "./chunk-36FLFRUE.js";
import {
  E,
  w
} from "./chunk-EKX3LLYN.js";
import {
  a2 as a,
  u2 as u
} from "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/layers/support/capabilities.js
var t4 = { analytics: { supportsCacheHint: false }, attachment: { supportsContentType: false, supportsExifInfo: false, supportsKeywords: false, supportsName: false, supportsSize: false, supportsCacheHint: false, supportsResize: false }, data: { isVersioned: false, supportsAttachment: false, supportsM: false, supportsZ: false }, editing: { supportsDeleteByAnonymous: false, supportsDeleteByOthers: false, supportsGeometryUpdate: false, supportsGlobalId: false, supportsReturnServiceEditsInSourceSpatialReference: false, supportsRollbackOnFailure: false, supportsUpdateByAnonymous: false, supportsUpdateByOthers: false, supportsUpdateWithoutM: false, supportsUploadWithItemId: false }, metadata: { supportsAdvancedFieldProperties: false }, operations: { supportsCalculate: false, supportsTruncate: false, supportsValidateSql: false, supportsAdd: false, supportsDelete: false, supportsEditing: false, supportsChangeTracking: false, supportsQuery: false, supportsQueryAnalytics: false, supportsQueryAttachments: false, supportsQueryTopFeatures: false, supportsResizeAttachments: false, supportsSync: false, supportsUpdate: false, supportsExceedsLimitStatistics: false }, queryRelated: { supportsCount: false, supportsOrderBy: false, supportsPagination: false, supportsCacheHint: false }, queryTopFeatures: { supportsCacheHint: false }, query: { maxRecordCount: 0, maxRecordCountFactor: 0, standardMaxRecordCount: 0, supportsCacheHint: false, supportsCentroid: false, supportsCompactGeometry: false, supportsDefaultSpatialReference: false, supportsFullTextSearch: false, supportsDisjointSpatialRelationship: false, supportsDistance: false, supportsDistinct: false, supportsExtent: false, supportsFormatPBF: false, supportsGeometryProperties: false, supportsHavingClause: false, supportsHistoricMoment: false, supportsMaxRecordCountFactor: false, supportsOrderBy: false, supportsPagination: false, supportsPercentileStatistics: false, supportsQuantization: false, supportsQuantizationEditMode: false, supportsQueryByOthers: false, supportsQueryGeometry: false, supportsResultType: false, supportsSqlExpression: false, supportsStandardizedQueriesOnly: false, supportsTopFeaturesQuery: false, supportsSpatialAggregationStatistics: false, supportedSpatialAggregationStatistics: { envelope: false, centroid: false, convexHull: false }, supportsStatistics: false, tileMaxRecordCount: 0 } };

// node_modules/@arcgis/core/views/3d/layers/i3s/I3SProjectionUtil.js
var s2 = 1;
var c = 5 - s2;

// node_modules/@arcgis/core/views/3d/support/orientedBoundingBox.js
var E3 = e3();
var F = n();
var H = n();
var J2 = n2();
var K = e();
function N(e7 = [0, 0, 0], t5 = [-1, -1, -1], a3 = e3()) {
  return { center: t2(e7), halfSize: t3(t5), quaternion: r3(a3) };
}
var _ = (() => {
  const e7 = new Int8Array(162);
  let t5 = 0;
  const a3 = (a4) => {
    for (let n6 = 0; n6 < a4.length; n6++) e7[t5 + n6] = a4[n6];
    t5 += 6;
  };
  return a3([6, 2, 3, 1, 5, 4]), a3([0, 2, 3, 1, 5, 4]), a3([0, 2, 3, 7, 5, 4]), a3([0, 1, 3, 2, 6, 4]), a3([0, 1, 3, 2, 0, 0]), a3([0, 1, 5, 7, 3, 2]), a3([0, 1, 3, 7, 6, 4]), a3([0, 1, 3, 7, 6, 2]), a3([0, 1, 5, 7, 6, 2]), a3([0, 1, 5, 4, 6, 2]), a3([0, 1, 5, 4, 0, 0]), a3([0, 1, 3, 7, 5, 4]), a3([0, 2, 6, 4, 0, 0]), a3([0, 0, 0, 0, 0, 0]), a3([1, 3, 7, 5, 0, 0]), a3([2, 3, 7, 6, 4, 0]), a3([2, 3, 7, 6, 0, 0]), a3([2, 3, 1, 5, 7, 6]), a3([0, 1, 5, 7, 6, 2]), a3([0, 1, 5, 7, 6, 4]), a3([0, 1, 3, 7, 6, 4]), a3([4, 5, 7, 6, 2, 0]), a3([4, 5, 7, 6, 0, 0]), a3([4, 5, 1, 3, 7, 6]), a3([0, 2, 3, 7, 5, 4]), a3([6, 2, 3, 7, 5, 4]), a3([6, 2, 3, 1, 5, 4]), e7;
})();
var re = n();
var se = n();
var ie = n();
var fe = e3();

// node_modules/@arcgis/core/views/3d/layers/i3s/I3SUtil.js
var ee = u2();
var re2;
function ue(t5, r6, n6, o4, a3) {
  const i = [];
  for (const e7 of r6) if (e7 && a3.includes(e7.name)) {
    const r7 = `${t5}/nodes/${n6}/attributes/${e7.key}/0`;
    i.push({ url: r7, storageInfo: e7 });
  }
  return E(i.map((t6) => U(t6.url, { responseType: "array-buffer" }).then((e7) => I(t6.storageInfo, e7.data)))).then((e7) => {
    const t6 = [];
    for (const r7 of o4) {
      const n7 = {};
      for (let t7 = 0; t7 < e7.length; t7++) {
        const o5 = e7[t7].value;
        null != o5 && (n7[i[t7].storageInfo.name] = me(o5, r7));
      }
      t6.push(n7);
    }
    return t6;
  });
}
!function(e7) {
  e7[e7.OUTSIDE = 0] = "OUTSIDE", e7[e7.INTERSECTS_CENTER_OUTSIDE = 1] = "INTERSECTS_CENTER_OUTSIDE", e7[e7.INTERSECTS_CENTER_INSIDE = 2] = "INTERSECTS_CENTER_INSIDE", e7[e7.INSIDE = 3] = "INSIDE";
}(re2 || (re2 = {}));
var fe2 = -32768;
var pe = -(2 ** 31);
function me(e7, t5) {
  if (!e7) return null;
  const r6 = e7[t5];
  if (u(e7)) return r6 === fe2 ? null : r6;
  if (a(e7)) return r6 === pe ? null : r6;
  return r6 != r6 ? null : r6;
}
var Ue = m({ color: [0, 0, 0, 0], opacity: 0 });
var Ae = new Array(24);
var De = new s(Ae, 3, true);
var Fe = n();
var _e = n();
var Oe = e();
var Je = e2();
var Ve = e4();
var Ye = u2();
var et = u2();
var tt = N();
var rt = n();
var nt = { data: new Array(72), size: 3, exclusive: true, stride: 3 };
var ot = e2();

// node_modules/@arcgis/core/layers/support/FetchAssociatedFeatureLayer.js
var l3 = class {
  constructor(t5, r6, e7, a3) {
    var _a;
    this._parsedUrl = t5, this._portalItem = r6, this._apiKey = e7, this.signal = a3, this._rootDocument = null;
    const i = (_a = this._parsedUrl) == null ? void 0 : _a.path.match(/^(.*)\/SceneServer\/layers\/([\d]*)\/?$/i);
    i && (this._urlParts = { root: i[1], layerId: parseInt(i[2], 10) });
  }
  async fetch() {
    if (!this._urlParts) return null;
    const t5 = this._portalItem ?? await this._portalItemFromServiceItemId();
    if (t(t5)) return this._loadFromUrl();
    const r6 = await this._findAndLoadRelatedPortalItem(t5);
    return t(r6) ? null : this._loadFeatureLayerFromPortalItem(r6);
  }
  async fetchPortalItem() {
    if (!this._urlParts) return null;
    const t5 = this._portalItem ?? await this._portalItemFromServiceItemId();
    return t(t5) ? null : this._findAndLoadRelatedPortalItem(t5);
  }
  async _fetchRootDocument() {
    if (r(this._rootDocument)) return this._rootDocument;
    if (t(this._urlParts)) return this._rootDocument = {}, {};
    const t5 = { query: { f: "json", token: this._apiKey }, responseType: "json", signal: this.signal }, i = `${this._urlParts.root}/SceneServer`;
    try {
      const e7 = await U(i, t5);
      this._rootDocument = e7.data;
    } catch {
      this._rootDocument = {};
    }
    return this._rootDocument;
  }
  async _fetchServiceOwningPortalUrl() {
    var _a, _b;
    const e7 = (_a = this._parsedUrl) == null ? void 0 : _a.path, a3 = e7 ? (_b = r2) == null ? void 0 : _b.findServerInfo(e7) : null;
    if (a3 == null ? void 0 : a3.owningSystemUrl) return a3.owningSystemUrl;
    const s5 = e7 ? e7.replace(/(.*\/rest)\/.*/i, "$1") + "/info" : null;
    try {
      const t5 = (await U(s5, { query: { f: "json" }, responseType: "json", signal: this.signal })).data.owningSystemUrl;
      if (t5) return t5;
    } catch (n6) {
      w(n6);
    }
    return null;
  }
  async _findAndLoadRelatedPortalItem(t5) {
    try {
      return (await t5.fetchRelatedItems({ relationshipType: "Service2Service", direction: "reverse" }, { signal: this.signal })).find((t6) => "Feature Service" === t6.type) || null;
    } catch (r6) {
      return w(r6), null;
    }
  }
  async _loadFeatureLayerFromPortalItem(t5) {
    await t5.load({ signal: this.signal });
    const r6 = await this._findMatchingAssociatedSublayerUrl(t5.url ?? "");
    return new He({ url: r6, portalItem: t5 }).load({ signal: this.signal });
  }
  async _loadFromUrl() {
    var _a;
    const t5 = await this._findMatchingAssociatedSublayerUrl(`${(_a = this._urlParts) == null ? void 0 : _a.root}/FeatureServer`);
    return new He({ url: t5 }).load({ signal: this.signal });
  }
  async _findMatchingAssociatedSublayerUrl(t5) {
    var _a;
    const e7 = t5.replace(/^(.*FeatureServer)(\/[\d]*\/?)?$/i, "$1"), a3 = { query: { f: "json" }, responseType: "json", authMode: "no-prompt", signal: this.signal }, i = (_a = this._urlParts) == null ? void 0 : _a.layerId, s5 = this._fetchRootDocument(), n6 = U(e7, a3), [o4, l4] = await Promise.all([n6, s5]), c4 = l4 && l4.layers, u5 = o4.data && o4.data.layers;
    if (!Array.isArray(u5)) throw new Error("expected layers array");
    if (Array.isArray(c4)) for (let r6 = 0; r6 < Math.min(c4.length, u5.length); r6++) {
      if (c4[r6].id === i) return `${e7}/${u5[r6].id}`;
    }
    else if (null != i && i < u5.length) return `${e7}/${u5[i].id}`;
    throw new Error("could not find matching associated sublayer");
  }
  async _portalItemFromServiceItemId() {
    const t5 = (await this._fetchRootDocument()).serviceItemId;
    if (!t5) return null;
    const r6 = new x({ id: t5, apiKey: this._apiKey }), e7 = await this._fetchServiceOwningPortalUrl();
    r(e7) && (r6.portal = new b({ url: e7 }));
    try {
      return r6.load({ signal: this.signal });
    } catch (s5) {
      return w(s5), null;
    }
  }
};

export {
  t4 as t,
  ue,
  l3 as l
};
//# sourceMappingURL=chunk-QLIMNVCT.js.map
