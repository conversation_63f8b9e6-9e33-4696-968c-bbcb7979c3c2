import {
  o
} from "./chunk-A2EXCZFA.js";
import {
  c as c3
} from "./chunk-EDV64J6E.js";
import {
  O as O2,
  W,
  ie,
  j,
  pe2 as pe,
  s,
  se
} from "./chunk-VXAO6YJP.js";
import {
  m
} from "./chunk-ANH6666P.js";
import {
  O,
  c as c2,
  r,
  t
} from "./chunk-J4YX6DLU.js";
import {
  p
} from "./chunk-BDKNA3OF.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  u
} from "./chunk-G5KX4JSG.js";
import {
  l
} from "./chunk-T23PB46T.js";
import {
  f
} from "./chunk-EKX3LLYN.js";
import {
  c
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/symbols/cim/CIMSymbolRasterizer.js
var M;
!function(e) {
  e.Legend = "legend", e.Preview = "preview";
}(M || (M = {}));
var _ = (e) => e && e.scaleFactor ? e.scaleFactor : 1;
var v = 96 / 72;
var z = class {
  constructor(e, t2) {
    this._spatialReference = e, this._avoidSDF = t2, this._resourceCache = /* @__PURE__ */ new Map(), this._imageDataCanvas = null, this._pictureMarkerCache = /* @__PURE__ */ new Map(), this._textRasterizer = new s(), this._cimResourceManager = new o(), this._rasterizer = new c3(this._cimResourceManager);
  }
  get resourceManager() {
    return this._cimResourceManager;
  }
  async rasterizeCIMSymbolAsync(e, t2, a, i, r2, s2, o2, n) {
    if (!e) return null;
    const { data: g } = e;
    if (!g || "CIMSymbolReference" !== g.type || !g.symbol) return null;
    const { symbol: u2 } = g;
    s2 || (s2 = O(u2));
    const d = await se.resolveSymbolOverrides(g, t2, this._spatialReference, r2, s2, o2, n);
    this._imageDataCanvas || (this._imageDataCanvas = document.createElement("canvas"));
    const p2 = this._imageDataCanvas, y = this._cimResourceManager, w = [];
    ie.fetchResources(d, y, w), w.length > 0 && await Promise.all(w);
    const { width: C, height: M2 } = a, _2 = I(s2, C, M2, i), z2 = ie.getEnvelope(d, _2, y);
    if (!z2) return null;
    const x2 = (window.devicePixelRatio || 1) * v;
    let R = 1, b = 0, P = 0;
    switch (u2.type) {
      case "CIMPointSymbol":
      case "CIMTextSymbol":
        {
          let e2 = 1;
          z2.width > C && (e2 = C / z2.width);
          let t3 = 1;
          z2.height > M2 && (t3 = M2 / z2.height), "preview" === i && (z2.width < C && (e2 = C / z2.width), z2.height < M2 && (t3 = M2 / z2.height)), R = Math.min(e2, t3), b = z2.x + z2.width / 2, P = z2.y + z2.height / 2;
        }
        break;
      case "CIMLineSymbol":
        {
          let e2 = 1;
          z2.height > M2 && (e2 = M2 / z2.height), R = e2, P = z2.y + z2.height / 2;
          const t3 = z2.x * R + C / 2, a2 = (z2.x + z2.width) * R + C / 2;
          if (t3 < 0) {
            const { paths: e3 } = _2;
            e3[0][0][0] -= t3;
          }
          if (a2 > C) {
            const { paths: e3 } = _2;
            e3[0][2][0] -= a2 - C;
          }
        }
        break;
      case "CIMPolygonSymbol": {
        b = z2.x + z2.width / 2, P = z2.y + z2.height / 2;
        const e2 = z2.x * R + C / 2, t3 = (z2.x + z2.width) * R + C / 2, a2 = z2.y * R + M2 / 2, i2 = (z2.y + z2.height) * R + M2 / 2, { rings: r3 } = _2;
        e2 < 0 && (r3[0][0][0] -= e2, r3[0][3][0] -= e2, r3[0][4][0] -= e2), a2 < 0 && (r3[0][0][1] += a2, r3[0][1][1] += a2, r3[0][4][1] += a2), t3 > C && (r3[0][1][0] -= t3 - C, r3[0][2][0] -= t3 - C), i2 > M2 && (r3[0][2][1] += i2 - M2, r3[0][3][1] += i2 - M2);
      }
    }
    p2.width = C * x2, p2.height = M2 * x2;
    const D = 1;
    p2.width += 2 * D, p2.height += 2 * D;
    const S = p2.getContext("2d"), k = O2.createIdentity();
    k.translate(-b, -P), k.scale(R * x2, -R * x2), k.translate(C * x2 / 2 + D, M2 * x2 / 2 + D), S.clearRect(0, 0, p2.width, p2.height);
    return new W(S, y, k, true).drawSymbol(d, _2), S.getImageData(0, 0, p2.width, p2.height);
  }
  async analyzeCIMSymbol(e, t2, a, r2, o2) {
    const n = [], c4 = t2 ? { geometryType: r2, spatialReference: this._spatialReference, fields: t2 } : null;
    let h;
    await j(e.data, c4, this._cimResourceManager, n, this._avoidSDF), f(o2);
    for (const i of n) "CIMPictureMarker" !== i.cim.type && "CIMPictureFill" !== i.cim.type && "CIMPictureStroke" !== i.cim.type || (h || (h = []), h.push(this._fetchPictureMarkerResource(i, o2))), a && "text" === i.type && "string" == typeof i.text && i.text.includes("[") && (i.text = c2(a, i.text, i.cim.textCase));
    return h && await Promise.all(h), n;
  }
  rasterizeCIMSymbol3D(e, t2, a, i, r2, s2) {
    const o2 = [];
    for (const n of e) {
      i && "function" == typeof i.scaleFactor && (i.scaleFactor = i.scaleFactor(t2, r2, s2));
      const e2 = this._getRasterizedResource(n, t2, a, i, r2, s2);
      if (!e2) continue;
      let c4 = 0, h = e2.anchorX || 0, l2 = e2.anchorY || 0, m2 = false, g = 0, u2 = 0;
      if ("esriGeometryPoint" === a) {
        const e3 = _(i);
        if (g = t(n.offsetX, t2, r2, s2) * e3 || 0, u2 = t(n.offsetY, t2, r2, s2) * e3 || 0, "marker" === n.type) c4 = t(n.rotation, t2, r2, s2) || 0, m2 = !!n.rotateClockwise && n.rotateClockwise;
        else if ("text" === n.type) {
          if (c4 = t(n.angle, t2, r2, s2) || 0, void 0 !== n.horizontalAlignment) switch (n.horizontalAlignment) {
            case "left":
              h = -0.5;
              break;
            case "right":
              h = 0.5;
              break;
            default:
              h = 0;
          }
          if (void 0 !== n.verticalAlignment) switch (n.verticalAlignment) {
            case "top":
              l2 = 0.5;
              break;
            case "bottom":
              l2 = -0.5;
              break;
            case "baseline":
              l2 = -0.25;
              break;
            default:
              l2 = 0;
          }
        }
      }
      null != e2 && o2.push({ angle: c4, rotateClockWise: m2, anchorX: h, anchorY: l2, offsetX: g, offsetY: u2, rasterizedResource: e2 });
    }
    return this.getSymbolImage(o2);
  }
  getSymbolImage(e) {
    const t2 = document.createElement("canvas"), i = c(t2.getContext("2d"));
    let s2 = 0, o2 = 0, n = 0, c4 = 0;
    const h = [];
    for (let a = 0; a < e.length; a++) {
      const t3 = e[a], l3 = t3.rasterizedResource;
      if (!l3) continue;
      const m3 = l3.size, g2 = t3.offsetX, u2 = t3.offsetY, f2 = t3.anchorX, d = t3.anchorY, p2 = t3.rotateClockWise || false;
      let y = t3.angle, w = u(g2) - m3[0] * (0.5 + f2), C = u(u2) - m3[1] * (0.5 + d), M2 = w + m3[0], _2 = C + m3[1];
      if (y) {
        p2 && (y = -y);
        const e2 = Math.sin(y * Math.PI / 180), t4 = Math.cos(y * Math.PI / 180), a2 = w * t4 - C * e2, i2 = w * e2 + C * t4, r2 = w * t4 - _2 * e2, s3 = w * e2 + _2 * t4, o3 = M2 * t4 - _2 * e2, n2 = M2 * e2 + _2 * t4, c5 = M2 * t4 - C * e2, h2 = M2 * e2 + C * t4;
        w = Math.min(a2, r2, o3, c5), C = Math.min(i2, s3, n2, h2), M2 = Math.max(a2, r2, o3, c5), _2 = Math.max(i2, s3, n2, h2);
      }
      s2 = w < s2 ? w : s2, o2 = C < o2 ? C : o2, n = M2 > n ? M2 : n, c4 = _2 > c4 ? _2 : c4;
      const v2 = i.createImageData(l3.size[0], l3.size[1]);
      v2.data.set(new Uint8ClampedArray(l3.image.buffer));
      const z2 = { offsetX: g2, offsetY: u2, rotateClockwise: p2, angle: y, rasterizedImage: v2, anchorX: f2, anchorY: d };
      h.push(z2);
    }
    t2.width = n - s2, t2.height = c4 - o2;
    const l2 = -s2, m2 = c4;
    for (let a = 0; a < h.length; a++) {
      const e2 = h[a], t3 = this._imageDataToCanvas(e2.rasterizedImage), s3 = e2.rasterizedImage.width, o3 = e2.rasterizedImage.height, n2 = l2 - s3 * (0.5 + e2.anchorX), c5 = m2 - o3 * (0.5 - e2.anchorY);
      if (e2.angle) {
        const a2 = (360 - e2.angle) * Math.PI / 180;
        i.save(), i.translate(u(e2.offsetX), -u(e2.offsetY)), i.translate(l2, m2), i.rotate(a2), i.translate(-l2, -m2), i.drawImage(t3, n2, c5), i.restore();
      } else i.drawImage(t3, n2 + u(e2.offsetX), c5 - u(e2.offsetY));
    }
    const g = new p({ x: l2 / t2.width - 0.5, y: m2 / t2.height - 0.5 });
    return { imageData: 0 !== t2.width && 0 !== t2.height ? i.getImageData(0, 0, t2.width, t2.height) : i.createImageData(1, 1), anchorPosition: g };
  }
  async _fetchPictureMarkerResource(e, a) {
    const i = e.materialHash;
    if (!this._pictureMarkerCache.get(i)) {
      const r2 = (await U(e.cim.url, { responseType: "image", signal: a && a.signal })).data;
      this._pictureMarkerCache.set(i, r2);
    }
  }
  _imageDataToCanvas(e) {
    this._imageDataCanvas || (this._imageDataCanvas = document.createElement("canvas"));
    const t2 = this._imageDataCanvas, i = c(t2.getContext("2d"));
    return t2.width = e.width, t2.height = e.height, i.putImageData(e, 0, 0), t2;
  }
  _imageTo32Array(t2, i, r2, s2) {
    this._imageDataCanvas || (this._imageDataCanvas = document.createElement("canvas"));
    const o2 = this._imageDataCanvas, n = c(o2.getContext("2d"));
    if (o2.width = i, o2.height = r2, n.drawImage(t2, 0, 0, i, r2), s2) {
      n.save();
      const a = new l(s2);
      n.fillStyle = a.toHex(), n.globalCompositeOperation = "multiply", n.fillRect(0, 0, i, r2), n.globalCompositeOperation = "destination-atop", n.drawImage(t2, 0, 0, i, r2), n.restore();
    }
    return new Uint32Array(n.getImageData(0, 0, i, r2).data.buffer);
  }
  _getRasterizedResource(e, t2, i, r2, s2, o2) {
    let n, c4, h;
    const l2 = null, m2 = null;
    if ("text" === e.type) return this._rasterizeTextResource(e, t2, r2, s2, o2);
    ({ analyzedCIM: n, hash: c4 } = x(e, t2, s2, o2));
    const g = _(r2);
    if ("CIMPictureMarker" === e.cim.type) {
      const i2 = t(e.size, t2, s2, o2) * g, { image: r3, width: n2, height: c5 } = c(this._getPictureResource(e, i2, t(e.color, t2, s2, o2)));
      return h = { image: r3, size: [n2, c5], sdf: false, simplePattern: false, anchorX: e.anchorPoint ? e.anchorPoint.x : 0, anchorY: e.anchorPoint ? e.anchorPoint.y : 0 }, h;
    }
    m(n, g, { preserveOutlineWidth: false });
    const u2 = n;
    c4 += i, r2 && (c4 += JSON.stringify(r2));
    const f2 = this._resourceCache;
    return f2.has(c4) ? f2.get(c4) : (h = this._rasterizer.rasterizeJSONResource({ cim: u2, type: e.type, url: e.url, mosaicHash: c4, size: l2, path: m2 }, window.devicePixelRatio || 1, this._avoidSDF), f2.set(c4, h), h);
  }
  _rasterizeTextResource(e, t2, a, i, r2) {
    const s2 = _(a), o2 = t(e.text, t2, i, r2);
    if (!o2 || 0 === o2.length) return null;
    const n = t(e.fontName, t2, i, r2), c4 = t(e.style, t2, i, r2), h = t(e.weight, t2, i, r2), l2 = t(e.decoration, t2, i, r2), m2 = t(e.size, t2, i, r2) * s2, g = t(e.horizontalAlignment, t2, i, r2), u2 = t(e.verticalAlignment, t2, i, r2), f2 = r(t(e.color, t2, i, r2)), d = r(t(e.outlineColor, t2, i, r2)), w = { color: f2, size: m2, horizontalAlignment: g, verticalAlignment: u2, font: { family: n, style: c4, weight: h, decoration: l2 }, halo: { size: t(e.outlineSize, t2, i, r2) || 0, color: d, style: c4 }, pixelRatio: 1, premultiplyColors: !this._avoidSDF };
    return this._textRasterizer.rasterizeText(o2, w);
  }
  _getPictureResource(e, t2, a) {
    const i = this._pictureMarkerCache.get(e.materialHash);
    if (!i) return null;
    const s2 = i.height / i.width, o2 = t2 ? s2 > 1 ? u(t2) : u(t2) / s2 : i.width, n = t2 ? s2 > 1 ? u(t2) * s2 : u(t2) : i.height;
    return { image: this._imageTo32Array(i, o2, n, a), width: o2, height: n };
  }
};
function I(e, t2, a, i) {
  const r2 = 1, s2 = -t2 / 2 + r2, o2 = t2 / 2 - r2, n = a / 2 - r2, c4 = -a / 2 + r2;
  switch (e) {
    case "esriGeometryPoint":
      return { x: 0, y: 0 };
    case "esriGeometryPolyline":
      return { paths: [[[s2, 0], [0, 0], [o2, 0]]] };
    default:
      return "legend" === i ? { rings: [[[s2, n], [o2, 0], [o2, c4], [s2, c4], [s2, n]]] } : { rings: [[[s2, n], [o2, n], [o2, c4], [s2, c4], [s2, n]]] };
  }
}
function x(e, t2, a, i) {
  let r2, s2;
  if ("function" == typeof e.materialHash) {
    r2 = (0, e.materialHash)(t2, a, i), s2 = pe(e.cim, e.materialOverrides);
  } else r2 = e.materialHash, s2 = e.cim;
  return { analyzedCIM: s2, hash: r2 };
}

export {
  M,
  z
};
//# sourceMappingURL=chunk-QFJ7EHPS.js.map
