{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/GraphicsLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as i}from\"../../../chunks/tslib.es6.js\";import t from\"../../../Graphic.js\";import e from\"../../../core/Collection.js\";import{destroyMaybe as s,isSome as h}from\"../../../core/maybe.js\";import{property as r}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import{LayerView2DMixin as o}from\"./LayerView2D.js\";import p from\"./graphics/GraphicContainer.js\";import g from\"./graphics/GraphicsView2D.js\";import c from\"../../layers/LayerView.js\";const l={remove(){},pause(){},resume(){}};let n=class extends(o(c)){constructor(){super(...arguments),this._highlightIds=new Map}attach(){this.graphicsView=new g({requestUpdateCallback:()=>this.requestUpdate(),view:this.view,graphics:this.layer.graphics,container:new p(this.view.featuresTilingScheme)}),this._updateHighlight(),this.container.addChild(this.graphicsView.container),this.addAttachHandles(this.layer.on(\"graphic-update\",this.graphicsView.graphicUpdateHandler))}detach(){this.container.removeAllChildren(),this.graphicsView=s(this.graphicsView)}async hitTest(i){return this.graphicsView?this.graphicsView.hitTest(i).map((t=>({type:\"graphic\",graphic:t,mapPoint:i,layer:this.layer}))):null}async fetchPopupFeatures(i){return this.graphicsView?this.graphicsView.hitTest(i).filter((i=>!!i.popupTemplate)):[]}queryGraphics(){return Promise.resolve(this.graphicsView.graphics)}update(i){this.graphicsView.processUpdate(i)}moveStart(){}viewChange(){this.graphicsView.viewChange()}moveEnd(){}isUpdating(){return!this.graphicsView||this.graphicsView.updating}highlight(i){let s;\"number\"==typeof i?s=[i]:i instanceof t?s=[i.uid]:Array.isArray(i)&&i.length>0?s=\"number\"==typeof i[0]?i:i.map((i=>i&&i.uid)):e.isCollection(i)&&i.length>0&&(s=i.map((i=>i&&i.uid)).toArray());const r=s?.filter(h);return r?.length?(this._addHighlight(r),{remove:()=>this._removeHighlight(r)}):l}_addHighlight(i){for(const t of i)if(this._highlightIds.has(t)){const i=this._highlightIds.get(t);this._highlightIds.set(t,i+1)}else this._highlightIds.set(t,1);this._updateHighlight()}_removeHighlight(i){for(const t of i)if(this._highlightIds.has(t)){const i=this._highlightIds.get(t)-1;0===i?this._highlightIds.delete(t):this._highlightIds.set(t,i)}this._updateHighlight()}_updateHighlight(){this.graphicsView?.setHighlight(Array.from(this._highlightIds.keys()))}};i([r()],n.prototype,\"graphicsView\",void 0),n=i([a(\"esri.views.2d.layers.GraphicsLayerView2D\")],n);const d=n;export{d as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgnB,IAAM,IAAE,EAAC,SAAQ;AAAC,GAAE,QAAO;AAAC,GAAE,SAAQ;AAAC,EAAC;AAAE,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,oBAAI;AAAA,EAAG;AAAA,EAAC,SAAQ;AAAC,SAAK,eAAa,IAAI,GAAE,EAAC,uBAAsB,MAAI,KAAK,cAAc,GAAE,MAAK,KAAK,MAAK,UAAS,KAAK,MAAM,UAAS,WAAU,IAAI,EAAE,KAAK,KAAK,oBAAoB,EAAC,CAAC,GAAE,KAAK,iBAAiB,GAAE,KAAK,UAAU,SAAS,KAAK,aAAa,SAAS,GAAE,KAAK,iBAAiB,KAAK,MAAM,GAAG,kBAAiB,KAAK,aAAa,oBAAoB,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,UAAU,kBAAkB,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,IAAE;AAAC,WAAO,KAAK,eAAa,KAAK,aAAa,QAAQA,EAAC,EAAE,IAAK,QAAI,EAAC,MAAK,WAAU,SAAQ,GAAE,UAASA,IAAE,OAAM,KAAK,MAAK,EAAG,IAAE;AAAA,EAAI;AAAA,EAAC,MAAM,mBAAmBA,IAAE;AAAC,WAAO,KAAK,eAAa,KAAK,aAAa,QAAQA,EAAC,EAAE,OAAQ,CAAAA,OAAG,CAAC,CAACA,GAAE,aAAc,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,QAAQ,QAAQ,KAAK,aAAa,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,aAAa,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,aAAa,WAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAM,CAAC,KAAK,gBAAc,KAAK,aAAa;AAAA,EAAQ;AAAA,EAAC,UAAUA,IAAE;AAAC,QAAI;AAAE,gBAAU,OAAOA,KAAE,IAAE,CAACA,EAAC,IAAEA,cAAa,IAAE,IAAE,CAACA,GAAE,GAAG,IAAE,MAAM,QAAQA,EAAC,KAAGA,GAAE,SAAO,IAAE,IAAE,YAAU,OAAOA,GAAE,CAAC,IAAEA,KAAEA,GAAE,IAAK,CAAAA,OAAGA,MAAGA,GAAE,GAAI,IAAE,EAAE,aAAaA,EAAC,KAAGA,GAAE,SAAO,MAAI,IAAEA,GAAE,IAAK,CAAAA,OAAGA,MAAGA,GAAE,GAAI,EAAE,QAAQ;AAAG,UAAMC,KAAE,uBAAG,OAAO;AAAG,YAAOA,MAAA,gBAAAA,GAAG,WAAQ,KAAK,cAAcA,EAAC,GAAE,EAAC,QAAO,MAAI,KAAK,iBAAiBA,EAAC,EAAC,KAAG;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAE;AAAC,eAAU,KAAKA,GAAE,KAAG,KAAK,cAAc,IAAI,CAAC,GAAE;AAAC,YAAMA,KAAE,KAAK,cAAc,IAAI,CAAC;AAAE,WAAK,cAAc,IAAI,GAAEA,KAAE,CAAC;AAAA,IAAC,MAAM,MAAK,cAAc,IAAI,GAAE,CAAC;AAAE,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,eAAU,KAAKA,GAAE,KAAG,KAAK,cAAc,IAAI,CAAC,GAAE;AAAC,YAAMA,KAAE,KAAK,cAAc,IAAI,CAAC,IAAE;AAAE,YAAIA,KAAE,KAAK,cAAc,OAAO,CAAC,IAAE,KAAK,cAAc,IAAI,GAAEA,EAAC;AAAA,IAAC;AAAC,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAJz1E;AAI01E,eAAK,iBAAL,mBAAmB,aAAa,MAAM,KAAK,KAAK,cAAc,KAAK,CAAC;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,IAAE,EAAE,CAACE,GAAE,0CAA0C,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["i", "r", "a"]}