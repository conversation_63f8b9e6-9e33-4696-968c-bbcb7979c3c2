{"version": 3, "sources": ["../../@arcgis/core/geometry/support/normalizeUtilsSync.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../core/maybe.js\";import{create as i}from\"./aaBoundingRect.js\";import{getBoundsXY as s}from\"./boundsUtils.js\";import{closeRings as n}from\"./coordsUtils.js\";import{isPoint as e,isMultipoint as h,isExtent as o,isPolygon as r,isPolyline as u}from\"./jsonUtils.js\";import{cutParams as m,updatePolyGeometry as x,offsetMagnitude as _}from\"./normalizeUtilsCommon.js\";import{getInfo as l,isWebMercator as c}from\"./spatialReferenceUtils.js\";function a(t){return f(t,!0)}function p(t){return f(t,!1)}function f(i,n){if(t(i))return null;const a=i.spatialReference,p=l(a),f=\"toJSON\"in i?i.toJSON():i;if(!p)return f;const I=c(a)?102100:4326,g=m[I].maxX,v=m[I].minX;if(e(f))return y(f,g,v);if(h(f))return f.points=f.points.map((t=>y(t,g,v))),f;if(o(f))return d(f,p);if(r(f)||u(f)){const t=s(S,f),i={xmin:t[0],ymin:t[1],xmax:t[2],ymax:t[3]},e=_(i.xmin,v)*(2*g),h=0===e?f:x(f,e);return i.xmin+=e,i.xmax+=e,i.xmax>g?P(h,g,n):i.xmin<v?P(h,v,n):h}return f}function d(t,i){if(!i)return t;const s=I(t,i).map((t=>t.extent));return s.length<2?s[0]||t:s.length>2?(t.xmin=i.valid[0],t.xmax=i.valid[1],t):{rings:s.map((t=>[[t.xmin,t.ymin],[t.xmin,t.ymax],[t.xmax,t.ymax],[t.xmax,t.ymin],[t.xmin,t.ymin]]))}}function y(t,i,s){if(Array.isArray(t)){const n=t[0];if(n>i){const s=_(n,i);t[0]=n+s*(-2*i)}else if(n<s){const i=_(n,s);t[0]=n+i*(-2*s)}}else{const n=t.x;if(n>i){const s=_(n,i);t.x+=s*(-2*i)}else if(n<s){const i=_(n,s);t.x+=i*(-2*s)}}return t}function I(t,i){const s=[],{ymin:n,ymax:e,xmin:h,xmax:o}=t,r=t.xmax-t.xmin,[u,m]=i.valid,{x,frameId:_}=g(t.xmin,i),{x:l,frameId:c}=g(t.xmax,i),a=x===l&&r>0;if(r>2*m){const t={xmin:h<o?x:l,ymin:n,xmax:m,ymax:e},i={xmin:u,ymin:n,xmax:h<o?l:x,ymax:e},r={xmin:0,ymin:n,xmax:m,ymax:e},a={xmin:u,ymin:n,xmax:0,ymax:e},p=[],f=[];v(t,r)&&p.push(_),v(t,a)&&f.push(_),v(i,r)&&p.push(c),v(i,a)&&f.push(c);for(let s=_+1;s<c;s++)p.push(s),f.push(s);s.push(new C(t,[_]),new C(i,[c]),new C(r,p),new C(a,f))}else x>l||a?s.push(new C({xmin:x,ymin:n,xmax:m,ymax:e},[_]),new C({xmin:u,ymin:n,xmax:l,ymax:e},[c])):s.push(new C({xmin:x,ymin:n,xmax:l,ymax:e},[_]));return s}function g(t,i){const[s,n]=i.valid,e=2*n;let h,o=0;return t>n?(h=Math.ceil(Math.abs(t-n)/e),t-=h*e,o=h):t<s&&(h=Math.ceil(Math.abs(t-s)/e),t+=h*e,o=-h),{x:t,frameId:o}}function v(t,i){const{xmin:s,ymin:n,xmax:e,ymax:h}=i;return O(t,s,n)&&O(t,s,h)&&O(t,e,h)&&O(t,e,n)}function O(t,i,s){return i>=t.xmin&&i<=t.xmax&&s>=t.ymin&&s<=t.ymax}function P(t,i,s=!0){const e=!u(t);if(e&&n(t),s){return(new T).cut(t,i)}const h=e?t.rings:t.paths,o=e?4:2,r=h.length,m=-2*i;for(let n=0;n<r;n++){const t=h[n];if(t&&t.length>=o){const i=[];for(const s of t)i.push([s[0]+m,s[1]]);h.push(i)}}return e?t.rings=h:t.paths=h,t}class C{constructor(t,i){this.extent=t,this.frameIds=i}}const S=i();class T{constructor(){this._linesIn=[],this._linesOut=[]}cut(t,i){let s;if(this._xCut=i,t.rings)this._closed=!0,s=t.rings,this._minPts=4;else{if(!t.paths)return null;this._closed=!1,s=t.paths,this._minPts=2}for(const e of s){if(!e||e.length<this._minPts)continue;let t=!0;for(const i of e)t?(this.moveTo(i),t=!1):this.lineTo(i);this._closed&&this.close()}this._pushLineIn(),this._pushLineOut(),s=[];for(const e of this._linesIn)e&&e.length>=this._minPts&&s.push(e);const n=-2*this._xCut;for(const e of this._linesOut)if(e&&e.length>=this._minPts){for(const t of e)t[0]+=n;s.push(e)}return this._closed?t.rings=s:t.paths=s,t}moveTo(t){this._pushLineIn(),this._pushLineOut(),this._prevSide=this._side(t[0]),this._moveTo(t[0],t[1],this._prevSide),this._prevPt=t,this._firstPt=t}lineTo(t){const i=this._side(t[0]);if(i*this._prevSide==-1){const s=this._intersect(this._prevPt,t);this._lineTo(this._xCut,s,0),this._prevSide=0,this._lineTo(t[0],t[1],i)}else this._lineTo(t[0],t[1],i);this._prevSide=i,this._prevPt=t}close(){const t=this._firstPt,i=this._prevPt;t[0]===i[0]&&t[1]===i[1]||this.lineTo(t),this._checkClosingPt(this._lineIn),this._checkClosingPt(this._lineOut)}_moveTo(t,i,s){this._closed?(this._lineIn.push([s<=0?t:this._xCut,i]),this._lineOut.push([s>=0?t:this._xCut,i])):(s<=0&&this._lineIn.push([t,i]),s>=0&&this._lineOut.push([t,i]))}_lineTo(t,i,s){this._closed?(this._addPolyVertex(this._lineIn,s<=0?t:this._xCut,i),this._addPolyVertex(this._lineOut,s>=0?t:this._xCut,i)):s<0?(0===this._prevSide&&this._pushLineOut(),this._lineIn.push([t,i])):s>0?(0===this._prevSide&&this._pushLineIn(),this._lineOut.push([t,i])):this._prevSide<0?(this._lineIn.push([t,i]),this._lineOut.push([t,i])):this._prevSide>0&&(this._lineOut.push([t,i]),this._lineIn.push([t,i]))}_addPolyVertex(t,i,s){const n=t.length;n>1&&t[n-1][0]===i&&t[n-2][0]===i?t[n-1][1]=s:t.push([i,s])}_checkClosingPt(t){const i=t.length;i>3&&t[0][0]===this._xCut&&t[i-2][0]===this._xCut&&t[1][0]===this._xCut&&(t[0][1]=t[i-2][1],t.pop())}_side(t){return t<this._xCut?-1:t>this._xCut?1:0}_intersect(t,i){const s=(this._xCut-t[0])/(i[0]-t[0]);return t[1]+s*(i[1]-t[1])}_pushLineIn(){this._lineIn&&this._lineIn.length>=this._minPts&&this._linesIn.push(this._lineIn),this._lineIn=[]}_pushLineOut(){this._lineOut&&this._lineOut.length>=this._minPts&&this._linesOut.push(this._lineOut),this._lineOut=[]}}export{T as CutVertical,p as normalizeCentralMeridianForDisplay,a as normalizeCentralMeridianSync};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI0c,SAAS,EAAEA,IAAE;AAAC,SAAOC,GAAED,IAAE,IAAE;AAAC;AAAC,SAASE,GAAEF,IAAE;AAAC,SAAOC,GAAED,IAAE,KAAE;AAAC;AAAC,SAASC,GAAEE,IAAE,GAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAK,QAAMC,KAAED,GAAE,kBAAiBD,KAAE,EAAEE,EAAC,GAAEH,KAAE,YAAWE,KAAEA,GAAE,OAAO,IAAEA;AAAE,MAAG,CAACD,GAAE,QAAOD;AAAE,QAAMI,KAAE,EAAED,EAAC,IAAE,SAAO,MAAKE,KAAE,EAAED,EAAC,EAAE,MAAKE,KAAE,EAAEF,EAAC,EAAE;AAAK,MAAG,EAAEJ,EAAC,EAAE,QAAOO,GAAEP,IAAEK,IAAEC,EAAC;AAAE,MAAG,EAAEN,EAAC,EAAE,QAAOA,GAAE,SAAOA,GAAE,OAAO,IAAK,CAAAD,OAAGQ,GAAER,IAAEM,IAAEC,EAAC,CAAE,GAAEN;AAAE,MAAG,EAAEA,EAAC,EAAE,QAAO,EAAEA,IAAEC,EAAC;AAAE,MAAG,EAAED,EAAC,KAAG,EAAEA,EAAC,GAAE;AAAC,UAAMD,KAAE,EAAE,GAAEC,EAAC,GAAEE,KAAE,EAAC,MAAKH,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,GAAE,MAAKA,GAAE,CAAC,EAAC,GAAE,IAAE,EAAEG,GAAE,MAAKI,EAAC,KAAG,IAAED,KAAG,IAAE,MAAI,IAAEL,KAAEQ,GAAER,IAAE,CAAC;AAAE,WAAOE,GAAE,QAAM,GAAEA,GAAE,QAAM,GAAEA,GAAE,OAAKG,KAAE,EAAE,GAAEA,IAAE,CAAC,IAAEH,GAAE,OAAKI,KAAE,EAAE,GAAEA,IAAE,CAAC,IAAE;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAAS,EAAED,IAAEG,IAAE;AAAC,MAAG,CAACA,GAAE,QAAOH;AAAE,QAAMS,KAAE,EAAET,IAAEG,EAAC,EAAE,IAAK,CAAAH,OAAGA,GAAE,MAAO;AAAE,SAAOS,GAAE,SAAO,IAAEA,GAAE,CAAC,KAAGT,KAAES,GAAE,SAAO,KAAGT,GAAE,OAAKG,GAAE,MAAM,CAAC,GAAEH,GAAE,OAAKG,GAAE,MAAM,CAAC,GAAEH,MAAG,EAAC,OAAMS,GAAE,IAAK,CAAAT,OAAG,CAAC,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,GAAE,CAACA,GAAE,MAAKA,GAAE,IAAI,CAAC,CAAE,EAAC;AAAC;AAAC,SAASQ,GAAER,IAAEG,IAAEM,IAAE;AAAC,MAAG,MAAM,QAAQT,EAAC,GAAE;AAAC,UAAM,IAAEA,GAAE,CAAC;AAAE,QAAG,IAAEG,IAAE;AAAC,YAAMM,KAAE,EAAE,GAAEN,EAAC;AAAE,MAAAH,GAAE,CAAC,IAAE,IAAES,MAAG,KAAGN;AAAA,IAAE,WAAS,IAAEM,IAAE;AAAC,YAAMN,KAAE,EAAE,GAAEM,EAAC;AAAE,MAAAT,GAAE,CAAC,IAAE,IAAEG,MAAG,KAAGM;AAAA,IAAE;AAAA,EAAC,OAAK;AAAC,UAAM,IAAET,GAAE;AAAE,QAAG,IAAEG,IAAE;AAAC,YAAMM,KAAE,EAAE,GAAEN,EAAC;AAAE,MAAAH,GAAE,KAAGS,MAAG,KAAGN;AAAA,IAAE,WAAS,IAAEM,IAAE;AAAC,YAAMN,KAAE,EAAE,GAAEM,EAAC;AAAE,MAAAT,GAAE,KAAGG,MAAG,KAAGM;AAAA,IAAE;AAAA,EAAC;AAAC,SAAOT;AAAC;AAAC,SAAS,EAAEA,IAAEG,IAAE;AAAC,QAAMM,KAAE,CAAC,GAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,IAAET,IAAEU,KAAEV,GAAE,OAAKA,GAAE,MAAK,CAACW,IAAE,CAAC,IAAER,GAAE,OAAM,EAAC,GAAE,SAAQ,EAAC,IAAE,EAAEH,GAAE,MAAKG,EAAC,GAAE,EAAC,GAAES,IAAE,SAAQC,GAAC,IAAE,EAAEb,GAAE,MAAKG,EAAC,GAAEC,KAAE,MAAIQ,MAAGF,KAAE;AAAE,MAAGA,KAAE,IAAE,GAAE;AAAC,UAAMV,KAAE,EAAC,MAAK,IAAE,IAAE,IAAEY,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,GAAET,KAAE,EAAC,MAAKQ,IAAE,MAAK,GAAE,MAAK,IAAE,IAAEC,KAAE,GAAE,MAAK,EAAC,GAAEF,KAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,GAAEN,KAAE,EAAC,MAAKO,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,GAAET,KAAE,CAAC,GAAED,KAAE,CAAC;AAAE,MAAED,IAAEU,EAAC,KAAGR,GAAE,KAAK,CAAC,GAAE,EAAEF,IAAEI,EAAC,KAAGH,GAAE,KAAK,CAAC,GAAE,EAAEE,IAAEO,EAAC,KAAGR,GAAE,KAAKW,EAAC,GAAE,EAAEV,IAAEC,EAAC,KAAGH,GAAE,KAAKY,EAAC;AAAE,aAAQJ,KAAE,IAAE,GAAEA,KAAEI,IAAEJ,KAAI,CAAAP,GAAE,KAAKO,EAAC,GAAER,GAAE,KAAKQ,EAAC;AAAE,IAAAA,GAAE,KAAK,IAAI,EAAET,IAAE,CAAC,CAAC,CAAC,GAAE,IAAI,EAAEG,IAAE,CAACU,EAAC,CAAC,GAAE,IAAI,EAAEH,IAAER,EAAC,GAAE,IAAI,EAAEE,IAAEH,EAAC,CAAC;AAAA,EAAC,MAAM,KAAEW,MAAGR,KAAEK,GAAE,KAAK,IAAI,EAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAI,EAAE,EAAC,MAAKE,IAAE,MAAK,GAAE,MAAKC,IAAE,MAAK,EAAC,GAAE,CAACC,EAAC,CAAC,CAAC,IAAEJ,GAAE,KAAK,IAAI,EAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAKG,IAAE,MAAK,EAAC,GAAE,CAAC,CAAC,CAAC,CAAC;AAAE,SAAOH;AAAC;AAAC,SAAS,EAAET,IAAEG,IAAE;AAAC,QAAK,CAACM,IAAE,CAAC,IAAEN,GAAE,OAAM,IAAE,IAAE;AAAE,MAAI,GAAE,IAAE;AAAE,SAAOH,KAAE,KAAG,IAAE,KAAK,KAAK,KAAK,IAAIA,KAAE,CAAC,IAAE,CAAC,GAAEA,MAAG,IAAE,GAAE,IAAE,KAAGA,KAAES,OAAI,IAAE,KAAK,KAAK,KAAK,IAAIT,KAAES,EAAC,IAAE,CAAC,GAAET,MAAG,IAAE,GAAE,IAAE,CAAC,IAAG,EAAC,GAAEA,IAAE,SAAQ,EAAC;AAAC;AAAC,SAAS,EAAEA,IAAEG,IAAE;AAAC,QAAK,EAAC,MAAKM,IAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC,IAAEN;AAAE,SAAO,EAAEH,IAAES,IAAE,CAAC,KAAG,EAAET,IAAES,IAAE,CAAC,KAAG,EAAET,IAAE,GAAE,CAAC,KAAG,EAAEA,IAAE,GAAE,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEG,IAAEM,IAAE;AAAC,SAAON,MAAGH,GAAE,QAAMG,MAAGH,GAAE,QAAMS,MAAGT,GAAE,QAAMS,MAAGT,GAAE;AAAI;AAAC,SAAS,EAAEA,IAAEG,IAAEM,KAAE,MAAG;AAAC,QAAM,IAAE,CAAC,EAAET,EAAC;AAAE,MAAG,KAAG,EAAEA,EAAC,GAAES,IAAE;AAAC,WAAO,IAAI,IAAG,IAAIT,IAAEG,EAAC;AAAA,EAAC;AAAC,QAAM,IAAE,IAAEH,GAAE,QAAMA,GAAE,OAAM,IAAE,IAAE,IAAE,GAAEU,KAAE,EAAE,QAAO,IAAE,KAAGP;AAAE,WAAQ,IAAE,GAAE,IAAEO,IAAE,KAAI;AAAC,UAAMV,KAAE,EAAE,CAAC;AAAE,QAAGA,MAAGA,GAAE,UAAQ,GAAE;AAAC,YAAMG,KAAE,CAAC;AAAE,iBAAUM,MAAKT,GAAE,CAAAG,GAAE,KAAK,CAACM,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,CAAC,CAAC;AAAE,QAAE,KAAKN,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,IAAEH,GAAE,QAAM,IAAEA,GAAE,QAAM,GAAEA;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEG,IAAE;AAAC,SAAK,SAAOH,IAAE,KAAK,WAASG;AAAA,EAAC;AAAC;AAAC,IAAM,IAAEQ,GAAE;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,CAAC,GAAE,KAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIX,IAAEG,IAAE;AAAC,QAAIM;AAAE,QAAG,KAAK,QAAMN,IAAEH,GAAE,MAAM,MAAK,UAAQ,MAAGS,KAAET,GAAE,OAAM,KAAK,UAAQ;AAAA,SAAM;AAAC,UAAG,CAACA,GAAE,MAAM,QAAO;AAAK,WAAK,UAAQ,OAAGS,KAAET,GAAE,OAAM,KAAK,UAAQ;AAAA,IAAC;AAAC,eAAU,KAAKS,IAAE;AAAC,UAAG,CAAC,KAAG,EAAE,SAAO,KAAK,QAAQ;AAAS,UAAIT,KAAE;AAAG,iBAAUG,MAAK,EAAE,CAAAH,MAAG,KAAK,OAAOG,EAAC,GAAEH,KAAE,SAAI,KAAK,OAAOG,EAAC;AAAE,WAAK,WAAS,KAAK,MAAM;AAAA,IAAC;AAAC,SAAK,YAAY,GAAE,KAAK,aAAa,GAAEM,KAAE,CAAC;AAAE,eAAU,KAAK,KAAK,SAAS,MAAG,EAAE,UAAQ,KAAK,WAASA,GAAE,KAAK,CAAC;AAAE,UAAM,IAAE,KAAG,KAAK;AAAM,eAAU,KAAK,KAAK,UAAU,KAAG,KAAG,EAAE,UAAQ,KAAK,SAAQ;AAAC,iBAAUT,MAAK,EAAE,CAAAA,GAAE,CAAC,KAAG;AAAE,MAAAS,GAAE,KAAK,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,UAAQT,GAAE,QAAMS,KAAET,GAAE,QAAMS,IAAET;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,YAAY,GAAE,KAAK,aAAa,GAAE,KAAK,YAAU,KAAK,MAAMA,GAAE,CAAC,CAAC,GAAE,KAAK,QAAQA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,KAAK,SAAS,GAAE,KAAK,UAAQA,IAAE,KAAK,WAASA;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMG,KAAE,KAAK,MAAMH,GAAE,CAAC,CAAC;AAAE,QAAGG,KAAE,KAAK,aAAW,IAAG;AAAC,YAAMM,KAAE,KAAK,WAAW,KAAK,SAAQT,EAAC;AAAE,WAAK,QAAQ,KAAK,OAAMS,IAAE,CAAC,GAAE,KAAK,YAAU,GAAE,KAAK,QAAQT,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,EAAC;AAAA,IAAC,MAAM,MAAK,QAAQH,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEG,EAAC;AAAE,SAAK,YAAUA,IAAE,KAAK,UAAQH;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK,UAASG,KAAE,KAAK;AAAQ,IAAAH,GAAE,CAAC,MAAIG,GAAE,CAAC,KAAGH,GAAE,CAAC,MAAIG,GAAE,CAAC,KAAG,KAAK,OAAOH,EAAC,GAAE,KAAK,gBAAgB,KAAK,OAAO,GAAE,KAAK,gBAAgB,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEG,IAAEM,IAAE;AAAC,SAAK,WAAS,KAAK,QAAQ,KAAK,CAACA,MAAG,IAAET,KAAE,KAAK,OAAMG,EAAC,CAAC,GAAE,KAAK,SAAS,KAAK,CAACM,MAAG,IAAET,KAAE,KAAK,OAAMG,EAAC,CAAC,MAAIM,MAAG,KAAG,KAAK,QAAQ,KAAK,CAACT,IAAEG,EAAC,CAAC,GAAEM,MAAG,KAAG,KAAK,SAAS,KAAK,CAACT,IAAEG,EAAC,CAAC;AAAA,EAAE;AAAA,EAAC,QAAQH,IAAEG,IAAEM,IAAE;AAAC,SAAK,WAAS,KAAK,eAAe,KAAK,SAAQA,MAAG,IAAET,KAAE,KAAK,OAAMG,EAAC,GAAE,KAAK,eAAe,KAAK,UAASM,MAAG,IAAET,KAAE,KAAK,OAAMG,EAAC,KAAGM,KAAE,KAAG,MAAI,KAAK,aAAW,KAAK,aAAa,GAAE,KAAK,QAAQ,KAAK,CAACT,IAAEG,EAAC,CAAC,KAAGM,KAAE,KAAG,MAAI,KAAK,aAAW,KAAK,YAAY,GAAE,KAAK,SAAS,KAAK,CAACT,IAAEG,EAAC,CAAC,KAAG,KAAK,YAAU,KAAG,KAAK,QAAQ,KAAK,CAACH,IAAEG,EAAC,CAAC,GAAE,KAAK,SAAS,KAAK,CAACH,IAAEG,EAAC,CAAC,KAAG,KAAK,YAAU,MAAI,KAAK,SAAS,KAAK,CAACH,IAAEG,EAAC,CAAC,GAAE,KAAK,QAAQ,KAAK,CAACH,IAAEG,EAAC,CAAC;AAAA,EAAE;AAAA,EAAC,eAAeH,IAAEG,IAAEM,IAAE;AAAC,UAAM,IAAET,GAAE;AAAO,QAAE,KAAGA,GAAE,IAAE,CAAC,EAAE,CAAC,MAAIG,MAAGH,GAAE,IAAE,CAAC,EAAE,CAAC,MAAIG,KAAEH,GAAE,IAAE,CAAC,EAAE,CAAC,IAAES,KAAET,GAAE,KAAK,CAACG,IAAEM,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBT,IAAE;AAAC,UAAMG,KAAEH,GAAE;AAAO,IAAAG,KAAE,KAAGH,GAAE,CAAC,EAAE,CAAC,MAAI,KAAK,SAAOA,GAAEG,KAAE,CAAC,EAAE,CAAC,MAAI,KAAK,SAAOH,GAAE,CAAC,EAAE,CAAC,MAAI,KAAK,UAAQA,GAAE,CAAC,EAAE,CAAC,IAAEA,GAAEG,KAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,IAAI;AAAA,EAAE;AAAA,EAAC,MAAMA,IAAE;AAAC,WAAOA,KAAE,KAAK,QAAM,KAAGA,KAAE,KAAK,QAAM,IAAE;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAEG,IAAE;AAAC,UAAMM,MAAG,KAAK,QAAMT,GAAE,CAAC,MAAIG,GAAE,CAAC,IAAEH,GAAE,CAAC;AAAG,WAAOA,GAAE,CAAC,IAAES,MAAGN,GAAE,CAAC,IAAEH,GAAE,CAAC;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,KAAK,QAAQ,UAAQ,KAAK,WAAS,KAAK,SAAS,KAAK,KAAK,OAAO,GAAE,KAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,YAAU,KAAK,SAAS,UAAQ,KAAK,WAAS,KAAK,UAAU,KAAK,KAAK,QAAQ,GAAE,KAAK,WAAS,CAAC;AAAA,EAAC;AAAC;", "names": ["t", "f", "p", "i", "a", "I", "g", "v", "y", "s", "r", "u", "l", "c"]}