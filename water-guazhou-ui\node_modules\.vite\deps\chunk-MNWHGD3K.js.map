{"version": 3, "sources": ["../../@arcgis/core/core/workers/utils.js", "../../@arcgis/core/core/workers/RemoteClient.js", "../../@arcgis/core/core/workers/Connection.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../has.js\";var t;!function(t){t[t.HANDSHAKE=0]=\"HANDSHAKE\",t[t.OPEN=1]=\"OPEN\",t[t.OPENED=2]=\"OPENED\",t[t.RESPONSE=3]=\"RESPONSE\",t[t.INVOKE=4]=\"INVOKE\",t[t.ABORT=5]=\"ABORT\",t[t.CLOSE=6]=\"CLOSE\",t[t.OPEN_PORT=7]=\"OPEN_PORT\",t[t.ON=8]=\"ON\"}(t||(t={}));let e=0;function r(){return e++}function n(t){return t&&\"object\"==typeof t&&(\"result\"in t||\"transferList\"in t)}function s(t){return t?\"string\"==typeof t?JSON.stringify({name:\"message\",message:t}):t.toJSON?JSON.stringify(t):JSON.stringify({name:t.name,message:t.message,details:t.details||{stack:t.stack}}):null}function o(e,r,f,a){if(r.type===t.OPEN_PORT)return void e.postMessage(r,[r.port]);if(r.type!==t.INVOKE&&r.type!==t.RESPONSE)return void e.postMessage(r);let u;if(n(f)?(u=i(f.transferList),r.data=f.result):(u=i(a),r.data=f),u){if(has(\"ff\"))for(const n of u)if(\"byteLength\"in n&&n.byteLength>267386880){const n=\"Worker call with large ArrayBuffer would crash Firefox\";switch(r.type){case t.INVOKE:throw n;case t.RESPONSE:return void o(e,{type:t.RESPONSE,jobId:r.jobId,error:s(n)})}}e.postMessage(r,u)}else e.postMessage(r)}function f(t){if(!t)return null;const e=t.data;return e?\"string\"==typeof e?JSON.parse(e):e:null}function i(t){if(!t||!t.length)return null;if(has(\"esri-workers-arraybuffer-transfer\"))return t;const e=t.filter((t=>!a(t)));return e.length?e:null}function a(t){return t instanceof ArrayBuffer||t&&t.constructor&&\"ArrayBuffer\"===t.constructor.name}export{t as MessageType,n as isTranferableResult,r as newJobId,o as postMessage,f as receiveMessage,s as toInvokeError};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{version as e}from\"../../kernel.js\";import s from\"../Error.js\";import{on as t}from\"../events.js\";import{removeMaybe as o,isSome as r}from\"../maybe.js\";import{isAborted as i,createAbortError as n,onAbort as a,isPromiseLike as c,isAbortError as l}from\"../promiseUtils.js\";import{newJobId as p,MessageType as h,receiveMessage as u,toInvokeError as _,postMessage as d}from\"./utils.js\";import{commitHash as g,buildDate as m}from\"../../support/revision.js\";const k={statsWorker:()=>import(\"../../smartMapping/statistics/support/statsWorker.js\"),geometryEngineWorker:()=>import(\"../../geometry/geometryEngineWorker.js\"),CSVSourceWorker:()=>import(\"../../layers/graphics/sources/support/CSVSourceWorker.js\"),EdgeProcessingWorker:()=>import(\"../../views/3d/webgl-engine/lib/edgeRendering/EdgeProcessingWorker.js\"),ElevationSamplerWorker:()=>import(\"../../geometry/support/meshUtils/ElevationSamplerWorker.js\"),FeatureServiceSnappingSourceWorker:()=>import(\"../../views/interactive/snapping/featureSources/featureServiceSource/FeatureServiceSnappingSourceWorker.js\"),GeoJSONSourceWorker:()=>import(\"../../layers/graphics/sources/geojson/GeoJSONSourceWorker.js\"),LercWorker:()=>import(\"../../layers/support/LercWorker.js\"),MemorySourceWorker:()=>import(\"../../layers/graphics/sources/support/MemorySourceWorker.js\"),PBFDecoderWorker:()=>import(\"../../views/3d/support/PBFDecoderWorker.js\"),Pipeline:()=>import(\"../../views/2d/layers/features/Pipeline.js\"),PointCloudWorker:()=>import(\"../../views/3d/layers/PointCloudWorker.js\"),RasterWorker:()=>import(\"../../layers/support/RasterWorker.js\"),SceneLayerSnappingSourceWorker:()=>import(\"../../views/interactive/snapping/featureSources/sceneLayerSource/SceneLayerSnappingSourceWorker.js\"),SceneLayerWorker:()=>import(\"../../views/3d/layers/SceneLayerWorker.js\"),WFSSourceWorker:()=>import(\"../../layers/graphics/sources/WFSSourceWorker.js\"),WorkerTileHandler:()=>import(\"../../views/2d/engine/vectorTiles/WorkerTileHandler.js\")},{CLOSE:b,ABORT:v,INVOKE:y,RESPONSE:j,OPEN_PORT:S,ON:f}=h,W=2;class M{constructor(e){this._timer=null,this._cancelledJobIds=new Set,this._invokeMessages=[],this._invoke=e,this._timer=null,this._process=this._process.bind(this)}push(e){e.type===h.ABORT?this._cancelledJobIds.add(e.jobId):(this._invokeMessages.push(e),null===this._timer&&(this._timer=setTimeout(this._process,0)))}clear(){this._invokeMessages.length=0,this._cancelledJobIds.clear(),this._timer=null}_process(){this._timer=null;for(const e of this._invokeMessages)this._cancelledJobIds.has(e.jobId)||this._invoke(e);this._cancelledJobIds.clear(),this._invokeMessages.length=0}}class w{static connect(e){const s=new MessageChannel;let t;t=\"function\"==typeof e?new e:\"default\"in e&&\"function\"==typeof e.default?new e.default:e;const o=new w(s.port1,{channel:s,client:t},(()=>null));return\"object\"==typeof t&&\"remoteClient\"in t&&(t.remoteClient=o),w.clients.set(o,t),s.port2}static loadWorker(e){const s=k[e];return s?s():Promise.resolve(null)}constructor(e,s,t){this._port=e,this._getNextJob=t,this._outJobs=new Map,this._inJobs=new Map,this._invokeQueue=new M((e=>this._onInvokeMessage(e))),this._client=s.client,this._onMessage=this._onMessage.bind(this),this._channel=s.channel,this._schedule=s.schedule,this._port.addEventListener(\"message\",this._onMessage),this._port.start()}close(){this._post({type:b}),this._close()}isBusy(){return this._outJobs.size>0}invoke(e,t,r){const c=r&&r.signal,l=r&&r.transferList;if(!this._port)return Promise.reject(new s(\"worker:port-closed\",`Cannot call invoke('${e}'), port is closed`,{methodName:e,data:t}));const h=p();return new Promise(((s,r)=>{if(i(c))return this._processWork(),void r(n());const p=a(c,(()=>{const e=this._outJobs.get(h);e&&(this._outJobs.delete(h),this._processWork(),o(e.abortHandle),this._post({type:v,jobId:h}),r(n()))})),u={resolve:s,reject:r,abortHandle:p,debugInfo:e};this._outJobs.set(h,u),this._post({type:y,jobId:h,methodName:e,abortable:null!=c},t,l)}))}on(e,s){const t=new MessageChannel;function o(e){s(e.data)}return this._port.postMessage({type:h.ON,eventType:e,port:t.port2},[t.port2]),t.port1.addEventListener(\"message\",o),t.port1.start(),{remove(){t.port1.postMessage({type:h.CLOSE}),t.port1.close(),t.port1.removeEventListener(\"message\",o)}}}jobAdded(){this._processWork()}openPort(){const e=new MessageChannel;return this._post({type:S,port:e.port2}),e.port1}_processWork(){if(this._outJobs.size>=W)return;const e=this._getNextJob();if(!e)return;const{methodName:s,data:t,invokeOptions:o,deferred:r}=e;this.invoke(s,t,o).then((e=>r.resolve(e))).catch((e=>r.reject(e)))}_close(){this._channel&&(this._channel=void 0),this._port.removeEventListener(\"message\",this._onMessage),this._port.close(),this._outJobs.forEach((e=>{o(e.abortHandle),e.reject(n(`Worker closing, aborting job calling '${e.debugInfo}'`))})),this._inJobs.clear(),this._outJobs.clear(),this._invokeQueue.clear(),this._port=this._client=this._schedule=null}_onMessage(e){r(this._schedule)?this._schedule((()=>this._processMessage(e))):this._processMessage(e)}_processMessage(e){const s=u(e);if(s)switch(s.type){case j:this._onResponseMessage(s);break;case y:this._invokeQueue.push(s);break;case v:this._onAbortMessage(s);break;case b:this._onCloseMessage();break;case S:this._onOpenPortMessage(s);break;case f:this._onOnMessage(s)}}_onAbortMessage(e){const s=this._inJobs,t=e.jobId,o=s.get(t);this._invokeQueue.push(e),o&&(o.controller&&o.controller.abort(),s.delete(t))}_onCloseMessage(){const e=this._client;this._close(),e&&\"destroy\"in e&&w.clients.get(this)===e&&e.destroy(),w.clients.delete(this),e&&e.remoteClient&&(e.remoteClient=null)}_onInvokeMessage(e){const{methodName:s,jobId:t,data:o,abortable:r}=e,i=r?new AbortController:null,n=this._inJobs;let a,p=this._client,h=p[s];try{if(!h&&s&&s.includes(\".\")){const e=s.split(\".\");for(let s=0;s<e.length-1;s++)p=p[e[s]],h=p[e[s+1]]}if(\"function\"!=typeof h)throw new TypeError(`${s} is not a function`);a=h.call(p,o,{client:this,signal:i?i.signal:null})}catch(u){return void this._post({type:j,jobId:t,error:_(u)})}c(a)?(n.set(t,{controller:i,promise:a}),a.then((e=>{n.has(t)&&(n.delete(t),this._post({type:j,jobId:t},e))}),(e=>{n.has(t)&&(n.delete(t),l(e)||this._post({type:j,jobId:t,error:_(e||{message:`Error encountered at method ${s}`})}))}))):this._post({type:j,jobId:t},a)}_onOpenPortMessage(e){new w(e.port,{client:this._client},(()=>null))}_onOnMessage(e){const{port:s}=e,o=this._client.on(e.eventType,(e=>{s.postMessage(e)})),r=t(e.port,\"message\",(e=>{u(e)?.type===h.CLOSE&&(r.remove(),o.remove(),s.close())}))}_onResponseMessage(e){const{jobId:t,error:r,data:i}=e,n=this._outJobs;if(!n.has(t))return;const a=n.get(t);n.delete(t),this._processWork(),o(a.abortHandle),r?a.reject(s.fromJSON(JSON.parse(r))):a.resolve(i)}_post(e,s,t){return d(this._port,e,s,t)}}w.kernelInfo={revision:g,version:e,buildDate:m},w.clients=new Map;export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{handlesGroup as e}from\"../handleUtils.js\";import t from\"../Logger.js\";import{isPromiseLike as s,createAbortError as n,createDeferred as i,throwIfAborted as o}from\"../promiseUtils.js\";import r from\"../Queue.js\";import l from\"./RemoteClient.js\";class h{constructor(){this._inUseClients=new Array,this._clients=new Array,this._clientPromises=new Array,this._ongoingJobsQueue=new r}destroy(){this.close()}get closed(){return!this._clients||!this._clients.length}open(e,t){return new Promise(((n,i)=>{let r=!0;const h=e=>{o(t.signal),r&&(r=!1,e())};this._clients.length=e.length,this._clientPromises.length=e.length,this._inUseClients.length=e.length;for(let o=0;o<e.length;++o){const r=e[o];s(r)?this._clientPromises[o]=r.then((e=>(this._clients[o]=new l(e,t,(()=>this._ongoingJobsQueue.pop()??null)),h(n),this._clients[o])),(()=>(h(i),null))):(this._clients[o]=new l(r,t,(()=>this._ongoingJobsQueue.pop()??null)),this._clientPromises[o]=Promise.resolve(this._clients[o]),h(n))}}))}broadcast(e,t,s){const n=new Array(this._clientPromises.length);for(let i=0;i<this._clientPromises.length;++i){const o=this._clientPromises[i];n[i]=o.then((n=>n?.invoke(e,t,s)))}return n}close(){let e;for(;e=this._ongoingJobsQueue.pop();)e.deferred.reject(n(`Worker closing, aborting job calling '${e.methodName}'`));for(const t of this._clientPromises)t.then((e=>e?.close()));this._clients.length=0,this._clientPromises.length=0}invoke(e,s,n){let o;Array.isArray(n)?(t.getLogger(\"esri.core.workers.Connection\").warn(\"invoke()\",\"The transferList parameter is deprecated, use the options object instead\"),o={transferList:n}):o=n;const r=i();this._ongoingJobsQueue.push({methodName:e,data:s,invokeOptions:o,deferred:r});for(let t=0;t<this._clientPromises.length;t++){const e=this._clients[t];e?e.jobAdded():this._clientPromises[t].then((e=>e?.jobAdded()))}return r.promise}on(t,s){return Promise.all(this._clientPromises).then((()=>e(this._clients.map((e=>e.on(t,s))))))}openPorts(){return new Promise((e=>{const t=new Array(this._clientPromises.length);let s=t.length;for(let n=0;n<this._clientPromises.length;++n){this._clientPromises[n].then((i=>{i&&(t[n]=i.openPort()),0==--s&&e(t)}))}}))}get test(){return{numClients:this._clients.length}}}export{h as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2B,IAAI;AAAE,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,KAAG,CAAC,IAAE;AAAI,EAAE,MAAI,IAAE,CAAC,EAAE;AAAE,IAAIC,KAAE;AAAE,SAASC,KAAG;AAAC,SAAOD;AAAG;AAAC,SAAS,EAAED,IAAE;AAAC,SAAOA,MAAG,YAAU,OAAOA,OAAI,YAAWA,MAAG,kBAAiBA;AAAE;AAAC,SAASG,GAAEH,IAAE;AAAC,SAAOA,KAAE,YAAU,OAAOA,KAAE,KAAK,UAAU,EAAC,MAAK,WAAU,SAAQA,GAAC,CAAC,IAAEA,GAAE,SAAO,KAAK,UAAUA,EAAC,IAAE,KAAK,UAAU,EAAC,MAAKA,GAAE,MAAK,SAAQA,GAAE,SAAQ,SAAQA,GAAE,WAAS,EAAC,OAAMA,GAAE,MAAK,EAAC,CAAC,IAAE;AAAI;AAAC,SAAS,EAAEC,IAAEC,IAAEE,IAAEC,IAAE;AAAC,MAAGH,GAAE,SAAO,EAAE,UAAU,QAAO,KAAKD,GAAE,YAAYC,IAAE,CAACA,GAAE,IAAI,CAAC;AAAE,MAAGA,GAAE,SAAO,EAAE,UAAQA,GAAE,SAAO,EAAE,SAAS,QAAO,KAAKD,GAAE,YAAYC,EAAC;AAAE,MAAI;AAAE,MAAG,EAAEE,EAAC,KAAG,IAAE,EAAEA,GAAE,YAAY,GAAEF,GAAE,OAAKE,GAAE,WAAS,IAAE,EAAEC,EAAC,GAAEH,GAAE,OAAKE,KAAG,GAAE;AAAC,QAAG,IAAI,IAAI;AAAE,iBAAUE,MAAK,EAAE,KAAG,gBAAeA,MAAGA,GAAE,aAAW,WAAU;AAAC,cAAMA,KAAE;AAAyD,gBAAOJ,GAAE,MAAK;AAAA,UAAC,KAAK,EAAE;AAAO,kBAAMI;AAAA,UAAE,KAAK,EAAE;AAAS,mBAAO,KAAK,EAAEL,IAAE,EAAC,MAAK,EAAE,UAAS,OAAMC,GAAE,OAAM,OAAMC,GAAEG,EAAC,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA;AAAC,IAAAL,GAAE,YAAYC,IAAE,CAAC;AAAA,EAAC,MAAM,CAAAD,GAAE,YAAYC,EAAC;AAAC;AAAC,SAASE,GAAEJ,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,QAAMC,KAAED,GAAE;AAAK,SAAOC,KAAE,YAAU,OAAOA,KAAE,KAAK,MAAMA,EAAC,IAAEA,KAAE;AAAI;AAAC,SAAS,EAAED,IAAE;AAAC,MAAG,CAACA,MAAG,CAACA,GAAE,OAAO,QAAO;AAAK,MAAG,IAAI,mCAAmC,EAAE,QAAOA;AAAE,QAAMC,KAAED,GAAE,OAAQ,CAAAA,OAAG,CAACK,GAAEL,EAAC,CAAE;AAAE,SAAOC,GAAE,SAAOA,KAAE;AAAI;AAAC,SAASI,GAAEL,IAAE;AAAC,SAAOA,cAAa,eAAaA,MAAGA,GAAE,eAAa,kBAAgBA,GAAE,YAAY;AAAI;;;ACAv9B,IAAM,IAAE,EAAC,aAAY,MAAI,OAAO,2BAAsD,GAAE,sBAAqB,MAAI,OAAO,oCAAwC,GAAE,iBAAgB,MAAI,OAAO,+BAA0D,GAAE,sBAAqB,MAAI,OAAO,oCAAuE,GAAE,wBAAuB,MAAI,OAAO,sCAA4D,GAAE,oCAAmC,MAAI,OAAO,kDAA4G,GAAE,qBAAoB,MAAI,OAAO,mCAA8D,GAAE,YAAW,MAAI,OAAO,0BAAoC,GAAE,oBAAmB,MAAI,OAAO,kCAA6D,GAAE,kBAAiB,MAAI,OAAO,gCAA4C,GAAE,UAAS,MAAI,OAAO,wBAA4C,GAAE,kBAAiB,MAAI,OAAO,gCAA2C,GAAE,cAAa,MAAI,OAAO,4BAAsC,GAAE,gCAA+B,MAAI,OAAO,8CAAoG,GAAE,kBAAiB,MAAI,OAAO,gCAA2C,GAAE,iBAAgB,MAAI,OAAO,+BAAkD,GAAE,mBAAkB,MAAI,OAAO,iCAAwD,EAAC;AAAz+C,IAA2+C,EAAC,OAAM,GAAE,OAAMO,IAAE,QAAO,GAAE,UAASC,IAAE,WAAU,GAAE,IAAGC,GAAC,IAAE;AAAliD,IAAoiD,IAAE;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,SAAO,MAAK,KAAK,mBAAiB,oBAAI,OAAI,KAAK,kBAAgB,CAAC,GAAE,KAAK,UAAQA,IAAE,KAAK,SAAO,MAAK,KAAK,WAAS,KAAK,SAAS,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,IAAAA,GAAE,SAAO,EAAE,QAAM,KAAK,iBAAiB,IAAIA,GAAE,KAAK,KAAG,KAAK,gBAAgB,KAAKA,EAAC,GAAE,SAAO,KAAK,WAAS,KAAK,SAAO,WAAW,KAAK,UAAS,CAAC;AAAA,EAAG;AAAA,EAAC,QAAO;AAAC,SAAK,gBAAgB,SAAO,GAAE,KAAK,iBAAiB,MAAM,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,WAAU;AAAC,SAAK,SAAO;AAAK,eAAUA,MAAK,KAAK,gBAAgB,MAAK,iBAAiB,IAAIA,GAAE,KAAK,KAAG,KAAK,QAAQA,EAAC;AAAE,SAAK,iBAAiB,MAAM,GAAE,KAAK,gBAAgB,SAAO;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,OAAO,QAAQA,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAe,QAAIC;AAAE,IAAAA,KAAE,cAAY,OAAOF,KAAE,IAAIA,OAAE,aAAYA,MAAG,cAAY,OAAOA,GAAE,UAAQ,IAAIA,GAAE,YAAQA;AAAE,UAAMG,KAAE,IAAI,GAAEF,GAAE,OAAM,EAAC,SAAQA,IAAE,QAAOC,GAAC,GAAG,MAAI,IAAK;AAAE,WAAM,YAAU,OAAOA,MAAG,kBAAiBA,OAAIA,GAAE,eAAaC,KAAG,GAAE,QAAQ,IAAIA,IAAED,EAAC,GAAED,GAAE;AAAA,EAAK;AAAA,EAAC,OAAO,WAAWD,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,WAAOC,KAAEA,GAAE,IAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,QAAMF,IAAE,KAAK,cAAYE,IAAE,KAAK,WAAS,oBAAI,OAAI,KAAK,UAAQ,oBAAI,OAAI,KAAK,eAAa,IAAI,EAAG,CAAAF,OAAG,KAAK,iBAAiBA,EAAC,CAAE,GAAE,KAAK,UAAQC,GAAE,QAAO,KAAK,aAAW,KAAK,WAAW,KAAK,IAAI,GAAE,KAAK,WAASA,GAAE,SAAQ,KAAK,YAAUA,GAAE,UAAS,KAAK,MAAM,iBAAiB,WAAU,KAAK,UAAU,GAAE,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,MAAM,EAAC,MAAK,EAAC,CAAC,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,SAAS,OAAK;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAEE,IAAEE,IAAE;AAAC,UAAMC,KAAED,MAAGA,GAAE,QAAO,IAAEA,MAAGA,GAAE;AAAa,QAAG,CAAC,KAAK,MAAM,QAAO,QAAQ,OAAO,IAAIH,GAAE,sBAAqB,uBAAuBD,EAAC,sBAAqB,EAAC,YAAWA,IAAE,MAAKE,GAAC,CAAC,CAAC;AAAE,UAAMI,KAAEF,GAAE;AAAE,WAAO,IAAI,QAAS,CAACH,IAAEG,OAAI;AAAC,UAAGG,GAAEF,EAAC,EAAE,QAAO,KAAK,aAAa,GAAE,KAAKD,GAAE,EAAE,CAAC;AAAE,YAAMG,KAAE,EAAEF,IAAG,MAAI;AAAC,cAAML,KAAE,KAAK,SAAS,IAAIM,EAAC;AAAE,QAAAN,OAAI,KAAK,SAAS,OAAOM,EAAC,GAAE,KAAK,aAAa,GAAE,EAAEN,GAAE,WAAW,GAAE,KAAK,MAAM,EAAC,MAAKH,IAAE,OAAMS,GAAC,CAAC,GAAEF,GAAE,EAAE,CAAC;AAAA,MAAE,CAAE,GAAE,IAAE,EAAC,SAAQH,IAAE,QAAOG,IAAE,aAAYG,IAAE,WAAUP,GAAC;AAAE,WAAK,SAAS,IAAIM,IAAE,CAAC,GAAE,KAAK,MAAM,EAAC,MAAK,GAAE,OAAMA,IAAE,YAAWN,IAAE,WAAU,QAAMK,GAAC,GAAEH,IAAE,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAe,aAASC,GAAEH,IAAE;AAAC,MAAAC,GAAED,GAAE,IAAI;AAAA,IAAC;AAAC,WAAO,KAAK,MAAM,YAAY,EAAC,MAAK,EAAE,IAAG,WAAUA,IAAE,MAAKE,GAAE,MAAK,GAAE,CAACA,GAAE,KAAK,CAAC,GAAEA,GAAE,MAAM,iBAAiB,WAAUC,EAAC,GAAED,GAAE,MAAM,MAAM,GAAE,EAAC,SAAQ;AAAC,MAAAA,GAAE,MAAM,YAAY,EAAC,MAAK,EAAE,MAAK,CAAC,GAAEA,GAAE,MAAM,MAAM,GAAEA,GAAE,MAAM,oBAAoB,WAAUC,EAAC;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,aAAa;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,UAAMH,KAAE,IAAI;AAAe,WAAO,KAAK,MAAM,EAAC,MAAK,GAAE,MAAKA,GAAE,MAAK,CAAC,GAAEA,GAAE;AAAA,EAAK;AAAA,EAAC,eAAc;AAAC,QAAG,KAAK,SAAS,QAAM,EAAE;AAAO,UAAMA,KAAE,KAAK,YAAY;AAAE,QAAG,CAACA,GAAE;AAAO,UAAK,EAAC,YAAWC,IAAE,MAAKC,IAAE,eAAcC,IAAE,UAASC,GAAC,IAAEJ;AAAE,SAAK,OAAOC,IAAEC,IAAEC,EAAC,EAAE,KAAM,CAAAH,OAAGI,GAAE,QAAQJ,EAAC,CAAE,EAAE,MAAO,CAAAA,OAAGI,GAAE,OAAOJ,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,aAAW,KAAK,WAAS,SAAQ,KAAK,MAAM,oBAAoB,WAAU,KAAK,UAAU,GAAE,KAAK,MAAM,MAAM,GAAE,KAAK,SAAS,QAAS,CAAAA,OAAG;AAAC,QAAEA,GAAE,WAAW,GAAEA,GAAE,OAAO,EAAE,yCAAyCA,GAAE,SAAS,GAAG,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,QAAQ,MAAM,GAAE,KAAK,SAAS,MAAM,GAAE,KAAK,aAAa,MAAM,GAAE,KAAK,QAAM,KAAK,UAAQ,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,WAAWA,IAAE;AAAC,MAAE,KAAK,SAAS,IAAE,KAAK,UAAW,MAAI,KAAK,gBAAgBA,EAAC,CAAE,IAAE,KAAK,gBAAgBA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAEF,GAAEC,EAAC;AAAE,QAAGC,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAKH;AAAE,aAAK,mBAAmBG,EAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,aAAa,KAAKA,EAAC;AAAE;AAAA,MAAM,KAAKJ;AAAE,aAAK,gBAAgBI,EAAC;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,gBAAgB;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,mBAAmBA,EAAC;AAAE;AAAA,MAAM,KAAKF;AAAE,aAAK,aAAaE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAE;AAAC,UAAMC,KAAE,KAAK,SAAQC,KAAEF,GAAE,OAAMG,KAAEF,GAAE,IAAIC,EAAC;AAAE,SAAK,aAAa,KAAKF,EAAC,GAAEG,OAAIA,GAAE,cAAYA,GAAE,WAAW,MAAM,GAAEF,GAAE,OAAOC,EAAC;AAAA,EAAE;AAAA,EAAC,kBAAiB;AAAC,UAAMF,KAAE,KAAK;AAAQ,SAAK,OAAO,GAAEA,MAAG,aAAYA,MAAG,GAAE,QAAQ,IAAI,IAAI,MAAIA,MAAGA,GAAE,QAAQ,GAAE,GAAE,QAAQ,OAAO,IAAI,GAAEA,MAAGA,GAAE,iBAAeA,GAAE,eAAa;AAAA,EAAK;AAAA,EAAC,iBAAiBA,IAAE;AAAC,UAAK,EAAC,YAAWC,IAAE,OAAMC,IAAE,MAAKC,IAAE,WAAUC,GAAC,IAAEJ,IAAEQ,KAAEJ,KAAE,IAAI,oBAAgB,MAAKK,KAAE,KAAK;AAAQ,QAAIC,IAAEH,KAAE,KAAK,SAAQD,KAAEC,GAAEN,EAAC;AAAE,QAAG;AAAC,UAAG,CAACK,MAAGL,MAAGA,GAAE,SAAS,GAAG,GAAE;AAAC,cAAMD,KAAEC,GAAE,MAAM,GAAG;AAAE,iBAAQA,KAAE,GAAEA,KAAED,GAAE,SAAO,GAAEC,KAAI,CAAAM,KAAEA,GAAEP,GAAEC,EAAC,CAAC,GAAEK,KAAEC,GAAEP,GAAEC,KAAE,CAAC,CAAC;AAAA,MAAC;AAAC,UAAG,cAAY,OAAOK,GAAE,OAAM,IAAI,UAAU,GAAGL,EAAC,oBAAoB;AAAE,MAAAS,KAAEJ,GAAE,KAAKC,IAAEJ,IAAE,EAAC,QAAO,MAAK,QAAOK,KAAEA,GAAE,SAAO,KAAI,CAAC;AAAA,IAAC,SAAO,GAAE;AAAC,aAAO,KAAK,KAAK,MAAM,EAAC,MAAKV,IAAE,OAAMI,IAAE,OAAMD,GAAE,CAAC,EAAC,CAAC;AAAA,IAAC;AAAC,MAAES,EAAC,KAAGD,GAAE,IAAIP,IAAE,EAAC,YAAWM,IAAE,SAAQE,GAAC,CAAC,GAAEA,GAAE,KAAM,CAAAV,OAAG;AAAC,MAAAS,GAAE,IAAIP,EAAC,MAAIO,GAAE,OAAOP,EAAC,GAAE,KAAK,MAAM,EAAC,MAAKJ,IAAE,OAAMI,GAAC,GAAEF,EAAC;AAAA,IAAE,GAAI,CAAAA,OAAG;AAAC,MAAAS,GAAE,IAAIP,EAAC,MAAIO,GAAE,OAAOP,EAAC,GAAE,EAAEF,EAAC,KAAG,KAAK,MAAM,EAAC,MAAKF,IAAE,OAAMI,IAAE,OAAMD,GAAED,MAAG,EAAC,SAAQ,+BAA+BC,EAAC,GAAE,CAAC,EAAC,CAAC;AAAA,IAAE,CAAE,KAAG,KAAK,MAAM,EAAC,MAAKH,IAAE,OAAMI,GAAC,GAAEQ,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBV,IAAE;AAAC,QAAI,GAAEA,GAAE,MAAK,EAAC,QAAO,KAAK,QAAO,GAAG,MAAI,IAAK;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAK,EAAC,MAAKC,GAAC,IAAED,IAAEG,KAAE,KAAK,QAAQ,GAAGH,GAAE,WAAW,CAAAA,OAAG;AAAC,MAAAC,GAAE,YAAYD,EAAC;AAAA,IAAC,CAAE,GAAEI,KAAEA,GAAEJ,GAAE,MAAK,WAAW,CAAAA,OAAG;AAJ/1M;AAIg2M,aAAAD,GAAEC,EAAC,MAAH,mBAAM,UAAO,EAAE,UAAQI,GAAE,OAAO,GAAED,GAAE,OAAO,GAAEF,GAAE,MAAM;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAE;AAAC,UAAK,EAAC,OAAME,IAAE,OAAME,IAAE,MAAKI,GAAC,IAAER,IAAES,KAAE,KAAK;AAAS,QAAG,CAACA,GAAE,IAAIP,EAAC,EAAE;AAAO,UAAMQ,KAAED,GAAE,IAAIP,EAAC;AAAE,IAAAO,GAAE,OAAOP,EAAC,GAAE,KAAK,aAAa,GAAE,EAAEQ,GAAE,WAAW,GAAEN,KAAEM,GAAE,OAAOT,GAAE,SAAS,KAAK,MAAMG,EAAC,CAAC,CAAC,IAAEM,GAAE,QAAQF,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMR,IAAEC,IAAEC,IAAE;AAAC,WAAO,EAAE,KAAK,OAAMF,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,EAAE,aAAW,EAAC,UAAS,GAAE,SAAQQ,IAAE,WAAU,EAAC,GAAE,EAAE,UAAQ,oBAAI;;;ACAv9M,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,gBAAc,IAAI,SAAM,KAAK,WAAS,IAAI,SAAM,KAAK,kBAAgB,IAAI,SAAM,KAAK,oBAAkB,IAAIC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAM,CAAC,KAAK,YAAU,CAAC,KAAK,SAAS;AAAA,EAAM;AAAA,EAAC,KAAKA,IAAEC,IAAE;AAAC,WAAO,IAAI,QAAS,CAACC,IAAEC,OAAI;AAAC,UAAIC,KAAE;AAAG,YAAMC,KAAE,CAAAL,OAAG;AAAC,UAAEC,GAAE,MAAM,GAAEG,OAAIA,KAAE,OAAGJ,GAAE;AAAA,MAAE;AAAE,WAAK,SAAS,SAAOA,GAAE,QAAO,KAAK,gBAAgB,SAAOA,GAAE,QAAO,KAAK,cAAc,SAAOA,GAAE;AAAO,eAAQM,KAAE,GAAEA,KAAEN,GAAE,QAAO,EAAEM,IAAE;AAAC,cAAMF,KAAEJ,GAAEM,EAAC;AAAE,UAAEF,EAAC,IAAE,KAAK,gBAAgBE,EAAC,IAAEF,GAAE,KAAM,CAAAJ,QAAI,KAAK,SAASM,EAAC,IAAE,IAAI,EAAEN,IAAEC,IAAG,MAAI,KAAK,kBAAkB,IAAI,KAAG,IAAK,GAAEI,GAAEH,EAAC,GAAE,KAAK,SAASI,EAAC,IAAK,OAAKD,GAAEF,EAAC,GAAE,KAAM,KAAG,KAAK,SAASG,EAAC,IAAE,IAAI,EAAEF,IAAEH,IAAG,MAAI,KAAK,kBAAkB,IAAI,KAAG,IAAK,GAAE,KAAK,gBAAgBK,EAAC,IAAE,QAAQ,QAAQ,KAAK,SAASA,EAAC,CAAC,GAAED,GAAEH,EAAC;AAAA,MAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,UAAUF,IAAEC,IAAEM,IAAE;AAAC,UAAML,KAAE,IAAI,MAAM,KAAK,gBAAgB,MAAM;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,gBAAgB,QAAO,EAAEA,IAAE;AAAC,YAAMG,KAAE,KAAK,gBAAgBH,EAAC;AAAE,MAAAD,GAAEC,EAAC,IAAEG,GAAE,KAAM,CAAAJ,OAAGA,MAAA,gBAAAA,GAAG,OAAOF,IAAEC,IAAEM,GAAG;AAAA,IAAC;AAAC,WAAOL;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAIF;AAAE,WAAKA,KAAE,KAAK,kBAAkB,IAAI,IAAG,CAAAA,GAAE,SAAS,OAAO,EAAE,yCAAyCA,GAAE,UAAU,GAAG,CAAC;AAAE,eAAUC,MAAK,KAAK,gBAAgB,CAAAA,GAAE,KAAM,CAAAD,OAAGA,MAAA,gBAAAA,GAAG,OAAQ;AAAE,SAAK,SAAS,SAAO,GAAE,KAAK,gBAAgB,SAAO;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEO,IAAEL,IAAE;AAAC,QAAII;AAAE,UAAM,QAAQJ,EAAC,KAAG,EAAE,UAAU,8BAA8B,EAAE,KAAK,YAAW,0EAA0E,GAAEI,KAAE,EAAC,cAAaJ,GAAC,KAAGI,KAAEJ;AAAE,UAAME,KAAE,EAAE;AAAE,SAAK,kBAAkB,KAAK,EAAC,YAAWJ,IAAE,MAAKO,IAAE,eAAcD,IAAE,UAASF,GAAC,CAAC;AAAE,aAAQH,KAAE,GAAEA,KAAE,KAAK,gBAAgB,QAAOA,MAAI;AAAC,YAAMD,KAAE,KAAK,SAASC,EAAC;AAAE,MAAAD,KAAEA,GAAE,SAAS,IAAE,KAAK,gBAAgBC,EAAC,EAAE,KAAM,CAAAD,OAAGA,MAAA,gBAAAA,GAAG,UAAW;AAAA,IAAC;AAAC,WAAOI,GAAE;AAAA,EAAO;AAAA,EAAC,GAAGH,IAAEM,IAAE;AAAC,WAAO,QAAQ,IAAI,KAAK,eAAe,EAAE,KAAM,MAAIH,GAAE,KAAK,SAAS,IAAK,CAAAJ,OAAGA,GAAE,GAAGC,IAAEM,EAAC,CAAE,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,IAAI,QAAS,CAAAP,OAAG;AAAC,YAAMC,KAAE,IAAI,MAAM,KAAK,gBAAgB,MAAM;AAAE,UAAIM,KAAEN,GAAE;AAAO,eAAQC,KAAE,GAAEA,KAAE,KAAK,gBAAgB,QAAO,EAAEA,IAAE;AAAC,aAAK,gBAAgBA,EAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAAA,OAAIF,GAAEC,EAAC,IAAEC,GAAE,SAAS,IAAG,KAAG,EAAEI,MAAGP,GAAEC,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM,EAAC,YAAW,KAAK,SAAS,OAAM;AAAA,EAAC;AAAC;", "names": ["t", "e", "r", "s", "f", "a", "n", "v", "j", "f", "e", "s", "t", "o", "r", "c", "h", "p", "i", "n", "a", "e", "t", "n", "i", "r", "h", "o", "s"]}