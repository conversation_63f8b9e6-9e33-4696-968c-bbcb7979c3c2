{"version": 3, "sources": ["../../@arcgis/core/layers/graphics/data/projectionSupport.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as s}from\"../../../core/maybe.js\";import{initializeProjection as t,projectMany as e}from\"../../../geometry/projection.js\";import{jsonAdapter as n}from\"../../../geometry/geometryAdapters/json.js\";import{isValid as r,equals as i,isWebMercator as o}from\"../../../geometry/support/spatialReferenceUtils.js\";import{lngLatToXY as a,xyToLngLat as m,canProject as u}from\"../../../geometry/support/webMercatorUtils.js\";const l=[0,0];function h(s,t){if(!t)return null;if(\"x\"in t){const e={x:0,y:0};return[e.x,e.y]=s(t.x,t.y,l),null!=t.z&&(e.z=t.z),null!=t.m&&(e.m=t.m),e}if(\"xmin\"in t){const e={xmin:0,ymin:0,xmax:0,ymax:0};return[e.xmin,e.ymin]=s(t.xmin,t.ymin,l),[e.xmax,e.ymax]=s(t.xmax,t.ymax,l),t.hasZ&&(e.zmin=t.zmin,e.zmax=t.zmax,e.hasZ=!0),t.hasM&&(e.mmin=t.mmin,e.mmax=t.mmax,e.hasM=!0),e}return\"rings\"in t?{rings:c(t.rings,s),hasM:t.hasM,hasZ:t.hasZ}:\"paths\"in t?{paths:c(t.paths,s),hasM:t.hasM,hasZ:t.hasZ}:\"points\"in t?{points:p(t.points,s),hasM:t.hasM,hasZ:t.hasZ}:null}function c(s,t){const e=[];for(const n of s)e.push(p(n,t));return e}function p(s,t){const e=[];for(const n of s){const s=t(n[0],n[1],[0,0]);e.push(s),n.length>2&&s.push(n[2]),n.length>3&&s.push(n[3])}return e}async function f(e,n){if(!e||!n)return;const r=Array.isArray(e)?e.map((t=>s(t.geometry)?t.geometry.spatialReference:null)).filter(s):[e];await t(r.map((s=>({source:s,dest:n}))))}const x=h.bind(null,a),y=h.bind(null,m);function g(s,t,a,m){if(!s)return s;if(a||(a=t,t=s.spatialReference),!r(t)||!r(a)||i(t,a))return s;if(u(t,a)){const t=o(a)?x(s):y(s);return t.spatialReference=a,t}return e(n,[s],t,a,null,m)[0]}class _{constructor(){this._jobs=[],this._timer=null,this._process=this._process.bind(this)}async push(s,t,e){if(!s||!s.length||!t||!e||i(t,e))return s;const n={geometries:s,inSpatialReference:t,outSpatialReference:e,resolve:null};return this._jobs.push(n),new Promise((s=>{n.resolve=s,null===this._timer&&(this._timer=setTimeout(this._process,10))}))}_process(){this._timer=null;const s=this._jobs.shift();if(!s)return;const{geometries:t,inSpatialReference:r,outSpatialReference:i,resolve:a}=s;u(r,i)?o(i)?a(t.map(x)):a(t.map(y)):a(e(n,t,r,i,null,null)),this._jobs.length>0&&(this._timer=setTimeout(this._process,10))}}const j=new _;function M(s,t,e){return j.push(s,t,e)}export{f as checkProjectionSupport,g as project,M as projectMany};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAIwa,IAAM,IAAE,CAAC,GAAE,CAAC;AAAE,SAAS,EAAE,GAAEA,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,MAAG,OAAMA,IAAE;AAAC,UAAM,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC;AAAE,WAAM,CAAC,EAAE,GAAE,EAAE,CAAC,IAAE,EAAEA,GAAE,GAAEA,GAAE,GAAE,CAAC,GAAE,QAAMA,GAAE,MAAI,EAAE,IAAEA,GAAE,IAAG,QAAMA,GAAE,MAAI,EAAE,IAAEA,GAAE,IAAG;AAAA,EAAC;AAAC,MAAG,UAASA,IAAE;AAAC,UAAM,IAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,EAAC;AAAE,WAAM,CAAC,EAAE,MAAK,EAAE,IAAI,IAAE,EAAEA,GAAE,MAAKA,GAAE,MAAK,CAAC,GAAE,CAAC,EAAE,MAAK,EAAE,IAAI,IAAE,EAAEA,GAAE,MAAKA,GAAE,MAAK,CAAC,GAAEA,GAAE,SAAO,EAAE,OAAKA,GAAE,MAAK,EAAE,OAAKA,GAAE,MAAK,EAAE,OAAK,OAAIA,GAAE,SAAO,EAAE,OAAKA,GAAE,MAAK,EAAE,OAAKA,GAAE,MAAK,EAAE,OAAK,OAAI;AAAA,EAAC;AAAC,SAAM,WAAUA,KAAE,EAAC,OAAM,EAAEA,GAAE,OAAM,CAAC,GAAE,MAAKA,GAAE,MAAK,MAAKA,GAAE,KAAI,IAAE,WAAUA,KAAE,EAAC,OAAM,EAAEA,GAAE,OAAM,CAAC,GAAE,MAAKA,GAAE,MAAK,MAAKA,GAAE,KAAI,IAAE,YAAWA,KAAE,EAAC,QAAO,EAAEA,GAAE,QAAO,CAAC,GAAE,MAAKA,GAAE,MAAK,MAAKA,GAAE,KAAI,IAAE;AAAI;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,QAAM,IAAE,CAAC;AAAE,aAAU,KAAK,EAAE,GAAE,KAAK,EAAE,GAAEA,EAAC,CAAC;AAAE,SAAO;AAAC;AAAC,SAAS,EAAE,GAAEA,IAAE;AAAC,QAAM,IAAE,CAAC;AAAE,aAAU,KAAK,GAAE;AAAC,UAAMC,KAAED,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,MAAE,KAAKC,EAAC,GAAE,EAAE,SAAO,KAAGA,GAAE,KAAK,EAAE,CAAC,CAAC,GAAE,EAAE,SAAO,KAAGA,GAAE,KAAK,EAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,eAAe,EAAE,GAAE,GAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE;AAAO,QAAMC,KAAE,MAAM,QAAQ,CAAC,IAAE,EAAE,IAAK,CAAAF,OAAG,EAAEA,GAAE,QAAQ,IAAEA,GAAE,SAAS,mBAAiB,IAAK,EAAE,OAAO,CAAC,IAAE,CAAC,CAAC;AAAE,QAAM,GAAEE,GAAE,IAAK,QAAI,EAAC,QAAO,GAAE,MAAK,EAAC,EAAG,CAAC;AAAC;AAAC,IAAM,IAAE,EAAE,KAAK,MAAK,CAAC;AAArB,IAAuBC,KAAE,EAAE,KAAK,MAAK,CAAC;AAAE,SAASC,GAAE,GAAEJ,IAAE,GAAE,GAAE;AAAC,MAAG,CAAC,EAAE,QAAO;AAAE,MAAG,MAAI,IAAEA,IAAEA,KAAE,EAAE,mBAAkB,CAAC,EAAEA,EAAC,KAAG,CAAC,EAAE,CAAC,KAAG,EAAEA,IAAE,CAAC,EAAE,QAAO;AAAE,MAAG,EAAEA,IAAE,CAAC,GAAE;AAAC,UAAMA,KAAE,EAAE,CAAC,IAAE,EAAE,CAAC,IAAEG,GAAE,CAAC;AAAE,WAAOH,GAAE,mBAAiB,GAAEA;AAAA,EAAC;AAAC,SAAO,GAAE,GAAE,CAAC,CAAC,GAAEA,IAAE,GAAE,MAAK,CAAC,EAAE,CAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,CAAC,GAAE,KAAK,SAAO,MAAK,KAAK,WAAS,KAAK,SAAS,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,KAAK,GAAEA,IAAE,GAAE;AAAC,QAAG,CAAC,KAAG,CAAC,EAAE,UAAQ,CAACA,MAAG,CAAC,KAAG,EAAEA,IAAE,CAAC,EAAE,QAAO;AAAE,UAAM,IAAE,EAAC,YAAW,GAAE,oBAAmBA,IAAE,qBAAoB,GAAE,SAAQ,KAAI;AAAE,WAAO,KAAK,MAAM,KAAK,CAAC,GAAE,IAAI,QAAS,CAAAC,OAAG;AAAC,QAAE,UAAQA,IAAE,SAAO,KAAK,WAAS,KAAK,SAAO,WAAW,KAAK,UAAS,EAAE;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,SAAO;AAAK,UAAM,IAAE,KAAK,MAAM,MAAM;AAAE,QAAG,CAAC,EAAE;AAAO,UAAK,EAAC,YAAWD,IAAE,oBAAmBE,IAAE,qBAAoB,GAAE,SAAQ,EAAC,IAAE;AAAE,MAAEA,IAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAEF,GAAE,IAAI,CAAC,CAAC,IAAE,EAAEA,GAAE,IAAIG,EAAC,CAAC,IAAE,EAAE,GAAE,GAAEH,IAAEE,IAAE,GAAE,MAAK,IAAI,CAAC,GAAE,KAAK,MAAM,SAAO,MAAI,KAAK,SAAO,WAAW,KAAK,UAAS,EAAE;AAAA,EAAE;AAAC;AAAC,IAAM,IAAE,IAAI;AAAE,SAAS,EAAE,GAAEF,IAAE,GAAE;AAAC,SAAO,EAAE,KAAK,GAAEA,IAAE,CAAC;AAAC;", "names": ["t", "s", "r", "y", "g"]}