-- 涵养水位扩展测试数据
-- 根据实际表结构 tb_conservation_water_level 和 tb_conservation_analysis 生成
-- 包含更多测点和更长时间范围的数据

-- 插入更多涵养水位数据
INSERT INTO tb_conservation_water_level (
    id, tenant_id, station_id, 
    raw_water_level, groundwater_level, level_change, 
    rainfall_amount, evaporation_amount, surface_runoff, extraction_amount, 
    soil_moisture, permeability_coefficient, record_time, 
    create_time, update_time, remark, data_source, creator
) VALUES 

-- 测点1 (station_001) - 最近15天数据
('cwl_021', 'default', 'station_001', 
 12.350, 8.250, -0.030, 8.5, 13.2, 1080.0, 520.0, 
 31.5, 0.0025, '2024-01-11 08:00:00', 
 '2024-01-11 08:00:00', '2024-01-11 08:00:00', '晴天监测', 1, 'admin'),

('cwl_022', 'default', 'station_001', 
 12.320, 8.220, -0.030, 6.2, 14.8, 980.0, 550.0, 
 30.8, 0.0025, '2024-01-12 08:00:00', 
 '2024-01-12 08:00:00', '2024-01-12 08:00:00', '持续下降', 1, 'admin'),

('cwl_023', 'default', 'station_001', 
 12.420, 8.320, 0.100, 22.8, 8.5, 1380.0, 420.0, 
 33.2, 0.0025, '2024-01-13 08:00:00', 
 '2024-01-13 08:00:00', '2024-01-13 08:00:00', '中雨补给', 1, 'admin'),

('cwl_024', 'default', 'station_001', 
 12.480, 8.380, 0.060, 18.5, 9.8, 1450.0, 380.0, 
 34.5, 0.0025, '2024-01-14 08:00:00', 
 '2024-01-14 08:00:00', '2024-01-14 08:00:00', '水位回升', 1, 'admin'),

('cwl_025', 'default', 'station_001', 
 12.450, 8.350, -0.030, 12.2, 11.5, 1280.0, 450.0, 
 33.8, 0.0025, '2024-01-15 08:00:00', 
 '2024-01-15 08:00:00', '2024-01-15 08:00:00', '正常波动', 1, 'admin'),

-- 测点2 (station_002) - 最近15天数据
('cwl_026', 'default', 'station_002', 
 15.850, 11.610, 0.030, 16.8, 9.2, 1720.0, 380.0, 
 38.2, 0.0032, '2024-01-11 08:00:00', 
 '2024-01-11 08:00:00', '2024-01-11 08:00:00', '稳定上升', 2, 'system'),

('cwl_027', 'default', 'station_002', 
 15.820, 11.580, -0.030, 14.2, 10.8, 1680.0, 410.0, 
 37.5, 0.0032, '2024-01-12 08:00:00', 
 '2024-01-12 08:00:00', '2024-01-12 08:00:00', '轻微下降', 2, 'system'),

('cwl_028', 'default', 'station_002', 
 15.880, 11.640, 0.060, 25.5, 6.8, 1850.0, 350.0, 
 39.8, 0.0032, '2024-01-13 08:00:00', 
 '2024-01-13 08:00:00', '2024-01-13 08:00:00', '降雨补给', 2, 'system'),

('cwl_029', 'default', 'station_002', 
 15.920, 11.680, 0.040, 20.8, 8.2, 1920.0, 320.0, 
 40.5, 0.0032, '2024-01-14 08:00:00', 
 '2024-01-14 08:00:00', '2024-01-14 08:00:00', '水位良好', 2, 'system'),

('cwl_030', 'default', 'station_002', 
 15.890, 11.650, -0.030, 15.2, 9.8, 1780.0, 380.0, 
 39.2, 0.0032, '2024-01-15 08:00:00', 
 '2024-01-15 08:00:00', '2024-01-15 08:00:00', '正常监测', 2, 'system'),

-- 测点3 (station_003) - 最近15天数据
('cwl_031', 'default', 'station_003', 
 9.680, 6.350, -0.070, 2.5, 20.8, 680.0, 780.0, 
 23.8, 0.0018, '2024-01-11 08:00:00', 
 '2024-01-11 08:00:00', '2024-01-11 08:00:00', '干旱影响', 1, 'admin'),

('cwl_032', 'default', 'station_003', 
 9.620, 6.290, -0.060, 1.2, 22.5, 620.0, 820.0, 
 22.5, 0.0018, '2024-01-12 08:00:00', 
 '2024-01-12 08:00:00', '2024-01-12 08:00:00', '水位告警', 1, 'admin'),

('cwl_033', 'default', 'station_003', 
 9.780, 6.450, 0.160, 15.8, 16.2, 950.0, 650.0, 
 27.2, 0.0018, '2024-01-13 08:00:00', 
 '2024-01-13 08:00:00', '2024-01-13 08:00:00', '人工补给', 1, 'admin'),

('cwl_034', 'default', 'station_003', 
 9.820, 6.490, 0.040, 12.5, 17.8, 1020.0, 620.0, 
 28.5, 0.0018, '2024-01-14 08:00:00', 
 '2024-01-14 08:00:00', '2024-01-14 08:00:00', '水位恢复', 1, 'admin'),

('cwl_035', 'default', 'station_003', 
 9.780, 6.450, -0.040, 8.2, 19.2, 880.0, 680.0, 
 26.8, 0.0018, '2024-01-15 08:00:00', 
 '2024-01-15 08:00:00', '2024-01-15 08:00:00', '需要监控', 1, 'admin'),

-- 新增测点4 (station_004) - 备用水源地
('cwl_036', 'default', 'station_004', 
 14.250, 9.850, 0.000, 18.5, 8.8, 1650.0, 420.0, 
 36.8, 0.0028, '2024-01-11 08:00:00', 
 '2024-01-11 08:00:00', '2024-01-11 08:00:00', '新测点启用', 1, 'admin'),

('cwl_037', 'default', 'station_004', 
 14.220, 9.820, -0.030, 15.2, 10.5, 1580.0, 450.0, 
 36.2, 0.0028, '2024-01-12 08:00:00', 
 '2024-01-12 08:00:00', '2024-01-12 08:00:00', '运行正常', 1, 'admin'),

('cwl_038', 'default', 'station_004', 
 14.280, 9.880, 0.060, 28.2, 6.5, 1820.0, 380.0, 
 38.5, 0.0028, '2024-01-13 08:00:00', 
 '2024-01-13 08:00:00', '2024-01-13 08:00:00', '降雨补给', 1, 'admin'),

('cwl_039', 'default', 'station_004', 
 14.320, 9.920, 0.040, 22.8, 7.8, 1920.0, 350.0, 
 39.2, 0.0028, '2024-01-14 08:00:00', 
 '2024-01-14 08:00:00', '2024-01-14 08:00:00', '水位上升', 1, 'admin'),

('cwl_040', 'default', 'station_004', 
 14.290, 9.890, -0.030, 16.5, 9.2, 1750.0, 420.0, 
 38.0, 0.0028, '2024-01-15 08:00:00', 
 '2024-01-15 08:00:00', '2024-01-15 08:00:00', '稳定运行', 1, 'admin'),

-- 新增测点5 (station_005) - 应急水源地
('cwl_041', 'default', 'station_005', 
 11.680, 7.750, 0.000, 12.8, 12.5, 1180.0, 580.0, 
 29.5, 0.0022, '2024-01-11 08:00:00', 
 '2024-01-11 08:00:00', '2024-01-11 08:00:00', '应急启用', 1, 'admin'),

('cwl_042', 'default', 'station_005', 
 11.650, 7.720, -0.030, 9.5, 14.2, 1120.0, 620.0, 
 28.8, 0.0022, '2024-01-12 08:00:00', 
 '2024-01-12 08:00:00', '2024-01-12 08:00:00', '水位下降', 1, 'admin'),

('cwl_043', 'default', 'station_005', 
 11.720, 7.790, 0.070, 22.5, 9.8, 1350.0, 520.0, 
 31.2, 0.0022, '2024-01-13 08:00:00', 
 '2024-01-13 08:00:00', '2024-01-13 08:00:00', '降雨回升', 1, 'admin'),

('cwl_044', 'default', 'station_005', 
 11.750, 7.820, 0.030, 18.2, 10.8, 1420.0, 480.0, 
 32.0, 0.0022, '2024-01-14 08:00:00', 
 '2024-01-14 08:00:00', '2024-01-14 08:00:00', '水位恢复', 1, 'admin'),

('cwl_045', 'default', 'station_005', 
 11.720, 7.790, -0.030, 14.5, 12.2, 1280.0, 550.0, 
 30.8, 0.0022, '2024-01-15 08:00:00', 
 '2024-01-15 08:00:00', '2024-01-15 08:00:00', '正常运行', 1, 'admin');

-- 插入更多分析结果数据
INSERT INTO tb_conservation_analysis (
    id, tenant_id, station_id, start_time, end_time,
    initial_level, final_level, level_change, avg_rainfall, avg_evaporation, total_extraction,
    conservation_coefficient, conservation_potential, suggested_conservation_amount,
    conservation_suggestion, risk_level, risk_description, algorithm_version,
    analysis_details, create_time, update_time, status, creator
) VALUES 

-- 测点1的第二次分析
('ca_006', 'default', 'station_001', 
 '2024-01-06 00:00:00', '2024-01-10 23:59:59',
 8.220, 8.280, 0.060, 20.76, 8.36, 2030.0,
 0.1245, 78.5, 800.0,
 '水位呈现良好回升趋势，建议维持当前管理措施。可适当增加开采量，但需持续监测降雨情况。', 
 1, '低风险：水位稳定回升，涵养效果良好', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-06T08:00:00","rawWaterLevel":12.32,"groundwaterLevel":8.22,"levelChange":0.04},{"time":"2024-01-07T08:00:00","rawWaterLevel":12.48,"groundwaterLevel":8.38,"levelChange":0.16}],"environmentFactors":{"avgTemperature":8.5,"avgHumidity":58.2,"soilType":"砂质壤土"}}',
 '2024-01-11 10:00:00', '2024-01-11 10:00:00', 2, 'admin'),

-- 测点2的分析
('ca_007', 'default', 'station_002', 
 '2024-01-06 00:00:00', '2024-01-10 23:59:59',
 11.520, 11.650, 0.130, 19.86, 8.46, 1750.0,
 0.1385, 88.2, 600.0,
 '水位保持优秀的上升态势，涵养潜力极佳。建议继续保持现有开采强度，加强雨水收集系统建设。', 
 1, '低风险：水位持续上升，涵养状况优秀', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-06T08:00:00","rawWaterLevel":15.78,"groundwaterLevel":11.52,"levelChange":0.06},{"time":"2024-01-07T08:00:00","rawWaterLevel":15.82,"groundwaterLevel":11.58,"levelChange":0.06}],"environmentFactors":{"avgTemperature":10.2,"avgHumidity":62.5,"soilType":"粘质壤土"}}',
 '2024-01-11 11:00:00', '2024-01-11 11:00:00', 2, 'admin'),

-- 测点3的紧急分析
('ca_008', 'default', 'station_003', 
 '2024-01-06 00:00:00', '2024-01-10 23:59:59',
 6.420, 6.450, 0.030, 8.84, 19.3, 3550.0,
 0.0155, 45.8, 2000.0,
 '经人工补给后水位有所恢复，但仍需严格控制开采量。建议开采量控制在400m³/日以内，增加补给频次。', 
 2, '中等风险：人工干预后有改善，需持续关注', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-06T08:00:00","rawWaterLevel":9.75,"groundwaterLevel":6.42,"levelChange":0.07},{"time":"2024-01-07T08:00:00","rawWaterLevel":9.68,"groundwaterLevel":6.32,"levelChange":-0.04}],"environmentFactors":{"avgTemperature":22.8,"avgHumidity":28.5,"soilType":"砂土"}}',
 '2024-01-11 12:00:00', '2024-01-11 12:00:00', 2, 'admin'),

-- 测点4的首次分析
('ca_009', 'default', 'station_004', 
 '2024-01-11 00:00:00', '2024-01-15 23:59:59',
 9.850, 9.890, 0.040, 20.24, 8.56, 2020.0,
 0.1445, 82.5, 700.0,
 '新启用测点运行良好，水位稳定上升。建议逐步增加开采量至设计标准，完善监测设备配置。', 
 1, '低风险：新测点运行良好，涵养状况稳定', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-11T08:00:00","rawWaterLevel":14.25,"groundwaterLevel":9.85,"levelChange":0.00},{"time":"2024-01-12T08:00:00","rawWaterLevel":14.22,"groundwaterLevel":9.82,"levelChange":-0.03}],"environmentFactors":{"avgTemperature":12.5,"avgHumidity":55.8,"soilType":"壤土"}}',
 '2024-01-16 09:00:00', '2024-01-16 09:00:00', 2, 'admin'),

-- 测点5的分析
('ca_010', 'default', 'station_005', 
 '2024-01-11 00:00:00', '2024-01-15 23:59:59',
 7.750, 7.790, 0.040, 15.5, 11.9, 2750.0,
 0.0909, 68.5, 1000.0,
 '应急水源地运行正常，水位基本稳定。建议控制开采量在设计范围内，做好应急预案。', 
 2, '中等风险：应急水源需谨慎使用，保持储备', 'v1.2.0',
 '{"levelTrend":[{"time":"2024-01-11T08:00:00","rawWaterLevel":11.68,"groundwaterLevel":7.75,"levelChange":0.00},{"time":"2024-01-12T08:00:00","rawWaterLevel":11.65,"groundwaterLevel":7.72,"levelChange":-0.03}],"environmentFactors":{"avgTemperature":15.8,"avgHumidity":48.2,"soilType":"砂质壤土"}}',
 '2024-01-16 10:00:00', '2024-01-16 10:00:00', 2, 'admin');

-- 提交事务
COMMIT;
