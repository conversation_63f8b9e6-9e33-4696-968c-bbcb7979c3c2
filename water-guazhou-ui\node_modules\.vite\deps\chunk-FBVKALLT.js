import {
  s as s4
} from "./chunk-22GGEXM2.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import {
  n
} from "./chunk-SGIJIEHB.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  j
} from "./chunk-EKX3LLYN.js";
import {
  s as s3
} from "./chunk-4RZONHOY.js";
import {
  s as s2
} from "./chunk-RV4I37UI.js";
import {
  s
} from "./chunk-XOI5RUBC.js";

// node_modules/@arcgis/core/layers/support/fromPortalItem.js
async function o(o2) {
  const a2 = "portalItem" in o2 ? o2 : { portalItem: o2 }, e2 = await import("./portalLayers-RJLGPF2K.js");
  try {
    return await e2.fromItem(a2);
  } catch (p) {
    const o3 = a2 && a2.portalItem, e3 = o3 && o3.id || "unset", l = o3 && o3.portal && o3.portal.url || s.portalUrl;
    throw s2.getLogger("esri.layers.support.fromPortalItem").error("#fromPortalItem()", "Failed to create layer from portal item (portal: '" + l + "', id: '" + e3 + "')", p), p;
  }
}

// node_modules/@arcgis/core/layers/Layer.js
var m2 = 0;
var h = class extends n.EventedMixin(s4(m)) {
  constructor() {
    super(...arguments), this.attributionDataUrl = null, this.fullExtent = new w(-180, -90, 180, 90, f.WGS84), this.id = Date.now().toString(16) + "-layer-" + m2++, this.legendEnabled = true, this.listMode = "show", this.opacity = 1, this.parent = null, this.popupEnabled = true, this.attributionVisible = true, this.spatialReference = f.WGS84, this.title = null, this.type = null, this.url = null, this.visible = true;
  }
  static async fromArcGISServerUrl(t) {
    const e2 = "string" == typeof t ? { url: t } : t;
    return (await import("./arcgisLayers-JPJL33OB.js")).fromUrl(e2);
  }
  static fromPortalItem(t) {
    return o(t);
  }
  initialize() {
    this.when().catch((t) => {
      j(t) || s2.getLogger(this.declaredClass).error("#load()", `Failed to load layer (title: '${this.title ?? "no title"}', id: '${this.id ?? "no id"}')`, { error: t });
    });
  }
  destroy() {
    if (this.parent) {
      const t = this, e2 = this.parent;
      "layers" in e2 && e2.layers.includes(t) ? e2.layers.remove(t) : "tables" in e2 && e2.tables.includes(t) ? e2.tables.remove(t) : "baseLayers" in e2 && e2.baseLayers.includes(t) ? e2.baseLayers.remove(t) : "baseLayers" in e2 && e2.referenceLayers.includes(t) && e2.referenceLayers.remove(t);
    }
  }
  get hasAttributionData() {
    return null != this.attributionDataUrl;
  }
  get parsedUrl() {
    return L(this.url);
  }
  async fetchAttributionData() {
    const t = this.attributionDataUrl;
    if (this.hasAttributionData && t) {
      return (await U(t, { query: { f: "json" }, responseType: "json" })).data;
    }
    throw new s3("layer:no-attribution-data", "Layer does not have attribution data");
  }
};
e([y({ type: String })], h.prototype, "attributionDataUrl", void 0), e([y({ type: w })], h.prototype, "fullExtent", void 0), e([y({ readOnly: true })], h.prototype, "hasAttributionData", null), e([y({ type: String, clonable: false })], h.prototype, "id", void 0), e([y({ type: Boolean, nonNullable: true })], h.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide", "hide-children"] })], h.prototype, "listMode", void 0), e([y({ type: Number, range: { min: 0, max: 1 }, nonNullable: true })], h.prototype, "opacity", void 0), e([y({ clonable: false })], h.prototype, "parent", void 0), e([y({ readOnly: true })], h.prototype, "parsedUrl", null), e([y({ type: Boolean })], h.prototype, "popupEnabled", void 0), e([y({ type: Boolean })], h.prototype, "attributionVisible", void 0), e([y({ type: f })], h.prototype, "spatialReference", void 0), e([y({ type: String })], h.prototype, "title", void 0), e([y({ readOnly: true, json: { read: false } })], h.prototype, "type", void 0), e([y()], h.prototype, "url", void 0), e([y({ type: Boolean, nonNullable: true })], h.prototype, "visible", void 0), h = e([a("esri.layers.Layer")], h);
var b = h;

export {
  b
};
//# sourceMappingURL=chunk-FBVKALLT.js.map
