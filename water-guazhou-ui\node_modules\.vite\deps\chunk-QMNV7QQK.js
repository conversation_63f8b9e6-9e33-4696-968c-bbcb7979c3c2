import {
  p as p3
} from "./chunk-TNGL5OFU.js";
import {
  d
} from "./chunk-22HLMDJ5.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import {
  l
} from "./chunk-5GX2JMCX.js";
import {
  U,
  r as r2
} from "./chunk-AW4AS2UW.js";
import {
  r as r3
} from "./chunk-WXFAAYJL.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  u2 as u
} from "./chunk-UOKTNY52.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  b
} from "./chunk-HP475EI3.js";
import {
  a as a2,
  f,
  p as p2,
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-XOI5RUBC.js";
import {
  p,
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/portal/PortalQueryResult.js
var e2 = class extends v {
  constructor(r4) {
    super(r4), this.nextQueryParams = null, this.queryParams = null, this.results = null, this.total = null;
  }
};
e([y()], e2.prototype, "nextQueryParams", void 0), e([y()], e2.prototype, "queryParams", void 0), e([y()], e2.prototype, "results", void 0), e([y()], e2.prototype, "total", void 0), e2 = e([a("esri.portal.PortalQueryResult")], e2);
var p4 = e2;

// node_modules/@arcgis/core/portal/Portal.js
var U2;
var B;
var D = { PortalGroup: () => import("./PortalGroup-HKSEUL3F.js"), PortalItem: () => import("./PortalItem-TUK6YOQD.js"), PortalUser: () => import("./PortalUser-LV6XTFVM.js") };
var j = U2 = class extends u(m) {
  constructor(e3) {
    super(e3), this._esriIdCredentialCreateHandle = null, this.access = null, this.allSSL = false, this.authMode = "auto", this.authorizedCrossOriginDomains = null, this.basemapGalleryGroupQuery = null, this.bingKey = null, this.canListApps = false, this.canListData = false, this.canListPreProvisionedItems = false, this.canProvisionDirectPurchase = false, this.canSearchPublic = true, this.canShareBingPublic = false, this.canSharePublic = false, this.canSignInArcGIS = false, this.canSignInIDP = false, this.colorSetsGroupQuery = null, this.commentsEnabled = false, this.created = null, this.culture = null, this.customBaseUrl = null, this.defaultBasemap = null, this.defaultDevBasemap = null, this.defaultExtent = null, this.defaultVectorBasemap = null, this.description = null, this.devBasemapGalleryGroupQuery = null, this.eueiEnabled = null, this.featuredGroups = null, this.featuredItemsGroupQuery = null, this.galleryTemplatesGroupQuery = null, this.livingAtlasGroupQuery = null, this.hasCategorySchema = false, this.helperServices = null, this.homePageFeaturedContent = null, this.homePageFeaturedContentCount = null, this.httpPort = null, this.httpsPort = null, this.id = null, this.ipCntryCode = null, this.isPortal = false, this.isReadOnly = false, this.layerTemplatesGroupQuery = null, this.maxTokenExpirationMinutes = null, this.modified = null, this.name = null, this.portalHostname = null, this.portalMode = null, this.portalProperties = null, this.region = null, this.rotatorPanels = null, this.showHomePageDescription = false, this.sourceJSON = null, this.supportsHostedServices = false, this.symbolSetsGroupQuery = null, this.templatesGroupQuery = null, this.units = null, this.url = s.portalUrl, this.urlKey = null, this.user = null, this.useStandardizedQuery = false, this.useVectorBasemaps = false, this.vectorBasemapGalleryGroupQuery = null;
  }
  normalizeCtorArgs(e3) {
    return "string" == typeof e3 ? { url: e3 } : e3;
  }
  destroy() {
    this._esriIdCredentialCreateHandle = p(this._esriIdCredentialCreateHandle);
  }
  readAuthorizedCrossOriginDomains(e3) {
    if (e3) for (const r4 of e3) s.request.trustedServers.includes(r4) || s.request.trustedServers.push(r4);
    return e3;
  }
  readDefaultBasemap(e3) {
    return this._readBasemap(e3);
  }
  readDefaultDevBasemap(e3) {
    return this._readBasemap(e3);
  }
  readDefaultVectorBasemap(e3) {
    return this._readBasemap(e3);
  }
  get extraQuery() {
    const e3 = !(this.user && this.user.orgId) || this.canSearchPublic;
    return this.id && !e3 ? ` AND orgid:${this.id}` : null;
  }
  get isOrganization() {
    return !!this.access;
  }
  get itemPageUrl() {
    return this.url ? `${this.url}/home/<USER>
  }
  get restUrl() {
    let e3 = this.url;
    if (e3) {
      const t = e3.indexOf("/sharing");
      e3 = t > 0 ? e3.substring(0, t) : this.url.replace(/\/+$/, ""), e3 += "/sharing/rest";
    }
    return e3;
  }
  get thumbnailUrl() {
    const e3 = this.restUrl, t = this.thumbnail;
    return e3 && t ? this._normalizeSSL(e3 + "/portals/self/resources/" + t) : null;
  }
  readUrlKey(e3) {
    return e3 ? e3.toLowerCase() : e3;
  }
  readUser(e3) {
    let t = null;
    return e3 && (t = p3.fromJSON(e3), t.portal = this), t;
  }
  load(e3) {
    const t = import("./Basemap-3W5JMK22.js").then(({ default: t2 }) => {
      f(e3), B = t2;
    }).then(() => this.sourceJSON ? this.sourceJSON : this.fetchSelf(this.authMode, false, e3)).then((e4) => {
      if (r3) {
        const e5 = r3;
        this.credential = e5.findCredential(this.restUrl), this.credential || this.authMode !== U2.AUTH_MODE_AUTO || (this._esriIdCredentialCreateHandle = e5.on("credential-create", () => {
          e5.findCredential(this.restUrl) && this.signIn().catch(() => {
          });
        }));
      }
      this.sourceJSON = e4, this.read(e4);
    });
    return this.addResolvingPromise(t), Promise.resolve(this);
  }
  async createElevationLayers() {
    await this.load();
    const e3 = this._getHelperService("defaultElevationLayers"), t = (await import("./ElevationLayer-CKIV3SWN.js")).default;
    return e3 ? e3.map((e4) => new t({ id: e4.id, url: e4.url })) : [];
  }
  fetchBasemaps(e3, r4) {
    const o2 = new d();
    return o2.query = e3 || (s.apiKey && r2(this.url) ? this.devBasemapGalleryGroupQuery : this.useVectorBasemaps ? this.vectorBasemapGalleryGroupQuery : this.basemapGalleryGroupQuery), o2.disableExtraQuery = true, this.queryGroups(o2, r4).then((e4) => {
      if (o2.num = 100, o2.query = 'type:"Web Map" -type:"Web Application"', e4.total) {
        const t = e4.results[0];
        return o2.sortField = t.sortField || "name", o2.sortOrder = t.sortOrder || "desc", t.queryItems(o2, r4);
      }
      return null;
    }).then((e4) => {
      let t;
      return t = e4 && e4.total ? e4.results.filter((e5) => "Web Map" === e5.type).map((e5) => new B({ portalItem: e5 })) : [], t;
    });
  }
  fetchCategorySchema(e3) {
    return this.hasCategorySchema ? this.request(this.restUrl + "/portals/self/categorySchema", e3).then((e4) => e4.categorySchema) : p2(e3) ? Promise.reject(a2()) : Promise.resolve([]);
  }
  fetchFeaturedGroups(e3) {
    const t = this.featuredGroups, r4 = new d();
    if (r4.num = 100, r4.sortField = "title", t && t.length) {
      const o2 = [];
      for (const e4 of t) o2.push(`(title:"${e4.title}" AND owner:${e4.owner})`);
      return r4.query = o2.join(" OR "), this.queryGroups(r4, e3).then((e4) => e4.results);
    }
    return p2(e3) ? Promise.reject(a2()) : Promise.resolve([]);
  }
  fetchRegions(e3) {
    var _a;
    const t = ((_a = this.user) == null ? void 0 : _a.culture) || this.culture || l();
    return this.request(this.restUrl + "/portals/regions", { ...e3, query: { culture: t } });
  }
  fetchSettings(e3) {
    var _a;
    const t = ((_a = this.user) == null ? void 0 : _a.culture) || this.culture || l();
    return this.request(this.restUrl + "/portals/self/settings", { ...e3, query: { culture: t } });
  }
  static getDefault() {
    return U2._default && !U2._default.destroyed || (U2._default = new U2()), U2._default;
  }
  queryGroups(e3, t) {
    return this.queryPortal("/community/groups", e3, "PortalGroup", t);
  }
  queryItems(e3, t) {
    return this.queryPortal("/search", e3, "PortalItem", t);
  }
  queryUsers(e3, t) {
    return e3.sortField || (e3.sortField = "username"), this.queryPortal("/community/users", e3, "PortalUser", t);
  }
  fetchSelf(e3 = this.authMode, t = false, r4) {
    const o2 = this.restUrl + "/portals/self", s3 = { authMode: e3, query: { culture: l().toLowerCase() }, ...r4 };
    return "auto" === s3.authMode && (s3.authMode = "no-prompt"), t && (s3.query.default = true), this.request(o2, s3);
  }
  queryPortal(e3, t, r4, o2) {
    const s3 = b(d, t), i = (t2) => this.request(this.restUrl + e3, { ...s3.toRequestOptions(this), ...o2 }).then((e4) => {
      const r5 = s3.clone();
      return r5.start = e4.nextStart, new p4({ nextQueryParams: r5, queryParams: s3, total: e4.total, results: U2._resultsToTypedArray(t2, { portal: this }, e4, o2) });
    }).then((e4) => Promise.all(e4.results.map((t3) => "function" == typeof t3.when ? t3.when() : e4)).then(() => e4, (t3) => (w(t3), e4)));
    return r4 && D[r4] ? D[r4]().then(({ default: e4 }) => (f(o2), i(e4))) : i();
  }
  signIn() {
    if (this.authMode === U2.AUTH_MODE_ANONYMOUS) return Promise.reject(new s2("portal:invalid-auth-mode", `Current "authMode"' is "${this.authMode}"`));
    if ("failed" === this.loadStatus) return Promise.reject(this.loadError);
    const e3 = (e4) => Promise.resolve().then(() => "not-loaded" === this.loadStatus ? (e4 || (this.authMode = "immediate"), this.load().then(() => null)) : "loading" === this.loadStatus ? this.load().then(() => this.credential ? null : (this.credential = e4, this.fetchSelf("immediate"))) : this.user && this.credential === e4 ? null : (this.credential = e4, this.fetchSelf("immediate"))).then((e5) => {
      e5 && (this.sourceJSON = e5, this.read(e5));
    });
    return r3 ? r3.getCredential(this.restUrl).then((t) => e3(t)) : e3(this.credential);
  }
  normalizeUrl(e3) {
    const t = this.credential && this.credential.token;
    return this._normalizeSSL(t ? e3 + (e3.includes("?") ? "&" : "?") + "token=" + t : e3);
  }
  requestToTypedArray(e3, t, r4) {
    return this.request(e3, t).then((e4) => {
      const t2 = U2._resultsToTypedArray(r4, { portal: this }, e4);
      return Promise.all(t2.map((t3) => "function" == typeof t3.when ? t3.when() : e4)).then(() => t2, () => t2);
    });
  }
  request(e3, t = {}) {
    const r4 = { f: "json", ...t.query }, { authMode: s3 = this.authMode === U2.AUTH_MODE_ANONYMOUS ? "anonymous" : "auto", body: i = null, cacheBust: a3 = false, method: l2 = "auto", responseType: u2 = "json", signal: n } = t, p5 = { authMode: s3, body: i, cacheBust: a3, method: l2, query: r4, responseType: u2, timeout: 0, signal: n };
    return U(this._normalizeSSL(e3), p5).then((e4) => e4.data);
  }
  toJSON() {
    throw new s2("internal:not-yet-implemented", "Portal.toJSON is not yet implemented");
  }
  static fromJSON(e3) {
    if (!e3) return null;
    if (e3.declaredClass) throw new Error("JSON object is already hydrated");
    return new U2({ sourceJSON: e3 });
  }
  _getHelperService(e3) {
    const t = this.helperServices && this.helperServices[e3];
    if (!t) throw new s2("portal:service-not-found", `The \`helperServices\` do not include an entry named "${e3}"`);
    return t;
  }
  _normalizeSSL(e3) {
    return e3.replace(/^http:/i, "https:").replace(":7080", ":7443");
  }
  _readBasemap(e3) {
    if (e3) {
      const t = B.fromJSON(e3);
      return t.portalItem = { portal: this }, t;
    }
    return null;
  }
  static _resultsToTypedArray(e3, t, r4, o2) {
    let s3 = [];
    if (r4) {
      const i = r(o2) ? o2.signal : null;
      s3 = r4.listings || r4.notifications || r4.userInvitations || r4.tags || r4.items || r4.groups || r4.comments || r4.provisions || r4.results || r4.relatedItems || r4, (e3 || t) && (s3 = s3.map((r5) => {
        const o3 = Object.assign(e3 ? e3.fromJSON(r5) : r5, t);
        return "function" == typeof o3.load && o3.load(i), o3;
      }));
    } else s3 = [];
    return s3;
  }
};
j.AUTH_MODE_ANONYMOUS = "anonymous", j.AUTH_MODE_AUTO = "auto", j.AUTH_MODE_IMMEDIATE = "immediate", e([y()], j.prototype, "access", void 0), e([y()], j.prototype, "allSSL", void 0), e([y()], j.prototype, "authMode", void 0), e([y()], j.prototype, "authorizedCrossOriginDomains", void 0), e([o("authorizedCrossOriginDomains")], j.prototype, "readAuthorizedCrossOriginDomains", null), e([y()], j.prototype, "basemapGalleryGroupQuery", void 0), e([y()], j.prototype, "bingKey", void 0), e([y()], j.prototype, "canListApps", void 0), e([y()], j.prototype, "canListData", void 0), e([y()], j.prototype, "canListPreProvisionedItems", void 0), e([y()], j.prototype, "canProvisionDirectPurchase", void 0), e([y()], j.prototype, "canSearchPublic", void 0), e([y()], j.prototype, "canShareBingPublic", void 0), e([y()], j.prototype, "canSharePublic", void 0), e([y()], j.prototype, "canSignInArcGIS", void 0), e([y()], j.prototype, "canSignInIDP", void 0), e([y()], j.prototype, "colorSetsGroupQuery", void 0), e([y()], j.prototype, "commentsEnabled", void 0), e([y({ type: Date })], j.prototype, "created", void 0), e([y()], j.prototype, "credential", void 0), e([y()], j.prototype, "culture", void 0), e([y()], j.prototype, "currentVersion", void 0), e([y()], j.prototype, "customBaseUrl", void 0), e([y()], j.prototype, "defaultBasemap", void 0), e([o("defaultBasemap")], j.prototype, "readDefaultBasemap", null), e([y()], j.prototype, "defaultDevBasemap", void 0), e([o("defaultDevBasemap")], j.prototype, "readDefaultDevBasemap", null), e([y({ type: w2 })], j.prototype, "defaultExtent", void 0), e([y()], j.prototype, "defaultVectorBasemap", void 0), e([o("defaultVectorBasemap")], j.prototype, "readDefaultVectorBasemap", null), e([y()], j.prototype, "description", void 0), e([y()], j.prototype, "devBasemapGalleryGroupQuery", void 0), e([y()], j.prototype, "eueiEnabled", void 0), e([y({ readOnly: true })], j.prototype, "extraQuery", null), e([y()], j.prototype, "featuredGroups", void 0), e([y()], j.prototype, "featuredItemsGroupQuery", void 0), e([y()], j.prototype, "galleryTemplatesGroupQuery", void 0), e([y()], j.prototype, "livingAtlasGroupQuery", void 0), e([y()], j.prototype, "hasCategorySchema", void 0), e([y()], j.prototype, "helpBase", void 0), e([y()], j.prototype, "helperServices", void 0), e([y()], j.prototype, "helpMap", void 0), e([y()], j.prototype, "homePageFeaturedContent", void 0), e([y()], j.prototype, "homePageFeaturedContentCount", void 0), e([y()], j.prototype, "httpPort", void 0), e([y()], j.prototype, "httpsPort", void 0), e([y()], j.prototype, "id", void 0), e([y()], j.prototype, "ipCntryCode", void 0), e([y({ readOnly: true })], j.prototype, "isOrganization", null), e([y()], j.prototype, "isPortal", void 0), e([y()], j.prototype, "isReadOnly", void 0), e([y({ readOnly: true })], j.prototype, "itemPageUrl", null), e([y()], j.prototype, "layerTemplatesGroupQuery", void 0), e([y()], j.prototype, "maxTokenExpirationMinutes", void 0), e([y({ type: Date })], j.prototype, "modified", void 0), e([y()], j.prototype, "name", void 0), e([y()], j.prototype, "portalHostname", void 0), e([y()], j.prototype, "portalMode", void 0), e([y()], j.prototype, "portalProperties", void 0), e([y()], j.prototype, "region", void 0), e([y({ readOnly: true })], j.prototype, "restUrl", null), e([y()], j.prototype, "rotatorPanels", void 0), e([y()], j.prototype, "showHomePageDescription", void 0), e([y()], j.prototype, "sourceJSON", void 0), e([y()], j.prototype, "staticImagesUrl", void 0), e([y({ json: { name: "2DStylesGroupQuery" } })], j.prototype, "stylesGroupQuery2d", void 0), e([y({ json: { name: "stylesGroupQuery" } })], j.prototype, "stylesGroupQuery3d", void 0), e([y()], j.prototype, "supportsHostedServices", void 0), e([y()], j.prototype, "symbolSetsGroupQuery", void 0), e([y()], j.prototype, "templatesGroupQuery", void 0), e([y()], j.prototype, "thumbnail", void 0), e([y({ readOnly: true })], j.prototype, "thumbnailUrl", null), e([y()], j.prototype, "units", void 0), e([y()], j.prototype, "url", void 0), e([y()], j.prototype, "urlKey", void 0), e([o("urlKey")], j.prototype, "readUrlKey", null), e([y()], j.prototype, "user", void 0), e([o("user")], j.prototype, "readUser", null), e([y()], j.prototype, "useStandardizedQuery", void 0), e([y()], j.prototype, "useVectorBasemaps", void 0), e([y()], j.prototype, "vectorBasemapGalleryGroupQuery", void 0), j = U2 = e([a("esri.portal.Portal")], j);
var b2 = j;

export {
  b2 as b
};
//# sourceMappingURL=chunk-QMNV7QQK.js.map
