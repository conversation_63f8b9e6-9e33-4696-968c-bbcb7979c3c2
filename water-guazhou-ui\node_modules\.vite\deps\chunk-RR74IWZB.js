import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/layers/mixins/APIKeyMixin.js
function t(e2) {
  return "portalItem" in e2;
}
var i = (i2) => {
  let o = class extends i2 {
    get apiKey() {
      var _a;
      return this._isOverridden("apiKey") ? this._get("apiKey") : t(this) ? (_a = this.portalItem) == null ? void 0 : _a.apiKey : null;
    }
    set apiKey(e2) {
      null != e2 ? this._override("apiKey", e2) : (this._clearOverride("apiKey"), this.clear("apiKey", "user"));
    }
  };
  return e([y({ type: String })], o.prototype, "apiKey", null), o = e([a("esri.layers.mixins.APIKeyMixin")], o), o;
};

export {
  i
};
//# sourceMappingURL=chunk-RR74IWZB.js.map
