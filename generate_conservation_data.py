#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涵养水位测试数据生成脚本
生成SQL插入语句和Excel导入数据
"""

import random
import datetime
import uuid
import csv
from typing import List, Dict

class ConservationDataGenerator:
    def __init__(self):
        # 测点配置
        self.stations = [
            {
                'id': 'station_001',
                'name': '瓜州县第一水源地',
                'location': '瓜州县城北5公里',
                'base_raw_level': 12.5,
                'base_ground_level': 8.3,
                'permeability': 0.0025,
                'soil_moisture_base': 32.0
            },
            {
                'id': 'station_002', 
                'name': '瓜州县第二水源地',
                'location': '瓜州县城东8公里',
                'base_raw_level': 15.7,
                'base_ground_level': 11.4,
                'permeability': 0.0032,
                'soil_moisture_base': 38.0
            },
            {
                'id': 'station_003',
                'name': '瓜州县第三水源地', 
                'location': '瓜州县城南12公里',
                'base_raw_level': 9.8,
                'base_ground_level': 6.5,
                'permeability': 0.0018,
                'soil_moisture_base': 25.0
            },
            {
                'id': 'station_004',
                'name': '瓜州县第四水源地',
                'location': '瓜州县城西6公里', 
                'base_raw_level': 14.2,
                'base_ground_level': 9.8,
                'permeability': 0.0028,
                'soil_moisture_base': 40.0
            },
            {
                'id': 'station_005',
                'name': '瓜州县第五水源地',
                'location': '瓜州县城东南10公里',
                'base_raw_level': 11.8,
                'base_ground_level': 7.9,
                'permeability': 0.0022,
                'soil_moisture_base': 30.0
            }
        ]
        
        # 季节性参数
        self.seasonal_factors = {
            'spring': {'rainfall_factor': 1.2, 'evaporation_factor': 0.8, 'extraction_factor': 1.0},
            'summer': {'rainfall_factor': 0.6, 'evaporation_factor': 1.5, 'extraction_factor': 1.3},
            'autumn': {'rainfall_factor': 1.0, 'evaporation_factor': 1.0, 'extraction_factor': 1.1},
            'winter': {'rainfall_factor': 0.4, 'evaporation_factor': 0.5, 'extraction_factor': 0.9}
        }

    def get_season(self, date: datetime.date) -> str:
        """根据日期获取季节"""
        month = date.month
        if month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        elif month in [9, 10, 11]:
            return 'autumn'
        else:
            return 'winter'

    def generate_water_level_data(self, station: Dict, date: datetime.date, prev_data: Dict = None) -> Dict:
        """生成单条水位数据"""
        season = self.get_season(date)
        factors = self.seasonal_factors[season]
        
        # 基础降雨量（毫米）
        base_rainfall = random.uniform(0, 40) * factors['rainfall_factor']
        
        # 基础蒸发量（毫米）
        base_evaporation = random.uniform(3, 25) * factors['evaporation_factor']
        
        # 基础开采量（立方米）
        base_extraction = random.uniform(300, 800) * factors['extraction_factor']
        
        # 地表径流量（立方米）
        surface_runoff = base_rainfall * random.uniform(30, 80)
        
        # 土壤含水率（%）
        soil_moisture = station['soil_moisture_base'] + random.uniform(-8, 8)
        
        # 水位计算（考虑前一天的影响）
        if prev_data:
            # 水位变化受降雨、蒸发、开采影响
            level_change = (base_rainfall - base_evaporation) * 0.01 - base_extraction * 0.0001
            level_change += random.uniform(-0.05, 0.05)  # 随机波动
            
            raw_water_level = prev_data['raw_water_level'] + level_change
            groundwater_level = prev_data['groundwater_level'] + level_change * 0.8
        else:
            # 初始数据
            raw_water_level = station['base_raw_level'] + random.uniform(-0.5, 0.5)
            groundwater_level = station['base_ground_level'] + random.uniform(-0.3, 0.3)
        
        # 确保水位不为负
        raw_water_level = max(0.1, raw_water_level)
        groundwater_level = max(0.1, groundwater_level)
        
        # 液位变化
        if prev_data:
            level_change = groundwater_level - prev_data['groundwater_level']
        else:
            level_change = 0
        
        # 生成备注
        remarks = self.generate_remark(base_rainfall, base_evaporation, level_change)
        
        return {
            'id': f"cwl_{uuid.uuid4().hex[:8]}",
            'station_id': station['id'],
            'station_name': station['name'],
            'station_location': station['location'],
            'raw_water_level': round(raw_water_level, 3),
            'groundwater_level': round(groundwater_level, 3),
            'level_change': round(level_change, 3),
            'rainfall_amount': round(base_rainfall, 1),
            'evaporation_amount': round(base_evaporation, 1),
            'surface_runoff': round(surface_runoff, 1),
            'extraction_amount': round(base_extraction, 1),
            'soil_moisture': round(soil_moisture, 1),
            'permeability_coefficient': station['permeability'],
            'record_time': date.strftime('%Y-%m-%d 08:00:00'),
            'remark': remarks,
            'data_source': random.choice([1, 1, 1, 2]),  # 75%手动，25%设备
            'creator': 'admin' if random.random() > 0.2 else 'operator',
            'creator_name': '系统管理员' if random.random() > 0.2 else '操作员'
        }

    def generate_remark(self, rainfall: float, evaporation: float, level_change: float) -> str:
        """生成备注信息"""
        remarks = []
        
        if rainfall > 25:
            remarks.append("强降雨")
        elif rainfall > 15:
            remarks.append("中雨")
        elif rainfall > 5:
            remarks.append("小雨")
        else:
            remarks.append("无降雨")
            
        if evaporation > 20:
            remarks.append("蒸发量大")
        elif evaporation > 15:
            remarks.append("蒸发量较大")
            
        if level_change > 0.1:
            remarks.append("水位上升")
        elif level_change < -0.1:
            remarks.append("水位下降")
        else:
            remarks.append("水位稳定")
            
        if level_change < -0.2:
            remarks.append("需要关注")
        elif level_change < -0.3:
            remarks.append("需要干预")
            
        return "，".join(remarks)

    def generate_analysis_data(self, station_data: List[Dict], station: Dict) -> Dict:
        """生成分析结果数据"""
        if len(station_data) < 2:
            return None
            
        start_data = station_data[0]
        end_data = station_data[-1]
        
        # 计算统计值
        avg_rainfall = sum(d['rainfall_amount'] for d in station_data) / len(station_data)
        avg_evaporation = sum(d['evaporation_amount'] for d in station_data) / len(station_data)
        total_extraction = sum(d['extraction_amount'] for d in station_data)
        
        level_change = end_data['groundwater_level'] - start_data['groundwater_level']
        
        # 涵养系数计算
        if level_change != 0:
            conservation_coefficient = (avg_rainfall - avg_evaporation) / abs(level_change) * 0.01
        else:
            conservation_coefficient = 0.05
            
        # 涵养潜力评分 (0-100)
        base_score = 50
        if level_change > 0:
            base_score += min(level_change * 100, 30)
        else:
            base_score += max(level_change * 100, -30)
            
        if avg_rainfall > 15:
            base_score += 10
        elif avg_rainfall < 5:
            base_score -= 15
            
        conservation_potential = max(0, min(100, base_score))
        
        # 风险等级评估
        risk_score = 0
        if level_change < -0.2:
            risk_score += 2
        if total_extraction > len(station_data) * 600:
            risk_score += 1
        if avg_rainfall < 8:
            risk_score += 1
        if conservation_potential < 40:
            risk_score += 1
            
        risk_level = min(3, max(1, risk_score))
        
        # 生成建议
        suggestions = []
        if risk_level == 3:
            suggestions.append("紧急建议：立即减少开采量")
            suggestions.append("增加人工回灌")
        elif risk_level == 2:
            suggestions.append("建议控制开采量")
            suggestions.append("加强监测")
        else:
            suggestions.append("维持现有管理措施")
            
        if avg_rainfall < 10:
            suggestions.append("加强雨水收集利用")
            
        conservation_suggestion = "；".join(suggestions) + "。"
        
        # 风险描述
        risk_descriptions = {
            1: "低风险：水位稳定，涵养状况良好",
            2: "中等风险：水位有下降趋势，需要采取措施",
            3: "高风险：水位持续下降，急需干预"
        }
        
        return {
            'id': f"ca_{uuid.uuid4().hex[:8]}",
            'station_id': station['id'],
            'station_name': station['name'],
            'start_time': start_data['record_time'],
            'end_time': end_data['record_time'],
            'initial_level': start_data['groundwater_level'],
            'final_level': end_data['groundwater_level'],
            'level_change': round(level_change, 3),
            'avg_rainfall': round(avg_rainfall, 2),
            'avg_evaporation': round(avg_evaporation, 2),
            'total_extraction': round(total_extraction, 1),
            'conservation_coefficient': round(conservation_coefficient, 4),
            'conservation_potential': round(conservation_potential, 1),
            'suggested_conservation_amount': round(abs(level_change) * 1000 + 500, 0),
            'conservation_suggestion': conservation_suggestion,
            'risk_level': risk_level,
            'risk_description': risk_descriptions[risk_level],
            'status': 2  # 已完成
        }

    def generate_data(self, days: int = 30) -> tuple:
        """生成指定天数的数据"""
        water_level_data = []
        analysis_data = []
        
        start_date = datetime.date.today() - datetime.timedelta(days=days)
        
        # 为每个测点生成数据
        station_data_map = {}
        
        for i in range(days):
            current_date = start_date + datetime.timedelta(days=i)
            
            for station in self.stations:
                station_id = station['id']
                
                # 获取前一天的数据
                prev_data = None
                if station_id in station_data_map and station_data_map[station_id]:
                    prev_data = station_data_map[station_id][-1]
                
                # 生成当天数据
                daily_data = self.generate_water_level_data(station, current_date, prev_data)
                water_level_data.append(daily_data)
                
                # 保存到站点数据映射
                if station_id not in station_data_map:
                    station_data_map[station_id] = []
                station_data_map[station_id].append(daily_data)
        
        # 生成分析数据（每7天一次分析）
        for station in self.stations:
            station_id = station['id']
            station_records = station_data_map[station_id]
            
            # 每7天生成一次分析
            for i in range(0, len(station_records), 7):
                end_idx = min(i + 7, len(station_records))
                if end_idx - i >= 3:  # 至少3天数据才分析
                    analysis_period_data = station_records[i:end_idx]
                    analysis_result = self.generate_analysis_data(analysis_period_data, station)
                    if analysis_result:
                        analysis_data.append(analysis_result)
        
        return water_level_data, analysis_data

    def export_to_sql(self, water_level_data: List[Dict], analysis_data: List[Dict], filename: str):
        """导出为SQL文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("-- 涵养水位测试数据\n")
            f.write("-- 生成时间: {}\n\n".format(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            
            # 水位数据
            f.write("-- 插入涵养水位数据\n")
            for data in water_level_data:
                sql = f"""INSERT INTO conservation_water_level (
    id, tenant_id, station_id, station_name, station_location,
    raw_water_level, groundwater_level, level_change,
    rainfall_amount, evaporation_amount, surface_runoff, extraction_amount,
    soil_moisture, permeability_coefficient, record_time,
    create_time, update_time, remark, data_source, creator, creator_name
) VALUES (
    '{data['id']}', 'default', '{data['station_id']}', '{data['station_name']}', '{data['station_location']}',
    {data['raw_water_level']}, {data['groundwater_level']}, {data['level_change']},
    {data['rainfall_amount']}, {data['evaporation_amount']}, {data['surface_runoff']}, {data['extraction_amount']},
    {data['soil_moisture']}, {data['permeability_coefficient']}, '{data['record_time']}',
    '{data['record_time']}', '{data['record_time']}', '{data['remark']}', {data['data_source']}, '{data['creator']}', '{data['creator_name']}'
);
"""
                f.write(sql)
            
            # 分析数据
            f.write("\n-- 插入分析结果数据\n")
            for data in analysis_data:
                sql = f"""INSERT INTO conservation_analysis (
    id, tenant_id, station_id, station_name, start_time, end_time,
    initial_level, final_level, level_change, avg_rainfall, avg_evaporation, total_extraction,
    conservation_coefficient, conservation_potential, suggested_conservation_amount,
    conservation_suggestion, risk_level, risk_description, algorithm_version,
    create_time, update_time, status, creator, creator_name
) VALUES (
    '{data['id']}', 'default', '{data['station_id']}', '{data['station_name']}', '{data['start_time']}', '{data['end_time']}',
    {data['initial_level']}, {data['final_level']}, {data['level_change']}, {data['avg_rainfall']}, {data['avg_evaporation']}, {data['total_extraction']},
    {data['conservation_coefficient']}, {data['conservation_potential']}, {data['suggested_conservation_amount']},
    '{data['conservation_suggestion']}', {data['risk_level']}, '{data['risk_description']}', 'v1.2.0',
    '{data['end_time']}', '{data['end_time']}', {data['status']}, 'admin', '系统管理员'
);
"""
                f.write(sql)
            
            f.write("\nCOMMIT;\n")

    def export_to_csv(self, water_level_data: List[Dict], filename: str):
        """导出为CSV文件（用于Excel导入）"""
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            headers = [
                '测点ID', '原水液位(米)', '地下水位(米)', '降雨量(毫米)', '蒸发量(毫米)',
                '地表径流量(立方米)', '地下水开采量(立方米)', '土壤含水率(%)', '渗透系数',
                '记录时间', '备注'
            ]
            writer.writerow(headers)
            
            # 写入数据
            for data in water_level_data:
                row = [
                    data['station_id'],
                    data['raw_water_level'],
                    data['groundwater_level'],
                    data['rainfall_amount'],
                    data['evaporation_amount'],
                    data['surface_runoff'],
                    data['extraction_amount'],
                    data['soil_moisture'],
                    data['permeability_coefficient'],
                    data['record_time'],
                    data['remark']
                ]
                writer.writerow(row)

if __name__ == "__main__":
    generator = ConservationDataGenerator()
    
    # 生成30天的数据
    print("正在生成涵养水位测试数据...")
    water_level_data, analysis_data = generator.generate_data(30)
    
    print(f"生成了 {len(water_level_data)} 条水位数据")
    print(f"生成了 {len(analysis_data)} 条分析数据")
    
    # 导出SQL文件
    generator.export_to_sql(water_level_data, analysis_data, "conservation_water_level_generated.sql")
    print("SQL文件已导出: conservation_water_level_generated.sql")
    
    # 导出CSV文件
    generator.export_to_csv(water_level_data, "conservation_water_level_generated.csv")
    print("CSV文件已导出: conservation_water_level_generated.csv")
    
    print("数据生成完成！")
