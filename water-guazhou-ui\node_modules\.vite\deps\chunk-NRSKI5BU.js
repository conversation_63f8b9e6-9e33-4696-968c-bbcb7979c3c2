import {
  b,
  h
} from "./chunk-P37TUI4J.js";
import {
  m
} from "./chunk-37DYRJVQ.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import {
  t
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/core/loadAll.js
async function l(o, r) {
  return await o.load(), i(o, r);
}
async function i(l2, i2) {
  const c = [], f = (...o) => {
    for (const r of o) t(r) || (Array.isArray(r) ? f(...r) : j.isCollection(r) ? r.forEach((o2) => f(o2)) : m.isLoadable(r) && c.push(r));
  };
  i2(f);
  let e = null;
  if (await h(c, async (o) => {
    const t2 = await b(s(o) ? o.loadAll() : o.load());
    false !== t2.ok || e || (e = t2);
  }), e) throw e.error;
  return l2;
}
function s(o) {
  return "loadAll" in o && "function" == typeof o.loadAll;
}

export {
  l,
  i
};
//# sourceMappingURL=chunk-NRSKI5BU.js.map
