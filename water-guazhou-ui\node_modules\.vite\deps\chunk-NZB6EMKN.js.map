{"version": 3, "sources": ["../../@arcgis/core/layers/support/CodedValue.js", "../../@arcgis/core/layers/support/Domain.js", "../../@arcgis/core/layers/support/CodedValueDomain.js", "../../@arcgis/core/layers/support/InheritedDomain.js", "../../@arcgis/core/layers/support/RangeDomain.js", "../../@arcgis/core/layers/support/domains.js", "../../@arcgis/core/layers/support/Field.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as e}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";var t;let p=t=class extends o{constructor(r){super(r),this.name=null,this.code=null}clone(){return new t({name:this.name,code:this.code})}};r([e({type:String,json:{write:!0}})],p.prototype,\"name\",void 0),r([e({type:[String,Number],json:{write:!0}})],p.prototype,\"code\",void 0),p=t=r([s(\"esri.layers.support.CodedValue\")],p);export{p as CodedValue};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import{JSONMap as o}from\"../../core/jsonMap.js\";import{JSONSupport as e}from\"../../core/JSONSupport.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{enumeration as t}from\"../../core/accessorSupport/decorators/enumeration.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";const c=new o({inherited:\"inherited\",codedValue:\"coded-value\",range:\"range\"});let a=class extends e{constructor(r){super(r),this.name=null,this.type=null}};r([s({type:String,json:{write:!0}})],a.prototype,\"name\",void 0),r([t(c)],a.prototype,\"type\",void 0),a=r([p(\"esri.layers.support.Domain\")],a);const i=a;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{clone as o}from\"../../core/lang.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{enumeration as s}from\"../../core/accessorSupport/decorators/enumeration.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import{CodedValue as c}from\"./CodedValue.js\";import a from\"./Domain.js\";var d;let p=d=class extends a{constructor(e){super(e),this.codedValues=null,this.type=\"coded-value\"}getName(e){let o=null;if(this.codedValues){const r=String(e);this.codedValues.some((e=>(String(e.code)===r&&(o=e.name),!!o)))}return o}clone(){return new d({codedValues:o(this.codedValues),name:this.name})}};e([r({type:[c],json:{write:!0}})],p.prototype,\"codedValues\",void 0),e([s({codedValue:\"coded-value\"})],p.prototype,\"type\",void 0),p=d=e([t(\"esri.layers.support.CodedValueDomain\")],p);const u=p;export{u as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import\"../../core/Logger.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import\"../../core/Error.js\";import\"../../core/has.js\";import{enumeration as o}from\"../../core/accessorSupport/decorators/enumeration.js\";import{subclass as e}from\"../../core/accessorSupport/decorators/subclass.js\";import s from\"./Domain.js\";var t;let i=t=class extends s{constructor(r){super(r),this.type=\"inherited\"}clone(){return new t}};r([o({inherited:\"inherited\"})],i.prototype,\"type\",void 0),i=t=r([e(\"esri.layers.support.InheritedDomain\")],i);const p=i;export{p as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{enumeration as a}from\"../../core/accessorSupport/decorators/enumeration.js\";import{subclass as t}from\"../../core/accessorSupport/decorators/subclass.js\";import o from\"./Domain.js\";var s;let n=s=class extends o{constructor(e){super(e),this.maxValue=null,this.minValue=null,this.type=\"range\"}clone(){return new s({maxValue:this.maxValue,minValue:this.minValue,name:this.name})}};e([r({type:Number,json:{type:[Number],read:{source:\"range\",reader:(e,r)=>r.range&&r.range[1]},write:{enabled:!1,overridePolicy(){return{enabled:null!=this.maxValue&&null==this.minValue}},target:\"range\",writer(e,r,a){r[a]=[this.minValue||0,e]}}}})],n.prototype,\"maxValue\",void 0),e([r({type:Number,json:{type:[Number],read:{source:\"range\",reader:(e,r)=>r.range&&r.range[0]},write:{target:\"range\",writer(e,r,a){r[a]=[e,this.maxValue||0]}}}})],n.prototype,\"minValue\",void 0),e([a({range:\"range\"})],n.prototype,\"type\",void 0),n=s=e([t(\"esri.layers.support.RangeDomain\")],n);const i=n;export{i as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../core/has.js\";import e from\"./CodedValueDomain.js\";import r from\"./Domain.js\";import o from\"./InheritedDomain.js\";import t from\"./RangeDomain.js\";const n={key:\"type\",base:r,typeMap:{range:t,\"coded-value\":e,inherited:o}};function i(r){if(!r||!r.type)return null;switch(r.type){case\"range\":return t.fromJSON(r);case\"codedValue\":return e.fromJSON(r);case\"inherited\":return o.fromJSON(r)}return null}export{e as CodedValueDomain,r as DomainBase,o as InheritedDomain,t as RangeDomain,i as fromJSON,n as types};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{JSONMap as t}from\"../../core/jsonMap.js\";import{JSONSupport as o}from\"../../core/JSONSupport.js\";import{property as r}from\"../../core/accessorSupport/decorators/property.js\";import{Integer as i}from\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{enumeration as a}from\"../../core/accessorSupport/decorators/enumeration.js\";import{reader as n}from\"../../core/accessorSupport/decorators/reader.js\";import{subclass as s}from\"../../core/accessorSupport/decorators/subclass.js\";import{types as l,fromJSON as p}from\"./domains.js\";import{kebabDict as d}from\"./fieldType.js\";var u;const c=new t({binary:\"binary\",coordinate:\"coordinate\",countOrAmount:\"count-or-amount\",dateAndTime:\"date-and-time\",description:\"description\",locationOrPlaceName:\"location-or-place-name\",measurement:\"measurement\",nameOrTitle:\"name-or-title\",none:\"none\",orderedOrRanked:\"ordered-or-ranked\",percentageOrRatio:\"percentage-or-ratio\",typeOrCategory:\"type-or-category\",uniqueIdentifier:\"unique-identifier\"});let m=u=class extends o{constructor(e){super(e),this.alias=null,this.defaultValue=void 0,this.description=null,this.domain=null,this.editable=!0,this.length=-1,this.name=null,this.nullable=!0,this.type=null,this.valueType=null,this.visible=!0}readDescription(e,{description:t}){let o=null;try{o=t?JSON.parse(t):null}catch(r){}return o?.value??null}readValueType(e,{description:t}){let o=null;try{o=t?JSON.parse(t):null}catch(r){}return o?c.fromJSON(o.fieldValueType):null}clone(){return new u({alias:this.alias,defaultValue:this.defaultValue,description:this.description,domain:this.domain&&this.domain.clone()||null,editable:this.editable,length:this.length,name:this.name,nullable:this.nullable,type:this.type,valueType:this.valueType,visible:this.visible})}};e([r({type:String,json:{write:!0}})],m.prototype,\"alias\",void 0),e([r({type:[String,Number],json:{write:{allowNull:!0}}})],m.prototype,\"defaultValue\",void 0),e([r()],m.prototype,\"description\",void 0),e([n(\"description\")],m.prototype,\"readDescription\",null),e([r({types:l,json:{read:{reader:p},write:!0}})],m.prototype,\"domain\",void 0),e([r({type:Boolean,json:{write:!0}})],m.prototype,\"editable\",void 0),e([r({type:i,json:{write:!0}})],m.prototype,\"length\",void 0),e([r({type:String,json:{write:!0}})],m.prototype,\"name\",void 0),e([r({type:Boolean,json:{write:!0}})],m.prototype,\"nullable\",void 0),e([a(d)],m.prototype,\"type\",void 0),e([r()],m.prototype,\"valueType\",void 0),e([n(\"valueType\",[\"description\"])],m.prototype,\"readValueType\",null),e([r({type:Boolean,json:{read:!1}})],m.prototype,\"visible\",void 0),m=u=e([s(\"esri.layers.support.Field\")],m);const y=m;export{y as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIkV,IAAI;AAAE,IAAIA,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK,MAAK,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,MAAK,KAAK,MAAK,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,gCAAgC,CAAC,GAAEA,EAAC;;;ACA/L,IAAM,IAAE,IAAI,EAAE,EAAC,WAAU,aAAY,YAAW,eAAc,OAAM,QAAO,CAAC;AAAE,IAAIC,KAAE,cAAc,EAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK,MAAK,KAAK,OAAK;AAAA,EAAI;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAACC,GAAE,CAAC,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,4BAA4B,CAAC,GAAEA,EAAC;AAAE,IAAME,KAAEF;;;ACAvU,IAAI;AAAE,IAAIG,KAAE,IAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,cAAY,MAAK,KAAK,OAAK;AAAA,EAAa;AAAA,EAAC,QAAQA,IAAE;AAAC,QAAIC,KAAE;AAAK,QAAG,KAAK,aAAY;AAAC,YAAM,IAAE,OAAOD,EAAC;AAAE,WAAK,YAAY,KAAM,CAAAA,QAAI,OAAOA,GAAE,IAAI,MAAI,MAAIC,KAAED,GAAE,OAAM,CAAC,CAACC,GAAG;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,aAAY,EAAE,KAAK,WAAW,GAAE,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACH,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAACG,GAAE,EAAC,YAAW,cAAa,CAAC,CAAC,GAAEH,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,IAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;;;ACAniB,IAAII;AAAE,IAAIC,KAAED,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAY,GAAE;AAAC,UAAM,CAAC,GAAE,KAAK,OAAK;AAAA,EAAW;AAAA,EAAC,QAAO;AAAC,WAAO,IAAID;AAAA,EAAC;AAAC;AAAE,EAAE,CAACE,GAAE,EAAC,WAAU,YAAW,CAAC,CAAC,GAAED,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,GAAEC,EAAC;AAAE,IAAME,KAAEF;;;ACA/N,IAAIG;AAAE,IAAI,IAAEA,KAAE,cAAcC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,MAAK,KAAK,WAAS,MAAK,KAAK,OAAK;AAAA,EAAO;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIF,GAAE,EAAC,UAAS,KAAK,UAAS,UAAS,KAAK,UAAS,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,QAAO,SAAQ,QAAO,CAACE,IAAE,MAAI,EAAE,SAAO,EAAE,MAAM,CAAC,EAAC,GAAE,OAAM,EAAC,SAAQ,OAAG,iBAAgB;AAAC,SAAM,EAAC,SAAQ,QAAM,KAAK,YAAU,QAAM,KAAK,SAAQ;AAAC,GAAE,QAAO,SAAQ,OAAOA,IAAE,GAAEC,IAAE;AAAC,IAAEA,EAAC,IAAE,CAAC,KAAK,YAAU,GAAED,EAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,CAAC,MAAM,GAAE,MAAK,EAAC,QAAO,SAAQ,QAAO,CAACA,IAAE,MAAI,EAAE,SAAO,EAAE,MAAM,CAAC,EAAC,GAAE,OAAM,EAAC,QAAO,SAAQ,OAAOA,IAAE,GAAEC,IAAE;AAAC,IAAEA,EAAC,IAAE,CAACD,IAAE,KAAK,YAAU,CAAC;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACE,GAAE,EAAC,OAAM,QAAO,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAEJ,KAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAE;;;ACAj/B,IAAMI,KAAE,EAAC,KAAI,QAAO,MAAKC,IAAE,SAAQ,EAAC,OAAMA,IAAE,eAAc,GAAE,WAAUC,GAAC,EAAC;AAAE,SAASD,GAAE,GAAE;AAAC,MAAG,CAAC,KAAG,CAAC,EAAE,KAAK,QAAO;AAAK,UAAO,EAAE,MAAK;AAAA,IAAC,KAAI;AAAQ,aAAOA,GAAE,SAAS,CAAC;AAAA,IAAE,KAAI;AAAa,aAAO,EAAE,SAAS,CAAC;AAAA,IAAE,KAAI;AAAY,aAAOC,GAAE,SAAS,CAAC;AAAA,EAAC;AAAC,SAAO;AAAI;;;ACAuP,IAAIC;AAAE,IAAMC,KAAE,IAAI,EAAE,EAAC,QAAO,UAAS,YAAW,cAAa,eAAc,mBAAkB,aAAY,iBAAgB,aAAY,eAAc,qBAAoB,0BAAyB,aAAY,eAAc,aAAY,iBAAgB,MAAK,QAAO,iBAAgB,qBAAoB,mBAAkB,uBAAsB,gBAAe,oBAAmB,kBAAiB,oBAAmB,CAAC;AAAE,IAAI,IAAED,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,eAAa,QAAO,KAAK,cAAY,MAAK,KAAK,SAAO,MAAK,KAAK,WAAS,MAAG,KAAK,SAAO,IAAG,KAAK,OAAK,MAAK,KAAK,WAAS,MAAG,KAAK,OAAK,MAAK,KAAK,YAAU,MAAK,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAE,EAAC,aAAYC,GAAC,GAAE;AAAC,QAAIC,KAAE;AAAK,QAAG;AAAC,MAAAA,KAAED,KAAE,KAAK,MAAMA,EAAC,IAAE;AAAA,IAAI,SAAO,GAAE;AAAA,IAAC;AAAC,YAAOC,MAAA,gBAAAA,GAAG,UAAO;AAAA,EAAI;AAAA,EAAC,cAAcF,IAAE,EAAC,aAAYC,GAAC,GAAE;AAAC,QAAIC,KAAE;AAAK,QAAG;AAAC,MAAAA,KAAED,KAAE,KAAK,MAAMA,EAAC,IAAE;AAAA,IAAI,SAAO,GAAE;AAAA,IAAC;AAAC,WAAOC,KAAEH,GAAE,SAASG,GAAE,cAAc,IAAE;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIJ,GAAE,EAAC,OAAM,KAAK,OAAM,cAAa,KAAK,cAAa,aAAY,KAAK,aAAY,QAAO,KAAK,UAAQ,KAAK,OAAO,MAAM,KAAG,MAAK,UAAS,KAAK,UAAS,QAAO,KAAK,QAAO,MAAK,KAAK,MAAK,UAAS,KAAK,UAAS,MAAK,KAAK,MAAK,WAAU,KAAK,WAAU,SAAQ,KAAK,QAAO,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,MAAK,EAAC,OAAM,EAAC,WAAU,KAAE,EAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,aAAa,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,OAAMK,IAAE,MAAK,EAAC,MAAK,EAAC,QAAOC,GAAC,GAAE,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACF,GAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,aAAY,CAAC,aAAa,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,IAAEJ,KAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,GAAE,CAAC;AAAE,IAAMO,KAAE;", "names": ["p", "a", "o", "i", "p", "i", "e", "o", "t", "i", "o", "p", "s", "i", "e", "a", "o", "n", "i", "p", "u", "c", "e", "t", "o", "n", "i", "y"]}