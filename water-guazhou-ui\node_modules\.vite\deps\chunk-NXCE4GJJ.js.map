{"version": 3, "sources": ["../../@arcgis/core/views/3d/webgl-engine/core/shaderLibrary/ScreenSizeScaling.glsl.js", "../../@arcgis/core/chunks/ShadedColorMaterial.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{addCameraPosition as e}from\"./util/View.glsl.js\";import{FloatPassUniform as i}from\"../shaderModules/FloatPassUniform.js\";import{glsl as r}from\"../shaderModules/interfaces.js\";function o(o,n){if(!n.screenSizeEnabled)return;const c=o.vertex;e(c,n),c.uniforms.add(new i(\"perScreenPixelRatio\",((e,i)=>i.camera.perScreenPixelRatio))),c.uniforms.add(new i(\"screenSizeScale\",(e=>e.screenSizeScale))),c.code.add(r`float computeRenderPixelSizeAt( vec3 pWorld ){\nvec3 viewForward = - vec3(view[0][2], view[1][2], view[2][2]);\nfloat viewDirectionDistance = abs(dot(viewForward, pWorld - cameraPosition));\nreturn viewDirectionDistance * perScreenPixelRatio;\n}\nvec3 screenSizeScaling(vec3 position, vec3 anchor){\nreturn position * screenSizeScale * computeRenderPixelSizeAt(anchor) + anchor;\n}`)}export{o as ScreenSizeScaling};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as e}from\"./vec4f64.js\";import{ScreenSizeScaling as o}from\"../views/3d/webgl-engine/core/shaderLibrary/ScreenSizeScaling.glsl.js\";import{ShaderOutput as r}from\"../views/3d/webgl-engine/core/shaderLibrary/ShaderOutput.js\";import{SliceDraw as i}from\"../views/3d/webgl-engine/core/shaderLibrary/Slice.glsl.js\";import{Transform as a}from\"../views/3d/webgl-engine/core/shaderLibrary/Transform.glsl.js\";import{multipassTerrainTest as l}from\"../views/3d/webgl-engine/core/shaderLibrary/shading/MultipassTerrainTest.glsl.js\";import{symbolAlpha<PERSON>utoff as d}from\"../views/3d/webgl-engine/core/shaderLibrary/util/AlphaCutoff.js\";import{ColorConversion as n}from\"../views/3d/webgl-engine/core/shaderLibrary/util/ColorConversion.glsl.js\";import{addProjViewLocalOrigin as s,addViewNormal as t}from\"../views/3d/webgl-engine/core/shaderLibrary/util/View.glsl.js\";import{Float3PassUniform as c}from\"../views/3d/webgl-engine/core/shaderModules/Float3PassUniform.js\";import{Float4PassUniform as g}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{glsl as m}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as v}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";import{TransparencyPassType as u}from\"../views/3d/webgl-engine/lib/TransparencyPassType.js\";import{VertexAttribute as p}from\"../views/3d/webgl-engine/lib/VertexAttribute.js\";function f(e){const f=new v,b=e.hasMultipassTerrain&&(e.output===r.Color||e.output===r.Alpha);f.include(a,e),f.include(o,e),f.include(i,e);const{vertex:h,fragment:C}=f;return C.include(n),s(h,e),C.uniforms.add(new g(\"uColor\",(e=>e.color))),f.attributes.add(p.POSITION,\"vec3\"),f.varyings.add(\"vWorldPosition\",\"vec3\"),b&&f.varyings.add(\"depth\",\"float\"),e.screenSizeEnabled&&f.attributes.add(p.OFFSET,\"vec3\"),e.shadingEnabled&&(t(h),f.attributes.add(p.NORMAL,\"vec3\"),f.varyings.add(\"vViewNormal\",\"vec3\")),h.code.add(m`\n    void main(void) {\n      vWorldPosition = ${e.screenSizeEnabled?\"screenSizeScaling(offset, position)\":\"position\"};\n  `),e.shadingEnabled&&h.code.add(m`vec3 worldNormal = normal;\nvViewNormal = (viewNormal * vec4(worldNormal, 1)).xyz;`),h.code.add(m`\n    ${b?\"depth = (view * vec4(vWorldPosition, 1.0)).z;\":\"\"}\n    gl_Position = transformPosition(proj, view, vWorldPosition);\n  }\n  `),b&&f.include(l,e),C.code.add(m`\n    void main() {\n      discardBySlice(vWorldPosition);\n      ${b?\"terrainDepthTest(gl_FragCoord, depth);\":\"\"}\n    `),e.shadingEnabled?(C.uniforms.add(new c(\"shadingDirection\",(e=>e.shadingDirection))),C.uniforms.add(new g(\"shadedColor\",(e=>w(e.shadingTint,e.color)))),C.code.add(m`vec3 viewNormalNorm = normalize(vViewNormal);\nfloat shadingFactor = 1.0 - clamp(-dot(viewNormalNorm, shadingDirection), 0.0, 1.0);\nvec4 finalColor = mix(uColor, shadedColor, shadingFactor);`)):C.code.add(m`vec4 finalColor = uColor;`),C.code.add(m`\n      ${e.output===r.ObjectAndLayerIdColor?m`finalColor.a = 1.0;`:\"\"}\n      if (finalColor.a < ${m.float(d)}) {\n        discard;\n      }\n      ${e.output===r.Alpha?m`gl_FragColor = vec4(finalColor.a);`:\"\"}\n\n      ${e.output===r.Color?m`gl_FragColor = highlightSlice(finalColor, vWorldPosition); ${e.transparencyPassType===u.Color?\"gl_FragColor = premultiplyAlpha(gl_FragColor);\":\"\"}`:\"\"}\n    }\n    `),f}function w(e,o){const r=1-e[3],i=e[3]+o[3]*r;return 0===i?(b[3]=i,b):(b[0]=(e[0]*e[3]+o[0]*o[3]*r)/i,b[1]=(e[1]*e[3]+o[1]*o[3]*r)/i,b[2]=(e[2]*e[3]+o[2]*o[3]*r)/i,b[3]=o[3],b)}const b=e(),h=Object.freeze(Object.defineProperty({__proto__:null,build:f},Symbol.toStringTag,{value:\"Module\"}));export{h as S,f as b};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIsL,SAASA,GAAEA,IAAEC,IAAE;AAAC,MAAG,CAACA,GAAE,kBAAkB;AAAO,QAAMC,KAAEF,GAAE;AAAO,IAAEE,IAAED,EAAC,GAAEC,GAAE,SAAS,IAAI,IAAIF,GAAE,uBAAuB,CAACG,IAAE,MAAI,EAAE,OAAO,mBAAoB,CAAC,GAAED,GAAE,SAAS,IAAI,IAAIF,GAAE,mBAAmB,CAAAG,OAAGA,GAAE,eAAgB,CAAC,GAAED,GAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzZ;AAAC;;;ACP03C,SAAS,EAAEE,IAAE;AAAC,QAAMC,KAAE,IAAIC,MAAEC,KAAEH,GAAE,wBAAsBA,GAAE,WAAS,EAAE,SAAOA,GAAE,WAAS,EAAE;AAAO,EAAAC,GAAE,QAAQ,GAAED,EAAC,GAAEC,GAAE,QAAQC,IAAEF,EAAC,GAAEC,GAAE,QAAQ,GAAED,EAAC;AAAE,QAAK,EAAC,QAAOI,IAAE,UAAS,EAAC,IAAEH;AAAE,SAAO,EAAE,QAAQD,EAAC,GAAE,EAAEI,IAAEJ,EAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,UAAU,CAAAA,OAAGA,GAAE,KAAM,CAAC,GAAEC,GAAE,WAAW,IAAI,EAAE,UAAS,MAAM,GAAEA,GAAE,SAAS,IAAI,kBAAiB,MAAM,GAAEE,MAAGF,GAAE,SAAS,IAAI,SAAQ,OAAO,GAAED,GAAE,qBAAmBC,GAAE,WAAW,IAAI,EAAE,QAAO,MAAM,GAAED,GAAE,mBAAiB,EAAEI,EAAC,GAAEH,GAAE,WAAW,IAAI,EAAE,QAAO,MAAM,GAAEA,GAAE,SAAS,IAAI,eAAc,MAAM,IAAGG,GAAE,KAAK,IAAI;AAAA;AAAA,yBAEr2DJ,GAAE,oBAAkB,wCAAsC,UAAU;AAAA,GAC1F,GAAEA,GAAE,kBAAgBI,GAAE,KAAK,IAAI;AAAA,uDACqB,GAAEA,GAAE,KAAK,IAAI;AAAA,MAC9DD,KAAE,kDAAgD,EAAE;AAAA;AAAA;AAAA,GAGvD,GAAEA,MAAGF,GAAE,QAAQI,IAAEL,EAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,QAG1BG,KAAE,2CAAyC,EAAE;AAAA,KAChD,GAAEH,GAAE,kBAAgB,EAAE,SAAS,IAAI,IAAIA,GAAE,oBAAoB,CAAAA,OAAGA,GAAE,gBAAiB,CAAC,GAAE,EAAE,SAAS,IAAI,IAAI,EAAE,eAAe,CAAAA,OAAG,EAAEA,GAAE,aAAYA,GAAE,KAAK,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA,2DAE9G,KAAG,EAAE,KAAK,IAAI,4BAA4B,GAAE,EAAE,KAAK,IAAI;AAAA,QAC1GA,GAAE,WAAS,EAAE,wBAAsB,yBAAuB,EAAE;AAAA,2BACzC,EAAE,MAAM,CAAC,CAAC;AAAA;AAAA;AAAA,QAG7BA,GAAE,WAAS,EAAE,QAAM,wCAAsC,EAAE;AAAA;AAAA,QAE3DA,GAAE,WAAS,EAAE,QAAM,+DAA+DA,GAAE,yBAAuBE,GAAE,QAAM,mDAAiD,EAAE,KAAG,EAAE;AAAA;AAAA,KAE9K,GAAED;AAAC;AAAC,SAAS,EAAED,IAAEE,IAAE;AAAC,QAAMI,KAAE,IAAEN,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEI;AAAE,SAAO,MAAI,KAAG,EAAE,CAAC,IAAE,GAAE,MAAI,EAAE,CAAC,KAAGN,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEI,MAAG,GAAE,EAAE,CAAC,KAAGN,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEI,MAAG,GAAE,EAAE,CAAC,KAAGN,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEE,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAEI,MAAG,GAAE,EAAE,CAAC,IAAEJ,GAAE,CAAC,GAAE;AAAE;AAAC,IAAM,IAAE,EAAE;AAAV,IAAYE,KAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["o", "n", "c", "e", "e", "f", "o", "b", "h", "n", "r"]}