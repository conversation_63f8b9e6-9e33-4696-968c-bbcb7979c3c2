import {
  R as R2
} from "./chunk-KYDW2SHL.js";
import {
  h
} from "./chunk-MSIU52YL.js";
import {
  e as e2
} from "./chunk-4CHRJPQP.js";
import {
  j
} from "./chunk-IEIKQ72S.js";
import {
  f as f2,
  u
} from "./chunk-I7WHRVHF.js";
import {
  R
} from "./chunk-JXLVNWKF.js";
import {
  e,
  v
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  E,
  f,
  w,
  x
} from "./chunk-EKX3LLYN.js";

// node_modules/@arcgis/core/views/2d/viewStateUtils.js
var t = Math.PI / 180;
function n(n2) {
  return n2 * t;
}
function o(t2, o2) {
  const a3 = n(o2.rotation), r = Math.abs(Math.cos(a3)), s = Math.abs(Math.sin(a3)), [u2, c] = o2.size;
  return t2[0] = Math.round(c * s + u2 * r), t2[1] = Math.round(c * r + u2 * s), t2;
}
function a2(t2, n2, o2, a3) {
  const [r, s] = n2, [u2, c] = a3, h2 = 0.5 * o2;
  return t2[0] = r - h2 * u2, t2[1] = s - h2 * c, t2[2] = r + h2 * u2, t2[3] = s + h2 * c, t2;
}

// node_modules/@arcgis/core/views/2d/layers/support/ExportStrategy.js
var y2 = u();
var x2 = [0, 0];
var S = new e2(0, 0, 0, 0);
var _ = { container: null, fetchSource: null, requestUpdate: null, imageMaxWidth: 2048, imageMaxHeight: 2048, imageRotationSupported: false, imageNormalizationSupported: false, hidpi: false };
var w2 = class extends v {
  constructor(t2) {
    super(t2), this._imagePromise = null, this.bitmaps = [], this.hidpi = _.hidpi, this.imageMaxWidth = _.imageMaxWidth, this.imageMaxHeight = _.imageMaxHeight, this.imageRotationSupported = _.imageRotationSupported, this.imageNormalizationSupported = _.imageNormalizationSupported, this.update = x(async (t3, e3) => {
      if (f(e3), !t3.stationary || this.destroyed) return;
      const i = t3.state, s = R(i.spatialReference), a3 = this.hidpi ? t3.pixelRatio : 1, n2 = this.imageNormalizationSupported && i.worldScreenWidth && i.worldScreenWidth < i.size[0], p = this.imageMaxWidth ?? 0, m = this.imageMaxHeight ?? 0;
      n2 ? (x2[0] = i.worldScreenWidth, x2[1] = i.size[1]) : this.imageRotationSupported ? (x2[0] = i.size[0], x2[1] = i.size[1]) : o(x2, i);
      const d = Math.floor(x2[0] * a3) > p || Math.floor(x2[1] * a3) > m, c = s && (i.extent.xmin < s.valid[0] || i.extent.xmax > s.valid[1]), u2 = !this.imageNormalizationSupported && c, g = !d && !u2, f3 = this.imageRotationSupported ? i.rotation : 0, y3 = this.container.children.slice();
      if (g) {
        const t4 = n2 ? i.paddedViewState.center : i.center;
        this._imagePromise && console.error("Image promise was not defined!"), this._imagePromise = this._singleExport(i, x2, t4, i.resolution, f3, a3, e3);
      } else {
        let t4 = Math.min(p, m);
        u2 && (t4 = Math.min(i.worldScreenWidth, t4)), this._imagePromise = this._tiledExport(i, t4, a3, e3);
      }
      try {
        const t4 = await this._imagePromise ?? [];
        f(e3);
        const i2 = [];
        if (this._imagePromise = null, this.destroyed) return;
        this.bitmaps = t4;
        for (const e4 of y3) t4.includes(e4) || i2.push(e4.fadeOut().then(() => {
          e4.remove(), e4.destroy();
        }));
        for (const e4 of t4) i2.push(e4.fadeIn());
        await Promise.all(i2);
      } catch (S2) {
        this._imagePromise = null, w(S2);
      }
    }, 5e3), this.updateExports = x(async (t3) => {
      const e3 = [];
      for (const i of this.container.children) {
        if (!i.visible || !i.stage) return;
        e3.push(t3(i).then(() => {
          i.invalidateTexture(), i.requestRender();
        }));
      }
      this._imagePromise = E(e3).then(() => this._imagePromise = null), await this._imagePromise;
    });
  }
  destroy() {
    this.bitmaps.forEach((t2) => t2.destroy()), this.bitmaps = [];
  }
  get updating() {
    return !this.destroyed && null !== this._imagePromise;
  }
  async _export(t2, e3, i, r, s, a3) {
    const n2 = await this.fetchSource(t2, Math.floor(e3 * s), Math.floor(i * s), { rotation: r, pixelRatio: s, signal: a3 });
    f(a3);
    const p = new R2(null, { immutable: true, requestRenderOnSourceChangedEnabled: true });
    return p.x = t2.xmin, p.y = t2.ymax, p.resolution = t2.width / e3, p.rotation = r, p.pixelRatio = s, p.opacity = 0, this.container.addChild(p), await p.setSourceAsync(n2, a3), f(a3), p;
  }
  async _singleExport(t2, e3, i, o2, r, s, a3) {
    a2(y2, i, o2, e3);
    const n2 = f2(y2, t2.spatialReference);
    return [await this._export(n2, e3[0], e3[1], r, s, a3)];
  }
  _tiledExport(t2, e3, i, o2) {
    const r = j.create({ size: e3, spatialReference: t2.spatialReference, scales: [t2.scale] }), s = new h(r), a3 = s.getTileCoverage(t2);
    if (!a3) return null;
    const n2 = [];
    return a3.forEach((r2, a4, p, l) => {
      S.set(r2, a4, p, 0), s.getTileBounds(y2, S);
      const d = f2(y2, t2.spatialReference);
      n2.push(this._export(d, e3, e3, 0, i, o2).then((t3) => (0 !== l && (S.set(r2, a4, p, l), s.getTileBounds(y2, S), t3.x = y2[0], t3.y = y2[3]), t3)));
    }), Promise.all(n2);
  }
};
e([y()], w2.prototype, "_imagePromise", void 0), e([y()], w2.prototype, "bitmaps", void 0), e([y()], w2.prototype, "container", void 0), e([y()], w2.prototype, "fetchSource", void 0), e([y()], w2.prototype, "hidpi", void 0), e([y()], w2.prototype, "imageMaxWidth", void 0), e([y()], w2.prototype, "imageMaxHeight", void 0), e([y()], w2.prototype, "imageRotationSupported", void 0), e([y()], w2.prototype, "imageNormalizationSupported", void 0), e([y()], w2.prototype, "requestUpdate", void 0), e([y()], w2.prototype, "updating", null), w2 = e([a("esri.views.2d.layers.support.ExportStrategy")], w2);
var v2 = w2;

export {
  v2 as v
};
//# sourceMappingURL=chunk-U56QVIJL.js.map
