{"version": 3, "sources": ["../../@arcgis/core/symbols/support/textUtils.js", "../../@arcgis/core/symbols/Font.js", "../../@arcgis/core/symbols/TextSymbol.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{clamp as e}from\"../../core/mathUtils.js\";import{ensureNumber as o}from\"../../core/accessorSupport/ensureType.js\";const l=[\"none\",\"underline\",\"line-through\"],t=[\"normal\",\"italic\",\"oblique\"],r=[\"normal\",\"lighter\",\"bold\",\"bolder\"],n={type:Number,cast:l=>{const t=o(l);return 0===t?1:e(t,.1,4)},nonNullable:!0},i=[\"left\",\"right\",\"center\"],a=[\"baseline\",\"top\",\"middle\",\"bottom\"],m={type:i,nonNullable:!0},p={type:a,nonNullable:!0};export{l as fontDecorations,t as fontStyles,r as fontWeights,m as horizontalAlignmentProperty,i as horizontalAlignments,n as lineHeightProperty,p as verticalAlignmentProperty,a as verticalAlignments};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import{JSONSupport as o}from\"../core/JSONSupport.js\";import{toPt as e}from\"../core/screenUtils.js\";import{property as r}from\"../core/accessorSupport/decorators/property.js\";import{cast as s}from\"../core/accessorSupport/decorators/cast.js\";import\"../core/arrayUtils.js\";import{subclass as i}from\"../core/accessorSupport/decorators/subclass.js\";import{fontDecorations as p,fontStyles as a,fontWeights as n}from\"./support/textUtils.js\";var c;let l=c=class extends o{constructor(t){super(t),this.decoration=\"none\",this.family=\"sans-serif\",this.size=9,this.style=\"normal\",this.weight=\"normal\"}castSize(t){return e(t)}clone(){return new c({decoration:this.decoration,family:this.family,size:this.size,style:this.style,weight:this.weight})}hash(){return`${this.decoration}.${this.family}.${this.size}.${this.style}.${this.weight}`}};t([r({type:p,json:{default:\"none\",write:!0}})],l.prototype,\"decoration\",void 0),t([r({type:String,json:{write:!0}})],l.prototype,\"family\",void 0),t([r({type:Number,json:{write:{overridePolicy:(t,o,e)=>({enabled:!e||!e.textSymbol3D})}}})],l.prototype,\"size\",void 0),t([s(\"size\")],l.prototype,\"castSize\",null),t([r({type:a,json:{default:\"normal\",write:!0}})],l.prototype,\"style\",void 0),t([r({type:n,json:{default:\"normal\",write:!0}})],l.prototype,\"weight\",void 0),l=c=t([i(\"esri.symbols.Font\")],l);const m=l;export{m as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../chunks/tslib.es6.js\";import o from\"../Color.js\";import{clone as e}from\"../core/lang.js\";import{toPt as i}from\"../core/screenUtils.js\";import{property as r}from\"../core/accessorSupport/decorators/property.js\";import{cast as s}from\"../core/accessorSupport/decorators/cast.js\";import{enumeration as n}from\"../core/accessorSupport/decorators/enumeration.js\";import{subclass as h}from\"../core/accessorSupport/decorators/subclass.js\";import{writer as l}from\"../core/accessorSupport/decorators/writer.js\";import p from\"./Font.js\";import a from\"./Symbol.js\";import{horizontalAlignmentProperty as d,verticalAlignmentProperty as c,lineHeightProperty as y}from\"./support/textUtils.js\";var f;let g=f=class extends a{constructor(...t){super(...t),this.backgroundColor=null,this.borderLineColor=null,this.borderLineSize=null,this.font=new p,this.horizontalAlignment=\"center\",this.kerning=!0,this.haloColor=null,this.haloSize=null,this.rightToLeft=null,this.rotated=!1,this.text=\"\",this.type=\"text\",this.verticalAlignment=\"baseline\",this.xoffset=0,this.yoffset=0,this.angle=0,this.width=null,this.lineWidth=192,this.lineHeight=1}normalizeCtorArgs(t,o,e){if(t&&\"string\"!=typeof t)return t;const i={};return t&&(i.text=t),o&&(i.font=o),e&&(i.color=e),i}writeLineWidth(t,o,e,i){i&&\"string\"!=typeof i?i.origin:o[e]=t}castLineWidth(t){return i(t)}writeLineHeight(t,o,e,i){i&&\"string\"!=typeof i?i.origin:o[e]=t}clone(){return new f({angle:this.angle,backgroundColor:e(this.backgroundColor),borderLineColor:e(this.borderLineColor),borderLineSize:this.borderLineSize,color:e(this.color),font:this.font&&this.font.clone(),haloColor:e(this.haloColor),haloSize:this.haloSize,horizontalAlignment:this.horizontalAlignment,kerning:this.kerning,lineHeight:this.lineHeight,lineWidth:this.lineWidth,rightToLeft:this.rightToLeft,rotated:this.rotated,text:this.text,verticalAlignment:this.verticalAlignment,width:this.width,xoffset:this.xoffset,yoffset:this.yoffset})}hash(){return`${this.backgroundColor&&this.backgroundColor.hash()}.${this.borderLineColor}.${this.borderLineSize}.${this.color?.hash()}.${this.font&&this.font.hash()}.${this.haloColor&&this.haloColor.hash()}.${this.haloSize}.${this.horizontalAlignment}.${this.kerning}.${this.rightToLeft}.${this.rotated}.${this.text}.${this.verticalAlignment}.${this.width}.${this.xoffset}.${this.yoffset}.${this.lineHeight}.${this.lineWidth}.${this.angle}`}};t([r({type:o,json:{write:!0}})],g.prototype,\"backgroundColor\",void 0),t([r({type:o,json:{write:!0}})],g.prototype,\"borderLineColor\",void 0),t([r({type:Number,json:{write:!0},cast:i})],g.prototype,\"borderLineSize\",void 0),t([r({type:p,json:{write:!0}})],g.prototype,\"font\",void 0),t([r({...d,json:{write:!0}})],g.prototype,\"horizontalAlignment\",void 0),t([r({type:Boolean,json:{write:!0}})],g.prototype,\"kerning\",void 0),t([r({type:o,json:{write:!0}})],g.prototype,\"haloColor\",void 0),t([r({type:Number,cast:i,json:{write:!0}})],g.prototype,\"haloSize\",void 0),t([r({type:Boolean,json:{write:!0}})],g.prototype,\"rightToLeft\",void 0),t([r({type:Boolean,json:{write:!0}})],g.prototype,\"rotated\",void 0),t([r({type:String,json:{write:!0}})],g.prototype,\"text\",void 0),t([n({esriTS:\"text\"},{readOnly:!0})],g.prototype,\"type\",void 0),t([r({...c,json:{write:!0}})],g.prototype,\"verticalAlignment\",void 0),t([r({type:Number,cast:i,json:{write:!0}})],g.prototype,\"xoffset\",void 0),t([r({type:Number,cast:i,json:{write:!0}})],g.prototype,\"yoffset\",void 0),t([r({type:Number,json:{read:t=>t&&-1*t,write:(t,o)=>o.angle=t&&-1*t}})],g.prototype,\"angle\",void 0),t([r({type:Number,json:{write:!0}})],g.prototype,\"width\",void 0),t([r({type:Number})],g.prototype,\"lineWidth\",void 0),t([l(\"lineWidth\")],g.prototype,\"writeLineWidth\",null),t([s(\"lineWidth\")],g.prototype,\"castLineWidth\",null),t([r(y)],g.prototype,\"lineHeight\",void 0),t([l(\"lineHeight\")],g.prototype,\"writeLineHeight\",null),g=f=t([h(\"esri.symbols.TextSymbol\")],g);const m=g;export{m as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIwH,IAAMA,KAAE,CAAC,QAAO,aAAY,cAAc;AAA1C,IAA4C,IAAE,CAAC,UAAS,UAAS,SAAS;AAA1E,IAA4EC,KAAE,CAAC,UAAS,WAAU,QAAO,QAAQ;AAAjH,IAAmH,IAAE,EAAC,MAAK,QAAO,MAAK,CAAAD,OAAG;AAAC,QAAME,KAAE,EAAEF,EAAC;AAAE,SAAO,MAAIE,KAAE,IAAEC,GAAED,IAAE,KAAG,CAAC;AAAC,GAAE,aAAY,KAAE;AAAhM,IAAkM,IAAE,CAAC,QAAO,SAAQ,QAAQ;AAA5N,IAA8NC,KAAE,CAAC,YAAW,OAAM,UAAS,QAAQ;AAAnQ,IAAqQ,IAAE,EAAC,MAAK,GAAE,aAAY,KAAE;AAA7R,IAA+RC,KAAE,EAAC,MAAKD,IAAE,aAAY,KAAE;;;ACA6C,IAAI;AAAE,IAAIE,KAAE,IAAE,cAAc,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,QAAO,KAAK,SAAO,cAAa,KAAK,OAAK,GAAE,KAAK,QAAM,UAAS,KAAK,SAAO;AAAA,EAAQ;AAAA,EAAC,SAASA,IAAE;AAAC,WAAOC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,YAAW,KAAK,YAAW,QAAO,KAAK,QAAO,MAAK,KAAK,MAAK,OAAM,KAAK,OAAM,QAAO,KAAK,OAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAM,GAAG,KAAK,UAAU,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,SAAQ,QAAO,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,EAAC,gBAAe,CAACC,IAAEC,IAAEC,QAAK,EAAC,SAAQ,CAACA,MAAG,CAACA,GAAE,aAAY,GAAE,EAAC,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,MAAM,CAAC,GAAEA,GAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,SAAQ,UAAS,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKI,IAAE,MAAK,EAAC,SAAQ,UAAS,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEJ,GAAE,WAAU,UAAS,MAAM,GAAEA,KAAE,IAAE,EAAE,CAACK,GAAE,mBAAmB,CAAC,GAAEL,EAAC;AAAE,IAAMM,KAAEN;;;ACAtqB,IAAI;AAAE,IAAI,IAAE,IAAE,cAAcO,GAAC;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,kBAAgB,MAAK,KAAK,iBAAe,MAAK,KAAK,OAAK,IAAIC,MAAE,KAAK,sBAAoB,UAAS,KAAK,UAAQ,MAAG,KAAK,YAAU,MAAK,KAAK,WAAS,MAAK,KAAK,cAAY,MAAK,KAAK,UAAQ,OAAG,KAAK,OAAK,IAAG,KAAK,OAAK,QAAO,KAAK,oBAAkB,YAAW,KAAK,UAAQ,GAAE,KAAK,UAAQ,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,MAAK,KAAK,YAAU,KAAI,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEE,IAAEC,IAAE;AAAC,QAAGH,MAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,UAAMI,KAAE,CAAC;AAAE,WAAOJ,OAAII,GAAE,OAAKJ,KAAGE,OAAIE,GAAE,OAAKF,KAAGC,OAAIC,GAAE,QAAMD,KAAGC;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAEE,IAAEC,IAAEC,IAAE;AAAC,IAAAA,MAAG,YAAU,OAAOA,KAAEA,GAAE,SAAOF,GAAEC,EAAC,IAAEH;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAOE,GAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEE,IAAEC,IAAEC,IAAE;AAAC,IAAAA,MAAG,YAAU,OAAOA,KAAEA,GAAE,SAAOF,GAAEC,EAAC,IAAEH;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,WAAO,IAAI,EAAE,EAAC,OAAM,KAAK,OAAM,iBAAgB,EAAE,KAAK,eAAe,GAAE,iBAAgB,EAAE,KAAK,eAAe,GAAE,gBAAe,KAAK,gBAAe,OAAM,EAAE,KAAK,KAAK,GAAE,MAAK,KAAK,QAAM,KAAK,KAAK,MAAM,GAAE,WAAU,EAAE,KAAK,SAAS,GAAE,UAAS,KAAK,UAAS,qBAAoB,KAAK,qBAAoB,SAAQ,KAAK,SAAQ,YAAW,KAAK,YAAW,WAAU,KAAK,WAAU,aAAY,KAAK,aAAY,SAAQ,KAAK,SAAQ,MAAK,KAAK,MAAK,mBAAkB,KAAK,mBAAkB,OAAM,KAAK,OAAM,SAAQ,KAAK,SAAQ,SAAQ,KAAK,QAAO,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAJ16D;AAI26D,WAAM,GAAG,KAAK,mBAAiB,KAAK,gBAAgB,KAAK,CAAC,IAAI,KAAK,eAAe,IAAI,KAAK,cAAc,KAAI,UAAK,UAAL,mBAAY,MAAM,IAAI,KAAK,QAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,aAAW,KAAK,UAAU,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,mBAAmB,IAAI,KAAK,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,iBAAiB,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAKK,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKA,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,mBAAkB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,GAAE,MAAKH,GAAC,CAAC,CAAC,GAAE,EAAE,WAAU,kBAAiB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKD,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,GAAG,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKI,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAKH,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,QAAO,OAAM,GAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,GAAGI,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,qBAAoB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAKJ,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAKA,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,WAAU,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,MAAK,CAAAF,OAAGA,MAAG,KAAGA,IAAE,OAAM,CAACA,IAAEE,OAAIA,GAAE,QAAMF,MAAG,KAAGA,GAAC,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,WAAW,CAAC,GAAE,EAAE,WAAU,kBAAiB,IAAI,GAAE,EAAE,CAAC,EAAE,WAAW,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,YAAY,CAAC,GAAE,EAAE,WAAU,mBAAkB,IAAI,GAAE,IAAE,IAAE,EAAE,CAACD,GAAE,yBAAyB,CAAC,GAAE,CAAC;AAAE,IAAME,KAAE;", "names": ["l", "r", "t", "a", "p", "l", "t", "o", "e", "r", "a", "m", "a", "t", "m", "o", "e", "i", "l", "p"]}