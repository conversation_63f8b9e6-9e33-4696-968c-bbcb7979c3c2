import {
  D
} from "./chunk-VFUA7AXS.js";
import "./chunk-ZJMR67LT.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-M46W6WAV.js";
import "./chunk-C65HMCEM.js";
import "./chunk-RCQLO7DH.js";
import "./chunk-ARNVPEMS.js";
import "./chunk-HSGVCYPR.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-ES4OMWUC.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-BNOUIPKE.js";
import "./chunk-7TISIISL.js";
import "./chunk-74RIZBAU.js";
import {
  e as e2
} from "./chunk-JSZR3BUH.js";
import "./chunk-6W6ECZU2.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-REFSHSQW.js";
import "./chunk-RURSJOSG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-LFDODH6Z.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-ZPQGODVG.js";
import "./chunk-GNVA6PUD.js";
import "./chunk-ERECWYOT.js";
import "./chunk-VBFIXQAN.js";
import "./chunk-LN7EIT6D.js";
import "./chunk-HQDK2TLZ.js";
import "./chunk-SROTSYJS.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-HDM6HCKB.js";
import "./chunk-ZONU323N.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-ICW345PU.js";
import "./chunk-WCGNZXE2.js";
import "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-2ILOD42U.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-NOEG2P2J.js";
import "./chunk-WGNECGZE.js";
import "./chunk-YGSDGECR.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import {
  x
} from "./chunk-GFEKPAJZ.js";
import "./chunk-4CFWXJIK.js";
import "./chunk-DHWMTT76.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-C3LWQPIC.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-72RC7KC7.js";
import "./chunk-PGSBPPQ2.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-V6NQCXYQ.js";
import "./chunk-XAKEPYSQ.js";
import {
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-HRASNGES.js";
import "./chunk-I4BKZ7SD.js";
import "./chunk-S5FBFNAP.js";
import "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  y
} from "./chunk-R4CPW7J5.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import "./chunk-HP475EI3.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/StreamLayerView2D.js
function u(e3, t2) {
  if (t(e3) && t(t2)) return null;
  const r3 = {};
  return r(t2) && (r3.geometry = t2.toJSON()), r(e3) && (r3.where = e3), r3;
}
var m = class extends D {
  constructor() {
    super(...arguments), this._enabledEventTypes = /* @__PURE__ */ new Set(), this._isUserPaused = false, this.errorString = null, this.connectionStatus = "disconnected";
  }
  initialize() {
    this.addHandles([l(() => this.layer.customParameters, (e3) => this._proxy.updateCustomParameters(e3)), this.layer.on("send-message-to-socket", (e3) => this._proxy.sendMessageToSocket(e3)), this.layer.on("send-message-to-client", (e3) => this._proxy.sendMessageToClient(e3)), l(() => this.layer.purgeOptions, () => this._update()), l(() => this.suspended, (e3) => {
      e3 ? this._proxy.pauseStream() : this._isUserPaused || this._proxy.resumeStream();
    })], "constructor");
  }
  get connectionError() {
    if (this.errorString) return new s("stream-controller", this.errorString);
  }
  pause() {
    this._isUserPaused = true, this._proxy.pauseStream();
  }
  resume() {
    this._isUserPaused = false, this._proxy.resumeStream();
  }
  on(e3, t2) {
    if (Array.isArray(e3)) return r2(e3.map((e4) => this.on(e4, t2)));
    const s2 = ["data-received", "message-received"].includes(e3);
    s2 && (this._enabledEventTypes.add(e3), this._proxy.enableEvent(e3, true));
    const o = super.on(e3, t2), i = this;
    return { remove() {
      o.remove(), s2 && (i._proxy.closed || i.hasEventListener(e3) || i._proxy.enableEvent(e3, false));
    } };
  }
  queryLatestObservations(e3, r3) {
    var _a, _b, _c;
    if (!(((_a = this.layer.timeInfo) == null ? void 0 : _a.endField) || ((_b = this.layer.timeInfo) == null ? void 0 : _b.startField) || ((_c = this.layer.timeInfo) == null ? void 0 : _c.trackIdField))) throw new s("streamlayer-no-timeField", "queryLatestObservation can only be used with services that define a TrackIdField");
    return this._proxy.queryLatestObservations(this._cleanUpQuery(e3), r3).then((e4) => {
      const t2 = x.fromJSON(e4);
      return t2.features.forEach((e5) => {
        e5.layer = this.layer, e5.sourceLayer = this.layer;
      }), t2;
    });
  }
  detach() {
    super.detach(), this.connectionStatus = "disconnected";
  }
  _createClientOptions() {
    return { ...super._createClientOptions(), setProperty: (e3) => {
      this.set(e3.propertyName, e3.value);
    } };
  }
  _createTileRendererHash(e3) {
    const t2 = `${JSON.stringify(this.layer.purgeOptions)}.${JSON.stringify(u(this.layer.definitionExpression, this.layer.geometryDefinition))})`;
    return super._createTileRendererHash(e3) + t2;
  }
  async _createServiceOptions() {
    const e3 = this.layer, { objectIdField: t2 } = e3, r3 = e3.fields.map((e4) => e4.toJSON()), s2 = e2(e3.geometryType), o = e3.timeInfo && e3.timeInfo.toJSON() || null, i = e3.spatialReference ? e3.spatialReference.toJSON() : null;
    return { type: "stream", fields: r3, geometryType: s2, objectIdField: t2, timeInfo: o, source: this.layer.parsedUrl, serviceFilter: u(this.layer.definitionExpression, this.layer.geometryDefinition), purgeOptions: this.layer.purgeOptions.toJSON(), enabledEventTypes: Array.from(this._enabledEventTypes.values()), spatialReference: i, maxReconnectionAttempts: this.layer.maxReconnectionAttempts, maxReconnectionInterval: this.layer.maxReconnectionInterval, updateInterval: this.layer.updateInterval, customParameters: e3.customParameters };
  }
};
e([y()], m.prototype, "errorString", void 0), e([y({ readOnly: true })], m.prototype, "connectionError", null), e([y()], m.prototype, "connectionStatus", void 0), m = e([a("esri.views.2d.layers.StreamLayerView2D")], m);
var y2 = m;
export {
  y2 as default
};
//# sourceMappingURL=StreamLayerView2D-INB7Y6RP.js.map
