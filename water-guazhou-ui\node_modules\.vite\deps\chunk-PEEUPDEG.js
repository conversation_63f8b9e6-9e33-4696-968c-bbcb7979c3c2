import {
  s
} from "./chunk-KUPAGB4V.js";
import {
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/core/accessorSupport/decorators/enumeration.js
function o(o2, r = {}) {
  const a = o2 instanceof s ? o2 : new s(o2, r), l = { type: (r == null ? void 0 : r.ignoreUnknown) ?? 1 ? a.apiValues : String, json: { type: a.jsonValues, read: !(r == null ? void 0 : r.readOnly) && { reader: a.read }, write: { writer: a.write } } };
  return void 0 !== (r == null ? void 0 : r.readOnly) && (l.readOnly = !!r.readOnly), void 0 !== (r == null ? void 0 : r.default) && (l.json.default = r.default), void 0 !== (r == null ? void 0 : r.name) && (l.json.name = r.name), void 0 !== (r == null ? void 0 : r.nonNullable) && (l.nonNullable = r.nonNullable), y(l);
}

export {
  o
};
//# sourceMappingURL=chunk-PEEUPDEG.js.map
