import {
  t
} from "./chunk-TTOVHTHV.js";
import {
  n as n3,
  p as p6
} from "./chunk-U4ZDG3O5.js";
import "./chunk-ZPQGODVG.js";
import "./chunk-GNVA6PUD.js";
import {
  s as s3
} from "./chunk-BMXLXOQI.js";
import "./chunk-CMY3T5EP.js";
import "./chunk-LN7EIT6D.js";
import {
  p as p5
} from "./chunk-7FKSPW4T.js";
import {
  i
} from "./chunk-ICW345PU.js";
import {
  o as o4
} from "./chunk-YAIU7YWS.js";
import {
  p as p7
} from "./chunk-3TGCMFWU.js";
import "./chunk-4HYRJ2BC.js";
import {
  C
} from "./chunk-WCGNZXE2.js";
import "./chunk-AZTQJ4L4.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import {
  n as n2,
  o as o3,
  p as p4
} from "./chunk-O6B5U6ZZ.js";
import {
  T as T2
} from "./chunk-PIXWSNML.js";
import "./chunk-GDMKZBSE.js";
import "./chunk-CB74CU6U.js";
import "./chunk-KCSQWRUD.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-2ILOD42U.js";
import {
  p as p3
} from "./chunk-OQ7AZTS4.js";
import "./chunk-3WHT6SV3.js";
import "./chunk-6TZFJURO.js";
import "./chunk-T4DDX2RP.js";
import "./chunk-4TTTQ6NZ.js";
import "./chunk-T72XIVTW.js";
import "./chunk-6YK77SK5.js";
import "./chunk-NOZFLZZL.js";
import {
  x
} from "./chunk-NOEG2P2J.js";
import "./chunk-WGNECGZE.js";
import "./chunk-YGSDGECR.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-JZBEMQMW.js";
import "./chunk-HM62IZSE.js";
import {
  y as y2
} from "./chunk-4CFWXJIK.js";
import "./chunk-DHWMTT76.js";
import "./chunk-CCAF47ZU.js";
import {
  a as a2
} from "./chunk-RK6FD6JL.js";
import "./chunk-VP6XUPJO.js";
import "./chunk-HTXGAKOK.js";
import {
  _
} from "./chunk-FDBF4TJR.js";
import "./chunk-YUDXR4IE.js";
import {
  c as c2
} from "./chunk-WTHPVARW.js";
import {
  O
} from "./chunk-B3Q27ZSC.js";
import {
  p as p2
} from "./chunk-XFYW3BMZ.js";
import {
  D,
  I,
  c,
  f as f2,
  l as l2,
  m,
  p
} from "./chunk-C3LWQPIC.js";
import "./chunk-VYC4DNQO.js";
import "./chunk-LVWRJMBJ.js";
import "./chunk-3HW44BD3.js";
import "./chunk-JSANYNBO.js";
import "./chunk-TPRZH2SY.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-P6BL3OFI.js";
import "./chunk-FZ7BG3VX.js";
import {
  n
} from "./chunk-5IKCVZDA.js";
import {
  t as t2
} from "./chunk-AHLG6PXW.js";
import {
  x as x2
} from "./chunk-72RC7KC7.js";
import "./chunk-PGSBPPQ2.js";
import "./chunk-6ENNE6EU.js";
import "./chunk-SX465FPD.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-S2ZSC2TN.js";
import "./chunk-XAKEPYSQ.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-HRASNGES.js";
import "./chunk-I4BKZ7SD.js";
import {
  k
} from "./chunk-S5FBFNAP.js";
import {
  M
} from "./chunk-VBAEC53F.js";
import "./chunk-C67OD7TM.js";
import "./chunk-5A4WR2SR.js";
import "./chunk-SJ35WMYN.js";
import "./chunk-UV4E33V4.js";
import {
  F,
  x as x3
} from "./chunk-PBQFTVHM.js";
import "./chunk-ZJKAJ76S.js";
import "./chunk-46HTCESL.js";
import "./chunk-NE5KC6IQ.js";
import "./chunk-CB5YGH7P.js";
import "./chunk-Q6JATJLO.js";
import "./chunk-CV3OR36A.js";
import "./chunk-JX2QAMUH.js";
import "./chunk-DVUUHX3W.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-55IDRPE2.js";
import "./chunk-MLFKSWC4.js";
import "./chunk-YS4MXRXZ.js";
import "./chunk-SJRT3EVN.js";
import "./chunk-RCNP3U5T.js";
import "./chunk-2CFIAWMM.js";
import "./chunk-3MWB7OGY.js";
import "./chunk-MURG32WB.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-HSQRAXGT.js";
import "./chunk-4FGIB6FH.js";
import "./chunk-LZMNPMOO.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-3BEYEFLH.js";
import "./chunk-NM5RTWYY.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-B2DWQPEO.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-DD2TTHXQ.js";
import {
  o as o2
} from "./chunk-7CPUVZNS.js";
import "./chunk-O7GYYCIW.js";
import "./chunk-7XFTGDGG.js";
import "./chunk-RWXVETUC.js";
import "./chunk-SCGGCSVU.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  U
} from "./chunk-6KAEXAW7.js";
import "./chunk-ACC3Z6G3.js";
import "./chunk-U4SVMKOQ.js";
import {
  w as w2
} from "./chunk-Q7LVCH5L.js";
import "./chunk-4VUBPPPE.js";
import "./chunk-HHGBW7LE.js";
import {
  o
} from "./chunk-FSN5N3WL.js";
import "./chunk-RZ2SBURQ.js";
import {
  f2 as f
} from "./chunk-HL2RFSF3.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-IJ6FZE6K.js";
import {
  l
} from "./chunk-YN7TTTXO.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-YD5Y4V7J.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import {
  a2 as a,
  u,
  y
} from "./chunk-R4CPW7J5.js";
import {
  r as r2
} from "./chunk-2CM7MIII.js";
import {
  T
} from "./chunk-HP475EI3.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/support/PurgeOptions.js
var t3;
var i2 = t3 = class extends l {
  constructor() {
    super(...arguments), this.age = null, this.ageReceived = null, this.displayCount = null, this.maxObservations = 1;
  }
  clone() {
    return new t3({ age: this.age, ageReceived: this.ageReceived, displayCount: this.displayCount, maxObservations: this.maxObservations });
  }
};
e([y({ type: Number, json: { write: true } })], i2.prototype, "age", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "ageReceived", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "displayCount", void 0), e([y({ type: Number, json: { write: true } })], i2.prototype, "maxObservations", void 0), i2 = t3 = e([a("esri.layers.support.PurgeOptions")], i2);
var p8 = i2;

// node_modules/@arcgis/core/layers/StreamLayer.js
var Y = s3();
function Z(e2, t4) {
  return new s2("layer:unsupported", `Layer (${e2.title}, ${e2.id}) of type '${e2.declaredClass}' ${t4}`, { layer: e2 });
}
var ee = class extends n3(p6(n(a2(t2(p2(p5(c2(_(O(o4(b))))))))))) {
  constructor(...e2) {
    super(...e2), this.copyright = null, this.definitionExpression = null, this.displayField = null, this.elevationInfo = null, this.fields = null, this.fieldsIndex = null, this.geometryDefinition = null, this.geometryType = null, this.labelsVisible = true, this.labelingInfo = null, this.legendEnabled = true, this.maxReconnectionAttempts = 0, this.maxReconnectionInterval = 20, this.maxScale = 0, this.minScale = 0, this.objectIdField = null, this.operationalLayerType = "ArcGISStreamLayer", this.popupEnabled = true, this.popupTemplate = null, this.purgeOptions = new p8(), this.screenSizePerspectiveEnabled = true, this.sourceJSON = null, this.spatialReference = f.WGS84, this.type = "stream", this.url = null, this.updateInterval = 300, this.webSocketUrl = null;
  }
  normalizeCtorArgs(e2, t4) {
    return "string" == typeof e2 ? { url: e2, ...t4 } : e2;
  }
  load(e2) {
    if (!("WebSocket" in globalThis)) return this.addResolvingPromise(Promise.reject(new s2("stream-layer:websocket-unsupported", "WebSocket is not supported in this browser. StreamLayer will not have real-time connection with the stream service."))), Promise.resolve(this);
    const t4 = r(e2) ? e2.signal : null;
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Stream Service", "Feed"] }, e2).catch(w).then(() => this._fetchService(t4))), Promise.resolve(this);
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  set renderer(e2) {
    F(e2, this.fieldsIndex), this._set("renderer", e2);
  }
  readRenderer(e2, t4, s4) {
    const n4 = (t4 = t4.layerDefinition || t4).drawingInfo && t4.drawingInfo.renderer || void 0;
    if (n4) {
      const e3 = o3(n4, t4, s4) || void 0;
      return e3 || s.getLogger(this.declaredClass).error("Failed to create renderer", { rendererDefinition: t4.drawingInfo.renderer, layer: this, context: s4 }), e3;
    }
    if (t4.defaultSymbol) return t4.types && t4.types.length ? new T2({ defaultSymbol: te(t4.defaultSymbol, t4, s4), field: t4.typeIdField, uniqueValueInfos: t4.types.map((e3) => ({ id: e3.id, symbol: te(e3.symbol, e3, s4) })) }) : new p3({ symbol: te(t4.defaultSymbol, t4, s4) });
  }
  async connect(e2) {
    const [{ createConnection: t4 }] = await Promise.all([import("./createConnection-SDYBKVSF.js"), this.load()]), r3 = this.geometryType ? o2.toJSON(this.geometryType) : null, { customParameters: o5 = null, definitionExpression: i3 = null, geometryDefinition: s4 = null, maxReconnectionAttempts: n4 = 0, maxReconnectionInterval: p9 = 20, spatialReference: a3 = this.spatialReference } = e2 || this.createConnectionParameters(), l3 = t4(this.parsedUrl, this.spatialReference, a3, r3, { geometry: s4, where: i3 }, n4, p9, o5 ?? void 0), d = r2([this.on("send-message-to-socket", (e3) => l3.sendMessageToSocket(e3)), this.on("send-message-to-client", (e3) => l3.sendMessageToClient(e3))]);
    return l3.once("destroy", d.remove), l3;
  }
  createConnectionParameters() {
    return { spatialReference: this.spatialReference, customParameters: this.customParameters, definitionExpression: this.definitionExpression, geometryDefinition: this.geometryDefinition, maxReconnectionAttempts: this.maxReconnectionAttempts, maxReconnectionInterval: this.maxReconnectionInterval };
  }
  createPopupTemplate(e2) {
    return p7(this, e2);
  }
  createQuery() {
    const e2 = new x();
    return e2.returnGeometry = true, e2.outFields = ["*"], e2.where = this.definitionExpression || "1=1", e2;
  }
  getFieldDomain(e2, t4) {
    if (!this.fields) return null;
    let r3 = null;
    return this.fields.some((t5) => (t5.name === e2 && (r3 = t5.domain), !!r3)), r3;
  }
  getField(e2) {
    return this.fieldsIndex.get(e2);
  }
  serviceSupportsSpatialReference(e2) {
    return true;
  }
  sendMessageToSocket(e2) {
    this.emit("send-message-to-socket", e2);
  }
  sendMessageToClient(e2) {
    this.emit("send-message-to-client", e2);
  }
  write(e2, t4) {
    const r3 = t4 == null ? void 0 : t4.messages;
    return this.webSocketUrl ? (r3 == null ? void 0 : r3.push(Z(this, "using a custom websocket connection cannot be written to web scenes and web maps")), null) : this.parsedUrl ? super.write(e2, t4) : (r3 == null ? void 0 : r3.push(Z(this, "using a client-side only connection without a url cannot be written to web scenes and web maps")), null);
  }
  async _fetchService(e2) {
    var _a, _b, _c;
    if (!!!this.webSocketUrl && this.parsedUrl) {
      if (!this.sourceJSON) {
        const { data: t4 } = await U(this.parsedUrl.path, { query: { f: "json", ...this.customParameters, ...this.parsedUrl.query }, responseType: "json", signal: e2 });
        this.sourceJSON = t4;
      }
    } else {
      if (!((_a = this.timeInfo) == null ? void 0 : _a.trackIdField)) throw new s2("stream-layer:missing-metadata", "The stream layer trackIdField must be specified.");
      if (!this.objectIdField) {
        const e3 = (_b = this.fields.find((e4) => "oid" === e4.type)) == null ? void 0 : _b.name;
        if (!e3) throw new s2("stream-layer:missing-metadata", "The stream layer objectIdField must be specified.");
        this.objectIdField = e3;
      }
      if (!this.fields) throw new s2("stream-layer:missing-metadata", "The stream layer fields must be specified.");
      if (this.fields.some((e3) => e3.name === this.objectIdField) || this.fields.push(new y2({ name: this.objectIdField, alias: this.objectIdField, type: "oid" })), !this.geometryType) throw new s2("stream-layer:missing-metadata", "The stream layer geometryType must be specified.");
      this.webSocketUrl && (this.url = this.webSocketUrl);
    }
    return this.read(this.sourceJSON, { origin: "service", portalItem: this.portalItem, portal: (_c = this.portalItem) == null ? void 0 : _c.portal, url: this.parsedUrl }), F(this.renderer, this.fieldsIndex), x3(this.timeInfo, this.fieldsIndex), this.objectIdField || (this.objectIdField = "__esri_stream_id__"), t(this, { origin: "service" });
  }
};
e([y({ type: String })], ee.prototype, "copyright", void 0), e([y({ readOnly: true })], ee.prototype, "defaultPopupTemplate", null), e([y({ type: String, json: { name: "layerDefinition.definitionExpression", write: { enabled: true, allowNull: true } } })], ee.prototype, "definitionExpression", void 0), e([y({ type: String })], ee.prototype, "displayField", void 0), e([y({ type: x2 })], ee.prototype, "elevationInfo", void 0), e([y(Y.fields)], ee.prototype, "fields", void 0), e([y(Y.fieldsIndex)], ee.prototype, "fieldsIndex", void 0), e([y({ type: w2 })], ee.prototype, "geometryDefinition", void 0), e([y({ type: o2.apiValues, json: { read: { reader: o2.read } } })], ee.prototype, "geometryType", void 0), e([y(m)], ee.prototype, "labelsVisible", void 0), e([y({ type: [C], json: { read: { source: "layerDefinition.drawingInfo.labelingInfo", reader: i }, write: { target: "layerDefinition.drawingInfo.labelingInfo" } } })], ee.prototype, "labelingInfo", void 0), e([y(c)], ee.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide"] })], ee.prototype, "listMode", void 0), e([y({ type: T })], ee.prototype, "maxReconnectionAttempts", void 0), e([y({ type: T })], ee.prototype, "maxReconnectionInterval", void 0), e([y(D)], ee.prototype, "maxScale", void 0), e([y(I)], ee.prototype, "minScale", void 0), e([y({ type: String })], ee.prototype, "objectIdField", void 0), e([y({ value: "ArcGISStreamLayer", type: ["ArcGISStreamLayer"] })], ee.prototype, "operationalLayerType", void 0), e([y(p)], ee.prototype, "popupEnabled", void 0), e([y({ type: k, json: { read: { source: "popupInfo" }, write: { target: "popupInfo" } } })], ee.prototype, "popupTemplate", void 0), e([y({ type: p8 })], ee.prototype, "purgeOptions", void 0), e([y({ types: p4, json: { origins: { service: { write: { target: "drawingInfo.renderer", enabled: false } }, "web-scene": { name: "layerDefinition.drawingInfo.renderer", types: n2, write: true } }, write: { target: "layerDefinition.drawingInfo.renderer" } } })], ee.prototype, "renderer", null), e([o("service", "renderer", ["drawingInfo.renderer", "defaultSymbol"]), o("renderer", ["layerDefinition.drawingInfo.renderer", "layerDefinition.defaultSymbol"])], ee.prototype, "readRenderer", null), e([y(l2)], ee.prototype, "screenSizePerspectiveEnabled", void 0), e([y()], ee.prototype, "sourceJSON", void 0), e([y({ type: f, json: { origins: { service: { read: { source: "spatialReference" } } } } })], ee.prototype, "spatialReference", void 0), e([y({ json: { read: false } })], ee.prototype, "type", void 0), e([y(f2)], ee.prototype, "url", void 0), e([y({ type: Number })], ee.prototype, "updateInterval", void 0), e([y({ type: String })], ee.prototype, "webSocketUrl", void 0), ee = e([a("esri.layers.StreamLayer")], ee);
var te = u({ types: M });
var re = ee;
export {
  re as default
};
//# sourceMappingURL=StreamLayer-6DQY42VC.js.map
