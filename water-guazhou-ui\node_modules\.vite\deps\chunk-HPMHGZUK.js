import {
  o,
  r
} from "./chunk-5AI3QK7R.js";
import {
  f
} from "./chunk-XBS7QZIQ.js";
import {
  U
} from "./chunk-AW4AS2UW.js";
import {
  c,
  v
} from "./chunk-ZACBBT3Y.js";
import {
  L
} from "./chunk-U4SVMKOQ.js";

// node_modules/@arcgis/core/rest/geometryService/cut.js
async function o2(o4, i, n, m) {
  const a = f(o4), p = i[0].spatialReference, u = { ...m, query: { ...a.query, f: "json", sr: JSON.stringify(p), target: JSON.stringify({ geometryType: c(i[0]), geometries: i }), cutter: JSON.stringify(n) } }, c2 = await U(a.path + "/cut", u), { cutIndexes: f2, geometries: g = [] } = c2.data;
  return { cutIndexes: f2, geometries: g.map((e) => {
    const t = v(e);
    return t.spatialReference = p, t;
  }) };
}

// node_modules/@arcgis/core/rest/geometryService/simplify.js
async function o3(o4, m, f2) {
  const n = "string" == typeof o4 ? L(o4) : o4, p = m[0].spatialReference, a = c(m[0]), u = { ...f2, query: { ...n.query, f: "json", sr: p.wkid ? p.wkid : JSON.stringify(p), geometries: JSON.stringify(r(m)) } }, { data: y } = await U(n.path + "/simplify", u);
  return o(y.geometries, a, p);
}

export {
  o2 as o,
  o3 as o2
};
//# sourceMappingURL=chunk-HPMHGZUK.js.map
