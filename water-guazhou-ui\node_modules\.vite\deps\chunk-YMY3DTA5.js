import {
  n
} from "./chunk-TNP2LXZZ.js";
import {
  e as e2
} from "./chunk-RE7K5Z3I.js";
import {
  e,
  s as s2,
  t
} from "./chunk-SEO6KEGF.js";
import {
  E,
  L
} from "./chunk-JXLVNWKF.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import {
  r
} from "./chunk-BVTIFMBM.js";

// node_modules/@arcgis/core/rest/query/operations/pbfOptimizedFeatureSet.js
var n2 = ["esriGeometryPoint", "esriGeometryMultipoint", "esriGeometryPolyline", "esriGeometryPolygon"];
var a = class {
  constructor(e3) {
    this._options = e3, this.geometryTypes = n2, this._coordinatePtr = 0, this._vertexDimension = 0;
  }
  createFeatureResult() {
    return new e2();
  }
  prepareFeatures(e3) {
    this._vertexDimension = 2, e3.hasZ && this._vertexDimension++, e3.hasM && this._vertexDimension++;
  }
  finishFeatureResult(o2) {
    if (!o2 || !o2.features || !o2.hasZ || !this._options.sourceSpatialReference || !o2.spatialReference || E(o2.spatialReference, this._options.sourceSpatialReference) || o2.spatialReference.vcsWkid) return;
    const i2 = L(this._options.sourceSpatialReference) / L(o2.spatialReference);
    if (1 !== i2) for (const e3 of o2.features) {
      if (!e(e3)) continue;
      const t3 = e3.geometry.coords;
      for (let e4 = 2; e4 < t3.length; e4 += 3) t3[e4] *= i2;
    }
  }
  addFeature(e3, t3) {
    e3.features.push(t3);
  }
  createFeature() {
    return new s2();
  }
  createSpatialReference() {
    return { wkid: 0 };
  }
  createGeometry() {
    return new t();
  }
  addField(e3, t3) {
    e3.fields.push(t3);
  }
  allocateCoordinates(e3) {
    e3.coords.length = e3.lengths.reduce((e4, t3) => e4 + t3, 0) * this._vertexDimension, this._coordinatePtr = 0;
  }
  addCoordinate(e3, t3) {
    e3.coords[this._coordinatePtr++] = t3;
  }
  addCoordinatePoint(e3, t3) {
    e3.coords.push(t3);
  }
  addLength(e3, t3) {
    e3.lengths.push(t3);
  }
  addQueryGeometry(e3, t3) {
    e3.queryGeometry = t3.queryGeometry, e3.queryGeometryType = t3.queryGeometryType;
  }
  createPointGeometry() {
    return new t();
  }
};

// node_modules/@arcgis/core/rest/query/operations/pbfFeatureServiceParser.js
var n3 = ["esriFieldTypeSmallInteger", "esriFieldTypeInteger", "esriFieldTypeSingle", "esriFieldTypeDouble", "esriFieldTypeString", "esriFieldTypeDate", "esriFieldTypeOID", "esriFieldTypeGeometry", "esriFieldTypeBlob", "esriFieldTypeRaster", "esriFieldTypeGUID", "esriFieldTypeGlobalID", "esriFieldTypeXML"];
var o = ["sqlTypeBigInt", "sqlTypeBinary", "sqlTypeBit", "sqlTypeChar", "sqlTypeDate", "sqlTypeDecimal", "sqlTypeDouble", "sqlTypeFloat", "sqlTypeGeometry", "sqlTypeGUID", "sqlTypeInteger", "sqlTypeLongNVarchar", "sqlTypeLongVarbinary", "sqlTypeLongVarchar", "sqlTypeNChar", "sqlTypeNVarchar", "sqlTypeOther", "sqlTypeReal", "sqlTypeSmallInt", "sqlTypeSqlXml", "sqlTypeTime", "sqlTypeTimestamp", "sqlTypeTimestamp2", "sqlTypeTinyInt", "sqlTypeVarbinary", "sqlTypeVarchar"];
var i = ["upperLeft", "lowerLeft"];
function c(e3) {
  return e3 >= n3.length ? null : n3[e3];
}
function l(e3) {
  return e3 >= o.length ? null : o[e3];
}
function g(e3) {
  return e3 >= i.length ? null : i[e3];
}
function p(e3, t3) {
  return t3 >= e3.geometryTypes.length ? null : e3.geometryTypes[t3];
}
function u(e3, t3, s3) {
  const r2 = 3, a2 = e3.asUnsafe(), n4 = t3.createPointGeometry(s3);
  for (; a2.next(); ) switch (a2.tag()) {
    case r2: {
      const e4 = a2.getUInt32(), s4 = a2.pos() + e4;
      let r3 = 0;
      for (; a2.pos() < s4; ) t3.addCoordinatePoint(n4, a2.getSInt64(), r3++);
      break;
    }
    default:
      a2.skip();
  }
  return n4;
}
function f(e3, t3, s3) {
  const r2 = 2, a2 = 3, n4 = e3.asUnsafe(), o2 = t3.createGeometry(s3), i2 = 2 + (s3.hasZ ? 1 : 0) + (s3.hasM ? 1 : 0);
  for (; n4.next(); ) switch (n4.tag()) {
    case r2: {
      const e4 = n4.getUInt32(), s4 = n4.pos() + e4;
      let r3 = 0;
      for (; n4.pos() < s4; ) t3.addLength(o2, n4.getUInt32(), r3++);
      break;
    }
    case a2: {
      const e4 = n4.getUInt32(), s4 = n4.pos() + e4;
      let r3 = 0;
      for (t3.allocateCoordinates(o2); n4.pos() < s4; ) t3.addCoordinate(o2, n4.getSInt64(), r3), r3++, r3 === i2 && (r3 = 0);
      break;
    }
    default:
      n4.skip();
  }
  return o2;
}
function y(e3) {
  const t3 = 1, s3 = 2, n4 = 3, o2 = e3.asUnsafe(), i2 = new t();
  let c2 = "esriGeometryPoint";
  for (; o2.next(); ) switch (o2.tag()) {
    case s3: {
      const e4 = o2.getUInt32(), t4 = o2.pos() + e4;
      for (; o2.pos() < t4; ) i2.lengths.push(o2.getUInt32());
      break;
    }
    case n4: {
      const e4 = o2.getUInt32(), t4 = o2.pos() + e4;
      for (; o2.pos() < t4; ) i2.coords.push(o2.getSInt64());
      break;
    }
    case t3:
      c2 = n2[o2.getEnum()];
      break;
    default:
      o2.skip();
  }
  return { queryGeometry: i2, queryGeometryType: c2 };
}
function b(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = 4, n4 = 5, o2 = 6, i2 = 7, c2 = 8, l2 = 9, g2 = e3.asUnsafe();
  for (; g2.next(); ) switch (g2.tag()) {
    case t3:
      return g2.getString();
    case s3:
      return g2.getFloat();
    case r2:
      return g2.getDouble();
    case a2:
      return g2.getSInt32();
    case n4:
      return g2.getUInt32();
    case o2:
      return g2.getInt64();
    case i2:
      return g2.getUInt64();
    case c2:
      return g2.getSInt64();
    case l2:
      return g2.getBool();
    default:
      return g2.skip(), null;
  }
  return null;
}
function k(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = 4, n4 = 5, o2 = 6, i2 = e3.asUnsafe(), g2 = { type: c(0) };
  for (; i2.next(); ) switch (i2.tag()) {
    case t3:
      g2.name = i2.getString();
      break;
    case s3:
      g2.type = c(i2.getEnum());
      break;
    case r2:
      g2.alias = i2.getString();
      break;
    case a2:
      g2.sqlType = l(i2.getEnum());
      break;
    case n4:
      i2.skip();
      break;
    case o2:
      g2.defaultValue = i2.getString();
      break;
    default:
      i2.skip();
  }
  return g2;
}
function d(e3) {
  const t3 = 1, s3 = 2, r2 = {}, a2 = e3.asUnsafe();
  for (; a2.next(); ) switch (a2.tag()) {
    case t3:
      r2.name = a2.getString();
      break;
    case s3:
      r2.isSystemMaintained = a2.getBool();
      break;
    default:
      a2.skip();
  }
  return r2;
}
function m(e3, t3, s3, r2) {
  const a2 = 1, n4 = 2, o2 = 4, i2 = t3.createFeature(s3);
  let c2 = 0;
  for (; e3.next(); ) switch (e3.tag()) {
    case a2: {
      const t4 = r2[c2++].name;
      i2.attributes[t4] = e3.processMessage(b);
      break;
    }
    case n4:
      i2.geometry = e3.processMessageWithArgs(f, t3, s3);
      break;
    case o2:
      i2.centroid = e3.processMessageWithArgs(u, t3, s3);
      break;
    default:
      e3.skip();
  }
  return i2;
}
function h(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = 4, n4 = [1, 1, 1, 1], o2 = e3.asUnsafe();
  for (; o2.next(); ) switch (o2.tag()) {
    case t3:
      n4[0] = o2.getDouble();
      break;
    case s3:
      n4[1] = o2.getDouble();
      break;
    case a2:
      n4[2] = o2.getDouble();
      break;
    case r2:
      n4[3] = o2.getDouble();
      break;
    default:
      o2.skip();
  }
  return n4;
}
function T(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = 4, n4 = [0, 0, 0, 0], o2 = e3.asUnsafe();
  for (; o2.next(); ) switch (o2.tag()) {
    case t3:
      n4[0] = o2.getDouble();
      break;
    case s3:
      n4[1] = o2.getDouble();
      break;
    case a2:
      n4[2] = o2.getDouble();
      break;
    case r2:
      n4[3] = o2.getDouble();
      break;
    default:
      o2.skip();
  }
  return n4;
}
function q(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = { originPosition: g(0) }, n4 = e3.asUnsafe();
  for (; n4.next(); ) switch (n4.tag()) {
    case t3:
      a2.originPosition = g(n4.getEnum());
      break;
    case s3:
      a2.scale = n4.processMessage(h);
      break;
    case r2:
      a2.translate = n4.processMessage(T);
      break;
    default:
      n4.skip();
  }
  return a2;
}
function I(e3) {
  const t3 = 1, s3 = 2, r2 = 3, a2 = {}, n4 = e3.asUnsafe();
  for (; n4.next(); ) switch (n4.tag()) {
    case t3:
      a2.shapeAreaFieldName = n4.getString();
      break;
    case s3:
      a2.shapeLengthFieldName = n4.getString();
      break;
    case r2:
      a2.units = n4.getString();
      break;
    default:
      n4.skip();
  }
  return a2;
}
function F(e3, t3) {
  const s3 = 1, r2 = 2, a2 = 3, n4 = 4, o2 = 5, i2 = t3.createSpatialReference();
  for (; e3.next(); ) switch (e3.tag()) {
    case s3:
      i2.wkid = e3.getUInt32();
      break;
    case o2:
      i2.wkt = e3.getString();
      break;
    case r2:
      i2.latestWkid = e3.getUInt32();
      break;
    case a2:
      i2.vcsWkid = e3.getUInt32();
      break;
    case n4:
      i2.latestVcsWkid = e3.getUInt32();
      break;
    default:
      e3.skip();
  }
  return i2;
}
function U(e3, t3) {
  const s3 = 1, r2 = 2, a2 = 3, n4 = 4, o2 = 5, i2 = 7, c2 = 8, l2 = 9, g2 = 10, u2 = 11, f2 = 12, y2 = 13, b2 = 15, h2 = t3.createFeatureResult(), T2 = e3.asUnsafe();
  h2.geometryType = p(t3, 0);
  let U2 = false;
  for (; T2.next(); ) switch (T2.tag()) {
    case s3:
      h2.objectIdFieldName = T2.getString();
      break;
    case a2:
      h2.globalIdFieldName = T2.getString();
      break;
    case n4:
      h2.geohashFieldName = T2.getString();
      break;
    case o2:
      h2.geometryProperties = T2.processMessage(I);
      break;
    case i2:
      h2.geometryType = p(t3, T2.getEnum());
      break;
    case c2:
      h2.spatialReference = T2.processMessageWithArgs(F, t3);
      break;
    case g2:
      h2.hasZ = T2.getBool();
      break;
    case u2:
      h2.hasM = T2.getBool();
      break;
    case f2:
      h2.transform = T2.processMessage(q);
      break;
    case l2: {
      const e4 = T2.getBool();
      h2.exceededTransferLimit = e4;
      break;
    }
    case y2:
      t3.addField(h2, T2.processMessage(k));
      break;
    case b2:
      U2 || (t3.prepareFeatures(h2), U2 = true), t3.addFeature(h2, T2.processMessageWithArgs(m, t3, h2, h2.fields));
      break;
    case r2:
      h2.uniqueIdField = T2.processMessage(d);
      break;
    default:
      T2.skip();
  }
  return t3.finishFeatureResult(h2), h2;
}
function S(e3, s3) {
  const r2 = 1, a2 = 4, n4 = {};
  let o2 = null;
  for (; e3.next(); ) switch (e3.tag()) {
    case a2:
      o2 = e3.processMessageWithArgs(y);
      break;
    case r2:
      n4.featureResult = e3.processMessageWithArgs(U, s3);
      break;
    default:
      e3.skip();
  }
  return r(o2) && n4.featureResult && s3.addQueryGeometry(n4, o2), n4;
}
function w(t3, r2) {
  try {
    const e3 = 2, a2 = new n(new Uint8Array(t3), new DataView(t3)), n4 = {};
    for (; a2.next(); ) if (a2.tag() === e3) n4.queryResult = a2.processMessageWithArgs(S, r2);
    else a2.skip();
    return n4;
  } catch (a2) {
    throw new s("query:parsing-pbf", "Error while parsing FeatureSet PBF payload", { error: a2 });
  }
}

// node_modules/@arcgis/core/rest/query/operations/pbfQueryUtils.js
function t2(t3, r2) {
  const u2 = w(t3, r2), o2 = u2.queryResult.featureResult, s3 = u2.queryResult.queryGeometry, y2 = u2.queryResult.queryGeometryType;
  if (o2 && o2.features && o2.features.length && o2.objectIdFieldName) {
    const e3 = o2.objectIdFieldName;
    for (const t4 of o2.features) t4.attributes && (t4.objectId = t4.attributes[e3]);
  }
  return o2 && (o2.queryGeometry = s3, o2.queryGeometryType = y2), o2;
}

export {
  a,
  c,
  q,
  t2 as t
};
//# sourceMappingURL=chunk-YMY3DTA5.js.map
