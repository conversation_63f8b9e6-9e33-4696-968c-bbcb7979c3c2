import {
  l
} from "./chunk-UOKTNY52.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";

// node_modules/@arcgis/core/renderers/support/LegendOptions.js
var s;
var p = s = class extends l {
  constructor() {
    super(...arguments), this.title = null;
  }
  clone() {
    return new s({ title: this.title });
  }
};
e([y({ type: String, json: { write: true } })], p.prototype, "title", void 0), p = s = e([a("esri.renderers.support.LegendOptions")], p);

export {
  p
};
//# sourceMappingURL=chunk-UYJR3ZHF.js.map
