{"version": 3, "sources": ["../../@arcgis/core/chunks/Laserlines.glsl.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{deg2rad as e}from\"../core/mathUtils.js\";import{s as i}from\"./vec2.js\";import{a as t}from\"./vec2f64.js\";import{b as n,n as a,l as o,i as r,m as l,c as s,f as c,a as d}from\"./vec3.js\";import{c as p}from\"./vec3f64.js\";import{t as f}from\"./vec4.js\";import{c as h}from\"./vec4f64.js\";import{pointAt as g}from\"../geometry/support/lineSegment.js\";import{create as m,fromPositionAndNormal as u}from\"../geometry/support/plane.js\";import{c as w}from\"./sphere.js\";import{Laserline as x}from\"../views/3d/webgl-engine/core/shaderLibrary/Laserline.glsl.js\";import{ScreenSpacePass as P}from\"../views/3d/webgl-engine/core/shaderLibrary/ScreenSpacePass.glsl.js\";import{Float2PassUniform as v}from\"../views/3d/webgl-engine/core/shaderModules/Float2PassUniform.js\";import{Float3PassUniform as b}from\"../views/3d/webgl-engine/core/shaderModules/Float3PassUniform.js\";import{Float4PassUniform as D}from\"../views/3d/webgl-engine/core/shaderModules/Float4PassUniform.js\";import{FloatPassUniform as S}from\"../views/3d/webgl-engine/core/shaderModules/FloatPassUniform.js\";import{glsl as M}from\"../views/3d/webgl-engine/core/shaderModules/interfaces.js\";import{ShaderBuilder as L}from\"../views/3d/webgl-engine/core/shaderModules/ShaderBuilder.js\";const C=e(6);function A(e){const i=new L;i.extensions.add(\"GL_OES_standard_derivatives\"),i.include(P),i.include(x,e);const t=i.fragment;if(e.lineVerticalPlaneEnabled||e.heightManifoldEnabled)if(t.uniforms.add(new S(\"maxPixelDistance\",((i,t)=>e.heightManifoldEnabled?2*t.camera.computeScreenPixelSizeAt(i.heightManifoldTarget):2*t.camera.computeScreenPixelSizeAt(i.lineVerticalPlaneSegment.origin)))),t.code.add(M`float planeDistancePixels(vec4 plane, vec3 pos) {\nfloat dist = dot(plane.xyz, pos) + plane.w;\nfloat width = fwidth(dist);\ndist /= min(width, maxPixelDistance);\nreturn abs(dist);\n}`),e.spherical){const e=(e,i,t)=>l(e,i.heightManifoldTarget,t.camera.viewMatrix),i=(e,i)=>l(e,[0,0,0],i.camera.viewMatrix);t.uniforms.add([new D(\"heightManifoldOrigin\",((t,r)=>(e(T,t,r),i(_,r),n(_,_,T),a(G,_),G[3]=o(_),G))),new b(\"globalOrigin\",((e,t)=>i(T,t))),new S(\"cosSphericalAngleThreshold\",((e,i)=>1-Math.max(2,r(i.camera.eye,e.heightManifoldTarget)*i.camera.perRenderPixelRatio)/o(e.heightManifoldTarget)))]),t.code.add(M`float globeDistancePixels(float posInGlobalOriginLength) {\nfloat dist = abs(posInGlobalOriginLength - heightManifoldOrigin.w);\nfloat width = fwidth(dist);\ndist /= min(width, maxPixelDistance);\nreturn abs(dist);\n}\nfloat heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {\nvec3 posInGlobalOriginNorm = normalize(globalOrigin - pos);\nfloat cosAngle = dot(posInGlobalOriginNorm, heightManifoldOrigin.xyz);\nvec3 posInGlobalOrigin = globalOrigin - pos;\nfloat posInGlobalOriginLength = length(posInGlobalOrigin);\nfloat sphericalDistance = globeDistancePixels(posInGlobalOriginLength);\nfloat planarDistance = planeDistancePixels(heightPlane, pos);\nreturn cosAngle < cosSphericalAngleThreshold ? sphericalDistance : planarDistance;\n}`)}else t.code.add(M`float heightManifoldDistancePixels(vec4 heightPlane, vec3 pos) {\nreturn planeDistancePixels(heightPlane, pos);\n}`);if(e.pointDistanceEnabled&&(t.uniforms.add(new S(\"maxPixelDistance\",((e,i)=>2*i.camera.computeScreenPixelSizeAt(e.pointDistanceTarget)))),t.code.add(M`float sphereDistancePixels(vec4 sphere, vec3 pos) {\nfloat dist = distance(sphere.xyz, pos) - sphere.w;\nfloat width = fwidth(dist);\ndist /= min(width, maxPixelDistance);\nreturn abs(dist);\n}`)),e.intersectsLineEnabled&&(t.uniforms.add(new S(\"perScreenPixelRatio\",((e,i)=>i.camera.perScreenPixelRatio))),t.code.add(M`float lineDistancePixels(vec3 start, vec3 dir, float radius, vec3 pos) {\nfloat dist = length(cross(dir, pos - start)) / (length(pos) * perScreenPixelRatio);\nreturn abs(dist) - radius;\n}`)),(e.lineVerticalPlaneEnabled||e.intersectsLineEnabled)&&t.code.add(M`bool pointIsWithinLine(vec3 pos, vec3 start, vec3 end) {\nvec3 dir = end - start;\nfloat t2 = dot(dir, pos - start);\nfloat l2 = dot(dir, dir);\nreturn t2 >= 0.0 && t2 <= l2;\n}`),t.code.add(M`void main() {\nvec3 pos;\nvec3 normal;\nfloat depthDiscontinuityAlpha;\nif (!laserlineReconstructFromDepth(pos, normal, depthDiscontinuityAlpha)) {\ndiscard;\n}\nvec4 color = vec4(0, 0, 0, 0);`),e.heightManifoldEnabled){t.uniforms.add([new v(\"angleCutoff\",(e=>V(e))),new D(\"heightPlane\",((e,i)=>z(e.heightManifoldTarget,e.renderCoordsHelper.worldUpAtPosition(e.heightManifoldTarget,T),i.camera.viewMatrix)))]);const i=e.spherical?M`normalize(globalOrigin - pos)`:M`heightPlane.xyz`;t.code.add(M`\n    {\n      float heightManifoldAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, ${i})));\n      vec4 heightManifoldColor = laserlineProfile(heightManifoldDistancePixels(heightPlane, pos));\n      color = max(color, heightManifoldColor * heightManifoldAlpha);\n    }\n    `)}return e.pointDistanceEnabled&&(t.uniforms.add([new v(\"angleCutoff\",(e=>V(e))),new D(\"pointDistanceSphere\",((e,i)=>O(e,i)))]),t.code.add(M`{\nfloat pointDistanceSphereDistance = sphereDistancePixels(pointDistanceSphere, pos);\nvec4 pointDistanceSphereColor = laserlineProfile(pointDistanceSphereDistance);\nfloat pointDistanceSphereAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, normalize(pos - pointDistanceSphere.xyz))));\ncolor = max(color, pointDistanceSphereColor * pointDistanceSphereAlpha);\n}`)),e.lineVerticalPlaneEnabled&&(t.uniforms.add([new v(\"angleCutoff\",(e=>V(e))),new D(\"lineVerticalPlane\",((e,i)=>j(e,i))),new b(\"lineVerticalStart\",((e,i)=>y(e,i))),new b(\"lineVerticalEnd\",((e,i)=>E(e,i)))]),t.code.add(M`{\nif (pointIsWithinLine(pos, lineVerticalStart, lineVerticalEnd)) {\nfloat lineVerticalDistance = planeDistancePixels(lineVerticalPlane, pos);\nvec4 lineVerticalColor = laserlineProfile(lineVerticalDistance);\nfloat lineVerticalAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, abs(dot(normal, lineVerticalPlane.xyz)));\ncolor = max(color, lineVerticalColor * lineVerticalAlpha);\n}\n}`)),e.intersectsLineEnabled&&(t.uniforms.add([new v(\"angleCutoff\",(e=>V(e))),new b(\"intersectsLineStart\",((e,i)=>l(T,e.lineStartWorld,i.camera.viewMatrix))),new b(\"intersectsLineEnd\",((e,i)=>l(T,e.lineEndWorld,i.camera.viewMatrix))),new b(\"intersectsLineDirection\",((e,i)=>(s(G,e.intersectsLineSegment.vector),G[3]=0,a(T,f(G,G,i.camera.viewMatrix))))),new S(\"intersectsLineRadius\",(e=>e.intersectsLineRadius))]),t.code.add(M`{\nif (pointIsWithinLine(pos, intersectsLineStart, intersectsLineEnd)) {\nfloat intersectsLineDistance = lineDistancePixels(intersectsLineStart, intersectsLineDirection, intersectsLineRadius, pos);\nvec4 intersectsLineColor = laserlineProfile(intersectsLineDistance);\nfloat intersectsLineAlpha = 1.0 - smoothstep(angleCutoff.x, angleCutoff.y, 1.0 - abs(dot(normal, intersectsLineDirection)));\ncolor = max(color, intersectsLineColor * intersectsLineAlpha);\n}\n}`)),t.code.add(M`gl_FragColor = laserlineOutput(color * depthDiscontinuityAlpha);\n}`),i}function V(t){return i(I,Math.cos(t.angleCutoff),Math.cos(Math.max(0,t.angleCutoff-e(2))))}function O(e,i){return l(W,e.pointDistanceOrigin,i.camera.viewMatrix),W[3]=r(e.pointDistanceOrigin,e.pointDistanceTarget),W}function j(e,i){const t=g(e.lineVerticalPlaneSegment,.5,T),n=e.renderCoordsHelper.worldUpAtPosition(t,R),o=a(_,e.lineVerticalPlaneSegment.vector),r=c(G,n,o);return a(r,r),z(e.lineVerticalPlaneSegment.origin,r,i.camera.viewMatrix)}function y(e,i){const t=s(T,e.lineVerticalPlaneSegment.origin);return e.renderCoordsHelper.setAltitude(t,0),l(t,t,i.camera.viewMatrix)}function E(e,i){const t=d(T,e.lineVerticalPlaneSegment.origin,e.lineVerticalPlaneSegment.vector);return e.renderCoordsHelper.setAltitude(t,0),l(t,t,i.camera.viewMatrix)}function z(e,i,t){return l(U,e,t),s(G,i),G[3]=0,f(G,G,t),u(U,G,F)}const I=t(),T=p(),G=h(),R=p(),_=p(),U=p(),F=m(),W=w(),H=Object.freeze(Object.defineProperty({__proto__:null,build:A,defaultAngleCutoff:C},Symbol.toStringTag,{value:\"Module\"}));export{H as L,A as b,C as d};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2sC,IAAM,IAAE,EAAE,CAAC;AAAE,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAE,IAAIC;AAAE,EAAAD,GAAE,WAAW,IAAI,6BAA6B,GAAEA,GAAE,QAAQC,EAAC,GAAED,GAAE,QAAQ,GAAED,EAAC;AAAE,QAAM,IAAEC,GAAE;AAAS,MAAGD,GAAE,4BAA0BA,GAAE,sBAAsB,KAAG,EAAE,SAAS,IAAI,IAAIE,GAAE,oBAAoB,CAACD,IAAEE,OAAIH,GAAE,wBAAsB,IAAEG,GAAE,OAAO,yBAAyBF,GAAE,oBAAoB,IAAE,IAAEE,GAAE,OAAO,yBAAyBF,GAAE,yBAAyB,MAAM,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpmD,GAAED,GAAE,WAAU;AAAC,UAAMA,KAAE,CAACA,IAAEC,IAAEE,OAAI,EAAEH,IAAEC,GAAE,sBAAqBE,GAAE,OAAO,UAAU,GAAEF,KAAE,CAACD,IAAEC,OAAI,EAAED,IAAE,CAAC,GAAE,GAAE,CAAC,GAAEC,GAAE,OAAO,UAAU;AAAE,MAAE,SAAS,IAAI,CAAC,IAAID,GAAE,wBAAwB,CAACG,IAAEC,QAAKJ,GAAE,GAAEG,IAAEC,EAAC,GAAEH,GAAEI,IAAED,EAAC,GAAE,EAAEC,IAAEA,IAAE,CAAC,GAAE,EAAE,GAAEA,EAAC,GAAE,EAAE,CAAC,IAAE,EAAEA,EAAC,GAAE,EAAG,GAAE,IAAIL,GAAE,gBAAgB,CAACA,IAAEG,OAAIF,GAAE,GAAEE,EAAC,CAAE,GAAE,IAAID,GAAE,8BAA8B,CAACF,IAAEC,OAAI,IAAE,KAAK,IAAI,GAAE,EAAEA,GAAE,OAAO,KAAID,GAAE,oBAAoB,IAAEC,GAAE,OAAO,mBAAmB,IAAE,EAAED,GAAE,oBAAoB,CAAE,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc3a;AAAA,EAAC,MAAM,GAAE,KAAK,IAAI;AAAA;AAAA,EAElB;AAAE,MAAGA,GAAE,yBAAuB,EAAE,SAAS,IAAI,IAAIE,GAAE,oBAAoB,CAACF,IAAEC,OAAI,IAAEA,GAAE,OAAO,yBAAyBD,GAAE,mBAAmB,CAAE,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvJ,IAAGA,GAAE,0BAAwB,EAAE,SAAS,IAAI,IAAIE,GAAE,uBAAuB,CAACF,IAAEC,OAAIA,GAAE,OAAO,mBAAoB,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA,EAG3H,KAAID,GAAE,4BAA0BA,GAAE,0BAAwB,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrE,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAOgB,GAAEA,GAAE,uBAAsB;AAAC,MAAE,SAAS,IAAI,CAAC,IAAIA,GAAE,eAAe,CAAAA,OAAG,EAAEA,EAAC,CAAE,GAAE,IAAIA,GAAE,eAAe,CAACA,IAAEC,OAAIK,GAAEN,GAAE,sBAAqBA,GAAE,mBAAmB,kBAAkBA,GAAE,sBAAqB,CAAC,GAAEC,GAAE,OAAO,UAAU,CAAE,CAAC,CAAC;AAAE,UAAMA,KAAED,GAAE,YAAU,mCAAiC;AAAmB,MAAE,KAAK,IAAI;AAAA;AAAA,mGAExOC,EAAC;AAAA;AAAA;AAAA;AAAA,KAI/F;AAAA,EAAC;AAAC,SAAOD,GAAE,yBAAuB,EAAE,SAAS,IAAI,CAAC,IAAIA,GAAE,eAAe,CAAAA,OAAG,EAAEA,EAAC,CAAE,GAAE,IAAIA,GAAE,uBAAuB,CAACA,IAAEC,OAAIM,GAAEP,IAAEC,EAAC,CAAE,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9I,IAAGD,GAAE,6BAA2B,EAAE,SAAS,IAAI,CAAC,IAAIA,GAAE,eAAe,CAAAA,OAAG,EAAEA,EAAC,CAAE,GAAE,IAAIA,GAAE,qBAAqB,CAACA,IAAEC,OAAI,EAAED,IAAEC,EAAC,CAAE,GAAE,IAAID,GAAE,qBAAqB,CAACA,IAAEC,OAAI,EAAED,IAAEC,EAAC,CAAE,GAAE,IAAID,GAAE,mBAAmB,CAACA,IAAEC,OAAI,EAAED,IAAEC,EAAC,CAAE,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO3N,IAAGD,GAAE,0BAAwB,EAAE,SAAS,IAAI,CAAC,IAAIA,GAAE,eAAe,CAAAA,OAAG,EAAEA,EAAC,CAAE,GAAE,IAAIA,GAAE,uBAAuB,CAACA,IAAEC,OAAI,EAAE,GAAED,GAAE,gBAAeC,GAAE,OAAO,UAAU,CAAE,GAAE,IAAID,GAAE,qBAAqB,CAACA,IAAEC,OAAI,EAAE,GAAED,GAAE,cAAaC,GAAE,OAAO,UAAU,CAAE,GAAE,IAAID,GAAE,2BAA2B,CAACA,IAAEC,QAAK,EAAE,GAAED,GAAE,sBAAsB,MAAM,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,GAAE,EAAE,GAAE,GAAEC,GAAE,OAAO,UAAU,CAAC,EAAG,GAAE,IAAIC,GAAE,wBAAwB,CAAAF,OAAGA,GAAE,oBAAqB,CAAC,CAAC,GAAE,EAAE,KAAK,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOta,IAAG,EAAE,KAAK,IAAI;AAAA,EACd,GAAEC;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAOG,GAAE,GAAE,KAAK,IAAI,EAAE,WAAW,GAAE,KAAK,IAAI,KAAK,IAAI,GAAE,EAAE,cAAY,EAAE,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,SAASG,GAAEP,IAAEC,IAAE;AAAC,SAAO,EAAE,GAAED,GAAE,qBAAoBC,GAAE,OAAO,UAAU,GAAE,EAAE,CAAC,IAAE,EAAED,GAAE,qBAAoBA,GAAE,mBAAmB,GAAE;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAED,GAAE,0BAAyB,KAAG,CAAC,GAAEQ,KAAER,GAAE,mBAAmB,kBAAkB,GAAES,EAAC,GAAEP,KAAE,EAAEG,IAAEL,GAAE,yBAAyB,MAAM,GAAEI,KAAE,EAAE,GAAEI,IAAEN,EAAC;AAAE,SAAO,EAAEE,IAAEA,EAAC,GAAEE,GAAEN,GAAE,yBAAyB,QAAOI,IAAEH,GAAE,OAAO,UAAU;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,GAAED,GAAE,yBAAyB,MAAM;AAAE,SAAOA,GAAE,mBAAmB,YAAY,GAAE,CAAC,GAAE,EAAE,GAAE,GAAEC,GAAE,OAAO,UAAU;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE;AAAC,QAAM,IAAE,EAAE,GAAED,GAAE,yBAAyB,QAAOA,GAAE,yBAAyB,MAAM;AAAE,SAAOA,GAAE,mBAAmB,YAAY,GAAE,CAAC,GAAE,EAAE,GAAE,GAAEC,GAAE,OAAO,UAAU;AAAC;AAAC,SAASK,GAAEN,IAAEC,IAAE,GAAE;AAAC,SAAO,EAAE,GAAED,IAAE,CAAC,GAAE,EAAE,GAAEC,EAAC,GAAE,EAAE,CAAC,IAAE,GAAE,EAAE,GAAE,GAAE,CAAC,GAAEI,GAAE,GAAE,GAAE,CAAC;AAAC;AAAC,IAAM,IAAEG,GAAE;AAAV,IAAY,IAAE,EAAE;AAAhB,IAAkB,IAAEA,GAAE;AAAtB,IAAwBC,KAAE,EAAE;AAA5B,IAA8BJ,KAAE,EAAE;AAAlC,IAAoC,IAAE,EAAE;AAAxC,IAA0C,IAAE,EAAE;AAA9C,IAAgD,IAAE,EAAE;AAApD,IAAsD,IAAE,OAAO,OAAO,OAAO,eAAe,EAAC,WAAU,MAAK,OAAM,GAAE,oBAAmB,EAAC,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;", "names": ["e", "i", "o", "t", "r", "_", "z", "O", "n", "R"]}