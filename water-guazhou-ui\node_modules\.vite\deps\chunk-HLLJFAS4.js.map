{"version": 3, "sources": ["../../@arcgis/core/core/sql/WhereClauseCache.js", "../../@arcgis/core/layers/graphics/data/attributeSupport.js", "../../@arcgis/core/layers/graphics/data/AttributesBuilder.js", "../../@arcgis/core/layers/graphics/data/QueryEngineResult.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../ItemCache.js\";import{WhereClause as e}from\"./WhereClause.js\";class c{constructor(e,c){this._cache=new t(e),this._invalidCache=new t(c)}get(t,c){const i=`${c.uid}:${t}`,r=this._cache.get(i);if(r)return r;if(void 0!==this._invalidCache.get(i))return null;try{const r=e.create(t,c);return this._cache.put(i,r),r}catch{return this._invalidCache.put(i,null),null}}}export{c as WhereClauseCache};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Error.js\";import{WhereClauseCache as i}from\"../../../core/sql/WhereClauseCache.js\";const n=new i(50,500),s=\"feature-store:unsupported-query\",t=\" as \",r=new Set([\"esriFieldTypeOID\",\"esriFieldTypeSmallInteger\",\"esriFieldTypeInteger\",\"esriFieldTypeSingle\",\"esriFieldTypeDouble\",\"esriFieldTypeLong\",\"esriFieldTypeDate\"]);function o(i,t){if(!t)return!0;const r=n.get(t,i);if(!r)throw new e(s,\"invalid SQL expression\",{where:t});if(!r.isStandardized)throw new e(s,\"where clause is not standard\",{where:t});return c(i,r.fieldNames,\"where clause contains missing fields\"),!0}function a(i,t,r){if(!t)return!0;const o=n.get(t,i);if(!o)throw new e(s,\"invalid SQL expression\",{having:t});if(!o.isAggregate)throw new e(s,\"having does not contain a valid aggregate function\",{having:t});const a=o.fieldNames;c(i,a,\"having contains missing fields\");if(!o.getExpressions().every((e=>{const{aggregateType:n,field:s}=e,t=i.get(s)?.name;return r.some((e=>{const{onStatisticField:s,statisticType:r}=e,o=i.get(s)?.name;return o===t&&r.toLowerCase().trim()===n}))})))throw new e(s,\"expressions in having should also exist in outStatistics\",{having:t});return!0}function l(e,i){return e?n.get(e,i):null}function c(i,n,t,r=!0){const o=[];for(const u of n)if(\"*\"!==u&&!i.has(u))if(r){const n=d(u);try{const t=l(n,i);if(!t)throw new e(s,\"invalid SQL expression\",{where:n});if(!t.isStandardized)throw new e(s,\"expression is not standard\",{clause:t});c(i,t.fieldNames,\"expression contains missing fields\")}catch(a){const e=a&&a.details;if(e&&(e.clause||e.where))throw a;e&&e.missingFields?o.push(...e.missingFields):o.push(u)}}else o.push(u);if(o.length)throw new e(s,t,{missingFields:o})}function d(e){return e.split(t)[0]}function u(e){return e.split(t)[1]}function f(e,i){const n=i.get(e);return!!n&&!r.has(n.type)}export{u as getAliasFromFieldName,d as getExpressionFromFieldName,l as getWhereClause,f as hasInvalidFieldType,c as validateFields,a as validateHaving,o as validateWhere};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as t}from\"../../../core/maybe.js\";import{getExpressionFromFieldName as e,getWhereClause as i,getAliasFromFieldName as s}from\"./attributeSupport.js\";import{processNullValue as a,getNormalizedValue as l}from\"../../../statistics/utils.js\";class r{constructor(t,a,l){this._fieldDataCache=new Map,this._returnDistinctMap=new Map,this.returnDistinctValues=t.returnDistinctValues??!1,this.fieldsIndex=l,this.featureAdapter=a;const r=t.outFields;if(r&&!r.includes(\"*\")){this.outFields=r;let t=0;for(const a of r){const r=e(a),n=this.fieldsIndex.get(r),u=n?null:i(r,l),d=n?n.name:s(a)||\"FIELD_EXP_\"+t++;this._fieldDataCache.set(a,{alias:d,clause:u})}}}countDistinctValues(t){return this.returnDistinctValues?(t.forEach((t=>this.getAttributes(t))),this._returnDistinctMap.size):t.length}getAttributes(t){const e=this._processAttributesForOutFields(t);return this._processAttributesForDistinctValues(e)}getFieldValue(t,e,s){const a=s?s.name:e;let l=null;return this._fieldDataCache.has(a)?l=this._fieldDataCache.get(a)?.clause:s||(l=i(e,this.fieldsIndex),this._fieldDataCache.set(a,{alias:a,clause:l})),s?this.featureAdapter.getAttribute(t,a):l?.calculateValue(t,this.featureAdapter)}getDataValue(t,e){const i=e.normalizationType,s=e.normalizationTotal;let r=e.field&&this.getFieldValue(t,e.field,this.fieldsIndex.get(e.field));if(e.field2&&(r=`${a(r)}${e.fieldDelimiter}${a(this.getFieldValue(t,e.field2,this.fieldsIndex.get(e.field2)))}`,e.field3&&(r=`${r}${e.fieldDelimiter}${a(this.getFieldValue(t,e.field3,this.fieldsIndex.get(e.field3)))}`)),i&&Number.isFinite(r)){const a=\"field\"===i&&e.normalizationField?this.getFieldValue(t,e.normalizationField,this.fieldsIndex.get(e.normalizationField)):null;r=l(r,i,a,s)}return r}getExpressionValue(t,e,i,s){const a={attributes:this.featureAdapter.getAttributes(t),layer:{fields:this.fieldsIndex.fields}},l=s.createExecContext(a,i);return s.executeFunction(e,l)}getExpressionValues(t,e,i,s){const a={fields:this.fieldsIndex.fields};return t.map((t=>{const l={attributes:this.featureAdapter.getAttributes(t),layer:a},r=s.createExecContext(l,i);return s.executeFunction(e,r)}))}validateItem(t,e){return this._fieldDataCache.has(e)||this._fieldDataCache.set(e,{alias:e,clause:i(e,this.fieldsIndex)}),this._fieldDataCache.get(e)?.clause?.testFeature(t,this.featureAdapter)??!1}validateItems(t,e){return this._fieldDataCache.has(e)||this._fieldDataCache.set(e,{alias:e,clause:i(e,this.fieldsIndex)}),this._fieldDataCache.get(e)?.clause?.testSet(t,this.featureAdapter)??!1}_processAttributesForOutFields(t){const e=this.outFields;if(!e||!e.length)return this.featureAdapter.getAttributes(t);const i={};for(const s of e){const{alias:e,clause:a}=this._fieldDataCache.get(s);i[e]=a?a.calculateValue(t,this.featureAdapter):this.featureAdapter.getAttribute(t,e)}return i}_processAttributesForDistinctValues(e){if(t(e)||!this.returnDistinctValues)return e;const i=this.outFields,s=[];if(i)for(const t of i){const{alias:i}=this._fieldDataCache.get(t);s.push(e[i])}else for(const t in e)s.push(e[t]);const a=`${(i||[\"*\"]).join(\",\")}=${s.join(\",\")}`;let l=this._returnDistinctMap.get(a)||0;return this._returnDistinctMap.set(a,++l),l>1?null:e}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as t}from\"../../../core/maybe.js\";import{polygonCentroid as i,extentCentroid as s}from\"../../../geometry/support/centroid.js\";import{getPolygonExtent as a,getGeometryExtent as r}from\"../../../geometry/support/extentUtils.js\";import{toQuantizationTransform as n}from\"../../../geometry/support/quantizationUtils.js\";import{isValid as o,equals as l}from\"../../../geometry/support/spatialReferenceUtils.js\";import u from\"./AttributesBuilder.js\";import{project as c}from\"./projectionSupport.js\";import{makeEdgeCandidate as d,makeVertexCandidate as m}from\"./SnappingCandidate.js\";import{cleanFromGeometryEngine as h,getGeometry as f,transformCentroid as g}from\"./utils.js\";import{isStringField as p}from\"../../support/fieldUtils.js\";import{isNullCountSupported as y,calculateStringStatistics as x,calculateStatistics as I,processSummaryStatisticsResult as T,calculateUniqueValuesCount as V,createUVResult as F,calculateClassBreaks as S,resolveCBResult as _,calculateHistogram as z,getAttributeComparator as v,calculatePercentile as b}from\"../../../statistics/utils.js\";import{loadArcade as R}from\"../../../support/arcadeOnDemand.js\";class A{constructor(e,t,i){this.items=e,this.query=t,this.geometryType=i.geometryType,this.hasM=i.hasM,this.hasZ=i.hasZ,this.fieldsIndex=i.fieldsIndex,this.objectIdField=i.objectIdField,this.spatialReference=i.spatialReference,this.featureAdapter=i.featureAdapter}get size(){return this.items.length}createQueryResponseForCount(){const e=new u(this.query,this.featureAdapter,this.fieldsIndex);if(!this.query.outStatistics)return e.countDistinctValues(this.items);const{groupByFieldsForStatistics:t,having:i,outStatistics:s}=this.query,a=t?.length;if(!!!a)return 1;const r=new Map,n=new Map,o=new Set;for(const l of s){const{statisticType:s}=l,a=\"exceedslimit\"!==s?l.onStatisticField:void 0;if(!n.has(a)){const i=[];for(const s of t){const t=this._getAttributeValues(e,s,r);i.push(t)}n.set(a,this._calculateUniqueValues(i,e.returnDistinctValues))}const u=n.get(a);for(const t in u){const{data:s,items:a}=u[t],r=s.join(\",\");i&&!e.validateItems(a,i)||o.add(r)}}return o.size}async createQueryResponse(){let e;if(this.query.outStatistics){e=this.query.outStatistics.some((e=>\"exceedslimit\"===e.statisticType))?this._createExceedsLimitQueryResponse(this.query):await this._createStatisticsQueryResponse(this.query)}else e=this._createFeatureQueryResponse(this.query);if(this.query.returnQueryGeometry){const t=this.query.geometry;o(this.query.outSR)&&!l(t.spatialReference,this.query.outSR)?e.queryGeometry=h({spatialReference:this.query.outSR,...c(t,t.spatialReference,this.query.outSR)}):e.queryGeometry=h({spatialReference:this.query.outSR,...t})}return e}createSnappingResponse(t,i){const s=this.featureAdapter,a=N(this.hasZ,this.hasM),{point:r,mode:n}=t,o=\"number\"==typeof t.distance?t.distance:t.distance.x,l=\"number\"==typeof t.distance?t.distance:t.distance.y,u={candidates:[]},c=\"esriGeometryPolygon\"===this.geometryType,h=this._getPointCreator(n,this.spatialReference,i),f=new q(null,0),g=new q(null,0),p={x:0,y:0,z:0};for(const y of this.items){const i=s.getGeometry(y);if(e(i))continue;const{coords:n,lengths:x}=i;if(f.coords=n,g.coords=n,t.types&D.EDGE){let e=0;for(let t=0;t<x.length;t++){const i=x[t];for(let t=0;t<i;t++,e+=a){const n=f;if(n.coordsIndex=e,t!==i-1){const t=g;t.coordsIndex=e+a;const i=p;E(p,r,n,t);const c=(r.x-i.x)/o,m=(r.y-i.y)/l,f=c*c+m*m;f<=1&&u.candidates.push(d(s.getObjectId(y),h(i),Math.sqrt(f),h(n),h(t)))}}}}if(t.types&D.VERTEX){const e=c?n.length-a:n.length;for(let t=0;t<e;t+=a){const e=f;e.coordsIndex=t;const i=(r.x-e.x)/o,a=(r.y-e.y)/l,n=i*i+a*a;n<=1&&u.candidates.push(m(s.getObjectId(y),h(e),Math.sqrt(n)))}}}return u.candidates.sort(((e,t)=>e.distance-t.distance)),u}_getPointCreator(e,i,s){const a=t(s)&&!l(i,s)?e=>c(e,i,s):e=>e,{hasZ:r}=this,n=0;return\"3d\"===e?r?({x:e,y:t,z:i})=>a({x:e,y:t,z:i}):({x:e,y:t})=>a({x:e,y:t,z:n}):({x:e,y:t})=>a({x:e,y:t})}async createSummaryStatisticsResponse(e){const{field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,minValue:n,maxValue:o,scale:l}=e,u=this.fieldsIndex.isDateField(t),c=await this._getDataValues({field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,scale:l}),d=y({normalizationType:a,normalizationField:s,minValue:n,maxValue:o}),m=this.fieldsIndex.get(t),h={value:.5,fieldType:m?.type},f=p(m)?x({values:c,supportsNullCount:d,percentileParams:h}):I({values:c,minValue:n,maxValue:o,useSampleStdDev:!a,supportsNullCount:d,percentileParams:h});return T(f,u)}async createUniqueValuesResponse(e){const{field:t,valueExpression:i,domains:s,returnAllCodedValues:a,scale:r}=e,n=await this._getDataValues({field:t,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,valueExpression:i,scale:r}),o=V(n);return F(o,s,a,e.fieldDelimiter)}async createClassBreaksResponse(e){const{field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,classificationMethod:n,standardDeviationInterval:o,minValue:l,maxValue:u,numClasses:c,scale:d}=e,m=await this._getDataValues({field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,scale:d}),h=S(m,{field:t,normalizationField:s,normalizationType:a,normalizationTotal:r,classificationMethod:n,standardDeviationInterval:o,minValue:l,maxValue:u,numClasses:c});return _(h,n)}async createHistogramResponse(e){const{field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,classificationMethod:n,standardDeviationInterval:o,minValue:l,maxValue:u,numBins:c,scale:d}=e,m=await this._getDataValues({field:t,valueExpression:i,normalizationField:s,normalizationType:a,normalizationTotal:r,scale:d});return z(m,{field:t,normalizationField:s,normalizationType:a,normalizationTotal:r,classificationMethod:n,standardDeviationInterval:o,minValue:l,maxValue:u,numBins:c})}_sortFeatures(e,t,i){if(e.length>1&&t&&t.length)for(const s of t.reverse()){const t=s.split(\" \"),a=t[0],r=this.fieldsIndex.get(a),n=!!t[1]&&\"desc\"===t[1].toLowerCase(),o=v(r?.type,n);e.sort(((e,t)=>{const s=i(e,a,r),n=i(t,a,r);return o(s,n)}))}}_createFeatureQueryResponse(e){const t=this.items,{geometryType:i,hasM:s,hasZ:a,objectIdField:r,spatialReference:o}=this,{outFields:l,outSR:u,quantizationParameters:c,resultRecordCount:d,resultOffset:m,returnZ:f,returnM:g}=e,p=null!=d&&t.length>(m||0)+d,y=l&&(l.includes(\"*\")?[...this.fieldsIndex.fields]:l.map((e=>this.fieldsIndex.get(e))));return{exceededTransferLimit:p,features:this._createFeatures(e,t),fields:y,geometryType:i,hasM:s&&g,hasZ:a&&f,objectIdFieldName:r,spatialReference:h(u||o),transform:c&&n(c)||null}}_createFeatures(e,t){const i=new u(e,this.featureAdapter,this.fieldsIndex),{hasM:s,hasZ:a}=this,{orderByFields:r,quantizationParameters:o,returnGeometry:l,returnCentroid:c,maxAllowableOffset:d,resultOffset:m,resultRecordCount:h,returnZ:p=!1,returnM:y=!1}=e,x=a&&p,I=s&&y;let T=[],V=0;const F=[...t];if(this._sortFeatures(F,r,((e,t,s)=>i.getFieldValue(e,t,s))),l||c){const e=n(o)??void 0;if(l&&!c)for(const t of F)T[V++]={attributes:i.getAttributes(t),geometry:f(this.geometryType,this.hasZ,this.hasM,this.featureAdapter.getGeometry(t),d,e,x,I)};else if(!l&&c)for(const t of F)T[V++]={attributes:i.getAttributes(t),centroid:g(this,this.featureAdapter.getCentroid(t,this),e)};else for(const t of F)T[V++]={attributes:i.getAttributes(t),centroid:g(this,this.featureAdapter.getCentroid(t,this),e),geometry:f(this.geometryType,this.hasZ,this.hasM,this.featureAdapter.getGeometry(t),d,e,x,I)}}else for(const n of F){const e=i.getAttributes(n);e&&(T[V++]={attributes:e})}const S=m||0;if(null!=h){const e=S+h;T=T.slice(S,Math.min(T.length,e))}return T}_createExceedsLimitQueryResponse(e){let i=!1,s=Number.POSITIVE_INFINITY,a=Number.POSITIVE_INFINITY,r=Number.POSITIVE_INFINITY;for(const t of e.outStatistics??[])if(\"exceedslimit\"===t.statisticType){s=null!=t.maxPointCount?t.maxPointCount:Number.POSITIVE_INFINITY,a=null!=t.maxRecordCount?t.maxRecordCount:Number.POSITIVE_INFINITY,r=null!=t.maxVertexCount?t.maxVertexCount:Number.POSITIVE_INFINITY;break}if(\"esriGeometryPoint\"===this.geometryType)i=this.items.length>s;else if(this.items.length>a)i=!0;else{const e=N(this.hasZ,this.hasM),s=this.featureAdapter;i=this.items.reduce(((e,i)=>{const a=s.getGeometry(i);return e+(t(a)&&a.coords.length||0)}),0)/e>r}return{fields:[{name:\"exceedslimit\",type:\"esriFieldTypeInteger\",alias:\"exceedslimit\",sqlType:\"sqlTypeInteger\",domain:null,defaultValue:null}],features:[{attributes:{exceedslimit:Number(i)}}]}}async _createStatisticsQueryResponse(e){const t={attributes:{}},i=[],s=new Map,a=new Map,r=new Map,n=new Map,o=new u(e,this.featureAdapter,this.fieldsIndex),l=e.outStatistics,{groupByFieldsForStatistics:c,having:d,orderByFields:m}=e,h=c&&c.length,f=!!h,g=f?c[0]:null,p=f&&!this.fieldsIndex.get(g);for(const u of l??[]){const{outStatisticFieldName:e,statisticType:l}=u,m=u,y=\"exceedslimit\"!==l?u.onStatisticField:void 0,x=\"percentile_disc\"===l||\"percentile_cont\"===l,I=\"EnvelopeAggregate\"===l||\"CentroidAggregate\"===l||\"ConvexHullAggregate\"===l,T=f&&1===h&&(y===g||p)&&\"count\"===l;if(f){if(!r.has(y)){const e=[];for(const t of c){const i=this._getAttributeValues(o,t,s);e.push(i)}r.set(y,this._calculateUniqueValues(e,!I&&o.returnDistinctValues))}const t=r.get(y);for(const i in t){const{count:a,data:r,items:l,itemPositions:u}=t[i],h=r.join(\",\");if(!d||o.validateItems(l,d)){const t=n.get(h)||{attributes:{}};if(I){t.aggregateGeometries||(t.aggregateGeometries={});const{aggregateGeometries:e,outStatisticFieldName:i}=await this._getAggregateGeometry(m,l);t.aggregateGeometries[i]=e}else{let i=null;if(T)i=a;else{const e=this._getAttributeValues(o,y,s),t=u.map((t=>e[t]));i=x&&\"statisticParameters\"in m?this._getPercentileValue(m,t):this._getStatisticValue(m,t,null,o.returnDistinctValues)}t.attributes[e]=i}let i=0;c.forEach(((e,s)=>t.attributes[this.fieldsIndex.get(e)?e:\"EXPR_\"+ ++i]=r[s])),n.set(h,t)}}}else if(I){t.aggregateGeometries||(t.aggregateGeometries={});const{aggregateGeometries:e,outStatisticFieldName:i}=await this._getAggregateGeometry(m,this.items);t.aggregateGeometries[i]=e}else{const i=this._getAttributeValues(o,y,s);t.attributes[e]=x&&\"statisticParameters\"in m?this._getPercentileValue(m,i):this._getStatisticValue(m,i,a,o.returnDistinctValues)}i.push({name:e,alias:e,type:\"esriFieldTypeDouble\"})}const y=f?Array.from(n.values()):[t];return this._sortFeatures(y,m,((e,t)=>e.attributes[t])),{fields:i,features:y}}async _getAggregateGeometry(e,t){const n=await import(\"../../../geometry/geometryEngineJSON.js\"),{statisticType:o,outStatisticFieldName:l}=e,{featureAdapter:u,spatialReference:c,geometryType:d,hasZ:m,hasM:h}=this,g=t.map((e=>f(d,m,h,u.getGeometry(e)))),p=n.convexHull(c,g,!0)[0],y={aggregateGeometries:null,outStatisticFieldName:null};if(\"EnvelopeAggregate\"===o){const e=p?a(p):r(n.union(c,g));y.aggregateGeometries={...e,spatialReference:c},y.outStatisticFieldName=l||\"extent\"}else if(\"CentroidAggregate\"===o){const e=p?i(p):s(r(n.union(c,g)));y.aggregateGeometries={x:e[0],y:e[1],spatialReference:c},y.outStatisticFieldName=l||\"centroid\"}else\"ConvexHullAggregate\"===o&&(y.aggregateGeometries=p,y.outStatisticFieldName=l||\"convexHull\");return y}_getStatisticValue(e,t,i,s){const{onStatisticField:a,statisticType:r}=e;let n=null;n=i?.has(a)?i.get(a):p(this.fieldsIndex.get(a))?x({values:t,returnDistinct:s}):I({values:s?[...new Set(t)]:t,minValue:null,maxValue:null,useSampleStdDev:!0}),i&&i.set(a,n);return n[\"var\"===r?\"variance\":r]}_getPercentileValue(e,t){const{onStatisticField:i,statisticParameters:s,statisticType:a}=e,{value:r,orderBy:n}=s,o=this.fieldsIndex.get(i);return b(t,{value:r,orderBy:n,fieldType:o?.type,isDiscrete:\"percentile_disc\"===a})}_getAttributeValues(e,t,i){if(i.has(t))return i.get(t);const s=this.fieldsIndex.get(t),a=this.items.map((i=>e.getFieldValue(i,t,s)));return i.set(t,a),a}_getAttributeDataValues(e,t){return this.items.map((i=>e.getDataValue(i,{field:t.field,field2:t.field2,field3:t.field3,fieldDelimiter:t.fieldDelimiter,normalizationField:t.normalizationField,normalizationType:t.normalizationType,normalizationTotal:t.normalizationTotal})))}async _getAttributeExpressionValues(e,t,i){const{arcadeUtils:s}=await R(),a=s.createFunction(t),r=i&&s.getViewInfo(i);return e.getExpressionValues(this.items,a,r,s)}_calculateUniqueValues(e,t){const i={},s=this.items,a=s.length;for(let r=0;r<a;r++){const a=s[r],n=[];for(const t of e)n.push(t[r]);const o=n.join(\",\");null==i[o]?i[o]={count:1,data:n,items:[a],itemPositions:[r]}:(t||i[o].count++,i[o].items.push(a),i[o].itemPositions.push(r))}return i}async _getDataValues(e){const t=new u(this.query,this.featureAdapter,this.fieldsIndex),{valueExpression:i,field:s,normalizationField:a,normalizationType:r,normalizationTotal:n,scale:o}=e,l=i?{viewingMode:\"map\",scale:o,spatialReference:this.query.outSR||this.spatialReference}:null;return i?this._getAttributeExpressionValues(t,i,l):this._getAttributeDataValues(t,{field:s,field2:e.field2,field3:e.field3,fieldDelimiter:e.fieldDelimiter,normalizationField:a,normalizationType:r,normalizationTotal:n})}}function E(e,t,i,s){const a=s.x-i.x,r=s.y-i.y,n=a*a+r*r,o=(t.x-i.x)*a+(t.y-i.y)*r,l=Math.min(1,Math.max(0,o/n));e.x=i.x+a*l,e.y=i.y+r*l}function N(e,t){return e?t?4:3:t?3:2}var D;!function(e){e[e.NONE=0]=\"NONE\",e[e.EDGE=1]=\"EDGE\",e[e.VERTEX=2]=\"VERTEX\"}(D||(D={}));class q{constructor(e,t){this.coords=e,this.coordsIndex=t}get x(){return this.coords[this.coordsIndex]}get y(){return this.coords[this.coordsIndex+1]}get z(){return this.coords[this.coordsIndex+2]}}export{A as QueryEngineResult,D as SnappingTypes};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8E,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAED,IAAE;AAAC,SAAK,SAAO,IAAIC,GAAEA,EAAC,GAAE,KAAK,gBAAc,IAAIA,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIE,IAAEF,IAAE;AAAC,UAAMG,KAAE,GAAGH,GAAE,GAAG,IAAIE,EAAC,IAAGE,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,QAAGC,GAAE,QAAOA;AAAE,QAAG,WAAS,KAAK,cAAc,IAAID,EAAC,EAAE,QAAO;AAAK,QAAG;AAAC,YAAMC,KAAEC,GAAE,OAAOH,IAAEF,EAAC;AAAE,aAAO,KAAK,OAAO,IAAIG,IAAEC,EAAC,GAAEA;AAAA,IAAC,QAAM;AAAC,aAAO,KAAK,cAAc,IAAID,IAAE,IAAI,GAAE;AAAA,IAAI;AAAA,EAAC;AAAC;;;ACAzQ,IAAM,IAAE,IAAIG,GAAE,IAAG,GAAG;AAApB,IAAsBC,KAAE;AAAxB,IAA0DC,KAAE;AAA5D,IAAmEC,KAAE,oBAAI,IAAI,CAAC,oBAAmB,6BAA4B,wBAAuB,uBAAsB,uBAAsB,qBAAoB,mBAAmB,CAAC;AAAE,SAAS,EAAEC,IAAEF,IAAE;AAAC,MAAG,CAACA,GAAE,QAAM;AAAG,QAAMC,KAAE,EAAE,IAAID,IAAEE,EAAC;AAAE,MAAG,CAACD,GAAE,OAAM,IAAI,EAAEF,IAAE,0BAAyB,EAAC,OAAMC,GAAC,CAAC;AAAE,MAAG,CAACC,GAAE,eAAe,OAAM,IAAI,EAAEF,IAAE,gCAA+B,EAAC,OAAMC,GAAC,CAAC;AAAE,SAAOF,GAAEI,IAAED,GAAE,YAAW,sCAAsC,GAAE;AAAE;AAAC,SAAS,EAAEC,IAAEF,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE,QAAM;AAAG,QAAMG,KAAE,EAAE,IAAIH,IAAEE,EAAC;AAAE,MAAG,CAACC,GAAE,OAAM,IAAI,EAAEJ,IAAE,0BAAyB,EAAC,QAAOC,GAAC,CAAC;AAAE,MAAG,CAACG,GAAE,YAAY,OAAM,IAAI,EAAEJ,IAAE,sDAAqD,EAAC,QAAOC,GAAC,CAAC;AAAE,QAAMI,KAAED,GAAE;AAAW,EAAAL,GAAEI,IAAEE,IAAE,gCAAgC;AAAE,MAAG,CAACD,GAAE,eAAe,EAAE,MAAO,CAAAE,OAAG;AAJ/3B;AAIg4B,UAAK,EAAC,eAAcC,IAAE,OAAMP,GAAC,IAAEM,IAAEL,MAAE,KAAAE,GAAE,IAAIH,EAAC,MAAP,mBAAU;AAAK,WAAOE,GAAE,KAAM,CAAAI,OAAG;AAJp8B,UAAAE;AAIq8B,YAAK,EAAC,kBAAiBR,IAAE,eAAcE,GAAC,IAAEI,IAAEF,MAAEI,MAAAL,GAAE,IAAIH,EAAC,MAAP,gBAAAQ,IAAU;AAAK,aAAOJ,OAAIH,MAAGC,GAAE,YAAY,EAAE,KAAK,MAAIK;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE,EAAE,OAAM,IAAI,EAAEP,IAAE,4DAA2D,EAAC,QAAOC,GAAC,CAAC;AAAE,SAAM;AAAE;AAAC,SAASQ,GAAEH,IAAEH,IAAE;AAAC,SAAOG,KAAE,EAAE,IAAIA,IAAEH,EAAC,IAAE;AAAI;AAAC,SAASJ,GAAEI,IAAEI,IAAEN,IAAEC,KAAE,MAAG;AAAC,QAAME,KAAE,CAAC;AAAE,aAAUM,MAAKH,GAAE,KAAG,QAAMG,MAAG,CAACP,GAAE,IAAIO,EAAC,EAAE,KAAGR,IAAE;AAAC,UAAMK,KAAEI,GAAED,EAAC;AAAE,QAAG;AAAC,YAAMT,KAAEQ,GAAEF,IAAEJ,EAAC;AAAE,UAAG,CAACF,GAAE,OAAM,IAAI,EAAED,IAAE,0BAAyB,EAAC,OAAMO,GAAC,CAAC;AAAE,UAAG,CAACN,GAAE,eAAe,OAAM,IAAI,EAAED,IAAE,8BAA6B,EAAC,QAAOC,GAAC,CAAC;AAAE,MAAAF,GAAEI,IAAEF,GAAE,YAAW,oCAAoC;AAAA,IAAC,SAAOI,IAAE;AAAC,YAAMC,KAAED,MAAGA,GAAE;AAAQ,UAAGC,OAAIA,GAAE,UAAQA,GAAE,OAAO,OAAMD;AAAE,MAAAC,MAAGA,GAAE,gBAAcF,GAAE,KAAK,GAAGE,GAAE,aAAa,IAAEF,GAAE,KAAKM,EAAC;AAAA,IAAC;AAAA,EAAC,MAAM,CAAAN,GAAE,KAAKM,EAAC;AAAE,MAAGN,GAAE,OAAO,OAAM,IAAI,EAAEJ,IAAEC,IAAE,EAAC,eAAcG,GAAC,CAAC;AAAC;AAAC,SAASO,GAAEL,IAAE;AAAC,SAAOA,GAAE,MAAML,EAAC,EAAE,CAAC;AAAC;AAAC,SAAS,EAAEK,IAAE;AAAC,SAAOA,GAAE,MAAML,EAAC,EAAE,CAAC;AAAC;AAAC,SAASW,GAAEN,IAAEH,IAAE;AAAC,QAAMI,KAAEJ,GAAE,IAAIG,EAAC;AAAE,SAAM,CAAC,CAACC,MAAG,CAACL,GAAE,IAAIK,GAAE,IAAI;AAAC;;;ACA/hD,IAAMM,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,kBAAgB,oBAAI,OAAI,KAAK,qBAAmB,oBAAI,OAAI,KAAK,uBAAqBF,GAAE,wBAAsB,OAAG,KAAK,cAAYE,IAAE,KAAK,iBAAeD;AAAE,UAAMF,KAAEC,GAAE;AAAU,QAAGD,MAAG,CAACA,GAAE,SAAS,GAAG,GAAE;AAAC,WAAK,YAAUA;AAAE,UAAIC,KAAE;AAAE,iBAAUC,MAAKF,IAAE;AAAC,cAAMA,KAAEI,GAAEF,EAAC,GAAEG,KAAE,KAAK,YAAY,IAAIL,EAAC,GAAEM,KAAED,KAAE,OAAKF,GAAEH,IAAEG,EAAC,GAAEC,KAAEC,KAAEA,GAAE,OAAK,EAAEH,EAAC,KAAG,eAAaD;AAAI,aAAK,gBAAgB,IAAIC,IAAE,EAAC,OAAME,IAAE,QAAOE,GAAC,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBL,IAAE;AAAC,WAAO,KAAK,wBAAsBA,GAAE,QAAS,CAAAA,OAAG,KAAK,cAAcA,EAAC,CAAE,GAAE,KAAK,mBAAmB,QAAMA,GAAE;AAAA,EAAM;AAAA,EAAC,cAAcA,IAAE;AAAC,UAAMM,KAAE,KAAK,+BAA+BN,EAAC;AAAE,WAAO,KAAK,oCAAoCM,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcN,IAAEM,IAAEC,IAAE;AAJ95B;AAI+5B,UAAMN,KAAEM,KAAEA,GAAE,OAAKD;AAAE,QAAIJ,KAAE;AAAK,WAAO,KAAK,gBAAgB,IAAID,EAAC,IAAEC,MAAE,UAAK,gBAAgB,IAAID,EAAC,MAA1B,mBAA6B,SAAOM,OAAIL,KAAEA,GAAEI,IAAE,KAAK,WAAW,GAAE,KAAK,gBAAgB,IAAIL,IAAE,EAAC,OAAMA,IAAE,QAAOC,GAAC,CAAC,IAAGK,KAAE,KAAK,eAAe,aAAaP,IAAEC,EAAC,IAAEC,MAAA,gBAAAA,GAAG,eAAeF,IAAE,KAAK;AAAA,EAAe;AAAA,EAAC,aAAaA,IAAEM,IAAE;AAAC,UAAME,KAAEF,GAAE,mBAAkBC,KAAED,GAAE;AAAmB,QAAIP,KAAEO,GAAE,SAAO,KAAK,cAAcN,IAAEM,GAAE,OAAM,KAAK,YAAY,IAAIA,GAAE,KAAK,CAAC;AAAE,QAAGA,GAAE,WAASP,KAAE,GAAG,EAAEA,EAAC,CAAC,GAAGO,GAAE,cAAc,GAAG,EAAE,KAAK,cAAcN,IAAEM,GAAE,QAAO,KAAK,YAAY,IAAIA,GAAE,MAAM,CAAC,CAAC,CAAC,IAAGA,GAAE,WAASP,KAAE,GAAGA,EAAC,GAAGO,GAAE,cAAc,GAAG,EAAE,KAAK,cAAcN,IAAEM,GAAE,QAAO,KAAK,YAAY,IAAIA,GAAE,MAAM,CAAC,CAAC,CAAC,MAAKE,MAAG,OAAO,SAAST,EAAC,GAAE;AAAC,YAAME,KAAE,YAAUO,MAAGF,GAAE,qBAAmB,KAAK,cAAcN,IAAEM,GAAE,oBAAmB,KAAK,YAAY,IAAIA,GAAE,kBAAkB,CAAC,IAAE;AAAK,MAAAP,KAAE,EAAEA,IAAES,IAAEP,IAAEM,EAAC;AAAA,IAAC;AAAC,WAAOR;AAAA,EAAC;AAAA,EAAC,mBAAmBC,IAAEM,IAAEE,IAAED,IAAE;AAAC,UAAMN,KAAE,EAAC,YAAW,KAAK,eAAe,cAAcD,EAAC,GAAE,OAAM,EAAC,QAAO,KAAK,YAAY,OAAM,EAAC,GAAEE,KAAEK,GAAE,kBAAkBN,IAAEO,EAAC;AAAE,WAAOD,GAAE,gBAAgBD,IAAEJ,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEM,IAAEE,IAAED,IAAE;AAAC,UAAMN,KAAE,EAAC,QAAO,KAAK,YAAY,OAAM;AAAE,WAAOD,GAAE,IAAK,CAAAA,OAAG;AAAC,YAAME,KAAE,EAAC,YAAW,KAAK,eAAe,cAAcF,EAAC,GAAE,OAAMC,GAAC,GAAEF,KAAEQ,GAAE,kBAAkBL,IAAEM,EAAC;AAAE,aAAOD,GAAE,gBAAgBD,IAAEP,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaC,IAAEM,IAAE;AAJ9lE;AAI+lE,WAAO,KAAK,gBAAgB,IAAIA,EAAC,KAAG,KAAK,gBAAgB,IAAIA,IAAE,EAAC,OAAMA,IAAE,QAAOJ,GAAEI,IAAE,KAAK,WAAW,EAAC,CAAC,KAAE,gBAAK,gBAAgB,IAAIA,EAAC,MAA1B,mBAA6B,WAA7B,mBAAqC,YAAYN,IAAE,KAAK,oBAAiB;AAAA,EAAE;AAAA,EAAC,cAAcA,IAAEM,IAAE;AAJpyE;AAIqyE,WAAO,KAAK,gBAAgB,IAAIA,EAAC,KAAG,KAAK,gBAAgB,IAAIA,IAAE,EAAC,OAAMA,IAAE,QAAOJ,GAAEI,IAAE,KAAK,WAAW,EAAC,CAAC,KAAE,gBAAK,gBAAgB,IAAIA,EAAC,MAA1B,mBAA6B,WAA7B,mBAAqC,QAAQN,IAAE,KAAK,oBAAiB;AAAA,EAAE;AAAA,EAAC,+BAA+BA,IAAE;AAAC,UAAMM,KAAE,KAAK;AAAU,QAAG,CAACA,MAAG,CAACA,GAAE,OAAO,QAAO,KAAK,eAAe,cAAcN,EAAC;AAAE,UAAMQ,KAAE,CAAC;AAAE,eAAUD,MAAKD,IAAE;AAAC,YAAK,EAAC,OAAMA,IAAE,QAAOL,GAAC,IAAE,KAAK,gBAAgB,IAAIM,EAAC;AAAE,MAAAC,GAAEF,EAAC,IAAEL,KAAEA,GAAE,eAAeD,IAAE,KAAK,cAAc,IAAE,KAAK,eAAe,aAAaA,IAAEM,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,oCAAoCF,IAAE;AAAC,QAAG,EAAEA,EAAC,KAAG,CAAC,KAAK,qBAAqB,QAAOA;AAAE,UAAME,KAAE,KAAK,WAAUD,KAAE,CAAC;AAAE,QAAGC,GAAE,YAAUR,MAAKQ,IAAE;AAAC,YAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,gBAAgB,IAAIR,EAAC;AAAE,MAAAO,GAAE,KAAKD,GAAEE,EAAC,CAAC;AAAA,IAAC;AAAA,QAAM,YAAUR,MAAKM,GAAE,CAAAC,GAAE,KAAKD,GAAEN,EAAC,CAAC;AAAE,UAAMC,KAAE,IAAIO,MAAG,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,IAAID,GAAE,KAAK,GAAG,CAAC;AAAG,QAAIL,KAAE,KAAK,mBAAmB,IAAID,EAAC,KAAG;AAAE,WAAO,KAAK,mBAAmB,IAAIA,IAAE,EAAEC,EAAC,GAAEA,KAAE,IAAE,OAAKI;AAAA,EAAC;AAAC;;;ACAx+D,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYG,IAAEC,IAAEC,IAAE;AAAC,SAAK,QAAMF,IAAE,KAAK,QAAMC,IAAE,KAAK,eAAaC,GAAE,cAAa,KAAK,OAAKA,GAAE,MAAK,KAAK,OAAKA,GAAE,MAAK,KAAK,cAAYA,GAAE,aAAY,KAAK,gBAAcA,GAAE,eAAc,KAAK,mBAAiBA,GAAE,kBAAiB,KAAK,iBAAeA,GAAE;AAAA,EAAc;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,MAAM;AAAA,EAAM;AAAA,EAAC,8BAA6B;AAAC,UAAMF,KAAE,IAAIG,GAAE,KAAK,OAAM,KAAK,gBAAe,KAAK,WAAW;AAAE,QAAG,CAAC,KAAK,MAAM,cAAc,QAAOH,GAAE,oBAAoB,KAAK,KAAK;AAAE,UAAK,EAAC,4BAA2BC,IAAE,QAAOC,IAAE,eAAcE,GAAC,IAAE,KAAK,OAAMC,KAAEJ,MAAA,gBAAAA,GAAG;AAAO,QAAG,CAAC,CAAC,CAACI,GAAE,QAAO;AAAE,UAAMF,KAAE,oBAAI,OAAIG,KAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,eAAUC,MAAKJ,IAAE;AAAC,YAAK,EAAC,eAAcA,GAAC,IAAEI,IAAEH,KAAE,mBAAiBD,KAAEI,GAAE,mBAAiB;AAAO,UAAG,CAACF,GAAE,IAAID,EAAC,GAAE;AAAC,cAAMH,KAAE,CAAC;AAAE,mBAAUE,MAAKH,IAAE;AAAC,gBAAMA,KAAE,KAAK,oBAAoBD,IAAEI,IAAED,EAAC;AAAE,UAAAD,GAAE,KAAKD,EAAC;AAAA,QAAC;AAAC,QAAAK,GAAE,IAAID,IAAE,KAAK,uBAAuBH,IAAEF,GAAE,oBAAoB,CAAC;AAAA,MAAC;AAAC,YAAMS,KAAEH,GAAE,IAAID,EAAC;AAAE,iBAAUJ,MAAKQ,IAAE;AAAC,cAAK,EAAC,MAAKL,IAAE,OAAMC,GAAC,IAAEI,GAAER,EAAC,GAAEE,KAAEC,GAAE,KAAK,GAAG;AAAE,QAAAF,MAAG,CAACF,GAAE,cAAcK,IAAEH,EAAC,KAAGK,GAAE,IAAIJ,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOI,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,sBAAqB;AAAC,QAAIP;AAAE,QAAG,KAAK,MAAM,eAAc;AAAC,MAAAA,KAAE,KAAK,MAAM,cAAc,KAAM,CAAAA,OAAG,mBAAiBA,GAAE,aAAc,IAAE,KAAK,iCAAiC,KAAK,KAAK,IAAE,MAAM,KAAK,+BAA+B,KAAK,KAAK;AAAA,IAAC,MAAM,CAAAA,KAAE,KAAK,4BAA4B,KAAK,KAAK;AAAE,QAAG,KAAK,MAAM,qBAAoB;AAAC,YAAMC,KAAE,KAAK,MAAM;AAAS,QAAE,KAAK,MAAM,KAAK,KAAG,CAAC,EAAEA,GAAE,kBAAiB,KAAK,MAAM,KAAK,IAAED,GAAE,gBAAcU,GAAE,EAAC,kBAAiB,KAAK,MAAM,OAAM,GAAG,EAAET,IAAEA,GAAE,kBAAiB,KAAK,MAAM,KAAK,EAAC,CAAC,IAAED,GAAE,gBAAcU,GAAE,EAAC,kBAAiB,KAAK,MAAM,OAAM,GAAGT,GAAC,CAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,uBAAuBC,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK,gBAAeC,KAAE,EAAE,KAAK,MAAK,KAAK,IAAI,GAAE,EAAC,OAAMF,IAAE,MAAKG,GAAC,IAAEL,IAAEM,KAAE,YAAU,OAAON,GAAE,WAASA,GAAE,WAASA,GAAE,SAAS,GAAEO,KAAE,YAAU,OAAOP,GAAE,WAASA,GAAE,WAASA,GAAE,SAAS,GAAEQ,KAAE,EAAC,YAAW,CAAC,EAAC,GAAEE,KAAE,0BAAwB,KAAK,cAAa,IAAE,KAAK,iBAAiBL,IAAE,KAAK,kBAAiBJ,EAAC,GAAEU,KAAE,IAAI,EAAE,MAAK,CAAC,GAAEC,KAAE,IAAI,EAAE,MAAK,CAAC,GAAEC,KAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAE,eAAUC,MAAK,KAAK,OAAM;AAAC,YAAMb,KAAEE,GAAE,YAAYW,EAAC;AAAE,UAAG,EAAEb,EAAC,EAAE;AAAS,YAAK,EAAC,QAAOI,IAAE,SAAQU,GAAC,IAAEd;AAAE,UAAGU,GAAE,SAAON,IAAEO,GAAE,SAAOP,IAAEL,GAAE,QAAMgB,GAAE,MAAK;AAAC,YAAIjB,KAAE;AAAE,iBAAQC,KAAE,GAAEA,KAAEe,GAAE,QAAOf,MAAI;AAAC,gBAAMC,KAAEc,GAAEf,EAAC;AAAE,mBAAQA,KAAE,GAAEA,KAAEC,IAAED,MAAID,MAAGK,IAAE;AAAC,kBAAMC,KAAEM;AAAE,gBAAGN,GAAE,cAAYN,IAAEC,OAAIC,KAAE,GAAE;AAAC,oBAAMD,KAAEY;AAAE,cAAAZ,GAAE,cAAYD,KAAEK;AAAE,oBAAMH,KAAEY;AAAE,cAAAJ,GAAEI,IAAEX,IAAEG,IAAEL,EAAC;AAAE,oBAAMU,MAAGR,GAAE,IAAED,GAAE,KAAGK,IAAEW,MAAGf,GAAE,IAAED,GAAE,KAAGM,IAAEI,KAAED,KAAEA,KAAEO,KAAEA;AAAE,cAAAN,MAAG,KAAGH,GAAE,WAAW,KAAKT,GAAEI,GAAE,YAAYW,EAAC,GAAE,EAAEb,EAAC,GAAE,KAAK,KAAKU,EAAC,GAAE,EAAEN,EAAC,GAAE,EAAEL,EAAC,CAAC,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGA,GAAE,QAAMgB,GAAE,QAAO;AAAC,cAAMjB,KAAEW,KAAEL,GAAE,SAAOD,KAAEC,GAAE;AAAO,iBAAQL,KAAE,GAAEA,KAAED,IAAEC,MAAGI,IAAE;AAAC,gBAAML,KAAEY;AAAE,UAAAZ,GAAE,cAAYC;AAAE,gBAAMC,MAAGC,GAAE,IAAEH,GAAE,KAAGO,IAAEF,MAAGF,GAAE,IAAEH,GAAE,KAAGQ,IAAEF,KAAEJ,KAAEA,KAAEG,KAAEA;AAAE,UAAAC,MAAG,KAAGG,GAAE,WAAW,KAAKR,GAAEG,GAAE,YAAYW,EAAC,GAAE,EAAEf,EAAC,GAAE,KAAK,KAAKM,EAAC,CAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOG,GAAE,WAAW,KAAM,CAACT,IAAEC,OAAID,GAAE,WAASC,GAAE,QAAS,GAAEQ;AAAA,EAAC;AAAA,EAAC,iBAAiBT,IAAEE,IAAEE,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,KAAG,CAAC,EAAEF,IAAEE,EAAC,IAAE,CAAAJ,OAAG,EAAEA,IAAEE,IAAEE,EAAC,IAAE,CAAAJ,OAAGA,IAAE,EAAC,MAAKG,GAAC,IAAE,MAAKG,KAAE;AAAE,WAAM,SAAON,KAAEG,KAAE,CAAC,EAAC,GAAEH,IAAE,GAAEC,IAAE,GAAEC,GAAC,MAAIG,GAAE,EAAC,GAAEL,IAAE,GAAEC,IAAE,GAAEC,GAAC,CAAC,IAAE,CAAC,EAAC,GAAEF,IAAE,GAAEC,GAAC,MAAII,GAAE,EAAC,GAAEL,IAAE,GAAEC,IAAE,GAAEK,GAAC,CAAC,IAAE,CAAC,EAAC,GAAEN,IAAE,GAAEC,GAAC,MAAII,GAAE,EAAC,GAAEL,IAAE,GAAEC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgCD,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,UAASG,IAAE,UAASC,IAAE,OAAMC,GAAC,IAAER,IAAES,KAAE,KAAK,YAAY,YAAYR,EAAC,GAAEU,KAAE,MAAM,KAAK,eAAe,EAAC,OAAMV,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,OAAMK,GAAC,CAAC,GAAEW,KAAE,EAAE,EAAC,mBAAkBd,IAAE,oBAAmBD,IAAE,UAASE,IAAE,UAASC,GAAC,CAAC,GAAEW,KAAE,KAAK,YAAY,IAAIjB,EAAC,GAAE,IAAE,EAAC,OAAM,KAAG,WAAUiB,MAAA,gBAAAA,GAAG,KAAI,GAAEN,KAAE,GAAEM,EAAC,IAAE,EAAE,EAAC,QAAOP,IAAE,mBAAkBQ,IAAE,kBAAiB,EAAC,CAAC,IAAE,EAAE,EAAC,QAAOR,IAAE,UAASL,IAAE,UAASC,IAAE,iBAAgB,CAACF,IAAE,mBAAkBc,IAAE,kBAAiB,EAAC,CAAC;AAAE,WAAO,EAAEP,IAAEH,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA2BT,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,iBAAgBC,IAAE,SAAQE,IAAE,sBAAqBC,IAAE,OAAMF,GAAC,IAAEH,IAAEM,KAAE,MAAM,KAAK,eAAe,EAAC,OAAML,IAAE,QAAOD,GAAE,QAAO,QAAOA,GAAE,QAAO,gBAAeA,GAAE,gBAAe,iBAAgBE,IAAE,OAAMC,GAAC,CAAC,GAAEI,KAAE,EAAED,EAAC;AAAE,WAAO,EAAEC,IAAEH,IAAEC,IAAEL,GAAE,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,0BAA0BA,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,sBAAqBG,IAAE,2BAA0BC,IAAE,UAASC,IAAE,UAASC,IAAE,YAAWE,IAAE,OAAMQ,GAAC,IAAEnB,IAAEkB,KAAE,MAAM,KAAK,eAAe,EAAC,OAAMjB,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,OAAMgB,GAAC,CAAC,GAAE,IAAE,EAAED,IAAE,EAAC,OAAMjB,IAAE,oBAAmBG,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,sBAAqBG,IAAE,2BAA0BC,IAAE,UAASC,IAAE,UAASC,IAAE,YAAWE,GAAC,CAAC;AAAE,WAAO,EAAE,GAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAwBN,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,sBAAqBG,IAAE,2BAA0BC,IAAE,UAASC,IAAE,UAASC,IAAE,SAAQE,IAAE,OAAMQ,GAAC,IAAEnB,IAAEkB,KAAE,MAAM,KAAK,eAAe,EAAC,OAAMjB,IAAE,iBAAgBC,IAAE,oBAAmBE,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,OAAMgB,GAAC,CAAC;AAAE,WAAO,EAAED,IAAE,EAAC,OAAMjB,IAAE,oBAAmBG,IAAE,mBAAkBC,IAAE,oBAAmBF,IAAE,sBAAqBG,IAAE,2BAA0BC,IAAE,UAASC,IAAE,UAASC,IAAE,SAAQE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,cAAcX,IAAEC,IAAEC,IAAE;AAAC,QAAGF,GAAE,SAAO,KAAGC,MAAGA,GAAE,OAAO,YAAUG,MAAKH,GAAE,QAAQ,GAAE;AAAC,YAAMA,KAAEG,GAAE,MAAM,GAAG,GAAEC,KAAEJ,GAAE,CAAC,GAAEE,KAAE,KAAK,YAAY,IAAIE,EAAC,GAAEC,KAAE,CAAC,CAACL,GAAE,CAAC,KAAG,WAASA,GAAE,CAAC,EAAE,YAAY,GAAEM,KAAEa,GAAEjB,MAAA,gBAAAA,GAAG,MAAKG,EAAC;AAAE,MAAAN,GAAE,KAAM,CAACA,IAAEC,OAAI;AAAC,cAAMG,KAAEF,GAAEF,IAAEK,IAAEF,EAAC,GAAEG,KAAEJ,GAAED,IAAEI,IAAEF,EAAC;AAAE,eAAOI,GAAEH,IAAEE,EAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,4BAA4BN,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAM,EAAC,cAAaC,IAAE,MAAKE,IAAE,MAAKC,IAAE,eAAcF,IAAE,kBAAiBI,GAAC,IAAE,MAAK,EAAC,WAAUC,IAAE,OAAMC,IAAE,wBAAuBE,IAAE,mBAAkBQ,IAAE,cAAaD,IAAE,SAAQN,IAAE,SAAQC,GAAC,IAAEb,IAAEc,KAAE,QAAMK,MAAGlB,GAAE,UAAQiB,MAAG,KAAGC,IAAEJ,KAAEP,OAAIA,GAAE,SAAS,GAAG,IAAE,CAAC,GAAG,KAAK,YAAY,MAAM,IAAEA,GAAE,IAAK,CAAAR,OAAG,KAAK,YAAY,IAAIA,EAAC,CAAE;AAAG,WAAM,EAAC,uBAAsBc,IAAE,UAAS,KAAK,gBAAgBd,IAAEC,EAAC,GAAE,QAAOc,IAAE,cAAab,IAAE,MAAKE,MAAGS,IAAE,MAAKR,MAAGO,IAAE,mBAAkBT,IAAE,kBAAiBO,GAAED,MAAGF,EAAC,GAAE,WAAUI,MAAGP,GAAEO,EAAC,KAAG,KAAI;AAAA,EAAC;AAAA,EAAC,gBAAgBX,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAIC,GAAEH,IAAE,KAAK,gBAAe,KAAK,WAAW,GAAE,EAAC,MAAKI,IAAE,MAAKC,GAAC,IAAE,MAAK,EAAC,eAAcF,IAAE,wBAAuBI,IAAE,gBAAeC,IAAE,gBAAeG,IAAE,oBAAmBQ,IAAE,cAAaD,IAAE,mBAAkB,GAAE,SAAQJ,KAAE,OAAG,SAAQC,KAAE,MAAE,IAAEf,IAAEgB,KAAEX,MAAGS,IAAEO,KAAEjB,MAAGW;AAAE,QAAIO,KAAE,CAAC,GAAE,IAAE;AAAE,UAAM,IAAE,CAAC,GAAGrB,EAAC;AAAE,QAAG,KAAK,cAAc,GAAEE,IAAG,CAACH,IAAEC,IAAEG,OAAIF,GAAE,cAAcF,IAAEC,IAAEG,EAAC,CAAE,GAAEI,MAAGG,IAAE;AAAC,YAAMX,KAAEI,GAAEG,EAAC,KAAG;AAAO,UAAGC,MAAG,CAACG,GAAE,YAAUV,MAAK,EAAE,CAAAqB,GAAE,GAAG,IAAE,EAAC,YAAWpB,GAAE,cAAcD,EAAC,GAAE,UAASmB,GAAE,KAAK,cAAa,KAAK,MAAK,KAAK,MAAK,KAAK,eAAe,YAAYnB,EAAC,GAAEkB,IAAEnB,IAAEgB,IAAEK,EAAC,EAAC;AAAA,eAAU,CAACb,MAAGG,GAAE,YAAUV,MAAK,EAAE,CAAAqB,GAAE,GAAG,IAAE,EAAC,YAAWpB,GAAE,cAAcD,EAAC,GAAE,UAAS,EAAE,MAAK,KAAK,eAAe,YAAYA,IAAE,IAAI,GAAED,EAAC,EAAC;AAAA,UAAO,YAAUC,MAAK,EAAE,CAAAqB,GAAE,GAAG,IAAE,EAAC,YAAWpB,GAAE,cAAcD,EAAC,GAAE,UAAS,EAAE,MAAK,KAAK,eAAe,YAAYA,IAAE,IAAI,GAAED,EAAC,GAAE,UAASoB,GAAE,KAAK,cAAa,KAAK,MAAK,KAAK,MAAK,KAAK,eAAe,YAAYnB,EAAC,GAAEkB,IAAEnB,IAAEgB,IAAEK,EAAC,EAAC;AAAA,IAAC,MAAM,YAAUf,MAAK,GAAE;AAAC,YAAMN,KAAEE,GAAE,cAAcI,EAAC;AAAE,MAAAN,OAAIsB,GAAE,GAAG,IAAE,EAAC,YAAWtB,GAAC;AAAA,IAAE;AAAC,UAAMuB,KAAEL,MAAG;AAAE,QAAG,QAAM,GAAE;AAAC,YAAMlB,KAAEuB,KAAE;AAAE,MAAAD,KAAEA,GAAE,MAAMC,IAAE,KAAK,IAAID,GAAE,QAAOtB,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOsB;AAAA,EAAC;AAAA,EAAC,iCAAiCtB,IAAE;AAAC,QAAIE,KAAE,OAAGE,KAAE,OAAO,mBAAkBC,KAAE,OAAO,mBAAkBF,KAAE,OAAO;AAAkB,eAAUF,MAAKD,GAAE,iBAAe,CAAC,EAAE,KAAG,mBAAiBC,GAAE,eAAc;AAAC,MAAAG,KAAE,QAAMH,GAAE,gBAAcA,GAAE,gBAAc,OAAO,mBAAkBI,KAAE,QAAMJ,GAAE,iBAAeA,GAAE,iBAAe,OAAO,mBAAkBE,KAAE,QAAMF,GAAE,iBAAeA,GAAE,iBAAe,OAAO;AAAkB;AAAA,IAAK;AAAC,QAAG,wBAAsB,KAAK,aAAa,CAAAC,KAAE,KAAK,MAAM,SAAOE;AAAA,aAAU,KAAK,MAAM,SAAOC,GAAE,CAAAH,KAAE;AAAA,SAAO;AAAC,YAAMF,KAAE,EAAE,KAAK,MAAK,KAAK,IAAI,GAAEI,KAAE,KAAK;AAAe,MAAAF,KAAE,KAAK,MAAM,OAAQ,CAACF,IAAEE,OAAI;AAAC,cAAMG,KAAED,GAAE,YAAYF,EAAC;AAAE,eAAOF,MAAG,EAAEK,EAAC,KAAGA,GAAE,OAAO,UAAQ;AAAA,MAAE,GAAG,CAAC,IAAEL,KAAEG;AAAA,IAAC;AAAC,WAAM,EAAC,QAAO,CAAC,EAAC,MAAK,gBAAe,MAAK,wBAAuB,OAAM,gBAAe,SAAQ,kBAAiB,QAAO,MAAK,cAAa,KAAI,CAAC,GAAE,UAAS,CAAC,EAAC,YAAW,EAAC,cAAa,OAAOD,EAAC,EAAC,EAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,+BAA+BF,IAAE;AAAC,UAAMC,KAAE,EAAC,YAAW,CAAC,EAAC,GAAEC,KAAE,CAAC,GAAEE,KAAE,oBAAI,OAAIC,KAAE,oBAAI,OAAIF,KAAE,oBAAI,OAAIG,KAAE,oBAAI,OAAIC,KAAE,IAAIJ,GAAEH,IAAE,KAAK,gBAAe,KAAK,WAAW,GAAEQ,KAAER,GAAE,eAAc,EAAC,4BAA2BW,IAAE,QAAOQ,IAAE,eAAcD,GAAC,IAAElB,IAAE,IAAEW,MAAGA,GAAE,QAAOC,KAAE,CAAC,CAAC,GAAEC,KAAED,KAAED,GAAE,CAAC,IAAE,MAAKG,KAAEF,MAAG,CAAC,KAAK,YAAY,IAAIC,EAAC;AAAE,eAAUJ,MAAKD,MAAG,CAAC,GAAE;AAAC,YAAK,EAAC,uBAAsBR,IAAE,eAAcQ,GAAC,IAAEC,IAAES,KAAET,IAAEM,KAAE,mBAAiBP,KAAEC,GAAE,mBAAiB,QAAOO,KAAE,sBAAoBR,MAAG,sBAAoBA,IAAEa,KAAE,wBAAsBb,MAAG,wBAAsBA,MAAG,0BAAwBA,IAAEc,KAAEV,MAAG,MAAI,MAAIG,OAAIF,MAAGC,OAAI,YAAUN;AAAE,UAAGI,IAAE;AAAC,YAAG,CAACT,GAAE,IAAIY,EAAC,GAAE;AAAC,gBAAMf,KAAE,CAAC;AAAE,qBAAUC,MAAKU,IAAE;AAAC,kBAAMT,KAAE,KAAK,oBAAoBK,IAAEN,IAAEG,EAAC;AAAE,YAAAJ,GAAE,KAAKE,EAAC;AAAA,UAAC;AAAC,UAAAC,GAAE,IAAIY,IAAE,KAAK,uBAAuBf,IAAE,CAACqB,MAAGd,GAAE,oBAAoB,CAAC;AAAA,QAAC;AAAC,cAAMN,KAAEE,GAAE,IAAIY,EAAC;AAAE,mBAAUb,MAAKD,IAAE;AAAC,gBAAK,EAAC,OAAMI,IAAE,MAAKF,IAAE,OAAMK,IAAE,eAAcC,GAAC,IAAER,GAAEC,EAAC,GAAEsB,KAAErB,GAAE,KAAK,GAAG;AAAE,cAAG,CAACgB,MAAGZ,GAAE,cAAcC,IAAEW,EAAC,GAAE;AAAC,kBAAMlB,KAAEK,GAAE,IAAIkB,EAAC,KAAG,EAAC,YAAW,CAAC,EAAC;AAAE,gBAAGH,IAAE;AAAC,cAAApB,GAAE,wBAAsBA,GAAE,sBAAoB,CAAC;AAAG,oBAAK,EAAC,qBAAoBD,IAAE,uBAAsBE,GAAC,IAAE,MAAM,KAAK,sBAAsBgB,IAAEV,EAAC;AAAE,cAAAP,GAAE,oBAAoBC,EAAC,IAAEF;AAAA,YAAC,OAAK;AAAC,kBAAIE,KAAE;AAAK,kBAAGoB,GAAE,CAAApB,KAAEG;AAAA,mBAAM;AAAC,sBAAML,KAAE,KAAK,oBAAoBO,IAAEQ,IAAEX,EAAC,GAAEH,KAAEQ,GAAE,IAAK,CAAAR,OAAGD,GAAEC,EAAC,CAAE;AAAE,gBAAAC,KAAEc,MAAG,yBAAwBE,KAAE,KAAK,oBAAoBA,IAAEjB,EAAC,IAAE,KAAK,mBAAmBiB,IAAEjB,IAAE,MAAKM,GAAE,oBAAoB;AAAA,cAAC;AAAC,cAAAN,GAAE,WAAWD,EAAC,IAAEE;AAAA,YAAC;AAAC,gBAAIA,KAAE;AAAE,YAAAS,GAAE,QAAS,CAACX,IAAEI,OAAIH,GAAE,WAAW,KAAK,YAAY,IAAID,EAAC,IAAEA,KAAE,UAAS,EAAEE,EAAC,IAAEC,GAAEC,EAAC,CAAE,GAAEE,GAAE,IAAIkB,IAAEvB,EAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,WAASoB,IAAE;AAAC,QAAApB,GAAE,wBAAsBA,GAAE,sBAAoB,CAAC;AAAG,cAAK,EAAC,qBAAoBD,IAAE,uBAAsBE,GAAC,IAAE,MAAM,KAAK,sBAAsBgB,IAAE,KAAK,KAAK;AAAE,QAAAjB,GAAE,oBAAoBC,EAAC,IAAEF;AAAA,MAAC,OAAK;AAAC,cAAME,KAAE,KAAK,oBAAoBK,IAAEQ,IAAEX,EAAC;AAAE,QAAAH,GAAE,WAAWD,EAAC,IAAEgB,MAAG,yBAAwBE,KAAE,KAAK,oBAAoBA,IAAEhB,EAAC,IAAE,KAAK,mBAAmBgB,IAAEhB,IAAEG,IAAEE,GAAE,oBAAoB;AAAA,MAAC;AAAC,MAAAL,GAAE,KAAK,EAAC,MAAKF,IAAE,OAAMA,IAAE,MAAK,sBAAqB,CAAC;AAAA,IAAC;AAAC,UAAMe,KAAEH,KAAE,MAAM,KAAKN,GAAE,OAAO,CAAC,IAAE,CAACL,EAAC;AAAE,WAAO,KAAK,cAAcc,IAAEG,IAAG,CAAClB,IAAEC,OAAID,GAAE,WAAWC,EAAC,CAAE,GAAE,EAAC,QAAOC,IAAE,UAASa,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,sBAAsBf,IAAEC,IAAE;AAAC,UAAMK,KAAE,MAAM,OAAO,kCAAyC,GAAE,EAAC,eAAcC,IAAE,uBAAsBC,GAAC,IAAER,IAAE,EAAC,gBAAeS,IAAE,kBAAiBE,IAAE,cAAaQ,IAAE,MAAKD,IAAE,MAAK,EAAC,IAAE,MAAKL,KAAEZ,GAAE,IAAK,CAAAD,OAAGoB,GAAED,IAAED,IAAE,GAAET,GAAE,YAAYT,EAAC,CAAC,CAAE,GAAEc,KAAER,GAAE,WAAWK,IAAEE,IAAE,IAAE,EAAE,CAAC,GAAEE,KAAE,EAAC,qBAAoB,MAAK,uBAAsB,KAAI;AAAE,QAAG,wBAAsBR,IAAE;AAAC,YAAMP,KAAEc,KAAE,EAAEA,EAAC,IAAE,EAAER,GAAE,MAAMK,IAAEE,EAAC,CAAC;AAAE,MAAAE,GAAE,sBAAoB,EAAC,GAAGf,IAAE,kBAAiBW,GAAC,GAAEI,GAAE,wBAAsBP,MAAG;AAAA,IAAQ,WAAS,wBAAsBD,IAAE;AAAC,YAAMP,KAAEc,KAAEX,GAAEW,EAAC,IAAE,EAAE,EAAER,GAAE,MAAMK,IAAEE,EAAC,CAAC,CAAC;AAAE,MAAAE,GAAE,sBAAoB,EAAC,GAAEf,GAAE,CAAC,GAAE,GAAEA,GAAE,CAAC,GAAE,kBAAiBW,GAAC,GAAEI,GAAE,wBAAsBP,MAAG;AAAA,IAAU,MAAK,2BAAwBD,OAAIQ,GAAE,sBAAoBD,IAAEC,GAAE,wBAAsBP,MAAG;AAAc,WAAOO;AAAA,EAAC;AAAA,EAAC,mBAAmBf,IAAEC,IAAEC,IAAEE,IAAE;AAAC,UAAK,EAAC,kBAAiBC,IAAE,eAAcF,GAAC,IAAEH;AAAE,QAAIM,KAAE;AAAK,IAAAA,MAAEJ,MAAA,gBAAAA,GAAG,IAAIG,OAAGH,GAAE,IAAIG,EAAC,IAAE,GAAE,KAAK,YAAY,IAAIA,EAAC,CAAC,IAAE,EAAE,EAAC,QAAOJ,IAAE,gBAAeG,GAAC,CAAC,IAAE,EAAE,EAAC,QAAOA,KAAE,CAAC,GAAG,IAAI,IAAIH,EAAC,CAAC,IAAEA,IAAE,UAAS,MAAK,UAAS,MAAK,iBAAgB,KAAE,CAAC,GAAEC,MAAGA,GAAE,IAAIG,IAAEC,EAAC;AAAE,WAAOA,GAAE,UAAQH,KAAE,aAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,IAAEC,IAAE;AAAC,UAAK,EAAC,kBAAiBC,IAAE,qBAAoBE,IAAE,eAAcC,GAAC,IAAEL,IAAE,EAAC,OAAMG,IAAE,SAAQG,GAAC,IAAEF,IAAEG,KAAE,KAAK,YAAY,IAAIL,EAAC;AAAE,WAAO,EAAED,IAAE,EAAC,OAAME,IAAE,SAAQG,IAAE,WAAUC,MAAA,gBAAAA,GAAG,MAAK,YAAW,sBAAoBF,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBL,IAAEC,IAAEC,IAAE;AAAC,QAAGA,GAAE,IAAID,EAAC,EAAE,QAAOC,GAAE,IAAID,EAAC;AAAE,UAAMG,KAAE,KAAK,YAAY,IAAIH,EAAC,GAAEI,KAAE,KAAK,MAAM,IAAK,CAAAH,OAAGF,GAAE,cAAcE,IAAED,IAAEG,EAAC,CAAE;AAAE,WAAOF,GAAE,IAAID,IAAEI,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,wBAAwBL,IAAEC,IAAE;AAAC,WAAO,KAAK,MAAM,IAAK,CAAAC,OAAGF,GAAE,aAAaE,IAAE,EAAC,OAAMD,GAAE,OAAM,QAAOA,GAAE,QAAO,QAAOA,GAAE,QAAO,gBAAeA,GAAE,gBAAe,oBAAmBA,GAAE,oBAAmB,mBAAkBA,GAAE,mBAAkB,oBAAmBA,GAAE,mBAAkB,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA8BD,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,aAAYE,GAAC,IAAE,MAAM,EAAE,GAAEC,KAAED,GAAE,eAAeH,EAAC,GAAEE,KAAED,MAAGE,GAAE,YAAYF,EAAC;AAAE,WAAOF,GAAE,oBAAoB,KAAK,OAAMK,IAAEF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBJ,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEE,KAAE,KAAK,OAAMC,KAAED,GAAE;AAAO,aAAQD,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,YAAME,KAAED,GAAED,EAAC,GAAEG,KAAE,CAAC;AAAE,iBAAUL,MAAKD,GAAE,CAAAM,GAAE,KAAKL,GAAEE,EAAC,CAAC;AAAE,YAAMI,KAAED,GAAE,KAAK,GAAG;AAAE,cAAMJ,GAAEK,EAAC,IAAEL,GAAEK,EAAC,IAAE,EAAC,OAAM,GAAE,MAAKD,IAAE,OAAM,CAACD,EAAC,GAAE,eAAc,CAACF,EAAC,EAAC,KAAGF,MAAGC,GAAEK,EAAC,EAAE,SAAQL,GAAEK,EAAC,EAAE,MAAM,KAAKF,EAAC,GAAEH,GAAEK,EAAC,EAAE,cAAc,KAAKJ,EAAC;AAAA,IAAE;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeF,IAAE;AAAC,UAAMC,KAAE,IAAIE,GAAE,KAAK,OAAM,KAAK,gBAAe,KAAK,WAAW,GAAE,EAAC,iBAAgBD,IAAE,OAAME,IAAE,oBAAmBC,IAAE,mBAAkBF,IAAE,oBAAmBG,IAAE,OAAMC,GAAC,IAAEP,IAAEQ,KAAEN,KAAE,EAAC,aAAY,OAAM,OAAMK,IAAE,kBAAiB,KAAK,MAAM,SAAO,KAAK,iBAAgB,IAAE;AAAK,WAAOL,KAAE,KAAK,8BAA8BD,IAAEC,IAAEM,EAAC,IAAE,KAAK,wBAAwBP,IAAE,EAAC,OAAMG,IAAE,QAAOJ,GAAE,QAAO,QAAOA,GAAE,QAAO,gBAAeA,GAAE,gBAAe,oBAAmBK,IAAE,mBAAkBF,IAAE,oBAAmBG,GAAC,CAAC;AAAA,EAAC;AAAC;AAAC,SAASI,GAAEV,IAAEC,IAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAED,GAAE,IAAEF,GAAE,GAAEC,KAAEC,GAAE,IAAEF,GAAE,GAAEI,KAAED,KAAEA,KAAEF,KAAEA,IAAEI,MAAGN,GAAE,IAAEC,GAAE,KAAGG,MAAGJ,GAAE,IAAEC,GAAE,KAAGC,IAAEK,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAED,KAAED,EAAC,CAAC;AAAE,EAAAN,GAAE,IAAEE,GAAE,IAAEG,KAAEG,IAAER,GAAE,IAAEE,GAAE,IAAEC,KAAEK;AAAC;AAAC,SAAS,EAAER,IAAEC,IAAE;AAAC,SAAOD,KAAEC,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAC;AAAC,IAAIgB;AAAE,CAAC,SAASjB,IAAE;AAAC,EAAAA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE;AAAQ,EAAEiB,OAAIA,KAAE,CAAC,EAAE;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYjB,IAAEC,IAAE;AAAC,SAAK,SAAOD,IAAE,KAAK,cAAYC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,OAAO,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,OAAO,KAAK,cAAY,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,IAAG;AAAC,WAAO,KAAK,OAAO,KAAK,cAAY,CAAC;AAAA,EAAC;AAAC;", "names": ["c", "e", "t", "i", "r", "f", "c", "s", "t", "r", "i", "o", "a", "e", "n", "_a", "l", "u", "d", "f", "r", "t", "a", "l", "d", "n", "u", "e", "s", "i", "e", "t", "i", "r", "s", "a", "n", "o", "l", "u", "E", "c", "f", "g", "p", "y", "x", "D", "m", "d", "v", "I", "T", "S", "h"]}