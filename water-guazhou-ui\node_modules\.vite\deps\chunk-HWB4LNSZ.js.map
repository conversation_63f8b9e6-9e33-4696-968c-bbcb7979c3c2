{"version": 3, "sources": ["../../@arcgis/core/views/layers/LayerView.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import t from\"../../core/Accessor.js\";import r from\"../../core/Evented.js\";import{HandleOwnerMixin as s}from\"../../core/HandleOwner.js\";import{IdentifiableMixin as i}from\"../../core/Identifiable.js\";import o from\"../../core/Logger.js\";import{unwrapOr as n}from\"../../core/maybe.js\";import{EsriPromiseMixin as l}from\"../../core/Promise.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";let d=class extends(s(i(l(r.EventedMixin(t))))){constructor(e){super(e),this.layer=null,this.parent=null}initialize(){this.when().catch((e=>{if(\"layerview:create-error\"!==e.name){const t=this.layer&&this.layer.id||\"no id\",r=this.layer&&this.layer.title||\"no title\";o.getLogger(this.declaredClass).error(\"#resolve()\",`Failed to resolve layer view (layer title: '${r}', id: '${t}')`,e)}}))}get fullOpacity(){return n(this.get(\"layer.opacity\"),1)*n(this.get(\"parent.fullOpacity\"),1)}get suspended(){return!this.canResume()}get suspendInfo(){return this.getSuspendInfo()}get legendEnabled(){return!this.suspended&&!0===this.layer?.legendEnabled}get updating(){return!(!this.updatingHandles?.updating&&!this.isUpdating())}get updatingProgress(){return this.updating?0:1}get visible(){return!0===this.layer?.visible}set visible(e){this._overrideIfSome(\"visible\",e)}canResume(){return this.visible&&this.layer?.loaded&&!this.parent?.suspended&&this.view?.ready||!1}getSuspendInfo(){const e=this.parent&&this.parent.suspended?this.parent.suspendInfo:{},t=this;return t.view&&t.view.ready||(e.viewNotReady=!0),this.layer&&this.layer.loaded||(e.layerNotLoaded=!0),this.visible||(e.layerInvisible=!0),e}isUpdating(){return!1}};e([a()],d.prototype,\"fullOpacity\",null),e([a()],d.prototype,\"layer\",void 0),e([a()],d.prototype,\"parent\",void 0),e([a({readOnly:!0})],d.prototype,\"suspended\",null),e([a({readOnly:!0})],d.prototype,\"suspendInfo\",null),e([a({readOnly:!0})],d.prototype,\"legendEnabled\",null),e([a({type:Boolean,readOnly:!0})],d.prototype,\"updating\",null),e([a({readOnly:!0})],d.prototype,\"updatingProgress\",null),e([a()],d.prototype,\"visible\",null),e([a()],d.prototype,\"view\",void 0),d=e([p(\"esri.views.layers.LayerView\")],d);const u=d;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6mB,IAAI,IAAE,cAAcA,GAAEC,GAAE,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,QAAM,MAAK,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,KAAK,EAAE,MAAO,CAAAA,OAAG;AAAC,UAAG,6BAA2BA,GAAE,MAAK;AAAC,cAAM,IAAE,KAAK,SAAO,KAAK,MAAM,MAAI,SAAQ,IAAE,KAAK,SAAO,KAAK,MAAM,SAAO;AAAW,UAAE,UAAU,KAAK,aAAa,EAAE,MAAM,cAAa,+CAA+C,CAAC,WAAW,CAAC,MAAKA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,EAAE,KAAK,IAAI,eAAe,GAAE,CAAC,IAAE,EAAE,KAAK,IAAI,oBAAoB,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,CAAC,KAAK,UAAU;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,IAAI,gBAAe;AAJvrC;AAIwrC,WAAM,CAAC,KAAK,aAAW,WAAK,UAAK,UAAL,mBAAY;AAAA,EAAa;AAAA,EAAC,IAAI,WAAU;AAJ5vC;AAI6vC,WAAM,EAAE,GAAC,UAAK,oBAAL,mBAAsB,aAAU,CAAC,KAAK,WAAW;AAAA,EAAE;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,WAAS,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAJv3C;AAIw3C,WAAM,WAAK,UAAK,UAAL,mBAAY;AAAA,EAAO;AAAA,EAAC,IAAI,QAAQA,IAAE;AAAC,SAAK,gBAAgB,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAJn9C;AAIo9C,WAAO,KAAK,aAAS,UAAK,UAAL,mBAAY,WAAQ,GAAC,UAAK,WAAL,mBAAa,gBAAW,UAAK,SAAL,mBAAW,UAAO;AAAA,EAAE;AAAA,EAAC,iBAAgB;AAAC,UAAMA,KAAE,KAAK,UAAQ,KAAK,OAAO,YAAU,KAAK,OAAO,cAAY,CAAC,GAAE,IAAE;AAAK,WAAO,EAAE,QAAM,EAAE,KAAK,UAAQA,GAAE,eAAa,OAAI,KAAK,SAAO,KAAK,MAAM,WAASA,GAAE,iBAAe,OAAI,KAAK,YAAUA,GAAE,iBAAe,OAAIA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAM;AAAA,EAAE;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,SAAQ,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,WAAU,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,IAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["a", "s", "e"]}