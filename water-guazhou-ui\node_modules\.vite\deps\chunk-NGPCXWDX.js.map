{"version": 3, "sources": ["../../@arcgis/core/layers/mixins/ScaleRangeLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as c}from\"../../core/accessorSupport/decorators/subclass.js\";const t=t=>{let l=class extends t{constructor(){super(...arguments),this.minScale=0,this.maxScale=0}get effectiveScaleRange(){const e={minScale:this.minScale,maxScale:this.maxScale},a=this.parent;a&&\"effectiveScaleRange\"in a&&r(e,a.effectiveScaleRange);const c=this._get(\"effectiveScaleRange\");return c&&c.minScale===e.minScale&&c.maxScale===e.maxScale?c:e}};return e([a({type:Number,nonNullable:!0,json:{write:!0}})],l.prototype,\"minScale\",void 0),e([a({type:Number,nonNullable:!0,json:{write:!0}})],l.prototype,\"maxScale\",void 0),e([a({readOnly:!0})],l.prototype,\"effectiveScaleRange\",null),l=e([c(\"esri.layers.mixins.ScaleRangeLayer\")],l),l};function r(e,a){return e.minScale=e.minScale>0?a.minScale>0?Math.min(e.minScale,a.minScale):e.minScale:a.minScale,e.maxScale=e.maxScale>0?a.maxScale>0?Math.max(e.maxScale,a.maxScale):e.maxScale:a.maxScale,e}export{t as ScaleRangeLayer};\n"], "mappings": ";;;;;;;;;AAI0R,IAAM,IAAE,CAAAA,OAAG;AAAC,MAAI,IAAE,cAAcA,GAAC;AAAA,IAAC,cAAa;AAAC,YAAM,GAAG,SAAS,GAAE,KAAK,WAAS,GAAE,KAAK,WAAS;AAAA,IAAC;AAAA,IAAC,IAAI,sBAAqB;AAAC,YAAMC,KAAE,EAAC,UAAS,KAAK,UAAS,UAAS,KAAK,SAAQ,GAAEC,KAAE,KAAK;AAAO,MAAAA,MAAG,yBAAwBA,MAAG,EAAED,IAAEC,GAAE,mBAAmB;AAAE,YAAM,IAAE,KAAK,KAAK,qBAAqB;AAAE,aAAO,KAAG,EAAE,aAAWD,GAAE,YAAU,EAAE,aAAWA,GAAE,WAAS,IAAEA;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,uBAAsB,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,oCAAoC,CAAC,GAAE,CAAC,GAAE;AAAC;AAAE,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOD,GAAE,WAASA,GAAE,WAAS,IAAEC,GAAE,WAAS,IAAE,KAAK,IAAID,GAAE,UAASC,GAAE,QAAQ,IAAED,GAAE,WAASC,GAAE,UAASD,GAAE,WAASA,GAAE,WAAS,IAAEC,GAAE,WAAS,IAAE,KAAK,IAAID,GAAE,UAASC,GAAE,QAAQ,IAAED,GAAE,WAASC,GAAE,UAASD;AAAC;", "names": ["t", "e", "a"]}