{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/graphics/GraphicContainer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{WGLDrawPhase as e}from\"../../engine/webgl/enums.js\";import r from\"./BaseGraphicContainer.js\";class i extends r{renderChildren(r){this.attributeView.update(),this.children.some((e=>e.hasData))&&(this.attributeView.bindTextures(r.context,!1),super.renderChildren(r),r.drawPhase===e.MAP&&this._renderChildren(r),this.hasHighlight()&&r.drawPhase===e.HIGHLIGHT&&this._renderHighlight(r),this._boundsRenderer&&this._boundsRenderer.doRender(r))}_renderHighlight(e){const{painter:r}=e,i=r.effects.highlight;i.bind(e),this._renderChildren(e,i.defines),i.draw(e),i.unbind()}}export{i as default};\n"], "mappings": ";;;;;;;;AAIoG,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,eAAe,GAAE;AAAC,SAAK,cAAc,OAAO,GAAE,KAAK,SAAS,KAAM,OAAG,EAAE,OAAQ,MAAI,KAAK,cAAc,aAAa,EAAE,SAAQ,KAAE,GAAE,MAAM,eAAe,CAAC,GAAE,EAAE,cAAY,EAAE,OAAK,KAAK,gBAAgB,CAAC,GAAE,KAAK,aAAa,KAAG,EAAE,cAAY,EAAE,aAAW,KAAK,iBAAiB,CAAC,GAAE,KAAK,mBAAiB,KAAK,gBAAgB,SAAS,CAAC;AAAA,EAAE;AAAA,EAAC,iBAAiB,GAAE;AAAC,UAAK,EAAC,SAAQ,EAAC,IAAE,GAAEA,KAAE,EAAE,QAAQ;AAAU,IAAAA,GAAE,KAAK,CAAC,GAAE,KAAK,gBAAgB,GAAEA,GAAE,OAAO,GAAEA,GAAE,KAAK,CAAC,GAAEA,GAAE,OAAO;AAAA,EAAC;AAAC;", "names": ["i"]}